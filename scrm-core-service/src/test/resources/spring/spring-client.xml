<?xml version="1.0" encoding="UTF-8"?>
<beans xmlns="http://www.springframework.org/schema/beans"
       xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
       xmlns:pigeon="http://code.dianping.com/schema/pigeon"
       xsi:schemaLocation="http://www.springframework.org/schema/beans
          http://www.springframework.org/schema/beans/spring-beans.xsd
          http://code.dianping.com/schema/pigeon
          http://code.dianping.com/schema/pigeon/pigeon-service-2.0.xsd">

    <bean id="corpWxService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.service.fe.corp.wx.thrift.CorpWxService"/>
        <property name="appKey" value="baby-customer-service"/>
        <property name="remoteAppkey" value="com.sankuai.ds.be.http"/>
        <property name="remoteServerPort" value="9000"/>
        <property name="timeout" value="10000"/>
        <property name="filters">
            <list>
                <bean class="com.sankuai.scrm.core.service.envrequestforwarding.filter.OfflineThriftFilter"/>
            </list>
        </property>
<!--        <property name="genericServiceName" value="com.sankuai.service.fe.corp.wx.thrift.CorpWxService"/> &lt;!&ndash; 实际的服务接口名 &ndash;&gt;-->
<!--        <property name="generic" value="json"/>-->
    </bean>

    <bean id="openGroupService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.service.fe.corp.wx.thrift.open.OpenGroupService"/>
        <property name="appKey" value="baby-customer-service"/>
        <property name="remoteAppkey" value="com.sankuai.ds.be.http"/>
        <property name="timeout" value="10000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="filters">
            <list>
                <bean class="com.sankuai.scrm.core.service.envrequestforwarding.filter.OfflineThriftFilter"/>
            </list>
        </property>
    </bean>

    <bean id="openFriendService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.service.fe.corp.wx.thrift.open.OpenFriendService"/>
        <property name="appKey" value="baby-customer-service"/>
        <property name="remoteAppkey" value="com.sankuai.ds.be.http"/>
        <property name="timeout" value="10000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="filters">
            <list>
                <bean class="com.sankuai.scrm.core.service.envrequestforwarding.filter.OfflineThriftFilter"/>
            </list>
        </property>
    </bean>

    <!-- 企微群组信息 -->
    <pigeon:reference id="groupInfoApplicationService"
                      timeout="1000"
                      url="com.dianping.baby.customer.operator.operatorhelper.api.GroupInfoApplicationService"
                      interface="com.dianping.baby.customer.operator.operatorhelper.api.GroupInfoApplicationService"/>

    <pigeon:reference id="msgTaskService"
                      timeout="1000"
                      url="com.dianping.baby.customer.operator.operatorhelper.api.msg.task.MsgTaskService"
                      interface="com.dianping.baby.customer.operator.operatorhelper.api.msg.task.MsgTaskService"/>


    <!-- 短链服务 -->
    <bean id="operateService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/memberOSSService/OperateService_1.0.0" />
        <property name="interfaceName" value="com.dianping.mobileossapi.service.operate.OperateService" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="sync" />
        <property name="timeout" value="1000" />
    </bean>

    <!-- 教育大象公众号 -->
    <pigeon:reference id="eduDaxiangSendService"
                      timeout="1000"
                      url="com.dianping.education.lab.base.api.EduDaxiangSendService"
                      interface="com.dianping.education.lab.base.api.EduDaxiangSendService"/>

    <!--商家新老客判断start-->
    <bean id="visitService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/apollo-visit/VisitService" />
        <property name="interfaceName" value="com.dianping.apollo.visit.api.service.VisitService" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="sync" />
        <property name="timeout" value="3000" />
    </bean>
    <!-- 商家新老客判断end -->

    <bean id="rpcUserThirdInfoService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy" lazy-init="false">
        <property name="serviceInterface" value="com.sankuai.wpt.user.thirdinfo.thrift.thirdinfo.ThirdInfoService"/>
        <property name="appKey" value="com.sankuai.medicalcosmetology.scrm.core"/>
        <property name="remoteAppkey" value="com.sankuai.wpt.user.thirdinfo"/>
        <property name="remoteServerPort" value="6491"/>
        <property name="timeout" value="500"/>
    </bean>

    <bean id="caseFacadeService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <!-- 设置为类全限定名即可 -->
        <property name="url" value="com.dianping.csc.pacific.proxy.api.CaseFacadeService"/>
        <!-- 设置为类全限定名即可 -->
        <property name="interfaceName" value="com.dianping.csc.pacific.proxy.api.CaseFacadeService" />
        <!-- 超时时间(毫秒) -->
        <property name="timeout" value="3000" />
    </bean>

    <!-- 节假日服务 start -->
    <bean id="mtHolidayService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" init-method="init" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.holiday.thrift.iface.MtHolidayService"/> <!-- 接口名 -->
        <property name="appKey" value="baby-customer-service"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.corehr.holiday"/>  <!-- 目标Server Appkey  -->
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>
    <!-- 节假日服务 end -->


    <bean id="dupImgTService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="appKey" value="com.sankuai.medicalcosmetology.scrm.core"/>
        <property name="serviceInterface" value="com.sankuai.edfu.service.DupImgTService"/> <!-- 接口名，需要保证接口全包名相同，通常需要打公共的SDK，这里为了简便，只是把包路径移动到了与provider中相同的地方 -->
        <property name="remoteAppkey" value="com.sankuai.horus.edfu"/>  <!-- 目标Server Appkey  -->
        <property name="nettyIO" value="true"/>
        <property name="timeout" value="4000"/>
    </bean>

    <bean id="groupgeoServiceRPCClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.service.mobile.group.geo.thrift.message.RPCGroupGeoService"/>
        <property name="timeout" value="3000"/>
        <property name="appKey" value="com.sankuai.medicalcosmetology.scrm.core"/>
        <property name="remoteAppkey" value="com.meituan.service.mobile.groupgeo"/>
        <property name="nettyIO" value="true"/>
    </bean>
    <bean id="geoThriftLoader" class="com.meituan.service.mobile.group.geo.loader.impl.GeoInfoThriftLoader">
        <property name="rPCGroupGeoServiceIface" ref="groupgeoServiceRPCClient"/>
    </bean>
    <bean id="cityService" class="com.meituan.service.mobile.group.geo.service.CityService"
          init-method="init" destroy-method="destroy">
        <property name="loader" ref="geoThriftLoader"/>
    </bean>
    <bean id="areaService" class="com.meituan.service.mobile.group.geo.service.AreaService"
          init-method="init">
        <property name="loader" ref="geoThriftLoader"/>
    </bean>
    <!-- persona -->
    <bean id="personaCrowdServiceClient" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.persona.crowd.thrift.PersonaCrowdService"/>
        <!-- thrift rpc 超时时间（毫秒）-->
        <property name="timeout" value="20000"/>
        <!-- 响应消息允许最大字节数（1.1.7版本开始支持）：可不配置，默认值16384000，如果有网页源码需求，这个可以加大 -->
        <property name="maxResponseMessageBytes" value="134217728"/>
        <property name="remoteAppkey" value="com.sankuai.nlpml.udm.utvsapi"/>
        <property name="filterByServiceName" value="true"/>
        <!-- 接入方的Appkey -->
        <property name="appKey" value="com.sankuai.medicalcosmetology.scrm.core"/>
    </bean>

    <!-- 根据订单对优惠码、地推等渠道进行识别的服务 -->
    <pigeon:reference id="orderIntentionQueryService"
                      timeout="1000"
                      interface="com.sankuai.carnation.distribution.intention.service.OrderIntentionQueryService"
                      url="com.sankuai.carnation.distribution.intention.service.OrderIntentionQueryService"/>

    <!-- 类目查询服务 -->
    <bean id="poiCategoryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/poi-cateproperty-service/poiCategoryService_1.0.0" />
        <property name="interfaceName" value="com.dianping.poi.cateproperty.api.service.POICategoryService" />
        <property name="timeout" value="1000" />
        <property name="serialize" value="hessian" />
        <property name="callType" value="sync" />
    </bean>

    <!-- 类目服务 -->
    <bean id="poiShopCategoryQueryService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/poi-shopcateprop-service/poiShopCategoryQueryService_1.0.0" />
        <property name="interfaceName" value="com.dianping.poi.shopcateprop.api.service.PoiShopCategoryQueryService" />
        <property name="serialize" value="hessian" />
        <property name="timeout" value="1000" />
    </bean>

    <!-- 城市转换服务 -->
    <bean id="cityTransformService" class="com.dianping.pigeon.remoting.invoker.config.spring.ReferenceBean" init-method="init">
        <property name="url" value="http://service.dianping.com/com.dianping.poi.transform.CityTransformService_1.0.0"/>
        <property name="interfaceName" value="com.dianping.poi.transform.CityTransformService"/>
        <property name="serialize" value="hessian"/>
        <property name="callType" value="sync"/>
        <property name="timeout" value="1000"/>
    </bean>
    <bean id="horusVideoServiceIf" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.horus.service.VideoServiceIf"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.medicalcosmetology.scrm.core"/>  <!-- 本地appkey com.sankuai.nlpml.ml.img com.sankuai.medicalcosmetology.scrm.core -->
        <property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  <!-- 目标 Server Appkey  -->
        <property name="timeout" value="5000"/>
        <property name="nettyIO" value="true"/>
        <!--        <property name="serverIpPorts" value="127.0.0.1:9001" />-->
        <!--        <property name="serverIpPorts" value="************:9002"/>-->
        <property name="remoteUniProto" value="true"/>
    </bean>


    <!-- 订单服务 -->
    <bean id="getUnifiedOrderService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/orderService/query/getUnifiedOrderService_1.0.0"/>
        <property name="iface" value="com.dianping.pay.order.service.query.GetUnifiedOrderService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>

    <!-- 咨询师工具服务 -->
    <pigeon:reference id="privateLiveConsultantAccountService"
                      timeout="5000"
                      interface="com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantAccountService"
                      url="com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantAccountService"/>

    <!-- 咨询师归因服务 -->
    <pigeon:reference id="privateLiveUserIntentionService"
                      timeout="5000"
                      interface="com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveUserIntentionService"
                      url="com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveUserIntentionService"/>

    <!-- 判断是否咨询师的服务 -->
    <pigeon:reference id="privateLiveConsultantVerifyService"
                      timeout="5000"
                      interface="com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantVerifyService"
                      url="com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantVerifyService"/>
    <!-- 咨询师工具服务 -->
    <pigeon:reference id="privateLiveConsultantSummaryService"
                      timeout="5000"
                      interface="com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantSummaryService"
                      url="com.sankuai.carnation.distribution.privatelive.consultant.service.PrivateLiveConsultantSummaryService"/>


    <!-- 直播间服务 -->
    <pigeon:reference id="liveRoomRpcService"
                      timeout="5000"
                      interface="com.sankuai.dzrtc.dzrtc.privatelive.biz.api.service.liveroomadmin.LiveRoomRpcService"
                      url="com.sankuai.dzrtc.dzrtc.privatelive.biz.api.service.liveroomadmin.LiveRoomRpcService"/>

    <pigeon:reference id="saaSConfigRpcService"
                      timeout="5000"
                      interface="com.sankuai.dzrtc.dzrtc.privatelive.biz.api.service.saasconfig.SaaSConfigRpcService"
                      url="com.sankuai.dzrtc.dzrtc.privatelive.biz.api.service.saasconfig.SaaSConfigRpcService"/>

    <!-- 商品池商品服务 -->
    <pigeon:reference id="commonSelectPoolItemService"
                      timeout="5000"
                      interface="com.sankuai.medicalcosmetology.product.selectify.api.service.CommonSelectPoolItemService"
                      url="com.sankuai.medicalcosmetology.product.selectify.api.service.CommonSelectPoolItemService"/>

    <!-- 商品池服务 -->
    <pigeon:reference id="commonSelectPoolService"
                      timeout="5000"
                      interface="com.sankuai.medicalcosmetology.product.selectify.api.service.CommonSelectPoolService"
                      url="com.sankuai.medicalcosmetology.product.selectify.api.service.CommonSelectPoolService"/>

    <pigeon:reference id="commonRecommendService"
                      timeout="5000"
                      interface="com.sankuai.medicalcosmetology.product.selectify.api.service.CommonRecommendService"
                      url="com.sankuai.medicalcosmetology.product.selectify.service.CommonRecommendService"/>


    <!-- 手机号、邮箱、银行卡、证件号token接口 -->
    <bean id="tokenAccessThriftService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.conch.certify.tokenaccess.thrift.TokenAccessThriftService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.medicalcosmetology.scrm.core"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.conch.certify.tokenaccess"/>  <!-- 目标Server Appkey  -->
        <property name="filterByServiceName" value="true" />
        <property name="timeout" value="5000" />  <!-- 具体超时时间参考上述重要提醒 -->
        <property name="nettyIO" value="true"/> <!-- 开启 Netty IO  -->
    </bean>

    <!-- 美团后台地理信息服务 行政区服务 -->
    <bean id="adminDivisionClientProxy" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.meituan.gis.remote.service.thrift.TAdminDivisionService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.medicalcosmetology.scrm.core"/> <!-- 业务方appkey -->
        <property name="remoteAppkey" value="com.sankuai.cos.mtgis"/> <!-- 目标 Server Appkey -->
        <property name="remoteServerPort" value="9104" />
        <property name="nettyIO" value="true"/>
        <property name="timeout" value="2000"/><!--超时时间ms-->
    </bean>
    <!-- 团购商品查询服务 -->
    <bean id="queryCenterDealGroupQueryService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.general.product.query.center.client.service.DealGroupQueryService"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.medicalcosmetology.scrm.core"/>  <!-- 本地appkey -->
        <property name="remoteAppkey" value="com.sankuai.productuser.query.center"/>  <!-- 目标Server Appkey  -->
        <property name="filterByServiceName" value="true"/>  <!-- 通过服务名称进行服务发现 -->
        <property name="timeout" value="1500"/>
    </bean>
    <!-- 泛商品查询服务 -->
    <bean id="skuProductService" class="com.dianping.dpsf.spring.ProxyBeanFactory" init-method="init">
        <property name="serviceName" value="http://service.dianping.com/tpfunService/skuProductService_1.0.0"/>
        <property name="iface" value="com.dianping.tpfun.product.api.sku.ProductService"/>
        <property name="serialize" value="hessian"/>
        <property name="callMethod" value="sync"/>
        <property name="timeout" value="2000"/>
    </bean>
    <bean id="idService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.mpproduct.idservice.api.service.IdService"/>
        <property name="appKey" value="com.sankuai.medicalcosmetology.scrm.core"/>
        <property name="remoteAppkey" value="com.sankuai.mpproduct.idservice"/>
        <property name="timeout" value="200"/>
        <property name="async" value="false"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
    </bean>
    <bean id="dupImgFea" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.meituan.horus.service.DupImgFea"/> <!-- 接口名 -->
        <property name="appKey" value="com.sankuai.medicalcosmetology.scrm.core"/>  <!-- 本地appkey com.sankuai.nlpml.ml.img com.sankuai.medicalcosmetology.scrm.core -->
        <property name="remoteAppkey" value="com.sankuai.nlpml.ml.horus"/>  <!-- 目标 Server Appkey  -->
        <property name="timeout" value="5000"/>
        <property name="nettyIO" value="true"/>
        <!--        <property name="serverIpPorts" value="127.0.0.1:9001" />-->
        <!--        <property name="serverIpPorts" value="************:9002"/>-->
        <property name="remoteUniProto" value="true"/>
    </bean>
    <bean id="personaDittoMetaService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy"
          destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.persona.meta.ditto.PersonaDittoMetaService"/>
        <!-- thrift rpc 超时时间（毫秒）-->
        <property name="timeout" value="20000"/>
        <!-- 响应消息允许最大字节数（1.1.7版本开始支持）：可不配置，默认值16384000，如果有网页源码需求，这个可以加大 -->
        <property name="maxResponseMessageBytes" value="134217728"/>
        <property name="remoteAppkey" value="com.sankuai.persona.metaservice"/>
        <property name="nettyIO" value="true"/>
        <property name="filterByServiceName" value="true"/>
        <!-- 接入方的Appkey -->
        <property name="appKey" value="com.sankuai.medicalcosmetology.scrm.core"/>
    </bean>
    <bean id="groupMsgTService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.service.fe.corp.ds.tservice.openapi.msg.GroupMsgTService"/>
        <property name="appKey" value="baby-customer-service"/>
        <property name="remoteAppkey" value="com.sankuai.ds.be.http"/>
        <property name="timeout" value="10000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="filters">
            <list>
                <bean class="com.sankuai.scrm.core.service.envrequestforwarding.filter.OfflineThriftFilter"/>
            </list>
        </property>
    </bean>
    <bean id="friendMsgTService" class="com.meituan.service.mobile.mtthrift.proxy.ThriftClientProxy" destroy-method="destroy">
        <property name="serviceInterface" value="com.sankuai.service.fe.corp.ds.tservice.openapi.msg.FriendMsgTService"/>
        <property name="appKey" value="com.sankuai.medicalcosmetology.scrm.core"/>
        <property name="remoteAppkey" value="com.sankuai.ds.be.http"/>
        <property name="timeout" value="10000"/>
        <property name="filterByServiceName" value="true"/>
        <property name="filters">
            <list>
                <bean class="com.sankuai.scrm.core.service.envrequestforwarding.filter.OfflineThriftFilter"/>
            </list>
        </property>
    </bean>

</beans>