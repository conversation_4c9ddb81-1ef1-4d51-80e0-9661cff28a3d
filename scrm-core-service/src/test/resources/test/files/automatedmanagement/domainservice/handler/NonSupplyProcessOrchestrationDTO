{"id": 52, "name": "测试任务7-1:群客户+定时+客户群群发+非供给类PPT", "processOrchestrationType": 4, "cron": "", "beginTime": 1724937240000, "endTime": 1725110040000, "status": 1, "validVersion": "1725023666460", "updateTime": 1725023666000, "creatorId": "wangxuefei05", "lastUpdaterId": "wangxuefei05", "participationRestrict": true, "participationRestrictionsCycle": 0, "participationRestrictionsTimes": 0, "appId": "yimei", "previewPic": "", "cronComment": "2024-08-30 21:14:00", "executorType": 1, "executorList": [{"executorId": "WangXueFei", "executorName": "王雪飞", "executorType": 2}], "crowdPackType": 3, "crowdPackIdList": [], "groupIdList": ["wrb_61EQAAOszfqOUoA-7mrOzXF0Vg9w"], "groupInfoList": [{"groupName": "军师测试1", "owner": "WangXueFei", "robotList": null, "groupId": "wrb_61EQAAOszfqOUoA-7mrOzXF0Vg9w", "createDate": 1724815828000, "memberCount": 3}], "crowdPackUpdateStrategyInfoDTO": null, "goalDTO": {"id": 222, "checkTime": "2", "checkTimeUnit": "2", "status": 1, "processOrchestrationId": 52, "processOrchestrationVersion": "1725023666460", "goalType": 1, "careNegativeResult": true, "positiveResultHighlightList": [90002, 90003], "negativeResultHighlightList": null, "goalConditionList": [{"id": 103, "filterFieldId": 30001, "operatorId": 15, "param": [], "groupId": 0, "processOrchestrationId": 52, "processOrchestrationVersion": "1725023666460", "processOrchestrationGoalId": 222}]}, "negativeGoalDTO": {"id": 221, "checkTime": "2", "checkTimeUnit": "2", "status": 1, "processOrchestrationId": 52, "processOrchestrationVersion": "1725023666460", "goalType": 2, "careNegativeResult": true, "positiveResultHighlightList": [90002, 90003], "negativeResultHighlightList": null, "goalConditionList": []}, "nodeMediumDTO": {"processOrchestrationNodeDTOList": [{"nodeId": 0, "preNodeId": -1, "nodeType": 4, "id": 418, "processOrchestrationId": 52, "processOrchestrationVersion": "1725023666460", "childrenNodes": [1]}, {"nodeId": 1, "preNodeId": 0, "nodeType": 2, "id": 419, "processOrchestrationId": 52, "processOrchestrationVersion": "1725023666460", "childrenNodes": []}], "conditionMap": {}, "actionMap": {"1": {"id": 177, "actionId": 1, "processOrchestrationId": 52, "processOrchestrationVersion": "1725023666460", "processOrchestrationNodeId": 1, "actionType": 7, "actionSubType": 5, "updateTime": 1725023666000, "contentType": 0, "contentList": null}}, "actionContentMap": {"1-1": [{"id": 176, "actionId": 1, "contentId": 1, "processOrchestrationId": 52, "processOrchestrationVersion": "1725023666460", "processOrchestrationNodeId": 1, "content": "故事是指过往发生的事，包含真实发生过历史，如史书，也包含了从未发生过的虚拟故事，例如电影或小说。有很多种媒介可以乘载故事，例如:文字、声音、及影像等。电影、电视剧、小说、游戏、漫画、ACG中的故事通常称为剧情。故事透过叙述的方式阐述几个情节，对于研究历史上文化的传播与分布具有很大作用。一些研究认为，所有的人类文化都有故事，说故事是普遍存在于所有人类文化的现象，也就是说，说故事是普世文化通则之一。美国作家娥苏拉·勒瑰恩声称“有些伟大的社会不使用轮子；但没有一个社会是不讲故事的”故事是指过往发生的事，包含真实发生过历史，如史书，也包含了从未发生过的虚拟故事，例如电影或小说。有很多种媒介可以乘载故事，例如:文字、声音、及影像等。电影、电视剧、小说、游戏、漫画、ACG中的故事通常称为剧情。故事透过叙述的方式阐述几个情节，对于研究历史上文化的传播与分布具有很大作用。一些研究认为，所有的人类文化都有故事，说故事是普遍存在于所有人类文化的现象，也就是说，说故事是普世文化通则之一。美国作家娥苏拉·勒瑰恩声称“有些伟大的社会不使用轮子；但没有一个社会是不讲故事的” 故事是指过往发生的事，包含真实发生过历史，如史书，也包含了从未发生过的虚拟故事，例如电影或小说。有很多种媒介可以乘载故事，例如:文字、声音、及影像等。电影、电视剧、小说、游戏、漫画、ACG中的故事通常称为剧情。故事透过叙述的方式阐述几个情节，对于研究历史上文化的传播与分布具有很大作用。一些研究认为，所有的人类文化都有故事，说故事是普遍存在于所有人类文化的现象，也就是说，说故事是普世文化通则之一。美国作家娥苏拉·勒瑰恩声称“有些伟大的社会不使用轮子；但没有一个社会是不讲故事的”故事是指过往发生的事，包含真实发生过历史，如史书，也包含了从未发生过的虚拟故事，例如电影或小说。有很多种媒介可以乘载故事，例如:文字、声音、及影像等。电影、电视剧、小说、游戏、漫画、ACG中的故事通常称为剧情。\n故事透过叙述的方式阐述几个情节，对于研究历史上文化的传播与分布具有很大作用。一些研究认为，所有的人类文化都有故事，说故事是普遍存在于所有人类文化的现象，也就是说，说故事是普世文化通则之一。美国作家娥苏拉·勒瑰恩声称“有些伟大的社会不使用轮子；但没有一个社会是不讲故事的”\n故事是指过往发生的事，包含真实发生过历史，如史书，也包含了从未发生过的虚拟故事，例如电影或小说。有很多种媒介可以乘载故事，例如:文字、声音、及影像等。电影、电视剧、小说、游戏、漫画、ACG中的故事通常称为剧情。故事透过叙述的方式阐述几个情节，对于研究历史上文化的传播与分布具有很大作用。一些研究认为，所有的人类文化都有故事，说故事是普遍存在于所有人类文化的现象，也就是说，说故事是普世文化通则之一。美国作家娥苏拉·勒瑰恩声称“有些伟大的社会不使用轮子；但没有一个社会是不讲故事的”故事是指过往发生的事AAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAAA", "updateTime": 1725023666000, "contentType": 0, "attachmentDTOList": null}]}, "actionAttachmentMap": {"1-1-1": [{"id": 195, "contentId": 1, "actionId": 1, "attachmentTypeId": 2, "attachmentContent": "{\"type\":2,\"icon\":\"\",\"title\":\"动画制作，有手就行\",\"desc\":\"测试\",\"appId\":\"\",\"picUrl\":\"https://msstest.sankuai.com/scrm-s3/测试15117984996579435698.png\",\"contentUrl\":\"https://www.google.com.hk/search?newwindow=1&sca_esv=74c3c93a61ef7e5d&sca_upv=1&rlz=1C5GCEM_enCN1106CN1107&q=%E5%8A%A8%E7%94%BB%E8%A7%86%E9%A2%91&tbm=vid&source=lnms&fbs=AEQNm0AaBOazvTRM_Uafu9eNJJzCBjj7Qh2T44wVPlShdsqpCHCf2XxEw8Ao0hZsW9sc8CRAM25-9apNOMH4QfiuKJIrPUf8-hHebYssYVPQ4c1EJG7MxtO9gbseb2ybqj2iBvtzoxjdeaMPCdovdla5yijtWY26aE1w0U11irR-fbVYb7RcFrs&sa=X&ved=2ahUKEwih-Ybcj5eIAxWHm68BHTvFMbsQ0pQJegQIFBAB&biw=1728&bih=959&dpr=2#\",\"originAppId\":\"\"}", "processOrchestrationId": 52, "processOrchestrationVersion": "1725023666460", "processOrchestrationNodeId": 1, "updateTime": null, "attachmentSupplyDetailDTO": null, "attachmentContentDetailDTO": {"title": "动画制作，有手就行", "contentUrl": "https://www.google.com.hk/search?newwindow=1&sca_esv=74c3c93a61ef7e5d&sca_upv=1&rlz=1C5GCEM_enCN1106CN1107&q=%E5%8A%A8%E7%94%BB%E8%A7%86%E9%A2%91&tbm=vid&source=lnms&fbs=AEQNm0AaBOazvTRM_Uafu9eNJJzCBjj7Qh2T44wVPlShdsqpCHCf2XxEw8Ao0hZsW9sc8CRAM25-9apNOMH4QfiuKJIrPUf8-hHebYssYVPQ4c1EJG7MxtO9gbseb2ybqj2iBvtzoxjdeaMPCdovdla5yijtWY26aE1w0U11irR-fbVYb7RcFrs&sa=X&ved=2ahUKEwih-Ybcj5eIAxWHm68BHTvFMbsQ0pQJegQIFBAB&biw=1728&bih=959&dpr=2#", "desc": "测试", "appId": "", "picUrl": "https://msstest.sankuai.com/scrm-s3/测试15117984996579435698.png", "originAppId": "", "icon": ""}}, {"id": 196, "contentId": 1, "actionId": 1, "attachmentTypeId": 1, "attachmentContent": "{\"type\":1,\"icon\":\"\",\"title\":\"\",\"desc\":\"\",\"appId\":\"\",\"picUrl\":\"https://msstest.sankuai.com/scrm-s3/医美53337040631446150592.png\",\"contentUrl\":\"\",\"originAppId\":\"\"}", "processOrchestrationId": 52, "processOrchestrationVersion": "1725023666460", "processOrchestrationNodeId": 1, "updateTime": null, "attachmentSupplyDetailDTO": null, "attachmentContentDetailDTO": {"title": "", "contentUrl": "", "desc": "", "appId": "", "picUrl": "https://msstest.sankuai.com/scrm-s3/医美53337040631446150592.png", "originAppId": "", "icon": ""}}, {"id": 197, "contentId": 1, "actionId": 1, "attachmentTypeId": 5, "attachmentContent": "{\"type\":5,\"icon\":\"\",\"title\":\"\",\"desc\":\"\",\"appId\":\"\",\"picUrl\":\"\",\"contentUrl\":\"https://msstest.sankuai.com/scrm-s3/PPT_new6290191909549650076.pptx\",\"originAppId\":\"\"}", "processOrchestrationId": 52, "processOrchestrationVersion": "1725023666460", "processOrchestrationNodeId": 1, "updateTime": null, "attachmentSupplyDetailDTO": null, "attachmentContentDetailDTO": {"title": "", "contentUrl": "https://msstest.sankuai.com/scrm-s3/PPT_new6290191909549650076.pptx", "desc": "", "appId": "", "picUrl": "", "originAppId": "", "icon": ""}}, {"id": 198, "contentId": 1, "actionId": 1, "attachmentTypeId": 4, "attachmentContent": "{\"type\":4,\"icon\":\"\",\"title\":\"\",\"desc\":\"\",\"appId\":\"\",\"picUrl\":\"\",\"contentUrl\":\"https://msstest.sankuai.com/scrm-s3/视频视频视频视频视频7574291482776808690.mp4\",\"originAppId\":\"\"}", "processOrchestrationId": 52, "processOrchestrationVersion": "1725023666460", "processOrchestrationNodeId": 1, "updateTime": null, "attachmentSupplyDetailDTO": null, "attachmentContentDetailDTO": {"title": "", "contentUrl": "https://msstest.sankuai.com/scrm-s3/视频视频视频视频视频7574291482776808690.mp4", "desc": "", "appId": "", "picUrl": "", "originAppId": "", "icon": ""}}, {"id": 199, "contentId": 1, "actionId": 1, "attachmentTypeId": 3, "attachmentContent": "{\"type\":3,\"icon\":\"\",\"title\":\"自定义小程序测试\",\"desc\":\"\",\"appId\":\"wxde8ac0a21135c07d\",\"picUrl\":\"https://msstest.sankuai.com/scrm-s3/医美5_new1051124086777919484.png\",\"contentUrl\":\"/gc/pages/deal/dealdetail/dealdetail&dealid=705900953\",\"originAppId\":\"\"}", "processOrchestrationId": 52, "processOrchestrationVersion": "1725023666460", "processOrchestrationNodeId": 1, "updateTime": null, "attachmentSupplyDetailDTO": null, "attachmentContentDetailDTO": {"title": "自定义小程序测试", "contentUrl": "/gc/pages/deal/dealdetail/dealdetail&dealid=705900953", "desc": "", "appId": "wxde8ac0a21135c07d", "picUrl": "https://msstest.sankuai.com/scrm-s3/医美5_new1051124086777919484.png", "originAppId": "", "icon": ""}}]}}, "executePlanDTO": {"id": 120, "processOrchestrationId": 52, "processOrchestrationVersion": "1725023666460", "processOrchestrationType": 4, "taskStartTime": 1725023940000, "status": 2, "updateTime": null}}