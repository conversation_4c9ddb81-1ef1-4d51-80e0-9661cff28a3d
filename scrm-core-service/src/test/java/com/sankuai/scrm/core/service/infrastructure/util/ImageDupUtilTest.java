package com.sankuai.scrm.core.service.infrastructure.util;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.scrm.core.service.BaseMockTest;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/9/20 17:12
 * @Version v1.0.0
 */
public class ImageDupUtilTest extends BaseMockTest {
    @Mock
    private RedisStoreClient redisClient;

    @InjectMocks
    private ImageDupUtil imageDupUtil;
    @Mock
    private ImageDupFeaUtil imageDupFeaUtil;

    /**
     * 测试isSameWxImg方法，当输入的URL为空时应返回false
     */
    @Test
    public void testIsSameWxImgWithEmptyUrls() {
        // arrange
        String destImgUrl = "";
        String refImgUrl = "";

        // act
        boolean result = imageDupUtil.isSameWxImg(destImgUrl, refImgUrl);

        // assert
        assertFalse("当输入的URL为空时，应返回false", result);
    }

    /**
     * 测试isSameWxImg方法，当Redis中已有缓存时应直接返回缓存结果
     */
    @Test
    public void testIsSameWxImgWithCache() {
        // arrange
        String destImgUrl = "http://example.com/dest.jpg";
        String refImgUrl = "http://example.com/ref.jpg";
        StoreKey storeKey = new StoreKey("pchat_image_compare", destImgUrl.hashCode(), refImgUrl.hashCode());
        when(redisClient.get(storeKey)).thenReturn(true);

        // act
        boolean result = imageDupUtil.isSameWxImg(destImgUrl, refImgUrl);

        // assert
        assertTrue("当Redis中已有缓存时，应直接返回缓存结果", result);
        verify(redisClient, times(1)).get(storeKey);
    }

    /**
     * 测试isSameWxImg方法，当Redis中无缓存且图片相同时应返回true
     */
    @Test
    public void testIsSameWxImgWithNoCacheAndImagesAreSame() {

        // arrange
        String destImgUrl = "http://example.com/dest.jpg";
        String refImgUrl = "http://example.com/ref.jpg";
        StoreKey storeKey = new StoreKey("pchat_image_compare", destImgUrl.hashCode(), refImgUrl.hashCode());
        when(redisClient.get(storeKey)).thenReturn(null);
        when(redisClient.set(any(StoreKey.class), anyBoolean())).thenReturn(true);

        // Mock内部调用的isSameImageAfterCompress方法
        ImageDupUtil spyImageDupUtil = spy(imageDupUtil);
        when(imageDupFeaUtil.isEnabled()).thenReturn(false);
        doReturn(true).when(spyImageDupUtil).isSameImageAfterCompress(destImgUrl, refImgUrl);

        // act
        boolean result = spyImageDupUtil.isSameWxImg(destImgUrl, refImgUrl);

        // assert
        assertTrue("当Redis中无缓存且图片相同时应返回true", result);
        verify(redisClient, times(1)).set(storeKey, true);
    }

    /**
     * 测试isSameWxImg方法，当Redis中无缓存且图片不同时应返回false
     */
    @Test
    public void testIsSameWxImgWithNoCacheAndImagesAreNotSame() {

        // arrange
        String destImgUrl = "http://example.com/dest.jpg";
        String refImgUrl = "http://example.com/ref.jpg";
        StoreKey storeKey = new StoreKey("pchat_image_compare", destImgUrl.hashCode(), refImgUrl.hashCode());
        when(redisClient.get(storeKey)).thenReturn(null);
        when(redisClient.set(any(StoreKey.class), anyBoolean())).thenReturn(true);

        // Mock内部调用的isSameImageAfterCompress方法
        ImageDupUtil spyImageDupUtil = spy(imageDupUtil);
        doReturn(false).when(spyImageDupUtil).isSameImageAfterCompress(destImgUrl, refImgUrl);
        when(imageDupFeaUtil.isEnabled()).thenReturn(false);
        // act
        boolean result = spyImageDupUtil.isSameWxImg(destImgUrl, refImgUrl);

        // assert
        assertFalse("当Redis中无缓存且图片不同时应返回false", result);
        verify(redisClient, times(1)).set(storeKey, false);
    }

    /**
     * 测试isSameWxImg方法，当Redis操作抛出异常时应正确处理并返回结果
     */
    @Test
    public void testIsSameWxImgWithRedisException() {

        // arrange
        String destImgUrl = "http://example.com/dest.jpg";
        String refImgUrl = "http://example.com/ref.jpg";
        StoreKey storeKey = new StoreKey("pchat_image_compare", destImgUrl.hashCode(), refImgUrl.hashCode());
        when(redisClient.get(storeKey)).thenThrow(new RuntimeException("Redis exception"));

        // Mock内部调用的isSameImageAfterCompress方法
        ImageDupUtil spyImageDupUtil = spy(imageDupUtil);
        doReturn(true).when(spyImageDupUtil).isSameImageAfterCompress(destImgUrl, refImgUrl);
        when(imageDupFeaUtil.isEnabled()).thenReturn(false);
        // act
        boolean result = spyImageDupUtil.isSameWxImg(destImgUrl, refImgUrl);

        // assert
        assertTrue("当Redis操作抛出异常时应正确处理并返回结果", result);
        // 确保即使Redis抛出异常，也不影响方法的正常执行
    }
}
