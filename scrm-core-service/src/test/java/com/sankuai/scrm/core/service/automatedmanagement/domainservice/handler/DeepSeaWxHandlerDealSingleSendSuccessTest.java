package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import java.lang.reflect.Method;
import java.util.Date;
import org.junit.*;
import org.junit.rules.ExpectedException;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DeepSeaWxHandlerDealSingleSendSuccessTest {

    @Rule
    public ExpectedException expectedException = ExpectedException.none();

    @InjectMocks
    private DeepSeaWxHandler deepSeaWxHandler;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    private ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO;

    private ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO;

    private String executorId;

    private String taskIdStr;

    private Method method;

    @Before
    public void setUp() throws Exception {
        executorId = "executor123";
        taskIdStr = "task456";
        detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(1L);
        wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(100L);
        // Set up default successful responses
        doReturn(1).when(wxInvokeLogDOMapper).insertSelective(any());
        doReturn(1).when(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
        doReturn(1).when(executeLogDOMapper).updateByExampleSelective(any(), any());
        // Get method reference
        method = DeepSeaWxHandler.class.getDeclaredMethod("dealSingleSendSuccess", String.class, ScrmAmProcessOrchestrationWxInvokeDetailDO.class, ScrmAmProcessOrchestrationWxInvokeLogDO.class, String.class);
        method.setAccessible(true);
    }

    /**
     * Test successful execution with all valid parameters
     */
    @Test
    public void testDealSingleSendSuccess_NormalCase() throws Throwable {
        // arrange
        ArgumentCaptor<ScrmAmProcessOrchestrationWxInvokeLogDO> logCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationWxInvokeLogDO.class);
        ArgumentCaptor<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationWxInvokeDetailDO.class);
        ArgumentCaptor<ScrmAmProcessOrchestrationExecuteLogDO> executeLogCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationExecuteLogDO.class);
        // act
        method.invoke(deepSeaWxHandler, executorId, detailDO, wxInvokeLogDO, taskIdStr);
        // assert
        verify(wxInvokeLogDOMapper).insertSelective(logCaptor.capture());
        verify(wxInvokeDetailDOMapper).updateByExampleSelective(detailCaptor.capture(), any());
        verify(executeLogDOMapper).updateByExampleSelective(executeLogCaptor.capture(), any());
        ScrmAmProcessOrchestrationWxInvokeLogDO capturedLog = logCaptor.getValue();
        assertEquals(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue(), capturedLog.getStatus().byteValue());
        assertEquals(taskIdStr, capturedLog.getJobid());
        ScrmAmProcessOrchestrationWxInvokeDetailDO capturedDetail = detailCaptor.getValue();
        assertEquals(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue(), capturedDetail.getStatus().byteValue());
        assertEquals(wxInvokeLogDO.getId(), capturedDetail.getInvokeLogId());
        ScrmAmProcessOrchestrationExecuteLogDO capturedExecuteLog = executeLogCaptor.getValue();
        assertEquals(executorId, capturedExecuteLog.getExecutorId());
        assertEquals(ScrmProcessOrchestrationExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue(), capturedExecuteLog.getStatus().byteValue());
        assertNotNull(capturedExecuteLog.getUpdateTime());
    }

    /**
     * Test when executeLogDO update fails
     */
    @Test
    public void testDealSingleSendSuccess_WhenExecuteLogUpdateFails() throws Throwable {
        // arrange
        doReturn(0).when(executeLogDOMapper).updateByExampleSelective(any(), any());
        // act
        method.invoke(deepSeaWxHandler, executorId, detailDO, wxInvokeLogDO, taskIdStr);
        // assert
        verify(wxInvokeLogDOMapper).insertSelective(any());
        verify(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper).updateByExampleSelective(any(), any());
    }

    /**
     * Test with null executorId
     */
    @Test
    public void testDealSingleSendSuccess_WithNullExecutorId() throws Throwable {
        // arrange
        ArgumentCaptor<ScrmAmProcessOrchestrationExecuteLogDO> executeLogCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationExecuteLogDO.class);
        // act
        method.invoke(deepSeaWxHandler, null, detailDO, wxInvokeLogDO, taskIdStr);
        // assert
        verify(executeLogDOMapper).updateByExampleSelective(executeLogCaptor.capture(), any());
        assertNotNull(executeLogCaptor.getValue().getUpdateTime());
    }

    /**
     * Test with null taskIdStr
     */
    @Test
    public void testDealSingleSendSuccess_WithNullTaskIdStr() throws Throwable {
        // arrange
        ArgumentCaptor<ScrmAmProcessOrchestrationWxInvokeLogDO> logCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationWxInvokeLogDO.class);
        // act
        method.invoke(deepSeaWxHandler, executorId, detailDO, wxInvokeLogDO, null);
        // assert
        verify(wxInvokeLogDOMapper).insertSelective(logCaptor.capture());
        assertNull(logCaptor.getValue().getJobid());
    }
}
