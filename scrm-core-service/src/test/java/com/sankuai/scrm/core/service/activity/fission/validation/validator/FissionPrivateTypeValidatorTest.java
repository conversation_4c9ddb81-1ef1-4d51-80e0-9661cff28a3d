package com.sankuai.scrm.core.service.activity.fission.validation.validator;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.activity.fission.request.ActivityBaseInfoRequest;
import com.sankuai.dz.srcm.activity.fission.request.GroupFissionActivityRequest;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionActivity;
import com.sankuai.scrm.core.service.activity.fission.domain.GroupFissionActivityDomainService;
import com.sankuai.scrm.core.service.activity.fission.validation.exception.FissionValidatorException;
import com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity.FriendChannelDynamicCode;
import com.sankuai.scrm.core.service.friend.dynamiccode.domain.FriendChannelDynamicCodeDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class FissionPrivateTypeValidatorTest {

    @Mock
    private FriendChannelDynamicCodeDomainService friendChannelDynamicCodeDomainService;

    @Mock
    private GroupFissionActivityDomainService groupFissionActivityDomainService;

    @InjectMocks
    private FissionPrivateTypeValidator validator;

    private GroupFissionActivityRequest request;

    private ActivityBaseInfoRequest baseInfo;

    @BeforeEach
    void setUp() {
        request = new GroupFissionActivityRequest();
        baseInfo = new ActivityBaseInfoRequest();
        request.setActivityBaseInfo(baseInfo);
    }

    /**
     * Test when friendChannelDynamicCodeId is null
     */
    @Test
    public void testValidateWhenFriendChannelDynamicCodeIdIsNull() {
        // arrange
        baseInfo.setFriendChannelDynamicCodeId(null);
        // act & assert
        FissionValidatorException exception = assertThrows(FissionValidatorException.class, () -> validator.validate(request, "operation"));
        assertEquals("好友渠道码未填写", exception.getMessage());
    }

    /**
     * Test when friendChannelDynamicCodeId is zero
     */
    @Test
    public void testValidateWhenFriendChannelDynamicCodeIdIsZero() {
        // arrange
        baseInfo.setFriendChannelDynamicCodeId(0L);
        // act & assert
        FissionValidatorException exception = assertThrows(FissionValidatorException.class, () -> validator.validate(request, "operation"));
        assertEquals("好友渠道码未填写", exception.getMessage());
    }

    /**
     * Test when friend channel dynamic code is not found
     */
    @Test
    public void testValidateWhenFriendChannelDynamicCodeNotFound() {
        // arrange
        baseInfo.setFriendChannelDynamicCodeId(123L);
        when(friendChannelDynamicCodeDomainService.queryFriendChannelDynamicCode(123L)).thenReturn(null);
        // act & assert
        FissionValidatorException exception = assertThrows(FissionValidatorException.class, () -> validator.validate(request, "operation"));
        assertEquals("找不到好友渠道码", exception.getMessage());
    }

    /**
     * Test when dynamic code poster is blank
     */
    @Test
    public void testValidateWhenDynamicCodePosterIsBlank() {
        // arrange
        baseInfo.setFriendChannelDynamicCodeId(123L);
        FriendChannelDynamicCode code = new FriendChannelDynamicCode();
        code.setDynamicCodePoster("");
        when(friendChannelDynamicCodeDomainService.queryFriendChannelDynamicCode(123L)).thenReturn(code);
        // act & assert
        FissionValidatorException exception = assertThrows(FissionValidatorException.class, () -> validator.validate(request, "operation"));
        assertEquals("好友渠道码海报为空，请完善海报信息", exception.getMessage());
    }

    /**
     * Test when friend channel code is used in another activity
     */
    @Test
    public void testValidateWhenFriendChannelCodeUsedInOtherActivity() {
        // arrange
        baseInfo.setFriendChannelDynamicCodeId(123L);
        FriendChannelDynamicCode code = new FriendChannelDynamicCode();
        code.setDynamicCodePoster("poster-url");
        code.setId(123L);
        when(friendChannelDynamicCodeDomainService.queryFriendChannelDynamicCode(123L)).thenReturn(code);
        when(groupFissionActivityDomainService.existFriendChannelOtherActivity(123L)).thenReturn(new GroupFissionActivity());
        // act & assert
        FissionValidatorException exception = assertThrows(FissionValidatorException.class, () -> validator.validate(request, "operation"));
        assertEquals("好友渠道码不能重复在不同活动中使用", exception.getMessage());
    }

    /**
     * Test happy path - all validations pass
     */
    @Test
    public void testValidateWhenAllValidationsPass() {
        // arrange
        baseInfo.setFriendChannelDynamicCodeId(123L);
        FriendChannelDynamicCode code = new FriendChannelDynamicCode();
        code.setDynamicCodePoster("poster-url");
        code.setId(123L);
        when(friendChannelDynamicCodeDomainService.queryFriendChannelDynamicCode(123L)).thenReturn(code);
        when(groupFissionActivityDomainService.existFriendChannelOtherActivity(123L)).thenReturn(null);
        // act & assert
        assertDoesNotThrow(() -> validator.validate(request, "operation"));
    }
}
