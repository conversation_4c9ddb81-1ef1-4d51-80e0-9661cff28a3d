package com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionContentDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActionContentDO;
import org.junit.Assert;
import org.junit.Test;
import java.util.Date;

public class ScrmProcessOrchestrationActionContentConverterTest {

    private ScrmProcessOrchestrationActionContentConverter converter = new ScrmProcessOrchestrationActionContentConverter();

    /**
     * 测试 convertToDTO 方法，当输入为 null 时，应返回 null
     */
    @Test
    public void testConvertToDTOWhenInputIsNull() {
        // arrange
        ScrmAmProcessOrchestrationActionContentDO input = null;
        // act
        ScrmProcessOrchestrationActionContentDTO result = converter.convertToDTO(input);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试 convertToDTO 方法，当输入不为 null 时，应返回一个新的 ScrmProcessOrchestrationActionContentDTO 对象，其属性值与输入对象的属性值相同
     */
    @Test
    public void testConvertToDTOWhenInputIsNotNull() {
        // arrange
        ScrmAmProcessOrchestrationActionContentDO input = new ScrmAmProcessOrchestrationActionContentDO();
        input.setId(1L);
        input.setActionId(2);
        input.setContentId(3);
        input.setProcessOrchestrationId(4L);
        input.setProcessOrchestrationVersion("5");
        input.setProcessOrchestrationNodeId(6L);
        input.setContent("content");
        input.setUpdateTime(new Date());
        input.setContentType((byte) 7);
        // act
        ScrmProcessOrchestrationActionContentDTO result = converter.convertToDTO(input);
        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(input.getId(), result.getId());
        Assert.assertEquals(input.getActionId(), result.getActionId());
        Assert.assertEquals(input.getContentId(), result.getContentId());
        Assert.assertEquals(input.getProcessOrchestrationId(), result.getProcessOrchestrationId());
        Assert.assertEquals(input.getProcessOrchestrationVersion(), result.getProcessOrchestrationVersion());
        Assert.assertEquals(input.getProcessOrchestrationNodeId(), result.getProcessOrchestrationNodeId());
        Assert.assertEquals(input.getContent(), result.getContent());
        Assert.assertEquals(input.getUpdateTime(), result.getUpdateTime());
        Assert.assertEquals(Integer.valueOf(input.getContentType().intValue()), result.getContentType());
    }
}
