package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecutePlanPackStatusEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration.ScrmProcessOrchestrationExecutePlanConverter;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackAndProcessMapDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackBaseInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutePlanDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackAndProcessMapDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecutePlanDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackAndProcessMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackBaseInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationExecutePlanDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackUpdateLockService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.RefinementOperationExecuteMessageProducer;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmSupportedFilterFieldOperatorTypeEnum;
import com.sankuai.scrm.core.service.tag.dal.example.ExternalContactTagExample;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackUpdateStrategyDO;
import com.sankuai.scrm.core.service.user.enums.ScrmUserTagEnum;
import java.util.HashSet;
import java.util.Set;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackDetailInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.utils.ConditionUtils;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.infrastructure.dal.babymapper.ContactUserDoMapper;
import com.sankuai.scrm.core.service.infrastructure.dal.entity.ContactUserDo;
import com.sankuai.scrm.core.service.infrastructure.dal.example.ContactUserDoExample;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserTag;
import com.sankuai.scrm.core.service.user.dal.example.ScrmUserTagExample;
import com.sankuai.scrm.core.service.user.dal.mapper.ext.ExtScrmUserTagMapper;
import org.mockito.ArgumentCaptor;

@ExtendWith(MockitoExtension.class)
class ExecuteWriteDomainServiceRunPreparedProcessOrchestrationTaskTest {

    // 后续补充详细测试用例
    @Mock
    private ExtScrmAmProcessOrchestrationExecutePlanDOMapper executePlanDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock
    private ScrmAmCrowdPackAndProcessMapDOMapper crowdPackAndProcessMapDOMapper;

    @Mock
    private ScrmAmCrowdPackBaseInfoDOMapper scrmAmCrowdPackBaseInfoDOMapper;

    @Mock
    private ScrmProcessOrchestrationExecutePlanConverter scrmProcessOrchestrationExecutePlanConverter;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock
    private CrowdPackUpdateLockService crowdPackUpdateLockService;

    @Mock
    private RefinementOperationExecuteMessageProducer refinementOperationExecuteMessageProducer;

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    private ScrmAmProcessOrchestrationExecutePlanDO planDO;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ScrmAmProcessOrchestrationInfoDO infoDO;

    @Mock
    private ExternalContactTagExample.Criteria criteria;

    private final ExecuteWriteDomainService service = new ExecuteWriteDomainService();

    @Mock
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private ExtScrmUserTagMapper userTagDOMapper;

    @Mock
    private ContactUserDoMapper contactUserDoMapper;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private ConditionUtils conditionUtils;

    @BeforeEach
    void setUp() {
        planDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        planDO.setId(1L);
        planDO.setProcessOrchestrationId(1L);
        planDO.setProcessOrchestrationVersion("1.0");
        planDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_TIMED_TASK.getValue().byteValue());
        planDO.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.PREPARING.getValue().byteValue());
        planDO.setPackStatus((byte) 0);
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setValidVersion("1.0");
        infoDO.setAppId("test-app-id");
    }

    /**
     * Tests when no prepared tasks are available
     */
    @Test
    public void testRunPreparedProcessOrchestrationTaskNoTasksAvailable() throws Throwable {
        // arrange
        when(executePlanDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        executeWriteDomainService.runPreparedProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, atLeastOnce()).selectByExample(any());
        verify(processOrchestrationReadDomainService, never()).queryProcessOrchestrationDetailFuture(anyLong());
    }

    /**
     * Tests successful execution of a prepared task
     */
    @Test
    public void testRunPreparedProcessOrchestrationTaskSuccess() throws Throwable {
        // arrange
        // Mock for updateCrowdPackExecuteTwoHoursLater
        when(executePlanDOMapper.selectByExample(any())).thenReturn(Collections.emptyList()).thenReturn(Collections.singletonList(planDO));
        CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(processOrchestrationDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(anyLong())).thenReturn(future);
        when(executePlanDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // Create a partial mock to avoid calling the actual runProcessOrchestrationTask
        ExecuteWriteDomainService partialMock = spy(executeWriteDomainService);
        doNothing().when(partialMock).runProcessOrchestrationTask(any());
        // act
        partialMock.runPreparedProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, atLeastOnce()).selectByExample(any());
        verify(processOrchestrationReadDomainService, atLeastOnce()).queryProcessOrchestrationDetailFuture(anyLong());
        verify(executePlanDOMapper).updateByExampleSelective(any(), any());
        verify(partialMock).runProcessOrchestrationTask(any());
    }

    /**
     * Tests when crowd pack update is needed
     */
    @Test
    public void testRunPreparedProcessOrchestrationTaskWithCrowdPackUpdate() throws Throwable {
        // arrange
        // Setup for updateCrowdPackExecuteTwoHoursLater
        Calendar twoHoursLater = Calendar.getInstance();
        twoHoursLater.add(Calendar.HOUR_OF_DAY, 2);
        ScrmAmProcessOrchestrationExecutePlanDO updatePlanDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        updatePlanDO.setId(2L);
        updatePlanDO.setProcessOrchestrationId(2L);
        updatePlanDO.setProcessOrchestrationVersion("1.0");
        updatePlanDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_TIMED_TASK.getValue().byteValue());
        updatePlanDO.setPackStatus(ScrmProcessOrchestrationExecutePlanPackStatusEnum.UPDATE_NOT_EXECUTED.getCode().byteValue());
        updatePlanDO.setTaskStartTime(new Date());
        // First call for updateCrowdPackExecuteTwoHoursLater, second for getPreparedExecuteScrmProcessOrchestrationDTO
        when(executePlanDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(updatePlanDO)).thenReturn(Collections.singletonList(planDO));
        // Setup for updateCrowdPackExecuteTwoHoursLater
        lenient().when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(anyLong())).thenReturn(infoDO);
        ScrmAmCrowdPackAndProcessMapDO mapDO = new ScrmAmCrowdPackAndProcessMapDO();
        mapDO.setPackId(10L);
        lenient().when(crowdPackAndProcessMapDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(mapDO));
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        // TEMP_PACK
        baseInfoDO.setType((byte) 1);
        lenient().when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(anyLong())).thenReturn(baseInfoDO);
        lenient().when(crowdPackUpdateLockService.getProducerValue(anyLong())).thenReturn(null);
        lenient().when(crowdPackUpdateLockService.tryProducerLock(anyLong(), anyInt())).thenReturn(true);
        lenient().doNothing().when(refinementOperationExecuteMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(any(), any(), any());
        CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(processOrchestrationDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(anyLong())).thenReturn(future);
        when(executePlanDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // Create a partial mock to avoid calling the actual runProcessOrchestrationTask
        ExecuteWriteDomainService partialMock = spy(executeWriteDomainService);
        doNothing().when(partialMock).runProcessOrchestrationTask(any());
        // act
        partialMock.runPreparedProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, atLeastOnce()).selectByExample(any());
        verify(processOrchestrationReadDomainService, atLeastOnce()).queryProcessOrchestrationDetailFuture(anyLong());
        verify(executePlanDOMapper).updateByExampleSelective(any(), any());
        verify(partialMock).runProcessOrchestrationTask(any());
    }

    private boolean invokeSetExternalFilterCriteria(ExternalContactTagExample.Criteria criteria, List<String> param, ScrmSupportedFilterFieldOperatorTypeEnum op) {
        try {
            Method method = ExecuteWriteDomainService.class.getDeclaredMethod("setExternalFilterCriteria", ExternalContactTagExample.Criteria.class, List.class, ScrmSupportedFilterFieldOperatorTypeEnum.class);
            method.setAccessible(true);
            return (boolean) method.invoke(service, criteria, param, op);
        } catch (InvocationTargetException e) {
            // 如果原方法抛出异常，我们认为它应该返回false
            return false;
        } catch (Exception e) {
            // 其他反射相关异常，测试失败
            fail("Reflection error: " + e.getMessage());
            return false;
        }
    }

    @Test
    public void testSetExternalFilterCriteriaBiggerOperatorWithValidParam() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("100");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.BIGGER);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdGreaterThan("100");
    }

    @Test
    public void testSetExternalFilterCriteriaBiggerOrEqualOperatorWithValidParam() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("200");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.BIGGER_OR_EQUAL);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdGreaterThanOrEqualTo("200");
    }

    @Test
    public void testSetExternalFilterCriteriaEqualOperatorWithValidParam() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("300");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.EQUAL);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdEqualTo("300");
    }

    @Test
    public void testSetExternalFilterCriteriaNotEqualOperatorWithValidParam() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("400");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.NOT_EQUAL);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdNotEqualTo("400");
    }

    @Test
    public void testSetExternalFilterCriteriaLessOperatorWithValidParam() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("500");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.LESS);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdLessThan("500");
    }

    @Test
    public void testSetExternalFilterCriteriaLessOrEqualOperatorWithValidParam() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("600");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.LESS_OR_EQUAL);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdLessThanOrEqualTo("600");
    }

    @Test
    public void testSetExternalFilterCriteriaInOperatorWithValidParams() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("1,2,3");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.IN);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdIn(Arrays.asList("1", "2", "3"));
    }

    @Test
    public void testSetExternalFilterCriteriaNotInOperatorWithValidParams() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("4,5,6");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.NOT_IN);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdNotIn(Arrays.asList("4", "5", "6"));
    }

    @Test
    public void testSetExternalFilterCriteriaBetweenOperatorWithValidParams() throws Throwable {
        // arrange
        List<String> param = Arrays.asList("10", "20");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.BETWEEN);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdBetween("10", "20");
    }

    @Test
    public void testSetExternalFilterCriteriaNotBetweenOperatorWithValidParams() throws Throwable {
        // arrange
        List<String> param = Arrays.asList("30", "40");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.NOT_BETWEEN);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdNotBetween("30", "40");
    }

    @Test
    public void testSetExternalFilterCriteriaIsNullOperator() throws Throwable {
        // arrange
        List<String> param = Collections.emptyList();
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.IS_NULL);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdIsNull();
    }

    @Test
    public void testSetExternalFilterCriteriaIsNotNullOperator() throws Throwable {
        // arrange
        List<String> param = Collections.emptyList();
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.IS_NOT_NULL);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdIsNotNull();
    }

    @Test
    public void testSetExternalFilterCriteriaLateThanOperatorWithValidParam() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("2023-01-01");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.LATE_THAN);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdGreaterThan("2023-01-01");
    }

    @Test
    public void testSetExternalFilterCriteriaEarlyThanOperatorWithValidParam() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("2023-12-31");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.EARLY_THAN);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdLessThan("2023-12-31");
    }

    @Test
    public void testSetExternalFilterCriteriaNearOperatorWithValidParam() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("7");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.NEAR);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdGreaterThanOrEqualTo(LocalDate.now().minusDays(7).toString());
    }

    @Test
    public void testSetExternalFilterCriteriaLastXDaysOperatorWithValidParam() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("14");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.LAST_X_DAYS);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdGreaterThanOrEqualTo(LocalDate.now().minusDays(14).toString());
    }

    @Test
    public void testSetExternalFilterCriteriaIsTrueOperator() throws Throwable {
        // arrange
        List<String> param = Collections.emptyList();
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.IS_TRUE);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdEqualTo("1");
    }

    @Test
    public void testSetExternalFilterCriteriaIsNotFalseOperator() throws Throwable {
        // arrange
        List<String> param = Collections.emptyList();
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.IS_NOT_FALSE);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdNotEqualTo("0");
    }

    @Test
    public void testSetExternalFilterCriteriaIsFalseOperator() throws Throwable {
        // arrange
        List<String> param = Collections.emptyList();
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.IS_FALSE);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdEqualTo("0");
    }

    @Test
    public void testSetExternalFilterCriteriaIsNotTrueOperator() throws Throwable {
        // arrange
        List<String> param = Collections.emptyList();
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.IS_NOT_TRUE);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdNotEqualTo("1");
    }

    @Test
    public void testSetExternalFilterCriteriaIsExistOperator() throws Throwable {
        // arrange
        List<String> param = Collections.emptyList();
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.IS_EXIST);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdIsNotNull();
    }

    @Test
    public void testSetExternalFilterCriteriaIsNotExistOperator() throws Throwable {
        // arrange
        List<String> param = Collections.emptyList();
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.IS_NOT_EXIST);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdIsNull();
    }

    @Test
    public void testSetExternalFilterCriteriaLikeOperatorWithValidParam() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("test");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.LIKE);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdLike("%test%");
    }

    @Test
    public void testSetExternalFilterCriteriaNotLikeOperatorWithValidParam() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("example");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.NOT_LIKE);
        // assert
        assertTrue(result);
        verify(criteria).andTagIdNotLike("%example%");
    }

    @Test
    public void testSetExternalFilterCriteriaContainsAllOperator() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("1,2,3");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.CONTAINS_ALL);
        // assert
        assertFalse(result);
        verifyNoInteractions(criteria);
    }

    @Test
    public void testSetExternalFilterCriteriaDefaultCase() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("value");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, null, null);
        // assert
        assertFalse(result);
        verifyNoInteractions(criteria);
    }

    @Test
    public void testSetExternalFilterCriteriaBetweenOperatorWithInsufficientParams() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("10");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.BETWEEN);
        // assert
        assertFalse(result);
        verifyNoInteractions(criteria);
    }

    @Test
    public void testSetExternalFilterCriteriaInOperatorWithEmptyParam() throws Throwable {
        // arrange
        List<String> param = Collections.singletonList("");
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.IN);
        // assert
        // 根据实际代码行为，这里可能返回true，因为空字符串会被分割成一个空数组
        // 我们需要修改断言来匹配实际行为
        assertTrue(result);
        verify(criteria).andTagIdIn(Collections.singletonList(""));
    }

    @Test
    public void testSetExternalFilterCriteriaWithNullParams() throws Throwable {
        // arrange
        List<String> param = null;
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.EQUAL);
        // assert
        assertFalse(result);
        verifyNoInteractions(criteria);
    }

    @Test
    public void testSetExternalFilterCriteriaWithEmptyParamsList() throws Throwable {
        // arrange
        List<String> param = Collections.emptyList();
        // act
        boolean result = invokeSetExternalFilterCriteria(criteria, param, ScrmSupportedFilterFieldOperatorTypeEnum.EQUAL);
        // assert
        assertFalse(result);
        verifyNoInteractions(criteria);
    }

    private ScrmAmCrowdPackUpdateStrategyDO invokeGetFilterFieldId(List<ScrmAmCrowdPackUpdateStrategyDO> values, Set<Long> needFilterFieldIds) throws Exception {
        Method method = ExecuteWriteDomainService.class.getDeclaredMethod("getFilterFieldId", List.class, Set.class);
        method.setAccessible(true);
        return (ScrmAmCrowdPackUpdateStrategyDO) method.invoke(executeWriteDomainService, values, needFilterFieldIds);
    }

    @Test
    void testGetFilterFieldId_EmptyList_ReturnsNull() throws Throwable {
        // arrange
        Set<Long> needFilterFieldIds = new HashSet<>(Arrays.asList(10001L, 10002L));
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Collections.emptyList(), needFilterFieldIds);
        // assert
        assertNull(result);
    }

    @Test
    void testGetFilterFieldId_AllItemsFiltered_ReturnsNull() throws Throwable {
        // arrange
        ScrmAmCrowdPackUpdateStrategyDO item1 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item1.getFilterFieldId()).thenReturn(10001L);
        ScrmAmCrowdPackUpdateStrategyDO item2 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item2.getFilterFieldId()).thenReturn(10002L);
        Set<Long> needFilterFieldIds = new HashSet<>(Arrays.asList(10001L, 10002L));
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Arrays.asList(item1, item2), needFilterFieldIds);
        // assert
        assertNull(result);
        // Verify that getFilterFieldId was called on both items
        verify(item1, atLeastOnce()).getFilterFieldId();
        verify(item2, atLeastOnce()).getFilterFieldId();
    }

    @Test
    void testGetFilterFieldId_ContainsCorpTag_ReturnsCorpTagItem() throws Throwable {
        // arrange
        ScrmAmCrowdPackUpdateStrategyDO item1 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item1.getFilterFieldId()).thenReturn(10001L);
        ScrmAmCrowdPackUpdateStrategyDO corpTagItem = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(corpTagItem.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        Set<Long> needFilterFieldIds = new HashSet<>(Collections.singletonList(10001L));
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Arrays.asList(item1, corpTagItem), needFilterFieldIds);
        // assert
        assertNotNull(result);
        assertEquals(ScrmUserTagEnum.CORP_TAG.getTagId(), result.getFilterFieldId());
        // Verify that getFilterFieldId was called on both items
        verify(item1, atLeastOnce()).getFilterFieldId();
        verify(corpTagItem, atLeastOnce()).getFilterFieldId();
    }

    @Test
    void testGetFilterFieldId_NoCorpTagButOtherItems_ReturnsFirstValidItem() throws Throwable {
        // arrange
        ScrmAmCrowdPackUpdateStrategyDO item1 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item1.getFilterFieldId()).thenReturn(10001L);
        ScrmAmCrowdPackUpdateStrategyDO item2 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item2.getFilterFieldId()).thenReturn(30001L);
        Set<Long> needFilterFieldIds = new HashSet<>(Collections.singletonList(10001L));
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Arrays.asList(item1, item2), needFilterFieldIds);
        // assert
        assertNotNull(result);
        assertEquals(30001L, result.getFilterFieldId());
        // Verify that getFilterFieldId was called on both items
        verify(item1, atLeastOnce()).getFilterFieldId();
        verify(item2, atLeastOnce()).getFilterFieldId();
    }

    @Test
    void testGetFilterFieldId_MultipleItemsWithCorpTag_ReturnsCorpTagItem() throws Throwable {
        // arrange
        ScrmAmCrowdPackUpdateStrategyDO item1 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item1.getFilterFieldId()).thenReturn(10001L);
        ScrmAmCrowdPackUpdateStrategyDO corpTagItem = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(corpTagItem.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        ScrmAmCrowdPackUpdateStrategyDO item2 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item2.getFilterFieldId()).thenReturn(30001L);
        Set<Long> needFilterFieldIds = new HashSet<>(Collections.singletonList(10001L));
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Arrays.asList(item1, corpTagItem, item2), needFilterFieldIds);
        // assert
        assertNotNull(result);
        assertEquals(ScrmUserTagEnum.CORP_TAG.getTagId(), result.getFilterFieldId());
        // Verify that getFilterFieldId was called on all items
        verify(item1, atLeastOnce()).getFilterFieldId();
        verify(corpTagItem, atLeastOnce()).getFilterFieldId();
        verify(item2, atLeastOnce()).getFilterFieldId();
    }

    @Test
    void testGetFilterFieldId_EmptyFilterSet_ReturnsFirstItem() throws Throwable {
        // arrange
        ScrmAmCrowdPackUpdateStrategyDO item1 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item1.getFilterFieldId()).thenReturn(10001L);
        ScrmAmCrowdPackUpdateStrategyDO item2 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item2.getFilterFieldId()).thenReturn(30001L);
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Arrays.asList(item1, item2), Collections.emptySet());
        // assert
        assertNotNull(result);
        assertEquals(10001L, result.getFilterFieldId());
        // Verify that getFilterFieldId was called on both items
        verify(item1, atLeastOnce()).getFilterFieldId();
        verify(item2, atLeastOnce()).getFilterFieldId();
    }

    @Test
    public void testUpdateCrowdPackManualSubTask_EmptyUserUnionIds() throws Throwable {
        List<String> userUnionIds = Collections.emptyList();
        Long crowdPackId = 123L;
        String appId = "app123";
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, crowdPackId, appId);
        assertTrue(result);
        verifyNoInteractions(crowdPackWriteDomainService, crowdPackReadDomainService, executeManagementService);
    }

    @Test
    public void testUpdateCrowdPackManualSubTask_DeletedTempPack() throws Throwable {
        List<String> userUnionIds = Arrays.asList("union1", "union2");
        Long crowdPackId = 123L;
        String appId = "app123";
        when(crowdPackWriteDomainService.isDeletedTempPack(crowdPackId)).thenReturn(true);
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, crowdPackId, appId);
        assertFalse(result);
        verify(crowdPackWriteDomainService).isDeletedTempPack(crowdPackId);
        verifyNoMoreInteractions(crowdPackWriteDomainService);
        verifyNoInteractions(crowdPackReadDomainService, executeManagementService);
    }

    @Test
    public void testUpdateCrowdPackManualSubTask_SuccessfulExecution() throws Throwable {
        List<String> userUnionIds = Arrays.asList("union1", "union2");
        Long crowdPackId = 123L;
        String appId = "app123";
        String corpId = "corp123";
        when(crowdPackWriteDomainService.isDeletedTempPack(crowdPackId)).thenReturn(false);
        ScrmCrowdPackDTO packDTO = new ScrmCrowdPackDTO();
        packDTO.setId(crowdPackId);
        packDTO.setValidPackVersion("v1");
        packDTO.setAppId(appId);
        packDTO.setScrmCrowdPackUpdateStrategyInfoDTO(new ScrmCrowdPackUpdateStrategyInfoDTO());
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(crowdPackId, appId)).thenReturn(packDTO);
        List<ScrmUserTag> userTags = new ArrayList<>();
        ScrmUserTag userTag = new ScrmUserTag();
        userTag.setUserId(456L);
        userTag.setAppId(appId);
        userTags.add(userTag);
        when(userTagDOMapper.selectByExample(any(ScrmUserTagExample.class))).thenReturn(userTags);
        List<CorpAppConfig> configs = new ArrayList<>();
        CorpAppConfig config = new CorpAppConfig();
        config.setAppId(appId);
        config.setCorpId(corpId);
        configs.add(config);
        when(appConfigRepository.getAllConfigs()).thenReturn(configs);
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
        when(conditionUtils.isCrowdPackConditionMatch(eq(packDTO), anyString(), any(), any())).thenReturn(true);
        List<ContactUserDo> contactUserDos = new ArrayList<>();
        ContactUserDo contactUserDo = new ContactUserDo();
        contactUserDo.setExternalUserId("ext123");
        contactUserDos.add(contactUserDo);
        when(contactUserDoMapper.selectByExample(any(ContactUserDoExample.class))).thenReturn(contactUserDos);
        when(crowdPackUpdateLockService.consumerIncrementValue(eq(crowdPackId), anyInt())).thenReturn(2L);
        when(crowdPackUpdateLockService.getProducerValue(crowdPackId)).thenReturn(2L);
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, crowdPackId, appId);
        assertTrue(result);
        verify(executeManagementService).subTaskRunBegin(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), crowdPackId);
        verify(executeManagementService).taskRunFinished(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), crowdPackId);
        verify(crowdPackReadDomainService).queryCrowdPackDetailInfo(crowdPackId, appId);
        verify(userTagDOMapper, times(userUnionIds.size())).selectByExample(any(ScrmUserTagExample.class));
        verify(contactUserDoMapper, atLeast(4)).selectByExample(any(ContactUserDoExample.class));
        ArgumentCaptor<ScrmAmCrowdPackDetailInfoDO> detailCaptor = ArgumentCaptor.forClass(ScrmAmCrowdPackDetailInfoDO.class);
        verify(crowdPackWriteDomainService, times(userUnionIds.size() * 2)).updateCrowdPackDetailInfo(detailCaptor.capture(), eq(true));
        verify(crowdPackUpdateLockService).consumerIncrementValue(crowdPackId, userUnionIds.size());
        verify(crowdPackUpdateLockService).getProducerValue(crowdPackId);
        verify(crowdPackUpdateLockService).deleteConsumerValue(crowdPackId);
        verify(crowdPackUpdateLockService).deleteProducerValue(crowdPackId);
    }

    @Test
    public void testUpdateCrowdPackManualSubTask_EmptyUserTags() throws Throwable {
        List<String> userUnionIds = Collections.singletonList("union1");
        Long crowdPackId = 123L;
        String appId = "app123";
        when(crowdPackWriteDomainService.isDeletedTempPack(crowdPackId)).thenReturn(false);
        ScrmCrowdPackDTO packDTO = new ScrmCrowdPackDTO();
        packDTO.setId(crowdPackId);
        packDTO.setValidPackVersion("v1");
        packDTO.setAppId(appId);
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(crowdPackId, appId)).thenReturn(packDTO);
        when(userTagDOMapper.selectByExample(any(ScrmUserTagExample.class))).thenReturn(Collections.emptyList());
        when(crowdPackUpdateLockService.consumerIncrementValue(eq(crowdPackId), anyInt())).thenReturn(1L);
        when(crowdPackUpdateLockService.getProducerValue(crowdPackId)).thenReturn(1L);
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, crowdPackId, appId);
        assertTrue(result);
        verify(userTagDOMapper).selectByExample(any(ScrmUserTagExample.class));
        verify(crowdPackWriteDomainService, never()).updateCrowdPackDetailInfo(any(), anyBoolean());
        verify(crowdPackUpdateLockService).deleteConsumerValue(crowdPackId);
        verify(crowdPackUpdateLockService).deleteProducerValue(crowdPackId);
    }

    @Test
    public void testUpdateCrowdPackManualSubTask_NoMatchingCondition() throws Throwable {
        List<String> userUnionIds = Collections.singletonList("union1");
        Long crowdPackId = 123L;
        String appId = "app123";
        String corpId = "corp123";
        when(crowdPackWriteDomainService.isDeletedTempPack(crowdPackId)).thenReturn(false);
        ScrmCrowdPackDTO packDTO = new ScrmCrowdPackDTO();
        packDTO.setId(crowdPackId);
        packDTO.setValidPackVersion("v1");
        packDTO.setAppId(appId);
        packDTO.setScrmCrowdPackUpdateStrategyInfoDTO(new ScrmCrowdPackUpdateStrategyInfoDTO());
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(crowdPackId, appId)).thenReturn(packDTO);
        List<ScrmUserTag> userTags = new ArrayList<>();
        ScrmUserTag userTag = new ScrmUserTag();
        userTag.setUserId(456L);
        userTag.setAppId(appId);
        userTags.add(userTag);
        when(userTagDOMapper.selectByExample(any(ScrmUserTagExample.class))).thenReturn(userTags);
        List<CorpAppConfig> configs = new ArrayList<>();
        CorpAppConfig config = new CorpAppConfig();
        config.setAppId(appId);
        config.setCorpId(corpId);
        configs.add(config);
        when(appConfigRepository.getAllConfigs()).thenReturn(configs);
        when(conditionUtils.isCrowdPackConditionMatch(eq(packDTO), anyString(), any(), any())).thenReturn(false);
        when(crowdPackUpdateLockService.consumerIncrementValue(eq(crowdPackId), anyInt())).thenReturn(1L);
        when(crowdPackUpdateLockService.getProducerValue(crowdPackId)).thenReturn(1L);
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, crowdPackId, appId);
        assertTrue(result);
        ArgumentCaptor<ScrmAmCrowdPackDetailInfoDO> detailCaptor = ArgumentCaptor.forClass(ScrmAmCrowdPackDetailInfoDO.class);
        verify(crowdPackWriteDomainService).updateCrowdPackDetailInfo(detailCaptor.capture(), eq(false));
        ScrmAmCrowdPackDetailInfoDO capturedDetail = detailCaptor.getValue();
        assertEquals(crowdPackId, capturedDetail.getPackId());
        assertEquals("v1", capturedDetail.getPackVersion());
        assertEquals(456L, capturedDetail.getExternalUserId());
        assertEquals("union1", capturedDetail.getExternalUserWxUnionId());
        assertEquals(appId, capturedDetail.getAppId());
    }
}
