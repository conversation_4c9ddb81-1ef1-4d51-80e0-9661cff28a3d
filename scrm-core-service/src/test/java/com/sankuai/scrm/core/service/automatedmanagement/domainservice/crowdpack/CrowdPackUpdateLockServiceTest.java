package com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import java.lang.reflect.Field;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.ArgumentMatchers.any;
import org.mockito.ArgumentCaptor;
import static org.mockito.ArgumentMatchers.eq;
import org.junit.jupiter.api.BeforeEach;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;

@ExtendWith(MockitoExtension.class)
class CrowdPackUpdateLockServiceTest {

    @Mock
    private RedisStoreClient redisClient;

    @InjectMocks
    private CrowdPackUpdateLockService crowdPackUpdateLockService;

    private static final Long TEST_PACK_ID = 12345L;

    private static final int TEST_INCREMENT = 5;

    private static final String SCRM_CROWD_PACK_UPDATE_LOCK_CATEGORY = "crowd_pack_update_category";

    private static final String PRODUCER_NUM_LOCK = "producer";

    private String getScrmCrowdPackUpdateLockCategory() throws Exception {
        Field field = CrowdPackUpdateLockService.class.getDeclaredField("SCRM_CROWD_PACK_UPDATE_LOCK_CATEGORY");
        field.setAccessible(true);
        return (String) field.get(null);
    }

    private String getProducerNumLock() throws Exception {
        Field field = CrowdPackUpdateLockService.class.getDeclaredField("PRODUCER_NUM_LOCK");
        field.setAccessible(true);
        return (String) field.get(null);
    }

    /**
     * 测试正常情况 - Redis中有生产者计数值
     */
    @Test
    public void testGetProducerValueWithValueInRedis() throws Throwable {
        // arrange
        Long packId = 12345L;
        Long expectedValue = 10L;
        StoreKey expectedStoreKey = new StoreKey(getScrmCrowdPackUpdateLockCategory(), packId, getProducerNumLock());
        when(redisClient.get(expectedStoreKey)).thenReturn(expectedValue);
        // act
        Long actualValue = crowdPackUpdateLockService.getProducerValue(packId);
        // assert
        assertEquals(expectedValue, actualValue);
        verify(redisClient).get(expectedStoreKey);
    }

    /**
     * 测试正常情况 - Redis中没有生产者计数值(返回null)
     */
    @Test
    public void testGetProducerValueWithNoValueInRedis() throws Throwable {
        // arrange
        Long packId = 12345L;
        StoreKey expectedStoreKey = new StoreKey(getScrmCrowdPackUpdateLockCategory(), packId, getProducerNumLock());
        when(redisClient.get(expectedStoreKey)).thenReturn(null);
        // act
        Long actualValue = crowdPackUpdateLockService.getProducerValue(packId);
        // assert
        assertNull(actualValue);
        verify(redisClient).get(expectedStoreKey);
    }

    /**
     * 测试异常情况 - Redis访问抛出异常
     */
    @Test
    public void testGetProducerValueWhenRedisThrowsException() throws Throwable {
        // arrange
        Long packId = 12345L;
        StoreKey expectedStoreKey = new StoreKey(getScrmCrowdPackUpdateLockCategory(), packId, getProducerNumLock());
        when(redisClient.get(expectedStoreKey)).thenThrow(new RuntimeException("Redis connection failed"));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            crowdPackUpdateLockService.getProducerValue(packId);
        });
        verify(redisClient).get(expectedStoreKey);
    }

    @Test
    public void testDeleteConsumerValueSuccess() throws Throwable {
        // arrange
        Long packId = 12345L;
        when(redisClient.delete(any(StoreKey.class))).thenReturn(true);
        // act
        boolean result = crowdPackUpdateLockService.deleteConsumerValue(packId);
        // assert
        assertTrue(result);
        verify(redisClient).delete(any(StoreKey.class));
    }

    @Test
    public void testDeleteConsumerValueFailure() throws Throwable {
        // arrange
        Long packId = 12345L;
        when(redisClient.delete(any(StoreKey.class))).thenReturn(false);
        // act
        boolean result = crowdPackUpdateLockService.deleteConsumerValue(packId);
        // assert
        assertFalse(result);
        verify(redisClient).delete(any(StoreKey.class));
    }

    @Test
    public void testDeleteConsumerValueWithException() throws Throwable {
        // arrange
        Long packId = 12345L;
        when(redisClient.delete(any(StoreKey.class))).thenThrow(new RuntimeException("Redis connection failed"));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            crowdPackUpdateLockService.deleteConsumerValue(packId);
        });
        verify(redisClient).delete(any(StoreKey.class));
    }

    @Test
    public void testDeleteConsumerValueWithNullPackId() throws Throwable {
        // arrange
        Long packId = null;
        when(redisClient.delete(any(StoreKey.class))).thenReturn(false);
        // act
        boolean result = crowdPackUpdateLockService.deleteConsumerValue(packId);
        // assert
        assertFalse(result);
        verify(redisClient).delete(any(StoreKey.class));
    }

    @Test
    public void testProducerDecrementValueNormalCase() throws Throwable {
        // arrange
        long expectedResult = 10L;
        when(redisClient.decrBy(any(StoreKey.class), eq((long) TEST_INCREMENT))).thenReturn(expectedResult);
        when(redisClient.expire(any(StoreKey.class), eq(60 * 60 * 2))).thenReturn(true);
        // act
        long result = crowdPackUpdateLockService.producerDecrementValue(TEST_PACK_ID, TEST_INCREMENT);
        // assert
        assertEquals(expectedResult, result);
        verify(redisClient).decrBy(any(StoreKey.class), eq((long) TEST_INCREMENT));
        verify(redisClient).expire(any(StoreKey.class), eq(60 * 60 * 2));
    }

    @Test
    public void testProducerDecrementValueWithMinIncrement() throws Throwable {
        // arrange
        long expectedResult = 9L;
        when(redisClient.decrBy(any(StoreKey.class), eq(1L))).thenReturn(expectedResult);
        when(redisClient.expire(any(StoreKey.class), eq(60 * 60 * 2))).thenReturn(true);
        // act
        long result = crowdPackUpdateLockService.producerDecrementValue(TEST_PACK_ID, 1);
        // assert
        assertEquals(expectedResult, result);
        verify(redisClient).decrBy(any(StoreKey.class), eq(1L));
        verify(redisClient).expire(any(StoreKey.class), eq(60 * 60 * 2));
    }

    @Test
    public void testProducerDecrementValueWithLargeIncrement() throws Throwable {
        // arrange
        long expectedResult = -1000L;
        int largeIncrement = Integer.MAX_VALUE;
        when(redisClient.decrBy(any(StoreKey.class), eq((long) largeIncrement))).thenReturn(expectedResult);
        when(redisClient.expire(any(StoreKey.class), eq(60 * 60 * 2))).thenReturn(true);
        // act
        long result = crowdPackUpdateLockService.producerDecrementValue(TEST_PACK_ID, largeIncrement);
        // assert
        assertEquals(expectedResult, result);
        verify(redisClient).decrBy(any(StoreKey.class), eq((long) largeIncrement));
        verify(redisClient).expire(any(StoreKey.class), eq(60 * 60 * 2));
    }

    @Test
    public void testProducerDecrementValueWithZeroIncrement() throws Throwable {
        // arrange
        long expectedResult = 10L;
        when(redisClient.decrBy(any(StoreKey.class), eq(0L))).thenReturn(expectedResult);
        when(redisClient.expire(any(StoreKey.class), eq(60 * 60 * 2))).thenReturn(true);
        // act
        long result = crowdPackUpdateLockService.producerDecrementValue(TEST_PACK_ID, 0);
        // assert
        assertEquals(expectedResult, result);
        verify(redisClient).decrBy(any(StoreKey.class), eq(0L));
        verify(redisClient).expire(any(StoreKey.class), eq(60 * 60 * 2));
    }

    @Test
    public void testProducerDecrementValueWithNegativeIncrement() throws Throwable {
        // arrange
        long expectedResult = 15L;
        int negativeIncrement = -5;
        when(redisClient.decrBy(any(StoreKey.class), eq((long) negativeIncrement))).thenReturn(expectedResult);
        when(redisClient.expire(any(StoreKey.class), eq(60 * 60 * 2))).thenReturn(true);
        // act
        long result = crowdPackUpdateLockService.producerDecrementValue(TEST_PACK_ID, negativeIncrement);
        // assert
        assertEquals(expectedResult, result);
        verify(redisClient).decrBy(any(StoreKey.class), eq((long) negativeIncrement));
        verify(redisClient).expire(any(StoreKey.class), eq(60 * 60 * 2));
    }

    @Test
    public void testProducerDecrementValueWithNullPackId() throws Throwable {
        // arrange
        long expectedResult = 5L;
        when(redisClient.decrBy(any(StoreKey.class), eq((long) TEST_INCREMENT))).thenReturn(expectedResult);
        when(redisClient.expire(any(StoreKey.class), eq(60 * 60 * 2))).thenReturn(true);
        // act
        long result = crowdPackUpdateLockService.producerDecrementValue(null, TEST_INCREMENT);
        // assert
        assertEquals(expectedResult, result);
        verify(redisClient).decrBy(any(StoreKey.class), eq((long) TEST_INCREMENT));
        verify(redisClient).expire(any(StoreKey.class), eq(60 * 60 * 2));
    }

    @Test
    public void testProducerDecrementValueWhenRedisThrowsException() throws Throwable {
        // arrange
        when(redisClient.decrBy(any(StoreKey.class), eq((long) TEST_INCREMENT))).thenThrow(new RuntimeException("Redis error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            crowdPackUpdateLockService.producerDecrementValue(TEST_PACK_ID, TEST_INCREMENT);
        });
        verify(redisClient).decrBy(any(StoreKey.class), eq((long) TEST_INCREMENT));
        verify(redisClient, never()).expire(any(StoreKey.class), anyInt());
    }

    @Test
    public void testGetConsumerValueWhenValueExists() throws Throwable {
        // arrange
        Long packId = 123L;
        Long expectedValue = 5L;
        when(redisClient.get(any(StoreKey.class))).thenReturn(expectedValue);
        // act
        Long result = crowdPackUpdateLockService.getConsumerValue(packId);
        // assert
        assertEquals(expectedValue, result);
        verify(redisClient).get(new StoreKey("crowd_pack_update_category", packId, "consumer"));
    }

    @Test
    public void testGetConsumerValueWhenValueNotExists() throws Throwable {
        // arrange
        Long packId = 123L;
        when(redisClient.get(any(StoreKey.class))).thenReturn(null);
        // act
        Long result = crowdPackUpdateLockService.getConsumerValue(packId);
        // assert
        assertNull(result);
        verify(redisClient).get(new StoreKey("crowd_pack_update_category", packId, "consumer"));
    }

    @Test
    public void testGetConsumerValueWhenPackIdIsNull() throws Throwable {
        // arrange
        when(redisClient.get(any(StoreKey.class))).thenReturn(null);
        // act
        Long result = crowdPackUpdateLockService.getConsumerValue(null);
        // assert
        assertNull(result);
        verify(redisClient).get(new StoreKey("crowd_pack_update_category", null, "consumer"));
    }

    @Test
    public void testGetConsumerValueWhenRedisThrowsException() throws Throwable {
        // arrange
        Long packId = 123L;
        RuntimeException expectedException = new RuntimeException("Redis error");
        when(redisClient.get(any(StoreKey.class))).thenThrow(expectedException);
        // act & assert
        RuntimeException thrownException = assertThrows(RuntimeException.class, () -> {
            crowdPackUpdateLockService.getConsumerValue(packId);
        });
        assertEquals("Redis error", thrownException.getMessage());
        verify(redisClient).get(new StoreKey("crowd_pack_update_category", packId, "consumer"));
    }

    @Test
    public void testGetConsumerValueWhenReturnTypeMismatch() throws Throwable {
        // arrange
        Long packId = 123L;
        String nonLongValue = "not-a-long";
        when(redisClient.get(any(StoreKey.class))).thenReturn(nonLongValue);
        // act & assert
        assertThrows(ClassCastException.class, () -> {
            crowdPackUpdateLockService.getConsumerValue(packId);
        });
        verify(redisClient).get(new StoreKey("crowd_pack_update_category", packId, "consumer"));
    }

    @Test
    public void testConsumerIncrementValueNormalIncrement() throws Throwable {
        // arrange
        Long packId = 123L;
        int increment = 5;
        long expectedResult = 10L;
        StoreKey expectedKey = new StoreKey("crowd_pack_update_category", packId, "consumer");
        when(redisClient.incrBy(any(StoreKey.class), eq((long) increment))).thenReturn(expectedResult);
        // act
        long result = crowdPackUpdateLockService.consumerIncrementValue(packId, increment);
        // assert
        assertEquals(expectedResult, result);
        verify(redisClient).incrBy(expectedKey, increment);
        verify(redisClient).expire(expectedKey, 60 * 60 * 2);
    }

    @Test
    public void testConsumerIncrementValueZeroIncrement() throws Throwable {
        // arrange
        Long packId = 123L;
        int increment = 0;
        long expectedResult = 5L;
        StoreKey expectedKey = new StoreKey("crowd_pack_update_category", packId, "consumer");
        when(redisClient.incrBy(any(StoreKey.class), eq(0L))).thenReturn(expectedResult);
        // act
        long result = crowdPackUpdateLockService.consumerIncrementValue(packId, increment);
        // assert
        assertEquals(expectedResult, result);
        verify(redisClient).incrBy(expectedKey, 0);
        verify(redisClient).expire(expectedKey, 60 * 60 * 2);
    }

    @Test
    public void testConsumerIncrementValueNegativeIncrement() throws Throwable {
        // arrange
        Long packId = 123L;
        int increment = -3;
        long expectedResult = 2L;
        StoreKey expectedKey = new StoreKey("crowd_pack_update_category", packId, "consumer");
        when(redisClient.incrBy(any(StoreKey.class), eq(-3L))).thenReturn(expectedResult);
        // act
        long result = crowdPackUpdateLockService.consumerIncrementValue(packId, increment);
        // assert
        assertEquals(expectedResult, result);
        verify(redisClient).incrBy(expectedKey, -3);
        verify(redisClient).expire(expectedKey, 60 * 60 * 2);
    }

    @Test
    public void testConsumerIncrementValueRedisIncrByException() throws Throwable {
        // arrange
        Long packId = 123L;
        int increment = 5;
        when(redisClient.incrBy(any(StoreKey.class), eq(5L))).thenThrow(new RuntimeException("Redis operation failed"));
        // act
        Exception exception = assertThrows(RuntimeException.class, () -> {
            crowdPackUpdateLockService.consumerIncrementValue(packId, increment);
        });
        // assert
        assertEquals("Redis operation failed", exception.getMessage());
        verify(redisClient).incrBy(any(StoreKey.class), eq(5L));
        verify(redisClient, never()).expire(any(StoreKey.class), anyInt());
    }

    @Test
    public void testConsumerIncrementValueRedisExpireException() throws Throwable {
        // arrange
        Long packId = 123L;
        int increment = 5;
        long expectedResult = 10L;
        when(redisClient.incrBy(any(StoreKey.class), eq(5L))).thenReturn(expectedResult);
        doThrow(new RuntimeException("Expire operation failed")).when(redisClient).expire(any(StoreKey.class), eq(60 * 60 * 2));
        // act
        Exception exception = assertThrows(RuntimeException.class, () -> {
            crowdPackUpdateLockService.consumerIncrementValue(packId, increment);
        });
        // assert
        assertEquals("Expire operation failed", exception.getMessage());
        verify(redisClient).incrBy(any(StoreKey.class), eq(5L));
        verify(redisClient).expire(any(StoreKey.class), eq(60 * 60 * 2));
    }

    @Test
    public void testConsumerIncrementValueNullPackId() throws Throwable {
        // arrange
        Long packId = null;
        int increment = 5;
        long expectedResult = 5L;
        when(redisClient.incrBy(any(StoreKey.class), eq(5L))).thenReturn(expectedResult);
        // act
        long result = crowdPackUpdateLockService.consumerIncrementValue(packId, increment);
        // assert
        assertEquals(expectedResult, result);
        verify(redisClient).incrBy(any(StoreKey.class), eq(5L));
        verify(redisClient).expire(any(StoreKey.class), eq(60 * 60 * 2));
    }

    @Test
    public void testConsumerIncrementValueLargeIncrement() throws Throwable {
        // arrange
        Long packId = 123L;
        int increment = Integer.MAX_VALUE;
        long expectedResult = Integer.MAX_VALUE + 1L;
        StoreKey expectedKey = new StoreKey("crowd_pack_update_category", packId, "consumer");
        when(redisClient.incrBy(any(StoreKey.class), eq((long) increment))).thenReturn(expectedResult);
        // act
        long result = crowdPackUpdateLockService.consumerIncrementValue(packId, increment);
        // assert
        assertEquals(expectedResult, result);
        verify(redisClient).incrBy(expectedKey, increment);
        verify(redisClient).expire(expectedKey, 60 * 60 * 2);
    }

    @Test
    public void testDeleteProducerValueSuccess() throws Throwable {
        // arrange
        Long packId = 12345L;
        StoreKey expectedStoreKey = new StoreKey(SCRM_CROWD_PACK_UPDATE_LOCK_CATEGORY, packId, PRODUCER_NUM_LOCK);
        when(redisClient.delete(expectedStoreKey)).thenReturn(true);
        // act
        boolean result = crowdPackUpdateLockService.deleteProducerValue(packId);
        // assert
        assertTrue(result);
        verify(redisClient).delete(expectedStoreKey);
    }

    @Test
    public void testDeleteProducerValueFailure() throws Throwable {
        // arrange
        Long packId = 12345L;
        StoreKey expectedStoreKey = new StoreKey(SCRM_CROWD_PACK_UPDATE_LOCK_CATEGORY, packId, PRODUCER_NUM_LOCK);
        when(redisClient.delete(expectedStoreKey)).thenReturn(false);
        // act
        boolean result = crowdPackUpdateLockService.deleteProducerValue(packId);
        // assert
        assertFalse(result);
        verify(redisClient).delete(expectedStoreKey);
    }

    @Test
    public void testDeleteProducerValueWithNullPackId() throws Throwable {
        // arrange
        Long packId = null;
        // 明确指定要模拟的delete方法版本
        doThrow(new NullPointerException()).when(redisClient).delete(any(StoreKey.class));
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            crowdPackUpdateLockService.deleteProducerValue(packId);
        });
    }

    @Test
    public void testDeleteProducerValueWithRedisException() throws Throwable {
        // arrange
        Long packId = 12345L;
        StoreKey expectedStoreKey = new StoreKey(SCRM_CROWD_PACK_UPDATE_LOCK_CATEGORY, packId, PRODUCER_NUM_LOCK);
        when(redisClient.delete(expectedStoreKey)).thenThrow(new RuntimeException("Redis error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            crowdPackUpdateLockService.deleteProducerValue(packId);
        });
        verify(redisClient).delete(expectedStoreKey);
    }

    @Test
    void testTryProducerLockSuccess() throws Throwable {
        // arrange
        Long packId = 123L;
        int expireInSeconds = 30;
        when(redisClient.setnx(any(StoreKey.class), eq(0L), eq(expireInSeconds))).thenReturn(true);
        // act
        boolean result = crowdPackUpdateLockService.tryProducerLock(packId, expireInSeconds);
        // assert
        assertTrue(result);
        verify(redisClient).setnx(any(StoreKey.class), eq(0L), eq(expireInSeconds));
    }

    @Test
    void testTryProducerLockFailure() throws Throwable {
        // arrange
        Long packId = 123L;
        int expireInSeconds = 30;
        when(redisClient.setnx(any(StoreKey.class), eq(0L), eq(expireInSeconds))).thenReturn(false);
        // act
        boolean result = crowdPackUpdateLockService.tryProducerLock(packId, expireInSeconds);
        // assert
        assertFalse(result);
        verify(redisClient).setnx(any(StoreKey.class), eq(0L), eq(expireInSeconds));
    }

    @Test
    void testTryProducerLockWithNullPackId() throws Throwable {
        // arrange
        Long packId = null;
        int expireInSeconds = 30;
        when(redisClient.setnx(any(StoreKey.class), eq(0L), eq(expireInSeconds))).thenReturn(true);
        // act
        boolean result = crowdPackUpdateLockService.tryProducerLock(packId, expireInSeconds);
        // assert
        assertTrue(result);
        verify(redisClient).setnx(any(StoreKey.class), eq(0L), eq(expireInSeconds));
    }

    @Test
    void testTryProducerLockWithZeroExpire() throws Throwable {
        // arrange
        Long packId = 123L;
        int expireInSeconds = 0;
        when(redisClient.setnx(any(StoreKey.class), eq(0L), eq(expireInSeconds))).thenReturn(true);
        // act
        boolean result = crowdPackUpdateLockService.tryProducerLock(packId, expireInSeconds);
        // assert
        assertTrue(result);
        verify(redisClient).setnx(any(StoreKey.class), eq(0L), eq(expireInSeconds));
    }

    @Test
    void testTryProducerLockWithRedisException() throws Throwable {
        // arrange
        Long packId = 123L;
        int expireInSeconds = 30;
        when(redisClient.setnx(any(StoreKey.class), eq(0L), eq(expireInSeconds))).thenThrow(new RuntimeException("Redis error"));
        // act & assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> crowdPackUpdateLockService.tryProducerLock(packId, expireInSeconds));
        assertEquals("Redis error", exception.getMessage());
    }

    @Test
    void testTryProducerLockWithNegativeExpire() throws Throwable {
        // arrange
        Long packId = 123L;
        int expireInSeconds = -1;
        when(redisClient.setnx(any(StoreKey.class), eq(0L), eq(expireInSeconds))).thenReturn(true);
        // act
        boolean result = crowdPackUpdateLockService.tryProducerLock(packId, expireInSeconds);
        // assert
        assertTrue(result);
        verify(redisClient).setnx(any(StoreKey.class), eq(0L), eq(expireInSeconds));
    }
}
