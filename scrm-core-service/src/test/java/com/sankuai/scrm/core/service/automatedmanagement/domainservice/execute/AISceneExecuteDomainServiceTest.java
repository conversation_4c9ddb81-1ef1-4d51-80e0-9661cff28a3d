package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAMRealtimeSceneAndProcessMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackUserAddCropTagDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.infrastructure.dal.babymapper.ContactUserDoMapper;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.tag.domain.ExternalContactTagDomainService;
import com.sankuai.scrm.core.service.tag.service.TagServiceImpl;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class AISceneExecuteDomainServiceTest {

    @Mock
    private TagServiceImpl tagService;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock
    private CrowdPackUserAddCropTagDomainService crowdPackUserAddCropTagDomainService;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private ExternalContactTagDomainService externalContactTagDomainService;

    @Mock
    private ScrmAMRealtimeSceneAndProcessMapDOMapper scrmAMRealtimeSceneAndProcessMapDOMapper;

    @Mock
    private ContactUserDoMapper contactUserDoMapper;

    @InjectMocks
    private AISceneExecuteDomainService aiSceneExecuteDomainService;

    private Long processId;

    private String wxUnionId;

    private IntelligentFollowResultDTO aiSceneContent;

    private ScrmProcessOrchestrationDTO processDTO;

    private ExecuteManagementDTO executeManagementDTO;

    @BeforeEach
    void setUp() {
        processId = 1L;
        wxUnionId = "wx123";
        aiSceneContent = new IntelligentFollowResultDTO();
        // Initialize processDTO
        processDTO = new ScrmProcessOrchestrationDTO();
        processDTO.setId(processId);
        processDTO.setValidVersion("1.0");
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(new ArrayList<>(Collections.singletonList(new ScrmProcessOrchestrationNodeDTO())));
        processDTO.setNodeMediumDTO(nodeMediumDTO);
        // Initialize executeManagementDTO
        executeManagementDTO = new ExecuteManagementDTO();
    }

    /**
     * Test when no message sending is needed
     */
    @Test
    void testRunRealTimeTaskWhenNoMessageNeeded() throws Throwable {
        // arrange
        aiSceneContent.setNeedSendMsg(false);
        // act
        StepExecuteResultDTO result = aiSceneExecuteDomainService.runRealTimeTask(0L, wxUnionId, aiSceneContent);
        // assert
        assertTrue(result.isSuccess());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SUCCESS.getCode(), result.getCode());
        verifyNoInteractions(processOrchestrationReadDomainService, executeWriteDomainService, executeManagementService);
    }

    /**
     * Test successful execution path
     */
    @Test
    void testRunRealTimeTaskSuccessfully() throws Throwable {
        // arrange
        aiSceneContent.setNeedSendMsg(true);
        StepExecuteResultDTO expectedResult = new StepExecuteResultDTO();
        expectedResult.setSuccess(true);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(processId)).thenReturn(CompletableFuture.completedFuture(processDTO));
        when(executeManagementService.getExecuteMediumManagementDTO(any())).thenReturn(executeManagementDTO);
        when(executeWriteDomainService.executeRealTimeTask(any(), any(), any(), any(), any(), any())).thenReturn(expectedResult);
        // act
        StepExecuteResultDTO result = aiSceneExecuteDomainService.runRealTimeTask(processId, wxUnionId, aiSceneContent);
        // assert
        assertTrue(result.isSuccess());
        verify(executeWriteDomainService).executeRealTimeTask(eq(processDTO), eq(wxUnionId), any(), any(), any(), eq(executeManagementDTO));
        verify(executeWriteDomainService).secondStaffTaskAssignStep(eq(processDTO), any(), eq(executeManagementDTO), any());
        verify(executeWriteDomainService).thirdWxGroupTouchStep(eq(processDTO), eq(executeManagementDTO), eq(expectedResult));
        verify(executeWriteDomainService).updateNodeExecuteLog(eq(processDTO), eq(executeManagementDTO));
    }

    /**
     * Test timeout scenario
     */
    @Test
    void testRunRealTimeTaskTimeout() throws Throwable {
        // arrange
        aiSceneContent.setNeedSendMsg(true);
        CompletableFuture<ScrmProcessOrchestrationDTO> future = spy(new CompletableFuture<>());
        doThrow(new TimeoutException()).when(future).get(eq(5L), eq(TimeUnit.SECONDS));
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(processId)).thenReturn(future);
        // act & assert
        assertThrows(TimeoutException.class, () -> aiSceneExecuteDomainService.runRealTimeTask(processId, wxUnionId, aiSceneContent));
        verifyNoInteractions(executeWriteDomainService, executeManagementService);
    }

    /**
     * Test interrupted exception scenario
     */
    @Test
    void testRunRealTimeTaskInterruptedException() throws Throwable {
        // arrange
        aiSceneContent.setNeedSendMsg(true);
        CompletableFuture<ScrmProcessOrchestrationDTO> future = spy(new CompletableFuture<>());
        doThrow(new InterruptedException()).when(future).get(eq(5L), eq(TimeUnit.SECONDS));
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(processId)).thenReturn(future);
        // act & assert
        assertThrows(InterruptedException.class, () -> aiSceneExecuteDomainService.runRealTimeTask(processId, wxUnionId, aiSceneContent));
        verifyNoInteractions(executeWriteDomainService, executeManagementService);
    }
}
