package com.sankuai.scrm.core.service.envrequestforwarding.mq.consumer;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.meituan.mafka.client.message.MafkaMessage;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.group.dal.babymapper.GroupInfoEntityMapper;
import com.sankuai.scrm.core.service.group.dal.entity.GroupInfoEntity;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import java.util.Properties;

import com.sankuai.dz.srcm.envrequestforwarding.enums.ConsumerGroupNameEnum;
import com.sankuai.dz.srcm.envrequestforwarding.enums.ResultTypeEnum;
import com.sankuai.dz.srcm.envrequestforwarding.request.DispatchedOnlineDataRequest;

@RunWith(MockitoJUnitRunner.class)
public class OfflineDataSyncAbstractConsumerGetCorpIdTest {

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private GroupInfoEntityMapper groupInfoEntityMapper;

    @InjectMocks
    private OfflineDataSyncAbstractConsumer offlineDataSyncAbstractConsumer = new OfflineDataSyncAbstractConsumer() {

        @Override
        public ResultTypeEnum offlineSyncHandle(DispatchedOnlineDataRequest request) {
            return null;
        }

        @Override
        public ConsumerGroupNameEnum getConsumerGroupNameEnum() {
            return null;
        }
    };

    @Test
    public void testGetCorpId_WhenCorpAppConfigNotNull() throws Throwable {
        String expectedCorpId = "testCorpId";
        String businessCode = "testBusinessCode";
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, "{\"businessCode\":\"" + businessCode + "\"}");
        CorpAppConfig corpAppConfig = new CorpAppConfig();
        corpAppConfig.setCorpId(expectedCorpId);
        when(corpAppConfigRepository.getConfigByBusinessCode(businessCode)).thenReturn(corpAppConfig);
        String result = offlineDataSyncAbstractConsumer.getCorpId(message);
        assertEquals(expectedCorpId, result);
    }
    @Test
    public void testGetCorpId_WhenBusinessCodeIsNull() throws Throwable {
        String expectedCorpId = "ww4c59cf2eafaf02fd";
        String businessCode = "";
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, "{\"timeStamp\":\"1731652450\",\"decryptXml\":\"<xml><WelcomeCode>yC3UT2Vd1jCIDB5FSkFskeZBbGLtG_yAsN6pd0-ra9c</WelcomeCode><State>47893</State><UserID>LiuAo</UserID><FromUserName>sys</FromUserName><ToUserName>ww4c59cf2eafaf02fd</ToUserName><CreateTime>1731652450</CreateTime><Event>change_external_contact</Event><MsgType>event</MsgType><ExternalUserID>wm8sspCgAAdrkGT5eDWuaDIcN8VGG5FA</ExternalUserID><ChangeType>add_external_contact</ChangeType></xml>\"}");
        CorpAppConfig corpAppConfig = new CorpAppConfig();
        corpAppConfig.setCorpId(expectedCorpId);
//        when(corpAppConfigRepository.getConfigByBusinessCode(businessCode)).thenReturn(corpAppConfig);
        String result = offlineDataSyncAbstractConsumer.getCorpId(message);
        assertEquals(expectedCorpId, result);
    }
    @Test
    public void testOnlineAndCorpIdCheck(){


        boolean result = offlineDataSyncAbstractConsumer.onlineAndCorpIdCheck("invalidCorpId");
        assertFalse(result);
    }
    @Test
    public void testOnlineDataDispatch(){
        String businessCode = "testBusinessCode";
        MafkaMessage message = new MafkaMessage("topic", 0, 0L, null, "{\"businessCode\":\"" + businessCode + "\"}");
        Properties properties= new Properties();
        offlineDataSyncAbstractConsumer.onlineDataDispatch(message,properties);
    }

}
