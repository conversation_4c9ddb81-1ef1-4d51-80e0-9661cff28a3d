package com.sankuai.scrm.core.service.external.contact.mq.consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import java.util.Properties;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MobileAddFriendRealTimeRpcResultConsumerTest {

    @InjectMocks
    private MobileAddFriendRealTimeRpcResultConsumer consumer;

    /**
     * Test normal successful initialization
     */
    @Test
    public void testAfterPropertiesSetSuccess() throws Exception {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            // Arrange
            IConsumerProcessor mockConsumer = mock(IConsumerProcessor.class);
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenReturn(mockConsumer);
            // Act
            consumer.afterPropertiesSet();
            // Assert
            mockedMafkaClient.verify(() -> {
                Properties props = new Properties();
                props.setProperty(ConsumerConstants.MafkaBGNamespace, "pingtai");
                props.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
                props.setProperty(ConsumerConstants.SubscribeGroup, "scrm.external.contact.add.friend.result.notify.consumer");
                MafkaClient.buildConsumerFactory(props, "deepsea.mobile.add.friend.result.notify");
            });
            verify(mockConsumer).recvMessageWithParallel(eq(String.class), any());
        }
    }

    /**
     * Test when MafkaClient.buildConsumerFactory throws exception
     */
    @Test
    public void testAfterPropertiesSetWhenBuildConsumerFactoryFails() throws Exception {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            // Arrange
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenThrow(new RuntimeException("Build failed"));
            // Act & Assert
            assertThrows(RuntimeException.class, () -> consumer.afterPropertiesSet());
        }
    }

    /**
     * Test when recvMessageWithParallel throws exception
     */
    @Test
    public void testAfterPropertiesSetWhenRecvMessageFails() throws Exception {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            // Arrange
            IConsumerProcessor mockConsumer = mock(IConsumerProcessor.class);
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenReturn(mockConsumer);
            doThrow(new RuntimeException("Listener setup failed")).when(mockConsumer).recvMessageWithParallel(any(), any());
            // Act & Assert
            assertThrows(RuntimeException.class, () -> consumer.afterPropertiesSet());
        }
    }

    /**
     * Test properties are set correctly
     */
    @Test
    public void testAfterPropertiesSetPropertiesAreCorrect() throws Exception {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            // Arrange
            IConsumerProcessor mockConsumer = mock(IConsumerProcessor.class);
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenAnswer(invocation -> {
                Properties props = invocation.getArgument(0);
                assertEquals("pingtai", props.getProperty(ConsumerConstants.MafkaBGNamespace));
                assertEquals("com.sankuai.medicalcosmetology.scrm.core", props.getProperty(ConsumerConstants.MafkaClientAppkey));
                assertEquals("scrm.external.contact.add.friend.result.notify.consumer", props.getProperty(ConsumerConstants.SubscribeGroup));
                return mockConsumer;
            });
            // Act
            consumer.afterPropertiesSet();
            // Assert
            mockedMafkaClient.verify(() -> MafkaClient.buildConsumerFactory(any(Properties.class), eq("deepsea.mobile.add.friend.result.notify")));
        }
    }
}
