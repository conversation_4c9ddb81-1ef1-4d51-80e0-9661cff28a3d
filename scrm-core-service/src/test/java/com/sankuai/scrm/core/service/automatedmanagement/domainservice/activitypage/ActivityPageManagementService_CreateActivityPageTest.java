package com.sankuai.scrm.core.service.automatedmanagement.domainservice.activitypage;

import com.sankuai.dz.srcm.automatedmanagement.dto.activitypage.ActivityPageCreatePagesDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductActivityPageDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductTagsDOMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ActivityPageManagementService_CreateActivityPageTest {

    @InjectMocks
    private ActivityPageManagementService activityPageManagementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductActivityPageDOMapper activityPageDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductTagsDOMapper productTagsDOMapper;

    private ActivityPageCreatePagesDTO activityPageCreatePagesDTO;

    @Before
    public void setUp() {
        activityPageCreatePagesDTO = new ActivityPageCreatePagesDTO();
        activityPageCreatePagesDTO.setActivityPageTitle("test");
        activityPageCreatePagesDTO.setProductIds(Arrays.asList(1L, 2L, 3L));
    }

    @Test(expected = Exception.class)
    public void testCreateActivityPageAppIdIsNull() throws Throwable {
        activityPageManagementService.createActivityPage(null, activityPageCreatePagesDTO);
    }

    @Test(expected = Exception.class)
    public void testCreateActivityPageActivityPageCreatePagesDTOIsNull() throws Throwable {
        activityPageManagementService.createActivityPage("test", null);
    }

    @Test(expected = Exception.class)
    public void testCreateActivityPageActivityPageTitleIsNull() throws Throwable {
        activityPageCreatePagesDTO.setActivityPageTitle(null);
        activityPageManagementService.createActivityPage("test", activityPageCreatePagesDTO);
    }

    @Test(expected = Exception.class)
    public void testCreateActivityPageActivityPageIsExist() throws Throwable {
        when(activityPageDOMapper.countByExample(any())).thenReturn(1L);
        activityPageManagementService.createActivityPage("test", activityPageCreatePagesDTO);
    }

    @Test(expected = Exception.class)
    public void testCreateActivityPageInsertTagError() throws Throwable {
        when(activityPageDOMapper.countByExample(any())).thenReturn(0L);
        // Simulate insert tag error
        when(productTagsDOMapper.insertSelective(any())).thenReturn(0);
        activityPageManagementService.createActivityPage("test", activityPageCreatePagesDTO);
    }

    @Test(expected = Exception.class)
    public void testCreateActivityPageInsertProductTagsRelationsByIdsError() throws Throwable {
        when(activityPageDOMapper.countByExample(any())).thenReturn(0L);
        when(productTagsDOMapper.insertSelective(any())).thenReturn(1);
        // Simulate insert product tags relations error
        when(productTagsDOMapper.insertSelective(any())).thenThrow(new RuntimeException("insert product tags relations error"));
        activityPageManagementService.createActivityPage("test", activityPageCreatePagesDTO);
    }

    @Test(expected = Exception.class)
    public void testCreateActivityPageInsertPageByDTOError() throws Throwable {
        when(activityPageDOMapper.countByExample(any())).thenReturn(0L);
        when(productTagsDOMapper.insertSelective(any())).thenReturn(1);
        // Simulate insert page error
        activityPageManagementService.createActivityPage("test", activityPageCreatePagesDTO);
    }
}
