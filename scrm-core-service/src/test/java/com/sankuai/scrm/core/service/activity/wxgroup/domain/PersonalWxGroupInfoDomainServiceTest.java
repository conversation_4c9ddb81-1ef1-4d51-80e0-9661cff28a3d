package com.sankuai.scrm.core.service.activity.wxgroup.domain;

import com.sankuai.dz.srcm.activity.wxgroup.dto.FuzzyQueryWxGroupInfoDTO;
import com.sankuai.scrm.core.service.activity.fission.domain.GroupFissionActivityDomainService;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmDSPersonalWxGroupInfoDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.example.ScrmActivityAndDSPersonalWxGroupRelationMapDOExample;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.example.ScrmDSPersonalWxGroupInfoDOExample;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmActivityAndDSPersonalWxGroupRelationMapDOMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmDSPersonalWxGroupInfoDOMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.dto.UpdateResultDTO;
import com.sankuai.scrm.core.service.infrastructure.mq.bo.corpwxgroupchange.OpenAssistantSimpleInfo;
import com.sankuai.scrm.core.service.infrastructure.mq.bo.corpwxgroupchange.OpenGroupChangeEvent;
import com.sankuai.scrm.core.service.infrastructure.mq.bo.corpwxgroupchange.OpenGroupSimpleInfo;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.service.fe.corp.wx.crm.TResponse.BaseTResponse;
import com.sankuai.service.fe.corp.wx.crm.dto.merchant.Group2PoiDTO;
import com.sankuai.service.fe.corp.wx.crm.tservice.MerchantCrmDomainTService;
import com.sankuai.service.fe.corp.wx.thrift.CorpWxService;
import com.sankuai.service.fe.corp.wx.thrift.GetQrCodeForWeekResponse;
import com.sankuai.service.fe.corp.wx.thrift.QrCodeInfo;
import org.apache.thrift.TException;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

/**
 * Unit tests for {@link PersonalWxGroupInfoDomainService}.
 */
@ExtendWith(MockitoExtension.class)
public class PersonalWxGroupInfoDomainServiceTest {

    @InjectMocks
    private PersonalWxGroupInfoDomainService personalWxGroupInfoDomainService;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private CorpWxService.Iface corpWxService;

    @Mock
    private GroupFissionActivityDomainService activityDomainService;

    @Mock
    private MerchantCrmDomainTService merchantCrmDomainTService;

    @Mock
    private ScrmActivityAndDSPersonalWxGroupRelationMapDOMapper personalWxGroupRelationMapDOMapper;

    @Mock
    private ScrmDSPersonalWxGroupInfoDOMapper personalWxGroupInfoDOMapper;

    @Captor
    private ArgumentCaptor<ScrmDSPersonalWxGroupInfoDOExample> groupInfoExampleCaptor;

    @Captor
    private ArgumentCaptor<ScrmActivityAndDSPersonalWxGroupRelationMapDOExample> relationMapExampleCaptor;

    @Captor
    private ArgumentCaptor<ScrmDSPersonalWxGroupInfoDO> groupInfoCaptor;

    @Nested
    @DisplayName("queryGroupInfoByDsGroupIdList 方法测试")
    public class QueryGroupInfoByDsGroupIdListTest {

        @Test
        @DisplayName("当 dsGroupIdList 为空时，应返回空列表")
        public void shouldReturnEmptyListWhenDsGroupIdListIsEmpty() {
            // 准备
            List<Long> emptyList = Collections.emptyList();

            // 执行
            List<ScrmDSPersonalWxGroupInfoDO> result = personalWxGroupInfoDomainService.queryGroupInfoByDsGroupIdList(emptyList);

            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            verify(personalWxGroupInfoDOMapper, never()).selectByExample(any());
        }

        @Test
        @DisplayName("当 dsGroupIdList 为null时，应返回空列表")
        public void shouldReturnEmptyListWhenDsGroupIdListIsNull() {
            // 执行
            List<ScrmDSPersonalWxGroupInfoDO> result = personalWxGroupInfoDomainService.queryGroupInfoByDsGroupIdList(null);

            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            verify(personalWxGroupInfoDOMapper, never()).selectByExample(any());
        }

        @Test
        @DisplayName("当 dsGroupIdList 有效时，应返回正确的群组信息列表")
        public void shouldReturnGroupInfoListWhenDsGroupIdListIsValid() {
            // 准备
            List<Long> dsGroupIdList = Arrays.asList(1001L, 1002L);
            
            ScrmDSPersonalWxGroupInfoDO group1 = new ScrmDSPersonalWxGroupInfoDO();
            group1.setId(1L);
            group1.setDsGroupId(1001L);
            group1.setGroupName("测试群1");
            group1.setStatus(1);
            
            ScrmDSPersonalWxGroupInfoDO group2 = new ScrmDSPersonalWxGroupInfoDO();
            group2.setId(2L);
            group2.setDsGroupId(1002L);
            group2.setGroupName("测试群2");
            group2.setStatus(1);
            
            List<ScrmDSPersonalWxGroupInfoDO> mockGroups = Arrays.asList(group1, group2);
            
            when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(mockGroups);
            
            // 执行
            List<ScrmDSPersonalWxGroupInfoDO> result = personalWxGroupInfoDomainService.queryGroupInfoByDsGroupIdList(dsGroupIdList);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertEquals(2, result.size(), "结果应包含2个群组");
            assertEquals("测试群1", result.get(0).getGroupName());
            assertEquals("测试群2", result.get(1).getGroupName());
            
            verify(personalWxGroupInfoDOMapper).selectByExample(groupInfoExampleCaptor.capture());
            ScrmDSPersonalWxGroupInfoDOExample capturedExample = groupInfoExampleCaptor.getValue();
            
            // 验证查询条件
            assertEquals(dsGroupIdList, capturedExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
            assertEquals(1, capturedExample.getOredCriteria().get(0).getCriteria().get(1).getValue());
        }
    }

    @Nested
    @DisplayName("queryGroupInfoByWxGroupId 方法测试")
    public class QueryGroupInfoByWxGroupIdTest {

        @Test
        @DisplayName("当wxGroupId为空时，应返回null")
        public void shouldReturnNullWhenWxGroupIdIsEmpty() {
            // 执行
            ScrmDSPersonalWxGroupInfoDO result = personalWxGroupInfoDomainService.queryGroupInfoByWxGroupId("");
            
            // 验证
            assertNull(result, "当wxGroupId为空时应返回null");
            verify(personalWxGroupInfoDOMapper, never()).selectByExample(any());
        }
        
        @Test
        @DisplayName("当wxGroupId为null时，应返回null")
        public void shouldReturnNullWhenWxGroupIdIsNull() {
            // 执行
            ScrmDSPersonalWxGroupInfoDO result = personalWxGroupInfoDomainService.queryGroupInfoByWxGroupId(null);
            
            // 验证
            assertNull(result, "当wxGroupId为null时应返回null");
            verify(personalWxGroupInfoDOMapper, never()).selectByExample(any());
        }
        
        @Test
        @DisplayName("当群组存在时，应返回正确的群组信息")
        public void shouldReturnCorrectGroupInfo() {
            // 准备
            String wxGroupId = "wxid_test_group";
            
            ScrmDSPersonalWxGroupInfoDO groupDO = new ScrmDSPersonalWxGroupInfoDO();
            groupDO.setId(1L);
            groupDO.setCorpId("ww123456");
            groupDO.setDsGroupId(1001L);
            groupDO.setWxGroupId(wxGroupId);
            groupDO.setGroupName("测试群组");
            groupDO.setCurrentMemberCount(50);
            groupDO.setOwner("zhangsan");
            
            List<ScrmDSPersonalWxGroupInfoDO> mockResult = Collections.singletonList(groupDO);
            when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(mockResult);
            
            // 执行
            ScrmDSPersonalWxGroupInfoDO result = personalWxGroupInfoDomainService.queryGroupInfoByWxGroupId(wxGroupId);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertEquals(groupDO.getDsGroupId(), result.getDsGroupId(), "群组ID应匹配");
            assertEquals(groupDO.getGroupName(), result.getGroupName(), "群组名称应匹配");
            assertEquals(groupDO.getWxGroupId(), result.getWxGroupId(), "微信群ID应匹配");
            assertEquals(groupDO.getCurrentMemberCount(), result.getCurrentMemberCount(), "当前成员数应匹配");
            
            // 验证查询条件
            verify(personalWxGroupInfoDOMapper).selectByExample(groupInfoExampleCaptor.capture());
            
            ScrmDSPersonalWxGroupInfoDOExample capturedExample = groupInfoExampleCaptor.getValue();
            List<ScrmDSPersonalWxGroupInfoDOExample.Criteria> criteria = capturedExample.getOredCriteria();
            
            assertEquals(wxGroupId, criteria.get(0).getCriteria().get(0).getValue(), "查询条件应包含正确的wxGroupId");
        }
        
        @Test
        @DisplayName("当群组不存在时，应返回null")
        public void shouldReturnNullWhenGroupNotFound() {
            // 准备
            String wxGroupId = "wxid_nonexistent_group";
            
            when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
            
            // 执行
            ScrmDSPersonalWxGroupInfoDO result = personalWxGroupInfoDomainService.queryGroupInfoByWxGroupId(wxGroupId);
            
            // 验证
            assertNull(result, "当群组不存在时应返回null");
            
            // 验证查询条件
            verify(personalWxGroupInfoDOMapper).selectByExample(groupInfoExampleCaptor.capture());
            
            ScrmDSPersonalWxGroupInfoDOExample capturedExample = groupInfoExampleCaptor.getValue();
            List<ScrmDSPersonalWxGroupInfoDOExample.Criteria> criteria = capturedExample.getOredCriteria();
            
            ScrmDSPersonalWxGroupInfoDO group1 = new ScrmDSPersonalWxGroupInfoDO();
            group1.setId(1L);
            group1.setWxGroupId(wxGroupId);
            group1.setGroupName("测试群1");
            group1.setStatus(1);
            
            ScrmDSPersonalWxGroupInfoDO group2 = new ScrmDSPersonalWxGroupInfoDO();
            group2.setId(2L);
            group2.setWxGroupId(wxGroupId);
            group2.setGroupName("测试群2");
            group2.setStatus(1);
            
            List<ScrmDSPersonalWxGroupInfoDO> mockGroups = Arrays.asList(group1, group2);
            
            when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(mockGroups);
            
            // 执行
            ScrmDSPersonalWxGroupInfoDO result2 = personalWxGroupInfoDomainService.queryGroupInfoByWxGroupId(wxGroupId);
            
            // 验证
            assertNotNull(result2, "结果不应为null");
            assertEquals(wxGroupId, result2.getWxGroupId());
            assertEquals("测试群1", result2.getGroupName()); // 应返回第一个
            
            verify(personalWxGroupInfoDOMapper,times(2)).selectByExample(groupInfoExampleCaptor.capture());
        }

        @Test
        @DisplayName("当没有找到群组信息时，应返回null")
        public void shouldReturnNullWhenNoGroupFound() {
            // 准备
            String wxGroupId = "test_wx_group_id";
            
            when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
            
            // 执行
            ScrmDSPersonalWxGroupInfoDO result = personalWxGroupInfoDomainService.queryGroupInfoByWxGroupId(wxGroupId);
            
            // 验证
            assertNull(result, "结果应为null");
            
            verify(personalWxGroupInfoDOMapper).selectByExample(groupInfoExampleCaptor.capture());
            ScrmDSPersonalWxGroupInfoDOExample capturedExample = groupInfoExampleCaptor.getValue();
            
            // 验证查询条件
            assertEquals(wxGroupId, capturedExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
            assertEquals(1, capturedExample.getOredCriteria().get(0).getCriteria().get(1).getValue());
        }
    }

    @Nested
    @DisplayName("updateWxGroup 方法测试")
    public class UpdateWxGroupTest {

        private OpenGroupChangeEvent createBasicChangeEvent() {
            OpenGroupChangeEvent changeEvent = new OpenGroupChangeEvent();
            changeEvent.setCorpId("test_corp_id");
            changeEvent.setOrgId(1234);
            changeEvent.setBusinessCode("test_business_code");
            changeEvent.setEventTime(System.currentTimeMillis());
            
            OpenGroupSimpleInfo groupInfo = new OpenGroupSimpleInfo();
            groupInfo.setDsGroupId(1001L);
            groupInfo.setWxGroupId("test_wx_group_id");
            groupInfo.setGroupName("测试群");
            groupInfo.setNotice("测试公告");
            groupInfo.setGroupType(3); // 个人微信群
            groupInfo.setMemberCount(10);
            List<OpenAssistantSimpleInfo> assistants = new ArrayList<>();
            OpenAssistantSimpleInfo assistant = new OpenAssistantSimpleInfo();
            assistant.setDsAssistantId(1001L);
            assistant.setAssistantName("test_assistant");
            assistants.add(assistant);
            groupInfo.setAssistantList(assistants);
            
            changeEvent.setOpenGroupSimpleInfo(groupInfo);
            
            return changeEvent;
        }
        
        private void setupCorpConfigAndGetPoiMocks(String corpId) {
            // 模拟企业配置
            CorpAppConfig config = new CorpAppConfig();
            config.setCorpId(corpId);
            when(corpAppConfigRepository.getAllConfigs()).thenReturn(Collections.singletonList(config));
            
            // 模拟获取POI信息
            Group2PoiDTO poiDTO = new Group2PoiDTO();
            poiDTO.setPoiId(5678L);
            poiDTO.setPoiName("测试门店");
            BaseTResponse<List<Group2PoiDTO>> poiResponse = new BaseTResponse<>();
            poiResponse.setCode(0);
            poiResponse.setData(Collections.singletonList(poiDTO));
            try {
                when(merchantCrmDomainTService.getPoiByGroup(anyList(), anyString())).thenReturn(poiResponse);
            } catch (TException e) {
                fail("Mock设置失败: " + e.getMessage());
            }
        }
        
        private void setupGetQrCodeMock() {
            // 模拟获取群二维码
            QrCodeInfo qrCode = new QrCodeInfo();
            qrCode.setQrcodeUrlCdn("https://test.qrcode.url");
            GetQrCodeForWeekResponse qrResponse = new GetQrCodeForWeekResponse();
            qrResponse.setCode(0);
            qrResponse.setData(qrCode);
            try {
                when(corpWxService.getQrCodeForWeek(any())).thenReturn(qrResponse);
            } catch (TException e) {
                fail("Mock设置失败: " + e.getMessage());
            }
        }

        @Test
        @DisplayName("当 changeEvent 为null时，应返回失败的结果")
        public void shouldReturnFailureWhenChangeEventIsNull() {
            // 执行
            UpdateResultDTO result = personalWxGroupInfoDomainService.updateWxGroup(null);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertFalse(result.isSuccess(), "结果应表示失败");
            verify(personalWxGroupInfoDOMapper, never()).selectByExample(any());
            verify(personalWxGroupInfoDOMapper, never()).insert(any());
            verify(personalWxGroupInfoDOMapper, never()).updateByPrimaryKeySelective(any());
        }
        
        @Test
        @DisplayName("当群类型不是个人微信群(type!=3)时，应返回失败的结果")
        public void shouldReturnFailureWhenGroupTypeIsNotPersonalWxGroup() {
            // 准备
            OpenGroupChangeEvent changeEvent = createBasicChangeEvent();
            changeEvent.getOpenGroupSimpleInfo().setGroupType(1); // 设置为非个人微信群类型
            
            // 执行
            UpdateResultDTO result = personalWxGroupInfoDomainService.updateWxGroup(changeEvent);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertFalse(result.isSuccess(), "结果应表示失败");
            verify(personalWxGroupInfoDOMapper, never()).selectByExample(any());
            verify(personalWxGroupInfoDOMapper, never()).insert(any());
            verify(personalWxGroupInfoDOMapper, never()).updateByPrimaryKeySelective(any());
        }
        
        @Test
        @DisplayName("当企业ID不在配置列表中时，应返回失败的结果")
        public void shouldReturnFailureWhenCorpIdNotInConfigs() {
            // 准备
            OpenGroupChangeEvent changeEvent = createBasicChangeEvent();
            
            // 模拟企业配置，但配置中没有当前企业ID
            CorpAppConfig config = new CorpAppConfig();
            config.setCorpId("other_corp_id");
            when(corpAppConfigRepository.getAllConfigs()).thenReturn(Collections.singletonList(config));
            
            // 执行
            UpdateResultDTO result = personalWxGroupInfoDomainService.updateWxGroup(changeEvent);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertFalse(result.isSuccess(), "结果应表示失败");
            verify(personalWxGroupInfoDOMapper, never()).selectByExample(any());
            verify(personalWxGroupInfoDOMapper, never()).insert(any());
            verify(personalWxGroupInfoDOMapper, never()).updateByPrimaryKeySelective(any());
        }
        
        @Test
        @DisplayName("处理 entrust 类型事件 - 群被托管，应新增群信息")
        public void shouldInsertGroupInfoWhenDsChangeTypeIsEntrust() throws TException {
            // 准备
            OpenGroupChangeEvent changeEvent = createBasicChangeEvent();
            changeEvent.setDsChangeType("entrust");
            
            // 设置企业配置和POI信息Mock
            setupCorpConfigAndGetPoiMocks(changeEvent.getCorpId());
            setupGetQrCodeMock();
            
            // 模拟数据库中不存在该群
            when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
            
            // 模拟插入成功
            when(personalWxGroupInfoDOMapper.insert(any())).thenReturn(1);
            
            // 执行
            UpdateResultDTO result = personalWxGroupInfoDomainService.updateWxGroup(changeEvent);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isSuccess(), "结果应表示成功");
            assertTrue(result.isNeedInit(), "应标记为需要初始化");
            
            verify(personalWxGroupInfoDOMapper).selectByExample(any());
            verify(personalWxGroupInfoDOMapper).insert(groupInfoCaptor.capture());
            verify(personalWxGroupInfoDOMapper, never()).updateByPrimaryKeySelective(any());
            
            // 验证插入的数据
            ScrmDSPersonalWxGroupInfoDO capturedGroup = groupInfoCaptor.getValue();
            assertEquals(changeEvent.getCorpId(), capturedGroup.getCorpId());
            assertEquals(changeEvent.getOpenGroupSimpleInfo().getDsGroupId(), capturedGroup.getDsGroupId());
            assertEquals(changeEvent.getOpenGroupSimpleInfo().getWxGroupId(), capturedGroup.getWxGroupId());
            assertEquals(1, capturedGroup.getStatus()); // 托管状态为启用
            assertNotNull(capturedGroup.getGroupQrCode(), "群二维码不应为null");
            assertEquals(5678L, capturedGroup.getPoiId().longValue(), "应设置正确的POI ID");
            assertEquals("测试门店", capturedGroup.getPoiName(), "应设置正确的POI名称");
        }
        
        @Test
        @DisplayName("处理 un_entrust 类型事件 - 群解除托管，应更新群状态")
        public void shouldUpdateGroupStatusWhenDsChangeTypeIsUnEntrust() {
            // 准备
            OpenGroupChangeEvent changeEvent = createBasicChangeEvent();
            changeEvent.setDsChangeType("un_entrust");
            changeEvent.setEventTime(174495556686L);
            
            // 设置企业配置Mock
            setupCorpConfigAndGetPoiMocks(changeEvent.getCorpId());
            
            // 模拟数据库中已存在该群
            ScrmDSPersonalWxGroupInfoDO existingGroup = new ScrmDSPersonalWxGroupInfoDO();
            existingGroup.setId(1L);
            existingGroup.setDsGroupId(changeEvent.getOpenGroupSimpleInfo().getDsGroupId());
            existingGroup.setWxGroupId(changeEvent.getOpenGroupSimpleInfo().getWxGroupId());
            existingGroup.setCorpId(changeEvent.getCorpId());
            existingGroup.setStatus(1); // 当前状态为托管
            existingGroup.setEventTime(174495556683L);
            
            when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(existingGroup));
            
            // 模拟更新成功
            when(personalWxGroupInfoDOMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
            
            // 执行
            UpdateResultDTO result = personalWxGroupInfoDomainService.updateWxGroup(changeEvent);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isSuccess(), "结果应表示成功");
            assertFalse(result.isNeedInit(), "不应标记为需要初始化");
            
            verify(personalWxGroupInfoDOMapper).selectByExample(any());
            verify(personalWxGroupInfoDOMapper, never()).insert(any());
            verify(personalWxGroupInfoDOMapper).updateByPrimaryKeySelective(groupInfoCaptor.capture());
            
            // 验证更新的数据
            ScrmDSPersonalWxGroupInfoDO capturedGroup = groupInfoCaptor.getValue();
            assertEquals(0, capturedGroup.getStatus(), "状态应更新为非托管(0)");
        }
        
        @Test
        @DisplayName("处理 update 类型事件 - 群信息变更，应更新群信息")
        public void shouldUpdateGroupInfoWhenDsChangeTypeIsUpdate() throws TException {
            // 准备
            OpenGroupChangeEvent changeEvent = createBasicChangeEvent();
            changeEvent.setDsChangeType("update");
            changeEvent.setEventTime(174495556686L);
            
            // 设置企业配置和POI信息Mock
            setupCorpConfigAndGetPoiMocks(changeEvent.getCorpId());
            setupGetQrCodeMock();
            
            // 模拟数据库中已存在该群
            ScrmDSPersonalWxGroupInfoDO existingGroup = new ScrmDSPersonalWxGroupInfoDO();
            existingGroup.setId(1L);
            existingGroup.setDsGroupId(changeEvent.getOpenGroupSimpleInfo().getDsGroupId());
            existingGroup.setWxGroupId(changeEvent.getOpenGroupSimpleInfo().getWxGroupId());
            existingGroup.setCorpId(changeEvent.getCorpId());
            existingGroup.setGroupName("旧群名称");
            existingGroup.setGroupNotice("旧公告");
            existingGroup.setStatus(1);
            existingGroup.setEventTime(174495556683L);
            
            when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(existingGroup));
            
            // 模拟更新成功
            when(personalWxGroupInfoDOMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
            
            // 执行
            UpdateResultDTO result = personalWxGroupInfoDomainService.updateWxGroup(changeEvent);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isSuccess(), "结果应表示成功");
            assertFalse(result.isNeedInit(), "不应标记为需要初始化");
            
            verify(personalWxGroupInfoDOMapper).selectByExample(any());
            verify(personalWxGroupInfoDOMapper, never()).insert(any());
            verify(personalWxGroupInfoDOMapper).updateByPrimaryKeySelective(groupInfoCaptor.capture());
            
            // 验证更新的数据
            ScrmDSPersonalWxGroupInfoDO capturedGroup = groupInfoCaptor.getValue();
            assertEquals(changeEvent.getOpenGroupSimpleInfo().getGroupName(), capturedGroup.getGroupName(), "群名称应更新");
            assertEquals(changeEvent.getOpenGroupSimpleInfo().getNotice(), capturedGroup.getGroupNotice(), "群公告应更新");
            assertEquals(1, capturedGroup.getStatus(), "状态应为托管(1)");
            assertNotNull(capturedGroup.getGroupQrCode(), "群二维码不应为null");
        }
    }

    @Nested
    @DisplayName("queryGroupInfoByDsId 方法测试")
    public class QueryGroupInfoByDsIdTest {

        @Test
        @DisplayName("当dsGroupId为null时，应返回null")
        public void shouldReturnNullWhenDsGroupIdIsNull() {
            // 执行
            ScrmDSPersonalWxGroupInfoDO result = personalWxGroupInfoDomainService.queryGroupInfoByDsId(null);
            
            // 验证
            assertNull(result, "结果应为null");
            verify(personalWxGroupInfoDOMapper, never()).selectByExample(any());
        }
        
        @Test
        @DisplayName("当没有找到群信息时，应返回null")
        public void shouldReturnNullWhenNoGroupInfoFound() {
            // 准备
            Long dsGroupId = 1001L;
            
            when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
            
            // 执行
            ScrmDSPersonalWxGroupInfoDO result = personalWxGroupInfoDomainService.queryGroupInfoByDsId(dsGroupId);
            
            // 验证
            assertNull(result, "结果应为null");
            verify(personalWxGroupInfoDOMapper).selectByExample(groupInfoExampleCaptor.capture());
            
            ScrmDSPersonalWxGroupInfoDOExample capturedExample = groupInfoExampleCaptor.getValue();
            assertEquals(dsGroupId, capturedExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
            assertEquals("event_time desc", capturedExample.getOrderByClause());
        }
        
        @Test
        @DisplayName("当找到多个群信息时，应返回按事件时间倒序的第一个")
        public void shouldReturnFirstGroupInfoOrderedByEventTimeDesc() {
            // 准备
            Long dsGroupId = 1001L;
            
            ScrmDSPersonalWxGroupInfoDO group1 = new ScrmDSPersonalWxGroupInfoDO();
            group1.setId(1L);
            group1.setDsGroupId(dsGroupId);
            group1.setGroupName("最新的群信息");
            group1.setEventTime(1000L);
            
            ScrmDSPersonalWxGroupInfoDO group2 = new ScrmDSPersonalWxGroupInfoDO();
            group2.setId(2L);
            group2.setDsGroupId(dsGroupId);
            group2.setGroupName("较旧的群信息");
            group2.setEventTime(500L);
            
            // 模拟数据库已经按事件时间倒序排列返回
            List<ScrmDSPersonalWxGroupInfoDO> mockGroups = Arrays.asList(group1, group2);
            
            when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(mockGroups);
            
            // 执行
            ScrmDSPersonalWxGroupInfoDO result = personalWxGroupInfoDomainService.queryGroupInfoByDsId(dsGroupId);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertEquals(group1.getId(), result.getId());
            assertEquals("最新的群信息", result.getGroupName());
            assertEquals(1000L, result.getEventTime());
            
            verify(personalWxGroupInfoDOMapper).selectByExample(groupInfoExampleCaptor.capture());
            
            ScrmDSPersonalWxGroupInfoDOExample capturedExample = groupInfoExampleCaptor.getValue();
            assertEquals(dsGroupId, capturedExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
            assertEquals("event_time desc", capturedExample.getOrderByClause());
        }
    }

    @Nested
    @DisplayName("queryGroupsNotExist 方法测试")
    public class QueryGroupsNotExistTest {

        @Test
        @DisplayName("当corpId为空时，应返回空列表")
        public void shouldReturnEmptyListWhenCorpIdIsEmpty() {
            // 执行
            List<Long> result = personalWxGroupInfoDomainService.queryGroupsNotExist("", Arrays.asList(1001L, 1002L));
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            verify(personalWxGroupInfoDOMapper, never()).selectByExample(any());
        }
        
        @Test
        @DisplayName("当corpId为null时，应返回空列表")
        public void shouldReturnEmptyListWhenCorpIdIsNull() {
            // 执行
            List<Long> result = personalWxGroupInfoDomainService.queryGroupsNotExist(null, Arrays.asList(1001L, 1002L));
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            verify(personalWxGroupInfoDOMapper, never()).selectByExample(any());
        }
        
        @Test
        @DisplayName("当deepSeaGroupIdList为空时，应返回空列表")
        public void shouldReturnEmptyListWhenDeepSeaGroupIdListIsEmpty() {
            // 执行
            List<Long> result = personalWxGroupInfoDomainService.queryGroupsNotExist("test_corp_id", Collections.emptyList());
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            verify(personalWxGroupInfoDOMapper, never()).selectByExample(any());
        }
        
        @Test
        @DisplayName("当deepSeaGroupIdList为null时，应返回空列表")
        public void shouldReturnEmptyListWhenDeepSeaGroupIdListIsNull() {
            // 执行
            List<Long> result = personalWxGroupInfoDomainService.queryGroupsNotExist("test_corp_id", null);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            verify(personalWxGroupInfoDOMapper, never()).selectByExample(any());
        }
        
        @Test
        @DisplayName("当所有群都已存在，应返回空列表")
        public void shouldReturnEmptyListWhenAllGroupsExist() {
            // 准备
            String corpId = "test_corp_id";
            List<Long> deepSeaGroupIds = Arrays.asList(1001L, 1002L);
            
            ScrmDSPersonalWxGroupInfoDO group1 = new ScrmDSPersonalWxGroupInfoDO();
            group1.setDsGroupId(1001L);
            group1.setCorpId(corpId);
            
            ScrmDSPersonalWxGroupInfoDO group2 = new ScrmDSPersonalWxGroupInfoDO();
            group2.setDsGroupId(1002L);
            group2.setCorpId(corpId);
            
            List<ScrmDSPersonalWxGroupInfoDO> existingGroups = Arrays.asList(group1, group2);
            
            when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(existingGroups);
            
            // 执行
            List<Long> result = personalWxGroupInfoDomainService.queryGroupsNotExist(corpId, deepSeaGroupIds);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            
            verify(personalWxGroupInfoDOMapper).selectByExample(groupInfoExampleCaptor.capture());
            
            ScrmDSPersonalWxGroupInfoDOExample capturedExample = groupInfoExampleCaptor.getValue();
            assertEquals(corpId, capturedExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
            assertEquals(deepSeaGroupIds, capturedExample.getOredCriteria().get(0).getCriteria().get(1).getValue());
        }
        
        @Test
        @DisplayName("当部分群不存在时，应返回不存在的群ID列表")
        public void shouldReturnNonExistentGroupIds() {
            // 准备
            String corpId = "test_corp_id";
            List<Long> deepSeaGroupIds = Arrays.asList(1001L, 1002L, 1003L);
            
            ScrmDSPersonalWxGroupInfoDO group1 = new ScrmDSPersonalWxGroupInfoDO();
            group1.setDsGroupId(1001L);
            group1.setCorpId(corpId);
            
            // 只有群1001存在，1002和1003不存在
            List<ScrmDSPersonalWxGroupInfoDO> existingGroups = Collections.singletonList(group1);
            
            when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(existingGroups);
            
            // 执行
            List<Long> result = personalWxGroupInfoDomainService.queryGroupsNotExist(corpId, deepSeaGroupIds);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertEquals(2, result.size(), "结果应包含2个不存在的群ID");
            assertTrue(result.contains(1002L), "结果应包含群ID 1002");
            assertTrue(result.contains(1003L), "结果应包含群ID 1003");
            
            verify(personalWxGroupInfoDOMapper).selectByExample(groupInfoExampleCaptor.capture());
        }
        
        @Test
        @DisplayName("当有重复的群ID时，应去重处理")
        public void shouldRemoveDuplicatesFromGroupIds() {
            // 准备
            String corpId = "test_corp_id";
            // 包含重复的群ID 1001
            List<Long> deepSeaGroupIds = Arrays.asList(1001L, 1001L, 1002L);
            
            ScrmDSPersonalWxGroupInfoDO group1 = new ScrmDSPersonalWxGroupInfoDO();
            group1.setDsGroupId(1001L);
            group1.setCorpId(corpId);
            
            // 只有群1001存在，1002不存在
            List<ScrmDSPersonalWxGroupInfoDO> existingGroups = Collections.singletonList(group1);
            
            when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(existingGroups);
            
            // 执行
            List<Long> result = personalWxGroupInfoDomainService.queryGroupsNotExist(corpId, deepSeaGroupIds);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertEquals(1, result.size(), "结果应包含1个不存在的群ID");
            assertTrue(result.contains(1002L), "结果应包含群ID 1002");
            
            verify(personalWxGroupInfoDOMapper).selectByExample(groupInfoExampleCaptor.capture());
            
            // 验证去重后的群ID列表被传入查询
            ScrmDSPersonalWxGroupInfoDOExample capturedExample = groupInfoExampleCaptor.getValue();
            List<Long> distinctIds = (List<Long>) capturedExample.getOredCriteria().get(0).getCriteria().get(1).getValue();
            assertEquals(2, distinctIds.size(), "去重后应有2个群ID");
        }
    }

    @Nested
    @DisplayName("fuzzyPageGroupInfo 和 fuzzyCountGroups 方法测试")
    public class FuzzyQueryGroupInfoTest {

        @Test
        @DisplayName("当corpId为空时，fuzzyPageGroupInfo应返回空列表")
        public void fuzzyPageGroupInfoShouldReturnEmptyListWhenCorpIdIsEmpty() {
            // 执行
            List<FuzzyQueryWxGroupInfoDTO> result = personalWxGroupInfoDomainService.fuzzyPageGroupInfo("", "测试群", 10, 1);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            verify(personalWxGroupInfoDOMapper, never()).selectByExample(any());
        }
        
        @Test
        @DisplayName("当corpId为null时，fuzzyPageGroupInfo应返回空列表")
        public void fuzzyPageGroupInfoShouldReturnEmptyListWhenCorpIdIsNull() {
            // 执行
            List<FuzzyQueryWxGroupInfoDTO> result = personalWxGroupInfoDomainService.fuzzyPageGroupInfo(null, "测试群", 10, 1);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            verify(personalWxGroupInfoDOMapper, never()).selectByExample(any());
        }
        
        @Test
        @DisplayName("fuzzyPageGroupInfo应正确处理分页参数和模糊查询")
        public void fuzzyPageGroupInfoShouldHandlePaginationAndFuzzySearch() {
            // 准备
            String corpId = "test_corp_id";
            String groupName = "测试群";
            int pageSize = 10;
            int pageNum = 2;
            
            ScrmDSPersonalWxGroupInfoDO group1 = new ScrmDSPersonalWxGroupInfoDO();
            group1.setId(1L);
            group1.setDsGroupId(1001L);
            group1.setWxGroupId("wxid_1");
            group1.setGroupName("测试群1");
            group1.setCurrentMemberCount(50);
            group1.setGroupQrCode("https://test.qrcode.url/1");
            
            ScrmDSPersonalWxGroupInfoDO group2 = new ScrmDSPersonalWxGroupInfoDO();
            group2.setId(2L);
            group2.setDsGroupId(1002L);
            group2.setWxGroupId("wxid_2");
            group2.setGroupName("测试群2");
            group2.setCurrentMemberCount(60);
            group2.setGroupQrCode("https://test.qrcode.url/2");
            
            List<ScrmDSPersonalWxGroupInfoDO> mockGroups = Arrays.asList(group1, group2);
            
            when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(mockGroups);
            
            // 执行
            List<FuzzyQueryWxGroupInfoDTO> result = personalWxGroupInfoDomainService.fuzzyPageGroupInfo(corpId, groupName, pageSize, pageNum);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertEquals(2, result.size(), "结果应包含2个群组");
            
            // 验证第一个结果
            FuzzyQueryWxGroupInfoDTO firstResult = result.get(0);
            assertEquals(group1.getDsGroupId(), firstResult.getDsGroupId());
            assertEquals(group1.getGroupName(), firstResult.getGroupName());
            assertEquals(group1.getWxGroupId(), firstResult.getWxGroupId());
            assertEquals(group1.getCurrentMemberCount(), firstResult.getCurrentMemberCount());
            assertEquals(group1.getGroupQrCode(), firstResult.getGroupQRCode());
            
            // 验证第二个结果
            FuzzyQueryWxGroupInfoDTO secondResult = result.get(1);
            assertEquals(group2.getDsGroupId(), secondResult.getDsGroupId());
            
            verify(personalWxGroupInfoDOMapper).selectByExample(groupInfoExampleCaptor.capture());
            
            ScrmDSPersonalWxGroupInfoDOExample capturedExample = groupInfoExampleCaptor.getValue();
            assertEquals(corpId, capturedExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
            assertEquals("%" + groupName + "%", capturedExample.getOredCriteria().get(0).getCriteria().get(1).getValue());
            
            // 验证分页参数
            assertEquals(pageSize, capturedExample.getRows());
            assertEquals((pageNum - 1) * pageSize, capturedExample.getOffset().intValue());
        }
        
        @Test
        @DisplayName("当groupName为null时，fuzzyPageGroupInfo应只根据corpId查询")
        public void fuzzyPageGroupInfoShouldQueryOnlyByCorpIdWhenGroupNameIsNull() {
            // 准备
            String corpId = "test_corp_id";
            int pageSize = 10;
            int pageNum = 1;
            
            List<ScrmDSPersonalWxGroupInfoDO> mockGroups = Collections.singletonList(new ScrmDSPersonalWxGroupInfoDO());
            
            when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(mockGroups);
            
            // 执行
            List<FuzzyQueryWxGroupInfoDTO> result = personalWxGroupInfoDomainService.fuzzyPageGroupInfo(corpId, null, pageSize, pageNum);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertEquals(1, result.size(), "结果应包含1个群组");
            
            verify(personalWxGroupInfoDOMapper).selectByExample(groupInfoExampleCaptor.capture());
            
            ScrmDSPersonalWxGroupInfoDOExample capturedExample = groupInfoExampleCaptor.getValue();
            assertEquals(corpId, capturedExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
            assertEquals(1, capturedExample.getOredCriteria().get(0).getCriteria().size(), "应只有一个查询条件(corpId)");
        }
        
        @Test
        @DisplayName("当groupName为空字符串时，fuzzyPageGroupInfo应只根据corpId查询")
        public void fuzzyPageGroupInfoShouldQueryOnlyByCorpIdWhenGroupNameIsEmpty() {
            // 准备
            String corpId = "test_corp_id";
            int pageSize = 10;
            int pageNum = 1;
            
            List<ScrmDSPersonalWxGroupInfoDO> mockGroups = Collections.singletonList(new ScrmDSPersonalWxGroupInfoDO());
            
            when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(mockGroups);
            
            // 执行
            List<FuzzyQueryWxGroupInfoDTO> result = personalWxGroupInfoDomainService.fuzzyPageGroupInfo(corpId, "", pageSize, pageNum);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertEquals(1, result.size(), "结果应包含1个群组");
            
            verify(personalWxGroupInfoDOMapper).selectByExample(groupInfoExampleCaptor.capture());
            
            ScrmDSPersonalWxGroupInfoDOExample capturedExample = groupInfoExampleCaptor.getValue();
            assertEquals(corpId, capturedExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
            assertEquals(1, capturedExample.getOredCriteria().get(0).getCriteria().size(), "应只有一个查询条件(corpId)");
        }
        
        @Test
        @DisplayName("当corpId为空时，fuzzyCountGroups应返回0")
        public void fuzzyCountGroupsShouldReturnZeroWhenCorpIdIsEmpty() {
            // 执行
            long result = personalWxGroupInfoDomainService.fuzzyCountGroups("", "测试群");
            
            // 验证
            assertEquals(0, result, "结果应为0");
            verify(personalWxGroupInfoDOMapper, never()).countByExample(any());
        }
        
        @Test
        @DisplayName("当corpId为null时，fuzzyCountGroups应返回0")
        public void fuzzyCountGroupsShouldReturnZeroWhenCorpIdIsNull() {
            // 执行
            long result = personalWxGroupInfoDomainService.fuzzyCountGroups(null, "测试群");
            
            // 验证
            assertEquals(0, result, "结果应为0");
            verify(personalWxGroupInfoDOMapper, never()).countByExample(any());
        }
        
        @Test
        @DisplayName("fuzzyCountGroups应正确处理模糊查询并返回总数")
        public void fuzzyCountGroupsShouldHandleFuzzySearchAndReturnTotal() {
            // 准备
            String corpId = "test_corp_id";
            String groupName = "测试群";
            long expectedCount = 42L;
            
            when(personalWxGroupInfoDOMapper.countByExample(any())).thenReturn(expectedCount);
            
            // 执行
            long result = personalWxGroupInfoDomainService.fuzzyCountGroups(corpId, groupName);
            
            // 验证
            assertEquals(expectedCount, result, "结果应为" + expectedCount);
            
            verify(personalWxGroupInfoDOMapper).countByExample(groupInfoExampleCaptor.capture());
            
            ScrmDSPersonalWxGroupInfoDOExample capturedExample = groupInfoExampleCaptor.getValue();
            assertEquals(corpId, capturedExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
            assertEquals("%" + groupName + "%", capturedExample.getOredCriteria().get(0).getCriteria().get(1).getValue());
        }
        
        @Test
        @DisplayName("当groupName为null时，fuzzyCountGroups应只根据corpId查询")
        public void fuzzyCountGroupsShouldQueryOnlyByCorpIdWhenGroupNameIsNull() {
            // 准备
            String corpId = "test_corp_id";
            long expectedCount = 10L;
            
            when(personalWxGroupInfoDOMapper.countByExample(any())).thenReturn(expectedCount);
            
            // 执行
            long result = personalWxGroupInfoDomainService.fuzzyCountGroups(corpId, null);
            
            // 验证
            assertEquals(expectedCount, result, "结果应为" + expectedCount);
            
            verify(personalWxGroupInfoDOMapper).countByExample(groupInfoExampleCaptor.capture());
            
            ScrmDSPersonalWxGroupInfoDOExample capturedExample = groupInfoExampleCaptor.getValue();
            assertEquals(corpId, capturedExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
            assertEquals(1, capturedExample.getOredCriteria().get(0).getCriteria().size(), "应只有一个查询条件(corpId)");
        }
    }
} 