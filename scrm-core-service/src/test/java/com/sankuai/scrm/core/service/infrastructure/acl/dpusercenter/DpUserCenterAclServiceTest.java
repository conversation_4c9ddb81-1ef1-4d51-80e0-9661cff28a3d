package com.sankuai.scrm.core.service.infrastructure.acl.dpusercenter;

import com.dianping.account.validation.AccountValidationService;
import com.dianping.account.validation.dto.AccountValidationResult;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DpUserCenterAclServiceTest {

    @InjectMocks
    private DpUserCenterAclService dpUserCenterAclService;

    @Mock
    private AccountValidationService accountValidationService;

    @Test
    public void testQueryDpUserIdsByMtUserIds() {
        String token = "testToken";
        AccountValidationResult result = new AccountValidationResult(true, 1L, 0);
        when(accountValidationService.validateDper(anyString())).thenReturn(result);
        long userId = dpUserCenterAclService.queryDpUserIdByToken(token);
        assertEquals(1L, userId);
    }

}