package com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationGoalDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration.ScrmProcessOrchestrationConverter;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration.ScrmProcessOrchestrationNodeConverter;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecutePlanDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationExecutePlanDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.DdlResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.RefinementOperationExecuteMessageProducer;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ProcessOrchestrationWriteDomainServiceTest {

    @InjectMocks
    private ProcessOrchestrationWriteDomainService processOrchestrationWriteDomainService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper scrmAmProcessOrchestrationExecuteLogDOMapper;

    @Mock(lenient = true)
    private ScrmAMRealtimeSceneAndProcessMapDOMapper scrmAMRealtimeSceneAndProcessMapDOMapper;

    @Mock(lenient = true)
    private ScrmAmConfigurationChangeLogDOMapper configurationChangeLogDOMapper;

    private Long processOrchestrationId = 1L;

    private String appId = "appId";

    private String misId = "misId";

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationNodeInfoDOMapper scrmAmProcessOrchestrationNodeInfoDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationConditionDOMapper scrmAmProcessOrchestrationConditionDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationActionDOMapper scrmAmProcessOrchestrationActionDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationActionContentDOMapper scrmAmProcessOrchestrationActionContentDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationActionAttachmentDOMapper scrmAmProcessOrchestrationActionAttachmentDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationGoalDOMapper scrmAmProcessOrchestrationGoalDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationGoalDetailDOMapper scrmAmProcessOrchestrationGoalDetailDOMapper;

    @Mock(lenient = true)
    private ScrmAmCrowdPackAndProcessMapDOMapper scrmAmCrowdPackAndProcessMapDOMapper;

    @Mock(lenient = true)
    private ScrmAMRealtimeSceneAndProcessMapDOMapper realtimeSceneAndProcessMapDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecutorLimitDOMapper scrmAmProcessOrchestrationExecutorLimitDOMapper;

    @Mock(lenient = true)
    private InformationGatheringService informationGatheringService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationNodeExecuteLogDOMapper scrmAmProcessOrchestrationNodeExecuteLogDOMapper;

    @Mock(lenient = true)
    private ExtScrmAmProcessOrchestrationExecutePlanDOMapper extScrmAmProcessOrchestrationExecutePlanDOMapper;

    private List<ScrmAmProcessOrchestrationNodeInfoDO> nodeInfoDOList;

    private ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO;

    private List<ScrmAmProcessOrchestrationConditionDO> conditionDOList;

    private List<ScrmAmProcessOrchestrationActionDO> actionDOList;

    private List<ScrmAmProcessOrchestrationActionContentDO> actionContentDOList;

    private List<ScrmAmProcessOrchestrationActionAttachmentDO> actionAttachmentDOList;

    private ScrmAmProcessOrchestrationGoalDO goalDO;

    private List<ScrmAmProcessOrchestrationGoalDetailDO> goalDetailDOList;

    private ScrmAmProcessOrchestrationGoalDO negativeDO;

    private List<ScrmAmProcessOrchestrationGoalDetailDO> negativeDetailDOList;

    private List<ScrmAmCrowdPackAndProcessMapDO> mapDOList;

    private List<ScrmAmProcessOrchestrationExecutorLimitDO> executorLimitDOList;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationConverter scrmProcessOrchestrationConverter;

    @Mock(lenient = true)
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock(lenient = true)
    private RefinementOperationExecuteMessageProducer refinementOperationExecuteMessageProducer;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationNodeConverter scrmProcessOrchestrationNodeConverter;

    private void initializeTestData() {
        nodeInfoDOList = new ArrayList<>();
        processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        conditionDOList = new ArrayList<>();
        actionDOList = new ArrayList<>();
        actionContentDOList = new ArrayList<>();
        actionAttachmentDOList = new ArrayList<>();
        goalDO = new ScrmAmProcessOrchestrationGoalDO();
        goalDetailDOList = new ArrayList<>();
        negativeDO = new ScrmAmProcessOrchestrationGoalDO();
        negativeDetailDOList = new ArrayList<>();
        mapDOList = new ArrayList<>();
        executorLimitDOList = new ArrayList<>();
    }

    private ScrmProcessOrchestrationDTO createProcessOrchestrationDTO() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setCrowdPackType(1);
        processOrchestrationDTO.setCrowdPackIdList(Arrays.asList(1L, 2L, 3L));
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(new ArrayList<>());
        nodeMediumDTO.setConditionMap(new HashMap<>());
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        ScrmProcessOrchestrationGoalDTO goalDTO = new ScrmProcessOrchestrationGoalDTO();
        processOrchestrationDTO.setGoalDTO(goalDTO);
        ScrmProcessOrchestrationGoalDTO negativeGoalDTO = new ScrmProcessOrchestrationGoalDTO();
        processOrchestrationDTO.setNegativeGoalDTO(negativeGoalDTO);
        return processOrchestrationDTO;
    }

    /**
     * 测试场景：当存在执行计划且开始时间早于当前时间
     */
    @Test
    public void testCheckNextRunTimeForUpdate_WhenExecutePlanExistsAndStartTimeBeforeNow() {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setEndTime(new Date(System.currentTimeMillis() + 1000000)); // 设置结束时间为当前时间之后
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, 1);
        Date date = calendar.getTime();
        processOrchestrationInfoDO.setCronComment(DateUtil.formatYMdHms(date));
        processOrchestrationInfoDO.setProcessOrchestrationType(TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        DdlResultDTO result = new DdlResultDTO();

        List<ScrmAmProcessOrchestrationExecutePlanDO> executePlanDOs = new ArrayList<>();
        ScrmAmProcessOrchestrationExecutePlanDO executePlanDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        executePlanDOs.add(executePlanDO);

        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(executePlanDOs);

        // act
        DdlResultDTO actualResult = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(processOrchestrationInfoDO, result);

        // assert
        Assertions.assertNotNull(actualResult);
    }

    /**
     * 测试场景：当不存在执行计划且开始时间早于当前时间
     */
    @Test
    public void testCheckNextRunTimeForUpdate_WhenNoExecutePlanAndStartTimeBeforeNow() {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setEndTime(new Date(System.currentTimeMillis() + 10000)); // 设置结束时间为当前时间之后
        processOrchestrationInfoDO.setProcessOrchestrationType(TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        processOrchestrationInfoDO.setCronComment(DateUtil.formatYMdHms(new Date()));
        DdlResultDTO result = new DdlResultDTO();

        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(new ArrayList<>());

        // act
        DdlResultDTO actualResult = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(processOrchestrationInfoDO, result);

        // assert
        Assertions.assertNotNull(actualResult);
    }

    /**
     * 测试流程编排不存在的情况
     */
    @Test
    public void testLightDeleteProcessOrchestration_NotExist() throws Throwable {
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(processOrchestrationId)).thenReturn(null);
        DdlResultDTO result = processOrchestrationWriteDomainService.lightDeleteProcessOrchestration(processOrchestrationId, appId, misId);
        Assertions.assertFalse(result.isSuccess());
        Assertions.assertEquals("流程编排不存在", result.getMsg());
        Assertions.assertEquals(processOrchestrationId, result.getId());
    }

    /**
     * 测试不属于此业务的情况
     */
    @Test
    public void testLightDeleteProcessOrchestration_NotBelong() throws Throwable {
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setAppId("otherAppId");
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(processOrchestrationId)).thenReturn(processOrchestrationInfoDO);
        DdlResultDTO result = processOrchestrationWriteDomainService.lightDeleteProcessOrchestration(processOrchestrationId, appId, misId);
        Assertions.assertFalse(result.isSuccess());
        Assertions.assertEquals("不属于此业务", result.getMsg());
        Assertions.assertEquals(processOrchestrationId, result.getId());
    }

    /**
     * 测试删除成功的情况
     */
    @Test
    public void testLightDeleteProcessOrchestration_Success() throws Throwable {
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setAppId(appId);
        // Assuming the default type is 0
        processOrchestrationInfoDO.setProcessOrchestrationType((byte) 0);
        // Set a valid version to avoid the exception
        processOrchestrationInfoDO.setValidVersion("1.0");
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(processOrchestrationId)).thenReturn(processOrchestrationInfoDO);
        when(realtimeSceneAndProcessMapDOMapper.deleteByExample(any())).thenReturn(1);
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        DdlResultDTO result = processOrchestrationWriteDomainService.lightDeleteProcessOrchestration(processOrchestrationId, appId, misId);
        Assertions.assertTrue(result.isSuccess());
        Assertions.assertEquals("删除成功", result.getMsg());
        Assertions.assertEquals(processOrchestrationId, result.getId());
    }

    @Test
    public void testInsertSubassembliesAllEmpty() throws Throwable {
        initializeTestData();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setValidVersion("1.0");
        processOrchestrationWriteDomainService.insertSubassemblies(nodeInfoDOList, processOrchestrationInfoDO, conditionDOList, actionDOList, actionContentDOList, actionAttachmentDOList, goalDO, goalDetailDOList, negativeDO, negativeDetailDOList, mapDOList, executorLimitDOList);
        // Adjusted the verification to reflect the actual behavior for an empty mapDOList
        verify(scrmAmCrowdPackAndProcessMapDOMapper).batchInsert(anyList());
    }

    @Test
    public void testInsertSubassembliesNodeInfoNotEmpty() throws Throwable {
        initializeTestData();
        nodeInfoDOList.add(new ScrmAmProcessOrchestrationNodeInfoDO());
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setValidVersion("1.0");
        when(scrmAmProcessOrchestrationNodeExecuteLogDOMapper.batchInsert(anyList())).thenReturn(1);
        processOrchestrationWriteDomainService.insertSubassemblies(nodeInfoDOList, processOrchestrationInfoDO, conditionDOList, actionDOList, actionContentDOList, actionAttachmentDOList, goalDO, goalDetailDOList, negativeDO, negativeDetailDOList, mapDOList, executorLimitDOList);
        verify(scrmAmProcessOrchestrationNodeInfoDOMapper).batchInsert(anyList());
        verify(scrmAmProcessOrchestrationNodeExecuteLogDOMapper).batchInsert(anyList());
    }

    /*@Test(expected = Exception.class)
    public void testCreateProcessOrchestrationNullDTO() throws Throwable {
        processOrchestrationWriteDomainService.createProcessOrchestration(null);
    }

    @Test(expected = Exception.class)
    public void testCreateProcessOrchestrationNullCrowdPackType() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createProcessOrchestrationDTO();
        processOrchestrationDTO.setCrowdPackType(null);
        processOrchestrationWriteDomainService.createProcessOrchestration(processOrchestrationDTO);
    }*/

    /**
     * 测试场景：实时场景窗口期校验，无冲突，processOrchestrationId为null
     */
    @Test
    public void testCheckRealTimeSceneTimeWindow_NoConflict_WithNullProcessOrchestrationId() {
        Calendar nowCalendar = Calendar.getInstance();
        Date oneHourBefore = new Date(nowCalendar.getTimeInMillis() - 3600 * 1000);
        Date oneHourAfter = new Date(nowCalendar.getTimeInMillis() + 3600 * 1000);
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any()))
                .thenReturn(Arrays.asList());

        String result = processOrchestrationWriteDomainService.checkRealTimeSceneTimeWindow(null, 1L, oneHourBefore, oneHourAfter);

        Assertions.assertEquals(null, result);
    }

    /**
     * 测试场景：实时场景窗口期校验，有冲突，processOrchestrationId为null
     */
    @Test
    public void testCheckRealTimeSceneTimeWindow_Conflict_WithNullProcessOrchestrationId() {
        Calendar nowCalendar = Calendar.getInstance();
        Date oneHourBefore = new Date(nowCalendar.getTimeInMillis() - 3600 * 1000);
        Date oneHourAfter = new Date(nowCalendar.getTimeInMillis() + 3600 * 1000);
        ScrmAMRealtimeSceneAndProcessMapDO scrmAMRealtimeSceneAndProcessMapDO = new ScrmAMRealtimeSceneAndProcessMapDO();
        scrmAMRealtimeSceneAndProcessMapDO.setBeginTime(new Date(nowCalendar.getTimeInMillis() - 1800 * 1000));
        scrmAMRealtimeSceneAndProcessMapDO.setEndTime(new Date(nowCalendar.getTimeInMillis() + 1800 * 1000));
        scrmAMRealtimeSceneAndProcessMapDO.setProcessOrchestrationId(2L);
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any()))
                .thenReturn(Arrays.asList(scrmAMRealtimeSceneAndProcessMapDO));

        String result = processOrchestrationWriteDomainService.checkRealTimeSceneTimeWindow(null, 1L, oneHourBefore, oneHourAfter);

        Assertions.assertEquals("该时段目标实时场景已被占用： 占用编排ID：2", result);
    }

    /**
     * 测试场景：实时场景窗口期校验，无冲突，processOrchestrationId非null
     */
    @Test
    public void testCheckRealTimeSceneTimeWindow_NoConflict_WithNonNullProcessOrchestrationId() {
        Calendar nowCalendar = Calendar.getInstance();
        Date oneHourBefore = new Date(nowCalendar.getTimeInMillis() - 3600 * 1000);
        Date oneHourAfter = new Date(nowCalendar.getTimeInMillis() + 3600 * 1000);
        ScrmAMRealtimeSceneAndProcessMapDO scrmAMRealtimeSceneAndProcessMapDO = new ScrmAMRealtimeSceneAndProcessMapDO();
        scrmAMRealtimeSceneAndProcessMapDO.setProcessOrchestrationId(1L);
        scrmAMRealtimeSceneAndProcessMapDO.setBeginTime(new Date(nowCalendar.getTimeInMillis() + 96400 * 1000));
        scrmAMRealtimeSceneAndProcessMapDO.setEndTime(new Date(nowCalendar.getTimeInMillis() + 166400 * 1000));
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any()))
                .thenReturn(Arrays.asList(scrmAMRealtimeSceneAndProcessMapDO));

        String result = processOrchestrationWriteDomainService.checkRealTimeSceneTimeWindow(1L, 1L, oneHourBefore, oneHourAfter);

        Assertions.assertEquals(null, result);
    }

    /**
     * 测试场景：实时场景窗口期校验，有冲突，processOrchestrationId非null
     */
    @Test
    public void testCheckRealTimeSceneTimeWindow_Conflict_WithNonNullProcessOrchestrationId() {
        Calendar nowCalendar = Calendar.getInstance();
        Date oneHourBefore = new Date(nowCalendar.getTimeInMillis() - 3600 * 1000);
        Date oneHourAfter = new Date(nowCalendar.getTimeInMillis() + 3600 * 1000);
        ScrmAMRealtimeSceneAndProcessMapDO scrmAMRealtimeSceneAndProcessMapDO1 = new ScrmAMRealtimeSceneAndProcessMapDO();
        scrmAMRealtimeSceneAndProcessMapDO1.setProcessOrchestrationId(1L);
        scrmAMRealtimeSceneAndProcessMapDO1.setBeginTime(new Date(nowCalendar.getTimeInMillis() - 1800 * 1000));
        scrmAMRealtimeSceneAndProcessMapDO1.setEndTime(new Date(nowCalendar.getTimeInMillis() + 1800 * 1000));
        ScrmAMRealtimeSceneAndProcessMapDO scrmAMRealtimeSceneAndProcessMapDO2 = new ScrmAMRealtimeSceneAndProcessMapDO();
        scrmAMRealtimeSceneAndProcessMapDO2.setProcessOrchestrationId(2L);
        scrmAMRealtimeSceneAndProcessMapDO2.setBeginTime(new Date(nowCalendar.getTimeInMillis() - 1800 * 1000));
        scrmAMRealtimeSceneAndProcessMapDO2.setEndTime(new Date(nowCalendar.getTimeInMillis() + 1800 * 1000));
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any()))
                .thenReturn(Arrays.asList(scrmAMRealtimeSceneAndProcessMapDO1, scrmAMRealtimeSceneAndProcessMapDO2));

        String result = processOrchestrationWriteDomainService.checkRealTimeSceneTimeWindow(3L, 1L, oneHourBefore, oneHourAfter);

        Assertions.assertEquals("该时段目标实时场景已被占用： 占用编排ID：1,2", result);
    }

    /**
     * 测试流程编排不存在的情况
     */
    @Test
    public void testChangeProcessOrchestrationStatus_ProcessOrchestrationNotExist() {
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setAppId("testAppId");
        processOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.VALID.getValue().byteValue());
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(any(Long.class))).thenReturn(null);

        DdlResultDTO result = processOrchestrationWriteDomainService.changeProcessOrchestrationStatus(1L, "testAppId", "testMisId", ScrmProcessOrchestrationStatusTypeEnum.VALID.getValue().byteValue());

        Assertions.assertFalse(result.isSuccess());
        Assertions.assertEquals("流程编排不存在", result.getMsg());
    }

    /**
     * 测试不属于此业务的情况
     */
    @Test
    public void testChangeProcessOrchestrationStatus_NotBelongToThisBusiness() {
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setAppId("testAppId");
        processOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.VALID.getValue().byteValue());
        processOrchestrationInfoDO.setAppId("anotherAppId");
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(any(Long.class))).thenReturn(processOrchestrationInfoDO);

        DdlResultDTO result = processOrchestrationWriteDomainService.changeProcessOrchestrationStatus(1L, "testAppId", "testMisId", ScrmProcessOrchestrationStatusTypeEnum.VALID.getValue().byteValue());

        Assertions.assertFalse(result.isSuccess());
        Assertions.assertEquals("不属于此业务", result.getMsg());
    }

    /**
     * 测试从暂停状态恢复任务的情况
     */
    @Test
    public void testChangeProcessOrchestrationStatus_ResumeFromPaused() {
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setAppId("testAppId");
        processOrchestrationInfoDO.setProcessOrchestrationType(TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        processOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.PAUSED.getValue().byteValue());
        Calendar beginCalendar = Calendar.getInstance();
        beginCalendar.setTime(new Date());
        beginCalendar.add(Calendar.DAY_OF_MONTH, -1);
        processOrchestrationInfoDO.setBeginTime(beginCalendar.getTime());
        Calendar endCalendar = Calendar.getInstance();
        endCalendar.setTime(new Date());
        endCalendar.add(Calendar.DAY_OF_MONTH, 1);

        Calendar startCalendar = Calendar.getInstance();
        startCalendar.setTime(new Date());
        startCalendar.add(Calendar.MINUTE, 9);
        processOrchestrationInfoDO.setCronComment(DateUtil.formatYMdHms(startCalendar.getTime()));
        processOrchestrationInfoDO.setEndTime(endCalendar.getTime());
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(any(Long.class))).thenReturn(processOrchestrationInfoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.updateByPrimaryKey(any(ScrmAmProcessOrchestrationInfoDO.class))).thenReturn(1);

        DdlResultDTO result = processOrchestrationWriteDomainService.changeProcessOrchestrationStatus(1L, "testAppId", "testMisId", ScrmProcessOrchestrationStatusTypeEnum.VALID.getValue().byteValue());

        Assertions.assertTrue(result.isSuccess());
        Assertions.assertEquals("恢复成功", result.getMsg());
    }

    /**
     * 测试暂停任务的情况
     */
    @Test
    public void testChangeProcessOrchestrationStatus_PauseTask() {
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setAppId("testAppId");
        processOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.VALID.getValue().byteValue());
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(any(Long.class))).thenReturn(processOrchestrationInfoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.updateByPrimaryKey(any(ScrmAmProcessOrchestrationInfoDO.class))).thenReturn(1);

        DdlResultDTO result = processOrchestrationWriteDomainService.changeProcessOrchestrationStatus(1L, "testAppId", "testMisId", ScrmProcessOrchestrationStatusTypeEnum.PAUSED.getValue().byteValue());

        Assertions.assertTrue(result.isSuccess());
        Assertions.assertEquals("暂停成功", result.getMsg());
    }

    /**
     * 测试状态错误，未找到对应操作的情况
     */
    @Test
    public void testChangeProcessOrchestrationStatus_InvalidStatus() {
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setAppId("testAppId");
        processOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.VALID.getValue().byteValue());
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(any(Long.class))).thenReturn(processOrchestrationInfoDO);

        DdlResultDTO result = processOrchestrationWriteDomainService.changeProcessOrchestrationStatus(1L, "testAppId", "testMisId", (byte) 99);

        Assertions.assertFalse(result.isSuccess());
        Assertions.assertEquals("状态错误，未找到对应操作，变更失败", result.getMsg());
    }

    /**
     * 测试暂停任务，当任务状态为VALID时
     */
    @Test
    public void testPauseTaskWithValidStatus() {
        ScrmAmProcessOrchestrationInfoDO validProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        validProcessOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.VALID.getValue().byteValue());
        ScrmAmProcessOrchestrationInfoDO runningProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        runningProcessOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.RUNNING.getValue().byteValue());
        // arrange
        Long processOrchestrationId = 1L;
        String misId = "testUser";
        when(scrmAmProcessOrchestrationInfoDOMapper.updateByPrimaryKey(any(ScrmAmProcessOrchestrationInfoDO.class))).thenReturn(1);

        // act
        DdlResultDTO result = processOrchestrationWriteDomainService.pauseTask(processOrchestrationId, misId, validProcessOrchestrationInfoDO);

        // assert
        Assertions.assertTrue(result.isSuccess());
        Assertions.assertEquals("暂停成功",result.getMsg());
    }

    /**
     * 测试暂停任务，当任务状态为RUNNING时
     */
    @Test
    public void testPauseTaskWithRunningStatus() {
        ScrmAmProcessOrchestrationInfoDO validProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        validProcessOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.VALID.getValue().byteValue());
        ScrmAmProcessOrchestrationInfoDO runningProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        runningProcessOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.RUNNING.getValue().byteValue());
        // arrange
        Long processOrchestrationId = 1L;
        String misId = "testUser";
        when(scrmAmProcessOrchestrationInfoDOMapper.updateByPrimaryKey(any(ScrmAmProcessOrchestrationInfoDO.class))).thenReturn(1);

        // act
        DdlResultDTO result = processOrchestrationWriteDomainService.pauseTask(processOrchestrationId, misId, runningProcessOrchestrationInfoDO);

        // assert
        Assertions.assertTrue(result.isSuccess());
        Assertions.assertEquals("任务中断",result.getMsg());
    }

    /**
     * 测试暂停任务，当任务状态为未知时
     */
    @Test
    public void testPauseTaskWithUnknownStatus() {
        ScrmAmProcessOrchestrationInfoDO validProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        validProcessOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.VALID.getValue().byteValue());
        ScrmAmProcessOrchestrationInfoDO runningProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        runningProcessOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.RUNNING.getValue().byteValue());
        // arrange
        Long processOrchestrationId = 1L;
        String misId = "testUser";
        ScrmAmProcessOrchestrationInfoDO unknownStatusProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        unknownStatusProcessOrchestrationInfoDO.setStatus((byte) 99);

        // act
        DdlResultDTO result = processOrchestrationWriteDomainService.pauseTask(processOrchestrationId, misId, unknownStatusProcessOrchestrationInfoDO);

        // assert
        Assertions.assertFalse(result.isSuccess());
        Assertions.assertEquals("未找到对应数据类型",result.getMsg());
    }

    /**
     * 测试暂停任务，当任务状态为MESSAGE_SENDING时
     */
    @Test
    public void testPauseTaskWithMessageSendingStatus() {

        ScrmAmProcessOrchestrationInfoDO messageSendingProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        messageSendingProcessOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENDING.getValue().byteValue());

        // arrange
        Long processOrchestrationId = 1L;
        String misId = "testUser";
        when(scrmAmProcessOrchestrationInfoDOMapper.updateByPrimaryKey(any(ScrmAmProcessOrchestrationInfoDO.class))).thenReturn(1);

        // act
        DdlResultDTO result = processOrchestrationWriteDomainService.pauseTask(processOrchestrationId, misId, messageSendingProcessOrchestrationInfoDO);

        // assert
        Assertions.assertTrue(result.isSuccess());
        Assertions.assertEquals("任务中断",result.getMsg());
        verify(scrmAmProcessOrchestrationInfoDOMapper).updateByPrimaryKey(any(ScrmAmProcessOrchestrationInfoDO.class));
    }

    /**
     * 测试更新执行计划状态
     */
    @Test
    public void testUpdateExecutePlanStatus() {

        ScrmAmProcessOrchestrationInfoDO messageSendingProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        messageSendingProcessOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENDING.getValue().byteValue());
        ScrmAmProcessOrchestrationInfoDO runningProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        runningProcessOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.RUNNING.getValue().byteValue());

        // arrange
        Long processOrchestrationId = 1L;
        String misId = "testUser";
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);

        // act
        processOrchestrationWriteDomainService.pauseTask(processOrchestrationId, misId, runningProcessOrchestrationInfoDO);

        // assert
        verify(extScrmAmProcessOrchestrationExecutePlanDOMapper).updateByExampleSelective(any(), any());
    }

    /**
     * 测试更新执行日志状态
     */
    @Test
    public void testUpdateExecuteLogStatus() {
        ScrmAmProcessOrchestrationInfoDO messageSendingProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        messageSendingProcessOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENDING.getValue().byteValue());
        ScrmAmProcessOrchestrationInfoDO runningProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        runningProcessOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.RUNNING.getValue().byteValue());
        // arrange
        Long processOrchestrationId = 1L;
        String misId = "testUser";
        when(scrmAmProcessOrchestrationExecuteLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);

        // act
        processOrchestrationWriteDomainService.pauseTask(processOrchestrationId, misId, runningProcessOrchestrationInfoDO);

        // assert
        verify(scrmAmProcessOrchestrationExecuteLogDOMapper).updateByExampleSelective(any(), any());
    }

    /**
     * 测试更新实时场景和进程映射
     */
    @Test
    public void testUpdateRealtimeSceneAndProcessMap() {
        // arrange
        ScrmAmProcessOrchestrationInfoDO messageSendingProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        messageSendingProcessOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENDING.getValue().byteValue());
        ScrmAmProcessOrchestrationInfoDO runningProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        runningProcessOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.RUNNING.getValue().byteValue());

        Long processOrchestrationId = 1L;
        String misId = "testUser";
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);

        // act
        processOrchestrationWriteDomainService.pauseTask(processOrchestrationId, misId, runningProcessOrchestrationInfoDO);

        // assert
        verify(scrmAMRealtimeSceneAndProcessMapDOMapper).updateByExampleSelective(any(), any());
    }

    /**
     * 测试插入配置变更日志
     */
    @Test
    public void testInsertConfigurationChangeLog() {
        // arrange
        ScrmAmProcessOrchestrationInfoDO messageSendingProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        messageSendingProcessOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENDING.getValue().byteValue());
        ScrmAmProcessOrchestrationInfoDO runningProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        runningProcessOrchestrationInfoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.RUNNING.getValue().byteValue());
        Long processOrchestrationId = 1L;
        String misId = "testUser";
        when(configurationChangeLogDOMapper.insert(any())).thenReturn(1);

        // act
        processOrchestrationWriteDomainService.pauseTask(processOrchestrationId, misId, runningProcessOrchestrationInfoDO);

        // assert
        verify(configurationChangeLogDOMapper).insert(any());
    }
}
