package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.dz.srcm.realtime.task.dto.RealtimePerceptionMessage;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmRealtimeSceneDO;
import com.sankuai.scrm.core.service.realtime.task.domainservice.SceneDomainService;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineDispatchMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineDispatchProducer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RealtimePerceptionConsumerRecvMessageTest {

    @Mock
    private SceneDomainService sceneDomainService;

    @Mock
    private GroupRetailAiEngineDispatchProducer producer;

    @Mock
    private RealTimeTaskConsumerConfig consumerConfig;

    @InjectMocks
    private RealtimePerceptionConsumer consumer;

    private Method recvMessageMethod;

    @BeforeEach
    void setUp() throws Exception {
        recvMessageMethod = RealtimePerceptionConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        recvMessageMethod.setAccessible(true);
    }

    /**
     * Tests successful message processing with valid appId
     */
    @Test
    public void testRecvMessageSuccessWithValidAppId() throws Throwable {
        // arrange
        String messageJson = "{\"sceneId\":1,\"userId\":123,\"extendFields\":{}}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", messageJson);
        MessagetContext context = new MessagetContext();
        ScrmRealtimeSceneDO sceneDO = new ScrmRealtimeSceneDO();
        sceneDO.setAppid("validApp");
        when(sceneDomainService.handleRealtimePerceptionMessage(any(RealtimePerceptionMessage.class))).thenReturn(sceneDO);
        when(consumerConfig.getValidAppId(anyLong(), anyString())).thenReturn("validApp");
        // when(consumerConfig.getValidAppIds()).thenReturn(Arrays.asList("validApp"));
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(producer).sendDelayMessage(any(GroupRetailAiEngineDispatchMessageDTO.class), eq(5000L));
    }

    /**
     * Tests message processing with invalid appId (no message sent)
     */
    @Test
    public void testRecvMessageSuccessWithInvalidAppId() throws Throwable {
        // arrange
        String messageJson = "{\"sceneId\":1,\"userId\":123,\"extendFields\":{}}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", messageJson);
        MessagetContext context = new MessagetContext();
        ScrmRealtimeSceneDO sceneDO = new ScrmRealtimeSceneDO();
        sceneDO.setAppid("invalidApp");
        when(sceneDomainService.handleRealtimePerceptionMessage(any(RealtimePerceptionMessage.class))).thenReturn(sceneDO);
        when(consumerConfig.getValidAppId(anyLong(), anyString())).thenReturn(null);
        // when(consumerConfig.getValidAppIds()).thenReturn(Arrays.asList("validApp"));
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(producer, never()).sendDelayMessage(any(), anyLong());
    }

    /**
     * Tests null message body case
     */
    @Test
    public void testRecvMessageNullMessageBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", null);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(sceneDomainService, never()).handleRealtimePerceptionMessage(any());
        verify(producer, never()).sendDelayMessage(any(), anyLong());
    }

    /**
     * Tests invalid JSON format in message body
     */
    @Test
    public void testRecvMessageInvalidJsonFormat() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", "invalid json");
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(sceneDomainService, never()).handleRealtimePerceptionMessage(any());
        verify(producer, never()).sendDelayMessage(any(), anyLong());
    }

    /**
     * Tests null perception message after parsing
     */
    @Test
    public void testRecvMessageNullPerceptionMessage() throws Throwable {
        // arrange
        // This will result in null after parsing
        String messageJson = "null";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", messageJson);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(sceneDomainService, never()).handleRealtimePerceptionMessage(any());
        verify(producer, never()).sendDelayMessage(any(), anyLong());
    }

    /**
     * Tests null sceneDO returned from sceneDomainService
     */
    @Test
    public void testRecvMessageNullSceneDO() throws Throwable {
        // arrange
        String messageJson = "{\"sceneId\":1,\"userId\":123,\"extendFields\":{}}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", messageJson);
        MessagetContext context = new MessagetContext();
        when(sceneDomainService.handleRealtimePerceptionMessage(any(RealtimePerceptionMessage.class))).thenReturn(null);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(producer, never()).sendDelayMessage(any(), anyLong());
    }

    /**
     * Tests exception during message processing
     */
    @Test
    public void testRecvMessageExceptionDuringProcessing() throws Throwable {
        // arrange
        String messageJson = "{\"sceneId\":1,\"userId\":123,\"extendFields\":{}}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", messageJson);
        MessagetContext context = new MessagetContext();
        when(sceneDomainService.handleRealtimePerceptionMessage(any(RealtimePerceptionMessage.class))).thenThrow(new RuntimeException("Processing error"));
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(producer, never()).sendDelayMessage(any(), anyLong());
    }

    /**
     * Tests exception during message sending
     */
    @Test
    public void testRecvMessageExceptionDuringSending() throws Throwable {
        // arrange
        String messageJson = "{\"sceneId\":1,\"userId\":123,\"extendFields\":{}}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", messageJson);
        MessagetContext context = new MessagetContext();
        ScrmRealtimeSceneDO sceneDO = new ScrmRealtimeSceneDO();
        sceneDO.setAppid("validApp");
        when(sceneDomainService.handleRealtimePerceptionMessage(any(RealtimePerceptionMessage.class))).thenReturn(sceneDO);
        when(consumerConfig.getValidAppId(anyLong(), anyString())).thenReturn("validApp");
        // when(consumerConfig.getValidAppIds()).thenReturn(Arrays.asList("validApp"));
        doThrow(new RuntimeException("Send failed")).when(producer).sendDelayMessage(any(), anyLong());
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(producer).sendDelayMessage(any(), anyLong());
    }
}
