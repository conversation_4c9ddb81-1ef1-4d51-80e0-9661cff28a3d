package com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration;

import static org.junit.Assert.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationGoalDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationGoalDO;
import java.util.Arrays;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ScrmProcessOrchestrationGoalConverterTest {

    private ScrmProcessOrchestrationGoalConverter converter = new ScrmProcessOrchestrationGoalConverter();

    /**
     * 测试 convertToDTO 方法，当 resource 为 null 时
     */
    @Test
    public void testConvertToDTOWhenResourceIsNull() {
        // arrange
        ScrmAmProcessOrchestrationGoalDO resource = null;
        // act
        ScrmProcessOrchestrationGoalDTO result = converter.convertToDTO(resource);
        // assert
        assertNull(result);
    }

    /**
     * 测试 convertToDTO 方法，当 resource 不为 null，但 positiveResultHighlightList 和 negativeResultHighlightList 为空时
     */
    @Test
    public void testConvertToDTOWhenListsAreNull() {
        // arrange
        ScrmAmProcessOrchestrationGoalDO resource = new ScrmAmProcessOrchestrationGoalDO();
        resource.setPositiveResultHighlightList(null);
        resource.setNegativeResultHighlightList(null);
        // act
        ScrmProcessOrchestrationGoalDTO result = converter.convertToDTO(resource);
        // assert
        assertNotNull(result);
        assertNull(result.getPositiveResultHighlightList());
        assertNull(result.getNegativeResultHighlightList());
    }

    /**
     * 测试 convertToDTO 方法，当 resource 不为 null，positiveResultHighlightList 和 negativeResultHighlightList 不为空时
     */
    @Test
    public void testConvertToDTOWhenListsAreNotNull() {
        // arrange
        ScrmAmProcessOrchestrationGoalDO resource = new ScrmAmProcessOrchestrationGoalDO();
        resource.setPositiveResultHighlightList("1,2,3");
        resource.setNegativeResultHighlightList("4,5,6");
        // act
        ScrmProcessOrchestrationGoalDTO result = converter.convertToDTO(resource);
        // assert
        assertNotNull(result);
        assertEquals(Arrays.asList(1L, 2L, 3L), result.getPositiveResultHighlightList());
        assertEquals(Arrays.asList(4L, 5L, 6L), result.getNegativeResultHighlightList());
    }

    /**
     * Tests the convertToDO method when the resource is null.
     */
    @Test
    public void testConvertToDONullResource() throws Throwable {
        ScrmProcessOrchestrationGoalDTO resource = null;
        ScrmAmProcessOrchestrationGoalDO result = converter.convertToDO(resource);
        assertNull(result);
    }

    /**
     * Tests the convertToDO method when the resource is not null, but positiveResultHighlightList and negativeResultHighlightList are empty.
     */
    @Test
    public void testConvertToDOEmptyHighlightLists() throws Throwable {
        ScrmProcessOrchestrationGoalDTO resource = new ScrmProcessOrchestrationGoalDTO();
        resource.setId(1L);
        resource.setCheckTime("10:00");
        resource.setCheckTimeUnit("hour");
        resource.setStatus((byte) 1);
        resource.setProcessOrchestrationId(1L);
        resource.setProcessOrchestrationVersion("1.0");
        resource.setGoalType((byte) 1);
        resource.setCareNegativeResult(true);
        ScrmAmProcessOrchestrationGoalDO result = converter.convertToDO(resource);
        assertNotNull(result);
        assertEquals(resource.getId(), result.getId());
        assertEquals(resource.getCheckTime(), result.getCheckTime());
        assertEquals(resource.getCheckTimeUnit(), result.getCheckTimeUnit());
        assertEquals(resource.getStatus(), result.getStatus());
        assertEquals(resource.getProcessOrchestrationId(), result.getProcessOrchestrationId());
        assertEquals(resource.getProcessOrchestrationVersion(), result.getProcessOrchestrationVersion());
        assertEquals(resource.getGoalType(), result.getGoalType());
        assertEquals(resource.getCareNegativeResult(), result.getCareNegativeResult());
        assertNull(result.getPositiveResultHighlightList());
        assertNull(result.getNegativeResultHighlightList());
    }

    /**
     * Tests the convertToDO method when the resource is not null, and positiveResultHighlightList and negativeResultHighlightList are not empty.
     */
    @Test
    public void testConvertToDONonEmptyHighlightLists() throws Throwable {
        ScrmProcessOrchestrationGoalDTO resource = new ScrmProcessOrchestrationGoalDTO();
        resource.setId(1L);
        resource.setCheckTime("10:00");
        resource.setCheckTimeUnit("hour");
        resource.setStatus((byte) 1);
        resource.setProcessOrchestrationId(1L);
        resource.setProcessOrchestrationVersion("1.0");
        resource.setGoalType((byte) 1);
        resource.setCareNegativeResult(true);
        resource.setPositiveResultHighlightList(Arrays.asList(1L, 2L, 3L));
        resource.setNegativeResultHighlightList(Arrays.asList(4L, 5L, 6L));
        ScrmAmProcessOrchestrationGoalDO result = converter.convertToDO(resource);
        assertNotNull(result);
        assertEquals(resource.getId(), result.getId());
        assertEquals(resource.getCheckTime(), result.getCheckTime());
        assertEquals(resource.getCheckTimeUnit(), result.getCheckTimeUnit());
        assertEquals(resource.getStatus(), result.getStatus());
        assertEquals(resource.getProcessOrchestrationId(), result.getProcessOrchestrationId());
        assertEquals(resource.getProcessOrchestrationVersion(), result.getProcessOrchestrationVersion());
        assertEquals(resource.getGoalType(), result.getGoalType());
        assertEquals(resource.getCareNegativeResult(), result.getCareNegativeResult());
        assertEquals("1,2,3", result.getPositiveResultHighlightList());
        assertEquals("4,5,6", result.getNegativeResultHighlightList());
    }
}
