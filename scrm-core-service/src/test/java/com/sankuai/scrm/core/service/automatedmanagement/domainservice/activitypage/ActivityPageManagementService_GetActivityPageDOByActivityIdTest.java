package com.sankuai.scrm.core.service.automatedmanagement.domainservice.activitypage;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductTagsDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationProductActivityPageDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationProductTagsDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductActivityPageDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductTagsDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationProductTagMapDOMapper;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ActivityPageManagementService_GetActivityPageDOByActivityIdTest {

    @InjectMocks
    private ActivityPageManagementService activityPageManagementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductActivityPageDOMapper activityPageDOMapper;

    private List<Long> activityIds;

    private String appId;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductTagsDOMapper productTagsDOMapper;

    @Mock(lenient = true)
    private ExtScrmAmProcessOrchestrationProductTagMapDOMapper extScrmAmProcessOrchestrationProductTagMapDOMapper;

    @Before
    public void setUp() {
        activityIds = Arrays.asList(1L, 2L, 3L);
        appId = "testAppId";
    }

    @Test
    public void testGetActivityPageDOByActivityIdWithEmptyActivityId() {
        List<ScrmAmProcessOrchestrationProductActivityPageDO> result = activityPageManagementService.getActivityPageDOByActivityId(Collections.emptyList(), appId);
        assertNull(result);
    }

    @Test
    public void testGetActivityPageDOByActivityIdWithEmptyAppId() {
        List<ScrmAmProcessOrchestrationProductActivityPageDO> result = activityPageManagementService.getActivityPageDOByActivityId(activityIds, "");
        assertNull(result);
    }

    @Test
    public void testGetActivityPageDOByActivityIdWithNoMatch() {
        when(activityPageDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductActivityPageDOExample.class))).thenReturn(Collections.emptyList());
        List<ScrmAmProcessOrchestrationProductActivityPageDO> result = activityPageManagementService.getActivityPageDOByActivityId(activityIds, appId);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetActivityPageDOByActivityIdWithMatch() {
        ScrmAmProcessOrchestrationProductActivityPageDO activityPageDO = new ScrmAmProcessOrchestrationProductActivityPageDO();
        activityPageDO.setId(1L);
        when(activityPageDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductActivityPageDOExample.class))).thenReturn(Collections.singletonList(activityPageDO));
        List<ScrmAmProcessOrchestrationProductActivityPageDO> result = activityPageManagementService.getActivityPageDOByActivityId(activityIds, appId);
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getId().longValue());
    }

    @Test
    public void testBatchDeleteActivityPagesActivityIdsIsNull() throws Throwable {
        String appId = "testAppId";
        activityPageManagementService.batchDeleteActivityPages(appId, null);
        verify(productTagsDOMapper, never()).deleteByExample(any());
        verify(activityPageDOMapper, never()).deleteByExample(any());
        verify(extScrmAmProcessOrchestrationProductTagMapDOMapper, never()).deleteByExample(any());
    }

    @Test(expected = Exception.class)
    public void testBatchDeleteActivityPagesAppIdIsNull() throws Throwable {
        String appId = "";
        activityPageManagementService.batchDeleteActivityPages(appId, Arrays.asList(1L, 2L, 3L));
    }

    @Test(expected = Exception.class)
    public void testBatchDeleteActivityPagesDeleteTagsByTagIdsFailed() throws Throwable {
        String appId = "testAppId";
        activityPageManagementService.batchDeleteActivityPages(appId, Arrays.asList(1L, 2L, 3L));
    }

    @Test(expected = Exception.class)
    public void testBatchDeleteActivityPagesDeletePagesByIdsFailed() throws Throwable {
        String appId = "testAppId";
        activityPageManagementService.batchDeleteActivityPages(appId, Arrays.asList(1L, 2L, 3L));
    }

    @Test(expected = Exception.class)
    public void testBatchDeleteActivityPagesNormal() throws Throwable {
        String appId = "testAppId";
        activityPageManagementService.batchDeleteActivityPages(appId, Arrays.asList(1L, 2L, 3L));
    }
}
