package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.AttachmentVO;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class OfficialWxHandlerBuildActivityPageAttachmentsTest {

    private OfficialWxHandler officialWxHandler;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private WeChatTokenAcl weChatTokenAcl;

    @BeforeEach
    void setUp() throws Exception {
        officialWxHandler = spy(new OfficialWxHandler());
        Field appConfigField = OfficialWxHandler.class.getDeclaredField("appConfigRepository");
        appConfigField.setAccessible(true);
        appConfigField.set(officialWxHandler, appConfigRepository);
        Field weChatTokenField = OfficialWxHandler.class.getDeclaredField("weChatTokenAcl");
        weChatTokenField.setAccessible(true);
        weChatTokenField.set(officialWxHandler, weChatTokenAcl);
    }

    /**
     * Test buildActivityPageAttachments with existing attachments
     */
    @Test
    public void testBuildActivityPageAttachmentsWithExistingAttachments() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setProcessOrchestrationNodeId(1L);
        ScrmAmProcessOrchestrationProductActivityPageDO pageDO = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageDO.setId(1L);
        List<ScrmAmProcessOrchestrationProductActivityPageDO> pageDOS = new ArrayList<>();
        pageDOS.add(pageDO);
        Map<String, AttachmentVO> existedAttachmentMap = new HashMap<>();
        AttachmentVO existingAttachment = new AttachmentVO();
        existedAttachmentMap.put("1-1", existingAttachment);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        stepExecuteResultDTO.setSuccess(true);
        // Mock the token retrieval
        WeChatTokenResult tokenResult = WeChatTokenResult.builder().errcode(0).errmsg("ok").access_token("testToken").build();
        // Mock the attachment retrieval
        doReturn(existingAttachment).when(officialWxHandler).getActivityPageAttachmentVO(any(), any(), any(), any(), any(), any());
        // act
        List<AttachmentVO> result = officialWxHandler.buildActivityPageAttachments(processOrchestrationDTO, actionAttachmentDTO, null, pageDOS, existedAttachmentMap, stepExecuteResultDTO, false);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertTrue(stepExecuteResultDTO.isSuccess());
    }

    /**
     * Test buildActivityPageAttachments with empty pageDOS list
     */
    @Test
    public void testBuildActivityPageAttachmentsWithEmptyPageDOS() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setProcessOrchestrationNodeId(1L);
        List<ScrmAmProcessOrchestrationProductActivityPageDO> pageDOS = new ArrayList<>();
        Map<String, AttachmentVO> existedAttachmentMap = new HashMap<>();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        stepExecuteResultDTO.setSuccess(true);
        // act
        List<AttachmentVO> result = officialWxHandler.buildActivityPageAttachments(processOrchestrationDTO, actionAttachmentDTO, null, pageDOS, existedAttachmentMap, stepExecuteResultDTO, false);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        assertTrue(stepExecuteResultDTO.isSuccess());
    }

    /**
     * Test buildActivityPageAttachments where token retrieval fails
     */
    @Test
    public void testBuildActivityPageAttachmentsWithTokenRetrievalFailure() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setProcessOrchestrationNodeId(1L);
        ScrmAmProcessOrchestrationProductActivityPageDO pageDO = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageDO.setId(1L);
        List<ScrmAmProcessOrchestrationProductActivityPageDO> pageDOS = new ArrayList<>();
        pageDOS.add(pageDO);
        Map<String, AttachmentVO> existedAttachmentMap = new HashMap<>();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        stepExecuteResultDTO.setSuccess(true);
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("testCorpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(WeChatTokenResult.builder().errcode(1).errmsg("Token retrieval failed").build());
        // act
        List<AttachmentVO> result = officialWxHandler.buildActivityPageAttachments(processOrchestrationDTO, actionAttachmentDTO, null, pageDOS, existedAttachmentMap, stepExecuteResultDTO, false);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        assertFalse(stepExecuteResultDTO.isSuccess());
        assertEquals("供给类官方途径群发消息-构建活动页失败-获取token失败", stepExecuteResultDTO.getMsg());
    }
}
