package com.sankuai.scrm.core.service.infrastructure.mq.consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.consumer.IMessageListener;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import java.lang.reflect.Field;
import java.util.Properties;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CorpWxGroupChangeConsumerTest {

    @Mock
    private IConsumerProcessor mockConsumer;

    @InjectMocks
    private CorpWxGroupChangeConsumer consumerUnderTest;

    private void setConsumerField(CorpWxGroupChangeConsumer instance, IConsumerProcessor consumer) throws NoSuchFieldException, IllegalAccessException {
        Field consumerField = CorpWxGroupChangeConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(instance, consumer);
    }

    @BeforeEach
    void setUp() {
        reset(mockConsumer);
    }

    /**
     * Test case when consumer is null - should do nothing
     */
    @Test
    public void testDestroyWhenConsumerIsNull() throws Throwable {
        // arrange
        CorpWxGroupChangeConsumer consumer = new CorpWxGroupChangeConsumer();
        setConsumerField(consumer, null);
        // act & assert
        assertDoesNotThrow(() -> consumer.destroy());
    }

    /**
     * Test case when consumer is not null - should call close()
     */
    @Test
    public void testDestroyWhenConsumerExists() throws Throwable {
        // arrange
        CorpWxGroupChangeConsumer consumer = new CorpWxGroupChangeConsumer();
        setConsumerField(consumer, mockConsumer);
        // act
        consumer.destroy();
        // assert
        verify(mockConsumer).close();
        verifyNoMoreInteractions(mockConsumer);
    }

    /**
     * Test case when consumer throws exception on close - should propagate exception
     */
    @Test
    public void testDestroyWhenConsumerThrowsException() throws Throwable {
        // arrange
        CorpWxGroupChangeConsumer consumer = new CorpWxGroupChangeConsumer();
        setConsumerField(consumer, mockConsumer);
        Exception expectedException = new Exception("Close failed");
        doThrow(expectedException).when(mockConsumer).close();
        // act & assert
        Exception actualException = assertThrows(Exception.class, () -> consumer.destroy());
        verify(mockConsumer).close();
        verifyNoMoreInteractions(mockConsumer);
    }

    /**
     * 测试正常初始化流程
     */
    @Test
    void testAfterPropertiesSet_Success() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            // Arrange
            ArgumentCaptor<Properties> propsCaptor = ArgumentCaptor.forClass(Properties.class);
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(propsCaptor.capture(), eq("corp.wx.group.change"))).thenReturn(mockConsumer);
            // Act
            consumerUnderTest.afterPropertiesSet();
            // Assert
            Properties capturedProps = propsCaptor.getValue();
            assertAll(() -> assertEquals("pingtai", capturedProps.getProperty(ConsumerConstants.MafkaBGNamespace)), () -> assertEquals("com.sankuai.medicalcosmetology.scrm.core", capturedProps.getProperty(ConsumerConstants.MafkaClientAppkey)), () -> assertEquals("corp.wx.group.change.scrm.consumer", capturedProps.getProperty(ConsumerConstants.SubscribeGroup)));
            verify(mockConsumer).recvMessageWithParallel(eq(String.class), notNull());
        }
    }

    /**
     * 测试当buildConsumerFactory抛出异常时的处理
     */
    @Test
    void testAfterPropertiesSet_WhenBuildConsumerFactoryFails() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            // Arrange
            mockedMafkaClient.when(() -> MafkaClient.<Properties, String>buildConsumerFactory(any(Properties.class), anyString())).thenThrow(new RuntimeException("Build failed"));
            // Act & Assert
            Exception exception = assertThrows(Exception.class, () -> consumerUnderTest.afterPropertiesSet());
            assertEquals("Build failed", exception.getMessage());
        }
    }

    /**
     * 测试当recvMessageWithParallel抛出异常时的处理
     */
    @Test
    void testAfterPropertiesSet_WhenRecvMessageFails() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            // Arrange
            mockedMafkaClient.when(() -> MafkaClient.<Properties, String>buildConsumerFactory(any(Properties.class), anyString())).thenReturn(mockConsumer);
            doThrow(new RuntimeException("Listener setup failed")).when(mockConsumer).recvMessageWithParallel(any(), any());
            // Act & Assert
            Exception exception = assertThrows(Exception.class, () -> consumerUnderTest.afterPropertiesSet());
            assertEquals("Listener setup failed", exception.getMessage());
        }
    }

    /**
     * 测试Properties配置是否正确
     */
    @Test
    void testAfterPropertiesSet_PropertiesConfiguration() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            // Arrange
            ArgumentCaptor<Properties> propsCaptor = ArgumentCaptor.forClass(Properties.class);
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(propsCaptor.capture(), eq("corp.wx.group.change"))).thenReturn(mockConsumer);
            // Act
            consumerUnderTest.afterPropertiesSet();
            // Assert
            Properties capturedProps = propsCaptor.getValue();
            assertAll(() -> assertEquals("pingtai", capturedProps.getProperty(ConsumerConstants.MafkaBGNamespace)), () -> assertEquals("com.sankuai.medicalcosmetology.scrm.core", capturedProps.getProperty(ConsumerConstants.MafkaClientAppkey)), () -> assertEquals("corp.wx.group.change.scrm.consumer", capturedProps.getProperty(ConsumerConstants.SubscribeGroup)));
        }
    }
}
