package com.sankuai.scrm.core.service.couponIntegration.domain;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.couponIntegration.dto.CouponDataDetailDTO;
import com.sankuai.dz.srcm.couponIntegration.request.CouponDataDetailQueryRequest;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.dashboard.domain.Utils;
import com.sankuai.scrm.core.service.infrastructure.acl.sso.SsosvOpenApi;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.junit.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CouponDashBoardDomainServiceQueryDetailByRequest1Test {

    @InjectMocks
    private CouponDashBoardDomainService couponDashBoardDomainService;

    @Mock
    private SsosvOpenApi ssosvOpenApi;

    @Mock
    private Utils utils;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private ScrmSceneCouponRecordsMapper sceneCouponRecordDOMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    void testQueryDetailByRequestWhenRequestIsNull() throws Throwable {
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest((CouponDataDetailQueryRequest) null);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 corpId 为空的情况
     */
    //    @Test
    //    public void testDailyCouponDataSummaryCorpIdIsNull() {
    @Test
    void testQueryDetailByRequestWhenPageSizeIsInvalid() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(0);
        request.setPageNumber(1);
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        assertTrue(result.isEmpty());
    }

    //        // arrange
    //        String corpId = null;
    //        // act
    //        couponDashBoardDomainService.dailyCouponDataSummary(corpId);
    //        // assert
    //        verify(corpAppConfigRepository, never()).getAppIdByCorpId(anyString());
    //    }
    /**
     * 测试 corpId 不为空，但 appId 为空的情况
     */
    @Test
    void testQueryDetailByRequestWhenPageNumberIsInvalid() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(10);
        request.setPageNumber(0);
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        assertTrue(result.isEmpty());
    }

    //    @Test
    //    public void testDailyCouponDataSummaryAppIdIsNull() {
    //        // arrange
    //        String corpId = "corpId";
    //        when(corpAppConfigRepository.getAppIdByCorpId(corpId)).thenReturn(null);
    //        // act
    //        couponDashBoardDomainService.dailyCouponDataSummary(corpId);
    @Test
    void testQueryDetailByRequestWhenNoRecordsFound() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(10);
        request.setPageNumber(1);
        when(sceneCouponRecordDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        assertTrue(result.isEmpty());
    }

    //        // assert
    //        verify(sceneCouponRecordDOMapper, never()).selectByExample(any());
    //        verify(couponDataSummaryDOMapper, never()).insertSelective(any(ScrmCouponDataSummaryDO.class));
    //    }
    /**
     * 测试 corpId 和 appId 都不为空，但查询 ScrmSceneCouponRecordDO 对象列表为空的情况
     */
    //    @Test
    //    public void testDailyCouponDataSummaryCouponRecordDOListIsEmpty() {
    @Test
    void testQueryDetailByRequestWithAuthentication() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(10);
        request.setPageNumber(1);
        request.setPathAppId("pathApp");
        request.setAccessToken("token");
        List<String> authCorpIds = Arrays.asList("corp1", "corp2");
        when(ssosvOpenApi.queryLoginNameByAccessToken("token")).thenReturn("user1");
        when(utils.authentication("user1", "pathApp")).thenReturn(authCorpIds);
        when(corpAppConfigRepository.getAppIdByCorpId("corp1")).thenReturn("app1");
        when(corpAppConfigRepository.getAppIdByCorpId("corp2")).thenReturn("app2");
        when(sceneCouponRecordDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        assertTrue(result.isEmpty());
    }
}
