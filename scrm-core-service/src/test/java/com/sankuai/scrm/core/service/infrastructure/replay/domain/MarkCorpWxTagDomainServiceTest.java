package com.sankuai.scrm.core.service.infrastructure.replay.domain;

import com.sankuai.scrm.core.service.infrastructure.replay.dal.entity.MarkWxTagLog;
import com.sankuai.scrm.core.service.infrastructure.replay.dal.example.MarkWxTagLogExample;
import com.sankuai.scrm.core.service.infrastructure.replay.dal.mapper.MarkWxTagLogMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class MarkCorpWxTagDomainServiceTest {

    @Mock
    private MarkWxTagLogMapper markWxTagLogMapper;

    @InjectMocks
    private MarkCorpWxTagDomainService markCorpWxTagDomainService;

    /**
     * Test successful update of MarkWxTagLog
     */
    @Test
    public void testUpdateMarkTagLog_Success() {
        // arrange
        MarkWxTagLog markWxTagLog = new MarkWxTagLog();
        markWxTagLog.setId(1L);
        markWxTagLog.setContent("test content");
        Date beforeUpdate = new Date();
        when(markWxTagLogMapper.updateByPrimaryKeyWithBLOBs(any(MarkWxTagLog.class))).thenReturn(1);
        // act
        markCorpWxTagDomainService.updateMarkTagLog(markWxTagLog);
        // assert
        ArgumentCaptor<MarkWxTagLog> logCaptor = ArgumentCaptor.forClass(MarkWxTagLog.class);
        verify(markWxTagLogMapper).updateByPrimaryKeyWithBLOBs(logCaptor.capture());
        MarkWxTagLog capturedLog = logCaptor.getValue();
        assertNotNull(capturedLog.getUpdateTime());
        assertTrue(capturedLog.getUpdateTime().after(beforeUpdate) || capturedLog.getUpdateTime().equals(beforeUpdate));
        assertEquals("test content", capturedLog.getContent());
    }

    /**
     * Test update with null input
     */
//    @Test(expected = NullPointerException.class)
//    public void testUpdateMarkTagLog_NullInput() {
//        // act
//        markCorpWxTagDomainService.updateMarkTagLog(null);
//    }

    /**
     * Test update time is set correctly
     */
    @Test
    public void testUpdateMarkTagLog_UpdateTimeSet() {
        // arrange
        MarkWxTagLog markWxTagLog = new MarkWxTagLog();
        markWxTagLog.setId(1L);
        // 10 seconds ago
        Date oldUpdateTime = new Date(System.currentTimeMillis() - 10000);
        markWxTagLog.setUpdateTime(oldUpdateTime);
        // act
        markCorpWxTagDomainService.updateMarkTagLog(markWxTagLog);
        // assert
        ArgumentCaptor<MarkWxTagLog> logCaptor = ArgumentCaptor.forClass(MarkWxTagLog.class);
        verify(markWxTagLogMapper).updateByPrimaryKeyWithBLOBs(logCaptor.capture());
        MarkWxTagLog capturedLog = logCaptor.getValue();
        assertNotNull(capturedLog.getUpdateTime());
        assertTrue(capturedLog.getUpdateTime().after(oldUpdateTime));
    }

    /**
     * Test case for successful marking of a tag when the uniqueKey exists
     */
    @Test
    public void testMarkTagSuccess_ExistingUniqueKey() throws Throwable {
        // arrange
        String uniqueKey = "existingKey";
        MarkWxTagLog existingLog = new MarkWxTagLog();
        existingLog.setUniqueKey(uniqueKey);
        existingLog.setStatus("0");
        when(markWxTagLogMapper.selectByExampleWithBLOBs(any(MarkWxTagLogExample.class))).thenReturn(Collections.singletonList(existingLog));
        // act
        markCorpWxTagDomainService.markTagSuccess(uniqueKey);
        // assert
        verify(markWxTagLogMapper).updateByPrimaryKeyWithBLOBs(argThat(log -> {
            return log.getStatus().equals("1") && log.getUpdateTime() != null;
        }));
    }

    /**
     * Test case for when the uniqueKey doesn't exist
     */
    @Test
    public void testMarkTagSuccess_NonExistentUniqueKey() throws Throwable {
        // arrange
        String uniqueKey = "nonExistentKey";
        when(markWxTagLogMapper.selectByExampleWithBLOBs(any(MarkWxTagLogExample.class))).thenReturn(Collections.emptyList());
        // act
        markCorpWxTagDomainService.markTagSuccess(uniqueKey);
        // assert
        verify(markWxTagLogMapper, never()).updateByPrimaryKeyWithBLOBs(any(MarkWxTagLog.class));
    }

    /**
     * Test case for when the database update fails
     */
    @Test
    public void testMarkTagSuccess_DatabaseUpdateFails() throws Throwable {
        // arrange
        String uniqueKey = "existingKey";
        MarkWxTagLog existingLog = new MarkWxTagLog();
        existingLog.setUniqueKey(uniqueKey);
        existingLog.setStatus("0");
        when(markWxTagLogMapper.selectByExampleWithBLOBs(any(MarkWxTagLogExample.class))).thenReturn(Collections.singletonList(existingLog));
        when(markWxTagLogMapper.updateByPrimaryKeyWithBLOBs(any(MarkWxTagLog.class))).thenThrow(new RuntimeException("Database update failed"));
        // act & assert
        try {
            markCorpWxTagDomainService.markTagSuccess(uniqueKey);
        } catch (RuntimeException e) {
            // Expected exception
        }
        verify(markWxTagLogMapper).updateByPrimaryKeyWithBLOBs(argThat(log -> {
            return log.getStatus().equals("1") && log.getUpdateTime() != null;
        }));
    }

    /**
     * Test saving mark tag log with valid parameters
     */
    @Test
    public void testSaveMarkTagLog_WithValidParameters() {
        // arrange
        String uniqueKey = "testKey";
        String content = "testContent";
        Integer errorCode = 100;
        ArgumentCaptor<MarkWxTagLog> logCaptor = ArgumentCaptor.forClass(MarkWxTagLog.class);
        // act
        markCorpWxTagDomainService.saveMarkTagLog(uniqueKey, content, errorCode);
        // assert
        verify(markWxTagLogMapper).insertSelective(logCaptor.capture());
        MarkWxTagLog capturedLog = logCaptor.getValue();
        assertEquals(uniqueKey, capturedLog.getUniqueKey());
        assertEquals(content, capturedLog.getContent());
        assertEquals(errorCode, capturedLog.getErrorCode());
        assertEquals(Integer.valueOf(1), capturedLog.getErrorCount());
        assertEquals("2", capturedLog.getStatus());
        assertNotNull(capturedLog.getAddTime());
        assertNotNull(capturedLog.getUpdateTime());
    }

    /**
     * Test saving mark tag log with null parameters
     */
    @Test
    public void testSaveMarkTagLog_WithNullParameters() {
        // arrange
        ArgumentCaptor<MarkWxTagLog> logCaptor = ArgumentCaptor.forClass(MarkWxTagLog.class);
        // act
        markCorpWxTagDomainService.saveMarkTagLog(null, null, null);
        // assert
        verify(markWxTagLogMapper).insertSelective(logCaptor.capture());
        MarkWxTagLog capturedLog = logCaptor.getValue();
        assertNull(capturedLog.getUniqueKey());
        assertNull(capturedLog.getContent());
        assertNull(capturedLog.getErrorCode());
        assertEquals(Integer.valueOf(1), capturedLog.getErrorCount());
        assertEquals("2", capturedLog.getStatus());
        assertNotNull(capturedLog.getAddTime());
        assertNotNull(capturedLog.getUpdateTime());
    }

    /**
     * Test saving mark tag log verifying timestamp logic
     */
    @Test
    public void testSaveMarkTagLog_VerifyTimestamps() {
        // arrange
        Date beforeTest = new Date();
        // act
        markCorpWxTagDomainService.saveMarkTagLog("key", "content", 1);
        // assert
        ArgumentCaptor<MarkWxTagLog> logCaptor = ArgumentCaptor.forClass(MarkWxTagLog.class);
        verify(markWxTagLogMapper).insertSelective(logCaptor.capture());
        MarkWxTagLog capturedLog = logCaptor.getValue();
        Date afterTest = new Date();
        assertTrue(capturedLog.getAddTime().compareTo(beforeTest) >= 0);
        assertTrue(capturedLog.getAddTime().compareTo(afterTest) <= 0);
        assertTrue(capturedLog.getUpdateTime().compareTo(beforeTest) >= 0);
        assertTrue(capturedLog.getUpdateTime().compareTo(afterTest) <= 0);
    }

    /**
     * Test saving mark tag log when mapper throws exception
     */
//    @Test(expected = RuntimeException.class)
//    public void testSaveMarkTagLog_WhenMapperThrowsException() {
//        // arrange
//        doThrow(new RuntimeException("DB Error")).when(markWxTagLogMapper).insertSelective(any());
//        // act
//        markCorpWxTagDomainService.saveMarkTagLog("key", "content", 1);
//        // assert - exception expected
//    }

    /**
     * Test case for blank uniqueKey
     */
    @Test
    public void testMarkTag_WhenUniqueKeyIsBlank_ShouldReturnEarly() {
        // arrange
        String uniqueKey = "";
        String content = "test content";
        Integer errorCode = 45035;
        // act
        markCorpWxTagDomainService.markTag(uniqueKey, content, errorCode);
        // assert
        verify(markWxTagLogMapper, never()).selectByExampleWithBLOBs(any());
        verify(markWxTagLogMapper, never()).updateByPrimaryKeyWithBLOBs(any());
        verify(markWxTagLogMapper, never()).insertSelective(any());
    }

    /**
     * Test case for null errorCode
     */
    @Test
    public void testMarkTag_WhenErrorCodeIsNull_ShouldMarkSuccess() {
        // arrange
        String uniqueKey = "testKey";
        String content = "test content";
        Integer errorCode = null;
        MarkWxTagLog existingLog = new MarkWxTagLog();
        existingLog.setUniqueKey(uniqueKey);
        existingLog.setStatus("2");
        when(markWxTagLogMapper.selectByExampleWithBLOBs(any())).thenReturn(Collections.singletonList(existingLog));
        // act
        markCorpWxTagDomainService.markTag(uniqueKey, content, errorCode);
        // assert
        ArgumentCaptor<MarkWxTagLog> logCaptor = ArgumentCaptor.forClass(MarkWxTagLog.class);
        verify(markWxTagLogMapper).updateByPrimaryKeyWithBLOBs(logCaptor.capture());
        assertEquals("1", logCaptor.getValue().getStatus());
    }

    /**
     * Test case for errorCode=45035 with existing log
     */
    @Test
    public void testMarkTag_WhenErrorCode45035AndExistingLog_ShouldUpdate() {
        // arrange
        String uniqueKey = "testKey";
        String content = "test content";
        Integer errorCode = 45035;
        MarkWxTagLog existingLog = new MarkWxTagLog();
        existingLog.setErrorCount(1);
        existingLog.setUniqueKey(uniqueKey);
        when(markWxTagLogMapper.selectByExampleWithBLOBs(any())).thenReturn(Collections.singletonList(existingLog));
        // act
        markCorpWxTagDomainService.markTag(uniqueKey, content, errorCode);
        // assert
        ArgumentCaptor<MarkWxTagLog> logCaptor = ArgumentCaptor.forClass(MarkWxTagLog.class);
        verify(markWxTagLogMapper).updateByPrimaryKeyWithBLOBs(logCaptor.capture());
        assertEquals(Integer.valueOf(2), logCaptor.getValue().getErrorCount());
        assertEquals(Integer.valueOf(45035), logCaptor.getValue().getErrorCode());
    }

    /**
     * Test case for errorCode=45035 with no existing log
     */
    @Test
    public void testMarkTag_WhenErrorCode45035AndNoExistingLog_ShouldInsert() {
        // arrange
        String uniqueKey = "testKey";
        String content = "test content";
        Integer errorCode = 45035;
        when(markWxTagLogMapper.selectByExampleWithBLOBs(any())).thenReturn(Collections.emptyList());
        // act
        markCorpWxTagDomainService.markTag(uniqueKey, content, errorCode);
        // assert
        ArgumentCaptor<MarkWxTagLog> logCaptor = ArgumentCaptor.forClass(MarkWxTagLog.class);
        verify(markWxTagLogMapper).insertSelective(logCaptor.capture());
        assertEquals(uniqueKey, logCaptor.getValue().getUniqueKey());
        assertEquals(content, logCaptor.getValue().getContent());
        assertEquals(Integer.valueOf(45035), logCaptor.getValue().getErrorCode());
        assertEquals(Integer.valueOf(1), logCaptor.getValue().getErrorCount());
        assertEquals("2", logCaptor.getValue().getStatus());
    }

    /**
     * Test case for non-45035 errorCode
     */
    @Test
    public void testMarkTag_WhenErrorCodeNotZeroOr45035_ShouldReturnEarly() {
        // arrange
        String uniqueKey = "testKey";
        String content = "test content";
        Integer errorCode = 45036;
        // act
        markCorpWxTagDomainService.markTag(uniqueKey, content, errorCode);
        // assert
        verify(markWxTagLogMapper, never()).selectByExampleWithBLOBs(any());
        verify(markWxTagLogMapper, never()).updateByPrimaryKeyWithBLOBs(any());
        verify(markWxTagLogMapper, never()).insertSelective(any());
    }

    /**
     * Test case for successful marking of tag with valid input
     * Verifies that:
     * 1. Update time is set to current time
     * 2. Status is set to "1"
     * 3. Mapper's update method is called with correct parameters
     */
    @Test
    public void testMarkTagSuccess_WithValidInput() throws Throwable {
        // arrange
        MarkWxTagLog input = new MarkWxTagLog();
        input.setId(1L);
        input.setUniqueKey("test-key");
        Date beforeTest = new Date();
        // act
        markCorpWxTagDomainService.markTagSuccess(input);
        // assert
        ArgumentCaptor<MarkWxTagLog> logCaptor = ArgumentCaptor.forClass(MarkWxTagLog.class);
        verify(markWxTagLogMapper).updateByPrimaryKeyWithBLOBs(logCaptor.capture());
        MarkWxTagLog captured = logCaptor.getValue();
        assertNotNull("Update time should not be null", captured.getUpdateTime());
        assertEquals("Status should be set to '1'", "1", captured.getStatus());
        assertEquals("Input ID should be preserved", input.getId(), captured.getId());
        assertEquals("Input uniqueKey should be preserved", input.getUniqueKey(), captured.getUniqueKey());
        // Verify update time is recent (within last second)
        Date afterTest = new Date();
        assertNotNull("Update time should not be null", captured.getUpdateTime());
        assertTrue("Update time should be after test start", captured.getUpdateTime().after(beforeTest) || captured.getUpdateTime().equals(beforeTest));
        assertTrue("Update time should be before test end", captured.getUpdateTime().before(afterTest) || captured.getUpdateTime().equals(afterTest));
    }

    /**
     * Test case for marking tag with null input
     * Expected to throw NullPointerException
     */
//    @Test(expected = NullPointerException.class)
//    public void testMarkTagSuccess_WithNullInput() throws Throwable {
//        // act
//        markCorpWxTagDomainService.markTagSuccess((MarkWxTagLog) null);
//    }
    @Test
    public void testQueryByUniqueKey_WhenRecordExists_ShouldReturnFirstRecord() throws Throwable {
        String uniqueKey = "test-key";
        MarkWxTagLog expectedLog = new MarkWxTagLog();
        expectedLog.setId(1L);
        expectedLog.setUniqueKey(uniqueKey);
        expectedLog.setAddTime(new Date());
        when(markWxTagLogMapper.selectByExampleWithBLOBs(any(MarkWxTagLogExample.class))).thenReturn(Arrays.asList(expectedLog));
        MarkWxTagLog result = markCorpWxTagDomainService.queryByUniqueKey(uniqueKey);
        assertNotNull(result);
        assertEquals(expectedLog.getId(), result.getId());
        assertEquals(expectedLog.getUniqueKey(), result.getUniqueKey());
        ArgumentCaptor<MarkWxTagLogExample> exampleCaptor = ArgumentCaptor.forClass(MarkWxTagLogExample.class);
        verify(markWxTagLogMapper).selectByExampleWithBLOBs(exampleCaptor.capture());
        MarkWxTagLogExample capturedExample = exampleCaptor.getValue();
        assertEquals("id desc", capturedExample.getOrderByClause());
        assertEquals(Integer.valueOf(1), capturedExample.getRows());
    }

    @Test
    public void testQueryByUniqueKey_WhenNoRecordExists_ShouldReturnNull() throws Throwable {
        String uniqueKey = "non-existing-key";
        when(markWxTagLogMapper.selectByExampleWithBLOBs(any(MarkWxTagLogExample.class))).thenReturn(Collections.emptyList());
        MarkWxTagLog result = markCorpWxTagDomainService.queryByUniqueKey(uniqueKey);
        assertNull(result);
        verify(markWxTagLogMapper).selectByExampleWithBLOBs(any(MarkWxTagLogExample.class));
    }

//    @Test(expected = RuntimeException.class)
//    public void testQueryByUniqueKey_WhenUniqueKeyIsNull_ShouldReturnNull() throws Throwable {
//        markCorpWxTagDomainService.queryByUniqueKey(null);
//    }

    @Test
    public void testQueryByUniqueKey_WhenMultipleRecordsExist_ShouldReturnFirstRecord() throws Throwable {
        String uniqueKey = "test-key";
        MarkWxTagLog log1 = new MarkWxTagLog();
        log1.setId(1L);
        log1.setUniqueKey(uniqueKey);
        MarkWxTagLog log2 = new MarkWxTagLog();
        log2.setId(2L);
        log2.setUniqueKey(uniqueKey);
        when(markWxTagLogMapper.selectByExampleWithBLOBs(any(MarkWxTagLogExample.class))).thenReturn(Arrays.asList(log1, log2));
        MarkWxTagLog result = markCorpWxTagDomainService.queryByUniqueKey(uniqueKey);
        assertNotNull(result);
        assertEquals(log1.getId(), result.getId());
        verify(markWxTagLogMapper).selectByExampleWithBLOBs(any(MarkWxTagLogExample.class));
    }

    /**
     * Test querying list with all parameters being null
     */
    @Test
    public void testQueryListAllParametersNull() throws Throwable {
        // Arrange
        List<MarkWxTagLog> expectedList = new ArrayList<>();
        when(markWxTagLogMapper.selectByExampleWithBLOBs(any())).thenReturn(expectedList);
        // Act
        List<MarkWxTagLog> result = markCorpWxTagDomainService.queryList(null, null, null, null, null);
        // Assert
        assertEquals(expectedList, result);
        verify(markWxTagLogMapper).selectByExampleWithBLOBs(any());
    }

    /**
     * Test querying list with only maxErrorCount parameter
     */
    @Test
    public void testQueryListOnlyMaxErrorCount() throws Throwable {
        // Arrange
        Integer maxErrorCount = 5;
        List<MarkWxTagLog> expectedList = new ArrayList<>();
        when(markWxTagLogMapper.selectByExampleWithBLOBs(any())).thenReturn(expectedList);
        // Act
        List<MarkWxTagLog> result = markCorpWxTagDomainService.queryList(maxErrorCount, null, null, null, null);
        // Assert
        assertEquals(expectedList, result);
        verify(markWxTagLogMapper).selectByExampleWithBLOBs(any());
        verifyNoMoreInteractions(markWxTagLogMapper);
    }

    /**
     * Test querying list with only startTime parameter
     */
    @Test
    public void testQueryListOnlyStartTime() throws Throwable {
        // Arrange
        Date startTime = new Date();
        List<MarkWxTagLog> expectedList = new ArrayList<>();
        when(markWxTagLogMapper.selectByExampleWithBLOBs(any())).thenReturn(expectedList);
        // Act
        List<MarkWxTagLog> result = markCorpWxTagDomainService.queryList(null, startTime, null, null, null);
        // Assert
        assertEquals(expectedList, result);
        verify(markWxTagLogMapper).selectByExampleWithBLOBs(any());
        verifyNoMoreInteractions(markWxTagLogMapper);
    }

    /**
     * Test querying list with only status parameter
     */
    @Test
    public void testQueryListOnlyStatus() throws Throwable {
        // Arrange
        String status = "ACTIVE";
        List<MarkWxTagLog> expectedList = new ArrayList<>();
        when(markWxTagLogMapper.selectByExampleWithBLOBs(any())).thenReturn(expectedList);
        // Act
        List<MarkWxTagLog> result = markCorpWxTagDomainService.queryList(null, null, status, null, null);
        // Assert
        assertEquals(expectedList, result);
        verify(markWxTagLogMapper).selectByExampleWithBLOBs(any());
        verifyNoMoreInteractions(markWxTagLogMapper);
    }

    /**
     * Test querying list with only offset and rows parameters
     */
    @Test
    public void testQueryListOnlyOffsetAndRows() throws Throwable {
        // Arrange
        Long maxId = 10L;
        Integer rows = 20;
        List<MarkWxTagLog> expectedList = new ArrayList<>();
        when(markWxTagLogMapper.selectByExampleWithBLOBs(any())).thenReturn(expectedList);
        // Act
        List<MarkWxTagLog> result = markCorpWxTagDomainService.queryList(null, null, null, rows, maxId);
        // Assert
        assertEquals(expectedList, result);
        verify(markWxTagLogMapper).selectByExampleWithBLOBs(argThat(example -> {
            if (!(example instanceof MarkWxTagLogExample)) {
                return false;
            }
            MarkWxTagLogExample markWxTagLogExample = (MarkWxTagLogExample) example;
            return rows.equals(markWxTagLogExample.getRows());
        }));
    }

    /**
     * Test querying list with all parameters provided
     */
    @Test
    public void testQueryListAllParametersProvided() throws Throwable {
        // Arrange
        Integer maxErrorCount = 5;
        Date startTime = new Date();
        String status = "ACTIVE";
        Long maxId = 10L;
        Integer rows = 20;
        List<MarkWxTagLog> expectedList = new ArrayList<>();
        when(markWxTagLogMapper.selectByExampleWithBLOBs(any())).thenReturn(expectedList);
        // Act
        List<MarkWxTagLog> result = markCorpWxTagDomainService.queryList(maxErrorCount, startTime, status, rows, maxId);
        // Assert
        assertEquals(expectedList, result);
        verify(markWxTagLogMapper).selectByExampleWithBLOBs(any());
        verifyNoMoreInteractions(markWxTagLogMapper);
    }
}
