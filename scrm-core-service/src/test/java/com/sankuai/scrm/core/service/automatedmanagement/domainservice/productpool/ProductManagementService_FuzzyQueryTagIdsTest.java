package com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool;

import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductTagsDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductTagsDO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Collections;
import java.util.List;
import static org.mockito.ArgumentMatchers.any;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductManagementService_FuzzyQueryTagIdsTest {

    @InjectMocks
    private ProductManagementService productManagementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductTagsDOMapper productTagsDOMapper;

    private String appId;

    private String tagName;

    @Before
    public void setUp() {
        appId = "appId";
        tagName = "tagName";
    }

    /**
     * 测试 appId 或 tagName 为空的情况
     */
    @Test
    public void testFuzzyQueryTagIdsWithEmptyAppIdOrTagName() throws Throwable {
        // arrange
        appId = "";
        // act
        List<String> result = productManagementService.fuzzyQueryTagIds(appId, tagName);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 appId 和 tagName 都不为空，但数据库中没有满足条件的记录的情况
     */
    @Test
    public void testFuzzyQueryTagIdsWithNoMatchedRecords() throws Throwable {
        // arrange
        when(productTagsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        List<String> result = productManagementService.fuzzyQueryTagIds(appId, tagName);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 appId 和 tagName 都不为空，数据库中有满足条件的记录的情况
     */
    @Test
    public void testFuzzyQueryTagIdsWithMatchedRecords() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationProductTagsDO tagDO = new ScrmAmProcessOrchestrationProductTagsDO();
        tagDO.setId(1L);
        when(productTagsDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(tagDO));
        // act
        List<String> result = productManagementService.fuzzyQueryTagIds(appId, tagName);
        // assert
        assertEquals(Collections.singletonList("1"), result);
    }
}
