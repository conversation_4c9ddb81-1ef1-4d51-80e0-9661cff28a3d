package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.automatedmanagement.request.UpdateExcelUploadingCrowdPackRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.UpdateExcelUploadingCrowdPackResultVO;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.CrowdPackInnerService;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.exception.ExcelUploadingCrowdPackException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

/**
 * Test class for ScrmAutoManagementServiceImpl#updateExcelUploadingCrowdPack
 */
@RunWith(MockitoJUnitRunner.class)
public class ScrmAutoManagementServiceImplUpdateExcelUploadingCrowdPackTest {

    @InjectMocks
    private ScrmAutoManagementServiceImpl scrmAutoManagementService;

    @Mock
    private CrowdPackInnerService crowdPackInnerService;

    private UpdateExcelUploadingCrowdPackRequest request;

    private UpdateExcelUploadingCrowdPackResultVO resultVO;

    @Before
    public void setUp() {
        request = new UpdateExcelUploadingCrowdPackRequest();
        request.setUrl("https://msstest.sankuai.com/scrm-s3/军师-人群包导入模板-v28511694306265908207.xls");
        request.setAppId("testApp");
        request.setCrowdPackId(1L);
        resultVO = new UpdateExcelUploadingCrowdPackResultVO();
    }

    /**
     * Test case for null request
     */
    @Test
    public void testUpdateExcelUploadingCrowdPack_NullRequest() throws Throwable {
        // arrange
        request = null;
        // act
        RemoteResponse<UpdateExcelUploadingCrowdPackResultVO> response = scrmAutoManagementService.updateExcelUploadingCrowdPack(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for null URL in request
     */
    @Test
    public void testUpdateExcelUploadingCrowdPack_NullUrl() throws Throwable {
        // arrange
        request.setUrl(null);
        // act
        RemoteResponse<UpdateExcelUploadingCrowdPackResultVO> response = scrmAutoManagementService.updateExcelUploadingCrowdPack(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for blank AppId in request
     */
    @Test
    public void testUpdateExcelUploadingCrowdPack_BlankAppId() throws Throwable {
        // arrange
        request.setAppId("");
        // act
        RemoteResponse<UpdateExcelUploadingCrowdPackResultVO> response = scrmAutoManagementService.updateExcelUploadingCrowdPack(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for null CrowdPackId in request
     */
    @Test
    public void testUpdateExcelUploadingCrowdPack_NullCrowdPackId() throws Throwable {
        // arrange
        request.setCrowdPackId(null);
        // act
        RemoteResponse<UpdateExcelUploadingCrowdPackResultVO> response = scrmAutoManagementService.updateExcelUploadingCrowdPack(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for successful update
     */
    @Test
    public void testUpdateExcelUploadingCrowdPack_Success() throws Throwable {
        // arrange
        when(crowdPackInnerService.updateExcelUploadingCrowdPack(any())).thenReturn(resultVO);
        // act
        RemoteResponse<UpdateExcelUploadingCrowdPackResultVO> response = scrmAutoManagementService.updateExcelUploadingCrowdPack(request);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(resultVO, response.getData());
    }

    /**
     * Test case for ExcelUploadingCrowdPackException
     */
    @Test
    public void testUpdateExcelUploadingCrowdPack_ExcelUploadingException() throws Throwable {
        // arrange
        String errorMessage = "Excel uploading error";
        when(crowdPackInnerService.updateExcelUploadingCrowdPack(any())).thenThrow(new ExcelUploadingCrowdPackException(errorMessage));
        // act
        RemoteResponse<UpdateExcelUploadingCrowdPackResultVO> response = scrmAutoManagementService.updateExcelUploadingCrowdPack(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for general exception
     */
    @Test
    public void testUpdateExcelUploadingCrowdPack_GeneralException() throws Throwable {
        // arrange
        when(crowdPackInnerService.updateExcelUploadingCrowdPack(any())).thenThrow(new RuntimeException("Unexpected error"));
        // act
        RemoteResponse<UpdateExcelUploadingCrowdPackResultVO> response = scrmAutoManagementService.updateExcelUploadingCrowdPack(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }
}
