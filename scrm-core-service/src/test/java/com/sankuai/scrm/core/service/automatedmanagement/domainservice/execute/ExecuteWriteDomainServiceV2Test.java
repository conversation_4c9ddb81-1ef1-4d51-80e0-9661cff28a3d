package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.MdpContextUtils;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationExecutePlanDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationCheckStatusEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationCheckStepEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessExecutionCheckPointDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessExecutionCheckPointDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessExecutionCheckPointDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackReadDomainService;

import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.Assert;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
public class ExecuteWriteDomainServiceV2Test {

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessExecutionCheckPointDOMapper processExecutionCheckPointDOMapper;

    @Mock
    private ExecuteManagementService executeManagementService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试getScrmCrowdPackDetailInfoDTO方法，当查询到用户信息时
     */
    @Test
    public void testGetScrmCrowdPackDetailInfoDTO_WhenUserFound() throws ExecutionException, InterruptedException, TimeoutException {
        String userUnionId = "testUnionId";
        String appId = "testAppId";
        ScrmProcessOrchestrationDTO scrmProcessOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        scrmProcessOrchestrationDTO.setAppId(appId);
        ScrmCrowdPackDetailInfoDTO expectedDTO = new ScrmCrowdPackDetailInfoDTO();
        when(crowdPackReadDomainService.queryUserByUnionId(eq(userUnionId), eq(appId))).thenReturn(expectedDTO);
        ScrmCrowdPackDetailInfoDTO result = executeWriteDomainService.getScrmCrowdPackDetailInfoDTO(scrmProcessOrchestrationDTO, userUnionId);
        verify(crowdPackReadDomainService, times(1)).queryUserByUnionId(eq(userUnionId), eq(appId));
        assert result == expectedDTO;
    }

    /**
     * 测试getScrmCrowdPackDetailInfoDTO方法，当未查询到用户信息且更新成功后再次查询到用户信息时
     */
    @Test
    public void testGetScrmCrowdPackDetailInfoDTO_WhenUserNotFoundThenFoundAfterUpdate() throws ExecutionException, InterruptedException, TimeoutException {
        String userUnionId = "testUnionId";
        String appId = "testAppId";
        ScrmProcessOrchestrationDTO scrmProcessOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        scrmProcessOrchestrationDTO.setAppId(appId);
        ScrmCrowdPackDetailInfoDTO expectedDTO = new ScrmCrowdPackDetailInfoDTO();
        // .thenReturn(null)
        when(crowdPackReadDomainService.queryUserByUnionId(eq(userUnionId), eq(appId))).thenReturn(expectedDTO);
        // when(executeWriteDomainService.updateCrowdPackSubTask(any())).thenReturn(true);
        // doNothing().when(executeWriteDomainService).updateCrowdPackSubTask(any());
        ScrmCrowdPackDetailInfoDTO result = executeWriteDomainService.getScrmCrowdPackDetailInfoDTO(scrmProcessOrchestrationDTO, userUnionId);
        verify(crowdPackReadDomainService, times(1)).queryUserByUnionId(eq(userUnionId), eq(appId));
        // verify(executeWriteDomainService, times(1)).updateCrowdPackSubTask(any());
        assert result == expectedDTO;
    }

    /**
     * 测试getScrmCrowdPackDetailInfoDTO方法，当未查询到用户信息且更新后仍未查询到用户信息时
     */
    @Test
    public void testGetScrmCrowdPackDetailInfoDTO_WhenUserNotFoundEvenAfterUpdate() throws ExecutionException, InterruptedException, TimeoutException {
        String userUnionId = "testUnionId";
        String appId = "testAppId";
        ScrmProcessOrchestrationDTO scrmProcessOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        scrmProcessOrchestrationDTO.setAppId(appId);
        ScrmCrowdPackDetailInfoDTO expectedDTO = new ScrmCrowdPackDetailInfoDTO();
        when(crowdPackReadDomainService.queryUserByUnionId(eq(userUnionId), eq(appId))).thenReturn(expectedDTO);
        // when(executeWriteDomainService.updateCrowdPackSubTask(any())).thenReturn(true);
        ScrmCrowdPackDetailInfoDTO result = executeWriteDomainService.getScrmCrowdPackDetailInfoDTO(scrmProcessOrchestrationDTO, userUnionId);
        verify(crowdPackReadDomainService, times(1)).queryUserByUnionId(eq(userUnionId), eq(appId));
        // verify(executeWriteDomainService, times(1)).updateCrowdPackSubTask(any());
        assert result == expectedDTO;
    }

    /**
     * 测试getScrmCrowdPackDetailInfoDTO方法，当更新操作抛出异常时
     */
    @Test
    public void testGetScrmCrowdPackDetailInfoDTO_WhenUpdateThrowsException() throws ExecutionException, InterruptedException, TimeoutException {
        String userUnionId = "testUnionId";
        String appId = "testAppId";
        ScrmProcessOrchestrationDTO scrmProcessOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        scrmProcessOrchestrationDTO.setAppId(appId);
        ScrmCrowdPackDetailInfoDTO expectedDTO = new ScrmCrowdPackDetailInfoDTO();
        when(crowdPackReadDomainService.queryUserByUnionId(eq(userUnionId), eq(appId))).thenReturn(expectedDTO);
        // when(executeWriteDomainService.updateCrowdPackSubTask(any())).thenThrow(new TimeoutException());
        ScrmCrowdPackDetailInfoDTO result = null;
        result = executeWriteDomainService.getScrmCrowdPackDetailInfoDTO(scrmProcessOrchestrationDTO, userUnionId);
        verify(crowdPackReadDomainService, times(1)).queryUserByUnionId(eq(userUnionId), eq(appId));
        // verify(executeWriteDomainService, times(1)).updateCrowdPackSubTask(any());
        assert result == expectedDTO;
    }

    /**
     * Test case when newStatus is null
     * Expect: No update operation performed
     */
    @Test
    public void testUpdateExecuteLogByIdsWhenNewStatusIsNull() {
        // arrange
        ScrmProcessOrchestrationExecuteStatusTypeEnum newStatus = null;
        List<Long> executeLogIds = Arrays.asList(1L, 2L);
        String sender = "testSender";
        // act
        executeWriteDomainService.updateExecuteLogByIds(newStatus, executeLogIds, sender);
        // assert
        verify(executeLogDOMapper, never()).updateByPrimaryKeySelective(any());
    }

    /**
     * Test case when executeLogIds is empty
     * Expect: No update operation performed
     */
    @Test
    public void testUpdateExecuteLogByIdsWhenExecuteLogIdsIsEmpty() {
        // arrange
        ScrmProcessOrchestrationExecuteStatusTypeEnum newStatus = ScrmProcessOrchestrationExecuteStatusTypeEnum.SUCCESS;
        List<Long> executeLogIds = Collections.emptyList();
        String sender = "testSender";
        // act
        executeWriteDomainService.updateExecuteLogByIds(newStatus, executeLogIds, sender);
        // assert
        verify(executeLogDOMapper, never()).updateByPrimaryKeySelective(any());
    }

    /**
     * Test case when executeLogIds is null
     * Expect: No update operation performed
     */
    @Test
    public void testUpdateExecuteLogByIdsWhenExecuteLogIdsIsNull() {
        // arrange
        ScrmProcessOrchestrationExecuteStatusTypeEnum newStatus = ScrmProcessOrchestrationExecuteStatusTypeEnum.SUCCESS;
        List<Long> executeLogIds = null;
        String sender = "testSender";
        // act
        executeWriteDomainService.updateExecuteLogByIds(newStatus, executeLogIds, sender);
        // assert
        verify(executeLogDOMapper, never()).updateByPrimaryKeySelective(any());
    }

    /**
     * Test case with valid single executeLogId
     * Expect: Update operation performed once with correct parameters
     */
    @Test
    public void testUpdateExecuteLogByIdsWithSingleExecuteLogId() {
        // arrange
        ScrmProcessOrchestrationExecuteStatusTypeEnum newStatus = ScrmProcessOrchestrationExecuteStatusTypeEnum.SUCCESS;
        List<Long> executeLogIds = Collections.singletonList(1L);
        String sender = "testSender";
        // act
        executeWriteDomainService.updateExecuteLogByIds(newStatus, executeLogIds, sender);
        // assert
        verify(executeLogDOMapper, times(1)).updateByPrimaryKeySelective(argThat(logDO -> logDO.getId().equals(1L) && logDO.getStatus() == newStatus.getValue().byteValue() && logDO.getExecutorId().equals(sender) && logDO.getUpdateTime() != null));
    }

    /**
     * Test case with multiple executeLogIds
     * Expect: Update operation performed for each ID with correct parameters
     */
    @Test
    public void testUpdateExecuteLogByIdsWithMultipleExecuteLogIds() {
        // arrange
        ScrmProcessOrchestrationExecuteStatusTypeEnum newStatus = ScrmProcessOrchestrationExecuteStatusTypeEnum.FAILED;
        List<Long> executeLogIds = Arrays.asList(1L, 2L, 3L);
        String sender = "testSender";
        // act
        executeWriteDomainService.updateExecuteLogByIds(newStatus, executeLogIds, sender);
        // assert
        verify(executeLogDOMapper, times(3)).updateByPrimaryKeySelective(argThat(logDO -> logDO.getStatus() == newStatus.getValue().byteValue() && logDO.getExecutorId().equals(sender) && logDO.getUpdateTime() != null));
    }

    /**
     * Test case when sender is null
     * Expect: Update operation performed with null executorId
     */
    @Test
    public void testUpdateExecuteLogByIdsWhenSenderIsNull() {
        // arrange
        ScrmProcessOrchestrationExecuteStatusTypeEnum newStatus = ScrmProcessOrchestrationExecuteStatusTypeEnum.SUCCESS;
        List<Long> executeLogIds = Collections.singletonList(1L);
        String sender = null;
        // act
        executeWriteDomainService.updateExecuteLogByIds(newStatus, executeLogIds, sender);
        // assert
        verify(executeLogDOMapper, times(1)).updateByPrimaryKeySelective(argThat(logDO -> logDO.getId().equals(1L) && logDO.getStatus() == newStatus.getValue().byteValue() && logDO.getExecutorId() == null && logDO.getUpdateTime() != null));
    }

    @Test
    public void testInsertEntranceCheckSuccess() throws Exception {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(100L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);

        Map<Long, ScrmProcessOrchestrationNodeDTO> nodeDTOMap = new HashMap<>();
        List<ScrmCrowdPackDTO> crowdPackDTOList = new ArrayList<>();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();


        // 使用反射调用私有方法

        ReflectionTestUtils.invokeMethod(executeWriteDomainService, "insertEntranceCheck",
                processOrchestrationDTO,nodeDTOMap,crowdPackDTOList,executeManagementDTO);
        // 指定具体的参数类型
        verify(processExecutionCheckPointDOMapper, times(1))
                .insert(any());
    }

    @Test
    public void testUpdateFirstCheckSuccess() throws Exception {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(100L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);


        // 使用反射调用私有方法

        ReflectionTestUtils.invokeMethod(executeWriteDomainService, "updateFirstCheck",
                processOrchestrationDTO);
        // 指定具体的参数类型
        verify(processExecutionCheckPointDOMapper, times(1))
                .updateByExampleSelective(any(),any());
    }

    @Test
    public void testUpdateFourCheckSuccess() throws Exception {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(100L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);


        // 使用反射调用私有方法

        ReflectionTestUtils.invokeMethod(executeWriteDomainService, "updateFourCheck",
                processOrchestrationDTO);
        // 指定具体的参数类型
        verify(processExecutionCheckPointDOMapper, times(1))
                .updateByExampleSelective(any(),any());
    }

    @Test
    public void testUpdateFinishFirstCheckSuccess() throws Exception {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(100L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);


        // 使用反射调用私有方法

        ReflectionTestUtils.invokeMethod(executeWriteDomainService, "updateFinishFirstCheck",
                processOrchestrationDTO);
        // 指定具体的参数类型
        verify(processExecutionCheckPointDOMapper, times(1))
                .updateByExampleSelective(any(),any());
    }

    @Test
    public void testUpdateFinishFourCheckSuccess() throws Exception {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(100L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);


        // 使用反射调用私有方法

        ReflectionTestUtils.invokeMethod(executeWriteDomainService, "updateFinishFourCheck",
                processOrchestrationDTO);
        // 指定具体的参数类型
        verify(processExecutionCheckPointDOMapper, times(1))
                .updateByExampleSelective(any(),any());
    }

    @Test
    public void testUpdateSecondCheckSuccess() throws Exception {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(100L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);


        // 使用反射调用私有方法

        ReflectionTestUtils.invokeMethod(executeWriteDomainService, "updateSecondCheck",
                processOrchestrationDTO);
        // 指定具体的参数类型
        verify(processExecutionCheckPointDOMapper, times(1))
                .updateByExampleSelective(any(),any());
    }

    @Test
    public void testUpdateThirdCheckSuccess() throws Exception {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(100L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);


        // 使用反射调用私有方法

        ReflectionTestUtils.invokeMethod(executeWriteDomainService, "updateThirdCheck",
                processOrchestrationDTO);
        // 指定具体的参数类型
        verify(processExecutionCheckPointDOMapper, times(1))
                .updateByExampleSelective(any(),any());
    }

    @Test
    public void testUpdateFinishSecondCheckSuccess() throws Exception {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(100L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);


        // 使用反射调用私有方法

        ReflectionTestUtils.invokeMethod(executeWriteDomainService, "updateFinishSecondCheck",
                processOrchestrationDTO);
        // 指定具体的参数类型
        verify(processExecutionCheckPointDOMapper, times(1))
                .updateByExampleSelective(any(),any());
    }

    @Test
    public void testUpdateFinishThirdCheckSuccess() throws Exception {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(100L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);


        // 使用反射调用私有方法

        ReflectionTestUtils.invokeMethod(executeWriteDomainService, "updateFinishThirdCheck",
                processOrchestrationDTO);
        // 指定具体的参数类型
        verify(processExecutionCheckPointDOMapper, times(1))
                .updateByExampleSelective(any(),any());
    }

    @Test
    public void testisExistSuccess() throws Exception {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(100L);
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        scrmCrowdPackDetailInfoDTO.setId(100L);
        scrmCrowdPackDetailInfoDTO.setExternalUserWxUnionId("100L");
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);


        // 使用反射调用私有方法

        Boolean isExist = ReflectionTestUtils.invokeMethod(executeWriteDomainService, "isExist",
                processOrchestrationDTO, scrmCrowdPackDetailInfoDTO);
        // 指定具体的参数类型
        verify(executeLogDOMapper, times(1))
                .countByExample(any());
        Assert.assertFalse(isExist);
    }

    @Test
    void testRunNeedRetryCheckProcessOrchestration_WithEmptyCheckList_ShouldReturn() {
        // Given
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
             MockedStatic<MdpContextUtils> mdpMock = mockStatic(MdpContextUtils.class)) {

            lionMock.when(() -> Lion.getInt(anyString(), eq("process.orchestration.check.hour"), eq(1)))
                    .thenReturn(1);
            mdpMock.when(MdpContextUtils::getAppKey).thenReturn("test-app");

            when(processExecutionCheckPointDOMapper.selectByExample(any(ScrmAmProcessExecutionCheckPointDOExample.class)))
                    .thenReturn(Collections.emptyList());

            // When
            executeWriteDomainService.runNeedRetryCheckProcessOrchestration();

            // Then
            verify(processExecutionCheckPointDOMapper).selectByExample(any(ScrmAmProcessExecutionCheckPointDOExample.class));
            verifyNoMoreInteractions(processExecutionCheckPointDOMapper);
        }
    }


    @Test
    void testRunNeedRetryCheckProcessOrchestration_Third_Retry() {
        Date mockHourAgo = new Date(System.currentTimeMillis() - 60 * 60 * 1000);
        List<ScrmAmProcessExecutionCheckPointDO> mockCheckPointList = new ArrayList<>();
        ScrmAmProcessExecutionCheckPointDO entranceCheckPoint = new ScrmAmProcessExecutionCheckPointDO();
        entranceCheckPoint.setId(1L);
        entranceCheckPoint.setStep(ScrmProcessOrchestrationCheckStepEnum.THIRD_STEP.getCode());
        entranceCheckPoint.setStepStatus(ScrmProcessOrchestrationCheckStatusEnum.UN_FINISHED.getCode());
        entranceCheckPoint.setUpdateTime(mockHourAgo);
        entranceCheckPoint.setOrchestrationData("{\"id\":969,\"name\":\"朋友圈-手动推品-商品\",\"processOrchestrationType\":4,\"cron\":\"\",\"beginTime\":1750851780000,\"endTime\":1751024580000,\"status\":1,\"validVersion\":\"1750937600966\",\"updateTime\":1750937600000,\"creatorId\":\"wangxuefei05\",\"lastUpdaterId\":\"wangxuefei05\",\"participationRestrict\":true,\"participationRestrictionsCycle\":0,\"participationRestrictionsTimes\":0,\"appId\":\"hanchengfuwu\",\"previewPic\":\"\",\"cronComment\":\"2025-06-26 19:43:00\",\"executorType\":1,\"effectiveTimeStart\":\"\",\"effectiveTimeEnd\":\"\",\"alarmthreshold\":\"\",\"alarmreceiver\":\"\",\"executorList\":[{\"executorId\":\"presto\",\"executorName\":\"王雪飞\",\"executorType\":2}],\"realTimeSceneId\":null,\"sceneType\":0,\"crowdPackType\":1,\"crowdPackIdList\":[1610],\"groupIdList\":[],\"groupInfoList\":null,\"crowdPackUpdateStrategyInfoDTO\":null,\"goalDTO\":{\"id\":3764,\"checkTime\":\"7\",\"checkTimeUnit\":\"3\",\"status\":1,\"processOrchestrationId\":969,\"processOrchestrationVersion\":\"1750937600966\",\"goalType\":1,\"careNegativeResult\":true,\"positiveResultHighlightList\":[90002,90003,90006],\"negativeResultHighlightList\":null,\"goalConditionList\":[]},\"negativeGoalDTO\":{\"id\":3763,\"checkTime\":\"7\",\"checkTimeUnit\":\"3\",\"status\":1,\"processOrchestrationId\":969,\"processOrchestrationVersion\":\"1750937600966\",\"goalType\":2,\"careNegativeResult\":true,\"positiveResultHighlightList\":[90002,90003,90006],\"negativeResultHighlightList\":null,\"goalConditionList\":[]},\"nodeMediumDTO\":{\"processOrchestrationNodeDTOList\":[{\"nodeId\":0,\"preNodeId\":-1,\"nodeType\":4,\"id\":4373,\"processOrchestrationId\":969,\"processOrchestrationVersion\":\"1750937600966\",\"childrenNodes\":[1750937568717]},{\"nodeId\":1750937568717,\"preNodeId\":0,\"nodeType\":2,\"id\":4374,\"processOrchestrationId\":969,\"processOrchestrationVersion\":\"1750937600966\",\"childrenNodes\":[]}],\"conditionMap\":{},\"actionMap\":{\"1750937568717\":{\"id\":2156,\"actionId\":1,\"processOrchestrationId\":969,\"processOrchestrationVersion\":\"1750937600966\",\"processOrchestrationNodeId\":1750937568717,\"actionType\":3,\"actionSubType\":6,\"updateTime\":1750937600000,\"contentType\":1,\"contentList\":null}},\"actionContentMap\":{\"1750937568717-1\":[{\"id\":2153,\"actionId\":1,\"contentId\":1,\"processOrchestrationId\":969,\"processOrchestrationVersion\":\"1750937600966\",\"processOrchestrationNodeId\":1750937568717,\"content\":\"\",\"updateTime\":1750937600000,\"contentType\":1,\"attachmentDTOList\":null}]},\"actionAttachmentMap\":{\"1750937568717-1-1\":[{\"id\":1607,\"contentId\":1,\"actionId\":1,\"attachmentTypeId\":7,\"attachmentContent\":\"{\\\"couponGroupId\\\":\\\"\\\",\\\"title\\\":\\\"社群专享特价爆品\\\",\\\"supplyType\\\":2,\\\"productType\\\":1,\\\"shelfName\\\":\\\"\\\",\\\"jumpPageType\\\":1,\\\"jumpUrl\\\":\\\"\\\",\\\"productId\\\":\\\"757275156,755059250\\\",\\\"supplyScope\\\":1,\\\"marketingCopy\\\":\\\"手动推品+商品\\\",\\\"marketingCopySource\\\":2,\\\"hotTagList\\\":\\\"\\\",\\\"topproductids\\\":\\\"\\\",\\\"headpicUrl\\\":\\\"https://msstest.sankuai.com/scrm-s3/poster-17509375976824863838421484383881.png\\\",\\\"posterSetting\\\":{\\\"title\\\":\\\"社群专享特价爆品\\\",\\\"image\\\":\\\"\\\",\\\"benefitInfo\\\":\\\"\\\"},\\\"advancedConfigEffect\\\":false,\\\"poiRestrict\\\":true,\\\"qrCodeUrl\\\":\\\"https://img.meituan.net/beautyimg/be2226b39e3e2b8926ed609258773fd730079.jpg\\\"}\",\"processOrchestrationId\":969,\"processOrchestrationVersion\":\"1750937600966\",\"processOrchestrationNodeId\":1750937568717,\"updateTime\":null,\"attachmentSupplyDetailDTO\":{\"supplyType\":2,\"productId\":\"757275156,755059250\",\"productType\":1,\"marketingCopy\":\"手动推品+商品\",\"marketingCopySource\":2,\"supplyScope\":1,\"hotTagList\":\"\",\"shelfName\":\"\",\"title\":\"社群专享特价爆品\",\"couponGroupId\":\"\",\"jumpPageType\":1,\"jumpUrl\":\"\",\"topproductids\":\"\",\"headpicUrl\":\"https://msstest.sankuai.com/scrm-s3/poster-17509375976824863838421484383881.png\",\"qrCodeUrl\":\"https://img.meituan.net/beautyimg/be2226b39e3e2b8926ed609258773fd730079.jpg\",\"advancedConfigEffect\":false,\"poiRestrict\":true,\"couponPeriodOfValidity\":null},\"attachmentContentDetailDTO\":null}]},\"nodeExecuteLogMap\":{},\"branchStatisticsMap\":{}},\"executePlanDTO\":{\"id\":776,\"processOrchestrationId\":969,\"processOrchestrationVersion\":\"1750937600966\",\"processOrchestrationType\":4,\"taskStartTime\":1750938180000,\"status\":1,\"updateTime\":null},\"demoScene\":false,\"demoCorpTagList\":null,\"aiScene\":false,\"aiSceneContent\":null}");
        entranceCheckPoint.setExecuteManagementData("{\"currentActionType\":null,\"taskCount\":0,\"taskSizeLimit\":40,\"staffTaskSizeLimit\":40,\"appid\":\"hanchengfuwu\",\"poiId\":null,\"staffTaskCounts\":{\"presto\":{\"staffId\":\"presto\",\"count\":0}},\"staffLimitSet\":[\"presto\"],\"alternateExecuteLogIdSet\":[],\"existedWxInvokeLogDOMap\":{},\"nodeId2StaffIdMap\":{},\"nodeExecuteLogDTOMap\":{\"0\":{\"id\":null,\"processOrchestrationId\":969,\"processOrchestrationVersion\":\"1750937600966\",\"processOrchestrationNodeId\":0,\"executeCount\":0},\"1750937568717\":{\"id\":null,\"processOrchestrationId\":969,\"processOrchestrationVersion\":\"1750937600966\",\"processOrchestrationNodeId\":1750937568717,\"executeCount\":0}},\"executedStaffIds\":[],\"existedCrowdDetailIds\":[]}");
        mockCheckPointList.add(entranceCheckPoint);
        // Given
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
             MockedStatic<MdpContextUtils> mdpMock = mockStatic(MdpContextUtils.class)) {

            lionMock.when(() -> Lion.getInt(anyString(), eq("process.orchestration.check.hour"), eq(1)))
                    .thenReturn(1);
            mdpMock.when(MdpContextUtils::getAppKey).thenReturn("test-app");

            when(processExecutionCheckPointDOMapper.selectByExample(any(ScrmAmProcessExecutionCheckPointDOExample.class)))
                    .thenReturn(mockCheckPointList);

            // When
            executeWriteDomainService.runNeedRetryCheckProcessOrchestration();

            // Then
            verify(processExecutionCheckPointDOMapper).selectByExample(any(ScrmAmProcessExecutionCheckPointDOExample.class));
        }
    }


}
