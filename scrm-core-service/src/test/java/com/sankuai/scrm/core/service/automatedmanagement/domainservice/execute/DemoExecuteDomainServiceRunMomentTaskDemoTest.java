package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum;
import com.sankuai.dz.srcm.tag.dto.TagDTO;
import com.sankuai.dz.srcm.tag.dto.TagGroupDTO;
import com.sankuai.dz.srcm.tag.dto.TagGroupListDTO;
import com.sankuai.dz.srcm.tag.request.AddCorpTagRequest;
import com.sankuai.dz.srcm.tag.service.TagService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.infrastructure.dal.babymapper.ContactUserDoMapper;
import com.sankuai.scrm.core.service.infrastructure.dal.entity.ContactUserDo;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.scrm.core.service.tag.domain.ExternalContactTagDomainService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * Test class for DemoExecuteDomainService
 */
@RunWith(MockitoJUnitRunner.class)
public class DemoExecuteDomainServiceRunMomentTaskDemoTest {

    @InjectMocks
    private DemoExecuteDomainService demoExecuteDomainService;

    @Mock
    private TagService tagService;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private ExternalContactTagDomainService externalContactTagDomainService;

    @Mock
    private ContactUserDoMapper contactUserDoMapper;

    private MockedStatic<Lion> mockedLion;

    private MockedStatic<Environment> mockedEnvironment;

    private static final String WHITE_LIST_KEY = "refinement.operation.demo.white.list";

    private static final Long PROCESS_ID = 123L;

    private static final String WX_UNION_ID = "test_union_id";

    private static final String APP_NAME = "test-app";

    private static final String TAG_GROUP_ID = "etdfWXVQAAvwqwhbwFFZ--j5Fmgnluyg";

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        mockedLion = mockStatic(Lion.class);
        mockedEnvironment = mockStatic(Environment.class);
        mockedEnvironment.when(Environment::getAppName).thenReturn(APP_NAME);
    }

    @After
    public void tearDown() {
        if (mockedLion != null) {
            mockedLion.close();
        }
        if (mockedEnvironment != null) {
            mockedEnvironment.close();
        }
    }

    private void setupBasicMocks() {
        mockedLion.when(() -> Lion.getList(eq(APP_NAME), eq(WHITE_LIST_KEY), eq(Long.class))).thenReturn(Arrays.asList(PROCESS_ID));
        ScrmProcessOrchestrationDTO orchestrationDTO = new ScrmProcessOrchestrationDTO();
        orchestrationDTO.setId(PROCESS_ID);
        orchestrationDTO.setValidVersion("1.0");
        orchestrationDTO.setAppId("hanchengfuwu");
        orchestrationDTO.setDemoCorpTagList(new ArrayList<>());
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        List<ScrmProcessOrchestrationNodeDTO> nodeDTOList = new ArrayList<>();
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setNodeId(0L);
        nodeDTO.setChildrenNodes(new ArrayList<>());
        nodeDTOList.add(nodeDTO);
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(nodeDTOList);
        orchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(PROCESS_ID)).thenReturn(CompletableFuture.completedFuture(orchestrationDTO));
        CorpAppConfig corpAppConfig = new CorpAppConfig();
        corpAppConfig.setAppId("hanchengfuwu");
        corpAppConfig.setCorpId("ww6155e0a24f071a89");
        when(corpAppConfigRepository.getConfigByAppId("hanchengfuwu")).thenReturn(corpAppConfig);
    }

    private void setupExistingTag() {
        TagGroupListDTO tagGroupListDTO = new TagGroupListDTO();
        List<TagGroupDTO> tagGroupList = new ArrayList<>();
        TagGroupDTO tagGroupDTO = new TagGroupDTO();
        tagGroupDTO.setTagGroupId(TAG_GROUP_ID);
        tagGroupDTO.setTagGroupName("DEMO用");
        List<TagDTO> tagList = new ArrayList<>();
        TagDTO tagDTO = new TagDTO();
        tagDTO.setTagName("hanchengfuwu_" + WX_UNION_ID);
        tagDTO.setTagId("test_tag_id");
        tagList.add(tagDTO);
        tagGroupDTO.setTagList(tagList);
        tagGroupList.add(tagGroupDTO);
        tagGroupListDTO.setTagGroupList(tagGroupList);
        when(tagService.queryTagGroupList(eq("hanchengfuwu"), eq(TAG_GROUP_ID), isNull(), isNull(), isNull())).thenReturn(RemoteResponse.success(tagGroupListDTO));
    }

    private void setupNonExistingTag() {
        // First query - empty tag list
        TagGroupListDTO emptyTagGroupListDTO = new TagGroupListDTO();
        List<TagGroupDTO> emptyTagGroupList = new ArrayList<>();
        TagGroupDTO emptyTagGroupDTO = new TagGroupDTO();
        emptyTagGroupDTO.setTagGroupId(TAG_GROUP_ID);
        emptyTagGroupDTO.setTagGroupName("DEMO用");
        emptyTagGroupDTO.setTagList(new ArrayList<>());
        emptyTagGroupList.add(emptyTagGroupDTO);
        emptyTagGroupListDTO.setTagGroupList(emptyTagGroupList);
        // Second query - tag list with newly created tag
        TagGroupListDTO tagGroupListDTO = new TagGroupListDTO();
        List<TagGroupDTO> tagGroupList = new ArrayList<>();
        TagGroupDTO tagGroupDTO = new TagGroupDTO();
        tagGroupDTO.setTagGroupId(TAG_GROUP_ID);
        tagGroupDTO.setTagGroupName("DEMO用");
        List<TagDTO> tagList = new ArrayList<>();
        TagDTO tagDTO = new TagDTO();
        tagDTO.setTagName("hanchengfuwu_" + WX_UNION_ID);
        tagDTO.setTagId("new_test_tag_id");
        tagList.add(tagDTO);
        tagGroupDTO.setTagList(tagList);
        tagGroupList.add(tagGroupDTO);
        tagGroupListDTO.setTagGroupList(tagGroupList);
        when(tagService.queryTagGroupList(eq("hanchengfuwu"), eq(TAG_GROUP_ID), isNull(), isNull(), isNull())).thenReturn(RemoteResponse.success(emptyTagGroupListDTO)).thenReturn(RemoteResponse.success(tagGroupListDTO));
        when(tagService.addCorpTag(any(AddCorpTagRequest.class))).thenReturn(RemoteResponse.success(true));
    }

    private void setupTagAddition(boolean success) {
        ExternalContactTagDomainService.AddCorpTagResult result = new ExternalContactTagDomainService.AddCorpTagResult();
        result.setResult(success);
        when(externalContactTagDomainService.addCorpTag(eq("hanchengfuwu"), eq("ww6155e0a24f071a89"), eq(WX_UNION_ID), anyString())).thenReturn(result);
    }

    private void setupContactUser() {
        ContactUserDo contactUser = new ContactUserDo();
        contactUser.setExternalUserId("test_external_id");
        when(contactUserDoMapper.selectByExample(any())).thenReturn(Collections.singletonList(contactUser));
    }

    private void setupExecuteWriteSuccess() {
        StepExecuteResultDTO successResult = new StepExecuteResultDTO();
        successResult.setSuccess(true);
        successResult.setProcessOrchestrationId(PROCESS_ID);
        successResult.setProcessOrchestrationVersion("1.0");
        successResult.setCode(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SUCCESS.getCode());
        when(executeWriteDomainService.processNodes(any(ScrmProcessOrchestrationDTO.class), any(ExecuteManagementDTO.class), anyMap(), anyList(), any(ScrmCrowdPackDetailInfoDTO.class), any())).thenReturn(successResult);
        ExecuteManagementDTO managementDTO = new // scrmProcessOrchestrationDTO
                // taskSizeLimit
                // appId
                // staffIds
                // staffTaskSizeLimit
                ExecuteManagementDTO(null, 100, "hanchengfuwu", Arrays.asList("staffId1", "staffId2"), 40);
        when(executeManagementService.getExecuteMediumManagementDTO(any())).thenReturn(managementDTO);
        doNothing().when(executeWriteDomainService).thirdWxGroupTouchStep(any(ScrmProcessOrchestrationDTO.class), any(ExecuteManagementDTO.class), any(StepExecuteResultDTO.class));
        doNothing().when(executeWriteDomainService).noticeStep(any(ScrmProcessOrchestrationDTO.class), any(ExecuteManagementDTO.class));
        doNothing().when(executeWriteDomainService).updateNodeExecuteLog(any(ScrmProcessOrchestrationDTO.class), any(ExecuteManagementDTO.class));
    }

    @Test
    public void testRunMomentTaskDemo_NotInWhiteList() throws Throwable {
        // arrange
        mockedLion.when(() -> Lion.getList(eq(APP_NAME), eq(WHITE_LIST_KEY), eq(Long.class))).thenReturn(Collections.singletonList(456L));
        // act
        StepExecuteResultDTO result = demoExecuteDomainService.runMomentTaskDemo(PROCESS_ID, WX_UNION_ID);
        // assert
        assertFalse(result.isSuccess());
        assertEquals(Integer.valueOf(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getCode()), Integer.valueOf(result.getCode()));
    }

    /*@Test
    public void testRunMomentTaskDemo_TagExists() throws Throwable {
        // arrange
        setupBasicMocks();
        setupExistingTag();
        setupTagAddition(true);
        setupContactUser();
        setupExecuteWriteSuccess();
        // act
        StepExecuteResultDTO result = demoExecuteDomainService.runMomentTaskDemo(PROCESS_ID, WX_UNION_ID);
        // assert
        assertTrue(result.isSuccess());
        verify(tagService, never()).addCorpTag(any());
    }*/

    /*@Test
    public void testRunMomentTaskDemo_CreateNewTag() throws Throwable {
        // arrange
        setupBasicMocks();
        setupNonExistingTag();
        setupTagAddition(true);
        setupContactUser();
        setupExecuteWriteSuccess();
        // act
        StepExecuteResultDTO result = demoExecuteDomainService.runMomentTaskDemo(PROCESS_ID, WX_UNION_ID);
        // assert
        assertTrue(result.isSuccess());
        verify(tagService).addCorpTag(any(AddCorpTagRequest.class));
    }*/

    /*@Test
    public void testRunMomentTaskDemo_TagAdditionFails() throws Throwable {
        // arrange
        setupBasicMocks();
        setupExistingTag();
        setupTagAddition(false);
        // act
        StepExecuteResultDTO result = demoExecuteDomainService.runMomentTaskDemo(PROCESS_ID, WX_UNION_ID);
        // assert
        assertFalse(result.isSuccess());
        assertEquals(Integer.valueOf(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.FAIL.getCode()), Integer.valueOf(result.getCode()));
    }*/

    /*@Test
    public void testRunMomentTaskDemo_ContactUserFound() throws Throwable {
        // arrange
        setupBasicMocks();
        setupExistingTag();
        setupTagAddition(true);
        setupContactUser();
        setupExecuteWriteSuccess();
        // act
        StepExecuteResultDTO result = demoExecuteDomainService.runMomentTaskDemo(PROCESS_ID, WX_UNION_ID);
        // assert
        assertTrue(result.isSuccess());
        verify(executeManagementService, never()).getUsersGroupList(anyString(), anyString());
    }*/

    /*@Test
    public void testRunMomentTaskDemo_MemberInfoFound() throws Throwable {
        // arrange
        setupBasicMocks();
        setupExistingTag();
        setupTagAddition(true);
        setupExecuteWriteSuccess();
        when(contactUserDoMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        MemberInfoEntity memberInfo = new MemberInfoEntity();
        memberInfo.setGroupMemberId("test_member_id");
        when(executeManagementService.getUsersGroupList(WX_UNION_ID, "hanchengfuwu")).thenReturn(Collections.singletonList(memberInfo));
        // act
        StepExecuteResultDTO result = demoExecuteDomainService.runMomentTaskDemo(PROCESS_ID, WX_UNION_ID);
        // assert
        assertTrue(result.isSuccess());
        verify(executeManagementService).getUsersGroupList(WX_UNION_ID, "hanchengfuwu");
    }*/
}