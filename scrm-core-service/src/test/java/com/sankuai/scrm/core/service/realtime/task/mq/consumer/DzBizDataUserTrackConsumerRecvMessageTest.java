package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.dianping.tgc.process.enums.PlatformEnum;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.aigc.service.config.AISceneABTestConfig;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineABTestRecordMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineMqMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.dto.ScrmDzBizDataUserTrackDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineABTestRecordMessageProducer;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineUserFootprintDiversionProducer;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DzBizDataUserTrackConsumerRecvMessageTest {

    @Mock
    private RealTimeTaskConsumerConfig consumerConfig;

    @Mock
    private GroupRetailAiEngineUserFootprintDiversionProducer userFootprintDiversionProducer;

    @InjectMocks
    private DzBizDataUserTrackConsumer dzBizDataUserTrackConsumer;

    @Mock
    private AISceneABTestConfig aiSceneABTestConfig;

    @Mock
    private GroupRetailAiEngineABTestRecordMessageProducer abTestRecordMessageProducer;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    private Method recvMessageMethod;

    private ConsumeStatus invokeRecvMessage(MafkaMessage<String> message, MessagetContext context) throws Exception {
        Method method = DzBizDataUserTrackConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        method.setAccessible(true);
        return (ConsumeStatus) method.invoke(dzBizDataUserTrackConsumer, message, context);
    }

    @BeforeEach
    void setUp() throws Exception {
        recvMessageMethod = DzBizDataUserTrackConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        recvMessageMethod.setAccessible(true);
    }

    @Test
    void testRecvMessage_NullMessageBody_ReturnsSuccess() throws Throwable {
        // Arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(null);
        try (MockedStatic<JsonUtils> jsonUtils = mockStatic(JsonUtils.class)) {
            jsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("mocked message string");
            // Act
            ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(dzBizDataUserTrackConsumer, message, new MessagetContext());
            // Assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verifyNoInteractions(userFootprintDiversionProducer, abTestRecordMessageProducer);
        }
    }

    @Test
    void testRecvMessage_EmptyUserIdList_ReturnsSuccess() throws Throwable {
        // Arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        ScrmDzBizDataUserTrackDTO dto = new ScrmDzBizDataUserTrackDTO();
        dto.setUserid(Collections.emptyList());
        String jsonBody = "{\"userid\":[]}";
        when(message.getBody()).thenReturn(jsonBody);
        try (MockedStatic<JsonUtils> jsonUtils = mockStatic(JsonUtils.class)) {
            jsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("mocked message string");
            jsonUtils.when(() -> JsonUtils.toObjectSafe(eq(jsonBody), eq(ScrmDzBizDataUserTrackDTO.class))).thenReturn(dto);
            // Act
            ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(dzBizDataUserTrackConsumer, message, new MessagetContext());
            // Assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verifyNoInteractions(userFootprintDiversionProducer, abTestRecordMessageProducer);
        }
    }

    @Test
    void testRecvMessage_MtUserInWhitelist_SendsFootprintMessage() throws Throwable {
        // Arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        ScrmDzBizDataUserTrackDTO dto = new ScrmDzBizDataUserTrackDTO();
        dto.setUserid(Arrays.asList(123L));
        dto.setPlatformlist(Arrays.asList("mt"));
        String jsonBody = "{\"userid\":[123],\"platformlist\":[\"mt\"]}";
        when(message.getBody()).thenReturn(jsonBody);
        try (MockedStatic<JsonUtils> jsonUtils = mockStatic(JsonUtils.class)) {
            jsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("mocked message string");
            jsonUtils.when(() -> JsonUtils.toObjectSafe(eq(jsonBody), eq(ScrmDzBizDataUserTrackDTO.class))).thenReturn(dto);
            when(consumerConfig.isInWhitelistAppIdUnknown(anyList(), eq(PlatformEnum.MT))).thenReturn(true);
            // Act
            ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(dzBizDataUserTrackConsumer, message, new MessagetContext());
            // Assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(userFootprintDiversionProducer).sendMessage(any(GroupRetailAiEngineMqMessageDTO.class));
            verify(abTestRecordMessageProducer, never()).sendMessage(any());
        }
    }

    @Test
    void testRecvMessage_DpUserInWhitelist_SendsFootprintMessage() throws Throwable {
        // Arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        ScrmDzBizDataUserTrackDTO dto = new ScrmDzBizDataUserTrackDTO();
        dto.setUserid(Arrays.asList(123L));
        dto.setPlatformlist(Arrays.asList("dp"));
        String jsonBody = "{\"userid\":[123],\"platformlist\":[\"dp\"]}";
        when(message.getBody()).thenReturn(jsonBody);
        try (MockedStatic<JsonUtils> jsonUtils = mockStatic(JsonUtils.class)) {
            jsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("mocked message string");
            jsonUtils.when(() -> JsonUtils.toObjectSafe(eq(jsonBody), eq(ScrmDzBizDataUserTrackDTO.class))).thenReturn(dto);
            when(consumerConfig.isInWhitelistAppIdUnknown(anyList(), eq(PlatformEnum.DP))).thenReturn(true);
            // Act
            ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(dzBizDataUserTrackConsumer, message, new MessagetContext());
            // Assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(userFootprintDiversionProducer).sendMessage(any(GroupRetailAiEngineMqMessageDTO.class));
            verify(abTestRecordMessageProducer, never()).sendMessage(any());
        }
    }

    @Test
    void testRecvMessage_NonWhitelistMtUserInControlGroup_SendsAbTestRecord() throws Throwable {
        // Arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        ScrmDzBizDataUserTrackDTO dto = new ScrmDzBizDataUserTrackDTO();
        dto.setUserid(Arrays.asList(123L));
        dto.setPlatformlist(Arrays.asList("mt"));
        String jsonBody = "{\"userid\":[123],\"platformlist\":[\"mt\"]}";
        when(message.getBody()).thenReturn(jsonBody);
        try (MockedStatic<JsonUtils> jsonUtils = mockStatic(JsonUtils.class)) {
            jsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("mocked message string");
            jsonUtils.when(() -> JsonUtils.toObjectSafe(eq(jsonBody), eq(ScrmDzBizDataUserTrackDTO.class))).thenReturn(dto);
            when(consumerConfig.isInWhitelistAppIdUnknown(anyList(), any())).thenReturn(false);
            when(aiSceneABTestConfig.checkIsInNonWxUserControlGroup()).thenReturn(true);
            when(aiSceneABTestConfig.getTestVersion()).thenReturn("v1");
            // Act
            ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(dzBizDataUserTrackConsumer, message, new MessagetContext());
            // Assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(userFootprintDiversionProducer, never()).sendMessage(any());
            verify(abTestRecordMessageProducer).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        }
    }

    @Test
    void testRecvMessage_NonWhitelistDpUserWithConversion_SendsAbTestRecord() throws Throwable {
        // Arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        ScrmDzBizDataUserTrackDTO dto = new ScrmDzBizDataUserTrackDTO();
        dto.setUserid(Arrays.asList(123L));
        dto.setPlatformlist(Arrays.asList("dp"));
        String jsonBody = "{\"userid\":[123],\"platformlist\":[\"dp\"]}";
        when(message.getBody()).thenReturn(jsonBody);
        try (MockedStatic<JsonUtils> jsonUtils = mockStatic(JsonUtils.class)) {
            jsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("mocked message string");
            jsonUtils.when(() -> JsonUtils.toObjectSafe(eq(jsonBody), eq(ScrmDzBizDataUserTrackDTO.class))).thenReturn(dto);
            when(consumerConfig.isInWhitelistAppIdUnknown(anyList(), any())).thenReturn(false);
            when(aiSceneABTestConfig.checkIsInNonWxUserControlGroup()).thenReturn(true);
            when(mtUserCenterAclService.getMtUserIdByDpUserId(123L)).thenReturn(456L);
            when(aiSceneABTestConfig.getTestVersion()).thenReturn("v1");
            // Act
            ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(dzBizDataUserTrackConsumer, message, new MessagetContext());
            // Assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(userFootprintDiversionProducer, never()).sendMessage(any());
            verify(abTestRecordMessageProducer).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        }
    }

    @Test
    void testRecvMessage_NonWhitelistDpUserConversionFailed_ReturnsSuccess() throws Throwable {
        // Arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        ScrmDzBizDataUserTrackDTO dto = new ScrmDzBizDataUserTrackDTO();
        dto.setUserid(Arrays.asList(123L));
        dto.setPlatformlist(Arrays.asList("dp"));
        String jsonBody = "{\"userid\":[123],\"platformlist\":[\"dp\"]}";
        when(message.getBody()).thenReturn(jsonBody);
        try (MockedStatic<JsonUtils> jsonUtils = mockStatic(JsonUtils.class)) {
            jsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("mocked message string");
            jsonUtils.when(() -> JsonUtils.toObjectSafe(eq(jsonBody), eq(ScrmDzBizDataUserTrackDTO.class))).thenReturn(dto);
            when(consumerConfig.isInWhitelistAppIdUnknown(anyList(), any())).thenReturn(false);
            when(aiSceneABTestConfig.checkIsInNonWxUserControlGroup()).thenReturn(true);
            when(mtUserCenterAclService.getMtUserIdByDpUserId(123L)).thenReturn(null);
            // Act
            ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(dzBizDataUserTrackConsumer, message, new MessagetContext());
            // Assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verifyNoInteractions(userFootprintDiversionProducer, abTestRecordMessageProducer);
        }
    }

    @Test
    void testRecvMessage_InvalidJson_ReturnsSuccess() throws Throwable {
        // Arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("invalid json");
        try (MockedStatic<JsonUtils> jsonUtils = mockStatic(JsonUtils.class)) {
            jsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("mocked message string");
            jsonUtils.when(() -> JsonUtils.toObjectSafe(eq("invalid json"), eq(ScrmDzBizDataUserTrackDTO.class))).thenReturn(null);
            // Act
            ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(dzBizDataUserTrackConsumer, message, new MessagetContext());
            // Assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verifyNoInteractions(userFootprintDiversionProducer, abTestRecordMessageProducer);
        }
    }

    @Test
    void testRecvMessage_GeneralException_ReturnsSuccess() throws Throwable {
        // Arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenThrow(new RuntimeException("test exception"));
        try (MockedStatic<JsonUtils> jsonUtils = mockStatic(JsonUtils.class)) {
            jsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("mocked message string");
            // Act
            ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(dzBizDataUserTrackConsumer, message, new MessagetContext());
            // Assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verifyNoInteractions(userFootprintDiversionProducer, abTestRecordMessageProducer);
        }
    }
}
