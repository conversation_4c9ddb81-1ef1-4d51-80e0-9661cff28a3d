package com.sankuai.scrm.core.service.infrastructure.acl.ds.enums;

import static org.junit.Assert.*;
import com.dianping.cat.Cat;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class MessageStrategyTypeEnumTest {

    /**
     * 测试 getDesc 方法
     */
    @Test
    public void testGetDesc() throws Throwable {
        // arrange
        MessageStrategyTypeEnum messageStrategyTypeEnum = MessageStrategyTypeEnum.RIGHT_NOW;
        // act
        String result = messageStrategyTypeEnum.getDesc();
        // assert
        assertEquals("立即发送", result);
    }

    /**
     * 测试 getDesc 方法，当枚举值为 FIXED_TIME 时
     */
    @Test
    public void testGetDescWhenFixedTime() throws Throwable {
        // arrange
        MessageStrategyTypeEnum messageStrategyTypeEnum = MessageStrategyTypeEnum.FIXED_TIME;
        // act
        String result = messageStrategyTypeEnum.getDesc();
        // assert
        assertEquals("定时发送", result);
    }
}
