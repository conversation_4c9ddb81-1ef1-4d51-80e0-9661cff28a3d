package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGroupSendMessageAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxGroupSendMessageRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.AttachmentVO;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.TextVO;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGroupSendMessageResponse;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.dto.SendListDTO;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerDealNormalOfficialWxGroupMessageTest {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private WxGroupSendMessageAcl wxGroupSendMessageAcl;

    @Mock
    private WeChatTokenAcl weChatTokenAcl;

    @Mock
    private ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private String executorId;

    private InvokeDetailKeyObject keyObject;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS;

    private StepExecuteResultDTO stepExecuteResultDTO;

    @Mock
    private ScrmAmProcessOrchestrationProductActivityPageDO pageInfo;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        processOrchestrationDTO.setAppId("testAppId");
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        executorId = "testExecutorId";
        keyObject = new InvokeDetailKeyObject("testKey", (byte) 1, (byte) 1, 1L);
        totalInvokeDetailDOS = new ArrayList<>();
        stepExecuteResultDTO = new StepExecuteResultDTO();
    }

    /**
     * Test already sent message scenario
     */
    @Test
    public void testDealNormalOfficialWxGroupMessage_AlreadySentMessage() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setProcessOrchestrationNodeId(1L);
        totalInvokeDetailDOS.add(detailDO);
        ScrmAmProcessOrchestrationWxInvokeLogDO existingLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        existingLogDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.CREATED_FAILED.getValue().byteValue());
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(existingLogDO));
        // act
        officialWxHandler.dealNormalOfficialWxGroupMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, stepExecuteResultDTO);
        // assert
        verify(wxGroupSendMessageAcl, never()).groupSendMessage(any(), any(), any());
    }

    /**
     * Test buildKey with all valid parameters and non-empty values
     * Verifies the key is correctly generated with all components
     */
    @Test
    public void testBuildKeyAllParametersValid() throws Throwable {
        // arrange
        String executorId = "executor123";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(1L);
        wxInvokeDetailDO.setType("messageType");
        WxGroupSendMessageRequest request = new WxGroupSendMessageRequest();
        request.setChat_type("group");
        AttachmentVO attachment = new AttachmentVO();
        request.setAttachments(Arrays.asList(attachment));
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("testContent");
        TextVO textVO = new TextVO();
        textVO.setContent("textContent");
        // act
        String result = OfficialWxHandler.buildKey(executorId, wxInvokeDetailDO, request, contentDTO, textVO);
        // assert
        assertNotNull(result);
        String[] parts = result.split("-");
        assertEquals("executor123", parts[0]);
        assertEquals("1", parts[1]);
        assertEquals("messageType", parts[2]);
        assertEquals("group", parts[3]);
        // content hash
        assertNotEquals("0", parts[4]);
        // text content length
        assertNotNull(parts[5]);
        // text hash
        assertNotEquals("0", parts[6]);
        // attachments hash
        assertNotEquals("0", parts[7]);
        // attachments size
        assertEquals("1", parts[8]);
    }

    /**
     * Test buildKey with null content in contentDTO
     * Verifies content hash is 0 when content is null
     */
    @Test
    public void testBuildKeyNullContent() throws Throwable {
        // arrange
        String executorId = "executor123";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(1L);
        wxInvokeDetailDO.setType("messageType");
        WxGroupSendMessageRequest request = new WxGroupSendMessageRequest();
        request.setChat_type("group");
        request.setAttachments(Collections.emptyList());
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent(null);
        TextVO textVO = new TextVO();
        textVO.setContent("textContent");
        // act
        String result = OfficialWxHandler.buildKey(executorId, wxInvokeDetailDO, request, contentDTO, textVO);
        // assert
        assertNotNull(result);
        String[] parts = result.split("-");
        // content hash should be 0
        assertEquals("0", parts[4]);
        // text content length
        assertNotNull(parts[5]);
        // attachments hash
        assertEquals("0", parts[7]);
        // attachments size
        assertEquals("0", parts[8]);
    }

    /**
     * Test buildKey with null TextVO
     * Verifies text length and hash are 0 when TextVO is null
     */
    @Test
    public void testBuildKeyNullTextVO() throws Throwable {
        // arrange
        String executorId = "executor123";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(1L);
        wxInvokeDetailDO.setType("messageType");
        WxGroupSendMessageRequest request = new WxGroupSendMessageRequest();
        request.setChat_type("group");
        request.setAttachments(Collections.emptyList());
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("testContent");
        // act
        String result = OfficialWxHandler.buildKey(executorId, wxInvokeDetailDO, request, contentDTO, null);
        // assert
        assertNotNull(result);
        String[] parts = result.split("-");
        // text content length should be 0
        assertEquals("0", parts[5]);
        // text hash should be 0
        assertEquals("0", parts[6]);
        // attachments hash
        assertEquals("0", parts[7]);
        // attachments size
        assertEquals("0", parts[8]);
    }

    /**
     * Test buildKey with empty attachments list
     * Verifies attachments hash and size are 0 when attachments list is empty
     */
    @Test
    public void testBuildKeyEmptyAttachments() throws Throwable {
        // arrange
        String executorId = "executor123";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(1L);
        wxInvokeDetailDO.setType("messageType");
        WxGroupSendMessageRequest request = new WxGroupSendMessageRequest();
        request.setChat_type("group");
        request.setAttachments(Collections.emptyList());
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("testContent");
        TextVO textVO = new TextVO();
        textVO.setContent("textContent");
        // act
        String result = OfficialWxHandler.buildKey(executorId, wxInvokeDetailDO, request, contentDTO, textVO);
        // assert
        assertNotNull(result);
        String[] parts = result.split("-");
        // attachments hash should be 0
        assertEquals("0", parts[7]);
        // attachments size should be 0
        assertEquals("0", parts[8]);
    }

    /**
     * Test case for chosen supply with empty related product IDs
     */
    @Test
    public void testBuildActivityPageAttachments_ChosenSupplyWithEmptyRelatedProductIds() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        List<ScrmAmProcessOrchestrationProductActivityPageDO> pageDOS = new ArrayList<>();
        Map<String, AttachmentVO> existedAttachmentMap = new HashMap<>();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        when(pageInfo.getRelatedProductIds()).thenReturn("");
        pageDOS.add(pageInfo);
        // act
        List<AttachmentVO> result = officialWxHandler.buildActivityPageAttachments(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageDOS, existedAttachmentMap, stepExecuteResultDTO, true);
        // assert
        assertTrue(result.isEmpty());
        assertEquals("供给类官方途径群发消息-非自动召回-活动页内容关联商品为空", stepExecuteResultDTO.getMsg());
        assertTrue(stepExecuteResultDTO.isExistedFailedAttachmentUpload());
        assertFalse(stepExecuteResultDTO.isSuccess());
        assertEquals(11, stepExecuteResultDTO.getCode());
    }

    /**
     * Test case when getActivityPageAttachmentVO returns null
     */
    @Test
    public void testBuildActivityPageAttachments_GetActivityPageAttachmentVOReturnsNull() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        List<ScrmAmProcessOrchestrationProductActivityPageDO> pageDOS = new ArrayList<>();
        Map<String, AttachmentVO> existedAttachmentMap = new HashMap<>();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        pageDOS.add(pageInfo);
        OfficialWxHandler spyHandler = spy(officialWxHandler);
        doReturn(null).when(spyHandler).getActivityPageAttachmentVO(any(), any(), any(), any(), any(), any());
        // act
        List<AttachmentVO> result = spyHandler.buildActivityPageAttachments(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageDOS, existedAttachmentMap, stepExecuteResultDTO, false);
        // assert
        assertTrue(result.isEmpty());
        verify(spyHandler).getActivityPageAttachmentVO(eq(processOrchestrationDTO), eq(actionAttachmentDTO), eq(supplyDetailDTO), eq(pageInfo), eq(existedAttachmentMap), eq(stepExecuteResultDTO));
    }

    /**
     * Test case when stepExecuteResultDTO success is false
     */
    @Test
    public void testBuildActivityPageAttachments_StepExecuteResultDTOSuccessIsFalse() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        List<ScrmAmProcessOrchestrationProductActivityPageDO> pageDOS = new ArrayList<>();
        Map<String, AttachmentVO> existedAttachmentMap = new HashMap<>();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        stepExecuteResultDTO.setSuccess(false);
        pageDOS.add(pageInfo);
        OfficialWxHandler spyHandler = spy(officialWxHandler);
        AttachmentVO attachmentVO = new AttachmentVO();
        doReturn(attachmentVO).when(spyHandler).getActivityPageAttachmentVO(any(), any(), any(), any(), any(), any());
        // act
        List<AttachmentVO> result = spyHandler.buildActivityPageAttachments(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageDOS, existedAttachmentMap, stepExecuteResultDTO, false);
        // assert
        assertTrue(result.isEmpty());
        verify(spyHandler).getActivityPageAttachmentVO(eq(processOrchestrationDTO), eq(actionAttachmentDTO), eq(supplyDetailDTO), eq(pageInfo), eq(existedAttachmentMap), eq(stepExecuteResultDTO));
    }

    /**
     * Test when send list is empty - should do nothing
     */
    @Test
    public void testDealPreventDisturbanceResultEmptyList() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        List<SendListDTO> sendListDTOS = Collections.emptyList();
        // act
        officialWxHandler.dealPreventDisturbanceResult(processOrchestrationId, processOrchestrationVersion, invokeLogDO, sendListDTOS);
        // assert
        verifyNoInteractions(wxInvokeDetailDOMapper);
        verifyNoInteractions(executeLogDOMapper);
    }

    /**
     * Test when no failed sends (status != 3) - should do nothing
     */
    @Test
    public void testDealPreventDisturbanceResultNoFailedSends() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        SendListDTO sendDTO = new SendListDTO();
        // not failed status
        sendDTO.setStatus(1);
        List<SendListDTO> sendListDTOS = Collections.singletonList(sendDTO);
        // act
        officialWxHandler.dealPreventDisturbanceResult(processOrchestrationId, processOrchestrationVersion, invokeLogDO, sendListDTOS);
        // assert
        verifyNoInteractions(wxInvokeDetailDOMapper);
        verifyNoInteractions(executeLogDOMapper);
    }

    /**
     * Test when executeLogId is null - should skip that record
     */
    @Test
    public void testDealPreventDisturbanceResultNullExecuteLogId() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setType("message");
        SendListDTO sendDTO = new SendListDTO();
        // failed status
        sendDTO.setStatus(3);
        sendDTO.setExternalUserid("user1");
        List<SendListDTO> sendListDTOS = Collections.singletonList(sendDTO);
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        // null executeLogId
        detailDO.setExecuteLogId(null);
        // WAIT_FOR_SEND status
        detailDO.setStatus((byte) 5);
        // Match the external user ID
        detailDO.setTargetId("user1");
        detailDO.setProcessOrchestrationId(processOrchestrationId);
        detailDO.setProcessOrchestrationVersion(processOrchestrationVersion);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOs = Collections.singletonList(detailDO);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(detailDOs);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // act
        officialWxHandler.dealPreventDisturbanceResult(processOrchestrationId, processOrchestrationVersion, invokeLogDO, sendListDTOS);
        // assert
        verify(wxInvokeDetailDOMapper).selectByExample(any());
        verify(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }
}
