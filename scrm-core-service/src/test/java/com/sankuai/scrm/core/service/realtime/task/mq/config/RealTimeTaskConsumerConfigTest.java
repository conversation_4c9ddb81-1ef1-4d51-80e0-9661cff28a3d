package com.sankuai.scrm.core.service.realtime.task.mq.config;

import com.dianping.lion.Environment;
import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.ConfigValue;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import com.sankuai.scrm.core.service.realtime.task.domainservice.ScrmGrowthUserInfoDomainService;
import com.sankuai.scrm.core.service.realtime.task.dto.RealTimeTaskConsumerConfigDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import java.util.*;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
public class RealTimeTaskConsumerConfigTest {

    @InjectMocks
    private RealTimeTaskConsumerConfig realTimeTaskConsumerConfig;

    @Mock
    private RealTimeTaskConsumerConfigDTO configDTO;

    @Mock
    private ConfigRepository mockConfigRepository;

    private MockedStatic<Lion> mockedLion;
    private MockedStatic<Environment> mockedEnvironment;
    private MockedStatic<JsonUtils> mockedJsonUtils;

    private static final String LION_KEY = "real_time_task_consumer_config";
    private static final String TEST_APP_NAME = "test_app_name";

    @Mock
    private ScrmGrowthUserInfoDomainService scrmGrowthUserInfoDomainService;

    @BeforeEach
    void setUp() throws Exception {
        mockedEnvironment = mockStatic(Environment.class);
        mockedLion = mockStatic(Lion.class);
        mockedJsonUtils = mockStatic(JsonUtils.class);

        mockedLion.when(Lion::getConfigRepository).thenReturn(mockConfigRepository);
        mockedEnvironment.when(Environment::getAppName).thenReturn(TEST_APP_NAME);
        mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("{}");

        // 设置 configRepository 字段
        Field configRepositoryField = RealTimeTaskConsumerConfig.class.getDeclaredField("configRepository");
        configRepositoryField.setAccessible(true);
        configRepositoryField.set(realTimeTaskConsumerConfig, mockConfigRepository);
    }

    @AfterEach
    void tearDown() {
        if (mockedLion != null) {
            mockedLion.close();
        }
        if (mockedEnvironment != null) {
            mockedEnvironment.close();
        }
        if (mockedJsonUtils != null) {
            mockedJsonUtils.close();
        }
    }

    private void callInitMethod() throws Exception {
        Method initMethod = RealTimeTaskConsumerConfig.class.getDeclaredMethod("init");
        initMethod.setAccessible(true);
        initMethod.invoke(realTimeTaskConsumerConfig);
    }

    private void setConfigDTO(RealTimeTaskConsumerConfig config, RealTimeTaskConsumerConfigDTO configDTO)
            throws Exception {
        Field field = RealTimeTaskConsumerConfig.class.getDeclaredField("configDTO");
        field.setAccessible(true);
        field.set(config, configDTO);
    }

    @Test
    void init_shouldInitializeSuccessfully_whenLionReturnsConfig() throws Exception {
        // Arrange
        RealTimeTaskConsumerConfigDTO expectedDto = new RealTimeTaskConsumerConfigDTO();
        expectedDto.setDispatchDelayTime(100L);

        mockedLion.when(() -> Lion.getBean(TEST_APP_NAME, LION_KEY, RealTimeTaskConsumerConfigDTO.class))
                .thenReturn(expectedDto);

        // Act
        callInitMethod();

        // Assert
        mockedLion.verify(() -> Lion.getBean(TEST_APP_NAME, LION_KEY, RealTimeTaskConsumerConfigDTO.class), times(1));
        verify(mockConfigRepository, times(1)).addConfigListener(eq(LION_KEY), eq(realTimeTaskConsumerConfig));

        RealTimeTaskConsumerConfigDTO actualDto = realTimeTaskConsumerConfig.getConfigDTO();
        assertNotNull(actualDto, "ConfigDTO should not be null after init");
        assertEquals(expectedDto.getDispatchDelayTime(), actualDto.getDispatchDelayTime(),
                "ConfigDTO should be updated from Lion");
    }

    @Test
    void init_shouldSetDtoToNull_whenLionReturnsNull() throws Exception {
        // Arrange
        mockedLion.when(() -> Lion.getBean(TEST_APP_NAME, LION_KEY, RealTimeTaskConsumerConfigDTO.class))
                .thenReturn(null);

        assertNotNull(realTimeTaskConsumerConfig.getConfigDTO());

        // Act
        callInitMethod();

        // Assert
        mockedLion.verify(() -> Lion.getBean(TEST_APP_NAME, LION_KEY, RealTimeTaskConsumerConfigDTO.class), times(1));
        verify(mockConfigRepository, times(1)).addConfigListener(eq(LION_KEY), eq(realTimeTaskConsumerConfig));
        assertNull(realTimeTaskConsumerConfig.getConfigDTO(),
                "ConfigDTO should be null when Lion.getBean returns null");
    }

    @Test
    void init_shouldThrowException_whenLionGetBeanThrowsException() {
        // Arrange
        RuntimeException lionException = new RuntimeException("Lion getBean failed");
        mockedLion.when(() -> Lion.getBean(TEST_APP_NAME, LION_KEY, RealTimeTaskConsumerConfigDTO.class))
                .thenThrow(lionException);

        // Act & Assert
        Exception actualException = assertThrows(Exception.class, this::callInitMethod);
        assertEquals(lionException, actualException.getCause(), "Expected RuntimeException from Lion to be the cause");

        verify(mockConfigRepository, never()).addConfigListener(anyString(), any());
    }

    @Test
    void init_shouldThrowException_whenAddConfigListenerThrowsException() throws Exception {
        // Arrange
        RealTimeTaskConsumerConfigDTO dummyDto = new RealTimeTaskConsumerConfigDTO();
        mockedLion.when(() -> Lion.getBean(TEST_APP_NAME, LION_KEY, RealTimeTaskConsumerConfigDTO.class))
                .thenReturn(dummyDto);

        RuntimeException listenerException = new RuntimeException("Add listener failed");
        doThrow(listenerException).when(mockConfigRepository).addConfigListener(eq(LION_KEY),
                eq(realTimeTaskConsumerConfig));

        // Act & Assert
        Exception actualException = assertThrows(Exception.class, this::callInitMethod);
        assertEquals(listenerException, actualException.getCause(),
                "Expected RuntimeException from addConfigListener to be the cause");

        mockedLion.verify(() -> Lion.getBean(TEST_APP_NAME, LION_KEY, RealTimeTaskConsumerConfigDTO.class), times(1));
    }

    /**
     * 测试当appId存在映射时，返回正确的processId
     */
    @Test
    public void testGetProcessIdByAppIdWhenAppIdExists() throws Throwable {
        // arrange
        String appId = "testApp";
        Long expectedProcessId = 12345L;
        Map<String, Long> mockMap = new HashMap<>();
        mockMap.put(appId, expectedProcessId);
        when(configDTO.getAiTaskAppId2ProcessIdMap()).thenReturn(mockMap);
        // act
        Long actualProcessId = realTimeTaskConsumerConfig.getProcessIdByAppId(appId);
        // assert
        assertEquals(expectedProcessId, actualProcessId);
    }

    /**
     * 测试当appId不存在映射时，返回null
     */
    @Test
    public void testGetProcessIdByAppIdWhenAppIdNotExists() throws Throwable {
        // arrange
        String appId = "nonExistApp";
        Map<String, Long> mockMap = new HashMap<>();
        mockMap.put("otherApp", 12345L);
        when(configDTO.getAiTaskAppId2ProcessIdMap()).thenReturn(mockMap);
        // act
        Long actualProcessId = realTimeTaskConsumerConfig.getProcessIdByAppId(appId);
        // assert
        assertNull(actualProcessId);
    }

    /**
     * 测试当appId为null时，返回null
     */
    @Test
    public void testGetProcessIdByAppIdWhenAppIdIsNull() throws Throwable {
        // arrange
        Map<String, Long> mockMap = new HashMap<>();
        mockMap.put("testApp", 12345L);
        when(configDTO.getAiTaskAppId2ProcessIdMap()).thenReturn(mockMap);
        // act
        Long actualProcessId = realTimeTaskConsumerConfig.getProcessIdByAppId(null);
        // assert
        assertNull(actualProcessId);
    }

    /**
     * 测试当aiTaskAppId2ProcessIdMap为null时，抛出NullPointerException
     */
    @Test
    public void testGetProcessIdByAppIdWhenMapIsNull() throws Throwable {
        // arrange
        String appId = "testApp";
        when(configDTO.getAiTaskAppId2ProcessIdMap()).thenReturn(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            realTimeTaskConsumerConfig.getProcessIdByAppId(appId);
        });
    }

    /**
     * 测试当前时间在有效时间范围内的情况
     */
    @Test
    public void testCheckInEffectiveTimeWhenCurrentTimeIsWithinRange() throws Throwable {
        // arrange
        RealTimeTaskConsumerConfig config = new RealTimeTaskConsumerConfig();
        RealTimeTaskConsumerConfigDTO configDTO = new RealTimeTaskConsumerConfigDTO();
        configDTO.setTaskEffectTime("10:00:00");
        configDTO.setTaskEffectTimeEnd("19:59:59");
        setConfigDTO(config, configDTO);
        try (MockedStatic<DateUtil> mockedDateUtil = mockStatic(DateUtil.class)) {
            // 12:00:00
            mockedDateUtil.when(DateUtil::getSecondOfDayNow).thenReturn(12 * 3600);
            mockedDateUtil.when(() -> DateUtil.getSecondOfDayByHHmmss("10:00:00")).thenReturn(10 * 3600);
            mockedDateUtil.when(() -> DateUtil.getSecondOfDayByHHmmss("19:59:59")).thenReturn(19 * 3600 + 59 * 60 + 59);
            // act
            boolean result = config.checkInEffectiveTime();
            // assert
            assertTrue(result);
        }
    }

    /**
     * 测试当前时间早于有效开始时间的情况
     */
    @Test
    public void testCheckInEffectiveTimeWhenCurrentTimeIsBeforeStartTime() throws Throwable {
        // arrange
        RealTimeTaskConsumerConfig config = new RealTimeTaskConsumerConfig();
        RealTimeTaskConsumerConfigDTO configDTO = new RealTimeTaskConsumerConfigDTO();
        configDTO.setTaskEffectTime("10:00:00");
        configDTO.setTaskEffectTimeEnd("19:59:59");
        setConfigDTO(config, configDTO);
        try (MockedStatic<DateUtil> mockedDateUtil = mockStatic(DateUtil.class)) {
            // 09:00:00
            mockedDateUtil.when(DateUtil::getSecondOfDayNow).thenReturn(9 * 3600);
            mockedDateUtil.when(() -> DateUtil.getSecondOfDayByHHmmss("10:00:00")).thenReturn(10 * 3600);
            mockedDateUtil.when(() -> DateUtil.getSecondOfDayByHHmmss("19:59:59")).thenReturn(19 * 3600 + 59 * 60 + 59);
            // act
            boolean result = config.checkInEffectiveTime();
            // assert
            assertFalse(result);
        }
    }

    /**
     * 测试当前时间晚于有效结束时间的情况
     */
    @Test
    public void testCheckInEffectiveTimeWhenCurrentTimeIsAfterEndTime() throws Throwable {
        // arrange
        RealTimeTaskConsumerConfig config = new RealTimeTaskConsumerConfig();
        RealTimeTaskConsumerConfigDTO configDTO = new RealTimeTaskConsumerConfigDTO();
        configDTO.setTaskEffectTime("10:00:00");
        configDTO.setTaskEffectTimeEnd("19:59:59");
        setConfigDTO(config, configDTO);
        try (MockedStatic<DateUtil> mockedDateUtil = mockStatic(DateUtil.class)) {
            // 20:00:00
            mockedDateUtil.when(DateUtil::getSecondOfDayNow).thenReturn(20 * 3600);
            mockedDateUtil.when(() -> DateUtil.getSecondOfDayByHHmmss("10:00:00")).thenReturn(10 * 3600);
            mockedDateUtil.when(() -> DateUtil.getSecondOfDayByHHmmss("19:59:59")).thenReturn(19 * 3600 + 59 * 60 + 59);
            // act
            boolean result = config.checkInEffectiveTime();
            // assert
            assertFalse(result);
        }
    }

    /**
     * 测试当前时间等于有效开始时间的边界情况
     */
    @Test
    public void testCheckInEffectiveTimeWhenCurrentTimeEqualsStartTime() throws Throwable {
        // arrange
        RealTimeTaskConsumerConfig config = new RealTimeTaskConsumerConfig();
        RealTimeTaskConsumerConfigDTO configDTO = new RealTimeTaskConsumerConfigDTO();
        configDTO.setTaskEffectTime("10:00:00");
        configDTO.setTaskEffectTimeEnd("19:59:59");
        setConfigDTO(config, configDTO);
        try (MockedStatic<DateUtil> mockedDateUtil = mockStatic(DateUtil.class)) {
            // 10:00:00
            mockedDateUtil.when(DateUtil::getSecondOfDayNow).thenReturn(10 * 3600);
            mockedDateUtil.when(() -> DateUtil.getSecondOfDayByHHmmss("10:00:00")).thenReturn(10 * 3600);
            mockedDateUtil.when(() -> DateUtil.getSecondOfDayByHHmmss("19:59:59")).thenReturn(19 * 3600 + 59 * 60 + 59);
            // act
            boolean result = config.checkInEffectiveTime();
            // assert
            assertFalse(result);
        }
    }

    /**
     * 测试当前时间等于有效结束时间的边界情况
     */
    @Test
    public void testCheckInEffectiveTimeWhenCurrentTimeEqualsEndTime() throws Throwable {
        // arrange
        RealTimeTaskConsumerConfig config = new RealTimeTaskConsumerConfig();
        RealTimeTaskConsumerConfigDTO configDTO = new RealTimeTaskConsumerConfigDTO();
        configDTO.setTaskEffectTime("10:00:00");
        configDTO.setTaskEffectTimeEnd("19:59:59");
        setConfigDTO(config, configDTO);
        try (MockedStatic<DateUtil> mockedDateUtil = mockStatic(DateUtil.class)) {
            // 19:59:59
            mockedDateUtil.when(DateUtil::getSecondOfDayNow).thenReturn(19 * 3600 + 59 * 60 + 59);
            mockedDateUtil.when(() -> DateUtil.getSecondOfDayByHHmmss("10:00:00")).thenReturn(10 * 3600);
            mockedDateUtil.when(() -> DateUtil.getSecondOfDayByHHmmss("19:59:59")).thenReturn(19 * 3600 + 59 * 60 + 59);
            // act
            boolean result = config.checkInEffectiveTime();
            // assert
            assertFalse(result);
        }
    }

    /**
     * 测试taskEffectTime格式错误的情况
     */
    @Test
    public void testCheckInEffectiveTimeWhenTaskEffectTimeFormatInvalid() throws Throwable {
        // arrange
        RealTimeTaskConsumerConfig config = new RealTimeTaskConsumerConfig();
        RealTimeTaskConsumerConfigDTO configDTO = new RealTimeTaskConsumerConfigDTO();
        configDTO.setTaskEffectTime("invalid-time");
        configDTO.setTaskEffectTimeEnd("19:59:59");
        setConfigDTO(config, configDTO);
        try (MockedStatic<DateUtil> mockedDateUtil = mockStatic(DateUtil.class)) {
            // 12:00:00
            mockedDateUtil.when(DateUtil::getSecondOfDayNow).thenReturn(12 * 3600);
            mockedDateUtil.when(() -> DateUtil.getSecondOfDayByHHmmss("invalid-time"))
                    .thenThrow(new RuntimeException("Invalid time format"));
            mockedDateUtil.when(() -> DateUtil.getSecondOfDayByHHmmss("19:59:59")).thenReturn(19 * 3600 + 59 * 60 + 59);
            // act & assert
            assertThrows(RuntimeException.class, () -> config.checkInEffectiveTime());
        }
    }

    /**
     * 测试taskEffectTimeEnd格式错误的情况
     */
    @Test
    public void testCheckInEffectiveTimeWhenTaskEffectTimeEndFormatInvalid() throws Throwable {
        // arrange
        RealTimeTaskConsumerConfig config = new RealTimeTaskConsumerConfig();
        RealTimeTaskConsumerConfigDTO configDTO = new RealTimeTaskConsumerConfigDTO();
        configDTO.setTaskEffectTime("10:00:00");
        configDTO.setTaskEffectTimeEnd("invalid-time");
        setConfigDTO(config, configDTO);
        try (MockedStatic<DateUtil> mockedDateUtil = mockStatic(DateUtil.class)) {
            // 12:00:00
            mockedDateUtil.when(DateUtil::getSecondOfDayNow).thenReturn(12 * 3600);
            mockedDateUtil.when(() -> DateUtil.getSecondOfDayByHHmmss("10:00:00")).thenReturn(10 * 3600);
            mockedDateUtil.when(() -> DateUtil.getSecondOfDayByHHmmss("invalid-time"))
                    .thenThrow(new RuntimeException("Invalid time format"));
            // act & assert
            assertThrows(RuntimeException.class, () -> config.checkInEffectiveTime());
        }
    }

    /**
     * Test normal case where config event has valid new config
     */
    @Test
    public void testConfigChangedNormalCase() throws Throwable {
        // arrange
        ConfigEvent configEvent = new ConfigEvent("key", new ConfigValue("key", "value", "source"), null);
        RealTimeTaskConsumerConfigDTO newConfig = new RealTimeTaskConsumerConfigDTO();
        newConfig.setDispatchDelayTime(1000L);
        mockedEnvironment.when(Environment::getAppName).thenReturn("testApp");
        mockedLion.when(() -> Lion.getBean(anyString(), anyString(), any())).thenReturn(newConfig);
        RealTimeTaskConsumerConfigDTO originalConfig = realTimeTaskConsumerConfig.getConfigDTO();
        // act
        realTimeTaskConsumerConfig.configChanged(configEvent);
        // assert
        assertNotEquals(originalConfig, realTimeTaskConsumerConfig.getConfigDTO());
        assertEquals(newConfig, realTimeTaskConsumerConfig.getConfigDTO());
    }

    /**
     * Test case where Lion.getBean() returns null
     */
    @Test
    public void testConfigChangedNullConfig() throws Throwable {
        // arrange
        ConfigEvent configEvent = new ConfigEvent("key", new ConfigValue("key", "value", "source"), null);
        mockedEnvironment.when(Environment::getAppName).thenReturn("testApp");
        mockedLion.when(() -> Lion.getBean(anyString(), anyString(), any())).thenReturn(null);
        RealTimeTaskConsumerConfigDTO originalConfig = realTimeTaskConsumerConfig.getConfigDTO();
        // act
        realTimeTaskConsumerConfig.configChanged(configEvent);
        // assert
        assertEquals(originalConfig, realTimeTaskConsumerConfig.getConfigDTO());
    }

    /**
     * Test case where Lion.getBean() throws exception
     */
    @Test
    public void testConfigChangedLionException() throws Throwable {
        // arrange
        ConfigEvent configEvent = new ConfigEvent("key", new ConfigValue("key", "value", "source"), null);
        RealTimeTaskConsumerConfigDTO originalConfig = realTimeTaskConsumerConfig.getConfigDTO();
        mockedEnvironment.when(Environment::getAppName).thenReturn("testApp");
        mockedLion.when(() -> Lion.getBean(anyString(), anyString(), any()))
                .thenThrow(new RuntimeException("Lion error"));
        // act
        Exception exception = assertThrows(RuntimeException.class,
                () -> realTimeTaskConsumerConfig.configChanged(configEvent));
        // assert
        assertEquals("Lion error", exception.getMessage());
        assertEquals(originalConfig, realTimeTaskConsumerConfig.getConfigDTO());
    }

    /**
     * Test case where JsonUtils.toStr() throws exception
     */
    @Test
    public void testConfigChangedJsonException() throws Throwable {
        // arrange
        ConfigEvent configEvent = new ConfigEvent("key", new ConfigValue("key", "value", "source"), null);
        RealTimeTaskConsumerConfigDTO newConfig = new RealTimeTaskConsumerConfigDTO();
        mockedEnvironment.when(Environment::getAppName).thenReturn("testApp");
        mockedLion.when(() -> Lion.getBean(anyString(), anyString(), any())).thenReturn(newConfig);
        // act
        realTimeTaskConsumerConfig.configChanged(configEvent);
        // assert
        assertEquals(newConfig, realTimeTaskConsumerConfig.getConfigDTO());
    }

    @Test
    public void testPercentSwitchWhenAppIdIsBlank() throws Throwable {
        // arrange
        Long userId = 123L;
        String blankAppId = " ";
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(userId, blankAppId);
        // assert
        assertFalse(result);
    }

    @Test
    public void testPercentSwitchWhenAppIdIsNull() throws Throwable {
        // arrange
        Long userId = 123L;
        String nullAppId = null;
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(userId, nullAppId);
        // assert
        assertFalse(result);
    }

    @Test
    public void testPercentSwitchWhenAppIdNotInConfigMap() throws Throwable {
        // arrange
        Long userId = 123L;
        String appId = "testApp";
        when(configDTO.getAiTaskAppId2SwitchPercentNumMapNew()).thenReturn(new HashMap<>());
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(userId, appId);
        // assert
        assertFalse(result);
    }

    @Test
    public void testPercentSwitchWhenPercentageListIsNull() throws Throwable {
        // arrange
        Long userId = 123L;
        String appId = "testApp";
        Map<String, List<Long>> percentageMap = new HashMap<>();
        percentageMap.put(appId, null);
        when(configDTO.getAiTaskAppId2SwitchPercentNumMapNew()).thenReturn(percentageMap);
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(userId, appId);
        // assert
        assertFalse(result);
    }

    @Test
    public void testPercentSwitchWhenPercentageListIsEmpty() throws Throwable {
        // arrange
        Long userId = 123L;
        String appId = "testApp";
        Map<String, List<Long>> percentageMap = new HashMap<>();
        percentageMap.put(appId, Collections.emptyList());
        when(configDTO.getAiTaskAppId2SwitchPercentNumMapNew()).thenReturn(percentageMap);
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(userId, appId);
        // assert
        assertFalse(result);
    }

    @Test
    public void testPercentSwitchWhenValidPercentageValues() throws Throwable {
        // arrange
        // Changed from 123L to 15L to be within range
        Long userId = 15L;
        String appId = "testApp";
        List<Long> percentages = Arrays.asList(10L, 20L);
        Map<String, List<Long>> percentageMap = new HashMap<>();
        percentageMap.put(appId, percentages);
        when(configDTO.getAiTaskAppId2SwitchPercentNumMapNew()).thenReturn(percentageMap);
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(userId, appId);
        // assert
        // userId % 100 = 15, which is between 10 and 20 -> should return true
        assertTrue(result);
    }

    @Test
    public void testPercentSwitchWhenReversedPercentageValues() throws Throwable {
        // arrange
        // Changed from 123L to 15L to be within range
        Long userId = 15L;
        String appId = "testApp";
        // reversed order
        List<Long> percentages = Arrays.asList(20L, 10L);
        Map<String, List<Long>> percentageMap = new HashMap<>();
        percentageMap.put(appId, percentages);
        when(configDTO.getAiTaskAppId2SwitchPercentNumMapNew()).thenReturn(percentageMap);
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(userId, appId);
        // assert
        // The method should swap min/max values internally
        // userId % 100 = 15, which is between 10 and 20 -> should return true
        assertTrue(result);
    }

    @Test
    public void testPercentSwitchWhenUserIdIsNull() throws Throwable {
        // arrange
        String appId = "testApp";
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(null, appId);
        // assert
        assertFalse(result);
    }

    @Test
    public void testPercentSwitchWhenUserIdIsAtRangeBoundary() throws Throwable {
        // arrange
        // Boundary case
        Long userId = 20L;
        String appId = "testApp";
        List<Long> percentages = Arrays.asList(10L, 20L);
        Map<String, List<Long>> percentageMap = new HashMap<>();
        percentageMap.put(appId, percentages);
        when(configDTO.getAiTaskAppId2SwitchPercentNumMapNew()).thenReturn(percentageMap);
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(userId, appId);
        // assert
        // userId % 100 = 20, which is exactly at max boundary -> should return true
        assertFalse(result);
    }

    @Test
    public void testPercentSwitchWhenUserIdIsBelowMinBoundary() throws Throwable {
        // arrange
        // Just below min boundary (10 < 10 is false)
        Long userId = 10L;
        String appId = "testApp";
        List<Long> percentages = Arrays.asList(10L, 20L);
        Map<String, List<Long>> percentageMap = new HashMap<>();
        percentageMap.put(appId, percentages);
        when(configDTO.getAiTaskAppId2SwitchPercentNumMapNew()).thenReturn(percentageMap);
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(userId, appId);
        // assert
        assertTrue(result);
    }
}
