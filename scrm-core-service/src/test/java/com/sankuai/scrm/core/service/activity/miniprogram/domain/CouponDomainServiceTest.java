package com.sankuai.scrm.core.service.activity.miniprogram.domain;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

import com.dianping.gmkt.event.api.api.EventRpcService;
import com.dianping.gmkt.event.api.v2.model.CouponInfo;
import com.dianping.gmkt.event.api.v2.model.CouponPrizeDetail;
import com.dianping.gmkt.event.api.v2.model.DrawResult;
import com.dianping.gmkt.event.api.v2.model.PigeonResponse;
import com.dianping.lion.client.Lion;
import com.dianping.unified.coupon.issue.api.dto.UnifiedCouponIssueResult;
import com.dianping.unified.coupon.issue.api.response.UnifiedCouponIssueBaseResponse;
import com.dianping.unified.coupon.issue.api.response.UnifiedCouponIssueResponse;
import com.dianping.unified.coupon.manage.api.UnifiedCouponGroupQueryService;
import com.dianping.unified.coupon.manage.api.response.CouponGroupStockQueryResponse;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import com.google.common.collect.Lists;
import com.sankuai.mpmkt.coupon.issue.api.IssueCouponService;

import java.util.List;

import com.sankuai.mpmkt.coupon.issue.api.result.CouponIssueValidateResult;
import com.sankuai.scrm.core.service.coupon.dal.entity.IssueCouponRecord;
import com.sankuai.scrm.core.service.coupon.dal.mapper.IssueCouponRecordMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/9/2 18:03
 * @Version v1.0.0
 */
@ExtendWith(MockitoExtension.class)
public class CouponDomainServiceTest {

    @InjectMocks
    private CouponDomainService couponDomainService;

    @Mock(lenient = true)
    private EventRpcService eventRpcService;

    @Mock
    private IssueCouponService issueCouponService;

    @Mock
    private UnifiedCouponGroupQueryService unifiedCouponGroupQueryService;

    @Mock
    private IssueCouponRecordMapper issueCouponRecordMapper;

    @Test
    public void testDrawPlatFormCoupon() {
        DrawResult drawResult = new DrawResult();
        PigeonResponse<DrawResult> response = new PigeonResponse<>(drawResult);
        CouponPrizeDetail couponPrizeDetail = new CouponPrizeDetail();
        drawResult.setPrizeData(couponPrizeDetail);
        ;
        when(eventRpcService.attendEvent(any())).thenReturn(response);
        List<CouponInfo> couponInfos = couponDomainService.drawPlatFormCoupon("abc", 1L, false);
        assertNotNull(couponInfos);
    }

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试sendMtCouponAndGetUnifiedCouponId成功返回unifiedCouponId的情况
     */
//    @Test
//    public void testSendMtCouponAndResponseSuccess() {
//        // arrange
//        Long userId = 1L;
//        String couponId = "couponId";
//        CouponIssueRequest couponIssueRequest = new CouponIssueRequest();
//        couponIssueRequest.setUserId(userId);
//        couponIssueRequest.setUserType("MT");
//        couponIssueRequest.setOperator("testApp");
//        CouponIssueInfo issueInfo = new CouponIssueInfo();
//        issueInfo.setCouponGroupId(couponId);
//        couponIssueRequest.setCouponGroupInfo(Collections.singletonList(issueInfo));
//        UnifiedCouponIssueResponse response = mock(UnifiedCouponIssueResponse.class);
//        UnifiedCouponIssueResult result = mock(UnifiedCouponIssueResult.class);
//        when(issueCouponService.issueCouponsToUser(any(CouponIssueRequest.class), any(UnifiedCouponIssueOption.class))).thenReturn(response);
//        when(response.isSuccess()).thenReturn(true);
//        when(response.getResult()).thenReturn(result);
//        //when(result.getResult()).thenReturn(Collections.singletonList(new UnifiedCouponIssueResult.UnifiedCouponIssueDetail("unifiedCouponId", userId, null, null, null)));
//        // act
//        String unifiedCouponId = couponDomainService.sendMtCouponAndResponse(userId, couponId);
//        // assert
//        assertEquals("unknown", unifiedCouponId);
//    }

    /**
     * 测试sendMtCouponAndGetUnifiedCouponId在response为null的情况
     */
//    @Test
//    public void testSendMtCouponAndResponseResponseNull() {
//        // arrange
//        Long userId = 1L;
//        String couponId = "couponId";
//        when(issueCouponService.issueCouponsToUser(any(CouponIssueRequest.class), any(UnifiedCouponIssueOption.class))).thenReturn(null);
//        // act
//        String unifiedCouponId = couponDomainService.sendMtCouponAndResponse(userId, couponId);
//        // assert
//        assertEquals("", unifiedCouponId);
//    }

    /**
     * 测试sendMtCouponAndGetUnifiedCouponId在response不成功的情况
     */
//    @Test
//    public void testSendMtCouponAndResponseResponseNotSuccess() {
//        // arrange
//        Long userId = 1L;
//        String couponId = "couponId";
//        UnifiedCouponIssueResponse response = mock(UnifiedCouponIssueResponse.class);
//        when(issueCouponService.issueCouponsToUser(any(CouponIssueRequest.class), any(UnifiedCouponIssueOption.class))).thenReturn(response);
//        when(response.isSuccess()).thenReturn(false);
//        // act
//        String unifiedCouponId = couponDomainService.sendMtCouponAndResponse(userId, couponId);
//        // assert
//        assertEquals("", unifiedCouponId);
//    }

    /**
     * 测试sendMtCouponAndGetUnifiedCouponId在result为空的情况
     */
//    @Test
//    public void testSendMtCouponAndResponseResultNull() {
//        // arrange
//        Long userId = 1L;
//        String couponId = "couponId";
//        UnifiedCouponIssueResponse response = mock(UnifiedCouponIssueResponse.class);
//        when(issueCouponService.issueCouponsToUser(any(CouponIssueRequest.class), any(UnifiedCouponIssueOption.class))).thenReturn(response);
//        when(response.isSuccess()).thenReturn(true);
//        when(response.getResult()).thenReturn(null);
//        // act
//        String unifiedCouponId = couponDomainService.sendMtCouponAndResponse(userId, couponId);
//        // assert
//        assertEquals("unknown", unifiedCouponId);
//    }

    /**
     * 测试sendMtCouponAndGetUnifiedCouponId在result.getResult()为空的情况
     */
//    @Test
//    public void testSendMtCouponAndGetUnifiedCouponIdResultGetResultNull() {
//        // arrange
//        Long userId = 1L;
//        String couponId = "couponId";
//        UnifiedCouponIssueResponse response = mock(UnifiedCouponIssueResponse.class);
//        UnifiedCouponIssueResult result = mock(UnifiedCouponIssueResult.class);
//        when(issueCouponService.issueCouponsToUser(any(CouponIssueRequest.class), any(UnifiedCouponIssueOption.class))).thenReturn(response);
//        when(response.isSuccess()).thenReturn(true);
//        when(response.getResult()).thenReturn(result);
//        when(result.getResult()).thenReturn(null);
//        // act
//        String unifiedCouponId = couponDomainService.sendMtCouponAndResponse(userId, couponId);
//        // assert
//        assertEquals("unknown", unifiedCouponId);
//    }
    @Test
    public void testBatchQueryStockInfo() {
        UnifiedCouponManageResponse<CouponGroupStockQueryResponse> unifiedCouponManageResponse = new UnifiedCouponManageResponse<>();
        unifiedCouponManageResponse.setResult(new CouponGroupStockQueryResponse());
        when(unifiedCouponGroupQueryService.queryStockInfo(any())).thenReturn(unifiedCouponManageResponse);
        CouponGroupStockQueryResponse response = couponDomainService.batchQueryStockInfo(Lists.newArrayList("1"));
        assertNotNull(response);
    }

    @Test
    public void testIssueCoupon() {
        UnifiedCouponIssueResponse unifiedCouponIssueResponse = new UnifiedCouponIssueResponse();
        unifiedCouponIssueResponse.setResult(new UnifiedCouponIssueResult());
        MockedStatic<Lion> lionMockedStatic = mockStatic(Lion.class);
        lionMockedStatic.when(() -> Lion.getBoolean(any(), any(), any())).thenReturn(true);
        when(issueCouponService.issueCouponsToUser(any(), any())).thenReturn(unifiedCouponIssueResponse);
        UnifiedCouponIssueResult couponIssueResult = couponDomainService.issueCoupon(1L, Lists.newArrayList("1"), "1", true);
        assertNotNull(couponIssueResult);
        lionMockedStatic.close();
    }

    @Test
    public void testPreValidMtCouponAndResponse(){
        UnifiedCouponIssueBaseResponse<CouponIssueValidateResult> unifiedCouponIssueBaseResponse = new UnifiedCouponIssueBaseResponse<>();
        unifiedCouponIssueBaseResponse.setResult(new CouponIssueValidateResult());
        MockedStatic<Lion> lionMockedStatic = mockStatic(Lion.class);
        lionMockedStatic.when(() -> Lion.getBoolean(any(), any(), any())).thenReturn(true);
        when(issueCouponService.preValidateIssueCouponsToUser(any())).thenReturn(unifiedCouponIssueBaseResponse);
        CouponIssueValidateResult couponIssueValidateResult = couponDomainService.preValidMtCouponAndResponse(1L, Lists.newArrayList("1"), "1", true);
        assertNotNull(couponIssueValidateResult);
        lionMockedStatic.close();
    }

    @Test
    public void testBatchInsertIssueCouponRecord(){
        when(issueCouponRecordMapper.batchInsert(anyList())).thenReturn(1);
        boolean result = couponDomainService.batchInsertIssueCouponRecord(Lists.newArrayList(new IssueCouponRecord()));
        assertTrue(result);
    }

}
