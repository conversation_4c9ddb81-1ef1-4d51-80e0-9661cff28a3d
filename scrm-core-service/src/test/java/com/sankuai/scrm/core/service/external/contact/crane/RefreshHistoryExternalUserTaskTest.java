package com.sankuai.scrm.core.service.external.contact.crane;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxContactAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxStaffAcl;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class RefreshHistoryExternalUserTaskTest {

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private CorpWxStaffAcl corpWxStaffAcl;

    @Mock
    private ContactUserDomain contactUserDomain;

    @Mock
    private CorpWxContactAcl corpWxContactAcl;

    @InjectMocks
    private RefreshHistoryExternalUserTask task;

    @BeforeEach
    void setUp() {
        // Reset static mocks if any
    }

    /**
     * Test when Lion returns blank appId
     */
    @Test
    public void testRefreshExternalUserWhenAppIdIsBlank() throws Throwable {
        // arrange
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn("testApp");
            lionMock.when(() -> Lion.getString("testApp", "scrm.core.refresh.appId")).thenReturn("");
            // act
            task.refreshExternalUser();
            // assert
            verify(appConfigRepository, never()).getCorpIdByAppId(any());
            verify(corpWxStaffAcl, never()).getFollowUserList(any());
        }
    }

    /**
     * Test when appConfigRepository returns blank corpId
     */
    @Test
    public void testRefreshExternalUserWhenCorpIdIsBlank() throws Throwable {
        // arrange
        String appId = "testAppId";
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn("testApp");
            lionMock.when(() -> Lion.getString("testApp", "scrm.core.refresh.appId")).thenReturn(appId);
            when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(null);
            // act
            task.refreshExternalUser();
            // assert
            verify(corpWxStaffAcl, never()).getFollowUserList(any());
        }
    }

    /**
     * Test when corpWxStaffAcl fails to return staffIdList after retries
     */
    @Test
    public void testRefreshExternalUserWhenStaffIdListIsEmptyAfterRetries() throws Throwable {
        // arrange
        String appId = "testAppId";
        String corpId = "testCorpId";
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn("testApp");
            lionMock.when(() -> Lion.getString("testApp", "scrm.core.refresh.appId")).thenReturn(appId);
            when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
            when(corpWxStaffAcl.getFollowUserList(corpId)).thenReturn(new ArrayList<>());
            // act
            task.refreshExternalUser();
            // assert
            verify(corpWxStaffAcl, times(3)).getFollowUserList(corpId);
            verify(corpWxContactAcl, never()).batchGetUserDetail(any(), any(), any());
        }
    }

    /**
     * Test when exception occurs during execution
     */
    @Test
    public void testRefreshExternalUserWhenExceptionOccurs() {
        // 手动创建 mock 对象进行调试
        CorpAppConfigRepository mockAppConfigRepository = mock(CorpAppConfigRepository.class);
        CorpWxStaffAcl mockCorpWxStaffAcl = mock(CorpWxStaffAcl.class);
        ContactUserDomain mockContactUserDomain = mock(ContactUserDomain.class);
        CorpWxContactAcl mockCorpWxContactAcl = mock(CorpWxContactAcl.class);

        // 手动注入依赖
        RefreshHistoryExternalUserTask task = new RefreshHistoryExternalUserTask();
        // 使用反射或setter方法注入依赖
        ReflectionTestUtils.setField(task, "appConfigRepository", mockAppConfigRepository);
        ReflectionTestUtils.setField(task, "corpWxStaffAcl", mockCorpWxStaffAcl);
        ReflectionTestUtils.setField(task, "contactUserDomain", mockContactUserDomain);
        ReflectionTestUtils.setField(task, "corpWxContactAcl", mockCorpWxContactAcl);

        String appId = "testAppId";
        String corpId = "testCorpId";

        try (MockedStatic<Environment> envMock = mockStatic(Environment.class);
             MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {

            envMock.when(Environment::getAppName).thenReturn("scrm-core-service");
            lionMock.when(() -> Lion.getString("scrm-core-service", "scrm.core.refresh.appId"))
                    .thenReturn(appId);

            // 现在这些应该可以正常工作
            when(mockAppConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
            when(mockCorpWxStaffAcl.getFollowUserList(corpId))
                    .thenThrow(new RuntimeException("Test exception"));

            assertDoesNotThrow(() -> {
                task.refreshExternalUser();
            });

            verify(mockAppConfigRepository).getCorpIdByAppId(appId);
            verify(mockCorpWxStaffAcl).getFollowUserList(corpId);
        }
    }
}
