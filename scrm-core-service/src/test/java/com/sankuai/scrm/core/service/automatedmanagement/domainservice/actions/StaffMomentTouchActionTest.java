package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationAttachmentTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationContentTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.UploadWxAttachmentAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxMomentSendAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxMomentSendRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.SenderListInfoVO;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.VisibleRangeVO;
import com.sankuai.scrm.core.service.infrastructure.vo.WechatMediaResult;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class StaffMomentTouchActionTest {

    @InjectMocks
    private StaffMomentTouchAction staffMomentTouchAction;

    @Mock(lenient = true)
    private ExecuteManagementService executeManagementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock(lenient = true)
    private WxMomentSendAcl wxMomentSendAcl;

    @Mock(lenient = true)
    private UploadWxAttachmentAcl uploadWxAttachmentAcl;

    @Mock(lenient = true)
    private InformationGatheringService informationGatheringService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ExecuteManagementDTO mediumManagementDTO;

    private ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO;

    private ScrmProcessOrchestrationNodeDTO currentProcessingNode;

    private ScrmAmProcessOrchestrationExecuteLogDO existedExecuteLogDO;

    private static final String processOrchestrationTestStr = "{ \"id\" : 263, \"name\" : \"企微朋友圈转发测试\", \"processOrchestrationType\" : 4, \"cron\" : \"\", \"beginTime\" : 1730169240000, \"endTime\" : 1730342040000, \"status\" : 1, \"validVersion\" : \"1731484313242\", \"updateTime\" : 1731484313000, \"creatorId\" : \"wangyonghao02\", \"lastUpdaterId\" : \"wangyonghao02\", \"participationRestrict\" : true, \"participationRestrictionsCycle\" : 0, \"participationRestrictionsTimes\" : 0, \"appId\" : \"yimei\", \"previewPic\" : \"\", \"cronComment\" : \"2024-10-30 10:34:00\", \"executorType\" : 1, \"executorList\" : [ { \"executorId\" : \"WangXueFei\", \"executorName\" : \"王雪飞\", \"executorType\" : 2 } ], \"crowdPackType\" : 3, \"crowdPackIdList\" : [ ], \"groupIdList\" : [ \"wrb_61EQAAnSZJ1NP3R3yqsyJtbL_4Kw\", \"wrb_61EQAA-ilg22ELq_-cIv-FURWR6A\" ], \"groupInfoList\" : [ { \"groupName\" : \"群发消息测试1025-2\", \"owner\" : \"WangXueFei\", \"robotList\" : [ \"困猪/:pig\" ], \"groupId\" : \"wrb_61EQAAnSZJ1NP3R3yqsyJtbL_4Kw\", \"createDate\" : 1729837100000, \"memberCount\" : 4 }, { \"groupName\" : \"群发消息测试1025-1\", \"owner\" : \"WangXueFei\", \"robotList\" : [ \"困猪/:pig\" ], \"groupId\" : \"wrb_61EQAA-ilg22ELq_-cIv-FURWR6A\", \"createDate\" : 1729837050000, \"memberCount\" : 4 } ], \"crowdPackUpdateStrategyInfoDTO\" : null, \"goalDTO\" : { \"id\" : 800, \"checkTime\" : \"7\", \"checkTimeUnit\" : \"3\", \"status\" : 1, \"processOrchestrationId\" : 263, \"processOrchestrationVersion\" : \"1731484313242\", \"goalType\" : 1, \"careNegativeResult\" : true, \"positiveResultHighlightList\" : [ 90002, 90003 ], \"negativeResultHighlightList\" : null, \"goalConditionList\" : [ ] }, \"negativeGoalDTO\" : { \"id\" : 799, \"checkTime\" : \"7\", \"checkTimeUnit\" : \"3\", \"status\" : 1, \"processOrchestrationId\" : 263, \"processOrchestrationVersion\" : \"1731484313242\", \"goalType\" : 2, \"careNegativeResult\" : true, \"positiveResultHighlightList\" : [ 90002, 90003 ], \"negativeResultHighlightList\" : null, \"goalConditionList\" : [ ] }, \"nodeMediumDTO\" : { \"processOrchestrationNodeDTOList\" : [ { \"nodeId\" : 0, \"preNodeId\" : -1, \"nodeType\" : 4, \"id\" : 1190, \"processOrchestrationId\" : 263, \"processOrchestrationVersion\" : \"1731484313242\", \"childrenNodes\" : [ 1730255027731 ] }, { \"nodeId\" : 1730255027731, \"preNodeId\" : 0, \"nodeType\" : 2, \"id\" : 1191, \"processOrchestrationId\" : 263, \"processOrchestrationVersion\" : \"1731484313242\", \"childrenNodes\" : [ ] } ], \"conditionMap\" : { }, \"actionMap\" : { \"1730255027731\" : { \"id\" : 510, \"actionId\" : 1, \"processOrchestrationId\" : 263, \"processOrchestrationVersion\" : \"1731484313242\", \"processOrchestrationNodeId\" : 1730255027731, \"actionType\" : 3, \"actionSubType\" : 6, \"updateTime\" : 1731484313000, \"contentType\" : 1, \"contentList\" : null } }, \"actionContentMap\" : { \"1730255027731-1\" : [ { \"id\" : 509, \"actionId\" : 1, \"contentId\" : 1, \"processOrchestrationId\" : 263, \"processOrchestrationVersion\" : \"1731484313242\", \"processOrchestrationNodeId\" : 1730255027731, \"content\" : \"\", \"updateTime\" : 1731484313000, \"contentType\" : 1, \"attachmentDTOList\" : null } ] }, \"actionAttachmentMap\" : { \"1730255027731-1-1\" : [ { \"id\" : 488, \"contentId\" : 1, \"actionId\" : 1, \"attachmentTypeId\" : 7, \"attachmentContent\" : \"{\\\"supplyType\\\":2,\\\"productType\\\":1,\\\"shelfName\\\":\\\"\\\",\\\"jumpPageType\\\":1,\\\"jumpUrl\\\":\\\"\\\",\\\"productId\\\":\\\"757123783\\\",\\\"supplyScope\\\":1,\\\"marketingCopy\\\":\\\"111\\\",\\\"marketingCopySource\\\":2,\\\"hotTagList\\\":\\\"\\\",\\\"topproductids\\\":\\\"\\\",\\\"headpicUrl\\\":\\\"https://msstest.sankuai.com/scrm-s3/poster-17314843069686918099920396694881.png\\\",\\\"posterSetting\\\":{\\\"title\\\":\\\"社群专项特价111\\\",\\\"image\\\":\\\"\\\",\\\"benefitInfo\\\":\\\"\\\"},\\\"qrCodeUrl\\\":\\\"https://img.meituan.net/beautyimg/af9454758f6b7eb5d80486bfa8389e8540032.jpg\\\"}\", \"processOrchestrationId\" : 263, \"processOrchestrationVersion\" : \"1731484313242\", \"processOrchestrationNodeId\" : 1730255027731, \"updateTime\" : null, \"attachmentSupplyDetailDTO\" : { \"supplyType\" : 2, \"productId\" : \"757123783\", \"productType\" : 1, \"marketingCopy\" : \"111\", \"marketingCopySource\" : 2, \"supplyScope\" : 1, \"hotTagList\" : \"\", \"shelfName\" : \"\", \"jumpPageType\" : 1, \"jumpUrl\" : \"\", \"topproductids\" : \"\", \"headpicUrl\" : \"https://msstest.sankuai.com/scrm-s3/poster-17314843069686918099920396694881.png\", \"qrCodeUrl\" : \"https://img.meituan.net/beautyimg/af9454758f6b7eb5d80486bfa8389e8540032.jpg\" }, \"attachmentContentDetailDTO\" : null } ] } }, \"executePlanDTO\" : null }";

    @Before
    public void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setNodeId(1L);
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(Collections.singletonList(nodeDTO));
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        mediumManagementDTO = new ExecuteManagementDTO(processOrchestrationDTO, 1, "appId", new ArrayList<>(), 1);
        scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        scrmCrowdPackDetailInfoDTO.setExternalUserWxUnionId("unionId");
        currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setNodeId(1L);
        existedExecuteLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        when(executeManagementService.getMtUserIdByUnionId(any())).thenReturn(1L);
    }

    @Test
    public void testDealStaffMomentAction_MediumManagementDTO_SizeEquals() throws Throwable {
        processOrchestrationDTO.setCrowdPackType(0);
        StepExecuteResultDTO result = staffMomentTouchAction.dealStaffMomentAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        assertFalse(result.isSuccess());
    }

    @Test
    public void testDealStaffMomentAction_MediumManagementDTO_SizeNotEquals() throws Throwable {
        mediumManagementDTO.getExistedWxInvokeLogDOMap().put("testStaffId", new ArrayList<>());
        processOrchestrationDTO.setCrowdPackType(0);
        StepExecuteResultDTO result = staffMomentTouchAction.dealStaffMomentAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        assertFalse(result.isSuccess());
    }

    @Test
    public void testDealStaffMomentAction_GetScrmAmProcessOrchestrationExecuteLogDOIsNull() throws Throwable {
        processOrchestrationDTO.setCrowdPackType(0);
        StepExecuteResultDTO result = staffMomentTouchAction.dealStaffMomentAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        assertFalse(result.isSuccess());
    }

    @Test
    public void testDealStaffMomentAction_GoalDTO_StatusEqualsOne() throws Throwable {
        ScrmProcessOrchestrationGoalDTO goalDTO = new ScrmProcessOrchestrationGoalDTO();
        goalDTO.setStatus((byte) 1);
        processOrchestrationDTO.setGoalDTO(goalDTO);
        processOrchestrationDTO.setCrowdPackType(0);
        StepExecuteResultDTO result = staffMomentTouchAction.dealStaffMomentAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        assertFalse(result.isSuccess());
    }

    @Test
    public void testDealStaffMomentAction_GoalDTO_StatusNotEqualsOne() throws Throwable {
        ScrmProcessOrchestrationGoalDTO goalDTO = new ScrmProcessOrchestrationGoalDTO();
        goalDTO.setStatus((byte) 2);
        processOrchestrationDTO.setCrowdPackType(0);
        processOrchestrationDTO.setGoalDTO(goalDTO);
        StepExecuteResultDTO result = staffMomentTouchAction.dealStaffMomentAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        assertFalse(result.isSuccess());
    }

    @Test
    public void testDealStaffMomentAction_MediumManagementDTO_NotContainsExecutorId() throws Throwable {
        processOrchestrationDTO.setCrowdPackType(0);
        StepExecuteResultDTO result = staffMomentTouchAction.dealStaffMomentAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        assertFalse(result.isSuccess());
    }

    /**
     * 测试 getWxMomentSendRequest 方法，当 actionDTO 的 contentType 为 SUPPLY 且 contentDTOS 不为空时
     */
    @Test
    public void testGetWxMomentSendRequestWithSupplyContentTypeAndNonEmptyContentDTOS() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationTestStr, ScrmProcessOrchestrationDTO.class);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = mock(ScrmAmProcessOrchestrationExecuteLogDO.class);
        ScrmProcessOrchestrationActionDTO actionDTO = processOrchestrationDTO.getNodeMediumDTO().getActionDTO(1730255027731L);
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(processOrchestrationDTO, 1, processOrchestrationDTO.getAppId(), processOrchestrationDTO.getExecutorList().stream().map(o->o.getExecutorId()).collect(Collectors.toList()), 2);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = processOrchestrationDTO.getNodeMediumDTO().getActionContentDTOList(actionDTO);
        // act
        when(uploadWxAttachmentAcl.uploadWxAttachment(any(), any(), any(), anyBoolean())).thenReturn(new WechatMediaResult());
        List<String> tagList = new ArrayList<>();
        tagList.add("tag1");
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        WxMomentSendRequest result = staffMomentTouchAction.getWxMomentSendRequestWithTagList(processOrchestrationDTO, executeLogDO, contentDTOS, actionDTO, tagList, executeManagementDTO, stepExecuteResultDTO);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getWxMomentSendRequest 方法，当 actionDTO 的 contentType 不为 SUPPLY 时
     */
    @Test
    public void testGetWxMomentSendRequestWithNonSupplyContentType() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationTestStr, ScrmProcessOrchestrationDTO.class);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = mock(ScrmAmProcessOrchestrationExecuteLogDO.class);
        ScrmProcessOrchestrationActionDTO actionDTO = processOrchestrationDTO.getNodeMediumDTO().getActionDTO(1730255027731L);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = processOrchestrationDTO.getNodeMediumDTO().getActionContentDTOList(actionDTO);
        actionDTO.setContentType(ScrmProcessOrchestrationContentTypeEnum.NON_SUPPLY.getValue());
        // act
        List<String> tagList = new ArrayList<>();
        tagList.add("tag1");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(processOrchestrationDTO, 1, processOrchestrationDTO.getAppId(), processOrchestrationDTO.getExecutorList().stream().map(o->o.getExecutorId()).collect(Collectors.toList()), 2);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        WxMomentSendRequest result = staffMomentTouchAction.getWxMomentSendRequestWithTagList(processOrchestrationDTO, executeLogDO, contentDTOS, actionDTO, tagList, executeManagementDTO, stepExecuteResultDTO);
        // assert
        assertNull(result);
    }

    /**
     * 测试 getWxMomentSendRequest 方法，当 contentDTOS 为空时
     */
    @Test
    public void testGetWxMomentSendRequestWithEmptyContentDTOS() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationTestStr, ScrmProcessOrchestrationDTO.class);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = mock(ScrmAmProcessOrchestrationExecuteLogDO.class);
        ScrmProcessOrchestrationActionDTO actionDTO = processOrchestrationDTO.getNodeMediumDTO().getActionDTO(1730255027731L);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = processOrchestrationDTO.getNodeMediumDTO().getActionContentDTOList(actionDTO);
        // act
        when(uploadWxAttachmentAcl.uploadWxAttachment(any(), any(), any(), anyBoolean())).thenReturn(new WechatMediaResult());
        List<String> tagList = new ArrayList<>();
        tagList.add("tag1");
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(processOrchestrationDTO, 1, processOrchestrationDTO.getAppId(), processOrchestrationDTO.getExecutorList().stream().map(o->o.getExecutorId()).collect(Collectors.toList()), 2);
        WxMomentSendRequest result = staffMomentTouchAction.getWxMomentSendRequestWithTagList(processOrchestrationDTO, executeLogDO, contentDTOS, actionDTO, tagList, executeManagementDTO,stepExecuteResultDTO);
        // assert
        assertNull(result);
    }

    /**
     * 测试内容为空且附件为空的情况
     */
    @Test
    public void testGetNormalWxMomentSendRequest_ContentAndAttachmentsEmpty() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationTestStr, ScrmProcessOrchestrationDTO.class);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = mock(ScrmAmProcessOrchestrationExecuteLogDO.class);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = processOrchestrationDTO.getNodeMediumDTO().getActionContentDTOList(processOrchestrationDTO.getNodeMediumDTO().getActionDTO(1730255027731L));
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(processOrchestrationDTO, 1, processOrchestrationDTO.getAppId(), processOrchestrationDTO.getExecutorList().stream().map(o->o.getExecutorId()).collect(Collectors.toList()), 2);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        WxMomentSendRequest result = staffMomentTouchAction.getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO,stepExecuteResultDTO);
        assertNull(result);
    }

    /**
     * 测试内容不为空但附件为空的情况
     */
    @Test
    public void testGetNormalWxMomentSendRequest_ContentNotEmptyAttachmentsEmpty() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationTestStr, ScrmProcessOrchestrationDTO.class);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = mock(ScrmAmProcessOrchestrationExecuteLogDO.class);
        ScrmProcessOrchestrationActionDTO actionDTO = processOrchestrationDTO.getNodeMediumDTO().getActionDTO(1730255027731L);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = processOrchestrationDTO.getNodeMediumDTO().getActionContentDTOList(actionDTO);
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("Test Content");
        contentDTOS.add(contentDTO);
        List<ScrmProcessOrchestrationActionAttachmentDTO> actionAttachmentDTOS = processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentDTOList(contentDTOS.get(0));
        ScrmProcessOrchestrationActionAttachmentDTO pic = new ScrmProcessOrchestrationActionAttachmentDTO();
        pic.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.PICTURE.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTOPic = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetailDTOPic.setPicUrl("picUrl");
        pic.setAttachmentContent(JsonUtils.toStr(contentDetailDTOPic));
        actionAttachmentDTOS.add(pic);
        ScrmProcessOrchestrationActionAttachmentDTO video = new ScrmProcessOrchestrationActionAttachmentDTO();
        video.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.VIDEO.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTOVideo = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetailDTOVideo.setPicUrl("picUrl");
        video.setAttachmentContent(JsonUtils.toStr(contentDetailDTOVideo));
        ScrmProcessOrchestrationActionAttachmentDTO link = new ScrmProcessOrchestrationActionAttachmentDTO();
        link.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.LINK.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTOLink = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetailDTOLink.setPicUrl("picUrl");
        link.setAttachmentContent(JsonUtils.toStr(contentDetailDTOLink));
        actionAttachmentDTOS.add(new ScrmProcessOrchestrationActionAttachmentDTO());
        WechatMediaResult mediaResult = new WechatMediaResult("media_id", 123456789L, "image");
        mediaResult.setErrcode(0);
        when(uploadWxAttachmentAcl.uploadWxAttachment(any(), any(), any(), anyBoolean())).thenReturn(mediaResult);
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(processOrchestrationDTO, 1, processOrchestrationDTO.getAppId(), processOrchestrationDTO.getExecutorList().stream().map(o->o.getExecutorId()).collect(Collectors.toList()), 2);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        WxMomentSendRequest result = staffMomentTouchAction.getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO,stepExecuteResultDTO);
        assertNotNull(result);
    }

    /**
     * 测试内容为空但附件不为空的情况
     */
    @Test
    public void testGetNormalWxMomentSendRequest_ContentEmptyAttachmentsNotEmpty() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationTestStr, ScrmProcessOrchestrationDTO.class);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = mock(ScrmAmProcessOrchestrationExecuteLogDO.class);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = processOrchestrationDTO.getNodeMediumDTO().getActionContentDTOList(processOrchestrationDTO.getNodeMediumDTO().getActionDTO(1730255027731L));
        ScrmProcessOrchestrationActionContentDTO contentDTO = contentDTOS.get(0);
        List<ScrmProcessOrchestrationActionAttachmentDTO> attachmentDTOList = processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentDTOList(contentDTOS.get(0));
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachmentDTO.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.PICTURE.getValue());
        attachmentDTO.setAttachmentContent("{\"picUrl\":\"http://example.com/image.jpg\"}");
        attachmentDTOList.add(attachmentDTO);
        contentDTO.setAttachmentDTOList(attachmentDTOList);
        contentDTOS.add(contentDTO);
        WechatMediaResult mediaResult = new WechatMediaResult("media_id", 123456789L, "image");
        mediaResult.setErrcode(0);
        when(uploadWxAttachmentAcl.uploadWxAttachment(any(), any(), any(), anyBoolean())).thenReturn(mediaResult);
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(processOrchestrationDTO, 1, processOrchestrationDTO.getAppId(), processOrchestrationDTO.getExecutorList().stream().map(o->o.getExecutorId()).collect(Collectors.toList()), 2);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        WxMomentSendRequest result = staffMomentTouchAction.getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO,stepExecuteResultDTO);
        assertNotNull(result);
    }

    /**
     * 测试附件上传失败的情况
     */
    @Test
    public void testGetNormalWxMomentSendRequest_AttachmentUploadFail() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationTestStr, ScrmProcessOrchestrationDTO.class);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = mock(ScrmAmProcessOrchestrationExecuteLogDO.class);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = processOrchestrationDTO.getNodeMediumDTO().getActionContentDTOList(processOrchestrationDTO.getNodeMediumDTO().getActionDTO(1730255027731L));
        ScrmProcessOrchestrationActionContentDTO contentDTO = contentDTOS.get(0);
        List<ScrmProcessOrchestrationActionAttachmentDTO> attachmentDTOList = processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentDTOList(contentDTOS.get(0));
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachmentDTO.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.PICTURE.getValue());
        attachmentDTO.setAttachmentContent("{\"picUrl\":\"http://example.com/image.jpg\"}");
        attachmentDTOList.add(attachmentDTO);
        contentDTO.setAttachmentDTOList(attachmentDTOList);
        contentDTOS.add(contentDTO);
        when(uploadWxAttachmentAcl.uploadWxAttachment(any(), any(), any(), anyBoolean())).thenReturn(WechatMediaResult.fail(-1, "fail"));
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(processOrchestrationDTO, 1, processOrchestrationDTO.getAppId(), processOrchestrationDTO.getExecutorList().stream().map(o->o.getExecutorId()).collect(Collectors.toList()), 2);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        WxMomentSendRequest result = staffMomentTouchAction.getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO,stepExecuteResultDTO);
        assertNull(result);
    }

    /**
     * 测试场景：当附件列表为空时，应返回null
     */
    @Test
    public void testGetSupplyWxMomentSendRequest_WhenAttachmentListIsEmpty() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationTestStr, ScrmProcessOrchestrationDTO.class);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = mock(ScrmAmProcessOrchestrationExecuteLogDO.class);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = processOrchestrationDTO.getNodeMediumDTO().getActionContentDTOList(processOrchestrationDTO.getNodeMediumDTO().getActionDTO(1730255027731L));
        processOrchestrationDTO.getNodeMediumDTO().setActionAttachmentMap(new HashMap<>());
        when(uploadWxAttachmentAcl.uploadWxAttachment(any(), any(), any(), anyBoolean())).thenReturn(new WechatMediaResult());
        // when(processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentDTOList(any())).thenReturn(new ArrayList<>());
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(processOrchestrationDTO, 1, processOrchestrationDTO.getAppId(), processOrchestrationDTO.getExecutorList().stream().map(o->o.getExecutorId()).collect(Collectors.toList()), 2);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        WxMomentSendRequest result = staffMomentTouchAction.getSupplyWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, stepExecuteResultDTO);
        assertNull(result);
    }

    /**
     * 测试场景：当附件列表不为空，但上传微信附件失败时，应返回null
     */
    @Test
    public void testGetSupplyWxMomentSendRequest_WhenUploadWxAttachmentFails() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationTestStr, ScrmProcessOrchestrationDTO.class);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = mock(ScrmAmProcessOrchestrationExecuteLogDO.class);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = processOrchestrationDTO.getNodeMediumDTO().getActionContentDTOList(processOrchestrationDTO.getNodeMediumDTO().getActionDTO(1730255027731L));
        List<ScrmProcessOrchestrationActionAttachmentDTO> attachmentDTOList = processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentDTOList(contentDTOS.get(0));
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachmentDTO.setAttachmentSupplyDetailDTO(new ScrmProcessOrchestrationAttachmentSupplyDetailDTO());
        attachmentDTOList.add(attachmentDTO);
        // when(processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentDTOList(any())).thenReturn(attachmentDTOList);
        when(uploadWxAttachmentAcl.uploadWxAttachment(any(), any(), any(), anyBoolean())).thenReturn(new WechatMediaResult());
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(processOrchestrationDTO, 1, processOrchestrationDTO.getAppId(), processOrchestrationDTO.getExecutorList().stream().map(o->o.getExecutorId()).collect(Collectors.toList()), 2);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        WxMomentSendRequest result = staffMomentTouchAction.getSupplyWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, stepExecuteResultDTO);
        assertNull(result);
    }

    /**
     * 测试场景：当附件列表不为空，且上传微信附件成功时，应返回非null的WxMomentSendRequest
     */
    @Test
    public void testGetSupplyWxMomentSendRequest_WhenUploadWxAttachmentSucceeds() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationTestStr, ScrmProcessOrchestrationDTO.class);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = mock(ScrmAmProcessOrchestrationExecuteLogDO.class);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = processOrchestrationDTO.getNodeMediumDTO().getActionContentDTOList(processOrchestrationDTO.getNodeMediumDTO().getActionDTO(1730255027731L));
        List<ScrmProcessOrchestrationActionAttachmentDTO> attachmentDTOList = processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentDTOList(contentDTOS.get(0));
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setHeadpicUrl("http://example.com/image.jpg");
        attachmentDTO.setAttachmentSupplyDetailDTO(supplyDetailDTO);
        attachmentDTOList.add(attachmentDTO);
        List<ScrmProcessOrchestrationActionAttachmentDTO> actionAttachmentDTOS = processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentDTOList(contentDTOS.get(0));
        ScrmProcessOrchestrationActionAttachmentDTO pic = new ScrmProcessOrchestrationActionAttachmentDTO();
        pic.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.PICTURE.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTOPic = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetailDTOPic.setPicUrl("picUrl");
        pic.setAttachmentContent(JsonUtils.toStr(contentDetailDTOPic));
        actionAttachmentDTOS.add(pic);
        ScrmProcessOrchestrationActionAttachmentDTO video = new ScrmProcessOrchestrationActionAttachmentDTO();
        video.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.VIDEO.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTOVideo = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetailDTOVideo.setPicUrl("picUrl");
        video.setAttachmentContent(JsonUtils.toStr(contentDetailDTOVideo));
        ScrmProcessOrchestrationActionAttachmentDTO link = new ScrmProcessOrchestrationActionAttachmentDTO();
        link.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.LINK.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTOLink = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetailDTOLink.setPicUrl("picUrl");
        link.setAttachmentContent(JsonUtils.toStr(contentDetailDTOLink));
        // when(processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentDTOList(any())).thenReturn(attachmentDTOList);
        WechatMediaResult mediaResult = new WechatMediaResult("media_id", 123456789L, "image");
        mediaResult.setErrcode(0);
        when(uploadWxAttachmentAcl.uploadWxAttachment(any(), any(), any(), anyBoolean())).thenReturn(mediaResult);
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(processOrchestrationDTO, 1, processOrchestrationDTO.getAppId(), processOrchestrationDTO.getExecutorList().stream().map(o->o.getExecutorId()).collect(Collectors.toList()), 2);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        WxMomentSendRequest result = staffMomentTouchAction.getSupplyWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO,stepExecuteResultDTO);
        assertNotNull(result);
        assertTrue(CollectionUtils.isNotEmpty(result.getAttachments()));
        assertEquals("image", result.getAttachments().get(0).getMsgtype());
        assertNotNull(result.getAttachments().get(0).getImage());
        assertEquals("media_id", result.getAttachments().get(0).getImage().getMedia_id());
    }

    /**
     * Test when nodeMediumDTO is null
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_NullNodeMediumDTO() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when processOrchestrationNodeDTOList is empty
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_EmptyNodeList() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(new ArrayList<>());
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when crowdPackType is 0
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_CrowdPackTypeZero() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setCrowdPackType(0);
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(Lists.newArrayList(new ScrmProcessOrchestrationNodeDTO()));
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("ALL_USER_TAG", result.get(0));
    }

    /**
     * Test when crowdPackType is not 0 and root node is null
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_NonZeroCrowdPackType_NullRootNode() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setCrowdPackType(1);
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(Lists.newArrayList(new ScrmProcessOrchestrationNodeDTO()));
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when crowdPackType is not 0 and has condition nodes
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_WithConditionNodes() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setCrowdPackType(1);
        // Setup root node
        ScrmProcessOrchestrationNodeDTO rootNode = new ScrmProcessOrchestrationNodeDTO();
        // PROCESS_ORCHESTRATION_ROOT
        rootNode.setNodeType(4);
        // Setup condition node
        ScrmProcessOrchestrationNodeDTO conditionNode = new ScrmProcessOrchestrationNodeDTO();
        conditionNode.setId(1L);
        // PROCESS_ORCHESTRATION_CONDITION
        conditionNode.setNodeType(1);
        // Setup node medium
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(Lists.newArrayList(rootNode, conditionNode));
        // Setup condition details for the condition node
        ScrmProcessOrchestrationConditionDetailDTO conditionDetail = new ScrmProcessOrchestrationConditionDetailDTO();
        // CORP_TAG
        conditionDetail.setFilterFieldId(40001L);
        conditionDetail.setParam(Lists.newArrayList("tag1"));
        conditionDetail.setGroupId(1);
        Map<Long, List<ScrmProcessOrchestrationConditionDetailDTO>> conditionMap = new HashMap<>();
        conditionMap.put(conditionNode.getId(), Lists.newArrayList(conditionDetail));
        nodeMediumDTO.setConditionMap(conditionMap);
        // Setup crowd pack strategy
        ScrmCrowdPackUpdateStrategyInfoDTO strategyInfoDTO = new ScrmCrowdPackUpdateStrategyInfoDTO();
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetails = new ArrayList<>();
        ScrmCrowdPackUpdateStrategyDetailDTO strategyDetail = new ScrmCrowdPackUpdateStrategyDetailDTO();
        // CORP_TAG
        strategyDetail.setFilterFieldId(40001L);
        strategyDetail.setParam(Lists.newArrayList("tag1"));
        strategyDetail.setGroupId(1);
        strategyDetails.add(strategyDetail);
        strategyInfoDTO.setCrowdPackUpdateStrategy(strategyDetails);
        processOrchestrationDTO.setCrowdPackUpdateStrategyInfoDTO(strategyInfoDTO);
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setId(1L);
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        assertEquals("tag1", result.get(0));
    }

    /**
     * 测试当wxMomentSendRequest的sender_list包含多个用户，且存在重复用户时的情况
     */
    @Test
    public void testBuildAndStoreInvokeLogListWithDuplicateSenderList() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        ExecuteManagementDTO mediumManagementDTO = new ExecuteManagementDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setNodeId(1L);
        String wxInvokeLogDOMapKey = "key";
        WxMomentSendRequest wxMomentSendRequest = new WxMomentSendRequest();
        VisibleRangeVO visibleRangeVO = new VisibleRangeVO();
        SenderListInfoVO senderListInfoVO = new SenderListInfoVO();
        List<String> userList = new ArrayList<>();
        userList.add("user1");
        userList.add("user1"); // Duplicate user
        userList.add("user2");
        senderListInfoVO.setUser_list(userList);
        visibleRangeVO.setSender_list(senderListInfoVO);
        wxMomentSendRequest.setVisible_range(visibleRangeVO);

        // act
        staffMomentTouchAction.buildAndStoreInvokeLogList(processOrchestrationDTO, mediumManagementDTO, currentProcessingNode, wxInvokeLogDOMapKey, wxMomentSendRequest);

        // assert
        verify(wxInvokeLogDOMapper, times(2)).insert(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class)); // Expect insert to be called twice for two unique users
    }

    /**
     * 测试当mediumManagementDTO的existedWxInvokeLogDOMap已包含wxInvokeLogDOMapKey时的情况
     */
    @Test
    public void testBuildAndStoreInvokeLogListWhenMapKeyExists() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        ExecuteManagementDTO mediumManagementDTO = new ExecuteManagementDTO();
        mediumManagementDTO.getExistedWxInvokeLogDOMap().put("key", new ArrayList<>());
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setNodeId(1L);
        String wxInvokeLogDOMapKey = "key";
        WxMomentSendRequest wxMomentSendRequest = new WxMomentSendRequest();
        VisibleRangeVO visibleRangeVO = new VisibleRangeVO();
        SenderListInfoVO senderListInfoVO = new SenderListInfoVO();
        List<String> userList = new ArrayList<>();
        userList.add("user1");
        senderListInfoVO.setUser_list(userList);
        visibleRangeVO.setSender_list(senderListInfoVO);
        wxMomentSendRequest.setVisible_range(visibleRangeVO);

        // act
        staffMomentTouchAction.buildAndStoreInvokeLogList(processOrchestrationDTO, mediumManagementDTO, currentProcessingNode, wxInvokeLogDOMapKey, wxMomentSendRequest);

        // assert
        verify(wxInvokeLogDOMapper, times(1)).insert(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
        assert(mediumManagementDTO.getExistedWxInvokeLogDOMap().get(wxInvokeLogDOMapKey).size() == 1); // Ensure the list associated with the map key now has one log
    }

    /**
     * 测试当wxMomentSendRequest的sender_list为空时的情况
     */
    @Test
    public void testBuildAndStoreInvokeLogListWithEmptySenderList() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ExecuteManagementDTO mediumManagementDTO = new ExecuteManagementDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        String wxInvokeLogDOMapKey = "key";
        WxMomentSendRequest wxMomentSendRequest = new WxMomentSendRequest();
        VisibleRangeVO visibleRangeVO = new VisibleRangeVO();
        visibleRangeVO.setSender_list(new SenderListInfoVO());
        wxMomentSendRequest.setVisible_range(visibleRangeVO);

        // act
        staffMomentTouchAction.buildAndStoreInvokeLogList(processOrchestrationDTO, mediumManagementDTO, currentProcessingNode, wxInvokeLogDOMapKey, wxMomentSendRequest);

        // assert
        verify(wxInvokeLogDOMapper, never()).insert(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
    }

    /**
     * 测试当wxMomentSendRequest的sender_list包含用户时的情况
     */
    @Test
    public void testBuildAndStoreInvokeLogListWithSenderList() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        ExecuteManagementDTO mediumManagementDTO = new ExecuteManagementDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setNodeId(1L);
        String wxInvokeLogDOMapKey = "key";
        WxMomentSendRequest wxMomentSendRequest = new WxMomentSendRequest();
        VisibleRangeVO visibleRangeVO = new VisibleRangeVO();
        SenderListInfoVO senderListInfoVO = new SenderListInfoVO();
        List<String> userList = new ArrayList<>();
        userList.add("user1");
        senderListInfoVO.setUser_list(userList);
        visibleRangeVO.setSender_list(senderListInfoVO);
        wxMomentSendRequest.setVisible_range(visibleRangeVO);

        // act
        staffMomentTouchAction.buildAndStoreInvokeLogList(processOrchestrationDTO, mediumManagementDTO, currentProcessingNode, wxInvokeLogDOMapKey, wxMomentSendRequest);

        // assert
        verify(wxInvokeLogDOMapper, times(1)).insert(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
    }
}