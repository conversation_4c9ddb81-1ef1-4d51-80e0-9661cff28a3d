package com.sankuai.scrm.core.service.activity.fission.validation;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FissionChainContextTest {

    @InjectMocks
    private FissionChainContext<String> fissionChainContext;

    @Mock
    private AbstractFissionChainValidator<String> mockValidator1;

    @Mock
    private AbstractFissionChainValidator<String> mockValidator2;

    /**
     * Test successful validation with multiple chain handlers
     */
    @Test
    public void testValidateWithValidMarkAndMultipleHandlers() {
        // arrange
        String mark = "testMark";
        String requestParam = "testParam";
        String operate = "testOperate";
        List<AbstractFissionChainValidator> validators = Arrays.asList(mockValidator1, mockValidator2);
        HashMap<String, List<AbstractFissionChainValidator>> chainMap = new HashMap<>();
        chainMap.put(mark, validators);
        // Use reflection to set private field
        try {
            java.lang.reflect.Field field = FissionChainContext.class.getDeclaredField("chainsGroupByMark");
            field.setAccessible(true);
            field.set(fissionChainContext, chainMap);
        } catch (Exception e) {
            fail("Failed to set up test case");
        }
        // act
        fissionChainContext.validate(mark, requestParam, operate);
        // assert
        verify(mockValidator1, times(1)).validate(requestParam, operate);
        verify(mockValidator2, times(1)).validate(requestParam, operate);
    }

    /**
     * Test validation when mark has no associated handlers
     */
    @Test
    public void testValidateWithNonExistentMark() {
        // arrange
        String mark = "nonExistentMark";
        String requestParam = "testParam";
        String operate = "testOperate";
        HashMap<String, List<AbstractFissionChainValidator>> chainMap = new HashMap<>();
        try {
            java.lang.reflect.Field field = FissionChainContext.class.getDeclaredField("chainsGroupByMark");
            field.setAccessible(true);
            field.set(fissionChainContext, chainMap);
        } catch (Exception e) {
            fail("Failed to set up test case");
        }
        // act & assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> fissionChainContext.validate(mark, requestParam, operate));
        assertEquals("指定的mark对应的责任链不存在", exception.getMessage());
    }

    /**
     * Test validation with empty handler list
     */
    @Test
    public void testValidateWithEmptyHandlerList() {
        // arrange
        String mark = "emptyMark";
        String requestParam = "testParam";
        String operate = "testOperate";
        HashMap<String, List<AbstractFissionChainValidator>> chainMap = new HashMap<>();
        chainMap.put(mark, Collections.emptyList());
        try {
            java.lang.reflect.Field field = FissionChainContext.class.getDeclaredField("chainsGroupByMark");
            field.setAccessible(true);
            field.set(fissionChainContext, chainMap);
        } catch (Exception e) {
            fail("Failed to set up test case");
        }
        // act
        fissionChainContext.validate(mark, requestParam, operate);
        // assert
        // No validators to verify, but method should complete without exception
    }

    /**
     * Test validation with null parameters
     */
    @Test
    public void testValidateWithNullParameters() {
        // arrange
        String mark = "testMark";
        String requestParam = null;
        String operate = null;
        List<AbstractFissionChainValidator> validators = Arrays.asList(mockValidator1);
        HashMap<String, List<AbstractFissionChainValidator>> chainMap = new HashMap<>();
        chainMap.put(mark, validators);
        try {
            java.lang.reflect.Field field = FissionChainContext.class.getDeclaredField("chainsGroupByMark");
            field.setAccessible(true);
            field.set(fissionChainContext, chainMap);
        } catch (Exception e) {
            fail("Failed to set up test case");
        }
        // act
        fissionChainContext.validate(mark, requestParam, operate);
        // assert
        verify(mockValidator1, times(1)).validate(null, null);
    }
}
