package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import com.sankuai.scrm.core.service.message.push.response.MsgPushResponse;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.*;

public class DeepSeaWxHandlerDealSingleSendFailedTest {

    @InjectMocks
    private DeepSeaWxHandler deepSeaWxHandler;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private ShortLinkUtils shortLinkUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    // Simple DTO class to represent the request
    public static class TestRequest {

        private String field1;

        private int field2;

        // Getters and setters
        public String getField1() {
            return field1;
        }

        public void setField1(String field1) {
            this.field1 = field1;
        }

        public int getField2() {
            return field2;
        }

        public void setField2(int field2) {
            this.field2 = field2;
        }
    }

    /**
     * Test case for normal scenario where the response contains a job ID.
     */
    @Test
    public void testDealSingleSendFailed_NormalScenario() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(1L);
        // Use the TestRequest class instead of Object
        TestRequest request = new TestRequest();
        request.setField1("value1");
        request.setField2(123);
        MsgPushResponse<Long> response = new MsgPushResponse<>();
        response.setData(12345L);
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        String executorId = "executor1";
        when(wxInvokeLogDOMapper.insertSelective(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class))).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class))).thenReturn(1);
        // act
        deepSeaWxHandler.dealSingleSendFailed(detailDO.getExecuteLogId(), request, response, wxInvokeLogDO, executorId);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).insertSelective(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class));
    }

    /**
     * Test case for scenario where the response does not contain a job ID.
     */
    @Test
    public void testDealSingleSendFailed_ResponseWithoutJobId() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(1L);
        // Use the TestRequest class instead of Object
        TestRequest request = new TestRequest();
        request.setField1("value1");
        request.setField2(123);
        MsgPushResponse<Long> response = new MsgPushResponse<>();
        response.setData(null);
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        String executorId = "executor1";
        when(wxInvokeLogDOMapper.insertSelective(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class))).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class))).thenReturn(1);
        // act
        deepSeaWxHandler.dealSingleSendFailed(detailDO.getExecuteLogId(), request, response, wxInvokeLogDO, executorId);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).insertSelective(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class));
    }

    /**
     * Test case for scenario where the response is null.
     */
    @Test
    public void testDealSingleSendFailed_NullResponse() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(1L);
        // Use the TestRequest class instead of Object
        TestRequest request = new TestRequest();
        request.setField1("value1");
        request.setField2(123);
        MsgPushResponse<Long> response = null;
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        String executorId = "executor1";
        when(wxInvokeLogDOMapper.insertSelective(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class))).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class))).thenReturn(1);
        // act
        deepSeaWxHandler.dealSingleSendFailed(detailDO.getExecuteLogId(), request, response, wxInvokeLogDO, executorId);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).insertSelective(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class));
    }

    /**
     * Test case for scenario where database insert/update operations fail.
     */
    @Test
    public void testDealSingleSendFailed_DatabaseFailure() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(1L);
        // Use the TestRequest class instead of Object
        TestRequest request = new TestRequest();
        request.setField1("value1");
        request.setField2(123);
        MsgPushResponse<Long> response = new MsgPushResponse<>();
        response.setData(12345L);
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        String executorId = "executor1";
        when(wxInvokeLogDOMapper.insertSelective(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class))).thenReturn(0);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(0);
        when(executeLogDOMapper.updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class))).thenReturn(0);
        // act
        deepSeaWxHandler.dealSingleSendFailed(detailDO.getExecuteLogId(), request, response, wxInvokeLogDO, executorId);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).insertSelective(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class));
    }

    /**
     * Test case for normal flow where marketing copy is not blank.
     */
    @Test
    public void testGetCouponPageAttachmentVOV2NormalFlowWithMarketingCopy() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTitle("Test Title");
        supplyDetailDTO.setHeadpicUrl("http://example.com/image.jpg");
        supplyDetailDTO.setMarketingCopy("Test Marketing Copy");
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        when(informationGatheringService.queryCommunityDistributor(any())).thenReturn("communityDistributorCode");
        when(productManagementService.getActivSceneCodeDO(any(), any(), any(), any(), any())).thenReturn(new ScrmAmProcessOrchestrationActivSceneCodeDO());
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("http://short.link");
        // act
        List<MsgPushContentDTO> result = deepSeaWxHandler.getCouponPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, null);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(ContentTypeTEnum.MINI_PROGRAM, result.get(0).getContentTypeTEnum());
        assertEquals(ContentTypeTEnum.TEXT, result.get(1).getContentTypeTEnum());
        assertEquals("Test Marketing Copy", result.get(1).getTextDTO().getContent());
    }

    /**
     * Test case for normal flow where marketing copy is blank.
     */
    @Test
    public void testGetCouponPageAttachmentVOV2NormalFlowWithoutMarketingCopy() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTitle("Test Title");
        supplyDetailDTO.setHeadpicUrl("http://example.com/image.jpg");
        supplyDetailDTO.setMarketingCopy("");
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        when(informationGatheringService.queryCommunityDistributor(any())).thenReturn("communityDistributorCode");
        when(productManagementService.getActivSceneCodeDO(any(), any(), any(), any(), any())).thenReturn(new ScrmAmProcessOrchestrationActivSceneCodeDO());
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("http://short.link");
        // act
        List<MsgPushContentDTO> result = deepSeaWxHandler.getCouponPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, null);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(ContentTypeTEnum.MINI_PROGRAM, result.get(0).getContentTypeTEnum());
    }

    /**
     * Test case for exception flow where supplyDetailDTO is null.
     */
    @Test(expected = NullPointerException.class)
    public void testGetCouponPageAttachmentVOV2ExceptionFlowSupplyDetailDTONull() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = null;
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        // act
        deepSeaWxHandler.getCouponPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, null);
        // assert
        // Expecting NullPointerException
    }

    /**
     * Test case for exception flow where pageInfo is null.
     */
    @Test(expected = NullPointerException.class)
    public void testGetCouponPageAttachmentVOV2ExceptionFlowPageInfoNull() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = null;
        // act
        deepSeaWxHandler.getCouponPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, null);
        // assert
        // Expecting NullPointerException
    }

    /**
     * Test case for exception flow where actionAttachmentDTO is null.
     */
    @Test(expected = NullPointerException.class)
    public void testGetCouponPageAttachmentVOV2ExceptionFlowActionAttachmentDTONull() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = null;
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        // act
        deepSeaWxHandler.getCouponPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, null);
        // assert
        // Expecting NullPointerException
    }

    /**
     * Test case for exception flow where processOrchestrationDTO is null.
     */
    @Test(expected = NullPointerException.class)
    public void testGetCouponPageAttachmentVOV2ExceptionFlowProcessOrchestrationDTONull() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = null;
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        // act
        deepSeaWxHandler.getCouponPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, null);
        // assert
        // Expecting NullPointerException
    }
}
