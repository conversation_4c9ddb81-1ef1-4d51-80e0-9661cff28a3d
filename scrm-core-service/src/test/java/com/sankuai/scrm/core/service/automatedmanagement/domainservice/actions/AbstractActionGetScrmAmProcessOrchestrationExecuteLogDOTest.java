package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import java.util.ArrayList;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class AbstractActionGetScrmAmProcessOrchestrationExecuteLogDOTest {

    @Spy
    private AbstractAction abstractAction = new AbstractAction() {

        @Override
        protected List<ScrmAmProcessOrchestrationExecuteLogDO> getExecuteLogByExample(ScrmAmProcessOrchestrationExecuteLogDOExample executeLogDOExample) {
            return new ArrayList<>();
        }
    };

    /**
     * Test case to cover example.limit(1) when participationRestrict is true
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_ParticipationRestrictTrue() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setParticipationRestrict(true);
        processOrchestrationDTO.setAppId("testApp");
        processOrchestrationDTO.setValidVersion("v1");
        ScrmCrowdPackDetailInfoDTO crowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        crowdPackDetailInfoDTO.setExternalUserWxUnionId("testUnionId");
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setNodeId(1L);
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, crowdPackDetailInfoDTO, nodeDTO, null);
        // assert
        assertNotNull(result);
        assertEquals("testApp", result.getAppId());
        assertEquals("system", result.getExecutorId());
    }

    /**
     * Test case to cover calendar manipulation when participationRestrictionsCycle > 1
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_ParticipationRestrictionsCycleGreaterThanOne() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setParticipationRestrict(false);
        processOrchestrationDTO.setParticipationRestrictionsCycle((byte) 3);
        processOrchestrationDTO.setParticipationRestrictionsTimes((byte) 2);
        ScrmCrowdPackDetailInfoDTO crowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        crowdPackDetailInfoDTO.setExternalUserWxUnionId("testUnionId");
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setNodeId(1L);
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, crowdPackDetailInfoDTO, nodeDTO, null);
        // assert
        assertNotNull(result);
    }

    /**
     * Test case to cover setting targetId based on null externalUserId
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_NullExternalUserId() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setParticipationRestrict(true);
        ScrmCrowdPackDetailInfoDTO crowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        crowdPackDetailInfoDTO.setExternalUserWxUnionId("testUnionId");
        crowdPackDetailInfoDTO.setExternalUserId(null);
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setNodeId(1L);
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, crowdPackDetailInfoDTO, nodeDTO, null);
        // assert
        assertNotNull(result);
        assertEquals(0L, result.getTargetId().longValue());
    }

    /**
     * Test case to cover existedLogs handling logic
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_WithExistedLogs() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setParticipationRestrict(false);
        processOrchestrationDTO.setParticipationRestrictionsTimes((byte) 3);
        ScrmCrowdPackDetailInfoDTO crowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        crowdPackDetailInfoDTO.setExternalUserWxUnionId("testUnionId");
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setNodeId(1L);
        List<ScrmAmProcessOrchestrationExecuteLogDO> existedLogs = new ArrayList<>();
        existedLogs.add(new ScrmAmProcessOrchestrationExecuteLogDO());
        doReturn(existedLogs).when(abstractAction).getExecuteLogByExample(any());
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, crowdPackDetailInfoDTO, nodeDTO, null);
        // assert
        assertNotNull(result);
    }
}
