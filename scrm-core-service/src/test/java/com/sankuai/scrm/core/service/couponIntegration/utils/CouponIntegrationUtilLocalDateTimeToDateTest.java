package com.sankuai.scrm.core.service.couponIntegration.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.Date;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CouponIntegrationUtilLocalDateTimeToDateTest {

    @InjectMocks
    private CouponIntegrationUtil couponIntegrationUtil;

    /**
     * 测试 localDateTimeToDate 方法，传入的 LocalDateTime 对象非空
     */
    @Test
    public void testLocalDateTimeToDateWhenLocalDateTimeIsNotNull() throws Throwable {
        // arrange
        LocalDateTime date = LocalDateTime.of(2022, 1, 1, 0, 0);
        // act
        Date result = couponIntegrationUtil.localDateTimeToDate(date);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试 localDateTimeToDate 方法，传入的 LocalDateTime 对象为 null
     */
    @Test(expected = NullPointerException.class)
    public void testLocalDateTimeToDateWhenLocalDateTimeIsNull() throws Throwable {
        // arrange
        LocalDateTime date = null;
        // act
        couponIntegrationUtil.localDateTimeToDate(date);
        // assert is handled by the expected exception
    }
}
