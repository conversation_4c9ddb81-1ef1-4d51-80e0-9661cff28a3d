package com.sankuai.scrm.core.service.automatedmanagement.mq.consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import java.util.Properties;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import java.lang.reflect.Field;
import org.mockito.Mock;
import org.mockito.junit.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.doThrow;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.dz.srcm.automatedmanagement.dto.mq.RefinementOperationExecuteMessage;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.exception.PersonaCrowdPackException;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.util.Collections;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

@ExtendWith(MockitoExtension.class)
class UpdateAllCrowdPackMessageConsumerTest {

    @InjectMocks
    private UpdateAllCrowdPackMessageConsumer consumer;

    @Mock
    private ExecuteWriteDomainService executeDomainService;

    /**
     * Test successful initialization with all required properties
     */
    @Test
    public void testAfterPropertiesSetSuccess() throws Exception {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockProcessor = mock(IConsumerProcessor.class);
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenReturn(mockProcessor);
            consumer.afterPropertiesSet();
            mockedMafkaClient.verify(() -> MafkaClient.buildConsumerFactory(any(Properties.class), eq("scrm.update.all.crowd.pack.msg")));
            verify(mockProcessor).recvMessageWithParallel(eq(String.class), any());
        }
    }

    /**
     * Test exception when building consumer factory fails
     */
    @Test
    public void testAfterPropertiesSetBuildConsumerFailure() throws Exception {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenThrow(new RuntimeException("Build failed"));
            Exception exception = assertThrows(RuntimeException.class, () -> {
                consumer.afterPropertiesSet();
            });
            assertEquals("Build failed", exception.getMessage());
        }
    }

    /**
     * Test exception when setting up message listener fails
     */
    @Test
    public void testAfterPropertiesSetRecvMessageFailure() throws Exception {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockProcessor = mock(IConsumerProcessor.class);
            doThrow(new RuntimeException("Listener setup failed")).when(mockProcessor).recvMessageWithParallel(any(), any());
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenReturn(mockProcessor);
            Exception exception = assertThrows(RuntimeException.class, () -> {
                consumer.afterPropertiesSet();
            });
            assertEquals("Listener setup failed", exception.getMessage());
        }
    }

    /**
     * Test that required properties are set correctly
     */
    @Test
    public void testAfterPropertiesSetPropertiesConfiguration() throws Exception {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockProcessor = mock(IConsumerProcessor.class);
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenAnswer(invocation -> {
                Properties props = invocation.getArgument(0);
                assertEquals("daozong", props.getProperty(ConsumerConstants.MafkaBGNamespace));
                assertEquals("com.sankuai.medicalcosmetology.scrm.core", props.getProperty(ConsumerConstants.MafkaClientAppkey));
                assertEquals("scrm.update.all.crowd.pack.msg.consumer", props.getProperty(ConsumerConstants.SubscribeGroup));
                return mockProcessor;
            });
            consumer.afterPropertiesSet();
        }
    }

    @Test
    public void testDestroyWhenConsumerIsNull() throws Throwable {
        // arrange
        UpdateAllCrowdPackMessageConsumer consumer = new UpdateAllCrowdPackMessageConsumer();
        // Set the static consumer field to null using reflection
        Field consumerField = UpdateAllCrowdPackMessageConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(null, null);
        // act
        consumer.destroy();
        // assert
        // No exception should be thrown
        // We can't verify a null object, but we can verify the field is still null
        Object fieldValue = consumerField.get(null);
        assert fieldValue == null : "Consumer field should remain null";
    }

    @Test
    public void testDestroyWhenConsumerIsNotNull() throws Throwable {
        // arrange
        UpdateAllCrowdPackMessageConsumer consumer = new UpdateAllCrowdPackMessageConsumer();
        IConsumerProcessor mockConsumer = mock(IConsumerProcessor.class);
        // Set the static consumer field using reflection
        Field consumerField = UpdateAllCrowdPackMessageConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(null, mockConsumer);
        // act
        consumer.destroy();
        // assert
        verify(mockConsumer, times(1)).close();
        // Reset the static field to avoid affecting other tests
        consumerField.set(null, null);
    }

    @Test
    public void testDestroyWhenConsumerCloseThrowsException() throws Throwable {
        // arrange
        UpdateAllCrowdPackMessageConsumer consumer = new UpdateAllCrowdPackMessageConsumer();
        IConsumerProcessor mockConsumer = mock(IConsumerProcessor.class);
        Exception expectedException = new Exception("Test exception");
        doThrow(expectedException).when(mockConsumer).close();
        // Set the static consumer field using reflection
        Field consumerField = UpdateAllCrowdPackMessageConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(null, mockConsumer);
        // act & assert
        Exception actualException = org.junit.jupiter.api.Assertions.assertThrows(Exception.class, () -> consumer.destroy());
        org.junit.jupiter.api.Assertions.assertEquals(expectedException, actualException);
        verify(mockConsumer, times(1)).close();
        // Reset the static field to avoid affecting other tests
        consumerField.set(null, null);
    }

    @Test
    public void testRecvMessageEmptyBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "");
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = consumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(executeDomainService, never()).updateCrowdPackSubTask(anyList());
        verify(executeDomainService, never()).updatePersonaCrowdPackSubTask(anyInt(), anyLong(), anyString(), anyString());
    }

    @Test
    public void testRecvMessageNullBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", null);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = consumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(executeDomainService, never()).updateCrowdPackSubTask(anyList());
        verify(executeDomainService, never()).updatePersonaCrowdPackSubTask(anyInt(), anyLong(), anyString(), anyString());
    }

    @Test
    public void testRecvMessageInvalidJson() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "invalid json");
        MessagetContext context = new MessagetContext();
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject(eq("invalid json"), any())).thenReturn(null);
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(executeDomainService, never()).updateCrowdPackSubTask(anyList());
            verify(executeDomainService, never()).updatePersonaCrowdPackSubTask(anyInt(), anyLong(), anyString(), anyString());
        }
    }

    @Test
    public void testRecvMessagePeriodicCrowdPackUpdateSuccess() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setTaskType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue());
        msg.setUserUnionIds(Collections.singletonList("union1"));
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject(eq("valid json"), any())).thenReturn(msg);
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(executeDomainService).updateCrowdPackSubTask(anyList());
        }
    }

    @Test
    public void testRecvMessagePeriodicCrowdPackUpdateExecutionException() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setTaskType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue());
        msg.setUserUnionIds(Collections.singletonList("union1"));
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject(eq("valid json"), any())).thenReturn(msg);
            doThrow(new ExecutionException("test", new Throwable())).when(executeDomainService).updateCrowdPackSubTask(anyList());
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.RECONSUME_LATER, result);
        }
    }

    @Test
    public void testRecvMessagePeriodicCrowdPackUpdateInterruptedException() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setTaskType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue());
        msg.setUserUnionIds(Collections.singletonList("union1"));
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject(eq("valid json"), any())).thenReturn(msg);
            doThrow(new InterruptedException("test")).when(executeDomainService).updateCrowdPackSubTask(anyList());
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.RECONSUME_LATER, result);
        }
    }

    @Test
    public void testRecvMessagePeriodicCrowdPackUpdateTimeoutException() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setTaskType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue());
        msg.setUserUnionIds(Collections.singletonList("union1"));
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject(eq("valid json"), any())).thenReturn(msg);
            doThrow(new TimeoutException("test")).when(executeDomainService).updateCrowdPackSubTask(anyList());
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.RECONSUME_LATER, result);
        }
    }

    @Test
    public void testRecvMessagePersonaCrowdPackUpdateWithPersonaException() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setTaskType(ScrmProcessOrchestrationTaskTypeEnum.PERSONA_CROWD_PACK_UPDATE.getValue());
        msg.setPersonaId(1);
        msg.setCrowdPackId(1L);
        msg.setAppId("app1");
        msg.setCrowdPackVersion("v1");
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject(eq("valid json"), any())).thenReturn(msg);
            doThrow(new PersonaCrowdPackException("test")).when(executeDomainService).updatePersonaCrowdPackSubTask(anyInt(), anyLong(), anyString(), anyString());
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        }
    }

    @Test
    public void testRecvMessagePersonaCrowdPackUpdateWithOtherException() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setTaskType(ScrmProcessOrchestrationTaskTypeEnum.PERSONA_CROWD_PACK_UPDATE.getValue());
        msg.setPersonaId(1);
        msg.setCrowdPackId(1L);
        msg.setAppId("app1");
        msg.setCrowdPackVersion("v1");
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject(eq("valid json"), any())).thenReturn(msg);
            doThrow(new RuntimeException("test")).when(executeDomainService).updatePersonaCrowdPackSubTask(anyInt(), anyLong(), anyString(), anyString());
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.RECONSUME_LATER, result);
        }
    }

    @Test
    public void testRecvMessageUnknownTaskType() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        // Unknown task type
        msg.setTaskType(999);
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject(eq("valid json"), any())).thenReturn(msg);
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(executeDomainService, never()).updateCrowdPackSubTask(anyList());
            verify(executeDomainService, never()).updatePersonaCrowdPackSubTask(anyInt(), anyLong(), anyString(), anyString());
        }
    }
}
