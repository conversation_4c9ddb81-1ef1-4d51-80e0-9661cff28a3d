package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentContentDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationAttachmentTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import com.sankuai.scrm.core.service.message.push.response.MsgPushResponse;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationContentSupplyTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.group.dynamiccode.constant.GroupDynamicCodeConstants;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import java.util.List;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
class DeepSeaWxHandlerBuildOfficialMsgDTOV2Test {

    private DeepSeaWxHandler handler;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @InjectMocks
    private DeepSeaWxHandler deepSeaWxHandler;

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private ShortLinkUtils shortLinkUtils;

    @BeforeEach
    void setUp() {
        handler = new DeepSeaWxHandler();
    }

    /**
     * Test building image type message content
     */
    @Test
    void testBuildOfficialMsgDTOV2ImageType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachment = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachment.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.PICTURE.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetail = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetail.setPicUrl("http://example.com/image.jpg");
        attachment.setAttachmentContentDetailDTO(contentDetail);
        // act
        MsgPushContentDTO result = handler.buildOfficialMsgDTOV2(attachment);
        // assert
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.IMAGE, result.getContentTypeTEnum());
        assertNotNull(result.getImageDTO());
        assertEquals("http://example.com/image.jpg", result.getImageDTO().getUrl());
    }

    /**
     * Test building link type message content
     */
    @Test
    void testBuildOfficialMsgDTOV2LinkType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachment = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachment.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.LINK.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetail = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetail.setContentUrl("http://example.com");
        contentDetail.setTitle("Example Title");
        contentDetail.setDesc("Example Description");
        contentDetail.setPicUrl("http://example.com/thumb.jpg");
        attachment.setAttachmentContentDetailDTO(contentDetail);
        // act
        MsgPushContentDTO result = handler.buildOfficialMsgDTOV2(attachment);
        // assert
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.LINK, result.getContentTypeTEnum());
        assertNotNull(result.getLinkDTO());
        assertEquals("http://example.com", result.getLinkDTO().getUrl());
        assertEquals("Example Title", result.getLinkDTO().getTitle());
        assertEquals("Example Description", result.getLinkDTO().getDescription());
        assertEquals("http://example.com/thumb.jpg", result.getLinkDTO().getThumbUrl());
    }

    /**
     * Test building mini program type message content
     */
    @Test
    void testBuildOfficialMsgDTOV2MiniProgramType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachment = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachment.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.MINI_PROGRAM.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetail = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetail.setOriginAppId("originApp");
        contentDetail.setAppId("appId");
        contentDetail.setDesc("Mini Program Description");
        contentDetail.setPicUrl("http://example.com/thumb.jpg");
        contentDetail.setContentUrl("/pages/index");
        attachment.setAttachmentContentDetailDTO(contentDetail);
        // act
        MsgPushContentDTO result = handler.buildOfficialMsgDTOV2(attachment);
        // assert
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.MINI_PROGRAM, result.getContentTypeTEnum());
        assertNotNull(result.getMiniProgramDTO());
        assertEquals("originApp", result.getMiniProgramDTO().getOriginAppId());
        assertEquals("appId", result.getMiniProgramDTO().getAppId());
        assertEquals("Mini Program Description", result.getMiniProgramDTO().getDescription());
        assertEquals("http://example.com/thumb.jpg", result.getMiniProgramDTO().getThumbnail());
        assertEquals("/pages/index", result.getMiniProgramDTO().getPagePath());
    }

    /**
     * Test building video type message content
     */
    @Test
    void testBuildOfficialMsgDTOV2VideoType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachment = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachment.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.VIDEO.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetail = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetail.setContentUrl("http://example.com/video.mp4");
        attachment.setAttachmentContentDetailDTO(contentDetail);
        // act
        MsgPushContentDTO result = handler.buildOfficialMsgDTOV2(attachment);
        // assert
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.VIDEO, result.getContentTypeTEnum());
        assertNotNull(result.getVideoDTO());
        assertEquals("http://example.com/video.mp4", result.getVideoDTO().getUrl());
    }

    /**
     * Test building text type message content
     */
    @Test
    void testBuildOfficialMsgDTOV2TextType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachment = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachment.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.TEXT.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetail = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetail.setContentUrl("This is a text message");
        attachment.setAttachmentContentDetailDTO(contentDetail);
        // act
        MsgPushContentDTO result = handler.buildOfficialMsgDTOV2(attachment);
        // assert
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.TEXT, result.getContentTypeTEnum());
        assertNotNull(result.getTextDTO());
        assertEquals("This is a text message", result.getTextDTO().getContent());
    }

    /**
     * Test handling unknown attachment type
     */
    @Test
    void testBuildOfficialMsgDTOV2UnknownType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachment = new ScrmProcessOrchestrationActionAttachmentDTO();
        // Unknown type
        attachment.setAttachmentTypeId(999);
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetail = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        attachment.setAttachmentContentDetailDTO(contentDetail);
        // act
        MsgPushContentDTO result = handler.buildOfficialMsgDTOV2(attachment);
        // assert
        assertNull(result);
    }

    /**
     * Test handling null input
     */
    @Test
    void testBuildOfficialMsgDTOV2NullInput() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachment = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            handler.buildOfficialMsgDTOV2(attachment);
        });
    }

    /**
     * Test handling missing content detail
     */
    @Test
    void testBuildOfficialMsgDTOV2MissingContentDetail() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachment = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachment.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.PICTURE.getValue());
        attachment.setAttachmentContentDetailDTO(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            handler.buildOfficialMsgDTOV2(attachment);
        });
    }

    static class TestRequest {

        @JsonProperty("field1")
        private String field1;

        @JsonProperty("field2")
        private Integer field2;

        public TestRequest(String field1, Integer field2) {
            this.field1 = field1;
            this.field2 = field2;
        }

        public String getField1() {
            return field1;
        }

        public Integer getField2() {
            return field2;
        }
    }

    /**
     * Test normal scenario with successful response containing Long data
     */
    @Test
    public void testDealSingleSendFailed_NormalScenarioWithLongData() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(1L);
        TestRequest request = new TestRequest("value1", 123);
        MsgPushResponse<Long> response = new MsgPushResponse<>();
        response.setData(12345L);
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        // CREATED_FAILED
        wxInvokeLogDO.setStatus((byte) 4);
        String executorId = "executor1";
        when(wxInvokeLogDOMapper.insertSelective(any())).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // act
        deepSeaWxHandler.dealSingleSendFailed(detailDO.getExecuteLogId(), request, response, wxInvokeLogDO, executorId);
        // assert
        verify(wxInvokeLogDOMapper).insertSelective(any());
        verify(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper).updateByExampleSelective(any(), any());
    }

    /**
     * Test scenario with null response data
     */
    @Test
    public void testDealSingleSendFailed_NullResponseData() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(1L);
        TestRequest request = new TestRequest("value1", 123);
        MsgPushResponse<Long> response = new MsgPushResponse<>();
        response.setData(null);
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        wxInvokeLogDO.setJobid(StringUtils.EMPTY);
        String executorId = "executor1";
        when(wxInvokeLogDOMapper.insertSelective(any())).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // act
        deepSeaWxHandler.dealSingleSendFailed(detailDO.getExecuteLogId(), request, response, wxInvokeLogDO, executorId);
        // assert
        verify(wxInvokeLogDOMapper).insertSelective(any());
        verify(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper).updateByExampleSelective(any(), any());
    }

    /**
     * Test scenario with null response
     */
    @Test
    public void testDealSingleSendFailed_NullResponse() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(1L);
        TestRequest request = new TestRequest("value1", 123);
        MsgPushResponse<Long> response = null;
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        wxInvokeLogDO.setJobid(StringUtils.EMPTY);
        String executorId = "executor1";
        when(wxInvokeLogDOMapper.insertSelective(any())).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // act
        deepSeaWxHandler.dealSingleSendFailed(detailDO.getExecuteLogId(), request, response, wxInvokeLogDO, executorId);
        // assert
        verify(wxInvokeLogDOMapper).insertSelective(any());
        verify(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper).updateByExampleSelective(any(), any());
    }

    /**
     * Test scenario with non-Long response data
     */
    @Test
    public void testDealSingleSendFailed_NonLongResponseData() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(1L);
        TestRequest request = new TestRequest("value1", 123);
        MsgPushResponse<String> response = new MsgPushResponse<>();
        response.setData("string-data");
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        wxInvokeLogDO.setJobid(StringUtils.EMPTY);
        String executorId = "executor1";
        when(wxInvokeLogDOMapper.insertSelective(any())).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // act
        deepSeaWxHandler.dealSingleSendFailed(detailDO.getExecuteLogId(), request, response, wxInvokeLogDO, executorId);
        // assert
        verify(wxInvokeLogDOMapper).insertSelective(any());
        verify(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper).updateByExampleSelective(any(), any());
    }

    /**
     * Test scenario with database update failures
     */
    @Test
    public void testDealSingleSendFailed_DatabaseUpdateFailures() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(1L);
        TestRequest request = new TestRequest("value1", 123);
        MsgPushResponse<Long> response = new MsgPushResponse<>();
        response.setData(12345L);
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        wxInvokeLogDO.setJobid(String.valueOf(response.getData()));
        String executorId = "executor1";
        when(wxInvokeLogDOMapper.insertSelective(any())).thenReturn(0);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(0);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(0);
        // act
        deepSeaWxHandler.dealSingleSendFailed(detailDO.getExecuteLogId(), request, response, wxInvokeLogDO, executorId);
        // assert
        verify(wxInvokeLogDOMapper).insertSelective(any());
        verify(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper).updateByExampleSelective(any(), any());
    }

    /**
     * Test scenario with null detailDO
     */
    @Test
    public void testDealSingleSendFailed_NullDetailDO() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = null;
        TestRequest request = new TestRequest("value1", 123);
        MsgPushResponse<Long> response = new MsgPushResponse<>();
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        String executorId = "executor1";
        // act & assert
        org.junit.jupiter.api.Assertions.assertThrows(RuntimeException.class, () -> {
            deepSeaWxHandler.dealSingleSendFailed(detailDO.getExecuteLogId(), request, response, wxInvokeLogDO, executorId);
        });
    }

    /**
     * Test scenario with null wxInvokeLogDO
     */
    @Test
    public void testDealSingleSendFailed_NullWxInvokeLogDO() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        TestRequest request = new TestRequest("value1", 123);
        MsgPushResponse<Long> response = new MsgPushResponse<>();
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = null;
        String executorId = "executor1";
        // act & assert
        org.junit.jupiter.api.Assertions.assertThrows(RuntimeException.class, () -> {
            deepSeaWxHandler.dealSingleSendFailed(detailDO.getExecuteLogId(), request, response, wxInvokeLogDO, executorId);
        });
    }

    @Test
    public void testGetCouponPageAttachmentVOV2NormalCase() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTitle("Test Title");
        supplyDetailDTO.setHeadpicUrl("http://test.com/image.jpg");
        supplyDetailDTO.setMarketingCopy("Test marketing copy");
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneCode("testSceneCode");
        // Must set this to avoid NPE
        sceneCodeDO.setPoiRestrict(1);
        when(informationGatheringService.queryCommunityDistributor(any())).thenReturn("testDistributorCode");
        when(productManagementService.getActivSceneCodeDO(any(), any(), any(), any(), any())).thenReturn(sceneCodeDO);
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("https://short.url");
        // act
        List<MsgPushContentDTO> result = deepSeaWxHandler.getCouponPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, executeManagementDTO);
        // assert
        assertEquals(2, result.size());
        assertEquals(GroupDynamicCodeConstants.MX_MINIP_APPID, result.get(0).getMiniProgramDTO().getAppId());
        assertEquals("Test Title", result.get(0).getMiniProgramDTO().getDescription());
        assertEquals("Test marketing copy", result.get(1).getTextDTO().getContent());
    }

    @Test
    public void testGetCouponPageAttachmentVOV2WithoutMarketingCopy() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTitle("Test Title");
        supplyDetailDTO.setHeadpicUrl("http://test.com/image.jpg");
        // No marketing copy
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneCode("testSceneCode");
        // Must set this to avoid NPE
        sceneCodeDO.setPoiRestrict(1);
        when(informationGatheringService.queryCommunityDistributor(any())).thenReturn("testDistributorCode");
        when(productManagementService.getActivSceneCodeDO(any(), any(), any(), any(), any())).thenReturn(sceneCodeDO);
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("https://short.url");
        // act
        List<MsgPushContentDTO> result = deepSeaWxHandler.getCouponPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, executeManagementDTO);
        // assert
        assertEquals(1, result.size());
        assertEquals(GroupDynamicCodeConstants.MX_MINIP_APPID, result.get(0).getMiniProgramDTO().getAppId());
        assertEquals("Test Title", result.get(0).getMiniProgramDTO().getDescription());
    }

    @Test
    public void testGetCouponPageAttachmentVOV2WithNullSupplyDetail() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            // null supplyDetailDTO
            // null supplyDetailDTO
            // null supplyDetailDTO
            // null supplyDetailDTO
            deepSeaWxHandler.getCouponPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, null, pageInfo, executeManagementDTO);
        });
    }

    @Test
    public void testGetCouponPageAttachmentVOV2WithNullSceneCodeDO() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTitle("Test Title");
        supplyDetailDTO.setHeadpicUrl("http://test.com/image.jpg");
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        when(informationGatheringService.queryCommunityDistributor(any())).thenReturn("testDistributorCode");
        when(productManagementService.getActivSceneCodeDO(any(), any(), any(), any(), any())).thenReturn(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            deepSeaWxHandler.getCouponPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, executeManagementDTO);
        });
    }

    @Test
    public void testGetCouponPageAttachmentVOV2WithNullDistributorCode() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTitle("Test Title");
        supplyDetailDTO.setHeadpicUrl("http://test.com/image.jpg");
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        when(informationGatheringService.queryCommunityDistributor(any())).thenReturn(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            deepSeaWxHandler.getCouponPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, executeManagementDTO);
        });
    }
}
