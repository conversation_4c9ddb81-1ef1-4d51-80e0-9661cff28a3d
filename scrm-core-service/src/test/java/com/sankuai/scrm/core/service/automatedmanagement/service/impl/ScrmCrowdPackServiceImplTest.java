package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.request.QueryCrowdPackDetailInfoByPackIdRequest;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackPageQueryPackDetailDomainServiceImpl;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ScrmCrowdPackServiceImplTest {

    @Mock
    private CrowdPackPageQueryPackDetailDomainServiceImpl crowdPackPageQueryPackDetailService;

    @InjectMocks
    private ScrmCrowdPackServiceImpl scrmCrowdPackService;

    /**
     * 测试request为null时返回参数错误
     */
    @Test
    public void testQueryCrowdPackDetailInfoByPackIdNullRequest() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = null;
        // act
        PageRemoteResponse<ScrmCrowdPackDetailInfoDTO> response = scrmCrowdPackService.queryCrowdPackDetailInfoByPackId(request);
        // assert
        assertNotNull(response);
        assertEquals("参数错误", response.getMsg());
        assertNull(response.getData());
    }

    /**
     * 测试packId为null时返回参数错误
     */
    @Test
    public void testQueryCrowdPackDetailInfoByPackIdNullPackId() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(null);
        request.setPageNumber(1);
        request.setPageSize(10);
        // act
        PageRemoteResponse<ScrmCrowdPackDetailInfoDTO> response = scrmCrowdPackService.queryCrowdPackDetailInfoByPackId(request);
        // assert
        assertNotNull(response);
        assertEquals("参数错误", response.getMsg());
        assertNull(response.getData());
    }

    /**
     * 测试pageSize超过100时返回错误
     */
    @Test
    public void testQueryCrowdPackDetailInfoByPackIdPageSizeExceedLimit() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(1L);
        request.setPageNumber(1);
        request.setPageSize(101);
        // act
        PageRemoteResponse<ScrmCrowdPackDetailInfoDTO> response = scrmCrowdPackService.queryCrowdPackDetailInfoByPackId(request);
        // assert
        assertNotNull(response);
        assertEquals("单次查询数量超过100条", response.getMsg());
        assertNull(response.getData());
    }

    /**
     * 测试pageSize等于100的边界情况
     */
    @Test
    public void testQueryCrowdPackDetailInfoByPackIdPageSizeBoundary() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(1L);
        request.setPageNumber(1);
        request.setPageSize(100);
        request.setLastPageId(0L);
        when(crowdPackPageQueryPackDetailService.countPageCrowdPackDetailInfoByPackIdList(request)).thenReturn(200L);
        when(crowdPackPageQueryPackDetailService.queryPageCrowdPackDetailInfoByPackIdList(request)).thenReturn(Collections.singletonList(new ScrmCrowdPackDetailInfoDTO()));
        // act
        PageRemoteResponse<ScrmCrowdPackDetailInfoDTO> response = scrmCrowdPackService.queryCrowdPackDetailInfoByPackId(request);
        // assert
        assertNotNull(response);
        assertFalse(response.getData().isEmpty());
        assertEquals(200L, response.getTotalHit());
        assertFalse(response.isEnd());
    }

    /**
     * 测试查询结果为空的情况
     */
    @Test
    public void testQueryCrowdPackDetailInfoByPackIdEmptyResult() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(1L);
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setLastPageId(0L);
        when(crowdPackPageQueryPackDetailService.countPageCrowdPackDetailInfoByPackIdList(request)).thenReturn(0L);
        // act
        PageRemoteResponse<ScrmCrowdPackDetailInfoDTO> response = scrmCrowdPackService.queryCrowdPackDetailInfoByPackId(request);
        // assert
        assertNotNull(response);
        assertTrue(response.getData().isEmpty());
        assertEquals(0L, response.getTotalHit());
        assertTrue(response.isEnd());
    }

    /**
     * 测试lastPageId为null时自动设置为0
     */
    @Test
    public void testQueryCrowdPackDetailInfoByPackIdNullLastPageId() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(1L);
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setLastPageId(null);
        when(crowdPackPageQueryPackDetailService.countPageCrowdPackDetailInfoByPackIdList(request)).thenReturn(5L);
        when(crowdPackPageQueryPackDetailService.queryPageCrowdPackDetailInfoByPackIdList(request)).thenReturn(Collections.singletonList(new ScrmCrowdPackDetailInfoDTO()));
        // act
        PageRemoteResponse<ScrmCrowdPackDetailInfoDTO> response = scrmCrowdPackService.queryCrowdPackDetailInfoByPackId(request);
        // assert
        assertNotNull(response);
        assertEquals(0L, request.getLastPageId());
        assertEquals(5L, response.getTotalHit());
        // Fixed: pageNumber(1) * pageSize(10) = 10 >= total(5) → isEnd should be true
        assertTrue(response.isEnd());
    }

    /**
     * 测试正常查询返回分页结果
     */
    @Test
    public void testQueryCrowdPackDetailInfoByPackIdNormalCase() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(1L);
        request.setPageNumber(2);
        request.setPageSize(10);
        request.setLastPageId(10L);
        ScrmCrowdPackDetailInfoDTO dto = new ScrmCrowdPackDetailInfoDTO();
        dto.setId(11L);
        dto.setPackId(1L);
        when(crowdPackPageQueryPackDetailService.countPageCrowdPackDetailInfoByPackIdList(request)).thenReturn(15L);
        when(crowdPackPageQueryPackDetailService.queryPageCrowdPackDetailInfoByPackIdList(request)).thenReturn(Collections.singletonList(dto));
        // act
        PageRemoteResponse<ScrmCrowdPackDetailInfoDTO> response = scrmCrowdPackService.queryCrowdPackDetailInfoByPackId(request);
        // assert
        assertNotNull(response);
        assertEquals(1, response.getData().size());
        assertEquals(15L, response.getTotalHit());
        // pageNumber(2) * pageSize(10) = 20 >= total(15) → isEnd should be true
        assertTrue(response.isEnd());
        assertEquals(1L, response.getData().get(0).getPackId());
    }

    /**
     * 测试pageNumber和pageSize为0时返回参数错误
     */
    @Test
    public void testQueryCrowdPackDetailInfoByPackIdInvalidPageParams() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(1L);
        request.setPageNumber(0);
        request.setPageSize(0);
        // act
        PageRemoteResponse<ScrmCrowdPackDetailInfoDTO> response = scrmCrowdPackService.queryCrowdPackDetailInfoByPackId(request);
        // assert
        assertNotNull(response);
        assertEquals("参数错误", response.getMsg());
        assertNull(response.getData());
    }
}
