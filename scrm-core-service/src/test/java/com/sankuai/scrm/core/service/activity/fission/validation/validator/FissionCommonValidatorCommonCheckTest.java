package com.sankuai.scrm.core.service.activity.fission.validation.validator;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.activity.fission.request.*;
import com.sankuai.scrm.core.service.activity.fission.validation.exception.FissionValidatorException;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FissionCommonValidatorCommonCheckTest {

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @InjectMocks
    private FissionCommonValidator validator;

    private void invokeCommonCheck(GroupFissionActivityRequest request) throws Exception {
        try {
            Method method = FissionCommonValidator.class.getDeclaredMethod("commonCheck", GroupFissionActivityRequest.class);
            method.setAccessible(true);
            method.invoke(validator, request);
        } catch (InvocationTargetException e) {
            // Unwrap the actual exception
            throw (Exception) e.getTargetException();
        }
    }

    private GroupFissionActivityRequest createValidRequest() {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setAppId("validAppId");
        ActivityBaseInfoRequest baseInfo = new ActivityBaseInfoRequest();
        baseInfo.setActivityName("Test Activity");
        baseInfo.setActivityInnerName("Test Inner Name");
        baseInfo.setActivityHeadImg("head.jpg");
        baseInfo.setStartTime(System.currentTimeMillis());
        baseInfo.setEndTime(System.currentTimeMillis() + 10000);
        baseInfo.setRule("Test Rule");
        baseInfo.setBackgroundImg("bg.jpg");
        request.setActivityBaseInfo(baseInfo);
        ShareCardInfo shareCard = new ShareCardInfo();
        shareCard.setCardTitle("Share Title");
        shareCard.setCardImg("share.jpg");
        request.setShareCardInfo(shareCard);
        RewardInfoRequest reward = new RewardInfoRequest();
        reward.setReceiveType(1);
        reward.setRewardType(1);
        reward.setPriceName("Test Prize");
        request.setRewardInfo(Collections.singletonList(reward));
        return request;
    }

    @Test
    void testCommonCheckNullRequest() throws Throwable {
        assertThrows(FissionValidatorException.class, () -> invokeCommonCheck(null), "Should throw exception for null request");
    }

    @Test
    void testCommonCheckNullActivityBaseInfo() throws Throwable {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityBaseInfo(null);
        assertThrows(FissionValidatorException.class, () -> invokeCommonCheck(request), "Should throw exception for null activityBaseInfo");
    }

    @Test
    void testCommonCheckInvalidAppId() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.setAppId(null);
        when(corpAppConfigRepository.getConfigByAppId(null)).thenReturn(null);
        assertThrows(FissionValidatorException.class, () -> invokeCommonCheck(request), "Should throw exception for invalid appId");
    }

    @Test
    void testCommonCheckMissingActivityName() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.getActivityBaseInfo().setActivityName(null);
        assertThrows(FissionValidatorException.class, () -> invokeCommonCheck(request), "Should throw exception for missing activity name");
    }

    @Test
    void testCommonCheckMissingActivityInnerName() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.getActivityBaseInfo().setActivityInnerName(null);
        assertThrows(FissionValidatorException.class, () -> invokeCommonCheck(request), "Should throw exception for missing activity inner name");
    }

    @Test
    void testCommonCheckMissingTime() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.getActivityBaseInfo().setStartTime(null);
        assertThrows(FissionValidatorException.class, () -> invokeCommonCheck(request), "Should throw exception for missing start time");
    }

    @Test
    void testCommonCheckMissingRule() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.getActivityBaseInfo().setRule(null);
        assertThrows(FissionValidatorException.class, () -> invokeCommonCheck(request), "Should throw exception for missing rule");
    }

    @Test
    void testCommonCheckMissingHeadImage() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.getActivityBaseInfo().setActivityHeadImg(null);
        assertThrows(FissionValidatorException.class, () -> invokeCommonCheck(request), "Should throw exception for missing head image");
    }

    @Test
    void testCommonCheckInvalidShareCardInfo() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.setShareCardInfo(new ShareCardInfo());
        assertThrows(FissionValidatorException.class, () -> invokeCommonCheck(request), "Should throw exception for invalid share card info");
    }

    @Test
    void testCommonCheckMissingBackgroundImg() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.getActivityBaseInfo().setBackgroundImg(null);
        assertThrows(FissionValidatorException.class, () -> invokeCommonCheck(request), "Should throw exception for missing background image");
    }

    @Test
    void testCommonCheckEmptyRewardInfo() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.setRewardInfo(Collections.emptyList());
        assertThrows(FissionValidatorException.class, () -> invokeCommonCheck(request), "Should throw exception for empty reward info");
    }

    @Test
    void testCommonCheckInvalidRewardItem() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.getRewardInfo().get(0).setReceiveType(null);
        assertThrows(FissionValidatorException.class, () -> invokeCommonCheck(request), "Should throw exception for invalid reward item");
    }

    @Test
    void testCommonCheckValidRequest() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        when(corpAppConfigRepository.getConfigByAppId("validAppId")).thenReturn(new CorpAppConfig());
        assertDoesNotThrow(() -> invokeCommonCheck(request), "Valid request should pass all checks");
    }
}
