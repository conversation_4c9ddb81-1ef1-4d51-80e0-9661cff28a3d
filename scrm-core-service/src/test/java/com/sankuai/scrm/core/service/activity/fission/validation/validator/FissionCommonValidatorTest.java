package com.sankuai.scrm.core.service.activity.fission.validation.validator;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.activity.fission.request.ActivityBaseInfoRequest;
import com.sankuai.dz.srcm.activity.fission.request.GroupFissionActivityRequest;
import com.sankuai.dz.srcm.activity.fission.request.PosterInfoRequest;
import com.sankuai.dz.srcm.activity.fission.request.RewardInfoRequest;
import com.sankuai.dz.srcm.activity.fission.request.ShareCardInfo;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionActivity;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionActivityMapper;
import com.sankuai.scrm.core.service.activity.fission.validation.enums.FissionChainOperationEnum;
import com.sankuai.scrm.core.service.activity.fission.validation.exception.FissionValidatorException;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.mapper.GroupDynamicCodeChannelConfigMapper;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.mapper.GroupDynamicCodeChannelMapper;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeChannel;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeChannelConfig;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FissionCommonValidatorTest {

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private GroupFissionActivityMapper groupFissionActivityMapper;

    @Mock
    private GroupDynamicCodeChannelConfigMapper codeChannelConfigMapper;

    @Mock
    private GroupDynamicCodeChannelMapper groupDynamicCodeChannelMapper;

    @InjectMocks
    private FissionCommonValidator validator;

    private GroupFissionActivityRequest createValidRequest() {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setAppId("validAppId");
        ActivityBaseInfoRequest baseInfo = new ActivityBaseInfoRequest();
        baseInfo.setActivityName("Test Activity");
        baseInfo.setActivityInnerName("Test Inner Name");
        baseInfo.setActivityHeadImg("headImg.jpg");
        baseInfo.setStartTime(System.currentTimeMillis() + 10000);
        baseInfo.setEndTime(System.currentTimeMillis() + 20000);
        baseInfo.setRule("Test Rule");
        baseInfo.setBackgroundImg("bg.jpg");
        request.setActivityBaseInfo(baseInfo);
        // Assuming ShareCardInfo and PosterInfoRequest are correctly imported and used
        ShareCardInfo shareCardInfo = new ShareCardInfo();
        shareCardInfo.setCardTitle("Test Title");
        shareCardInfo.setCardImg("card.jpg");
        request.setShareCardInfo(shareCardInfo);
        // Assuming PosterInfoRequest is correctly imported and used
        PosterInfoRequest posterInfo = new PosterInfoRequest();
        posterInfo.setId(1L);
        request.setPosterInfo(posterInfo);
        List<RewardInfoRequest> rewards = new ArrayList<>();
        RewardInfoRequest reward = new RewardInfoRequest();
        reward.setReceiveType(1);
        reward.setRewardType(1);
        reward.setPriceName("Test Reward");
        rewards.add(reward);
        request.setRewardInfo(rewards);
        return request;
    }

    @Test
    public void testValidateNullRequest() throws Throwable {
        assertThrows(FissionValidatorException.class, () -> validator.validate(null, FissionChainOperationEnum.INSERT.getOperation()));
    }

    @Test
    public void testValidateInvalidAppId() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.setAppId(null);
        assertThrows(FissionValidatorException.class, () -> validator.validate(request, FissionChainOperationEnum.INSERT.getOperation()));
    }

    @Test
    public void testValidateInsertWithInvalidTimeRange() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.getActivityBaseInfo().setStartTime(System.currentTimeMillis() + 10000);
        request.getActivityBaseInfo().setEndTime(System.currentTimeMillis() + 5000);
        assertThrows(FissionValidatorException.class, () -> validator.validate(request, FissionChainOperationEnum.INSERT.getOperation()));
    }

    @Test
    public void testValidateInsertWithStartTimeBeforeNow() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.getActivityBaseInfo().setStartTime(System.currentTimeMillis() - 10000);
        request.getActivityBaseInfo().setEndTime(System.currentTimeMillis() + 5000);
        assertThrows(FissionValidatorException.class, () -> validator.validate(request, FissionChainOperationEnum.INSERT.getOperation()));
    }

    @Test
    public void testValidateValidInsertOperation() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        when(corpAppConfigRepository.getConfigByAppId(any())).thenReturn(new CorpAppConfig());
        assertDoesNotThrow(() -> validator.validate(request, FissionChainOperationEnum.INSERT.getOperation()));
    }

    @Test
    public void testValidateUpdateNonExistentActivity() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.setActivityId(1L);
        assertThrows(FissionValidatorException.class, () -> validator.validate(request, FissionChainOperationEnum.UPDATE.getOperation()));
    }

    @Test
    public void testValidateUpdateWithNonExistentPoster() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.setActivityId(1L);
        GroupFissionActivity activity = new GroupFissionActivity();
        assertThrows(FissionValidatorException.class, () -> validator.validate(request, FissionChainOperationEnum.UPDATE.getOperation()));
    }

    @Test
    public void testValidateUpdateWithNonExistentChannel() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.setActivityId(1L);
        GroupFissionActivity activity = new GroupFissionActivity();
        activity.setChannelId(1L);
        assertThrows(FissionValidatorException.class, () -> validator.validate(request, FissionChainOperationEnum.UPDATE.getOperation()));
    }

    @Test
    public void testValidateUpdateWithChangedAppId() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.setActivityId(1L);
        request.setAppId("newAppId");
        GroupFissionActivity activity = new GroupFissionActivity();
        activity.setAppId("oldAppId");
        activity.setChannelId(1L);
        assertThrows(FissionValidatorException.class, () -> validator.validate(request, FissionChainOperationEnum.UPDATE.getOperation()));
    }

    @Test
    public void testValidateUpdateNotStartedActivityWithInvalidTimeRange() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.setActivityId(1L);
        request.getActivityBaseInfo().setStartTime(System.currentTimeMillis() + 20000);
        request.getActivityBaseInfo().setEndTime(System.currentTimeMillis() + 10000);
        GroupFissionActivity activity = new GroupFissionActivity();
        activity.setAppId(request.getAppId());
        activity.setChannelId(1L);
        // future start time
        activity.setStartTime(new Date(System.currentTimeMillis() + 30000));
        assertThrows(FissionValidatorException.class, () -> validator.validate(request, FissionChainOperationEnum.UPDATE.getOperation()));
    }

    @Test
    public void testValidateValidUpdateForNotStartedActivity() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.setActivityId(1L);
        request.getActivityBaseInfo().setStartTime(System.currentTimeMillis() + 10000);
        request.getActivityBaseInfo().setEndTime(System.currentTimeMillis() + 20000);
        GroupFissionActivity activity = new GroupFissionActivity();
        activity.setAppId(request.getAppId());
        activity.setChannelId(1L);
        // future start time
        activity.setStartTime(new Date(System.currentTimeMillis() + 30000));
        when(groupFissionActivityMapper.selectByPrimaryKey(1L)).thenReturn(activity);
        when(codeChannelConfigMapper.selectByPrimaryKey(any())).thenReturn(new GroupDynamicCodeChannelConfig());
        when(groupDynamicCodeChannelMapper.selectByPrimaryKey(1L)).thenReturn(new GroupDynamicCodeChannel());
        when(corpAppConfigRepository.getConfigByAppId(any())).thenReturn(new CorpAppConfig());
        assertDoesNotThrow(() -> validator.validate(request, FissionChainOperationEnum.UPDATE.getOperation()));
    }

    @Test
    public void testValidateUpdateStartedActivityWithModifiedStartTime() throws Throwable {
        GroupFissionActivityRequest request = createValidRequest();
        request.setActivityId(1L);
        // modified to past time
        request.getActivityBaseInfo().setStartTime(System.currentTimeMillis() - 5000);
        GroupFissionActivity activity = new GroupFissionActivity();
        activity.setAppId(request.getAppId());
        activity.setChannelId(1L);
        // already started
        activity.setStartTime(new Date(System.currentTimeMillis() - 10000));
        assertThrows(FissionValidatorException.class, () -> validator.validate(request, FissionChainOperationEnum.UPDATE.getOperation()));
    }
}
