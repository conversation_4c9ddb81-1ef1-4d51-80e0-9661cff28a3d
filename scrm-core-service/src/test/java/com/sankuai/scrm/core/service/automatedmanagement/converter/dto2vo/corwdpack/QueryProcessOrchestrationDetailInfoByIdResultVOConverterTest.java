package com.sankuai.scrm.core.service.automatedmanagement.converter.dto2vo.corwdpack;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.response.QueryProcessOrchestrationDetailInfoByIdResultVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationInfoVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationNodeVO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationGoalVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmCrowdPackUpdateStrategyInfoVO;
import static org.junit.Assert.*;
import org.junit.*;
import java.util.List;

@RunWith(MockitoJUnitRunner.class)
public class QueryProcessOrchestrationDetailInfoByIdResultVOConverterTest {

    @InjectMocks
    private QueryProcessOrchestrationDetailInfoByIdResultVOConverter converter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationInfoVOConverter scrmProcessOrchestrationInfoVOConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationNodeVOConverter scrmProcessOrchestrationNodeVOConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationGoalVOConverter scrmProcessOrchestrationGoalVOConverter;

    @Mock(lenient = true)
    private ScrmCrowdPackUpdateStrategyInfoVOConverter scrmCrowdPackUpdateStrategyInfoVOConverter;

    private ScrmProcessOrchestrationDTO resource;

    private QueryProcessOrchestrationDetailInfoByIdResultVO result;

    @Before
    public void setUp() {
        resource = new ScrmProcessOrchestrationDTO();
        result = new QueryProcessOrchestrationDetailInfoByIdResultVO();
    }

    @Test
    public void testConvertToDONullResource() throws Throwable {
        ScrmProcessOrchestrationDTO nullResource = null;
        QueryProcessOrchestrationDetailInfoByIdResultVO result = converter.convertToDO(nullResource);
        assertNull(result);
    }

    @Test
    public void testConvertToDONullNodeMediumDTO() throws Throwable {
        resource.setNodeMediumDTO(null);
        // Initialize crowdPackType to avoid NullPointerException
        resource.setCrowdPackType(1);
        when(scrmProcessOrchestrationInfoVOConverter.convertToDO(resource)).thenReturn(new ScrmProcessOrchestrationInfoVO());
        QueryProcessOrchestrationDetailInfoByIdResultVO result = converter.convertToDO(resource);
        assertNotNull(result);
        verify(scrmProcessOrchestrationInfoVOConverter, times(1)).convertToDO(resource);
        verify(scrmProcessOrchestrationNodeVOConverter, times(0)).convertToDO(any(ScrmProcessOrchestrationNodeMediumDTO.class));
    }

    @Test
    public void testConvertToDONonNullNodeMediumDTO() throws Throwable {
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        resource.setNodeMediumDTO(nodeMediumDTO);
        // Initialize crowdPackType to avoid NullPointerException
        resource.setCrowdPackType(1);
        when(scrmProcessOrchestrationInfoVOConverter.convertToDO(resource)).thenReturn(new ScrmProcessOrchestrationInfoVO());
        when(scrmProcessOrchestrationNodeVOConverter.convertToDO(nodeMediumDTO)).thenReturn(new ArrayList<>());
        QueryProcessOrchestrationDetailInfoByIdResultVO result = converter.convertToDO(resource);
        assertNotNull(result);
        verify(scrmProcessOrchestrationInfoVOConverter, times(1)).convertToDO(resource);
        verify(scrmProcessOrchestrationNodeVOConverter, times(1)).convertToDO(nodeMediumDTO);
    }

    @Test
    public void testConvertToDOCrowdPackTypeNotTwo() throws Throwable {
        // Initialize crowdPackType to avoid NullPointerException
        resource.setCrowdPackType(1);
        when(scrmProcessOrchestrationInfoVOConverter.convertToDO(resource)).thenReturn(new ScrmProcessOrchestrationInfoVO());
        QueryProcessOrchestrationDetailInfoByIdResultVO result = converter.convertToDO(resource);
        assertNotNull(result);
        verify(scrmProcessOrchestrationInfoVOConverter, times(1)).convertToDO(resource);
        verify(scrmCrowdPackUpdateStrategyInfoVOConverter, times(0)).convertToDO(any());
    }

    @Test
    public void testConvertToDOCrowdPackTypeTwo() throws Throwable {
        // Initialize crowdPackType to avoid NullPointerException
        resource.setCrowdPackType(2);
        when(scrmProcessOrchestrationInfoVOConverter.convertToDO(resource)).thenReturn(new ScrmProcessOrchestrationInfoVO());
        when(scrmCrowdPackUpdateStrategyInfoVOConverter.convertToDO(resource.getCrowdPackUpdateStrategyInfoDTO())).thenReturn(new ScrmCrowdPackUpdateStrategyInfoVO());
        QueryProcessOrchestrationDetailInfoByIdResultVO result = converter.convertToDO(resource);
        assertNotNull(result);
        verify(scrmProcessOrchestrationInfoVOConverter, times(1)).convertToDO(resource);
        verify(scrmCrowdPackUpdateStrategyInfoVOConverter, times(1)).convertToDO(resource.getCrowdPackUpdateStrategyInfoDTO());
    }

    @Test
    public void testConvertToDOGoalDTONullNegativeGoalDTO() throws Throwable {
        resource.setGoalDTO(new com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationGoalDTO());
        resource.setNegativeGoalDTO(null);
        // Initialize crowdPackType to avoid NullPointerException
        resource.setCrowdPackType(1);
        when(scrmProcessOrchestrationInfoVOConverter.convertToDO(resource)).thenReturn(new ScrmProcessOrchestrationInfoVO());
        QueryProcessOrchestrationDetailInfoByIdResultVO result = converter.convertToDO(resource);
        assertNotNull(result);
        verify(scrmProcessOrchestrationInfoVOConverter, times(1)).convertToDO(resource);
        verify(scrmProcessOrchestrationGoalVOConverter, times(1)).convertToDO(anyList());
    }

    @Test
    public void testConvertToDONullGoalDTONonNullNegativeGoalDTO() throws Throwable {
        resource.setGoalDTO(null);
        resource.setNegativeGoalDTO(new com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationGoalDTO());
        // Initialize crowdPackType to avoid NullPointerException
        resource.setCrowdPackType(1);
        when(scrmProcessOrchestrationInfoVOConverter.convertToDO(resource)).thenReturn(new ScrmProcessOrchestrationInfoVO());
        QueryProcessOrchestrationDetailInfoByIdResultVO result = converter.convertToDO(resource);
        assertNotNull(result);
        verify(scrmProcessOrchestrationInfoVOConverter, times(1)).convertToDO(resource);
        verify(scrmProcessOrchestrationGoalVOConverter, times(1)).convertToDO(anyList());
    }

    @Test
    public void testConvertToDONonNullGoalDTONonNullNegativeGoalDTO() throws Throwable {
        resource.setGoalDTO(new com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationGoalDTO());
        resource.setNegativeGoalDTO(new com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationGoalDTO());
        // Initialize crowdPackType to avoid NullPointerException
        resource.setCrowdPackType(1);
        when(scrmProcessOrchestrationInfoVOConverter.convertToDO(resource)).thenReturn(new ScrmProcessOrchestrationInfoVO());
        QueryProcessOrchestrationDetailInfoByIdResultVO result = converter.convertToDO(resource);
        assertNotNull(result);
        verify(scrmProcessOrchestrationInfoVOConverter, times(1)).convertToDO(resource);
        verify(scrmProcessOrchestrationGoalVOConverter, times(1)).convertToDO(anyList());
    }
}
