package com.sankuai.scrm.core.service.infrastructure.acl;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class StoreCacheAclTest {

    @InjectMocks
    private StoreCacheAcl storeCacheAcl;

    @Mock(lenient = true)
    private RedisStoreClient redisStoreClient;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 getFromCache 方法在正常情况下的行为
     */
    @Test
    public void testGetFromCacheNormal() throws Throwable {
        // arrange
        StoreKey storeKey = new StoreKey("testCategory", "param1", "param2");
        Object expectedValue = new Object();
        when(redisStoreClient.get(storeKey)).thenReturn(expectedValue);
        // act
        Object actualValue = storeCacheAcl.getFromCache(storeKey);
        // assert
        assertEquals(expectedValue, actualValue);
        verify(redisStoreClient, times(1)).get(storeKey);
    }

    /**
     * 测试 getFromCache 方法在异常情况下的行为
     */
    @Test
    public void testGetFromCacheException() throws Throwable {
        // arrange
        StoreKey storeKey = new StoreKey("testCategory", "param1", "param2");
        when(redisStoreClient.get(storeKey)).thenThrow(new RuntimeException());
        // act
        Object actualValue = storeCacheAcl.getFromCache(storeKey);
        // assert
        assertNull(actualValue);
        verify(redisStoreClient, times(1)).get(storeKey);
    }
}
