package com.sankuai.scrm.core.service.automatedmanagement.domainservice.activitypage;

import com.sankuai.dz.srcm.automatedmanagement.dto.activitypage.ActivityPageUpdatePagesDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductItemsDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductActivityPageDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductItemsDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationProductTagMapDOMapper;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class ActivityPageManagementService_UpdateActivityPageTest {

    @InjectMocks
    private ActivityPageManagementService activityPageManagementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductItemsDOMapper productItemsDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductActivityPageDOMapper activityPageDOMapper;

    @Mock(lenient = true)
    private ExtScrmAmProcessOrchestrationProductTagMapDOMapper extScrmAmProcessOrchestrationProductTagMapDOMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testUpdateActivityPageValidInput() throws Throwable {
        String appId = "testAppId";
        ActivityPageUpdatePagesDTO updatePagesDTO = new ActivityPageUpdatePagesDTO();
        updatePagesDTO.setActivityPageId(1L);
        updatePagesDTO.setActivityPageTitle("New Title");
        updatePagesDTO.setProductIds(Arrays.asList(1L, 2L));
        // Mocking the behavior to return a non-null list of ScrmAmProcessOrchestrationProductActivityPageDO objects
        // with non-null tagIds
        ScrmAmProcessOrchestrationProductActivityPageDO activityPageDO = new ScrmAmProcessOrchestrationProductActivityPageDO();
        activityPageDO.setTagId(1L);
        when(activityPageDOMapper.selectByExample(any())).thenReturn(Arrays.asList(activityPageDO));
        when(activityPageDOMapper.countByExample(any())).thenReturn(0L);
        when(activityPageDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Arrays.asList(new ScrmAmProcessOrchestrationProductItemsDO(), new ScrmAmProcessOrchestrationProductItemsDO()));
        when(extScrmAmProcessOrchestrationProductTagMapDOMapper.insertSelective(any())).thenReturn(1);
        activityPageManagementService.updateActivityPage(appId, updatePagesDTO);
        verify(activityPageDOMapper, times(1)).updateByExampleSelective(any(), any());
        verify(extScrmAmProcessOrchestrationProductTagMapDOMapper, times(1)).deleteByExample(any());
        verify(extScrmAmProcessOrchestrationProductTagMapDOMapper, times(2)).insertSelective(any());
    }

    // Other test cases remain unchanged
    @Test(expected = Exception.class)
    public void testUpdateActivityPageNullAppId() throws Throwable {
        ActivityPageUpdatePagesDTO updatePagesDTO = new ActivityPageUpdatePagesDTO();
        updatePagesDTO.setActivityPageId(1L);
        activityPageManagementService.updateActivityPage(null, updatePagesDTO);
    }

    @Test(expected = Exception.class)
    public void testUpdateActivityPageNullUpdatePagesDTO() throws Throwable {
        activityPageManagementService.updateActivityPage("testAppId", null);
    }

    @Test(expected = Exception.class)
    public void testUpdateActivityPageNullActivityPageId() throws Throwable {
        ActivityPageUpdatePagesDTO updatePagesDTO = new ActivityPageUpdatePagesDTO();
        activityPageManagementService.updateActivityPage("testAppId", updatePagesDTO);
    }

    @Test(expected = Exception.class)
    public void testUpdateActivityPageNullActivityPageTitle() throws Throwable {
        ActivityPageUpdatePagesDTO updatePagesDTO = new ActivityPageUpdatePagesDTO();
        updatePagesDTO.setActivityPageId(1L);
        activityPageManagementService.updateActivityPage("testAppId", updatePagesDTO);
    }

    @Test(expected = Exception.class)
    public void testUpdateActivityPageDuplicateTitle() throws Throwable {
        String appId = "testAppId";
        ActivityPageUpdatePagesDTO updatePagesDTO = new ActivityPageUpdatePagesDTO();
        updatePagesDTO.setActivityPageId(1L);
        updatePagesDTO.setActivityPageTitle("Existing Title");
        when(activityPageDOMapper.countByExample(any())).thenReturn(1L);
        activityPageManagementService.updateActivityPage(appId, updatePagesDTO);
    }

    @Test(expected = Exception.class)
    public void testUpdateActivityPageFailedUpdate() throws Throwable {
        String appId = "testAppId";
        ActivityPageUpdatePagesDTO updatePagesDTO = new ActivityPageUpdatePagesDTO();
        updatePagesDTO.setActivityPageId(1L);
        updatePagesDTO.setActivityPageTitle("New Title");
        when(activityPageDOMapper.countByExample(any())).thenReturn(0L);
        when(activityPageDOMapper.updateByExampleSelective(any(), any())).thenReturn(0);
        activityPageManagementService.updateActivityPage(appId, updatePagesDTO);
    }

    @Test(expected = Exception.class)
    public void testUpdateActivityPageFailedProductTagUpdate() throws Throwable {
        String appId = "testAppId";
        ActivityPageUpdatePagesDTO updatePagesDTO = new ActivityPageUpdatePagesDTO();
        updatePagesDTO.setActivityPageId(1L);
        updatePagesDTO.setActivityPageTitle("New Title");
        updatePagesDTO.setProductIds(Collections.singletonList(1L));
        when(activityPageDOMapper.countByExample(any())).thenReturn(0L);
        when(activityPageDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(new ScrmAmProcessOrchestrationProductItemsDO()));
        when(extScrmAmProcessOrchestrationProductTagMapDOMapper.insertSelective(any())).thenReturn(0);
        activityPageManagementService.updateActivityPage(appId, updatePagesDTO);
    }
}
