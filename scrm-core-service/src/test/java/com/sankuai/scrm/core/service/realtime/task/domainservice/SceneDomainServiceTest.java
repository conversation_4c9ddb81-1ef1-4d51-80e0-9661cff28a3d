package com.sankuai.scrm.core.service.realtime.task.domainservice;

import com.dianping.lion.Environment;
import com.dianping.unified.coupon.issue.api.dto.UnifiedCouponIssueDetail;
import com.dianping.unified.coupon.issue.api.dto.UnifiedCouponIssueResult;
import com.dianping.unified.coupon.issue.api.response.UnifiedCouponIssueResponse;
import com.meituan.service.mobile.group.geo.service.CityService;
import com.sankuai.dz.srcm.automatedmanagement.service.ScrmRefinementOperationBackEndService;
import com.sankuai.dz.srcm.couponIntegration.dto.AddCouponResopnse;
import com.sankuai.dz.srcm.couponIntegration.enums.FunctionModuleEnum;
import com.sankuai.dz.srcm.realtime.task.dto.ExtendFieldsDTO;
import com.sankuai.dz.srcm.realtime.task.dto.PersonaSceneDTO;
import com.sankuai.dz.srcm.realtime.task.dto.RealtimePerceptionMessage;
import com.sankuai.dz.srcm.realtime.task.dto.SceneSendCouponResponse;
import com.sankuai.dz.srcm.realtime.task.request.SceneAddRequest;
import com.sankuai.dz.srcm.realtime.task.request.SceneQueryRequest;
import com.sankuai.dz.srcm.realtime.task.response.SceneAddResultVO;
import com.sankuai.dz.srcm.realtime.task.response.SceneQueryResultVO;
import com.sankuai.scrm.core.service.activity.miniprogram.domain.CouponDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmSceneProcessPriorityDOMapper;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.couponIntegration.domain.CouponDashBoardDomainService;
import com.sankuai.scrm.core.service.couponIntegration.utils.CouponIntegrationUtil;
import com.sankuai.scrm.core.service.external.contact.dal.babymapper.ContactUserMapper;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmRealtimeSceneDO;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmRealtimeSceneUserRecordDO;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmRealtimeSceneDOMapper;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmRealtimeSceneUserRecordDOMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class SceneDomainServiceTest {

    @InjectMocks
    private SceneDomainService sceneDomainService;

    @Mock
    private ScrmRealtimeSceneDOMapper scrmRealtimeSceneDOMapper;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private ScrmRefinementOperationBackEndService scrmRefinementOperationBackEndService;

    @Mock
    private ContactUserMapper contactUserMapper;

    @Mock
    private ScrmRealtimeSceneUserRecordDOMapper scrmRealtimeSceneUserRecordDOMapper;

    @Mock
    private ScrmAmSceneProcessPriorityDOMapper sceneProcessPriorityDOMapper;

    @Mock
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordDOMapper;

    @Mock
    private CouponDomainService couponDomainService;

    @Mock
    private CouponDashBoardDomainService couponDashBoardDomainService;

    @Mock(lenient = true)
    private CouponIntegrationUtil couponIntegrationUtil;

    @Mock
    private Environment environment;

    @Mock
    private CityService cityService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 handleRealtimePerceptionMessage 方法，当 message 为 null 时
     */
    @Test
    public void testHandleRealtimePerceptionMessageMessageIsNull() throws Throwable {
        // arrange
        // act
        sceneDomainService.handleRealtimePerceptionMessage(null);
        // assert
        verifyNoInteractions(scrmRealtimeSceneDOMapper, corpAppConfigRepository, mtUserCenterAclService, scrmRefinementOperationBackEndService, contactUserMapper);
    }

    /**
     * 测试 handleRealtimePerceptionMessage 方法，当 message 中 sceneId 为 null 时
     */
    @Test
    public void testHandleRealtimePerceptionMessageSceneIdIsNull() throws Throwable {
        // arrange
        RealtimePerceptionMessage message = new RealtimePerceptionMessage();
        // act
        sceneDomainService.handleRealtimePerceptionMessage(message);
        // assert
        verifyNoInteractions(scrmRealtimeSceneDOMapper, corpAppConfigRepository, mtUserCenterAclService, scrmRefinementOperationBackEndService, contactUserMapper);
    }

    /**
     * 测试 handleRealtimePerceptionMessage 方法，当 sceneId 无效时
     */
    @Test
    public void testHandleRealtimePerceptionMessageInvalidSceneId() throws Throwable {
        // arrange
        RealtimePerceptionMessage message = new RealtimePerceptionMessage();
        message.setSceneId(1);
        //        when(scrmRealtimeSceneDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        // act
        sceneDomainService.handleRealtimePerceptionMessage(message);
        // assert
        //        verify(scrmRealtimeSceneDOMapper).selectByExample(any());
        verifyNoMoreInteractions(scrmRealtimeSceneDOMapper, corpAppConfigRepository, mtUserCenterAclService, scrmRefinementOperationBackEndService, contactUserMapper);
    }

    /**
     * 测试 handleRealtimePerceptionMessage 方法，当用户不是我们的客户时
     */
    @Test(expected = Exception.class)
    public void testHandleRealtimePerceptionMessageUserIsNotOurCustomer() throws Throwable {
        // arrange
        RealtimePerceptionMessage message = new RealtimePerceptionMessage();
        message.setSceneId(1);
        message.setUserId(1L);
        ExtendFieldsDTO extendFieldsDTO = new ExtendFieldsDTO();
        message.setExtendFields(extendFieldsDTO);
        List<ScrmRealtimeSceneDO> sceneDOs = new ArrayList<>();
        sceneDOs.add(new ScrmRealtimeSceneDO());
        when(scrmRealtimeSceneDOMapper.selectByExample(any())).thenReturn(sceneDOs);
        when(mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(anyLong(), anyString())).thenReturn("unionId");
        //        when(corpAppConfigRepository.getConfigByAppId(anyString())).thenReturn(new CorpAppConfig());
        //        when(contactUserMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        // act
        sceneDomainService.handleRealtimePerceptionMessage(message);
        // assert
        verify(scrmRealtimeSceneDOMapper).selectByExample(any());
        verify(mtUserCenterAclService).getUnionIdByUserIdFromMtUserCenter(anyLong(), anyString());
        verify(corpAppConfigRepository).getConfigByAppId(anyString());
        verify(contactUserMapper).selectByExample(any());
        verify(scrmRealtimeSceneUserRecordDOMapper).insertSelective(any(ScrmRealtimeSceneUserRecordDO.class));
    }

    /**
     * 测试pageQuerySceneList方法，当pageSize为null时
     */
    @Test
    public void testPageQuerySceneListPageSizeIsNull() throws Throwable {
        // arrange
        SceneQueryRequest request = new SceneQueryRequest();
        request.setPageSize(null);
        // act
        List<SceneQueryResultVO> result = sceneDomainService.pageQuerySceneList(request);
        // assert
        assertNull(result);
    }

    /**
     * 测试pageQuerySceneList方法，正常情况
     */
    @Test
    public void testPageQuerySceneListNormal() throws Throwable {
        // arrange
        SceneQueryRequest request = new SceneQueryRequest();
        request.setPageSize(10);
        request.setPageNum(1);
        request.setAppId("testAppId");
        request.setSceneName("testSceneName");
        request.setOwner("testOwner");
        List<ScrmRealtimeSceneDO> scrmRealtimeSceneDOList = new ArrayList<>();
        ScrmRealtimeSceneDO scrmRealtimeSceneDO = new ScrmRealtimeSceneDO();
        scrmRealtimeSceneDO.setId(1L);
        scrmRealtimeSceneDO.setScenename("testSceneName");
        scrmRealtimeSceneDO.setScenedesc("testSceneDesc");
        scrmRealtimeSceneDO.setScenetype(1);
        scrmRealtimeSceneDO.setOwner("testOwner");
        scrmRealtimeSceneDO.setSceneid(100);
        scrmRealtimeSceneDO.setUpdateTime(new Date());
        scrmRealtimeSceneDOList.add(scrmRealtimeSceneDO);
        when(scrmRealtimeSceneDOMapper.selectByExample(any())).thenReturn(scrmRealtimeSceneDOList);
        when(scrmRealtimeSceneUserRecordDOMapper.countByExample(any())).thenReturn(5L);
        // act
        List<SceneQueryResultVO> result = sceneDomainService.pageQuerySceneList(request);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        SceneQueryResultVO resultVO = result.get(0);
        assertEquals("testSceneName", resultVO.getSceneName());
        assertEquals(Long.valueOf(5), resultVO.getCurrentCustomerCount());
        assertEquals("testSceneDesc", resultVO.getSceneDesc());
        assertEquals("testOwner", resultVO.getOwner());
        assertEquals(new SimpleDateFormat("yyyy-MM-dd HH:mm:ss").format(scrmRealtimeSceneDO.getUpdateTime()), resultVO.getCurrentEffectiveVersion());
    }

    /**
     * 测试pageQuerySceneList方法，当appId为空时
     */
    @Test
    public void testPageQuerySceneListAppIdIsEmpty() throws Throwable {
        // arrange
        SceneQueryRequest request = new SceneQueryRequest();
        request.setPageSize(10);
        request.setPageNum(1);
        request.setAppId("");
        // act
        List<SceneQueryResultVO> result = sceneDomainService.pageQuerySceneList(request);
        // assert
        assertNull(result);
    }

    /**
     * 测试pageQuerySceneList方法，当pageNum为null时
     */
    @Test
    public void testPageQuerySceneListPageNumIsNull() throws Throwable {
        // arrange
        SceneQueryRequest request = new SceneQueryRequest();
        request.setPageSize(10);
        request.setPageNum(null);
        // act
        List<SceneQueryResultVO> result = sceneDomainService.pageQuerySceneList(request);
        // assert
        assertNull(result);
    }

    // 更多测试用例可以根据方法的不同逻辑路径继续添加
    /**
     * 测试pageQuerySceneList方法，当pageNum和pageSize都为负数时
     */
    @Test
    public void testPageQuerySceneListPageNumAndPageSizeAreNegative() throws Throwable {
        // arrange
        SceneQueryRequest request = new SceneQueryRequest();
        request.setPageSize(-1);
        request.setPageNum(-1);
        // act
        List<SceneQueryResultVO> result = sceneDomainService.pageQuerySceneList(request);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景发券成功
     */
    @Test
    public void testSceneSendCoupon_Success() {
        // arrange
        String unionId = "testUnionId";
        String couponId = "testCouponId";
        String appId = "testAppId";
        String distributorCode = "testDistributorCode";
        String sceneDetail = "testSceneDetail";
        Integer sceneType = 1;
        UnifiedCouponIssueResponse mockResponse = new UnifiedCouponIssueResponse();
        UnifiedCouponIssueResult mockResult = new UnifiedCouponIssueResult();
        List<UnifiedCouponIssueDetail> issueDetails = new ArrayList<>();
        mockResult.setResult(issueDetails);
        UnifiedCouponIssueDetail detail = new UnifiedCouponIssueDetail();
        detail.setUnifiedCouponId("couponId");
        detail.setDiscountAmount(new BigDecimal("10"));
        detail.setPriceLimit(new BigDecimal("100"));
        issueDetails.add(detail);
        mockResponse.setSuccess(true);
        mockResponse.setResult(mockResult);
        //mockResponse.setResult(new UnifiedCouponIssueResult(issueDetails, 0, 1));
        when(couponDomainService.sendMtCouponAndResponse(any(Long.class), any(String.class), any(),anyInt())).thenReturn(mockResponse);
        when(couponIntegrationUtil.preValidateAndAddCouponList(anyList(), anyString(), any(), any(), any(), any())).thenReturn(new AddCouponResopnse());
        when(couponDashBoardDomainService.getStatisticStatus(anyString(), anyString(), anyString(), anyInt())).thenReturn(1);
        // act
        SceneSendCouponResponse response = sceneDomainService.sceneSendCoupon(unionId, couponId, appId, distributorCode, sceneDetail, sceneType, FunctionModuleEnum.AUTOMATED_MARKETING, new Date());
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertFalse(response.getSuccessList().isEmpty());
    }

    /**
     * 测试场景发券失败，参数为空
     */
    @Test
    public void testSceneSendCoupon_FailDueToEmptyParameters() {
        // arrange
        String unionId = "";
        String couponId = "";
        String appId = "";
        String distributorCode = "";
        String sceneDetail = "";
        Integer sceneType = 1;
        // act
        SceneSendCouponResponse response = sceneDomainService.sceneSendCoupon(unionId, couponId, appId, distributorCode, sceneDetail, sceneType, FunctionModuleEnum.AUTOMATED_MARKETING, new Date());
        // assert
        assertNull(response);
    }

    /**
     * 测试场景发券失败，发券服务返回失败
     */
    @Test
    public void testSceneSendCoupon_FailDueToServiceFailure() {
        // arrange
        String unionId = "testUnionId";
        String couponId = "testCouponId";
        String appId = "testAppId";
        String distributorCode = "testDistributorCode";
        String sceneDetail = "testSceneDetail";
        Integer sceneType = 1;
        UnifiedCouponIssueResponse mockResponse = new UnifiedCouponIssueResponse();
        mockResponse.setSuccess(false);
        when(couponDomainService.sendMtCouponAndResponse(any(Long.class), any(String.class), any(), anyInt())).thenReturn(mockResponse);
        // act
        SceneSendCouponResponse response = sceneDomainService.sceneSendCoupon(unionId, couponId, appId, distributorCode, sceneDetail, sceneType, FunctionModuleEnum.AUTOMATED_MARKETING, new Date());
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
    }

    /**
     * 测试checkCity方法，当appId为空时应返回false
     */
    @Test
    public void testCheckCityWithEmptyAppId() {
        boolean result = sceneDomainService.checkCity("1", "");
        assertFalse(result);
    }

    /**
     * 测试checkCity方法，当cityId为空时应返回false
     */
    @Test
    public void testCheckCityWithNullCityId() {
        boolean result = sceneDomainService.checkCity(null, "APP_ID_XIUYU_ZXK");
        assertFalse(result);
    }

    @Test
    public void testCheckCity() {
        boolean result = sceneDomainService.checkCity("1", "zongfa");
        assertFalse(result);
    }

    @Test
    public void testAddSceneByRequest() {
        SceneAddRequest request = new SceneAddRequest();
        request.setAppId("test");
        request.setOwner("test");
        PersonaSceneDTO personaSceneDTO = new PersonaSceneDTO();
        personaSceneDTO.setSceneDesc("test");
        personaSceneDTO.setSceneId(123);
        request.setPersonaSceneDTO(personaSceneDTO);
        when(scrmRealtimeSceneDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        SceneAddResultVO vo = sceneDomainService.addSceneByRequest(request);
        assertNotNull(vo);
    }

    /**
     * Test case for null request
     */
    @Test
    public void testAddSceneByRequest_NullRequest() {
        // arrange
        SceneAddRequest request = null;
        // act
        SceneAddResultVO result = sceneDomainService.addSceneByRequest(request);
        // assert
        assertFalse(result.isSuccess());
    }

    /**
     * Test case for null personaSceneDTO
     */
    @Test
    public void testAddSceneByRequest_NullPersonaSceneDTO() {
        // arrange
        SceneAddRequest request = new SceneAddRequest();
        request.setAppId("testApp");
        request.setOwner("testOwner");
        request.setPersonaSceneDTO(null);
        // act
        SceneAddResultVO result = sceneDomainService.addSceneByRequest(request);
        // assert
        assertFalse(result.isSuccess());
    }

    /**
     * Test case for empty appId
     */
    @Test
    public void testAddSceneByRequest_EmptyAppId() {
        // arrange
        SceneAddRequest request = new SceneAddRequest();
        request.setAppId("");
        request.setOwner("testOwner");
        request.setPersonaSceneDTO(new PersonaSceneDTO());
        // act
        SceneAddResultVO result = sceneDomainService.addSceneByRequest(request);
        // assert
        assertFalse(result.isSuccess());
    }

    /**
     * Test case for empty owner
     */
    @Test
    public void testAddSceneByRequest_EmptyOwner() {
        // arrange
        SceneAddRequest request = new SceneAddRequest();
        request.setAppId("testApp");
        request.setOwner("");
        request.setPersonaSceneDTO(new PersonaSceneDTO());
        // act
        SceneAddResultVO result = sceneDomainService.addSceneByRequest(request);
        // assert
        assertFalse(result.isSuccess());
    }

    /**
     * Test case for null sceneId
     */
    @Test
    public void testAddSceneByRequest_NullSceneId() {
        // arrange
        SceneAddRequest request = new SceneAddRequest();
        request.setAppId("testApp");
        request.setOwner("testOwner");
        PersonaSceneDTO personaSceneDTO = new PersonaSceneDTO();
        personaSceneDTO.setSceneId(null);
        request.setPersonaSceneDTO(personaSceneDTO);
        // act
        SceneAddResultVO result = sceneDomainService.addSceneByRequest(request);
        // assert
        assertFalse(result.isSuccess());
    }

    /**
     * Test case for existing deleted scene
     */
    @Test
    public void testAddSceneByRequest_ExistingDeletedScene() {
        // arrange
        SceneAddRequest request = new SceneAddRequest();
        request.setAppId("testApp");
        request.setOwner("testOwner");
        PersonaSceneDTO personaSceneDTO = new PersonaSceneDTO();
        personaSceneDTO.setSceneId(1);
        request.setPersonaSceneDTO(personaSceneDTO);
        ScrmRealtimeSceneDO existingScene = new ScrmRealtimeSceneDO();
        existingScene.setIsDeleted("1");
        when(scrmRealtimeSceneDOMapper.selectByExample(any())).thenReturn(Arrays.asList(existingScene));
        when(scrmRealtimeSceneDOMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        // act
        SceneAddResultVO result = sceneDomainService.addSceneByRequest(request);
        // assert
        assertTrue(result.isSuccess());
        verify(scrmRealtimeSceneDOMapper).updateByPrimaryKeySelective(any());
    }

    /**
     * Test case for existing non-deleted scene
     */
    @Test
    public void testAddSceneByRequest_ExistingNonDeletedScene() {
        // arrange
        SceneAddRequest request = new SceneAddRequest();
        request.setAppId("testApp");
        request.setOwner("testOwner");
        PersonaSceneDTO personaSceneDTO = new PersonaSceneDTO();
        personaSceneDTO.setSceneId(1);
        request.setPersonaSceneDTO(personaSceneDTO);
        ScrmRealtimeSceneDO existingScene = new ScrmRealtimeSceneDO();
        existingScene.setIsDeleted("0");
        when(scrmRealtimeSceneDOMapper.selectByExample(any())).thenReturn(Arrays.asList(existingScene));
        // act
        SceneAddResultVO result = sceneDomainService.addSceneByRequest(request);
        // assert
        assertFalse(result.isSuccess());
    }

    /**
     * Test case for successful insert
     */
    @Test
    public void testAddSceneByRequest_SuccessfulInsert() {
        // arrange
        SceneAddRequest request = new SceneAddRequest();
        request.setAppId("testApp");
        request.setOwner("testOwner");
        PersonaSceneDTO personaSceneDTO = new PersonaSceneDTO();
        personaSceneDTO.setSceneId(1);
        request.setPersonaSceneDTO(personaSceneDTO);
        when(scrmRealtimeSceneDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(scrmRealtimeSceneDOMapper.insertSelective(any())).thenReturn(1);
        // act
        SceneAddResultVO result = sceneDomainService.addSceneByRequest(request);
        // assert
        assertTrue(result.isSuccess());
        verify(scrmRealtimeSceneDOMapper).insertSelective(any());
    }
}
