package com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.impl;

import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationGoalDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.request.CreateProcessOrchestrationRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.CreateProcessOrchestrationResultVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmCrowdPackUpdateStrategyInfoVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationGoalVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationInfoVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationNodeVO;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2vo.corwdpack.ScrmCrowdPackUpdateStrategyInfoVOConverter;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2vo.corwdpack.ScrmProcessOrchestrationGoalVOConverter;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2vo.corwdpack.ScrmProcessOrchestrationInfoVOConverter;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2vo.corwdpack.ScrmProcessOrchestrationNodeVOConverter;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.DdlResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.RefinementOperationExecuteMessageProducer;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ProcessOrchestrationInnerServiceImpl_CreateProcessOrchestrationTest {

    @InjectMocks
    private ProcessOrchestrationInnerServiceImpl processOrchestrationInnerService;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationInfoVOConverter processOrchestrationInfoVOConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationNodeVOConverter scrmProcessOrchestrationNodeVOConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationGoalVOConverter scrmProcessOrchestrationGoalVOConverter;

    @Mock(lenient = true)
    private ScrmCrowdPackUpdateStrategyInfoVOConverter scrmCrowdPackUpdateStrategyInfoVOConverter;

    @Mock(lenient = true)
    private ProcessOrchestrationWriteDomainService processOrchestrationWriteDomainService;

    @Mock(lenient = true)
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock(lenient = true)
    private RefinementOperationExecuteMessageProducer refinementOperationExecuteMessageProducer;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testCreateProcessOrchestrationNormalCase() throws Throwable {
        CreateProcessOrchestrationRequest request = new CreateProcessOrchestrationRequest();
        request.setAppId("testApp");
        request.setMisId("testMis");
        ScrmProcessOrchestrationInfoVO infoVO = new ScrmProcessOrchestrationInfoVO();
        infoVO.setName("Test Orchestration");
        request.setProcessOrchestrationInfo(infoVO);
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        when(processOrchestrationInfoVOConverter.convertToDTO(any())).thenReturn(dto);
        DdlResultDTO ddlResultDTO = new DdlResultDTO(true, "Success", 1L);
        when(processOrchestrationWriteDomainService.createProcessOrchestration(any())).thenReturn(ddlResultDTO);
        CreateProcessOrchestrationResultVO result = processOrchestrationInnerService.createProcessOrchestration(request);
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals(Long.valueOf(1L), result.getId());
        verify(processOrchestrationWriteDomainService).createProcessOrchestration(any());
    }

    @Test
    public void testCreateProcessOrchestrationWithDifferentNodeTypes() throws Throwable {
        CreateProcessOrchestrationRequest request = new CreateProcessOrchestrationRequest();
        request.setAppId("testApp");
        request.setMisId("testMis");
        ScrmProcessOrchestrationInfoVO infoVO = new ScrmProcessOrchestrationInfoVO();
        infoVO.setName("Test Orchestration");
        request.setProcessOrchestrationInfo(infoVO);
        ScrmProcessOrchestrationNodeVO conditionNode = new ScrmProcessOrchestrationNodeVO();
        conditionNode.setNodeType(1);
        ScrmProcessOrchestrationNodeVO actionNode = new ScrmProcessOrchestrationNodeVO();
        actionNode.setNodeType(2);
        ScrmProcessOrchestrationNodeVO autoActionNode = new ScrmProcessOrchestrationNodeVO();
        autoActionNode.setNodeType(3);
        request.setProcessOrchestrationNodeList(Arrays.asList(conditionNode, actionNode, autoActionNode));
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        when(processOrchestrationInfoVOConverter.convertToDTO(any())).thenReturn(dto);
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        when(scrmProcessOrchestrationNodeVOConverter.convertToDTO(any())).thenReturn(nodeMediumDTO);
        DdlResultDTO ddlResultDTO = new DdlResultDTO(true, "Success", 1L);
        when(processOrchestrationWriteDomainService.createProcessOrchestration(any())).thenReturn(ddlResultDTO);
        CreateProcessOrchestrationResultVO result = processOrchestrationInnerService.createProcessOrchestration(request);
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals(Long.valueOf(1L), result.getId());
        verify(scrmProcessOrchestrationNodeVOConverter).convertToDTO(any());
        verify(processOrchestrationWriteDomainService).createProcessOrchestration(any());
    }

    @Test
    public void testCreateProcessOrchestrationWithGoals() throws Throwable {
        CreateProcessOrchestrationRequest request = new CreateProcessOrchestrationRequest();
        request.setAppId("testApp");
        request.setMisId("testMis");
        ScrmProcessOrchestrationInfoVO infoVO = new ScrmProcessOrchestrationInfoVO();
        infoVO.setName("Test Orchestration");
        request.setProcessOrchestrationInfo(infoVO);
        ScrmProcessOrchestrationGoalVO goalVO = new ScrmProcessOrchestrationGoalVO();
        request.setProcessOrchestrationGoal(goalVO);
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        when(processOrchestrationInfoVOConverter.convertToDTO(any())).thenReturn(dto);
        ScrmProcessOrchestrationGoalDTO goalDTO = new ScrmProcessOrchestrationGoalDTO();
        goalDTO.setGoalType((byte) 1);
        when(scrmProcessOrchestrationGoalVOConverter.convertToDTO(any())).thenReturn(Arrays.asList(goalDTO));
        DdlResultDTO ddlResultDTO = new DdlResultDTO(true, "Success", 1L);
        when(processOrchestrationWriteDomainService.createProcessOrchestration(any())).thenReturn(ddlResultDTO);
        CreateProcessOrchestrationResultVO result = processOrchestrationInnerService.createProcessOrchestration(request);
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals(Long.valueOf(1L), result.getId());
        verify(scrmProcessOrchestrationGoalVOConverter).convertToDTO(any());
        verify(processOrchestrationWriteDomainService).createProcessOrchestration(any());
    }

    /*@Test(expected = Exception.class)
    public void testCreateProcessOrchestrationWithIllegalAccessException() throws Throwable {
        CreateProcessOrchestrationRequest request = new CreateProcessOrchestrationRequest();
        request.setAppId("testApp");
        request.setMisId("testMis");
        ScrmProcessOrchestrationInfoVO infoVO = new ScrmProcessOrchestrationInfoVO();
        infoVO.setName("Test Orchestration");
        request.setProcessOrchestrationInfo(infoVO);
        when(processOrchestrationInfoVOConverter.convertToDTO(any())).thenReturn(new ScrmProcessOrchestrationDTO());
        when(processOrchestrationWriteDomainService.createProcessOrchestration(any())).thenThrow(new IllegalAccessException("Test exception"));
        processOrchestrationInnerService.createProcessOrchestration(request);
    }*/

    @Test
    public void testCreateProcessOrchestrationWithExecutionPlan() throws Throwable {
        CreateProcessOrchestrationRequest request = new CreateProcessOrchestrationRequest();
        request.setAppId("testApp");
        request.setMisId("testMis");
        ScrmProcessOrchestrationInfoVO infoVO = new ScrmProcessOrchestrationInfoVO();
        infoVO.setName("Test Orchestration");
        infoVO.setBeginTime(DateUtil.formatYMdHms(new Date()));
        infoVO.setEndTime(DateUtil.formatYMdHms(new Date(System.currentTimeMillis() + 86400000)));
        infoVO.setCronComment("0 0 12 * * ?");
        request.setProcessOrchestrationInfo(infoVO);
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setBeginTime(new Date());
        dto.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        dto.setCronComment("0 0 12 * * ?");
        when(processOrchestrationInfoVOConverter.convertToDTO(any())).thenReturn(dto);
        DdlResultDTO ddlResultDTO = new DdlResultDTO(true, "Success", 1L);
        when(processOrchestrationWriteDomainService.createProcessOrchestration(any())).thenReturn(ddlResultDTO);
        CreateProcessOrchestrationResultVO result = processOrchestrationInnerService.createProcessOrchestration(request);
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals(Long.valueOf(1L), result.getId());
        verify(processOrchestrationWriteDomainService).createProcessOrchestration(any());
    }

    @Test
    public void testCreateProcessOrchestrationWithoutExecutionPlanFutureStart() throws Throwable {
        CreateProcessOrchestrationRequest request = new CreateProcessOrchestrationRequest();
        request.setAppId("testApp");
        request.setMisId("testMis");
        ScrmProcessOrchestrationInfoVO infoVO = new ScrmProcessOrchestrationInfoVO();
        infoVO.setName("Test Orchestration");
        infoVO.setBeginTime(DateUtil.formatYMdHms(new Date(System.currentTimeMillis() + 172800000)));
        infoVO.setEndTime(DateUtil.formatYMdHms(new Date(System.currentTimeMillis() + 259200000)));
        infoVO.setCronComment("0 0 12 * * ?");
        request.setProcessOrchestrationInfo(infoVO);
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setBeginTime(new Date(System.currentTimeMillis() + 172800000));
        dto.setEndTime(new Date(System.currentTimeMillis() + 259200000));
        dto.setCronComment("0 0 12 * * ?");
        when(processOrchestrationInfoVOConverter.convertToDTO(any())).thenReturn(dto);
        DdlResultDTO ddlResultDTO = new DdlResultDTO(true, "Success", 1L);
        when(processOrchestrationWriteDomainService.createProcessOrchestration(any())).thenReturn(ddlResultDTO);
        CreateProcessOrchestrationResultVO result = processOrchestrationInnerService.createProcessOrchestration(request);
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals(Long.valueOf(1L), result.getId());
        verify(processOrchestrationWriteDomainService).createProcessOrchestration(any());
    }

    @Test
    public void testCreateProcessOrchestrationWithoutExecutionPlanPastEnd() throws Throwable {
        CreateProcessOrchestrationRequest request = new CreateProcessOrchestrationRequest();
        request.setAppId("testApp");
        request.setMisId("testMis");
        ScrmProcessOrchestrationInfoVO infoVO = new ScrmProcessOrchestrationInfoVO();
        infoVO.setName("Test Orchestration");
        infoVO.setBeginTime(DateUtil.formatYMdHms(new Date(System.currentTimeMillis() - 172800000)));
        infoVO.setEndTime(DateUtil.formatYMdHms(new Date(System.currentTimeMillis() - 86400000)));
        infoVO.setCronComment("0 0 12 * * ?");
        request.setProcessOrchestrationInfo(infoVO);
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setBeginTime(new Date(System.currentTimeMillis() - 172800000));
        dto.setEndTime(new Date(System.currentTimeMillis() - 86400000));
        dto.setCronComment("0 0 12 * * ?");
        when(processOrchestrationInfoVOConverter.convertToDTO(any())).thenReturn(dto);
        DdlResultDTO ddlResultDTO = new DdlResultDTO(true, "Success", 1L);
        when(processOrchestrationWriteDomainService.createProcessOrchestration(any())).thenReturn(ddlResultDTO);
        CreateProcessOrchestrationResultVO result = processOrchestrationInnerService.createProcessOrchestration(request);
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals(Long.valueOf(1L), result.getId());
        verify(processOrchestrationWriteDomainService).createProcessOrchestration(any());
    }

    @Test
    public void testCreateProcessOrchestrationWithGroupInfo() throws Throwable {
        CreateProcessOrchestrationRequest request = new CreateProcessOrchestrationRequest();
        request.setAppId("testApp");
        request.setMisId("testMis");
        ScrmProcessOrchestrationInfoVO infoVO = new ScrmProcessOrchestrationInfoVO();
        infoVO.setName("Test Orchestration");
        request.setProcessOrchestrationInfo(infoVO);
        request.setGroupIdInfoList(Collections.singletonList("{\"groupId\":\"123\",\"groupName\":\"TestGroup\"}"));
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        when(processOrchestrationInfoVOConverter.convertToDTO(any())).thenReturn(dto);
        DdlResultDTO ddlResultDTO = new DdlResultDTO(true, "Success", 1L);
        when(processOrchestrationWriteDomainService.createProcessOrchestration(any())).thenReturn(ddlResultDTO);
        CreateProcessOrchestrationResultVO result = processOrchestrationInnerService.createProcessOrchestration(request);
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals(Long.valueOf(1L), result.getId());
        verify(processOrchestrationWriteDomainService).createProcessOrchestration(any());
    }

    @Test
    public void testCreateProcessOrchestrationWithCrowdPackUpdateStrategy() throws Throwable {
        CreateProcessOrchestrationRequest request = new CreateProcessOrchestrationRequest();
        request.setAppId("testApp");
        request.setMisId("testMis");
        ScrmProcessOrchestrationInfoVO infoVO = new ScrmProcessOrchestrationInfoVO();
        infoVO.setName("Test Orchestration");
        request.setProcessOrchestrationInfo(infoVO);
        ScrmCrowdPackUpdateStrategyInfoVO strategyInfoVO = new ScrmCrowdPackUpdateStrategyInfoVO();
        request.setScrmCrowdPackUpdateStrategyInfoVO(strategyInfoVO);
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        when(processOrchestrationInfoVOConverter.convertToDTO(any())).thenReturn(dto);
        when(scrmCrowdPackUpdateStrategyInfoVOConverter.convertToDTO(any())).thenReturn(new ScrmCrowdPackUpdateStrategyInfoDTO());
        DdlResultDTO ddlResultDTO = new DdlResultDTO(true, "Success", 1L);
        when(processOrchestrationWriteDomainService.createProcessOrchestration(any())).thenReturn(ddlResultDTO);
        CreateProcessOrchestrationResultVO result = processOrchestrationInnerService.createProcessOrchestration(request);
        assertNotNull(result);
        assertTrue(result.getSuccess());
        assertEquals(Long.valueOf(1L), result.getId());
        verify(scrmCrowdPackUpdateStrategyInfoVOConverter).convertToDTO(any());
        verify(processOrchestrationWriteDomainService).createProcessOrchestration(any());
    }
}
