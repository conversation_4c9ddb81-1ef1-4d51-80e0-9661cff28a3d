package com.sankuai.scrm.core.service.automatedmanagement.domainservice.activitypage;

import com.sankuai.scrm.core.service.automatedmanagement.domainservice.activitypage.ActivityPageManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductActivityPageDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationProductActivityPageDOExample;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.dz.srcm.automatedmanagement.request.activitypage.PagingQueryActivityPagetRequest;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ActivityPageManagementService_FuzzQueryActivityPageCountTest {

    @InjectMocks
    private ActivityPageManagementService activityPageManagementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductActivityPageDOMapper activityPageDOMapper;

    private PagingQueryActivityPagetRequest request;

    @Before
    public void setUp() {
        request = new PagingQueryActivityPagetRequest();
    }

    @Test
    public void testFuzzQueryActivityPageCountRequestIsNull() {
        Long result = activityPageManagementService.fuzzQueryActivityPageCount(null);
        assertEquals(Long.valueOf(0), result);
    }

    @Test
    public void testFuzzQueryActivityPageCountPageSizeOrPageNumIsInvalid() {
        request.setPageSize(-1);
        Long result = activityPageManagementService.fuzzQueryActivityPageCount(request);
        assertEquals(Long.valueOf(0), result);
    }

    @Test
    public void testFuzzQueryActivityPageCountAppIdIsNull() {
        request.setPageSize(10);
        request.setPageNum(1);
        Long result = activityPageManagementService.fuzzQueryActivityPageCount(request);
        assertEquals(Long.valueOf(0), result);
    }

    @Test
    public void testFuzzQueryActivityPageCountAppIdAndActivityPageDescAreValid() {
        request.setPageSize(10);
        request.setPageNum(1);
        request.setAppId("testAppId");
        request.setActivityPageDesc("testActivityPageDesc");
        when(activityPageDOMapper.countByExample(any(ScrmAmProcessOrchestrationProductActivityPageDOExample.class))).thenReturn(10L);
        Long result = activityPageManagementService.fuzzQueryActivityPageCount(request);
        assertEquals(Long.valueOf(10), result);
    }
}
