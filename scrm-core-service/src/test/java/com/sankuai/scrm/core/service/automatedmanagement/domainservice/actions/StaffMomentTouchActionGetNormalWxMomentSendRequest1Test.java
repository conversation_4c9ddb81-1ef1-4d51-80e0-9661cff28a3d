package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionContentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.UploadWxAttachmentAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxMomentSendRequest;
import com.sankuai.scrm.core.service.infrastructure.vo.WechatMediaResult;
import com.sankuai.scrm.core.service.infrastructure.vo.WxMediaType;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class StaffMomentTouchActionGetNormalWxMomentSendRequest1Test {

    @InjectMocks
    private StaffMomentTouchAction staffMomentTouchAction;

    @Mock
    private UploadWxAttachmentAcl uploadWxAttachmentAcl;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;

    private List<ScrmProcessOrchestrationActionContentDTO> contentDTOS;

    private ExecuteManagementDTO executeManagementDTO;

    private StepExecuteResultDTO result;

    @BeforeEach
    public void setUp() {
        // Initialize common test objects
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setAppId("testApp");
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        nodeMediumDTO.setActionAttachmentMap(new HashMap<>());
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        contentDTOS = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("test content");
        contentDTO.setProcessOrchestrationNodeId(1L);
        contentDTO.setActionId(1);
        contentDTO.setContentId(1);
        contentDTOS.add(contentDTO);
        executeManagementDTO = new ExecuteManagementDTO();
        executeManagementDTO.setStaffLimitSet(Collections.singleton("staff1"));
        result = new StepExecuteResultDTO();
        result.setSuccess(true);
    }

    /**
     * Test picture attachment upload success case
     */
    @Test
    public void testGetNormalWxMomentSendRequest_PictureUploadSuccess() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        // PICTURE type
        attachmentDTO.setAttachmentTypeId(1);
        attachmentDTO.setAttachmentContent("{\"picUrl\":\"http://test.com/pic.jpg\",\"contentUrl\":\"http://test.com/content\"}");
        List<ScrmProcessOrchestrationActionAttachmentDTO> attachmentList = new ArrayList<>();
        attachmentList.add(attachmentDTO);
        // nodeId-actionId-contentId
        String key = "1-1-1";
        processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentMap().put(key, attachmentList);
        WechatMediaResult mediaResult = new WechatMediaResult();
        mediaResult.setErrcode(0);
        mediaResult.setMedia_id("testMediaId");
        when(uploadWxAttachmentAcl.uploadWxAttachment(anyString(), eq(WxMediaType.image), anyString(), eq(true))).thenReturn(mediaResult);
        // act
        WxMomentSendRequest request = staffMomentTouchAction.getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
        // assert
        assertNotNull(request);
        assertTrue(result.isSuccess());
        assertNotNull(request.getAttachments());
        assertEquals(1, request.getAttachments().size());
        assertEquals("image", request.getAttachments().get(0).getMsgtype());
    }

    /**
     * Test picture attachment upload failure case
     */
    @Test
    public void testGetNormalWxMomentSendRequest_PictureUploadFail() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        // PICTURE type
        attachmentDTO.setAttachmentTypeId(1);
        attachmentDTO.setAttachmentContent("{\"picUrl\":\"http://test.com/pic.jpg\",\"contentUrl\":\"http://test.com/content\"}");
        List<ScrmProcessOrchestrationActionAttachmentDTO> attachmentList = new ArrayList<>();
        attachmentList.add(attachmentDTO);
        // nodeId-actionId-contentId
        String key = "1-1-1";
        processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentMap().put(key, attachmentList);
        WechatMediaResult mediaResult = new WechatMediaResult();
        mediaResult.setErrcode(1);
        mediaResult.setErrmsg("Upload failed");
        when(uploadWxAttachmentAcl.uploadWxAttachment(anyString(), eq(WxMediaType.image), anyString(), eq(true))).thenReturn(mediaResult);
        // act
        WxMomentSendRequest request = staffMomentTouchAction.getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
        // assert
        assertNull(request);
        assertFalse(result.isSuccess());
        assertTrue(result.isExistedFailedAttachmentUpload());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ATTACHMENT_UPLOAD_FAIL.getCode(), result.getCode());
    }

    /**
     * Test video attachment upload success case
     */
    @Test
    public void testGetNormalWxMomentSendRequest_VideoUploadSuccess() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        // VIDEO type
        attachmentDTO.setAttachmentTypeId(4);
        attachmentDTO.setAttachmentContent("{\"contentUrl\":\"http://test.com/video.mp4\"}");
        List<ScrmProcessOrchestrationActionAttachmentDTO> attachmentList = new ArrayList<>();
        attachmentList.add(attachmentDTO);
        // nodeId-actionId-contentId
        String key = "1-1-1";
        processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentMap().put(key, attachmentList);
        WechatMediaResult mediaResult = new WechatMediaResult();
        mediaResult.setErrcode(0);
        mediaResult.setMedia_id("testVideoMediaId");
        when(uploadWxAttachmentAcl.uploadWxAttachment(anyString(), eq(WxMediaType.video), anyString(), eq(true))).thenReturn(mediaResult);
        // act
        WxMomentSendRequest request = staffMomentTouchAction.getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
        // assert
        assertNotNull(request);
        assertTrue(result.isSuccess());
        assertNotNull(request.getAttachments());
        assertEquals(1, request.getAttachments().size());
        assertEquals("video", request.getAttachments().get(0).getMsgtype());
    }

    /**
     * Test video attachment upload failure case
     */
    @Test
    public void testGetNormalWxMomentSendRequest_VideoUploadFail() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        // VIDEO type
        attachmentDTO.setAttachmentTypeId(4);
        attachmentDTO.setAttachmentContent("{\"contentUrl\":\"http://test.com/video.mp4\"}");
        List<ScrmProcessOrchestrationActionAttachmentDTO> attachmentList = new ArrayList<>();
        attachmentList.add(attachmentDTO);
        // nodeId-actionId-contentId
        String key = "1-1-1";
        processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentMap().put(key, attachmentList);
        WechatMediaResult mediaResult = new WechatMediaResult();
        mediaResult.setErrcode(1);
        mediaResult.setErrmsg("Upload failed");
        when(uploadWxAttachmentAcl.uploadWxAttachment(anyString(), eq(WxMediaType.video), anyString(), eq(true))).thenReturn(mediaResult);
        // act
        WxMomentSendRequest request = staffMomentTouchAction.getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
        // assert
        assertNull(request);
        assertFalse(result.isSuccess());
        assertTrue(result.isExistedFailedAttachmentUpload());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ATTACHMENT_UPLOAD_FAIL.getCode(), result.getCode());
    }
}