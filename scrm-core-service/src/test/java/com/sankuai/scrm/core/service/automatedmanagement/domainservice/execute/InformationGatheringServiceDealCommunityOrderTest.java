package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationDistributorCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationDistributorCodeDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationDistributorCodeDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.order.UnifiedOrderAclService;
import com.sankuai.technician.trade.api.order.enums.OrderDistributionTypeEnum;
import com.sankuai.technician.trade.api.order.message.OrderOperateNotify;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.springframework.beans.factory.annotation.Autowired;
import static org.junit.jupiter.api.Assertions.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationRelatedOrderInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationRelatedOrderInfoDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationRelatedOrderInfoDOMapper;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class InformationGatheringServiceDealCommunityOrderTest {

    @InjectMocks
    private InformationGatheringService informationGatheringService;

    @Mock
    private UnifiedOrderAclService unifiedOrderAclService;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock
    private ScrmAmProcessOrchestrationDistributorCodeDOMapper distributorCodeDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationRelatedOrderInfoDOMapper relatedOrderInfoDOMapper;

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test dealCommunityOrder when operateNotify is null
     */
    @Test
    public void testDealCommunityOrder_NullOperateNotify() throws Throwable {
        // arrange
        OrderOperateNotify operateNotify = null;
        // act
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test dealCommunityOrder when operateNotify is not null but channel is not COMMUNITY
     */
    @Test
    public void testDealCommunityOrder_NonCommunityChannel() throws Throwable {
        // arrange
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.MT_FENFEN.getCode());
        operateNotify.setOperateType(1);
        // act
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test dealCommunityOrder when operateNotify is not null, channel is COMMUNITY but extInfo is empty
     */
    @Test
    public void testDealCommunityOrder_EmptyExtInfo() throws Throwable {
        // arrange
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        operateNotify.setOperateType(1);
        Map<String, String> extInfo = new HashMap<>();
        operateNotify.setExtInfo(extInfo);
        // act
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test dealCommunityOrder when operateNotify is not null, channel is COMMUNITY, extInfo is not empty but distributorId is missing
     */
    @Test
    public void testDealCommunityOrder_MissingDistributorId() throws Throwable {
        // arrange
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        operateNotify.setOperateType(1);
        Map<String, String> extInfo = new HashMap<>();
        extInfo.put("someKey", "someValue");
        operateNotify.setExtInfo(extInfo);
        // act
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test dealCommunityOrder when operateNotify is not null, channel is COMMUNITY, extInfo contains distributorId but it's invalid
     */
    @Test
    public void testDealCommunityOrder_InvalidDistributorId() throws Throwable {
        // arrange
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        operateNotify.setOperateType(1);
        Map<String, String> extInfo = new HashMap<>();
        extInfo.put("distributorId", "0");
        operateNotify.setExtInfo(extInfo);
        // act
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test dealCommunityOrder when operateNotify is not null, channel is COMMUNITY, extInfo contains valid distributorId but no matching distributorCodeDOs
     */
    @Test
    public void testDealCommunityOrder_NoMatchingDistributorCodeDOs() throws Throwable {
        // arrange
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        operateNotify.setOperateType(1);
        Map<String, String> extInfo = new HashMap<>();
        extInfo.put("distributorId", "123");
        operateNotify.setExtInfo(extInfo);
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    private void invokeInsertRelateOrderInfo(UnifiedOrderWithId order, ScrmAmProcessOrchestrationDistributorCodeDO codeDO, long executeLogId, byte relateType, String couponGroupId, String unifiedCouponId) throws Exception {
        Method method = InformationGatheringService.class.getDeclaredMethod("insertRelateOrderInfo", UnifiedOrderWithId.class, ScrmAmProcessOrchestrationDistributorCodeDO.class, long.class, byte.class, String.class, String.class);
        method.setAccessible(true);
        method.invoke(informationGatheringService, order, codeDO, executeLogId, relateType, couponGroupId, unifiedCouponId);
    }

    @Test
    public void testInsertRelateOrderInfoWhenNoExistingOrder() throws Throwable {
        // arrange
        UnifiedOrderWithId order = new UnifiedOrderWithId();
        order.setUnifiedOrderId("order123");
        order.setTotalAmount(new BigDecimal("100.50"));
        order.setMtUserId(12345L);
        ScrmAmProcessOrchestrationDistributorCodeDO codeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        codeDO.setProcessOrchestrationId(1L);
        codeDO.setProcessOrchestrationVersion("v1");
        when(relatedOrderInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationRelatedOrderInfoDOExample.class))).thenReturn(Collections.emptyList());
        // act
        invokeInsertRelateOrderInfo(order, codeDO, 10L, (byte) 1, "group1", "coupon1");
        // assert
        verify(relatedOrderInfoDOMapper).insert(any(ScrmAmProcessOrchestrationRelatedOrderInfoDO.class));
    }

    @Test
    public void testInsertRelateOrderInfoWhenExistingOrderFound() throws Throwable {
        // arrange
        UnifiedOrderWithId order = new UnifiedOrderWithId();
        order.setUnifiedOrderId("order123");
        ScrmAmProcessOrchestrationDistributorCodeDO codeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        ScrmAmProcessOrchestrationRelatedOrderInfoDO existingOrder = new ScrmAmProcessOrchestrationRelatedOrderInfoDO();
        List<ScrmAmProcessOrchestrationRelatedOrderInfoDO> existingOrders = new ArrayList<>();
        existingOrders.add(existingOrder);
        when(relatedOrderInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationRelatedOrderInfoDOExample.class))).thenReturn(existingOrders);
        // act
        invokeInsertRelateOrderInfo(order, codeDO, 10L, (byte) 1, null, null);
        // assert
        verify(relatedOrderInfoDOMapper, never()).insert(any());
    }

    @Test
    public void testInsertRelateOrderInfoWithNullCouponInfo() throws Throwable {
        // arrange
        UnifiedOrderWithId order = new UnifiedOrderWithId();
        order.setUnifiedOrderId("order123");
        order.setTotalAmount(new BigDecimal("100.50"));
        order.setMtUserId(12345L);
        ScrmAmProcessOrchestrationDistributorCodeDO codeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        codeDO.setProcessOrchestrationId(1L);
        codeDO.setProcessOrchestrationVersion("v1");
        when(relatedOrderInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationRelatedOrderInfoDOExample.class))).thenReturn(Collections.emptyList());
        // act
        invokeInsertRelateOrderInfo(order, codeDO, 10L, (byte) 1, null, null);
        // assert
        verify(relatedOrderInfoDOMapper).insert(argThat(record -> record.getCouponGroupId() == null && record.getUnifiedCouponId() == null));
    }

    @Test
    public void testInsertRelateOrderInfoWithCouponInfo() throws Throwable {
        // arrange
        UnifiedOrderWithId order = new UnifiedOrderWithId();
        order.setUnifiedOrderId("order123");
        order.setTotalAmount(new BigDecimal("100.50"));
        order.setMtUserId(12345L);
        ScrmAmProcessOrchestrationDistributorCodeDO codeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        codeDO.setProcessOrchestrationId(1L);
        codeDO.setProcessOrchestrationVersion("v1");
        when(relatedOrderInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationRelatedOrderInfoDOExample.class))).thenReturn(Collections.emptyList());
        // act
        invokeInsertRelateOrderInfo(order, codeDO, 10L, (byte) 1, "group1", "coupon1");
        // assert
        verify(relatedOrderInfoDOMapper).insert(argThat(record -> "group1".equals(record.getCouponGroupId()) && "coupon1".equals(record.getUnifiedCouponId())));
    }

    @Test
    public void testInsertRelateOrderInfoWithNullOrder() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationDistributorCodeDO codeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        // act & assert
        Exception exception = assertThrows(InvocationTargetException.class, () -> {
            invokeInsertRelateOrderInfo(null, codeDO, 10L, (byte) 1, null, null);
        });
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    @Test
    public void testInsertRelateOrderInfoWithNullCodeDO() throws Throwable {
        // arrange
        UnifiedOrderWithId order = new UnifiedOrderWithId();
        order.setUnifiedOrderId("order123");
        // act & assert
        Exception exception = assertThrows(InvocationTargetException.class, () -> {
            invokeInsertRelateOrderInfo(order, null, 10L, (byte) 1, null, null);
        });
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    @Test
    public void testInsertRelateOrderInfoWithZeroAmount() throws Throwable {
        // arrange
        UnifiedOrderWithId order = new UnifiedOrderWithId();
        order.setUnifiedOrderId("order123");
        order.setTotalAmount(BigDecimal.ZERO);
        order.setMtUserId(12345L);
        ScrmAmProcessOrchestrationDistributorCodeDO codeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        codeDO.setProcessOrchestrationId(1L);
        codeDO.setProcessOrchestrationVersion("v1");
        when(relatedOrderInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationRelatedOrderInfoDOExample.class))).thenReturn(Collections.emptyList());
        // act
        invokeInsertRelateOrderInfo(order, codeDO, 10L, (byte) 1, null, null);
        // assert
        verify(relatedOrderInfoDOMapper).insert(argThat(record -> BigDecimal.ZERO.compareTo(record.getOrderTotalAmount()) == 0));
    }

    @Test
    public void testInsertRelateOrderInfoWithNullUnifiedOrderId() throws Throwable {
        // arrange
        // unifiedOrderId not set
        UnifiedOrderWithId order = new UnifiedOrderWithId();
        ScrmAmProcessOrchestrationDistributorCodeDO codeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        // act & assert
        Exception exception = assertThrows(InvocationTargetException.class, () -> {
            invokeInsertRelateOrderInfo(order, codeDO, 10L, (byte) 1, null, null);
        });
        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("Value for uniOrderId cannot be null", exception.getCause().getMessage());
    }
}
