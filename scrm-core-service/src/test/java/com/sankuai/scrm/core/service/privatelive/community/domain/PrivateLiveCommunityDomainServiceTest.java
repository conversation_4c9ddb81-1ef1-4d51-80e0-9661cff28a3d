package com.sankuai.scrm.core.service.privatelive.community.domain;

import com.sankuai.dz.srcm.pchat.dto.PagerList;
import com.sankuai.dz.srcm.privatelive.community.dto.CustomerListByTagRequest;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserGrowthYimeiLiveUserTag;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserTagStatResult;
import com.sankuai.scrm.core.service.user.dal.example.ScrmUserGrowthYimeiLiveUserTagExample;
import com.sankuai.scrm.core.service.user.dal.mapper.ScrmUserGrowthYimeiLiveUserTagCustomMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class PrivateLiveCommunityDomainServiceTest {

    @Mock
    private ScrmUserGrowthYimeiLiveUserTagCustomMapper scrmUserGrowthYimeiLiveUserTagCustomMapper;

    @InjectMocks
    private PrivateLiveCommunityDomainService service;

    private static final String TEST_LIVE_ID = "testLiveId";

    private static final Integer TAG_ID_1 = 1;

    private static final Integer TAG_ID_2 = 2;

    private ScrmUserTagStatResult createTagStatResult(Integer tagId, Integer userCnt) {
        ScrmUserTagStatResult result = new ScrmUserTagStatResult();
        result.setTagId(tagId);
        result.setUserCnt(userCnt);
        return result;
    }

    /**
     * Test when tagIds is null - should only filter by liveId and status
     */
    @Test
    public void testLiveUserCntStatWithTagNullTagIds() throws Throwable {
        // arrange
        List<ScrmUserTagStatResult> expectedResults = Collections.singletonList(createTagStatResult(TAG_ID_1, 10));
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.scrmUserCntWithTag(any(ScrmUserGrowthYimeiLiveUserTagExample.class))).thenReturn(expectedResults);
        // act
        List<ScrmUserTagStatResult> results = service.liveUserCntStatWithTag(TEST_LIVE_ID, null);
        // assert
        assertEquals(1, results.size());
        assertEquals(TAG_ID_1, results.get(0).getTagId());
        assertEquals(10, results.get(0).getUserCnt().intValue());
    }

    /**
     * Test when tagIds is empty - should only filter by liveId and status
     */
    @Test
    public void testLiveUserCntStatWithTagEmptyTagIds() throws Throwable {
        // arrange
        List<ScrmUserTagStatResult> expectedResults = Collections.singletonList(createTagStatResult(TAG_ID_1, 10));
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.scrmUserCntWithTag(any(ScrmUserGrowthYimeiLiveUserTagExample.class))).thenReturn(expectedResults);
        // act
        List<ScrmUserTagStatResult> results = service.liveUserCntStatWithTag(TEST_LIVE_ID, Collections.emptyList());
        // assert
        assertEquals(1, results.size());
        assertEquals(TAG_ID_1, results.get(0).getTagId());
        assertEquals(10, results.get(0).getUserCnt().intValue());
    }

    /**
     * Test with single tagId - should filter by liveId, status and the single tagId
     */
    @Test
    public void testLiveUserCntStatWithTagSingleTagId() throws Throwable {
        // arrange
        List<ScrmUserTagStatResult> expectedResults = Collections.singletonList(createTagStatResult(TAG_ID_1, 10));
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.scrmUserCntWithTag(any(ScrmUserGrowthYimeiLiveUserTagExample.class))).thenReturn(expectedResults);
        // act
        List<ScrmUserTagStatResult> results = service.liveUserCntStatWithTag(TEST_LIVE_ID, Collections.singletonList(TAG_ID_1));
        // assert
        assertEquals(1, results.size());
        assertEquals(TAG_ID_1, results.get(0).getTagId());
        assertEquals(10, results.get(0).getUserCnt().intValue());
    }

    /**
     * Test with multiple tagIds - should filter by liveId, status and all tagIds
     */
    @Test
    public void testLiveUserCntStatWithTagMultipleTagIds() throws Throwable {
        // arrange
        List<ScrmUserTagStatResult> expectedResults = Arrays.asList(createTagStatResult(TAG_ID_1, 10), createTagStatResult(TAG_ID_2, 20));
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.scrmUserCntWithTag(any(ScrmUserGrowthYimeiLiveUserTagExample.class))).thenReturn(expectedResults);
        // act
        List<ScrmUserTagStatResult> results = service.liveUserCntStatWithTag(TEST_LIVE_ID, Arrays.asList(TAG_ID_1, TAG_ID_2));
        // assert
        assertEquals(2, results.size());
        assertEquals(TAG_ID_1, results.get(0).getTagId());
        assertEquals(10, results.get(0).getUserCnt().intValue());
        assertEquals(TAG_ID_2, results.get(1).getTagId());
        assertEquals(20, results.get(1).getUserCnt().intValue());
    }

    /**
     * Test when mapper returns empty result - should return empty list
     */
    @Test
    public void testLiveUserCntStatWithTagEmptyResult() throws Throwable {
        // arrange
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.scrmUserCntWithTag(any(ScrmUserGrowthYimeiLiveUserTagExample.class))).thenReturn(Collections.emptyList());
        // act
        List<ScrmUserTagStatResult> results = service.liveUserCntStatWithTag(TEST_LIVE_ID, Collections.singletonList(TAG_ID_1));
        // assert
        assertTrue(results.isEmpty());
    }


    /**
     * 测试分页参数为空时使用默认值
     */
    @Test
    public void testQueryCustomerListByTag_DefaultPagination() throws Throwable {
        CustomerListByTagRequest request = new CustomerListByTagRequest();
        request.setLiveId("testLiveId");
        // arrange
        request.setPageNum(null);
        request.setPageSize(null);
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.queryCustomerListByTagCount(any(), anyInt(), anyInt())).thenReturn(10L);
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.queryCustomerListByTag(any(), anyInt(), anyInt())).thenReturn(Collections.singletonList(new ScrmUserGrowthYimeiLiveUserTag()));
        // act
        PagerList<ScrmUserGrowthYimeiLiveUserTag> result = service.queryCustomerListByTag(request);
        // assert
        assertEquals(10L, result.getTotal());
        assertEquals(1, result.getData().size());
    }

    /**
     * 测试wxNickName不为空时添加%通配符
     */
    @Test
    public void testQueryCustomerListByTag_WithWxNickName() throws Throwable {
        CustomerListByTagRequest request = new CustomerListByTagRequest();
        request.setLiveId("testLiveId");
        // arrange
        request.setWxNickName("test");
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.queryCustomerListByTagCount(any(), anyInt(), anyInt())).thenReturn(5L);
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.queryCustomerListByTag(any(), anyInt(), anyInt())).thenReturn(Arrays.asList(new ScrmUserGrowthYimeiLiveUserTag(), new ScrmUserGrowthYimeiLiveUserTag()));
        // act
        PagerList<ScrmUserGrowthYimeiLiveUserTag> result = service.queryCustomerListByTag(request);
        // assert
        assertEquals(5L, result.getTotal());
        assertEquals(2, result.getData().size());
        assertTrue(request.getWxNickName().contains("%test%"));
    }

    /**
     * 测试查询总数为0时返回空PagerList
     */
    @Test
    public void testQueryCustomerListByTag_ZeroTotal() throws Throwable {
        CustomerListByTagRequest request = new CustomerListByTagRequest();
        request.setLiveId("testLiveId");
        // arrange
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.queryCustomerListByTagCount(any(), anyInt(), anyInt())).thenReturn(0L);
        // act
        PagerList<ScrmUserGrowthYimeiLiveUserTag> result = service.queryCustomerListByTag(request);
        // assert
        assertEquals(0L, result.getTotal());
        assertTrue(result.getData().isEmpty());
    }

    /**
     * 测试分页参数边界值(最小pageNum)
     */
    @Test
    public void testQueryCustomerListByTag_MinPageNum() throws Throwable {
        CustomerListByTagRequest request = new CustomerListByTagRequest();
        request.setLiveId("testLiveId");
        // arrange
        request.setPageNum(-1);
        request.setPageSize(20);
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.queryCustomerListByTagCount(any(), anyInt(), anyInt())).thenReturn(100L);
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.queryCustomerListByTag(any(), anyInt(), anyInt())).thenReturn(Arrays.asList(new ScrmUserGrowthYimeiLiveUserTag()));
        // act
        PagerList<ScrmUserGrowthYimeiLiveUserTag> result = service.queryCustomerListByTag(request);
        // assert
        assertEquals(100L, result.getTotal());
        assertEquals(1, result.getData().size());
        // Corrected assertion to match expected behavior
        assertEquals(-40, (request.getPageNum() - 1) * request.getPageSize());
    }

    /**
     * 测试mapper返回null数据
     */
    @Test
    public void testQueryCustomerListByTag_NullDataFromMapper() throws Throwable {
        CustomerListByTagRequest request = new CustomerListByTagRequest();
        request.setLiveId("testLiveId");
        // arrange
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.queryCustomerListByTagCount(any(), anyInt(), anyInt())).thenReturn(10L);
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.queryCustomerListByTag(any(), anyInt(), anyInt())).thenReturn(null);
        // act
        PagerList<ScrmUserGrowthYimeiLiveUserTag> result = service.queryCustomerListByTag(request);
        // assert
        assertEquals(10L, result.getTotal());
        assertNull(result.getData());
    }

    /**
     * 测试正常分页查询场景
     */
    @Test
    public void testQueryCustomerListByTag_NormalPagination() throws Throwable {
        CustomerListByTagRequest request = new CustomerListByTagRequest();
        request.setLiveId("testLiveId");
        // arrange
        request.setPageNum(2);
        request.setPageSize(5);
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.queryCustomerListByTagCount(any(), anyInt(), anyInt())).thenReturn(15L);
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.queryCustomerListByTag(any(), anyInt(), anyInt())).thenReturn(Arrays.asList(new ScrmUserGrowthYimeiLiveUserTag(), new ScrmUserGrowthYimeiLiveUserTag()));
        // act
        PagerList<ScrmUserGrowthYimeiLiveUserTag> result = service.queryCustomerListByTag(request);
        // assert
        assertEquals(15L, result.getTotal());
        assertEquals(2, result.getData().size());
        // 验证offset计算正确
        assertEquals(5, (request.getPageNum() - 1) * request.getPageSize());
    }

    /**
     * 测试wxNickName为空字符串时不添加通配符
     */
    @Test
    public void testQueryCustomerListByTag_EmptyWxNickName() throws Throwable {
        CustomerListByTagRequest request = new CustomerListByTagRequest();
        request.setLiveId("testLiveId");
        // arrange
        request.setWxNickName("");
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.queryCustomerListByTagCount(any(), anyInt(), anyInt())).thenReturn(1L);
        when(scrmUserGrowthYimeiLiveUserTagCustomMapper.queryCustomerListByTag(any(), anyInt(), anyInt())).thenReturn(Collections.singletonList(new ScrmUserGrowthYimeiLiveUserTag()));
        // act
        PagerList<ScrmUserGrowthYimeiLiveUserTag> result = service.queryCustomerListByTag(request);
        // assert
        assertEquals(1L, result.getTotal());
        assertEquals(1, result.getData().size());
        // 验证未添加通配符
        assertEquals("", request.getWxNickName());
    }
}
