package com.sankuai.scrm.core.service.activity.miniprogram.service;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.activity.miniprogram.vo.CouponVO;
import com.sankuai.scrm.core.service.BaseMockTest;
import com.sankuai.scrm.core.service.activity.miniprogram.domain.ItemDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.order.domain.ScrmWideTradeOrderDomainService;
import org.junit.Assert;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;


public class ActivityCustomerServiceImplTest extends BaseMockTest {

    @InjectMocks
    private ActivityCustomerServiceImpl activityCustomerService;

    @Mock
    private ScrmWideTradeOrderDomainService scrmWideTradeOrderDomainService;
    @Mock(lenient = true)
    private MtUserCenterAclService mtUserCenterAclService;
    @Mock(lenient = true)
    private ItemDomainService itemDomainService;



    @Test
    public void testNewCustomerCheck() {
        String appId = "yimei";
        Long mtUserId = 1L;
        Mockito.when(scrmWideTradeOrderDomainService.queryWideTradeOrderByUserId(mtUserId))
                .thenReturn(null);
        RemoteResponse<Boolean> response = activityCustomerService.checkNewCustomer(appId, mtUserId);
        Assert.assertTrue(response.getData());
    }

    @Test
    public void testDrawCouponPackageWithNullParameters() {
        // 模拟传入null参数
        RemoteResponse<List<CouponVO>> response = activityCustomerService.drawCouponPackage(null, null, null, null, null);
        assertEquals("参数缺失，无法领取", response.getMsg());
    }

    @Test
    public void testDrawCouponPackageWithInvalidToken() {
        try (MockedStatic<Lion> lionMockedStatic = Mockito.mockStatic(Lion.class)) {
            // 模拟配置要求验证token，但传入的token无效
            lionMockedStatic.when(() -> Lion.getBoolean(anyString(), anyString(), anyBoolean())).thenReturn(true);
            when(mtUserCenterAclService.queryUserIdByToken(anyString())).thenReturn(0L);
//            when(itemDomainService.queryItem(anyLong())).thenReturn(null);
            RemoteResponse<List<CouponVO>> response = activityCustomerService.drawCouponPackage(1L, 1, 1L, 1L, "");
            assertEquals("用户信息错误", response.getMsg());
        }
    }

}