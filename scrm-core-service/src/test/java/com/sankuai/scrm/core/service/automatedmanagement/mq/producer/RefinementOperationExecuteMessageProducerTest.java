package com.sankuai.scrm.core.service.automatedmanagement.mq.producer;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.dz.srcm.automatedmanagement.dto.mq.RefinementOperationExecuteMessage;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import java.lang.reflect.Field;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class RefinementOperationExecuteMessageProducerTest {

    @InjectMocks
    private RefinementOperationExecuteMessageProducer producer;

    @Mock
    private IProducerProcessor mafkaProducer;

    private IProducerProcessor originalProducer;

    @Before
    public void setUp() throws Exception {
        // Store the original producer
        Field producerField = RefinementOperationExecuteMessageProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        originalProducer = (IProducerProcessor) producerField.get(null);
        // Set our mock producer
        producerField.set(null, mafkaProducer);
    }

    @After
    public void tearDown() throws Exception {
        // Restore the original producer
        Field producerField = RefinementOperationExecuteMessageProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, originalProducer);
    }

    @Test
    public void testSendPersonaCrowdPackUpdateTaskExecuteMessage_NormalCase() throws Throwable {
        // arrange
        Integer personaId = 123;
        Long packId = 456L;
        String appId = "testApp";
        String crowdPackVersion = "1.0";
        ProducerResult producerResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(mafkaProducer.sendMessage(anyString())).thenReturn(producerResult);
        // act
        producer.sendPersonaCrowdPackUpdateTaskExecuteMessage(personaId, packId, appId, crowdPackVersion);
        // assert
        verify(mafkaProducer, times(1)).sendMessage(anyString());
    }

    @Test
    public void testSendPersonaCrowdPackUpdateTaskExecuteMessage_NullPersonaId() throws Throwable {
        // arrange
        Long packId = 456L;
        String appId = "testApp";
        String crowdPackVersion = "1.0";
        ProducerResult producerResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(mafkaProducer.sendMessage(anyString())).thenReturn(producerResult);
        // act
        producer.sendPersonaCrowdPackUpdateTaskExecuteMessage(null, packId, appId, crowdPackVersion);
        // assert
        verify(mafkaProducer, times(1)).sendMessage(anyString());
    }

    @Test
    public void testSendPersonaCrowdPackUpdateTaskExecuteMessage_NullPackId() throws Throwable {
        // arrange
        Integer personaId = 123;
        String appId = "testApp";
        String crowdPackVersion = "1.0";
        ProducerResult producerResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(mafkaProducer.sendMessage(anyString())).thenReturn(producerResult);
        // act
        producer.sendPersonaCrowdPackUpdateTaskExecuteMessage(personaId, null, appId, crowdPackVersion);
        // assert
        verify(mafkaProducer, times(1)).sendMessage(anyString());
    }

    @Test
    public void testSendPersonaCrowdPackUpdateTaskExecuteMessage_VerifyMessageContent() throws Throwable {
        // arrange
        Integer personaId = 123;
        Long packId = 456L;
        String appId = "testApp";
        String crowdPackVersion = "1.0";
        ProducerResult producerResult = new ProducerResult(ProducerStatus.SEND_OK);
        ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);
        when(mafkaProducer.sendMessage(messageCaptor.capture())).thenReturn(producerResult);
        // act
        producer.sendPersonaCrowdPackUpdateTaskExecuteMessage(personaId, packId, appId, crowdPackVersion);
        // assert
        verify(mafkaProducer, times(1)).sendMessage(anyString());
        String capturedMessage = messageCaptor.getValue();
        assertNotNull(capturedMessage);
        // You can add more specific assertions about the message content here if needed
    }

    @Test
    public void testSendPersonaCrowdPackUpdateTaskExecuteMessage_ProducerFailure() throws Throwable {
        // arrange
        Integer personaId = 123;
        Long packId = 456L;
        String appId = "testApp";
        String crowdPackVersion = "1.0";
        when(mafkaProducer.sendMessage(anyString())).thenThrow(new RuntimeException("Producer failure"));
        // act
        producer.sendPersonaCrowdPackUpdateTaskExecuteMessage(personaId, packId, appId, crowdPackVersion);
        // assert
        verify(mafkaProducer, times(3)).sendMessage(anyString());
    }
}
