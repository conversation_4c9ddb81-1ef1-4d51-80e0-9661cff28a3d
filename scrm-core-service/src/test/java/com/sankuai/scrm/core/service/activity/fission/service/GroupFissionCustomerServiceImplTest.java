package com.sankuai.scrm.core.service.activity.fission.service;

import com.sankuai.dz.srcm.activity.fission.dto.FriendInfoDTO;
import com.sankuai.dz.srcm.activity.fission.enums.InvitationStatusEnum;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionActivity;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionInvitationRecord;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionInvitationRecordMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmPersonalWxUserAuthorisationInfoDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.example.ScrmPersonalWxUserAuthorisationInfoDOExample;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmPersonalWxUserAuthorisationInfoDOMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.UnsupportedEncodingException;
import java.lang.reflect.Method;
import java.net.URLEncoder;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class GroupFissionCustomerServiceImplTest {

    @Mock
    private GroupFissionInvitationRecordMapper invitationRecordMapper;

    @Mock
    private ScrmPersonalWxUserAuthorisationInfoDOMapper scrmPersonalWxUserAuthorisationInfoDOMapper;

    @Mock
    private GroupFissionActivity activity;

    @InjectMocks
    private GroupFissionCustomerServiceImpl service;

    private String invokePrivateMethod(String appId, Long activityId, String inviterUnionId) throws Exception {
        Method method = GroupFissionCustomerServiceImpl.class.getDeclaredMethod("getNonEntMiniProgramPath", String.class, Long.class, String.class);
        method.setAccessible(true);
        return (String) method.invoke(service, appId, activityId, inviterUnionId);
    }

    /**
     * Test case for null or empty parameters
     * Should return null when any parameter is null or empty
     */
    @Test
    public void testGetNonEntMiniProgramPathWithNullParams() throws Throwable {
        // act
        String result1 = invokePrivateMethod(null, 123L, "union123");
        String result2 = invokePrivateMethod("app123", null, "union123");
        String result3 = invokePrivateMethod("app123", 123L, null);
        String result4 = invokePrivateMethod("", 123L, "union123");
        String result5 = invokePrivateMethod("app123", 123L, "");
        // assert
        assertNull(result1);
        assertNull(result2);
        assertNull(result3);
        assertNull(result4);
        assertNull(result5);
    }

    /**
     * Test case for normal successful path generation
     * Should return formatted mini program path with encoded URL
     */
    @Test
    public void testGetNonEntMiniProgramPathSuccess() throws Throwable {
        // arrange
        String appId = "app123";
        Long activityId = 123L;
        String inviterUnionId = "union123";
        String expectedH5Url = "https://g.dianping.com/yimei-community-wxapp-web/web/fissioninvite.html?activityId=123&appId=app123&inviterUnionId=union123";
        String expectedEncodedUrl = "encoded_url";
        try (MockedStatic<URLEncoder> mockedStatic = mockStatic(URLEncoder.class)) {
            mockedStatic.when(() -> URLEncoder.encode(expectedH5Url, "UTF-8")).thenReturn(expectedEncodedUrl);
            // act
            String result = invokePrivateMethod(appId, activityId, inviterUnionId);
            // assert
            String expectedResult = String.format("/index/pages/h5/h5?f_userId=1&f_openId=1&f_token=1&f_ci=1&f_pos=1&f_openIdCipher=1&f_finger=1&weburl=%s", expectedEncodedUrl);
            assertEquals(expectedResult, result);
            mockedStatic.verify(() -> URLEncoder.encode(expectedH5Url, "UTF-8"));
        }
    }

    /**
     * Test case for URL encoding exception
     * Should return null when URL encoding fails
     */
    @Test
    public void testGetNonEntMiniProgramPathEncodingException() throws Throwable {
        // arrange
        String appId = "app123";
        Long activityId = 123L;
        String inviterUnionId = "union123";
        try (MockedStatic<URLEncoder> mockedStatic = mockStatic(URLEncoder.class)) {
            mockedStatic.when(() -> URLEncoder.encode(anyString(), anyString())).thenThrow(new UnsupportedEncodingException("Test exception"));
            // act
            String result = invokePrivateMethod(appId, activityId, inviterUnionId);
            // assert
            assertNull(result);
            mockedStatic.verify(() -> URLEncoder.encode(anyString(), eq("UTF-8")));
        }
    }

    /**
     * Test case for special characters in parameters
     * Should properly handle special characters in URL encoding
     */
    @Test
    public void testGetNonEntMiniProgramPathWithSpecialChars() throws Throwable {
        // arrange
        String appId = "app@123";
        Long activityId = 123L;
        String inviterUnionId = "union#123";
        String expectedH5Url = "https://g.dianping.com/yimei-community-wxapp-web/web/fissioninvite.html?activityId=123&appId=app@123&inviterUnionId=union#123";
        String expectedEncodedUrl = "encoded_special_chars";
        try (MockedStatic<URLEncoder> mockedStatic = mockStatic(URLEncoder.class)) {
            mockedStatic.when(() -> URLEncoder.encode(expectedH5Url, "UTF-8")).thenReturn(expectedEncodedUrl);
            // act
            String result = invokePrivateMethod(appId, activityId, inviterUnionId);
            // assert
            String expectedResult = String.format("/index/pages/h5/h5?f_userId=1&f_openId=1&f_token=1&f_ci=1&f_pos=1&f_openIdCipher=1&f_finger=1&weburl=%s", expectedEncodedUrl);
            assertEquals(expectedResult, result);
            mockedStatic.verify(() -> URLEncoder.encode(expectedH5Url, "UTF-8"));
        }
    }

    /**
     * 测试当appId为空时返回空Map
     */
    @Test
    void testGetUnionIdAuthorisationInfoMap_whenAppIdIsEmpty_shouldReturnEmptyMap() {
        // arrange
        String appId = "";
        List<String> inviteeUnionIds = Arrays.asList("unionId1");

        // act
        Map<String, ScrmPersonalWxUserAuthorisationInfoDO> result = service.getUnionIdAuthorisationInfoMap(appId, inviteeUnionIds);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试当inviteeUnionIds为null时返回空Map
     */
    @Test
    void testGetUnionIdAuthorisationInfoMap_whenInviteeUnionIdsIsNull_shouldReturnEmptyMap() {
        // arrange
        String appId = "testAppId";
        List<String> inviteeUnionIds = null;

        // act
        Map<String, ScrmPersonalWxUserAuthorisationInfoDO> result = service.getUnionIdAuthorisationInfoMap(appId, inviteeUnionIds);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试当inviteeUnionIds为空列表时返回空Map
     */
    @Test
    void testGetUnionIdAuthorisationInfoMap_whenInviteeUnionIdsIsEmpty_shouldReturnEmptyMap() {
        // arrange
        String appId = "testAppId";
        List<String> inviteeUnionIds = Collections.emptyList();

        // act
        Map<String, ScrmPersonalWxUserAuthorisationInfoDO> result = service.getUnionIdAuthorisationInfoMap(appId, inviteeUnionIds);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试当Mapper返回空列表时返回空Map
     */
    @Test
    void testGetUnionIdAuthorisationInfoMap_whenMapperReturnsEmptyList_shouldReturnEmptyMap() {
        // arrange
        String appId = "testAppId";
        List<String> inviteeUnionIds = Arrays.asList("unionId1", "unionId2");
        when(scrmPersonalWxUserAuthorisationInfoDOMapper.selectByExample(any(ScrmPersonalWxUserAuthorisationInfoDOExample.class)))
            .thenReturn(Collections.emptyList());

        // act
        Map<String, ScrmPersonalWxUserAuthorisationInfoDO> result = service.getUnionIdAuthorisationInfoMap(appId, inviteeUnionIds);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(scrmPersonalWxUserAuthorisationInfoDOMapper).selectByExample(any(ScrmPersonalWxUserAuthorisationInfoDOExample.class));
    }

    /**
     * 测试当Mapper返回包含唯一unionId的数据时返回正确的Map
     */
    @Test
    void testGetUnionIdAuthorisationInfoMap_whenMapperReturnsDataWithUniqueUnionIds_shouldReturnCorrectMap() {
        // arrange
        String appId = "testAppId";
        List<String> inviteeUnionIds = Arrays.asList("unionId1", "unionId2");

        ScrmPersonalWxUserAuthorisationInfoDO authInfo1 = new ScrmPersonalWxUserAuthorisationInfoDO();
        authInfo1.setUnionId("unionId1");
        authInfo1.setUserName("User1");

        ScrmPersonalWxUserAuthorisationInfoDO authInfo2 = new ScrmPersonalWxUserAuthorisationInfoDO();
        authInfo2.setUnionId("unionId2");
        authInfo2.setUserName("User2");

        List<ScrmPersonalWxUserAuthorisationInfoDO> mockList = Arrays.asList(authInfo1, authInfo2);
        when(scrmPersonalWxUserAuthorisationInfoDOMapper.selectByExample(any(ScrmPersonalWxUserAuthorisationInfoDOExample.class)))
            .thenReturn(mockList);

        // act
        Map<String, ScrmPersonalWxUserAuthorisationInfoDO> result = service.getUnionIdAuthorisationInfoMap(appId, inviteeUnionIds);

        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(authInfo1, result.get("unionId1"));
        assertEquals(authInfo2, result.get("unionId2"));
        verify(scrmPersonalWxUserAuthorisationInfoDOMapper).selectByExample(any(ScrmPersonalWxUserAuthorisationInfoDOExample.class));
    }

    /**
     * 测试当Mapper返回包含重复unionId的数据时正确处理重复
     */
    @Test
    void testGetUnionIdAuthorisationInfoMap_whenMapperReturnsDataWithDuplicateUnionIds_shouldHandleDuplicates() {
        // arrange
        String appId = "testAppId";
        List<String> inviteeUnionIds = Arrays.asList("unionId1", "unionId2");

        ScrmPersonalWxUserAuthorisationInfoDO authInfo1_first = new ScrmPersonalWxUserAuthorisationInfoDO();
        authInfo1_first.setUnionId("unionId1");
        authInfo1_first.setUserName("User1_First");

        ScrmPersonalWxUserAuthorisationInfoDO authInfo1_second = new ScrmPersonalWxUserAuthorisationInfoDO();
        authInfo1_second.setUnionId("unionId1");
        authInfo1_second.setUserName("User1_Second");

        ScrmPersonalWxUserAuthorisationInfoDO authInfo2 = new ScrmPersonalWxUserAuthorisationInfoDO();
        authInfo2.setUnionId("unionId2");
        authInfo2.setUserName("User2");

        List<ScrmPersonalWxUserAuthorisationInfoDO> mockList = Arrays.asList(authInfo1_first, authInfo1_second, authInfo2);
        when(scrmPersonalWxUserAuthorisationInfoDOMapper.selectByExample(any(ScrmPersonalWxUserAuthorisationInfoDOExample.class)))
            .thenReturn(mockList);

        // act
        Map<String, ScrmPersonalWxUserAuthorisationInfoDO> result = service.getUnionIdAuthorisationInfoMap(appId, inviteeUnionIds);

        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(authInfo1_first, result.get("unionId1")); // 验证保留第一个
        assertEquals(authInfo2, result.get("unionId2"));
        verify(scrmPersonalWxUserAuthorisationInfoDOMapper).selectByExample(any(ScrmPersonalWxUserAuthorisationInfoDOExample.class));
    }

    /**
     * 测试当appId为空时返回空列表
     */
    @Test
    void testGetInvitationList_whenAppIdIsEmpty_shouldReturnEmptyList() {
        // arrange
        String appId = "";
        Long activityId = 1L;
        String inviterUnionId = "inviter1";

        // act
        List<FriendInfoDTO> result = service.getInvitationList(appId, activityId, inviterUnionId, activity);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(invitationRecordMapper, never()).selectByExample(any());
    }

    /**
     * 测试当activityId为null时返回空列表
     */
    @Test
    void testGetInvitationList_whenActivityIdIsNull_shouldReturnEmptyList() {
        // arrange
        String appId = "testAppId";
        Long activityId = null;
        String inviterUnionId = "inviter1";

        // act
        List<FriendInfoDTO> result = service.getInvitationList(appId, activityId, inviterUnionId, activity);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(invitationRecordMapper, never()).selectByExample(any());
    }

    /**
     * 测试当inviterUnionId为空时返回空列表
     */
    @Test
    void testGetInvitationList_whenInviterUnionIdIsEmpty_shouldReturnEmptyList() {
        // arrange
        String appId = "testAppId";
        Long activityId = 1L;
        String inviterUnionId = "";

        // act
        List<FriendInfoDTO> result = service.getInvitationList(appId, activityId, inviterUnionId, activity);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(invitationRecordMapper, never()).selectByExample(any());
    }

    /**
     * 测试当没有邀请记录时返回空列表
     */
    @Test
    void testGetInvitationList_whenNoInvitationRecords_shouldReturnEmptyList() {
        // arrange
        String appId = "testAppId";
        Long activityId = 1L;
        String inviterUnionId = "inviter1";
        when(invitationRecordMapper.selectByExample(any())).thenReturn(Collections.emptyList());

        // act
        List<FriendInfoDTO> result = service.getInvitationList(appId, activityId, inviterUnionId, activity);

        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
        verify(invitationRecordMapper).selectByExample(any());
        verify(scrmPersonalWxUserAuthorisationInfoDOMapper, never()).selectByExample(any());
    }

    /**
     * 测试NON_WECOM_GROUP类型活动，有邀请记录和授权信息时返回正确的FriendInfoDTO列表
     */
    @Test
    void testGetInvitationList_NonWecomGroup_withValidRecordsAndAuthInfo_shouldReturnCorrectFriendInfoList() {
        // arrange
        String appId = "testAppId";
        Long activityId = 1L;
        String inviterUnionId = "inviter1";
        String inviteeUnionId = "invitee1";

        // 设置活动类型
        when(activity.getActivityType()).thenReturn((byte)4);
        
        // 设置活动属性，使getInvitationStatus返回VALID
        when(activity.getNewUserCheck()).thenReturn(false);
        when(activity.getQuitCheck()).thenReturn(false);
        when(activity.getRiskCheck()).thenReturn(false);
        when(activity.getCopyDynamicChannelInfoStr()).thenReturn(null);

        // 准备邀请记录
        GroupFissionInvitationRecord record = new GroupFissionInvitationRecord();
        record.setInviteeUnionId(inviteeUnionId);
        record.setEnterTime(new Date());
        record.setInviteeMtCityId(0L);
        when(invitationRecordMapper.selectByExample(any())).thenReturn(Collections.singletonList(record));

        // 准备授权信息
        ScrmPersonalWxUserAuthorisationInfoDO authInfo = new ScrmPersonalWxUserAuthorisationInfoDO();
        authInfo.setUnionId(inviteeUnionId);
        authInfo.setUserName("TestUser");
        authInfo.setAvatar("http://test.avatar.com");
        when(scrmPersonalWxUserAuthorisationInfoDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(authInfo));

        // act
        List<FriendInfoDTO> result = service.getInvitationList(appId, activityId, inviterUnionId, activity);

        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        FriendInfoDTO friendInfo = result.get(0);
        assertEquals(authInfo.getAvatar(), friendInfo.getAvatar());
        assertEquals(authInfo.getUserName(), friendInfo.getNickName());
        assertEquals(record.getEnterTime(), friendInfo.getInviteTime());
        assertEquals(InvitationStatusEnum.VALID.getCode(), friendInfo.getInviteStatus());
        
        verify(invitationRecordMapper).selectByExample(any());
        verify(scrmPersonalWxUserAuthorisationInfoDOMapper).selectByExample(any());
    }

    /**
     * 测试NON_WECOM_GROUP类型活动，当授权信息为空时返回的FriendInfoDTO中姓名和头像为null
     */
    @Test
    void testGetInvitationList_NonWecomGroup_whenAuthInfoMapIsEmpty_shouldReturnFriendInfoWithNullNameAvatar() {
        // arrange
        String appId = "testAppId";
        Long activityId = 1L;
        String inviterUnionId = "inviter1";
        String inviteeUnionId = "invitee1";

        // 设置活动类型
        when(activity.getActivityType()).thenReturn((byte)4);
        
        // 设置活动属性，使getInvitationStatus返回VALID
        when(activity.getNewUserCheck()).thenReturn(false);
        when(activity.getQuitCheck()).thenReturn(false);
        when(activity.getRiskCheck()).thenReturn(false);
        when(activity.getCopyDynamicChannelInfoStr()).thenReturn(null);

        // 准备邀请记录
        GroupFissionInvitationRecord record = new GroupFissionInvitationRecord();
        record.setInviteeUnionId(inviteeUnionId);
        record.setEnterTime(new Date());
        record.setInviteeMtCityId(0L);
        when(invitationRecordMapper.selectByExample(any())).thenReturn(Collections.singletonList(record));

        // 设置授权信息为空
        when(scrmPersonalWxUserAuthorisationInfoDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());

        // act
        List<FriendInfoDTO> result = service.getInvitationList(appId, activityId, inviterUnionId, activity);

        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        FriendInfoDTO friendInfo = result.get(0);
        assertNull(friendInfo.getAvatar());
        assertNull(friendInfo.getNickName());
        assertEquals(record.getEnterTime(), friendInfo.getInviteTime());
        assertEquals(InvitationStatusEnum.VALID.getCode(), friendInfo.getInviteStatus());
        
        verify(invitationRecordMapper).selectByExample(any());
        verify(scrmPersonalWxUserAuthorisationInfoDOMapper).selectByExample(any());
    }

    /**
     * 测试NON_WECOM_GROUP类型活动，不同的邀请状态
     */
    @Test
    void testGetInvitationList_NonWecomGroup_withDifferentInvitationStatus_shouldReflectInOutput() {
        // arrange
        String appId = "testAppId";
        Long activityId = 1L;
        String inviterUnionId = "inviter1";
        String inviteeUnionId = "invitee1";

        // 设置活动类型
        when(activity.getActivityType()).thenReturn((byte)4);
        
        // 设置活动属性，使getInvitationStatus返回INVALID_OLD_USER
        when(activity.getNewUserCheck()).thenReturn(true);
        when(activity.getQuitCheck()).thenReturn(false);
        when(activity.getRiskCheck()).thenReturn(false);
        when(activity.getCopyDynamicChannelInfoStr()).thenReturn(null);

        // 准备邀请记录
        GroupFissionInvitationRecord record = new GroupFissionInvitationRecord();
        record.setInviteeUnionId(inviteeUnionId);
        record.setEnterTime(new Date());
        record.setInviteeMtCityId(0L);
        record.setNewUser(false); // 设置为老用户
        when(invitationRecordMapper.selectByExample(any())).thenReturn(Collections.singletonList(record));

        // 准备授权信息
        ScrmPersonalWxUserAuthorisationInfoDO authInfo = new ScrmPersonalWxUserAuthorisationInfoDO();
        authInfo.setUnionId(inviteeUnionId);
        authInfo.setUserName("TestUser");
        authInfo.setAvatar("http://test.avatar.com");
        when(scrmPersonalWxUserAuthorisationInfoDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(authInfo));

        // act
        List<FriendInfoDTO> result = service.getInvitationList(appId, activityId, inviterUnionId, activity);

        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        FriendInfoDTO friendInfo = result.get(0);
        assertEquals(authInfo.getAvatar(), friendInfo.getAvatar());
        assertEquals(authInfo.getUserName(), friendInfo.getNickName());
        assertEquals(record.getEnterTime(), friendInfo.getInviteTime());
        assertEquals(InvitationStatusEnum.INVALID_OLD_USER.getCode(), friendInfo.getInviteStatus());
        
        verify(invitationRecordMapper).selectByExample(any());
        verify(scrmPersonalWxUserAuthorisationInfoDOMapper).selectByExample(any());
    }
}
