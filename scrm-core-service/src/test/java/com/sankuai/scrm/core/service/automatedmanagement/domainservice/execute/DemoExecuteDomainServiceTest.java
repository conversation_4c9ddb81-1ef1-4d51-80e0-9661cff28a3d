package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAMRealtimeSceneAndProcessMapDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAMRealtimeSceneAndProcessMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.infrastructure.dal.babymapper.ContactUserDoMapper;
import com.sankuai.scrm.core.service.infrastructure.dal.entity.ContactUserDo;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.TimeoutException;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DemoExecuteDomainServiceTest {

    @InjectMocks
    private DemoExecuteDomainService demoExecuteDomainService;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock
    private ContactUserDoMapper contactUserDoMapper;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private ExecuteWriteDomainService executeWriteDomainService;

    // private MockedStatic<Environment> mockedEnvironment;

    private static final Long PROCESS_ID = 123L;

    private static final String WX_UNION_ID = "test_union_id";

    private static final String VALID_VERSION = "1.0";

    @Mock
    private ScrmAMRealtimeSceneAndProcessMapDOMapper scrmAMRealtimeSceneAndProcessMapDOMapper;

    private static final Long SCENE_ID = 1L;

    private static final Long PROCESS_ORCHESTRATION_ID = 100L;

    @Before
    public void setUp() {
        // Set up dependencies using ReflectionTestUtils
        ReflectionTestUtils.setField(demoExecuteDomainService, "scrmAMRealtimeSceneAndProcessMapDOMapper", scrmAMRealtimeSceneAndProcessMapDOMapper);
        ReflectionTestUtils.setField(demoExecuteDomainService, "processOrchestrationReadDomainService", processOrchestrationReadDomainService);
        ReflectionTestUtils.setField(demoExecuteDomainService, "executeManagementService", executeManagementService);
        ReflectionTestUtils.setField(demoExecuteDomainService, "executeWriteDomainService", executeWriteDomainService);
    }

   /* @After
    public void after() {
        mockedEnvironment.close();
        mockedEnvironment.
    }*/

    private ScrmProcessOrchestrationDTO createOrchestrationDTO() {
        ScrmProcessOrchestrationDTO orchestrationDTO = new ScrmProcessOrchestrationDTO();
        orchestrationDTO.setId(PROCESS_ID);
        orchestrationDTO.setValidVersion(VALID_VERSION);
        // Create node medium DTO
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        // Create root node
        ScrmProcessOrchestrationNodeDTO rootNode = new ScrmProcessOrchestrationNodeDTO();
        rootNode.setNodeId(0L);
        rootNode.setChildrenNodes(Arrays.asList(1L));
        // Add nodes to medium DTO
        List<ScrmProcessOrchestrationNodeDTO> nodeDTOList = new ArrayList<>();
        nodeDTOList.add(rootNode);
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(nodeDTOList);
        orchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        return orchestrationDTO;
    }

    /**
     * Test when processOrchestrationId is not in white list
     */
    @Test
    public void testRunNormalTaskDemo_NotInWhiteList() throws Throwable {
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            // arrange
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getList(eq("test-app"), eq("refinement.operation.demo.white.list"), eq(Long.class))).thenReturn(Arrays.asList(456L, 789L));
            // act
            StepExecuteResultDTO result = demoExecuteDomainService.runNormalTaskDemo(PROCESS_ID, WX_UNION_ID);
            // assert
            assertFalse(result.isSuccess());
            assertEquals((Integer) ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getCode(), result.getCode());
            assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getDesc(), result.getMsg());
        }
    }

    /**
     * Test when contact user exists
     */
    @Test
    public void testRunNormalTaskDemo_ContactUserExists() throws Throwable {
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            // arrange
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getList(eq("test-app"), eq("refinement.operation.demo.white.list"), eq(Long.class))).thenReturn(Arrays.asList(PROCESS_ID, 789L));
            ScrmProcessOrchestrationDTO orchestrationDTO = createOrchestrationDTO();
            CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(orchestrationDTO);
            when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(PROCESS_ID)).thenReturn(future);
            ContactUserDo contactUserDo = new ContactUserDo();
            contactUserDo.setExternalUserId("ext_user_id");
            when(contactUserDoMapper.selectByExample(any())).thenReturn(Arrays.asList(contactUserDo));
            StepExecuteResultDTO expectedResult = new StepExecuteResultDTO();
            when(executeWriteDomainService.processNodes(any(), any(), any(), any(), any(), any())).thenReturn(expectedResult);
            // act
            StepExecuteResultDTO result = demoExecuteDomainService.runNormalTaskDemo(PROCESS_ID, WX_UNION_ID);
            // assert
            assertSame(expectedResult, result);
            verify(executeWriteDomainService).thirdWxGroupTouchStep(eq(orchestrationDTO), any(), eq(expectedResult));
            verify(executeWriteDomainService).noticeStep(eq(orchestrationDTO), any());
            verify(executeWriteDomainService).updateNodeExecuteLog(eq(orchestrationDTO), any());
        }
    }

    /**
     * Test when contact user not exists but member info exists
     */
    @Test
    public void testRunNormalTaskDemo_MemberInfoExists() throws Throwable {
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            // arrange
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getList(eq("test-app"), eq("refinement.operation.demo.white.list"), eq(Long.class))).thenReturn(Arrays.asList(PROCESS_ID));
            ScrmProcessOrchestrationDTO orchestrationDTO = createOrchestrationDTO();
            CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(orchestrationDTO);
            when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(PROCESS_ID)).thenReturn(future);
            when(contactUserDoMapper.selectByExample(any())).thenReturn(new ArrayList<>());
            MemberInfoEntity memberInfo = new MemberInfoEntity();
            memberInfo.setGroupMemberId("group_member_id");
            when(executeManagementService.getUsersGroupList(WX_UNION_ID, "hanchengfuwu")).thenReturn(Arrays.asList(memberInfo));
            StepExecuteResultDTO expectedResult = new StepExecuteResultDTO();
            when(executeWriteDomainService.processNodes(any(), any(), any(), any(), any(), any())).thenReturn(expectedResult);
            // act
            StepExecuteResultDTO result = demoExecuteDomainService.runNormalTaskDemo(PROCESS_ID, WX_UNION_ID);
            // assert
            assertSame(expectedResult, result);
            verify(executeWriteDomainService).thirdWxGroupTouchStep(eq(orchestrationDTO), any(), eq(expectedResult));
            verify(executeWriteDomainService).noticeStep(eq(orchestrationDTO), any());
            verify(executeWriteDomainService).updateNodeExecuteLog(eq(orchestrationDTO), any());
        }
    }

    /**
     * Test when both contact user and member info not exist
     */
    @Test
    public void testRunNormalTaskDemo_NoUserInfo() throws Throwable {
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            // arrange
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getList(eq("test-app"), eq("refinement.operation.demo.white.list"), eq(Long.class))).thenReturn(Arrays.asList(PROCESS_ID));
            ScrmProcessOrchestrationDTO orchestrationDTO = createOrchestrationDTO();
            CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(orchestrationDTO);
            when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(PROCESS_ID)).thenReturn(future);
            when(contactUserDoMapper.selectByExample(any())).thenReturn(new ArrayList<>());
            when(executeManagementService.getUsersGroupList(WX_UNION_ID, "hanchengfuwu")).thenReturn(new ArrayList<>());
            StepExecuteResultDTO expectedResult = new StepExecuteResultDTO();
            when(executeWriteDomainService.processNodes(any(), any(), any(), any(), any(), any())).thenReturn(expectedResult);
            // act
            StepExecuteResultDTO result = demoExecuteDomainService.runNormalTaskDemo(PROCESS_ID, WX_UNION_ID);
            // assert
            assertSame(expectedResult, result);
            verify(executeWriteDomainService).thirdWxGroupTouchStep(eq(orchestrationDTO), any(), eq(expectedResult));
            verify(executeWriteDomainService).noticeStep(eq(orchestrationDTO), any());
            verify(executeWriteDomainService).updateNodeExecuteLog(eq(orchestrationDTO), any());
        }
    }

    /**
     * Test when query process orchestration times out
     */
    @Test(expected = TimeoutException.class)
    public void testRunNormalTaskDemo_QueryTimeout() throws Throwable {
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            // arrange
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getList(eq("test-app"), eq("refinement.operation.demo.white.list"), eq(Long.class))).thenReturn(Arrays.asList(PROCESS_ID));
            CompletableFuture<ScrmProcessOrchestrationDTO> future = new CompletableFuture<>();
            when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(PROCESS_ID)).thenReturn(future);
            // act
            demoExecuteDomainService.runNormalTaskDemo(PROCESS_ID, WX_UNION_ID);
        }
    }

    /**
     * Test case for successful execution when process orchestration exists
     */
    @Test
    public void testRunRealTimeTaskDemo_Success() throws Throwable {
        // arrange
        ScrmAMRealtimeSceneAndProcessMapDO mapDO = new ScrmAMRealtimeSceneAndProcessMapDO();
        mapDO.setProcessOrchestrationId(PROCESS_ORCHESTRATION_ID);
        List<ScrmAMRealtimeSceneAndProcessMapDO> mapDOList = Arrays.asList(mapDO);
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any())).thenReturn(mapDOList);
        ScrmProcessOrchestrationDTO orchestrationDTO = new ScrmProcessOrchestrationDTO();
        orchestrationDTO.setValidVersion("1.0");
        orchestrationDTO.setId(PROCESS_ORCHESTRATION_ID);
        // Set up NodeMediumDTO
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        ScrmProcessOrchestrationNodeDTO rootNode = new ScrmProcessOrchestrationNodeDTO();
        rootNode.setNodeId(0L);
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(Arrays.asList(rootNode));
        orchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        ExecuteManagementDTO managementDTO = new ExecuteManagementDTO();
        StepExecuteResultDTO expectedResult = new StepExecuteResultDTO();
        expectedResult.setSuccess(true);
        // act
        StepExecuteResultDTO result = demoExecuteDomainService.runRealTimeTaskDemo(SCENE_ID, WX_UNION_ID);
        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals((int) ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getCode(), (int) result.getCode());
    }

    /**
     * Test case for when scene mapping is not found
     */
    @Test
    public void testRunRealTimeTaskDemo_SceneMappingNotFound() throws Throwable {
        // arrange
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        // act & assert
        assertThrows(IndexOutOfBoundsException.class, () -> {
            demoExecuteDomainService.runRealTimeTaskDemo(SCENE_ID, WX_UNION_ID);
        });
    }

    /**
     * Test case for when process orchestration is not found
     */
    @Test
    public void testRunRealTimeTaskDemo_ProcessOrchestrationNotFound() throws Throwable {
        // arrange
        ScrmAMRealtimeSceneAndProcessMapDO mapDO = new ScrmAMRealtimeSceneAndProcessMapDO();
        mapDO.setProcessOrchestrationId(PROCESS_ORCHESTRATION_ID);
        List<ScrmAMRealtimeSceneAndProcessMapDO> mapDOList = Arrays.asList(mapDO);
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any())).thenReturn(mapDOList);
        // act
        StepExecuteResultDTO result = demoExecuteDomainService.runRealTimeTaskDemo(SCENE_ID, WX_UNION_ID);
        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals((int) ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getCode(), (int) result.getCode());
    }

    /**
     * Test case for when scene is not in whitelist
     */
    @Test
    public void testRunRealTimeTaskDemo_NotInWhitelist() throws Throwable {
        // arrange
        ScrmAMRealtimeSceneAndProcessMapDO mapDO = new ScrmAMRealtimeSceneAndProcessMapDO();
        mapDO.setProcessOrchestrationId(PROCESS_ORCHESTRATION_ID);
        List<ScrmAMRealtimeSceneAndProcessMapDO> mapDOList = Arrays.asList(mapDO);
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any())).thenReturn(mapDOList);
        // act
        StepExecuteResultDTO result = demoExecuteDomainService.runRealTimeTaskDemo(SCENE_ID, WX_UNION_ID);
        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals((int) ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getCode(), (int) result.getCode());
        // Verify that no further processing occurred
        verify(executeWriteDomainService, never()).executeRealTimeTask(any(), any(), any(), any(), any(), any());
        verify(executeWriteDomainService, never()).secondStaffTaskAssignStep(any(), any(), any(), any());
        verify(executeWriteDomainService, never()).thirdWxGroupTouchStep(any(), any(), any());
        verify(executeWriteDomainService, never()).updateNodeExecuteLog(any(), any());
    }

    /**
            * 测试getTagGroupId方法在非生产环境下的返回值
     */
    @Test
    public void testGetTagGroupIdNonProdEnv() {
        try(MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class)){
            // arrange
            mockedEnvironment.when(Environment::isProductEnv).thenReturn(false);

            // act
            String result = DemoExecuteDomainService.getTagGroupId();

            // assert
            assertEquals("etdfWXVQAAfQRaEUyspNG53pGLj91ELQ", result);
        }
    }

    /**
     * 测试getTagGroupId方法在生产环境下的返回值
     */
    @Test
    public void testGetTagGroupIdProdEnv() {
        try(MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class)){
            // arrange
            mockedEnvironment.when(Environment::isProductEnv).thenReturn(true);

            // act
            String result = DemoExecuteDomainService.getTagGroupId();

            // assert
            assertEquals("etdfWXVQAAvwqwhbwFFZ--j5Fmgnluyg", result);
        }
    }

    /**
     * 测试getTagGroupId方法在环境变量未正确设置时的行为
     * 注意：这个测试用例是基于对Environment类行为的假设，实际上Environment.isProductEnv()的实现可能不会导致未设置环境变量的情况。
     * 但为了完整性和示例，这里假设存在这样的情况。
     */
    @Test(expected = RuntimeException.class)
    public void testGetTagGroupIdWithUnsetEnvironment() {
        try(MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class)){
            // arrange
            mockedEnvironment.when(Environment::isProductEnv).thenThrow(new RuntimeException("Environment variable not set"));

            // act
            DemoExecuteDomainService.getTagGroupId();

            // assert
            // Expected exception
        }
    }
}