package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.dianping.tgc.process.enums.PlatformEnum;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.aigc.service.config.AISceneABTestConfig;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineMqMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.dto.ScrmDzBizDataUserTrackDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineUserFootprintDiversionProducer;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class DzBizDataUserTrackViewConsumerRecvMessageTest {

    @Mock
    private RealTimeTaskConsumerConfig consumerConfig;

    @Mock
    private GroupRetailAiEngineUserFootprintDiversionProducer userFootprintDiversionProducer;

    @Mock
    private AISceneABTestConfig aiSceneABTestConfig;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @InjectMocks
    private DzBizDataUserTrackViewConsumer consumer;

    private Method recvMessageMethod;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        // Get the private method via reflection
        recvMessageMethod = DzBizDataUserTrackViewConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        recvMessageMethod.setAccessible(true);
    }

    /**
     * Test empty message body returns CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessageEmptyMessageBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", null);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test empty userid list returns CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessageEmptyUserIdList() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO dto = new ScrmDzBizDataUserTrackDTO();
        dto.setUserid(Collections.emptyList());
        String json = JsonUtils.toStr(dto);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", json);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test MT platform user in whitelist sends footprint message
     */
    @Test
    public void testRecvMessageMtUserInWhitelist() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO dto = new ScrmDzBizDataUserTrackDTO();
        dto.setUserid(Arrays.asList(123L));
        dto.setPlatformlist(Arrays.asList("mt"));
        String json = JsonUtils.toStr(dto);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", json);
        MessagetContext context = new MessagetContext();
        when(consumerConfig.isInWhitelistAppIdUnknown(anyList(), any(PlatformEnum.class))).thenReturn(true);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootprintDiversionProducer).sendMessage(any(GroupRetailAiEngineMqMessageDTO.class));
    }

    /**
     * Test DP platform user in whitelist sends footprint message
     */
    @Test
    public void testRecvMessageDpUserInWhitelist() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO dto = new ScrmDzBizDataUserTrackDTO();
        dto.setUserid(Arrays.asList(123L));
        dto.setPlatformlist(Arrays.asList("dp"));
        String json = JsonUtils.toStr(dto);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", json);
        MessagetContext context = new MessagetContext();
        when(consumerConfig.isInWhitelistAppIdUnknown(anyList(), any(PlatformEnum.class))).thenReturn(true);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootprintDiversionProducer).sendMessage(any(GroupRetailAiEngineMqMessageDTO.class));
    }

    /**
     * Test user not in whitelist but in control group sends AB test message
     */
    @Test
    public void testRecvMessageUserInControlGroup() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO dto = new ScrmDzBizDataUserTrackDTO();
        dto.setUserid(Arrays.asList(123L));
        dto.setPlatformlist(Arrays.asList("mt"));
        String json = JsonUtils.toStr(dto);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", json);
        MessagetContext context = new MessagetContext();
        when(consumerConfig.isInWhitelistAppIdUnknown(anyList(), any(PlatformEnum.class))).thenReturn(false);
        when(aiSceneABTestConfig.checkIsInNonWxUserControlGroup()).thenReturn(true);
        when(aiSceneABTestConfig.getTestVersion()).thenReturn("v1");
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(aiSceneABTestConfig).checkIsInNonWxUserControlGroup();
    }

    /**
     * Test DP user not in whitelist but in control group with successful MT user conversion
     */
    @Test
    public void testRecvMessageDpUserInControlGroupWithMtConversion() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO dto = new ScrmDzBizDataUserTrackDTO();
        dto.setUserid(Arrays.asList(123L));
        dto.setPlatformlist(Arrays.asList("dp"));
        String json = JsonUtils.toStr(dto);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", json);
        MessagetContext context = new MessagetContext();
        when(consumerConfig.isInWhitelistAppIdUnknown(anyList(), any(PlatformEnum.class))).thenReturn(false);
        when(aiSceneABTestConfig.checkIsInNonWxUserControlGroup()).thenReturn(true);
        when(aiSceneABTestConfig.getTestVersion()).thenReturn("v1");
        when(mtUserCenterAclService.getMtUserIdByDpUserId(123L)).thenReturn(456L);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(mtUserCenterAclService).getMtUserIdByDpUserId(123L);
    }

    /**
     * Test DP user not in whitelist but in control group with failed MT user conversion
     */
    @Test
    public void testRecvMessageDpUserInControlGroupWithFailedMtConversion() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO dto = new ScrmDzBizDataUserTrackDTO();
        dto.setUserid(Arrays.asList(123L));
        dto.setPlatformlist(Arrays.asList("dp"));
        String json = JsonUtils.toStr(dto);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", json);
        MessagetContext context = new MessagetContext();
        when(consumerConfig.isInWhitelistAppIdUnknown(anyList(), any(PlatformEnum.class))).thenReturn(false);
        when(aiSceneABTestConfig.checkIsInNonWxUserControlGroup()).thenReturn(true);
        when(mtUserCenterAclService.getMtUserIdByDpUserId(123L)).thenReturn(null);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(mtUserCenterAclService).getMtUserIdByDpUserId(123L);
    }

    /**
     * Test invalid JSON message returns CONSUME_SUCCESS with error logged
     */
    @Test
    public void testRecvMessageInvalidJson() throws Throwable {
        // arrange
        String invalidJson = "{invalid}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", invalidJson);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test exception during processing returns CONSUME_SUCCESS with error logged
     */
    @Test
    public void testRecvMessageProcessingException() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO dto = new ScrmDzBizDataUserTrackDTO();
        dto.setUserid(Arrays.asList(123L));
        dto.setPlatformlist(Arrays.asList("mt"));
        String json = JsonUtils.toStr(dto);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, "key", json);
        MessagetContext context = new MessagetContext();
        when(consumerConfig.isInWhitelistAppIdUnknown(anyList(), any(PlatformEnum.class))).thenThrow(new RuntimeException("Test exception"));
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }
}
