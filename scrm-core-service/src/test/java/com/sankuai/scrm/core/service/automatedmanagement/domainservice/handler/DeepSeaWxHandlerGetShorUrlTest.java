package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DeepSeaWxHandlerGetShorUrlTest {

    @Mock
    private ShortLinkUtils shortLinkUtils;

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @InjectMocks
    private DeepSeaWxHandler deepSeaWxHandler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test scenario: Record exists with various non-WAIT_FOR_CREATE status values
     * Expected: Should return false for all status values except 1
     * Tests boundary values and common status values
     */
    @ParameterizedTest(name = "Test checkMessageNotSend with status = {0}")
    @ValueSource(bytes = { 0, 2, 3, 4, 5, 6, 7, 8, 9, 10, 127, -128 })
    @DisplayName("Test checkMessageNotSend with different status values")
    void testCheckMessageNotSendWithDifferentStatus(byte status) {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setId(1L);
        ScrmAmProcessOrchestrationWxInvokeDetailDO dbDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        dbDetailDO.setStatus(status);
        when(wxInvokeDetailDOMapper.selectByPrimaryKey(1L)).thenReturn(dbDetailDO);
        // act
        boolean result = deepSeaWxHandler.checkMessageNotSend(detailDO);
        // assert
        assertFalse(result, String.format("Should return false for status value: %d", status));
        verify(wxInvokeDetailDOMapper).selectByPrimaryKey(1L);
    }

    /**
     * Test when coreUrl is blank
     */
    @Test
    void testGetShorUrlCoreUrlIsBlank() throws Throwable {
        // arrange
        String blankUrl = "";
        // act
        String result = officialWxHandler.getShorUrl(blankUrl, false);
        // assert
        assertEquals(blankUrl, result);
    }

    /**
     * Test when coreUrl doesn't start with http
     */
    @Test
    void testGetShorUrlCoreUrlNotStartWithHttp() throws Throwable {
        // arrange
        String invalidUrl = "ftp://test.com";
        // act
        String result = officialWxHandler.getShorUrl(invalidUrl, false);
        // assert
        assertEquals(invalidUrl, result);
    }

    /**
     * Test when coreUrl length is less than 400 and forceShortUrl is false
     */
    @Test
    void testGetShorUrlCoreUrlLengthLessThan400AndForceShortUrlFalse() throws Throwable {
        // arrange
        String shortUrl = "http://test.com";
        // act
        String result = officialWxHandler.getShorUrl(shortUrl, false);
        // assert
        assertEquals(shortUrl, result);
    }

    /**
     * Test when coreUrl length is greater than or equal to 400 and forceShortUrl is false
     */
    @Test
    void testGetShorUrlCoreUrlLengthGreaterThan400AndForceShortUrlFalse() throws Throwable {
        // arrange
        String longUrl = "http://test.com/" + new String(new char[400]);
        String expectedShortUrl = "http://short.url";
        when(shortLinkUtils.getShortLink(longUrl, 90)).thenReturn(expectedShortUrl);
        // act
        String result = officialWxHandler.getShorUrl(longUrl, false);
        // assert
        assertEquals(expectedShortUrl, result);
        verify(shortLinkUtils).getShortLink(longUrl, 90);
    }

    /**
     * Test when coreUrl length is less than 400 and forceShortUrl is true
     */
    @Test
    void testGetShorUrlCoreUrlLengthLessThan400AndForceShortUrlTrue() throws Throwable {
        // arrange
        String shortUrl = "http://test.com";
        String expectedShortUrl = "http://short.url";
        when(shortLinkUtils.getShortLink(shortUrl, 90)).thenReturn(expectedShortUrl);
        // act
        String result = officialWxHandler.getShorUrl(shortUrl, true);
        // assert
        assertEquals(expectedShortUrl, result);
        verify(shortLinkUtils).getShortLink(shortUrl, 90);
    }

    /**
     * Test when coreUrl length is greater than or equal to 400 and forceShortUrl is true
     */
    @Test
    void testGetShorUrlCoreUrlLengthGreaterThan400AndForceShortUrlTrue() throws Throwable {
        // arrange
        String longUrl = "http://test.com/" + new String(new char[400]);
        String expectedShortUrl = "http://short.url";
        when(shortLinkUtils.getShortLink(longUrl, 90)).thenReturn(expectedShortUrl);
        // act
        String result = officialWxHandler.getShorUrl(longUrl, true);
        // assert
        assertEquals(expectedShortUrl, result);
        verify(shortLinkUtils).getShortLink(longUrl, 90);
    }

    /**
     * Test when shortLinkUtils.getShortLink() throws an exception
     */
    @Test
    void testGetShorUrlShortLinkUtilsThrowsException() throws Throwable {
        // arrange
        String longUrl = "http://test.com/" + new String(new char[400]);
        when(shortLinkUtils.getShortLink(longUrl, 90)).thenThrow(new RuntimeException("创建短链失败，响应为空"));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            officialWxHandler.getShorUrl(longUrl, false);
        });
        verify(shortLinkUtils).getShortLink(longUrl, 90);
    }

    /**
     * Test scenario: Input parameter is null
     * Expected: Should return false and not call mapper
     */
    @Test
    @DisplayName("Test checkMessageNotSend with null input")
    void testCheckMessageNotSendWithNullInput() throws Throwable {
        // arrange & act
        boolean result = deepSeaWxHandler.checkMessageNotSend(null);
        // assert
        assertFalse(result, "Should return false when input is null");
        verify(wxInvokeDetailDOMapper, never()).selectByPrimaryKey(any());
    }

    /**
     * Test scenario: Database record is not found
     * Expected: Should return false when no record exists in database
     */
    @Test
    @DisplayName("Test checkMessageNotSend when record not found in database")
    void testCheckMessageNotSendWithNoRecordFound() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setId(1L);
        when(wxInvokeDetailDOMapper.selectByPrimaryKey(1L)).thenReturn(null);
        // act
        boolean result = deepSeaWxHandler.checkMessageNotSend(detailDO);
        // assert
        assertFalse(result, "Should return false when no record found in database");
        verify(wxInvokeDetailDOMapper).selectByPrimaryKey(1L);
    }

    /**
     * Test scenario: Record exists with WAIT_FOR_CREATE status
     * Expected: Should return true for status = 1 (WAIT_FOR_CREATE)
     */
    @Test
    @DisplayName("Test checkMessageNotSend with WAIT_FOR_CREATE status")
    void testCheckMessageNotSendWithWaitForCreateStatus() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setId(1L);
        ScrmAmProcessOrchestrationWxInvokeDetailDO dbDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        // WAIT_FOR_CREATE status
        dbDetailDO.setStatus((byte) 1);
        when(wxInvokeDetailDOMapper.selectByPrimaryKey(1L)).thenReturn(dbDetailDO);
        // act
        boolean result = deepSeaWxHandler.checkMessageNotSend(detailDO);
        // assert
        assertTrue(result, "Should return true when status is WAIT_FOR_CREATE");
        verify(wxInvokeDetailDOMapper).selectByPrimaryKey(1L);
    }

    /**
     * Test when record status is not WAIT_FOR_CREATE
     */
    @Test
    void testCheckMessageNotSendWithNonWaitForCreateStatus() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setId(1L);
        ScrmAmProcessOrchestrationWxInvokeDetailDO dbDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        // Any status other than WAIT_FOR_CREATE
        dbDetailDO.setStatus((byte) 2);
        when(wxInvokeDetailDOMapper.selectByPrimaryKey(1L)).thenReturn(dbDetailDO);
        // act
        boolean result = deepSeaWxHandler.checkMessageNotSend(detailDO);
        // assert
        assertFalse(result, "Should return false when status is not WAIT_FOR_CREATE");
        verify(wxInvokeDetailDOMapper).selectByPrimaryKey(1L);
    }
}
