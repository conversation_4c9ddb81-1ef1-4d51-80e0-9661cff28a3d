package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.same;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.OfficialWxHandler;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

class CrowdFriendTouchActionCheckStatusV2Test {

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private OfficialWxHandler officialWxHandler;

    @InjectMocks
    private CrowdFriendTouchAction crowdFriendTouchAction;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // 设置基本的mock行为
        doNothing().when(officialWxHandler).checkOfficialWxStatusV2(any(), any(), any(), any());
    }

    /**
     * Test case for empty result from database query
     */
    @Test
    public void testCheckStatusV2WithEmptyInvokeLogDOS() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(new ArrayList<>());
        // act
        crowdFriendTouchAction.checkStatusV2(processOrchestrationDTO);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        verify(officialWxHandler, never()).checkOfficialWxStatusV2(any(), any(), any(), any());
    }

    /**
     * Test case for single MESSAGE type entry
     */
    @Test
    public void testCheckStatusV2WithSingleMessageTypeEntry() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setType("message");
        invokeLogDO.setStatus((byte) 5);
        invokeLogDO.setProcessOrchestrationId(1L);
        invokeLogDO.setProcessOrchestrationVersion("1.0");
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(Arrays.asList(invokeLogDO));
        // act
        crowdFriendTouchAction.checkStatusV2(processOrchestrationDTO);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        verify(officialWxHandler, times(1)).checkOfficialWxStatusV2(same(processOrchestrationDTO), eq(1L), eq("1.0"), same(invokeLogDO));
    }

    /**
     * Test case for single GROUP_MESSAGE type entry
     */
    @Test
    public void testCheckStatusV2WithSingleGroupMessageTypeEntry() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setType("groupMessage");
        invokeLogDO.setStatus((byte) 5);
        invokeLogDO.setProcessOrchestrationId(1L);
        invokeLogDO.setProcessOrchestrationVersion("1.0");
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(Arrays.asList(invokeLogDO));
        // act
        crowdFriendTouchAction.checkStatusV2(processOrchestrationDTO);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        verify(officialWxHandler, times(1)).checkOfficialWxStatusV2(same(processOrchestrationDTO), eq(1L), eq("1.0"), same(invokeLogDO));
    }

    /**
     * Test case for multiple entries of different types
     */
    @Test
    public void testCheckStatusV2WithMultipleEntries() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO1 = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO1.setType("message");
        invokeLogDO1.setStatus((byte) 5);
        invokeLogDO1.setProcessOrchestrationId(1L);
        invokeLogDO1.setProcessOrchestrationVersion("1.0");
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO2 = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO2.setType("groupMessage");
        invokeLogDO2.setStatus((byte) 5);
        invokeLogDO2.setProcessOrchestrationId(1L);
        invokeLogDO2.setProcessOrchestrationVersion("1.0");
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO3 = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO3.setType("UNSUPPORTED_TYPE");
        invokeLogDO3.setStatus((byte) 5);
        invokeLogDO3.setProcessOrchestrationId(1L);
        invokeLogDO3.setProcessOrchestrationVersion("1.0");
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> invokeLogDOS = Arrays.asList(invokeLogDO1, invokeLogDO2, invokeLogDO3);
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(invokeLogDOS);
        // act
        crowdFriendTouchAction.checkStatusV2(processOrchestrationDTO);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        verify(officialWxHandler, times(1)).checkOfficialWxStatusV2(same(processOrchestrationDTO), eq(1L), eq("1.0"), same(invokeLogDO1));
        verify(officialWxHandler, times(1)).checkOfficialWxStatusV2(same(processOrchestrationDTO), eq(1L), eq("1.0"), same(invokeLogDO2));
        verifyNoMoreInteractions(officialWxHandler);
    }

    /**
     * Test case for entry with status other than WAIT_FOR_SEND
     */
    @Test
    public void testCheckStatusV2WithNonWaitForSendStatus() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setType("message");
        // 非 WAIT_FOR_SEND 状态
        invokeLogDO.setStatus((byte) 6);
        invokeLogDO.setProcessOrchestrationId(1L);
        invokeLogDO.setProcessOrchestrationVersion("1.0");
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> invokeLogDOS = Arrays.asList(invokeLogDO);
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(invokeLogDOS);
        // act
        crowdFriendTouchAction.checkStatusV2(processOrchestrationDTO);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        verify(officialWxHandler, times(1)).checkOfficialWxStatusV2(same(processOrchestrationDTO), eq(1L), eq("1.0"), same(invokeLogDO));
    }
}
