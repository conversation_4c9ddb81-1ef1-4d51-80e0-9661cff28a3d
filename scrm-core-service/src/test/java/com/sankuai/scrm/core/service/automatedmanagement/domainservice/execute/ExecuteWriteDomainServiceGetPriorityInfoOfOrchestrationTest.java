package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.education.lab.base.api.EduDaxiangSendService;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.activity.fission.dto.activity.MktCouponInfoDTO;
import com.sankuai.dz.srcm.activity.fission.service.ActivityFissionService;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.AutoActionContentDetailDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActionContentDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActionDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationActionContentDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationActionDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.PriorityInfoDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.junit.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
public class ExecuteWriteDomainServiceGetPriorityInfoOfOrchestrationTest {

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock
    private ScrmAmProcessOrchestrationActionDOMapper actionDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationActionContentDOMapper actionContentDOMapper;

    @Mock
    private ActivityFissionService activityFissionService;

    private Method getPriorityInfoMethod;

    private ScrmAmProcessOrchestrationInfoDO infoDO;

    // Helper class for test
    public static class AutoActionContentDetailDTO {

        private String couponGroupId;

        public String getCouponGroupId() {
            return couponGroupId;
        }

        public void setCouponGroupId(String couponGroupId) {
            this.couponGroupId = couponGroupId;
        }
    }

    @Before
    public void setUp() throws Exception {
        getPriorityInfoMethod = ExecuteWriteDomainService.class.getDeclaredMethod("getPriorityInfoOfOrchestration", ScrmAmProcessOrchestrationInfoDO.class);
        getPriorityInfoMethod.setAccessible(true);
        infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setValidVersion("1.0");
    }

    @Test
    public void testGetPriorityInfoOfOrchestration_NullInput() throws Throwable {
        PriorityInfoDTO result = (PriorityInfoDTO) getPriorityInfoMethod.invoke(executeWriteDomainService, (Object) null);
        assertNull(result);
    }

    @Test
    public void testGetPriorityInfoOfOrchestration_EmptyActions() throws Throwable {
        when(actionDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        PriorityInfoDTO result = (PriorityInfoDTO) getPriorityInfoMethod.invoke(executeWriteDomainService, infoDO);
        assertNull(result);
    }

    @Test
    public void testGetPriorityInfoOfOrchestration_NoCouponDistributionActions() throws Throwable {
        List<ScrmAmProcessOrchestrationActionDO> actions = new ArrayList<>();
        actions.add(ScrmAmProcessOrchestrationActionDO.builder().actionType((byte) 1).build());
        when(actionDOMapper.selectByExample(any())).thenReturn(actions);
        PriorityInfoDTO result = (PriorityInfoDTO) getPriorityInfoMethod.invoke(executeWriteDomainService, infoDO);
        assertNull(result);
    }

    @Test
    public void testGetPriorityInfoOfOrchestration_EmptyActionContent() throws Throwable {
        List<ScrmAmProcessOrchestrationActionDO> actions = new ArrayList<>();
        actions.add(ScrmAmProcessOrchestrationActionDO.builder().actionType((byte) 8).processOrchestrationNodeId(1L).build());
        when(actionDOMapper.selectByExample(any())).thenReturn(actions);
        when(actionContentDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        PriorityInfoDTO result = (PriorityInfoDTO) getPriorityInfoMethod.invoke(executeWriteDomainService, infoDO);
        assertNull(result);
    }

    @Test
    public void testGetPriorityInfoOfOrchestration_BlankContent() throws Throwable {
        List<ScrmAmProcessOrchestrationActionDO> actions = new ArrayList<>();
        actions.add(ScrmAmProcessOrchestrationActionDO.builder().actionType((byte) 8).processOrchestrationNodeId(1L).build());
        List<ScrmAmProcessOrchestrationActionContentDO> contents = new ArrayList<>();
        contents.add(ScrmAmProcessOrchestrationActionContentDO.builder().processOrchestrationNodeId(1L).content("").build());
        when(actionDOMapper.selectByExample(any())).thenReturn(actions);
        when(actionContentDOMapper.selectByExample(any())).thenReturn(contents);
        PriorityInfoDTO result = (PriorityInfoDTO) getPriorityInfoMethod.invoke(executeWriteDomainService, infoDO);
        assertNull(result);
    }

    @Test
    public void testGetPriorityInfoOfOrchestration_InvalidJsonContent() throws Throwable {
        List<ScrmAmProcessOrchestrationActionDO> actions = new ArrayList<>();
        actions.add(ScrmAmProcessOrchestrationActionDO.builder().actionType((byte) 8).processOrchestrationNodeId(1L).build());
        List<ScrmAmProcessOrchestrationActionContentDO> contents = new ArrayList<>();
        // Empty valid JSON object
        // Empty valid JSON object
        // Empty valid JSON object
        contents.add(ScrmAmProcessOrchestrationActionContentDO.builder().processOrchestrationNodeId(1L).content("{}").build());
        when(actionDOMapper.selectByExample(any())).thenReturn(actions);
        when(actionContentDOMapper.selectByExample(any())).thenReturn(contents);
        PriorityInfoDTO result = (PriorityInfoDTO) getPriorityInfoMethod.invoke(executeWriteDomainService, infoDO);
        assertNull(result);
    }

    @Test
    public void testGetPriorityInfoOfOrchestration_NullContent() throws Throwable {
        List<ScrmAmProcessOrchestrationActionDO> actions = new ArrayList<>();
        actions.add(ScrmAmProcessOrchestrationActionDO.builder().actionType((byte) 8).processOrchestrationNodeId(1L).build());
        List<ScrmAmProcessOrchestrationActionContentDO> contents = new ArrayList<>();
        contents.add(ScrmAmProcessOrchestrationActionContentDO.builder().processOrchestrationNodeId(1L).content(null).build());
        when(actionDOMapper.selectByExample(any())).thenReturn(actions);
        when(actionContentDOMapper.selectByExample(any())).thenReturn(contents);
        PriorityInfoDTO result = (PriorityInfoDTO) getPriorityInfoMethod.invoke(executeWriteDomainService, infoDO);
        assertNull(result);
    }

    @Test
    public void testGetPriorityInfoOfOrchestration_MissingCouponGroupId() throws Throwable {
        List<ScrmAmProcessOrchestrationActionDO> actions = new ArrayList<>();
        actions.add(ScrmAmProcessOrchestrationActionDO.builder().actionType((byte) 8).processOrchestrationNodeId(1L).build());
        AutoActionContentDetailDTO contentDetail = new AutoActionContentDetailDTO();
        // Don't set couponGroupId
        List<ScrmAmProcessOrchestrationActionContentDO> contents = new ArrayList<>();
        contents.add(ScrmAmProcessOrchestrationActionContentDO.builder().processOrchestrationNodeId(1L).content(JsonUtils.toStr(contentDetail)).build());
        when(actionDOMapper.selectByExample(any())).thenReturn(actions);
        when(actionContentDOMapper.selectByExample(any())).thenReturn(contents);
        PriorityInfoDTO result = (PriorityInfoDTO) getPriorityInfoMethod.invoke(executeWriteDomainService, infoDO);
        assertNull(result);
    }

    @Test
    public void testGetPriorityInfoOfOrchestration_NullCouponInfo() throws Throwable {
        List<ScrmAmProcessOrchestrationActionDO> actions = new ArrayList<>();
        actions.add(ScrmAmProcessOrchestrationActionDO.builder().actionType((byte) 8).processOrchestrationNodeId(1L).build());
        AutoActionContentDetailDTO contentDetail = new AutoActionContentDetailDTO();
        contentDetail.setCouponGroupId("123");
        List<ScrmAmProcessOrchestrationActionContentDO> contents = new ArrayList<>();
        contents.add(ScrmAmProcessOrchestrationActionContentDO.builder().processOrchestrationNodeId(1L).content(JsonUtils.toStr(contentDetail)).build());
        when(actionDOMapper.selectByExample(any())).thenReturn(actions);
        when(actionContentDOMapper.selectByExample(any())).thenReturn(contents);
        when(activityFissionService.queryMktCouponInfo(anyString())).thenReturn(RemoteResponse.success(null));
        PriorityInfoDTO result = (PriorityInfoDTO) getPriorityInfoMethod.invoke(executeWriteDomainService, infoDO);
        assertNull(result);
    }

    @Test
    public void testGetPriorityInfoOfOrchestration_NullDiscountAmount() throws Throwable {
        List<ScrmAmProcessOrchestrationActionDO> actions = new ArrayList<>();
        actions.add(ScrmAmProcessOrchestrationActionDO.builder().actionType((byte) 8).processOrchestrationNodeId(1L).build());
        AutoActionContentDetailDTO contentDetail = new AutoActionContentDetailDTO();
        contentDetail.setCouponGroupId("123");
        List<ScrmAmProcessOrchestrationActionContentDO> contents = new ArrayList<>();
        contents.add(ScrmAmProcessOrchestrationActionContentDO.builder().processOrchestrationNodeId(1L).content(JsonUtils.toStr(contentDetail)).build());
        MktCouponInfoDTO couponInfo = new MktCouponInfoDTO();
        couponInfo.setDiscountAmount(null);
        when(actionDOMapper.selectByExample(any())).thenReturn(actions);
        when(actionContentDOMapper.selectByExample(any())).thenReturn(contents);
        when(activityFissionService.queryMktCouponInfo("123")).thenReturn(RemoteResponse.success(couponInfo));
        PriorityInfoDTO result = (PriorityInfoDTO) getPriorityInfoMethod.invoke(executeWriteDomainService, infoDO);
        assertNull(result);
    }

    @Test
    public void testGetPriorityInfoOfOrchestration_SuccessSingleCoupon() throws Throwable {
        List<ScrmAmProcessOrchestrationActionDO> actions = new ArrayList<>();
        actions.add(ScrmAmProcessOrchestrationActionDO.builder().actionType((byte) 8).processOrchestrationNodeId(1L).build());
        AutoActionContentDetailDTO contentDetail = new AutoActionContentDetailDTO();
        contentDetail.setCouponGroupId("123");
        List<ScrmAmProcessOrchestrationActionContentDO> contents = new ArrayList<>();
        contents.add(ScrmAmProcessOrchestrationActionContentDO.builder().processOrchestrationNodeId(1L).content(JsonUtils.toStr(contentDetail)).build());
        MktCouponInfoDTO couponInfo = new MktCouponInfoDTO();
        couponInfo.setDiscountAmount(new BigDecimal("100"));
        when(actionDOMapper.selectByExample(any())).thenReturn(actions);
        when(actionContentDOMapper.selectByExample(any())).thenReturn(contents);
        when(activityFissionService.queryMktCouponInfo("123")).thenReturn(RemoteResponse.success(couponInfo));
        PriorityInfoDTO result = (PriorityInfoDTO) getPriorityInfoMethod.invoke(executeWriteDomainService, infoDO);
        assertNotNull(result);
        assertEquals(new BigDecimal("100"), result.getMaxDiscountAmount());
        assertEquals("123", result.getCouponGroupId());
        assertEquals(Long.valueOf(1L), result.getNodeId());
    }

    @Test
    public void testGetPriorityInfoOfOrchestration_MultipleCoupons() throws Throwable {
        List<ScrmAmProcessOrchestrationActionDO> actions = new ArrayList<>();
        actions.add(ScrmAmProcessOrchestrationActionDO.builder().actionType((byte) 8).processOrchestrationNodeId(1L).build());
        actions.add(ScrmAmProcessOrchestrationActionDO.builder().actionType((byte) 8).processOrchestrationNodeId(2L).build());
        AutoActionContentDetailDTO contentDetail1 = new AutoActionContentDetailDTO();
        contentDetail1.setCouponGroupId("123");
        AutoActionContentDetailDTO contentDetail2 = new AutoActionContentDetailDTO();
        contentDetail2.setCouponGroupId("456");
        List<ScrmAmProcessOrchestrationActionContentDO> contents = new ArrayList<>();
        contents.add(ScrmAmProcessOrchestrationActionContentDO.builder().processOrchestrationNodeId(1L).content(JsonUtils.toStr(contentDetail1)).build());
        contents.add(ScrmAmProcessOrchestrationActionContentDO.builder().processOrchestrationNodeId(2L).content(JsonUtils.toStr(contentDetail2)).build());
        MktCouponInfoDTO couponInfo1 = new MktCouponInfoDTO();
        couponInfo1.setDiscountAmount(new BigDecimal("100"));
        MktCouponInfoDTO couponInfo2 = new MktCouponInfoDTO();
        couponInfo2.setDiscountAmount(new BigDecimal("200"));
        when(actionDOMapper.selectByExample(any())).thenReturn(actions);
        when(actionContentDOMapper.selectByExample(any())).thenReturn(contents);
        when(activityFissionService.queryMktCouponInfo("123")).thenReturn(RemoteResponse.success(couponInfo1));
        when(activityFissionService.queryMktCouponInfo("456")).thenReturn(RemoteResponse.success(couponInfo2));
        PriorityInfoDTO result = (PriorityInfoDTO) getPriorityInfoMethod.invoke(executeWriteDomainService, infoDO);
        assertNotNull(result);
        assertEquals(new BigDecimal("200"), result.getMaxDiscountAmount());
        assertEquals("456", result.getCouponGroupId());
        assertEquals(Long.valueOf(2L), result.getNodeId());
    }
}
