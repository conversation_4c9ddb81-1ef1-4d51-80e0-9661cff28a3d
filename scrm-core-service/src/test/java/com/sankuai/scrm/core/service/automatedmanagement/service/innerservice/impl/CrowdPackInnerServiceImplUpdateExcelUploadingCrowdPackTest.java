package com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.impl;

import com.sankuai.dz.srcm.automatedmanagement.request.UpdateExcelUploadingCrowdPackRequest;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackBaseInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.exception.ExcelUploadingCrowdPackException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

@RunWith(MockitoJUnitRunner.class)
public class CrowdPackInnerServiceImplUpdateExcelUploadingCrowdPackTest {

    @InjectMocks
    private CrowdPackInnerServiceImpl crowdPackInnerService;

    @Mock
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock
    private ExecuteWriteDomainService executeWriteDomainService;

    private UpdateExcelUploadingCrowdPackRequest request;

    private ScrmAmCrowdPackBaseInfoDO baseInfoDO;

    @Before
    public void setUp() {
        // Initialize request
        request = new UpdateExcelUploadingCrowdPackRequest();
        request.setMisId("test_mis");
        request.setSsoMisId("test_mis");
        request.setCrowdPackId(1L);
        request.setUrl("https://msstest.sankuai.com/scrm-s3/军师-人群包导入模板-v28511694306265908207.xls");
        request.setAppId("test_app");
        request.setFullFileName("test.xlsx");
        // Initialize baseInfoDO
        baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setId(1L);
        baseInfoDO.setValidPackVersion("v1");
        baseInfoDO.setName("Test Crowd Pack");
        baseInfoDO.setRemark("Test Remark");
        baseInfoDO.setType((byte) 1);
        baseInfoDO.setAppId("test_app");
        baseInfoDO.setCreatorId("test_creator");
        baseInfoDO.setLastUpdaterId("test_updater");
        baseInfoDO.setUpdateTime(new Date());
    }

    /**
     * Test invalid user authentication scenario
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testUpdateExcelUploadingCrowdPack_InvalidAuth() throws Throwable {
        // arrange
        request.setSsoMisId("different_mis");
        // act
        crowdPackInnerService.updateExcelUploadingCrowdPack(request);
        // no assert needed as we expect exception
    }

    /**
     * Test null URL scenario
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testUpdateExcelUploadingCrowdPack_NullUrl() throws Throwable {
        // arrange
        request.setUrl(null);
        // act
        crowdPackInnerService.updateExcelUploadingCrowdPack(request);
        // no assert needed as we expect exception
    }

    /**
     * Test empty URL scenario
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testUpdateExcelUploadingCrowdPack_EmptyUrl() throws Throwable {
        // arrange
        request.setUrl("");
        // act
        crowdPackInnerService.updateExcelUploadingCrowdPack(request);
        // no assert needed as we expect exception
    }

    /**
     * Test null crowd pack ID scenario
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testUpdateExcelUploadingCrowdPack_NullPackId() throws Throwable {
        // arrange
        request.setCrowdPackId(null);
        // act
        crowdPackInnerService.updateExcelUploadingCrowdPack(request);
        // no assert needed as we expect exception
    }

    /**
     * Test null app ID scenario
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testUpdateExcelUploadingCrowdPack_NullAppId() throws Throwable {
        // arrange
        request.setAppId(null);
        // act
        crowdPackInnerService.updateExcelUploadingCrowdPack(request);
        // no assert needed as we expect exception
    }


    /**
     * Test null MIS ID scenario
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testUpdateExcelUploadingCrowdPack_NullMisId() throws Throwable {
        // arrange
        request.setMisId(null);
        // act
        crowdPackInnerService.updateExcelUploadingCrowdPack(request);
        // no assert needed as we expect exception
    }

    /**
     * Test null SSO MIS ID scenario
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testUpdateExcelUploadingCrowdPack_NullSsoMisId() throws Throwable {
        // arrange
        request.setSsoMisId(null);
        // act
        crowdPackInnerService.updateExcelUploadingCrowdPack(request);
        // no assert needed as we expect exception
    }
}
