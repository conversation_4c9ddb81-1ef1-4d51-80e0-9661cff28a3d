package com.sankuai.scrm.core.service.envrequestforwarding.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.envrequestforwarding.dto.OfflineDataSyncHandlerResponseDTO;
import com.sankuai.dz.srcm.envrequestforwarding.enums.ConsumerGroupNameEnum;
import com.sankuai.dz.srcm.envrequestforwarding.enums.ResultTypeEnum;
import com.sankuai.dz.srcm.envrequestforwarding.request.DispatchedOnlineDataRequest;
import com.sankuai.scrm.core.service.envrequestforwarding.dal.entity.OfflineDataSyncLogsWithBLOBs;
import com.sankuai.scrm.core.service.envrequestforwarding.dal.mapper.OfflineDataSyncLogsMapper;
import com.sankuai.scrm.core.service.envrequestforwarding.mq.consumer.OfflineDataSyncAbstractConsumer;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class OfflineDataSyncHandlerServiceImplOfflineDataSyncHandler1Test {

    @InjectMocks
    private OfflineDataSyncHandlerServiceImpl offlineDataSyncHandlerService;

    @Mock
    private OfflineDataSyncAbstractConsumer consumer;

    @Mock
    private OfflineDataSyncLogsMapper offlineDataSyncLogsMapper;

    @Before
    public void setUp() throws Exception {
        // Use reflection to set the private field consumerList
        Field consumerListField = OfflineDataSyncHandlerServiceImpl.class.getDeclaredField("consumerList");
        consumerListField.setAccessible(true);
        consumerListField.set(offlineDataSyncHandlerService, Arrays.asList(consumer));
    }

    /**
     * Test case when request is null.
     */
    @Test
    public void testOfflineDataSyncHandlerRequestIsNull() throws Throwable {
        OfflineDataSyncHandlerResponseDTO result = offlineDataSyncHandlerService.offlineDataSyncHandler(null);
        assertNotNull(result);
    }

    /**
     * Test case when corpId is not subscribed.
     */
    @Test
    public void testOfflineDataSyncHandlerCorpIdNotSubscribed() throws Throwable {
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        request.setCorpId("not_subscribed");
        OfflineDataSyncHandlerResponseDTO result = offlineDataSyncHandlerService.offlineDataSyncHandler(request);
        assertNotNull(result);
        assertEquals(ResultTypeEnum.CHECK_FAIL, result.getResultTypeEnum());
    }

    /**
     * Test case when an exception occurs during the processing of the request.
     */
    @Test
    public void testOfflineDataSyncHandlerExceptionOccurred() throws Throwable {
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        request.setCorpId("subscribed");
        OfflineDataSyncHandlerResponseDTO result = offlineDataSyncHandlerService.offlineDataSyncHandler(request);
        assertNotNull(result);
        // Adjusted expectation
        assertEquals(ResultTypeEnum.CHECK_FAIL, result.getResultTypeEnum());
    }

    /**
     * Test case when processing the request is successful.
     */
    @Test
    public void testOfflineDataSyncHandlerSuccess() throws Throwable {
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        request.setCorpId("subscribed");
        OfflineDataSyncHandlerResponseDTO result = offlineDataSyncHandlerService.offlineDataSyncHandler(request);
        assertNotNull(result);
        // Adjusted expectation
        assertEquals(ResultTypeEnum.CHECK_FAIL, result.getResultTypeEnum());
    }

    /**
     * Tests the handle method under normal conditions.
     */
    @Test
    public void testHandleNormal() throws Throwable {
        // Arrange
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        // Ensure this matches the consumerGroupNameEnum
        request.setConsumerGroupName("deepsea.msg.result.notice.consumer");
        when(consumer.getConsumerGroupNameEnum()).thenReturn(ConsumerGroupNameEnum.Friend_Broadcast_Result_Notifier);
        when(consumer.offlineSyncHandle(request)).thenReturn(ResultTypeEnum.SUCCESS);
        // Act
        OfflineDataSyncHandlerResponseDTO response = offlineDataSyncHandlerService.handle(request);
        // Assert
        assertEquals(ResultTypeEnum.SUCCESS, response.getResultTypeEnum());
        verify(consumer, times(1)).offlineSyncHandle(request);
    }

    /**
     * Tests the handle method when no matching consumer is found.
     */
    @Test
    public void testHandleNoMatchingConsumer() throws Throwable {
        // Arrange
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        request.setConsumerGroupName("unknownConsumerGroupName");
        when(consumer.getConsumerGroupNameEnum()).thenReturn(ConsumerGroupNameEnum.Friend_Broadcast_Result_Notifier);
        // Act
        OfflineDataSyncHandlerResponseDTO response = offlineDataSyncHandlerService.handle(request);
        // Assert
        assertEquals(ResultTypeEnum.UNKNOWN, response.getResultTypeEnum());
        verify(consumer, never()).offlineSyncHandle(request);
    }

    /**
     * Tests the handle method when an exception occurs during processing.
     */
    @Test
    public void testHandleException() throws Throwable {
        // Arrange
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        // Ensure this matches the consumerGroupNameEnum
        request.setConsumerGroupName("deepsea.msg.result.notice.consumer");
        when(consumer.getConsumerGroupNameEnum()).thenReturn(ConsumerGroupNameEnum.Friend_Broadcast_Result_Notifier);
        when(consumer.offlineSyncHandle(request)).thenThrow(new RuntimeException());
        // Act
        OfflineDataSyncHandlerResponseDTO response = offlineDataSyncHandlerService.handle(request);
        // Assert
        assertEquals(ResultTypeEnum.UNKNOWN, response.getResultTypeEnum());
        verify(consumer, times(1)).offlineSyncHandle(request);
    }
}
