package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxMomentSendAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxMomentSendResponse;
import java.util.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class StaffMomentTouchActionDealStaffMomentAction1Test {

    @Spy
    @InjectMocks
    private StaffMomentTouchAction staffMomentTouchAction;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private WxMomentSendAcl wxMomentSendAcl;

    private ScrmProcessOrchestrationDTO createProcessOrchestrationDTO(Long nodeId) {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setDemoScene(false);
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        // Set up action DTO
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setProcessOrchestrationNodeId(nodeId);
        // POST_WECHAT_MOMENT
        actionDTO.setActionSubType(6);
        // Set up action content
        List<ScrmProcessOrchestrationActionContentDTO> contentList = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("test content");
        contentList.add(contentDTO);
        actionDTO.setContentList(contentList);
        // Add action to nodeMediumDTO
        Map<Long, ScrmProcessOrchestrationActionDTO> actionMap = new HashMap<>();
        actionMap.put(nodeId, actionDTO);
        nodeMediumDTO.setActionMap(actionMap);
        // Add content to nodeMediumDTO
        Map<String, List<ScrmProcessOrchestrationActionContentDTO>> contentMap = new HashMap<>();
        contentMap.put(nodeId + "-" + actionDTO.getActionId(), contentList);
        nodeMediumDTO.setActionContentMap(contentMap);
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        return processOrchestrationDTO;
    }

    /**
     * Test when executeLogDO is null
     */
    @Test
    public void testDealStaffMomentAction_NullExecuteLog() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createProcessOrchestrationDTO(1L);
        ExecuteManagementDTO mediumManagementDTO = new ExecuteManagementDTO();
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setNodeId(1L);
        List<String> corpTagList = Collections.singletonList("tag1");
        doReturn(corpTagList).when(staffMomentTouchAction).getStaffMomentTouchCorpTagList(any(), any());
        doReturn(null).when(staffMomentTouchAction).getScrmAmProcessOrchestrationExecuteLogDO(any(), any(), any(), any());
        // act
        StepExecuteResultDTO result = staffMomentTouchAction.dealStaffMomentAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertFalse(result.isSuccess());
    }

    /**
     * Test when contactUsers is empty
     */
    @Test
    public void testDealStaffMomentAction_EmptyContactUsers() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createProcessOrchestrationDTO(1L);
        ExecuteManagementDTO mediumManagementDTO = new ExecuteManagementDTO();
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setNodeId(1L);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        List<String> corpTagList = Collections.singletonList("tag1");
        doReturn(corpTagList).when(staffMomentTouchAction).getStaffMomentTouchCorpTagList(any(), any());
        doReturn(executeLogDO).when(staffMomentTouchAction).getScrmAmProcessOrchestrationExecuteLogDO(any(), any(), any(), any());
        when(executeManagementService.getContactUserByExternalUnionId(any(), any())).thenReturn(Collections.emptyList());
        // act
        StepExecuteResultDTO result = staffMomentTouchAction.dealStaffMomentAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertFalse(result.isSuccess());
    }

    /**
     * Test successful execution with ALL_USER_TAG
     */
    @Test
    public void testDealStaffMomentAction_SuccessWithAllUserTag() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createProcessOrchestrationDTO(1L);
        ExecuteManagementDTO mediumManagementDTO = new ExecuteManagementDTO();
        mediumManagementDTO.setStaffLimitSet(new HashSet<>(Arrays.asList("staff1")));
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setNodeId(1L);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setExecutorId("staff1");
        List<String> corpTagList = Collections.singletonList("ALL_USER_TAG");
        doReturn(corpTagList).when(staffMomentTouchAction).getStaffMomentTouchCorpTagList(any(), any());
        doReturn(executeLogDO).when(staffMomentTouchAction).getScrmAmProcessOrchestrationExecuteLogDO(any(), any(), any(), any());
        ContactUser contactUser = new ContactUser();
        contactUser.setStaffId("staff1");
        contactUser.setId(1L);
        when(executeManagementService.getContactUserByExternalUnionId(any(), any())).thenReturn(Collections.singletonList(contactUser));
        WxMomentSendResponse response = new WxMomentSendResponse();
        response.setErrCode(0);
        response.setJobId("job1");
        when(wxMomentSendAcl.sendMoment(any(), any())).thenReturn(response);
        // act
        StepExecuteResultDTO result = staffMomentTouchAction.dealStaffMomentAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertTrue(result.isSuccess());
        assertEquals(currentProcessingNode.getNodeId(), result.getProcessOrchestrationNodeId());
        verify(wxInvokeLogDOMapper, atLeastOnce()).updateByPrimaryKey(any());
    }

    /**
     * Test failed execution with error response
     */
    @Test
    public void testDealStaffMomentAction_FailedWithErrorResponse() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createProcessOrchestrationDTO(1L);
        ExecuteManagementDTO mediumManagementDTO = new ExecuteManagementDTO();
        mediumManagementDTO.setStaffLimitSet(new HashSet<>(Arrays.asList("staff1")));
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setNodeId(1L);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setExecutorId("staff1");
        List<String> corpTagList = Collections.singletonList("tag1");
        doReturn(corpTagList).when(staffMomentTouchAction).getStaffMomentTouchCorpTagList(any(), any());
        doReturn(executeLogDO).when(staffMomentTouchAction).getScrmAmProcessOrchestrationExecuteLogDO(any(), any(), any(), any());
        ContactUser contactUser = new ContactUser();
        contactUser.setStaffId("staff1");
        contactUser.setId(1L);
        when(executeManagementService.getContactUserByExternalUnionId(any(), any())).thenReturn(Collections.singletonList(contactUser));
        WxMomentSendResponse response = new WxMomentSendResponse();
        response.setErrCode(1);
        when(wxMomentSendAcl.sendMoment(any(), any())).thenReturn(response);
        // act
        StepExecuteResultDTO result = staffMomentTouchAction.dealStaffMomentAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertTrue(result.isSuccess());
        verify(wxInvokeLogDOMapper, atLeastOnce()).updateByPrimaryKey(any());
    }
}
