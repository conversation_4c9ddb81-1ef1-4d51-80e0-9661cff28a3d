package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxGroupSendMessageRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGroupSendMessageResponse;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerDealChosenSupplyOfficialWxMessageTest {

    @Spy
    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private WeChatTokenAcl weChatTokenAcl;

    @Mock
    private ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private String executorId;

    private InvokeDetailKeyObject keyObject;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS;

    private StepExecuteResultDTO stepExecuteResultDTO;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        executorId = "testExecutorId";
        keyObject = new InvokeDetailKeyObject("testKey", (byte) 1, (byte) 1, 1L);
        totalInvokeDetailDOS = new ArrayList<>();
        stepExecuteResultDTO = new StepExecuteResultDTO();
    }

    /**
     * Test when entry value list is empty
     */
    @Test
    public void testDealChosenSupplyOfficialWxMessage_WhenEntryValueEmpty() throws Throwable {
        // arrange
        // act
        officialWxHandler.dealChosenSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, Collections.emptyList(), stepExecuteResultDTO);
        // assert
        verify(appConfigRepository, never()).getCorpIdByAppId(any());
    }

    /**
     * Test when content DTOs list is empty
     */
    @Test
    public void testDealChosenSupplyOfficialWxMessage_WhenContentDTOsEmpty() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setProcessOrchestrationNodeId(1L);
        totalInvokeDetailDOS.add(detailDO);
        doReturn(new ScrmProcessOrchestrationActionDTO()).when(nodeMediumDTO).getActionDTO(eq(1L));
        doReturn(Collections.emptyList()).when(nodeMediumDTO).getActionContentDTOList(any());
        // act
        officialWxHandler.dealChosenSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, stepExecuteResultDTO);
        // assert
        verify(appConfigRepository, never()).getCorpIdByAppId(any());
    }
}
