package com.sankuai.scrm.core.service.realtime.task.mq.config;

import com.meituan.mdp.boot.starter.mdpcache.core.Cache;
import com.sankuai.scrm.core.service.realtime.task.domainservice.ScrmGrowthUserInfoDomainService;
import com.sankuai.scrm.core.service.realtime.task.dto.RealTimeTaskConsumerConfigDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class RealTimeTaskConsumerConfigIsInWhitelist1Test {

    @Mock
    private ScrmGrowthUserInfoDomainService scrmGrowthUserInfoDomainService;

    @Mock
    private Cache localCache;

    @Spy
    @InjectMocks
    private RealTimeTaskConsumerConfig config;

    private RealTimeTaskConsumerConfigDTO configDTO;

    @BeforeEach
    void setUp() {
        configDTO = new RealTimeTaskConsumerConfigDTO();
        configDTO.setTestAppId("testApp");
        // 通过反射设置configDTO
        try {
            Field field = RealTimeTaskConsumerConfig.class.getDeclaredField("configDTO");
            field.setAccessible(true);
            field.set(config, configDTO);
            // 设置localCache
            Field cacheField = RealTimeTaskConsumerConfig.class.getDeclaredField("localCache");
            cacheField.setAccessible(true);
            cacheField.set(config, localCache);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试空列表输入应返回false
     */
    @Test
    public void testIsInWhitelistShouldReturnFalseWhenInputIsEmpty() throws Throwable {
        // arrange
        List<Long> userIds = Collections.emptyList();
        // act
        boolean result = config.isInWhitelist(userIds);
        // assert
        assertThat(result).isFalse();
    }

    /**
     * 测试当用户ID在白名单中时应返回true
     */
    @Test
    public void testIsInWhitelistShouldReturnTrueWhenUserIdInWhiteList() throws Throwable {
        // arrange
        Long userId = 1001L;
        configDTO.getWhiteListSet().add(userId);
        List<Long> userIds = Arrays.asList(userId);
        // act
        boolean result = config.isInWhitelist(userIds);
        // assert
        assertThat(result).isTrue();
    }
}
