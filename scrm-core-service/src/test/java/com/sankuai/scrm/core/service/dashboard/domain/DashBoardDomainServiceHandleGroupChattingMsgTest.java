package com.sankuai.scrm.core.service.dashboard.domain;

import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.external.contact.domain.ExternalContactBaseInfoDomainService;
import com.sankuai.scrm.core.service.group.domain.GroupMemberDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.service.fe.corp.wx.model.OpenGroupMemberBriefed;
import com.sankuai.service.fe.corp.wx.mq.event.OpenGroupChattingEvent;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.Mockito.*;

public class DashBoardDomainServiceHandleGroupChattingMsgTest {

    @InjectMocks
    private DashBoardDomainService dashBoardDomainService;

    @Mock
    private DashBoardESWriteDomainService dashBoardESWriteDomainService;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private ExternalContactBaseInfoDomainService externalContactBaseInfoDomainService;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private GroupMemberDomainService groupMemberDomainService;

    @Mock
    private ContactUserDomain contactUserDomain;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testHandleGroupChattingMsgWithNullMsg() throws Throwable {
        dashBoardDomainService.handleGroupChattingMsg(null);
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(anyString(), any(), anyString());
    }

    @Test
    public void testHandleGroupChattingMsgWithInvalidMsg() throws Throwable {
        dashBoardDomainService.handleGroupChattingMsg("invalid msg");
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(anyString(), any(), anyString());
    }

    @Test
    public void testHandleGroupChattingMsgWithNullEvent() throws Throwable {
        when(corpAppConfigRepository.getConfigByBusinessCode(anyString())).thenReturn(null);
        dashBoardDomainService.handleGroupChattingMsg("{}");
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(anyString(), any(), anyString());
    }

    @Test
    public void testHandleGroupChattingMsgWithNullCorpId() throws Throwable {
        OpenGroupChattingEvent event = new OpenGroupChattingEvent();
        event.setMember(new OpenGroupMemberBriefed());
        event.setBusinessCode("businessCode");
        when(corpAppConfigRepository.getConfigByBusinessCode("businessCode")).thenReturn(null);
        dashBoardDomainService.handleGroupChattingMsg("{\"businessCode\":\"businessCode\"}");
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(anyString(), any(), anyString());
    }

    @Test
    public void testHandleGroupChattingMsgWithNullMember() throws Throwable {
        OpenGroupChattingEvent event = new OpenGroupChattingEvent();
        event.setCorpId("corpId");
        dashBoardDomainService.handleGroupChattingMsg("{\"corpId\":\"corpId\"}");
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(anyString(), any(), anyString());
    }

    @Test
    public void testHandleGroupChattingMsgWithNullUnionId() throws Throwable {
        OpenGroupChattingEvent event = new OpenGroupChattingEvent();
        event.setCorpId("corpId");
        event.setMember(new OpenGroupMemberBriefed());
        dashBoardDomainService.handleGroupChattingMsg("{\"corpId\":\"corpId\",\"member\":{}}");
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(anyString(), any(), anyString());
    }

    @Test
    public void testHandleGroupChattingMsgWithValidMsg() throws Throwable {
        OpenGroupChattingEvent event = new OpenGroupChattingEvent();
        event.setCorpId("corpId");
        event.setMember(new OpenGroupMemberBriefed());
        event.getMember().setUnionId("unionId");
        event.setMessageTime(System.currentTimeMillis() / 1000);
        dashBoardDomainService.handleGroupChattingMsg("{\"corpId\":\"corpId\",\"member\":{\"unionId\":\"unionId\"},\"messageTime\":" + event.getMessageTime() + "}");
        verify(dashBoardESWriteDomainService, times(1)).updateESDocByIdSelective(anyString(), any(), anyString());
    }

    @Test
    public void testConvertMtUserIdToUnionIdMtUserIdIsNull() throws Throwable {
        // Corrected the variable declaration and usage
        String result = dashBoardDomainService.convertMtUserIdToUnionId(null, "corpId");
        assertNull(result);
    }

    @Test
    public void testConvertMtUserIdToUnionIdUnionIdIsNull() throws Throwable {
        when(externalContactBaseInfoDomainService.getUnionIdByMtUserIdAndAppId(anyLong(), anyString())).thenReturn(null);
        when(mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(anyLong(), anyString())).thenReturn(null);
        // Corrected the variable declaration and usage
        String result = dashBoardDomainService.convertMtUserIdToUnionId(1L, "corpId");
        assertNull(result);
    }

    @Test
    public void testConvertMtUserIdToUnionIdUnionIdIsNotNull() throws Throwable {
        // Mock the corpAppConfigRepository to return a non-null appId
        when(corpAppConfigRepository.getAppIdByCorpId(anyString())).thenReturn("appId");
        when(externalContactBaseInfoDomainService.getUnionIdByMtUserIdAndAppId(anyLong(), anyString())).thenReturn("unionId");
        // Corrected the variable declaration and usage
        String result = dashBoardDomainService.convertMtUserIdToUnionId(1L, "corpId");
        assertEquals("unionId", result);
    }

    @Test
    public void testConvertMtUserIdToUnionIdException() throws Throwable {
        when(externalContactBaseInfoDomainService.getUnionIdByMtUserIdAndAppId(anyLong(), anyString())).thenThrow(new RuntimeException());
        // Corrected the variable declaration and usage
        String result = dashBoardDomainService.convertMtUserIdToUnionId(1L, "corpId");
        assertNull(result);
    }

    @Test
    public void testHandleDashBoardUserLogWhenAddContactUser_UnionIdIsNull() throws Throwable {
        dashBoardDomainService.handleDashBoardUserLogWhenAddContactUser("corpId", null, "externalUserId", "");
        verifyNoInteractions(dashBoardESWriteDomainService);
    }

    @Test
    public void testHandleDashBoardUserLogWhenAddContactUser_GroupMemberIsEmpty() throws Throwable {
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(anyString(), anyString())).thenReturn(Collections.emptyList());
        dashBoardDomainService.handleDashBoardUserLogWhenAddContactUser("corpId", "unionId", "externalUserId", "");
        verify(contactUserDomain, times(1)).getContactUsersByCorpIdAndUnionIdAndExternalUserIdFromMaster(anyString(), anyString(), anyString());
    }
}
