package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationNodeTypeEnum;
import com.sankuai.scrm.core.service.user.enums.ScrmUserTagEnum;
import java.lang.reflect.Method;
import java.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class StaffMomentTouchActionGetRootNodeCorpTagListTest {

    private StaffMomentTouchAction action;

    private Method method;

    @InjectMocks
    private StaffMomentTouchAction staffMomentTouchAction;

    @BeforeEach
    void setUp() throws Exception {
        action = new StaffMomentTouchAction();
        method = StaffMomentTouchAction.class.getDeclaredMethod("getRootNodeCorpTagList", List.class);
        method.setAccessible(true);
    }

    /**
     * 使用反射调用私有方法 getRootNodeCorpTagList
     */
    private Set<String> invokeGetRootNodeCorpTagList(List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList) throws Exception {
        return (Set<String>) method.invoke(action, strategyDetailDTOList);
    }

    /**
     * 创建一个带有必要属性的DTO对象
     */
    private ScrmCrowdPackUpdateStrategyDetailDTO createDTO(Long filterId, Integer groupId, List<String> param) {
        ScrmCrowdPackUpdateStrategyDetailDTO dto = new ScrmCrowdPackUpdateStrategyDetailDTO();
        dto.setFilterFieldId(filterId);
        dto.setGroupId(groupId);
        dto.setParam(param);
        return dto;
    }

    /**
     * 测试空输入列表
     */
    @Test
    public void testGetRootNodeCorpTagListEmptyInput() throws Throwable {
        // arrange
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Collections.emptyList();
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(strategyDetailDTOList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试非空输入列表但没有分组
     */
    @Test
    public void testGetRootNodeCorpTagListNoGroups() throws Throwable {
        // arrange
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Arrays.asList(createDTO(10001L, 1, Collections.emptyList()));
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(strategyDetailDTOList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试分组中没有企业标签
     */
    @Test
    public void testGetRootNodeCorpTagListGroupWithNoCorpTags() throws Throwable {
        // arrange
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Arrays.asList(createDTO(10001L, 1, Arrays.asList("tag1")));
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(strategyDetailDTOList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试分组中同时包含企业标签和其他标签
     */
    @Test
    public void testGetRootNodeCorpTagListGroupWithCorpAndOtherTags() throws Throwable {
        // arrange
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Arrays.asList(createDTO(ScrmUserTagEnum.CORP_TAG.getTagId(), 1, Arrays.asList("tag1")), createDTO(10001L, 1, Arrays.asList("tag2")));
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(strategyDetailDTOList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试分组中有多个企业标签
     */
    @Test
    public void testGetRootNodeCorpTagListGroupWithMultipleCorpTags() throws Throwable {
        // arrange
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Arrays.asList(createDTO(ScrmUserTagEnum.CORP_TAG.getTagId(), 1, Arrays.asList("tag1")), createDTO(ScrmUserTagEnum.CORP_TAG.getTagId(), 1, Arrays.asList("tag2")));
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(strategyDetailDTOList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试分组中只有一个企业标签且没有其他标签
     */
    @Test
    public void testGetRootNodeCorpTagListGroupWithOneCorpTagNoOtherTags() throws Throwable {
        // arrange
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Arrays.asList(createDTO(ScrmUserTagEnum.CORP_TAG.getTagId(), 1, Arrays.asList("tag1")));
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(strategyDetailDTOList);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains("tag1"));
    }

    /**
     * 测试分组中只有一个企业标签但参数列表为空
     */
    @Test
    public void testGetRootNodeCorpTagListGroupWithOneCorpTagEmptyParamList() throws Throwable {
        // arrange
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Arrays.asList(createDTO(ScrmUserTagEnum.CORP_TAG.getTagId(), 1, Collections.emptyList()));
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(strategyDetailDTOList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试分组中只有一个企业标签且参数列表非空
     */
    @Test
    public void testGetRootNodeCorpTagListGroupWithOneCorpTagNonEmptyParamList() throws Throwable {
        // arrange
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Arrays.asList(createDTO(ScrmUserTagEnum.CORP_TAG.getTagId(), 1, Arrays.asList("tag1")));
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(strategyDetailDTOList);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains("tag1"));
    }

    /**
     * 测试多个分组中都有有效的企业标签
     */
    @Test
    public void testGetRootNodeCorpTagListMultipleGroupsWithValidCorpTags() throws Throwable {
        // arrange
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Arrays.asList(createDTO(ScrmUserTagEnum.CORP_TAG.getTagId(), 1, Arrays.asList("tag1")), createDTO(ScrmUserTagEnum.CORP_TAG.getTagId(), 2, Arrays.asList("tag2")));
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(strategyDetailDTOList);
        // assert
        assertEquals(2, result.size());
        assertTrue(result.contains("tag1"));
        assertTrue(result.contains("tag2"));
    }

    /**
     * 测试输入列表为 null
     */
    @Test
    public void testGetRootNodeCorpTagListNullInput() throws Throwable {
        // act & assert
        Set<String> result = invokeGetRootNodeCorpTagList(null);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试分组中企业标签的参数列表为 null
     */
    @Test
    public void testGetRootNodeCorpTagListGroupWithNullCorpTagParamList() throws Throwable {
        // arrange
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetailDTOList = Arrays.asList(createDTO(ScrmUserTagEnum.CORP_TAG.getTagId(), 1, null));
        // act
        Set<String> result = invokeGetRootNodeCorpTagList(strategyDetailDTOList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test getRootNodeFromProcessOrchestration when no root node exists in list
     * Should return null when no node has nodeType=4 (PROCESS_ORCHESTRATION_ROOT)
     */
    @Test
    public void testGetRootNodeFromProcessOrchestration_NoRootNodeExists() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationNodeDTO> nodeList = new ArrayList<>();
        // Create a condition node
        ScrmProcessOrchestrationNodeDTO conditionNode = new ScrmProcessOrchestrationNodeDTO();
        conditionNode.setNodeId(1L);
        conditionNode.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_CONDITION.getValue());
        // Create an action node
        ScrmProcessOrchestrationNodeDTO actionNode = new ScrmProcessOrchestrationNodeDTO();
        actionNode.setNodeId(2L);
        actionNode.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ACTION.getValue());
        // Create an auto action node
        ScrmProcessOrchestrationNodeDTO autoActionNode = new ScrmProcessOrchestrationNodeDTO();
        autoActionNode.setNodeId(3L);
        autoActionNode.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_AUTO_ACTION.getValue());
        // Add all non-root nodes to list
        nodeList.add(conditionNode);
        nodeList.add(actionNode);
        nodeList.add(autoActionNode);
        // act
        ScrmProcessOrchestrationNodeDTO result = staffMomentTouchAction.getRootNodeFromProcessOrchestration(nodeList);
        // assert
        assertNull(result, "Should return null when no root node exists in the list");
    }

    /**
     * Test getRootNodeFromProcessOrchestration with empty list
     * Should return null when list is empty
     */
    @Test
    public void testGetRootNodeFromProcessOrchestration_EmptyList() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationNodeDTO> nodeList = new ArrayList<>();
        // act
        ScrmProcessOrchestrationNodeDTO result = staffMomentTouchAction.getRootNodeFromProcessOrchestration(nodeList);
        // assert
        assertNull(result, "Should return null when list is empty");
    }

    /**
     * Test getRootNodeFromProcessOrchestration with null list
     * Should throw NullPointerException when list is null
     */
    @Test
    public void testGetRootNodeFromProcessOrchestration_NullList() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationNodeDTO> nodeList = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            staffMomentTouchAction.getRootNodeFromProcessOrchestration(nodeList);
        }, "Should throw NullPointerException when list is null");
    }
}
