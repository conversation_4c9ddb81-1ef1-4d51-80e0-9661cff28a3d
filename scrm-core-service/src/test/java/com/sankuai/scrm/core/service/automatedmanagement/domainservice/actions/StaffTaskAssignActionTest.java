package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import static org.mockito.ArgumentMatchers.any;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class StaffTaskAssignActionTest {

    @InjectMocks
    private StaffTaskAssignAction staffTaskAssignAction;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock(lenient = true)
    private ExecuteManagementDTO mediumManagementDTO;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    @Mock(lenient = true)
    private ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationNodeDTO currentProcessingNode;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDO existedExecuteLogDO;

    @Mock(lenient = true)
    private ExecuteManagementService executeManagementService;

    @Before
    public void setUp() {
        when(processOrchestrationDTO.isParticipationRestrict()).thenReturn(true);
        when(processOrchestrationDTO.getParticipationRestrictionsCycle()).thenReturn((byte) 1);
        when(processOrchestrationDTO.getParticipationRestrictionsTimes()).thenReturn((byte) 1);
        when(executeManagementService.getMtUserIdByUnionId(anyString())).thenReturn(1L);
    }

    @Test
    public void testDealStaffTaskAssignAction_ExistedExecuteLogDOIsNullAndHistoryExecuteLogExistsAndParticipationRestrictIsTrue() throws Throwable {
        when(scrmCrowdPackDetailInfoDTO.getExternalUserWxUnionId()).thenReturn("testUnionId");
        when(executeLogDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(new ScrmAmProcessOrchestrationExecuteLogDO()));
        staffTaskAssignAction.dealStaffTaskAssignAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        verify(executeLogDOMapper, times(1)).selectByExample(any());
    }

    @Test
    public void testDealStaffTaskAssignAction_ExistedExecuteLogDOIsNullAndHistoryExecuteLogExistsAndParticipationRestrictIsFalseAndHistoryExecuteLogCountLessThanParticipationRestrictTimes() throws Throwable {
        when(processOrchestrationDTO.isParticipationRestrict()).thenReturn(false);
        when(scrmCrowdPackDetailInfoDTO.getExternalUserWxUnionId()).thenReturn("testUnionId");
        when(executeLogDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(new ScrmAmProcessOrchestrationExecuteLogDO()));
        staffTaskAssignAction.dealStaffTaskAssignAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        verify(executeLogDOMapper, times(1)).selectByExample(any());
    }

    @Test
    public void testDealStaffTaskAssignAction_ExistedExecuteLogDOIsNullAndHistoryExecuteLogExistsAndParticipationRestrictIsFalseAndHistoryExecuteLogCountGreaterThanParticipationRestrictTimes() throws Throwable {
        when(processOrchestrationDTO.isParticipationRestrict()).thenReturn(false);
        when(scrmCrowdPackDetailInfoDTO.getExternalUserWxUnionId()).thenReturn("testUnionId");
        when(executeLogDOMapper.selectByExample(any())).thenReturn(Collections.nCopies(2, new ScrmAmProcessOrchestrationExecuteLogDO()));
        staffTaskAssignAction.dealStaffTaskAssignAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        verify(executeLogDOMapper, times(1)).selectByExample(any());
    }

    @Test
    public void testDealStaffTaskAssignAction_ExistedExecuteLogDOIsNullAndNoHistoryExecuteLog() throws Throwable {
        when(scrmCrowdPackDetailInfoDTO.getExternalUserWxUnionId()).thenReturn("testUnionId");
        when(executeLogDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        staffTaskAssignAction.dealStaffTaskAssignAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        verify(executeLogDOMapper, times(1)).selectByExample(any());
    }

    @Test
    public void testDealStaffTaskAssignAction_ExistedExecuteLogDOIsNotNull() throws Throwable {
        staffTaskAssignAction.dealStaffTaskAssignAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        verify(executeLogDOMapper, times(0)).selectByExample(any());
    }
}
