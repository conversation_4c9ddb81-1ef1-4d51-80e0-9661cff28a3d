package com.sankuai.scrm.core.service.infrastructure.acl.track;

import com.sankuai.dzshoplist.search.intervention.UserTrackQueryService;
import com.sankuai.dzshoplist.search.intervention.dto.SequenceDTO;
import com.sankuai.dzshoplist.search.intervention.dto.UserTrackDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.track.request.UserTrackRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.track.result.UserTrackResult;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class UserTrackAclTest {

    @Mock
    private UserTrackQueryService userTrackQueryService;

    @InjectMocks
    private UserTrackAcl userTrackAcl;

    private UserTrackRequest createTestRequest() {
        UserTrackRequest request = new UserTrackRequest();
        request.setPlatform(1);
        request.setUserId(12345L);
        request.setStartTimestamp(1000L);
        request.setEndTimestamp(2000L);
        return request;
    }

    private SequenceDTO createBaseSequenceDTO() {
        SequenceDTO custom = new SequenceDTO();
        custom.setActionList(new ArrayList<>());
        custom.setTimestampList(new ArrayList<>());
        custom.setItemIdList(new ArrayList<>());
        custom.setTypeList(new ArrayList<>());
        custom.setPlatformList(new ArrayList<>());
        return custom;
    }

    /**
     * Test when UserTrackQueryService returns null
     */
    @Test
    public void testQueryUserTrack_ServiceReturnsNull() throws Throwable {
        // arrange
        UserTrackRequest request = createTestRequest();
        when(userTrackQueryService.getUserTrack(request.getPlatform(), request.getUserId(), request.getStartTimestamp(), request.getEndTimestamp())).thenReturn(null);
        // act
        List<UserTrackResult> result = userTrackAcl.queryUserTrack(request);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test when custom sequence is null
     */
    @Test
    public void testQueryUserTrack_CustomSequenceIsNull() throws Throwable {
        // arrange
        UserTrackRequest request = createTestRequest();
        UserTrackDTO userTrackDTO = new UserTrackDTO();
        userTrackDTO.setCustom(null);
        when(userTrackQueryService.getUserTrack(request.getPlatform(), request.getUserId(), request.getStartTimestamp(), request.getEndTimestamp())).thenReturn(userTrackDTO);
        // act
        List<UserTrackResult> result = userTrackAcl.queryUserTrack(request);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test click action with deal/product type
     */
    @Test
    public void testQueryUserTrack_ClickActionWithDealProductType() throws Throwable {
        // arrange
        UserTrackRequest request = createTestRequest();
        UserTrackDTO userTrackDTO = new UserTrackDTO();
        SequenceDTO custom = createBaseSequenceDTO();
        custom.setActionList(new ArrayList<>(Arrays.asList("click")));
        custom.setTimestampList(new ArrayList<>(Arrays.asList(123456789L)));
        custom.setItemIdList(new ArrayList<>(Arrays.asList(1001L)));
        custom.setTypeList(new ArrayList<>(Arrays.asList("deal")));
        custom.setPlatformList(new ArrayList<>(Arrays.asList("platform1")));
        userTrackDTO.setCustom(custom);
        when(userTrackQueryService.getUserTrack(request.getPlatform(), request.getUserId(), request.getStartTimestamp(), request.getEndTimestamp())).thenReturn(userTrackDTO);
        // act
        List<UserTrackResult> result = userTrackAcl.queryUserTrack(request);
        // assert
        assertEquals(1, result.size());
        UserTrackResult trackResult = result.get(0);
        // VIEW_PRODUCT
        assertEquals(1, trackResult.getActionType());
        assertEquals("1001", trackResult.getActionContent());
        assertEquals(123456789L, trackResult.getActionTimestamp());
        assertEquals("platform1", trackResult.getPlatForm());
    }

    /**
     * Test click action with poi type
     */
    @Test
    public void testQueryUserTrack_ClickActionWithPoiType() throws Throwable {
        // arrange
        UserTrackRequest request = createTestRequest();
        UserTrackDTO userTrackDTO = new UserTrackDTO();
        SequenceDTO custom = createBaseSequenceDTO();
        custom.setActionList(new ArrayList<>(Arrays.asList("click")));
        custom.setTimestampList(new ArrayList<>(Arrays.asList(123456789L)));
        custom.setItemIdList(new ArrayList<>(Arrays.asList(1001L)));
        custom.setShopIdList(new ArrayList<>(Arrays.asList(1001L)));
        custom.setTypeList(new ArrayList<>(Arrays.asList("poi")));
        custom.setPlatformList(new ArrayList<>(Arrays.asList("platform1")));
        userTrackDTO.setCustom(custom);
        when(userTrackQueryService.getUserTrack(request.getPlatform(), request.getUserId(), request.getStartTimestamp(), request.getEndTimestamp())).thenReturn(userTrackDTO);
        // act
        List<UserTrackResult> result = userTrackAcl.queryUserTrack(request);
        // assert
        assertEquals(1, result.size());
        UserTrackResult trackResult = result.get(0);
        // VIEW_SHOP
        assertEquals(2, trackResult.getActionType());
        assertEquals("1001", trackResult.getActionContent());
        assertEquals(123456789L, trackResult.getActionTimestamp());
        assertEquals("platform1", trackResult.getPlatForm());
    }

    /**
     * Test add to cart action
     */
    @Test
    public void testQueryUserTrack_AddToCartAction() throws Throwable {
        // arrange
        UserTrackRequest request = createTestRequest();
        UserTrackDTO userTrackDTO = new UserTrackDTO();
        SequenceDTO custom = createBaseSequenceDTO();
        custom.setActionList(new ArrayList<>(Arrays.asList("atc")));
        custom.setTimestampList(new ArrayList<>(Arrays.asList(123456789L)));
        custom.setItemIdList(new ArrayList<>(Arrays.asList(1001L)));
        custom.setTypeList(new ArrayList<>(Arrays.asList("deal")));
        custom.setPlatformList(new ArrayList<>(Arrays.asList("platform1")));
        userTrackDTO.setCustom(custom);
        when(userTrackQueryService.getUserTrack(request.getPlatform(), request.getUserId(), request.getStartTimestamp(), request.getEndTimestamp())).thenReturn(userTrackDTO);
        // act
        List<UserTrackResult> result = userTrackAcl.queryUserTrack(request);
        // assert
        assertEquals(1, result.size());
        UserTrackResult trackResult = result.get(0);
        // ADD_TO_CART
        assertEquals(11, trackResult.getActionType());
        assertEquals("1001", trackResult.getActionContent());
        assertEquals(123456789L, trackResult.getActionTimestamp());
        assertEquals("platform1", trackResult.getPlatForm());
    }

    /**
     * Test order action
     */
    @Test
    public void testQueryUserTrack_OrderAction() throws Throwable {
        // arrange
        UserTrackRequest request = createTestRequest();
        UserTrackDTO userTrackDTO = new UserTrackDTO();
        SequenceDTO custom = createBaseSequenceDTO();
        custom.setActionList(new ArrayList<>(Arrays.asList("ord")));
        custom.setTimestampList(new ArrayList<>(Arrays.asList(123456789L)));
        custom.setItemIdList(new ArrayList<>(Arrays.asList(1001L)));
        custom.setTypeList(new ArrayList<>(Arrays.asList("deal")));
        custom.setPlatformList(new ArrayList<>(Arrays.asList("platform1")));
        userTrackDTO.setCustom(custom);
        when(userTrackQueryService.getUserTrack(request.getPlatform(), request.getUserId(), request.getStartTimestamp(), request.getEndTimestamp())).thenReturn(userTrackDTO);
        // act
        List<UserTrackResult> result = userTrackAcl.queryUserTrack(request);
        // assert
        assertEquals(1, result.size());
        UserTrackResult trackResult = result.get(0);
        // ORDER
        assertEquals(9, trackResult.getActionType());
        assertEquals("1001", trackResult.getActionContent());
        assertEquals(123456789L, trackResult.getActionTimestamp());
        assertEquals("platform1", trackResult.getPlatForm());
    }

    /**
     * Test pay action
     */
    @Test
    public void testQueryUserTrack_PayAction() throws Throwable {
        // arrange
        UserTrackRequest request = createTestRequest();
        UserTrackDTO userTrackDTO = new UserTrackDTO();
        SequenceDTO custom = createBaseSequenceDTO();
        custom.setActionList(new ArrayList<>(Arrays.asList("pay")));
        custom.setTimestampList(new ArrayList<>(Arrays.asList(123456789L)));
        custom.setItemIdList(new ArrayList<>(Arrays.asList(1001L)));
        custom.setTypeList(new ArrayList<>(Arrays.asList("deal")));
        custom.setPlatformList(new ArrayList<>(Arrays.asList("platform1")));
        custom.setShopIdList(new ArrayList<>(Arrays.asList(1001L)));
        userTrackDTO.setCustom(custom);
        when(userTrackQueryService.getUserTrack(request.getPlatform(), request.getUserId(), request.getStartTimestamp(), request.getEndTimestamp())).thenReturn(userTrackDTO);
        // act
        List<UserTrackResult> result = userTrackAcl.queryUserTrack(request);
        // assert
        assertEquals(1, result.size());
        UserTrackResult trackResult = result.get(0);
        // PAY
        assertEquals(10, trackResult.getActionType());
        assertEquals("1001", trackResult.getActionContent());
        assertEquals(123456789L, trackResult.getActionTimestamp());
        assertEquals("platform1", trackResult.getPlatForm());
    }

    /**
     * Test unknown action type
     */
    @Test
    public void testQueryUserTrack_UnknownActionType() throws Throwable {
        // arrange
        UserTrackRequest request = createTestRequest();
        UserTrackDTO userTrackDTO = new UserTrackDTO();
        SequenceDTO custom = createBaseSequenceDTO();
        custom.setActionList(new ArrayList<>(Arrays.asList("unknown")));
        custom.setTimestampList(new ArrayList<>(Arrays.asList(123456789L)));
        custom.setItemIdList(new ArrayList<>(Arrays.asList(1001L)));
        custom.setTypeList(new ArrayList<>(Arrays.asList("deal")));
        custom.setPlatformList(new ArrayList<>(Arrays.asList("platform1")));
        userTrackDTO.setCustom(custom);
        when(userTrackQueryService.getUserTrack(request.getPlatform(), request.getUserId(), request.getStartTimestamp(), request.getEndTimestamp())).thenReturn(userTrackDTO);
        // act
        List<UserTrackResult> result = userTrackAcl.queryUserTrack(request);
        // assert
        assertEquals(1, result.size());
        UserTrackResult trackResult = result.get(0);
        assertNull(trackResult.getActionType());
        assertNull(trackResult.getActionContent());
        assertEquals(123456789L, trackResult.getActionTimestamp());
        assertEquals("platform1", trackResult.getPlatForm());
    }

    /**
     * Test empty action list
     */
    @Test
    public void testQueryUserTrack_EmptyActionList() throws Throwable {
        // arrange
        UserTrackRequest request = createTestRequest();
        UserTrackDTO userTrackDTO = new UserTrackDTO();
        SequenceDTO custom = createBaseSequenceDTO();
        userTrackDTO.setCustom(custom);
        when(userTrackQueryService.getUserTrack(request.getPlatform(), request.getUserId(), request.getStartTimestamp(), request.getEndTimestamp())).thenReturn(userTrackDTO);
        // act
        List<UserTrackResult> result = userTrackAcl.queryUserTrack(request);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test exception handling
     */
    @Test
    public void testQueryUserTrack_ExceptionThrown() throws Throwable {
        // arrange
        UserTrackRequest request = createTestRequest();
        when(userTrackQueryService.getUserTrack(request.getPlatform(), request.getUserId(), request.getStartTimestamp(), request.getEndTimestamp())).thenThrow(new RuntimeException("Test exception"));
        // act
        List<UserTrackResult> result = userTrackAcl.queryUserTrack(request);
        // assert
        assertTrue(result.isEmpty());
        verify(userTrackQueryService).getUserTrack(request.getPlatform(), request.getUserId(), request.getStartTimestamp(), request.getEndTimestamp());
    }

    /**
     * Test multiple actions in sequence
     */
    @Test
    public void testQueryUserTrack_MultipleActions() throws Throwable {
        // arrange
        UserTrackRequest request = createTestRequest();
        UserTrackDTO userTrackDTO = new UserTrackDTO();
        SequenceDTO custom = createBaseSequenceDTO();
        custom.setActionList(new ArrayList<>(Arrays.asList("click", "atc", "ord", "pay")));
        custom.setTimestampList(new ArrayList<>(Arrays.asList(1L, 2L, 3L, 4L)));
        custom.setItemIdList(new ArrayList<>(Arrays.asList(1001L, 1002L, 1003L, 1004L)));
        custom.setShopIdList(new ArrayList<>(Arrays.asList(1001L, 1002L, 1003L, 1004L)));
        custom.setTypeList(new ArrayList<>(Arrays.asList("deal", "deal", "deal", "deal")));
        custom.setPlatformList(new ArrayList<>(Arrays.asList("p1", "p2", "p3", "p4")));
        userTrackDTO.setCustom(custom);
        when(userTrackQueryService.getUserTrack(request.getPlatform(), request.getUserId(), request.getStartTimestamp(), request.getEndTimestamp())).thenReturn(userTrackDTO);
        // act
        List<UserTrackResult> result = userTrackAcl.queryUserTrack(request);
        // assert
        assertEquals(4, result.size());
        // Check first action (VIEW_PRODUCT)
        UserTrackResult firstResult = result.get(0);
        assertEquals(1, firstResult.getActionType());
        assertEquals("1001", firstResult.getActionContent());
        assertEquals(1L, firstResult.getActionTimestamp());
        assertEquals("p1", firstResult.getPlatForm());
        // Check second action (ADD_TO_CART)
        UserTrackResult secondResult = result.get(1);
        assertEquals(11, secondResult.getActionType());
        assertEquals("1002", secondResult.getActionContent());
        assertEquals(2L, secondResult.getActionTimestamp());
        assertEquals("p2", secondResult.getPlatForm());
        // Check third action (ORDER)
        UserTrackResult thirdResult = result.get(2);
        assertEquals(9, thirdResult.getActionType());
        assertEquals("1003", thirdResult.getActionContent());
        assertEquals(3L, thirdResult.getActionTimestamp());
        assertEquals("p3", thirdResult.getPlatForm());
        // Check fourth action (PAY)
        UserTrackResult fourthResult = result.get(3);
        assertEquals(10, fourthResult.getActionType());
        assertEquals("1004", fourthResult.getActionContent());
        assertEquals(4L, fourthResult.getActionTimestamp());
        assertEquals("p4", fourthResult.getPlatForm());
    }
}
