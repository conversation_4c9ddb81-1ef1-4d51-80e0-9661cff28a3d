package com.sankuai.scrm.core.service.automatedmanagement.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmSupportedFilterFieldOperatorTypeEnum;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserTag;
import java.lang.reflect.InvocationTargetException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/7/24
 */
@RunWith(MockitoJUnitRunner.class)
public class OperateUtilsV2Test {

    @InjectMocks
    private OperateUtilsV2 operateUtilsV2;

    @Test
    public void test() throws ClassNotFoundException, InvocationTargetException, IllegalAccessException {
        ScrmUserTag scrmUserTag = new ScrmUserTag();
        scrmUserTag.setTagValue("1");
        List<String> params = new ArrayList<>();
        params.add("1");
        Boolean result = operateUtilsV2.compute(scrmUserTag, params, ScrmSupportedFilterFieldOperatorTypeEnum.EQUAL, "java.lang.Integer");
        Assert.assertTrue(result);
    }

    @Test
    public void testComputeWhenFieldTypeClassIsBlank() throws Throwable {
        ScrmUserTag dataResource = new ScrmUserTag();
        List<String> param = Collections.singletonList("test");
        Boolean result = operateUtilsV2.compute(dataResource, param, ScrmSupportedFilterFieldOperatorTypeEnum.EQUAL, "");
        assertFalse(result);
    }

    @Test
    public void testComputeWhenParamIsEmpty() throws Throwable {
        ScrmUserTag dataResource = new ScrmUserTag();
        List<String> param = Collections.emptyList();
        Boolean result = operateUtilsV2.compute(dataResource, param, ScrmSupportedFilterFieldOperatorTypeEnum.EQUAL, "java.lang.String");
        assertFalse(result);
    }

    @Test
    public void testComputeString() throws Throwable {
        ScrmUserTag dataResource = new ScrmUserTag();
        dataResource.setTagValue("test");
        List<String> param = Collections.singletonList("test");
        Boolean result = operateUtilsV2.compute(dataResource, param, ScrmSupportedFilterFieldOperatorTypeEnum.EQUAL, "java.lang.String");
        assertTrue(result);
    }

    @Test
    public void testComputeLong() throws Throwable {
        ScrmUserTag dataResource = new ScrmUserTag();
        dataResource.setTagValue("100");
        List<String> param = Collections.singletonList("100");
        Boolean result = operateUtilsV2.compute(dataResource, param, ScrmSupportedFilterFieldOperatorTypeEnum.EQUAL, "java.lang.Long");
        assertTrue(result);
    }

    @Test
    public void testComputeBoolean() throws Throwable {
        ScrmUserTag dataResource = new ScrmUserTag();
        dataResource.setTagValue("true");
        List<String> param = Collections.singletonList("true");
        Boolean result = operateUtilsV2.compute(dataResource, param, ScrmSupportedFilterFieldOperatorTypeEnum.IS_TRUE, "java.lang.Boolean");
        assertTrue(result);
    }

    @Test
    public void testComputeByte() throws Throwable {
        ScrmUserTag dataResource = new ScrmUserTag();
        dataResource.setTagValue("1");
        List<String> param = Collections.singletonList("1");
        Boolean result = operateUtilsV2.compute(dataResource, param, ScrmSupportedFilterFieldOperatorTypeEnum.EQUAL, "java.lang.Byte");
        assertTrue(result);
    }

    @Test
    public void testComputeBigDecimal() throws Throwable {
        ScrmUserTag dataResource = new ScrmUserTag();
        dataResource.setTagValue("100.00");
        List<String> param = Collections.singletonList("100.00");
        Boolean result = operateUtilsV2.compute(dataResource, param, ScrmSupportedFilterFieldOperatorTypeEnum.EQUAL, "java.math.BigDecimal");
        assertTrue(result);
    }

    @Test
    public void testComputeDate() throws Throwable {
        ScrmUserTag dataResource = new ScrmUserTag();
        dataResource.setTagValue("2023-01-01 00:00:00");
        List<String> param = Collections.singletonList("2023-01-01 00:00:00");
        Boolean result = operateUtilsV2.compute(dataResource, param, ScrmSupportedFilterFieldOperatorTypeEnum.EQUAL, "java.util.Date");
        assertTrue(result);
    }

    @Test
    public void testComputeUnsupportedType() throws Throwable {
        ScrmUserTag dataResource = new ScrmUserTag();
        dataResource.setTagValue("test");
        List<String> param = Collections.singletonList("test");
        Boolean result = operateUtilsV2.compute(dataResource, param, ScrmSupportedFilterFieldOperatorTypeEnum.EQUAL, "java.lang.Double");
        assertFalse(result);
    }
}
