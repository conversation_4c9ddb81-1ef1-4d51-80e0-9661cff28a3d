package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.meituan.mafka.client.consumer.IConsumerProcessor;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import java.lang.reflect.Field;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import com.meituan.mafka.client.MafkaClient;
import java.util.Properties;
import com.sankuai.scrm.core.service.realtime.task.domainservice.SceneDomainService;
import org.mockito.InjectMocks;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.stubbing.Answer;

@RunWith(MockitoJUnitRunner.class)
public class RealtimePerceptionConsumerAfterPropertiesSetTest {

    @Mock
    private IConsumerProcessor consumer;

    private RealtimePerceptionConsumer realtimePerceptionConsumer;

    @Before
    public void setUp() throws Exception {
        realtimePerceptionConsumer = new RealtimePerceptionConsumer();
    }

    @Test(expected = Exception.class)
    public void testAfterPropertiesSetException() throws Throwable {
        // Create a spy of the RealtimePerceptionConsumer
        RealtimePerceptionConsumer spyConsumer = spy(realtimePerceptionConsumer);
        // Use reflection to set the mock consumer
        Field consumerField = RealtimePerceptionConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(spyConsumer, consumer);
        // Mock the void method to throw an exception
        doThrow(new Exception()).when(consumer).recvMessageWithParallel(any(Class.class), any());
        // Execute the method under test - should throw exception
        spyConsumer.afterPropertiesSet();
    }
}
