package com.sankuai.scrm.core.service.userWechatCoupon.dal.example;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ScrmUserWechatCouponExampleCreateCriteriaTest {

    @InjectMocks
    private ScrmUserWechatCouponExample example;

    @Mock
    private List<ScrmUserWechatCouponExample.Criteria> mockOredCriteria;

    @Test
    public void testCreateCriteriaWhenOredCriteriaIsEmpty() throws Throwable {
        when(mockOredCriteria.size()).thenReturn(0);
        ScrmUserWechatCouponExample.Criteria result = example.createCriteria();
        assertNotNull(result);
        verify(mockOredCriteria).size();
        verify(mockOredCriteria).add(result);
        verifyNoMoreInteractions(mockOredCriteria);
    }

    @Test
    public void testCreateCriteriaWhenOredCriteriaIsNotEmpty() throws Throwable {
        when(mockOredCriteria.size()).thenReturn(1);
        ScrmUserWechatCouponExample.Criteria result = example.createCriteria();
        assertNotNull(result);
        verify(mockOredCriteria).size();
        verify(mockOredCriteria, never()).add(any());
        verifyNoMoreInteractions(mockOredCriteria);
    }

    @Test
    public void testCreateCriteriaReturnsValidCriteriaObject() throws Throwable {
        when(mockOredCriteria.size()).thenReturn(0);
        ScrmUserWechatCouponExample.Criteria result = example.createCriteria();
        assertNotNull(result);
        // Assuming isValid() checks if oredCriteria is empty
        assertTrue("The Criteria object should be valid when oredCriteria is empty", !mockOredCriteria.isEmpty());
        verify(mockOredCriteria).size();
        verify(mockOredCriteria).add(result);
    }

    @Test
    public void testCreateCriteriaMultipleCallsBehavior() throws Throwable {
        when(mockOredCriteria.size()).thenReturn(0).thenReturn(1);
        ScrmUserWechatCouponExample.Criteria firstCall = example.createCriteria();
        ScrmUserWechatCouponExample.Criteria secondCall = example.createCriteria();
        assertNotNull(firstCall);
        assertNotNull(secondCall);
        assertNotSame(firstCall, secondCall);
        verify(mockOredCriteria, times(2)).size();
        verify(mockOredCriteria).add(firstCall);
        verify(mockOredCriteria, never()).add(secondCall);
    }

    @Test
    public void testCreateCriteriaInConcurrentScenario() throws Throwable {
        when(mockOredCriteria.size()).thenReturn(0);
        Runnable task = () -> {
            ScrmUserWechatCouponExample.Criteria criteria = example.createCriteria();
            assertNotNull(criteria);
        };
        Thread t1 = new Thread(task);
        Thread t2 = new Thread(task);
        t1.start();
        t2.start();
        t1.join();
        t2.join();
        verify(mockOredCriteria, atLeast(2)).size();
        verify(mockOredCriteria, atLeast(2)).add(any());
    }
}
