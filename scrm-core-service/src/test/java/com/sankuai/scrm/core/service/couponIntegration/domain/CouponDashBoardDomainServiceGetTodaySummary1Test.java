package com.sankuai.scrm.core.service.couponIntegration.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.couponIntegration.dto.CouponDataSummaryDTO;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.couponIntegration.utils.DateUtils;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

public class CouponDashBoardDomainServiceGetTodaySummary1Test {

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @InjectMocks
    private CouponDashBoardDomainService couponDashBoardDomainService;

    private final String corpId = "testCorpId";

    private final String appId = "testAppId";

    private final Date startOfToday = DateUtils.getStartOfToday();

    private final Date endOfToday = DateUtils.getEndOfToday();

    @Mock
    private ScrmSceneCouponRecordsMapper sceneCouponRecordDOMapper;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试 corpId 为空的情况
     */
    @Test
    public void testGetTodaySummaryCorpIdIsNull() throws Throwable {
        // arrange
        String nullCorpId = null;
        // act
        CouponDataSummaryDTO result = couponDashBoardDomainService.getTodaySummary(nullCorpId);
        // assert
        assertNull(result);
    }

    /**
     * 测试 corpId 为空的情况
     */
    //    @Test
    //    public void testDailyCouponDataSummaryCorpIdIsNull() {
    //        // arrange
    //        String corpId = null;
    /**
     * 测试 appId 为空的情况
     */
    @Test
    public void testGetTodaySummaryAppIdIsNull() throws Throwable {
        // arrange
        when(corpAppConfigRepository.getAppIdByCorpId(corpId)).thenReturn(null);
        // act
        CouponDataSummaryDTO result = couponDashBoardDomainService.getTodaySummary(corpId);
        // assert
        assertNull(result);
    }

    //        // act
    //        couponDashBoardDomainService.dailyCouponDataSummary(corpId);
    //        // assert
    //        verify(corpAppConfigRepository, never()).getAppIdByCorpId(anyString());
    //    }
    /**
     * 测试 corpId 不为空，但 appId 为空的情况
     */
    //    @Test
    //    public void testDailyCouponDataSummaryAppIdIsNull() {
    //        // arrange
    //        String corpId = "corpId";
    /**
     * 测试发放记录和使用记录都为空的情况
     */
    @Test
    public void testGetTodaySummaryNoRecords() throws Throwable {
        // arrange
        when(corpAppConfigRepository.getAppIdByCorpId(corpId)).thenReturn(appId);
        when(sceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(Collections.emptyList());
        // act
        CouponDataSummaryDTO result = couponDashBoardDomainService.getTodaySummary(corpId);
        // assert
        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result.getCouponSendAmount());
        assertEquals(0L, result.getCouponSendCount().longValue());
        assertEquals(BigDecimal.ZERO, result.getCouponUseAmount());
        assertEquals(0L, result.getCouponUseCount().longValue());
    }

    //        when(corpAppConfigRepository.getAppIdByCorpId(corpId)).thenReturn(null);
    //        // act
    //        couponDashBoardDomainService.dailyCouponDataSummary(corpId);
    //        // assert
    //        verify(sceneCouponRecordDOMapper, never()).selectByExample(any());
    //        verify(couponDataSummaryDOMapper, never()).insertSelective(any(ScrmCouponDataSummaryDO.class));
    //    }
    /**
     * 测试 corpId 和 appId 都不为空，但查询 ScrmSceneCouponRecordDO 对象列表为空的情况
     */
    //    @Test
    //    public void testDailyCouponDataSummaryCouponRecordDOListIsEmpty() {
    //        // arrange
    //        String corpId = "corpId";
    //        when(sceneCouponRecordDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
    //        // act
    //        couponDashBoardDomainService.dailyCouponDataSummary(corpId);
    /**
     * 测试发放记录和使用记录都不为空的情况
     */
    @Test
    public void testGetTodaySummaryHasCoupon() throws Throwable {
        // arrange
        ScrmSceneCouponRecords sendRecord = new ScrmSceneCouponRecords();
        sendRecord.setCouponamount(new BigDecimal("100"));
        ScrmSceneCouponRecords useRecord = new ScrmSceneCouponRecords();
        useRecord.setCouponamount(new BigDecimal("50"));
        when(corpAppConfigRepository.getAppIdByCorpId(corpId)).thenReturn(appId);
        when(sceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(Collections.singletonList(sendRecord)).thenReturn(Collections.singletonList(useRecord));
        // act
        CouponDataSummaryDTO result = couponDashBoardDomainService.getTodaySummary(corpId);
        // assert
        assertNotNull(result);
        assertEquals(new BigDecimal("100"), result.getCouponSendAmount());
        assertEquals(1L, result.getCouponSendCount().longValue());
        assertEquals(new BigDecimal("50"), result.getCouponUseAmount());
        assertEquals(1L, result.getCouponUseCount().longValue());
    }
}
