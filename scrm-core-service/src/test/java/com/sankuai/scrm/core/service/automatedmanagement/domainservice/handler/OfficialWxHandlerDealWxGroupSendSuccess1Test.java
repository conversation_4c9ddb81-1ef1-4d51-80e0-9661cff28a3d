package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGroupSendMessageResponse;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.dto.SendListDTO;
import com.sankuai.scrm.core.service.message.push.dto.MsgTaskDetailResultDTO;
import com.sankuai.scrm.core.service.util.ThreadPoolUtils;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.invocation.InvocationOnMock;
import org.mockito.junit.*;
import org.mockito.stubbing.Answer;

class OfficialWxHandlerDealWxGroupSendSuccess1Test {

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // Mock database operations to execute immediately
        doAnswer(invocation -> {
            ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = invocation.getArgument(0);
            ScrmAmProcessOrchestrationWxInvokeDetailDOExample example = invocation.getArgument(1);
            return 1;
        }).when(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
    }

    /**
     * Tests null response handling
     */
    @Test
    void testDealWxGroupSendSuccessWithNullResponse() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS = Collections.singletonList(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            officialWxHandler.dealWxGroupSendSuccess(processOrchestrationDTO, wxInvokeLogDO, null, wxInvokeDetailDOS);
        });
        verifyNoInteractions(wxInvokeLogDOMapper);
        verifyNoInteractions(wxInvokeDetailDOMapper);
    }

    /**
     * Tests empty wxInvokeDetailDOS handling
     */
    @Test
    void testDealWxGroupSendSuccessWithEmptyDetails() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        WxGroupSendMessageResponse response = new WxGroupSendMessageResponse();
        response.setMsgId("msg123");
        response.setFailList(Collections.emptyList());
        // act
        officialWxHandler.dealWxGroupSendSuccess(processOrchestrationDTO, wxInvokeLogDO, response, Collections.emptyList());
        // assert
        assertEquals(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue(), wxInvokeLogDO.getStatus());
        verify(wxInvokeLogDOMapper).insert(wxInvokeLogDO);
        verify(wxInvokeDetailDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * Tests database exception during log insertion
     */
    @Test
    void testDealWxGroupSendSuccessWithDbException() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        WxGroupSendMessageResponse response = new WxGroupSendMessageResponse();
        response.setMsgId("msg123");
        response.setFailList(Collections.emptyList());
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS = Collections.singletonList(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        doThrow(new RuntimeException("DB error")).when(wxInvokeLogDOMapper).insert(any());
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            officialWxHandler.dealWxGroupSendSuccess(processOrchestrationDTO, wxInvokeLogDO, response, wxInvokeDetailDOS);
        });
    }
}
