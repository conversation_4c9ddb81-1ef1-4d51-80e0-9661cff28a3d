package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductInfoDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class OfficialWxHandlerBuildNormalAttachmentSupplyTest {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private ShortLinkUtils shortLinkUtils;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO;

    private List<MsgPushContentDTO> msgPushContentDTOList;

    private Map<String, MsgPushContentDTO> existedAttachmentMap;

    private StringBuilder textContent;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> invokeDetailDOList;

    @Before
    public void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setProcessOrchestrationNodeId(1L);
        actionAttachmentDTO.setProcessOrchestrationId(1L);
        actionAttachmentDTO.setProcessOrchestrationVersion("1.0");
        msgPushContentDTOList = new ArrayList<>();
        existedAttachmentMap = new HashMap<>();
        textContent = new StringBuilder();
        invokeDetailDOList = new ArrayList<>();
    }

    private void invokePrivateMethod(ScrmProcessOrchestrationDTO processOrchestrationDTO, ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO, List<MsgPushContentDTO> msgPushContentDTOList, Map<String, MsgPushContentDTO> existedAttachmentMap, StringBuilder textContent, List<ScrmAmProcessOrchestrationWxInvokeDetailDO> invokeDetailDOList) throws Exception {
        Method method = OfficialWxHandler.class.getDeclaredMethod("buildNormalAttachmentSupply", ScrmProcessOrchestrationDTO.class, ScrmProcessOrchestrationActionAttachmentDTO.class, List.class, Map.class, StringBuilder.class, List.class);
        method.setAccessible(true);
        method.invoke(officialWxHandler, processOrchestrationDTO, actionAttachmentDTO, msgPushContentDTOList, existedAttachmentMap, textContent, invokeDetailDOList);
    }

    /**
     * Test case for null supply detail DTO
     */
    @Test
    public void testBuildNormalAttachmentSupply_NullSupplyDetail() throws Throwable {
        // arrange
        actionAttachmentDTO.setAttachmentSupplyDetailDTO(null);
        // act
        invokePrivateMethod(processOrchestrationDTO, actionAttachmentDTO, msgPushContentDTOList, existedAttachmentMap, textContent, invokeDetailDOList);
        // assert
        assertTrue(msgPushContentDTOList.isEmpty());
        assertEquals(0, textContent.length());
    }

    /**
     * Test case for COUPON_PROMOTION with valid product ID
     */
    @Test
    public void testBuildNormalAttachmentSupply_CouponPromotion_ValidProduct() throws Throwable {
        // arrange
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        // COUPON_PROMOTION
        supplyDetailDTO.setSupplyType(4);
        supplyDetailDTO.setProductId("123");
        supplyDetailDTO.setTitle("Test Coupon");
        actionAttachmentDTO.setAttachmentSupplyDetailDTO(supplyDetailDTO);
        ScrmAmProcessOrchestrationProductActivityPageDO pageDO = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageDO.setMiniProgramAppId("wxde8ac0a21135c07d");
        pageDO.setThumbPicUrl("test-url");
        when(productManagementService.queryActivityPagesById(anyList())).thenReturn(Arrays.asList(pageDO));
        when(informationGatheringService.queryCommunityDistributor(any())).thenReturn("testDistributor");
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        when(productManagementService.getActivSceneCodeDO(any(), any(), any(), any(), any())).thenReturn(sceneCodeDO);
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("test-url");
        // act
        invokePrivateMethod(processOrchestrationDTO, actionAttachmentDTO, msgPushContentDTOList, existedAttachmentMap, textContent, invokeDetailDOList);
        // assert
        assertFalse(msgPushContentDTOList.isEmpty());
        assertEquals(1, msgPushContentDTOList.size());
        assertEquals(ContentTypeTEnum.MINI_PROGRAM, msgPushContentDTOList.get(0).getContentTypeTEnum());
    }

    /**
     * Test case for MANUAL_PRODUCT_PROMOTION with AI generated content
     */
    @Test
    public void testBuildNormalAttachmentSupply_ManualPromotion_AIContent() throws Throwable {
        // arrange
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        // MANUAL_PRODUCT_PROMOTION
        supplyDetailDTO.setSupplyType(2);
        supplyDetailDTO.setProductId("123");
        supplyDetailDTO.setMarketingCopySource(1);
        supplyDetailDTO.setProductType(1);
        actionAttachmentDTO.setAttachmentSupplyDetailDTO(supplyDetailDTO);
        ProductInfoDTO productInfo = new ProductInfoDTO();
        productInfo.setProductId(123L);
        productInfo.setH5Url("test-url");
        productInfo.setHeadPic("test-pic");
        when(productManagementService.queryProductInfoByProductIdAndAppId(eq(123L), anyString())).thenReturn(productInfo);
        when(productManagementService.getCommentFromAIGC(any(), anyList(), anyString())).thenReturn("AI Generated Content");
        when(informationGatheringService.queryCommunityDistributor(any())).thenReturn("testDistributor");
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("test-url");
        // act
        invokePrivateMethod(processOrchestrationDTO, actionAttachmentDTO, msgPushContentDTOList, existedAttachmentMap, textContent, invokeDetailDOList);
        // assert
        assertFalse(msgPushContentDTOList.isEmpty());
        assertEquals("AI Generated Content", textContent.toString());
    }

    /**
     * Test case for CUSTOMIZED_PRODUCT_PROMOTION
     */
    @Test
    public void testBuildNormalAttachmentSupply_CustomizedPromotion() throws Throwable {
        // arrange
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        // CUSTOMIZED_PRODUCT_PROMOTION
        supplyDetailDTO.setSupplyType(3);
        supplyDetailDTO.setJumpUrl("test-jump-url");
        supplyDetailDTO.setMarketingCopy("Test Marketing Copy");
        supplyDetailDTO.setShelfName("Test Shelf");
        actionAttachmentDTO.setAttachmentSupplyDetailDTO(supplyDetailDTO);
        when(informationGatheringService.queryCommunityDistributor(any())).thenReturn("testDistributor");
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("test-url");
        // act
        invokePrivateMethod(processOrchestrationDTO, actionAttachmentDTO, msgPushContentDTOList, existedAttachmentMap, textContent, invokeDetailDOList);
        // assert
        assertFalse(msgPushContentDTOList.isEmpty());
        assertEquals("Test Marketing Copy", textContent.toString());
    }

    /**
     * Test case for invalid product IDs
     */
    @Test
    public void testBuildNormalAttachmentSupply_InvalidProductIds() throws Throwable {
        // arrange
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        // MANUAL_PRODUCT_PROMOTION
        supplyDetailDTO.setSupplyType(2);
        // Mix of valid and invalid IDs
        supplyDetailDTO.setProductId("123,abc,456");
        supplyDetailDTO.setMarketingCopySource(1);
        supplyDetailDTO.setProductType(1);
        actionAttachmentDTO.setAttachmentSupplyDetailDTO(supplyDetailDTO);
        // Mock for valid product ID
        ProductInfoDTO productInfo = new ProductInfoDTO();
        productInfo.setProductId(123L);
        productInfo.setH5Url("test-url");
        productInfo.setHeadPic("test-pic");
        productInfo.setProductTitle("Test Product");
        when(productManagementService.queryProductInfoByProductIdAndAppId(eq(123L), anyString())).thenReturn(productInfo);
        when(informationGatheringService.queryCommunityDistributor(any())).thenReturn("testDistributor");
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("test-url");
        // act
        invokePrivateMethod(processOrchestrationDTO, actionAttachmentDTO, msgPushContentDTOList, existedAttachmentMap, textContent, invokeDetailDOList);
        // assert
        assertEquals(1, msgPushContentDTOList.size());
        verify(productManagementService, times(1)).queryProductInfoByProductIdAndAppId(eq(123L), anyString());
        verify(productManagementService, never()).queryProductInfoByProductIdAndAppId(eq(456L), anyString());
    }

    /**
     * Test case for existing attachment in map
     */
    @Test
    public void testBuildNormalAttachmentSupply_ExistingAttachment() throws Throwable {
        // arrange
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        // CUSTOMIZED_PRODUCT_PROMOTION
        supplyDetailDTO.setSupplyType(3);
        supplyDetailDTO.setJumpUrl("test-url");
        actionAttachmentDTO.setAttachmentSupplyDetailDTO(supplyDetailDTO);
        MsgPushContentDTO existingContent = new MsgPushContentDTO();
        existingContent.setContentTypeTEnum(ContentTypeTEnum.MINI_PROGRAM);
        existedAttachmentMap.put("3-test-url", existingContent);
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("test-url");
        // act
        invokePrivateMethod(processOrchestrationDTO, actionAttachmentDTO, msgPushContentDTOList, existedAttachmentMap, textContent, invokeDetailDOList);
        // assert
        assertEquals(1, msgPushContentDTOList.size());
        assertEquals(existingContent.getContentTypeTEnum(), msgPushContentDTOList.get(0).getContentTypeTEnum());
    }

    /**
     * Test case for MANUAL_PRODUCT_PROMOTION with multiple products
     */
    @Test
    public void testBuildNormalAttachmentSupply_ManualPromotion_MultipleProducts() throws Throwable {
        // arrange
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        // MANUAL_PRODUCT_PROMOTION
        supplyDetailDTO.setSupplyType(2);
        supplyDetailDTO.setProductId("123,456");
        // Ensure this is set
        supplyDetailDTO.setMarketingCopySource(1);
        supplyDetailDTO.setProductType(1);
        actionAttachmentDTO.setAttachmentSupplyDetailDTO(supplyDetailDTO);
        ProductInfoDTO productInfo = new ProductInfoDTO();
        productInfo.setProductId(123L);
        productInfo.setH5Url("test-url");
        productInfo.setHeadPic("test-pic");
        when(productManagementService.queryProductInfoByProductIdAndAppId(anyLong(), anyString())).thenReturn(productInfo);
        when(informationGatheringService.queryCommunityDistributor(any())).thenReturn("testDistributor");
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("test-url");
        // act
        invokePrivateMethod(processOrchestrationDTO, actionAttachmentDTO, msgPushContentDTOList, existedAttachmentMap, textContent, invokeDetailDOList);
        // assert
        assertEquals(2, msgPushContentDTOList.size());
    }
}
