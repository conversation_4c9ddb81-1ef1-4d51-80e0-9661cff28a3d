package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.automatedmanagement.request.CreatePersonaCrowdPackRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.CreatePersonaCrowdPackResultVO;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.CrowdPackInnerService;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.exception.PersonaCrowdPackException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test class for ScrmAutoManagementServiceImpl#createPersonaCrowdPack
 */
@RunWith(MockitoJUnitRunner.class)
public class ScrmAutoManagementServiceImplCreatePersonaCrowdPackTest {

    @InjectMocks
    private ScrmAutoManagementServiceImpl scrmAutoManagementService;

    @Mock
    private CrowdPackInnerService crowdPackInnerService;

    private CreatePersonaCrowdPackRequest request;

    private CreatePersonaCrowdPackResultVO resultVO;

    @Before
    public void setUp() {
        request = new CreatePersonaCrowdPackRequest();
        resultVO = new CreatePersonaCrowdPackResultVO();
    }

    /**
     * Test case for null request
     */
    @Test
    public void testCreatePersonaCrowdPack_NullRequest() throws Throwable {
        // arrange
        request = null;
        // act
        RemoteResponse<CreatePersonaCrowdPackResultVO> response = scrmAutoManagementService.createPersonaCrowdPack(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for request with null personaCrowdId
     */
    @Test
    public void testCreatePersonaCrowdPack_NullPersonaCrowdId() throws Throwable {
        // arrange
        request.setPersonaCrowdId(null);
        request.setAppId("test-app");
        // act
        RemoteResponse<CreatePersonaCrowdPackResultVO> response = scrmAutoManagementService.createPersonaCrowdPack(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for request with blank appId
     */
    @Test
    public void testCreatePersonaCrowdPack_BlankAppId() throws Throwable {
        // arrange
        request.setPersonaCrowdId(123);
        request.setAppId("");
        // act
        RemoteResponse<CreatePersonaCrowdPackResultVO> response = scrmAutoManagementService.createPersonaCrowdPack(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for successful creation
     */
    @Test
    public void testCreatePersonaCrowdPack_Success() throws Throwable {
        // arrange
        request.setPersonaCrowdId(123);
        request.setAppId("test-app");
        when(crowdPackInnerService.createPersonaCrowdPack(request)).thenReturn(resultVO);
        // act
        RemoteResponse<CreatePersonaCrowdPackResultVO> response = scrmAutoManagementService.createPersonaCrowdPack(request);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(resultVO, response.getData());
    }

    /**
     * Test case for PersonaCrowdPackException
     */
    @Test
    public void testCreatePersonaCrowdPack_PersonaCrowdPackException() throws Throwable {
        // arrange
        request.setPersonaCrowdId(123);
        request.setAppId("test-app");
        String errorMsg = "Persona crowd pack error";
        when(crowdPackInnerService.createPersonaCrowdPack(request)).thenThrow(new PersonaCrowdPackException(errorMsg));
        // act
        RemoteResponse<CreatePersonaCrowdPackResultVO> response = scrmAutoManagementService.createPersonaCrowdPack(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for general exception
     */
    @Test
    public void testCreatePersonaCrowdPack_GeneralException() throws Throwable {
        // arrange
        request.setPersonaCrowdId(123);
        request.setAppId("test-app");
        when(crowdPackInnerService.createPersonaCrowdPack(request)).thenThrow(new RuntimeException("Unexpected error"));
        // act
        RemoteResponse<CreatePersonaCrowdPackResultVO> response = scrmAutoManagementService.createPersonaCrowdPack(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }
}
