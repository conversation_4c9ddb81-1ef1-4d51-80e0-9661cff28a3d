package com.sankuai.scrm.core.service.external.contact.domain;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.external.contact.dal.babymapper.ContactUserMapper;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.dal.example.ContactUserExample;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ContactUserDomainQueryExternalUserFollowStaffListTest {

    @InjectMocks
    private ContactUserDomain contactUserDomain;

    @Mock
    private ContactUserMapper contactUserMapper;

    private String corpId = "corpId";

    private String externalUserId = "externalUserId";

    private ContactUser contactUser = new ContactUser();

    {
        contactUser.setStaffId("staffId");
    }

    /**
     * 测试 corpId 或 externalUserId 为空的情况
     */
    @Test
    public void testQueryExternalUserFollowStaffListWithEmptyCorpIdOrExternalUserId() throws Throwable {
        List<String> result = contactUserDomain.queryExternalUserFollowStaffList(null, externalUserId);
        assertEquals(Collections.emptyList(), result);
        result = contactUserDomain.queryExternalUserFollowStaffList(corpId, null);
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试查询结果为空的情况
     */
    @Test
    public void testQueryExternalUserFollowStaffListWithEmptyResult() throws Throwable {
        when(contactUserMapper.selectByExample(any(ContactUserExample.class))).thenReturn(Collections.emptyList());
        List<String> result = contactUserDomain.queryExternalUserFollowStaffList(corpId, externalUserId);
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试查询结果不为空的情况
     */
    @Test
    public void testQueryExternalUserFollowStaffListWithNonEmptyResult() throws Throwable {
        when(contactUserMapper.selectByExample(any(ContactUserExample.class))).thenReturn(Arrays.asList(contactUser));
        List<String> result = contactUserDomain.queryExternalUserFollowStaffList(corpId, externalUserId);
        assertEquals(Arrays.asList("staffId"), result);
    }
}
