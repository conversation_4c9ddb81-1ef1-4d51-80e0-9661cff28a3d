package com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.impl;

import com.sankuai.dz.srcm.automatedmanagement.request.CreatePersonaCrowdPackRequest;
import com.sankuai.dz.srcm.automatedmanagement.request.QueryPersonaCrowdInfoRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.CreatePersonaCrowdPackResultVO;
import com.sankuai.dz.srcm.automatedmanagement.response.QueryPersonaCrowdPackDetailResultVO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackBaseInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.dto.QueryPersonaCrowdPackDetailDTO;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.exception.PersonaCrowdPackException;
import com.sankuai.scrm.core.service.infrastructure.acl.persona.PersonaService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CrowdPackInnerServiceImplQueryPersonaCrowdPackDetailTest {

    @InjectMocks
    private CrowdPackInnerServiceImpl crowdPackInnerService;

    @Mock
    private PersonaService personaService;

    private QueryPersonaCrowdInfoRequest request;

    private QueryPersonaCrowdPackDetailDTO mockDto;

    @Mock
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock
    private ExecuteWriteDomainService executeWriteDomainService;

    @Before
    public void setUp() {
        request = new QueryPersonaCrowdInfoRequest();
        request.setPersonaCrowdId(123);
        mockDto = new QueryPersonaCrowdPackDetailDTO();
        mockDto.setCrowdPackId(123);
        mockDto.setCrowdName("TestCrowd");
        mockDto.setOwner("TestOwner");
        mockDto.setCrowdPackSize(100L);
        mockDto.setExportStatus(1);
    }

    /**
     * Test successful query with valid data
     */
    @Test
    public void testQueryPersonaCrowdPackDetail_Success() {
        // arrange
        when(personaService.getPersonaCrowdInfo(anyInt())).thenReturn(mockDto);
        // act
        QueryPersonaCrowdPackDetailResultVO result = crowdPackInnerService.queryPersonaCrowdPackDetail(request);
        // assert
        assertNotNull(result);
        assertEquals(mockDto.getCrowdPackId(), result.getCrowdPackId());
        assertEquals(mockDto.getCrowdName(), result.getCrowdName());
        assertEquals(mockDto.getOwner(), result.getOwner());
        assertEquals(mockDto.getCrowdPackSize(), result.getCrowdPackSize());
        assertEquals(mockDto.getExportStatus(), result.getExportStatus());
    }

    /**
     * Test when PersonaService returns null
     */
    @Test(expected = PersonaCrowdPackException.class)
    public void testQueryPersonaCrowdPackDetail_NullResponse() {
        // arrange
        when(personaService.getPersonaCrowdInfo(anyInt())).thenReturn(null);
        // act
        crowdPackInnerService.queryPersonaCrowdPackDetail(request);
        // assert - exception expected
    }

    /**
     * Test with null request
     */
    @Test(expected = NullPointerException.class)
    public void testQueryPersonaCrowdPackDetail_NullRequest() {
        // act
        crowdPackInnerService.queryPersonaCrowdPackDetail(null);
        // assert - exception expected
    }

    /**
     * Test case for empty appId
     */
    @Test(expected = PersonaCrowdPackException.class)
    public void testCreatePersonaCrowdPack_EmptyAppId() throws Throwable {
        // arrange
        CreatePersonaCrowdPackRequest request = new CreatePersonaCrowdPackRequest();
        request.setAppId("");
        request.setMisId("mis1");
        request.setSsoMisId("mis1");
        request.setPersonaCrowdId(1);
        // act
        crowdPackInnerService.createPersonaCrowdPack(request);
    }

    /**
     * Test case for null appId
     */
    @Test(expected = PersonaCrowdPackException.class)
    public void testCreatePersonaCrowdPack_NullAppId() throws Throwable {
        // arrange
        CreatePersonaCrowdPackRequest request = new CreatePersonaCrowdPackRequest();
        request.setAppId(null);
        request.setMisId("mis1");
        request.setSsoMisId("mis1");
        request.setPersonaCrowdId(1);
        // act
        crowdPackInnerService.createPersonaCrowdPack(request);
    }

    /**
     * Test case for mismatched misId and ssoMisId
     */
    @Test(expected = PersonaCrowdPackException.class)
    public void testCreatePersonaCrowdPack_MismatchedMisId() throws Throwable {
        // arrange
        CreatePersonaCrowdPackRequest request = new CreatePersonaCrowdPackRequest();
        request.setAppId("app1");
        request.setMisId("mis1");
        request.setSsoMisId("mis2");
        request.setPersonaCrowdId(1);
        // act
        crowdPackInnerService.createPersonaCrowdPack(request);
    }

    /**
     * Test case for existing persona pack
     */
    @Test(expected = PersonaCrowdPackException.class)
    public void testCreatePersonaCrowdPack_ExistingPersonaPack() throws Throwable {
        // arrange
        CreatePersonaCrowdPackRequest request = new CreatePersonaCrowdPackRequest();
        request.setAppId("app1");
        request.setMisId("mis1");
        request.setSsoMisId("mis1");
        request.setPersonaCrowdId(1);
        when(crowdPackReadDomainService.isPersonaPackExists("app1", 1)).thenReturn(true);
        // act
        crowdPackInnerService.createPersonaCrowdPack(request);
    }

    /**
     * Test case for empty user list from persona service
     */
    @Test(expected = PersonaCrowdPackException.class)
    public void testCreatePersonaCrowdPack_EmptyUserList() throws Throwable {
        // arrange
        CreatePersonaCrowdPackRequest request = new CreatePersonaCrowdPackRequest();
        request.setAppId("app1");
        request.setMisId("mis1");
        request.setSsoMisId("mis1");
        request.setPersonaCrowdId(1);
        when(crowdPackReadDomainService.isPersonaPackExists("app1", 1)).thenReturn(false);
        when(personaService.getUserIdListByCrowdId(1)).thenReturn("url1");
        when(personaService.getMtUserIdListByUrl("url1")).thenReturn(new ArrayList<>());
        // act
        crowdPackInnerService.createPersonaCrowdPack(request);
    }

    /**
     * Test case for invalid crowdPackId returned from write service
     */
    @Test(expected = PersonaCrowdPackException.class)
    public void testCreatePersonaCrowdPack_InvalidCrowdPackId() throws Throwable {
        // arrange
        CreatePersonaCrowdPackRequest request = new CreatePersonaCrowdPackRequest();
        request.setAppId("app1");
        request.setMisId("mis1");
        request.setSsoMisId("mis1");
        request.setPersonaCrowdId(1);
        when(crowdPackReadDomainService.isPersonaPackExists("app1", 1)).thenReturn(false);
        when(personaService.getUserIdListByCrowdId(1)).thenReturn("url1");
        when(personaService.getMtUserIdListByUrl("url1")).thenReturn(Arrays.asList(1L, 2L));
        when(crowdPackWriteDomainService.insertCrowdPackBaseInfo(any())).thenReturn(0L);
        // act
        crowdPackInnerService.createPersonaCrowdPack(request);
    }

    /**
     * Test case for successful creation of persona crowd pack
     */
    @Test
    public void testCreatePersonaCrowdPack_Success() throws Throwable {
        // arrange
        CreatePersonaCrowdPackRequest request = new CreatePersonaCrowdPackRequest();
        request.setAppId("app1");
        request.setMisId("mis1");
        request.setSsoMisId("mis1");
        request.setPersonaCrowdId(1);
        List<Long> userIds = Arrays.asList(1L, 2L);
        when(crowdPackReadDomainService.isPersonaPackExists("app1", 1)).thenReturn(false);
        when(personaService.getUserIdListByCrowdId(1)).thenReturn("url1");
        when(personaService.getMtUserIdListByUrl("url1")).thenReturn(userIds);
        // Capture the ScrmAmCrowdPackBaseInfoDO argument
        ArgumentCaptor<ScrmAmCrowdPackBaseInfoDO> baseInfoCaptor = ArgumentCaptor.forClass(ScrmAmCrowdPackBaseInfoDO.class);
        // Mock the behavior and set ID
        when(crowdPackWriteDomainService.insertCrowdPackBaseInfo(baseInfoCaptor.capture())).thenAnswer(invocation -> {
            ScrmAmCrowdPackBaseInfoDO baseInfo = invocation.getArgument(0);
            baseInfo.setId(100L);
            return 100L;
        });
        // act
        CreatePersonaCrowdPackResultVO result = crowdPackInnerService.createPersonaCrowdPack(request);
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(100L), result.getCrowdPackId());
        // Verify the captured baseInfo
        ScrmAmCrowdPackBaseInfoDO capturedBaseInfo = baseInfoCaptor.getValue();
        assertEquals("app1", capturedBaseInfo.getAppId());
        assertEquals("mis1", capturedBaseInfo.getCreatorId());
        assertEquals("mis1", capturedBaseInfo.getLastUpdaterId());
        assertEquals("Persona同步人群", capturedBaseInfo.getName());
        assertEquals("ID: 1", capturedBaseInfo.getRemark());
        assertEquals(Integer.valueOf(1), capturedBaseInfo.getPersonaId());
        assertEquals(Long.valueOf(100L), capturedBaseInfo.getId());
        // Verify executeWriteDomainService call with any string for version
        verify(executeWriteDomainService).concurrentUpdatePersonaCrowdPackAsync(eq("app1"), eq(new HashSet<>(userIds)), eq(100L), anyString());
    }

    /**
     * Test case for null URL from persona service
     */
    @Test(expected = PersonaCrowdPackException.class)
    public void testCreatePersonaCrowdPack_NullPersonaUrl() throws Throwable {
        // arrange
        CreatePersonaCrowdPackRequest request = new CreatePersonaCrowdPackRequest();
        request.setAppId("app1");
        request.setMisId("mis1");
        request.setSsoMisId("mis1");
        request.setPersonaCrowdId(1);
        when(crowdPackReadDomainService.isPersonaPackExists("app1", 1)).thenReturn(false);
        when(personaService.getUserIdListByCrowdId(1)).thenReturn(null);
        // act
        crowdPackInnerService.createPersonaCrowdPack(request);
    }

    /**
     * Test case for empty URL from persona service
     */
    @Test(expected = PersonaCrowdPackException.class)
    public void testCreatePersonaCrowdPack_EmptyPersonaUrl() throws Throwable {
        // arrange
        CreatePersonaCrowdPackRequest request = new CreatePersonaCrowdPackRequest();
        request.setAppId("app1");
        request.setMisId("mis1");
        request.setSsoMisId("mis1");
        request.setPersonaCrowdId(1);
        when(crowdPackReadDomainService.isPersonaPackExists("app1", 1)).thenReturn(false);
        when(personaService.getUserIdListByCrowdId(1)).thenReturn("");
        // act
        crowdPackInnerService.createPersonaCrowdPack(request);
    }
}
