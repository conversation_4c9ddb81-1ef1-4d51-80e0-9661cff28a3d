package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.persona.PersonaService;
import com.sankuai.scrm.core.service.util.CronUtils;
import java.lang.reflect.Method;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.io.IOException;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ExecuteWriteDomainServiceCheckPeriodicTimedTaskStatusTest {

    @Mock
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    private ScrmAmProcessOrchestrationInfoDO infoDO;

    @Mock
    private PersonaService personaService;

    @Mock
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Before
    public void setUp() {
        infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_TIMED_TASK.getValue().byteValue());
        infoDO.setCron("0 0 12 * * ?");
    }

    /**
     * Get access to the private getRecordTime method using reflection
     */
    private Method getRecordTimeMethod() throws Exception {
        Method method = ExecuteWriteDomainService.class.getDeclaredMethod("getRecordTime");
        method.setAccessible(true);
        return method;
    }

    /**
     * Test case for when the status is RUNNING and there are pending WxInvokeLog entries.
     */
    @Test
    public void testCheckPeriodicTimedTaskStatus_RunningStatus_PendingWxInvokeLogEntries() throws Throwable {
        // arrange
        Calendar calendar = Calendar.getInstance();
        infoDO.setBeginTime(calendar.getTime());
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        infoDO.setEndTime(calendar.getTime());
        infoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.RUNNING.getValue().byteValue());
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(invokeLogDO));
        // act
        executeWriteDomainService.checkPeriodicTimedTaskStatus(infoDO);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).updateByPrimaryKey(infoDO);
        assertEquals((int) ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENDING.getValue().byteValue(), (int) infoDO.getStatus());
    }

    /**
     * Test case for when the status is MESSAGE_SENDING and there are no pending WxInvokeLog entries.
     */
    @Test
    public void testCheckPeriodicTimedTaskStatus_MessageSendingStatus_NoPendingWxInvokeLogEntries() throws Throwable {
        // arrange
        Calendar calendar = Calendar.getInstance();
        infoDO.setBeginTime(calendar.getTime());
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        infoDO.setEndTime(calendar.getTime());
        infoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENDING.getValue().byteValue());
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(processOrchestrationReadDomainService.queryGoalFuture(any())).thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));
        // act
        executeWriteDomainService.checkPeriodicTimedTaskStatus(infoDO);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).updateByPrimaryKey(infoDO);
        assertEquals((int) ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENT.getValue().byteValue(), (int) infoDO.getStatus());
    }

    /**
     * Test successful execution with valid user list
     */
    @Test
    public void testUpdatePersonaCrowdPackSubTask_Success() throws Throwable {
        // arrange
        Integer personaId = 123;
        Long packId = 456L;
        String appId = "testApp";
        String packVersion = "v1";
        String url = "http://test.url";
        when(personaService.getUserIdListByCrowdId(personaId)).thenReturn(url);
        when(personaService.getMtUserIdListByUrl(url)).thenReturn(Arrays.asList(1L, 2L));
        doNothing().when(crowdPackWriteDomainService).deleteCrowdPackDetailInfoByPackIdAndVersion(packId, packVersion);
        // act
        executeWriteDomainService.updatePersonaCrowdPackSubTask(personaId, packId, appId, packVersion);
        // assert
        verify(personaService, times(1)).getUserIdListByCrowdId(personaId);
        verify(personaService, times(1)).getMtUserIdListByUrl(url);
        verify(crowdPackWriteDomainService, times(1)).deleteCrowdPackDetailInfoByPackIdAndVersion(packId, packVersion);
    }

    /**
     * Test execution with empty user list
     */
    @Test
    public void testUpdatePersonaCrowdPackSubTask_EmptyUserList() throws Throwable {
        // arrange
        Integer personaId = 123;
        Long packId = 456L;
        String appId = "testApp";
        String packVersion = "v1";
        String url = "http://test.url";
        when(personaService.getUserIdListByCrowdId(personaId)).thenReturn(url);
        when(personaService.getMtUserIdListByUrl(url)).thenReturn(Collections.emptyList());
        doNothing().when(crowdPackWriteDomainService).deleteCrowdPackDetailInfoByPackIdAndVersion(packId, packVersion);
        // act
        executeWriteDomainService.updatePersonaCrowdPackSubTask(personaId, packId, appId, packVersion);
        // assert
        verify(personaService, times(1)).getUserIdListByCrowdId(personaId);
        verify(personaService, times(1)).getMtUserIdListByUrl(url);
        verify(crowdPackWriteDomainService, times(1)).deleteCrowdPackDetailInfoByPackIdAndVersion(packId, packVersion);
    }

    /**
     * Test execution with null URL returned
     */
    @Test
    public void testUpdatePersonaCrowdPackSubTask_NullUrl() throws Throwable {
        // arrange
        Integer personaId = 123;
        Long packId = 456L;
        String appId = "testApp";
        String packVersion = "v1";
        when(personaService.getUserIdListByCrowdId(personaId)).thenReturn(null);
        // act
        executeWriteDomainService.updatePersonaCrowdPackSubTask(personaId, packId, appId, packVersion);
        // assert
        verify(personaService, times(1)).getUserIdListByCrowdId(personaId);
        verify(personaService, never()).getMtUserIdListByUrl(anyString());
        verify(crowdPackWriteDomainService, times(1)).deleteCrowdPackDetailInfoByPackIdAndVersion(packId, packVersion);
    }

    /**
     * Test execution when PersonaService throws IOException
     */
    @Test(expected = IOException.class)
    public void testUpdatePersonaCrowdPackSubTask_PersonaServiceThrowsException() throws Throwable {
        // arrange
        Integer personaId = 123;
        Long packId = 456L;
        String appId = "testApp";
        String packVersion = "v1";
        String url = "http://test.url";
        when(personaService.getUserIdListByCrowdId(personaId)).thenReturn(url);
        doThrow(new IOException("Test exception")).when(personaService).getMtUserIdListByUrl(url);
        // act
        executeWriteDomainService.updatePersonaCrowdPackSubTask(personaId, packId, appId, packVersion);
    }

    /**
     * Test execution with minimum valid persona ID
     */
    @Test
    public void testUpdatePersonaCrowdPackSubTask_MinimumPersonaId() throws Throwable {
        // arrange
        Integer personaId = 1;
        Long packId = 456L;
        String appId = "testApp";
        String packVersion = "v1";
        String url = "http://test.url";
        when(personaService.getUserIdListByCrowdId(personaId)).thenReturn(url);
        when(personaService.getMtUserIdListByUrl(url)).thenReturn(Arrays.asList(1L));
        doNothing().when(crowdPackWriteDomainService).deleteCrowdPackDetailInfoByPackIdAndVersion(packId, packVersion);
        // act
        executeWriteDomainService.updatePersonaCrowdPackSubTask(personaId, packId, appId, packVersion);
        // assert
        verify(personaService, times(1)).getUserIdListByCrowdId(personaId);
        verify(personaService, times(1)).getMtUserIdListByUrl(url);
        verify(crowdPackWriteDomainService, times(1)).deleteCrowdPackDetailInfoByPackIdAndVersion(packId, packVersion);
    }

    /**
     * Test that getRecordTime returns date object representing start of current day
     * Method: getRecordTime
     * Scenario: Verify the returned date represents midnight (00:00:00.000) of current day
     */
    @Test
    public void testGetRecordTime_ShouldReturnStartOfCurrentDay() throws Throwable {
        // arrange
        Calendar expectedCal = Calendar.getInstance();
        expectedCal.set(Calendar.HOUR_OF_DAY, 0);
        expectedCal.set(Calendar.MINUTE, 0);
        expectedCal.set(Calendar.SECOND, 0);
        expectedCal.set(Calendar.MILLISECOND, 0);
        // act
        Date result = (Date) getRecordTimeMethod().invoke(executeWriteDomainService);
        Calendar resultCal = Calendar.getInstance();
        resultCal.setTime(result);
        // assert
        assertEquals(expectedCal.get(Calendar.YEAR), resultCal.get(Calendar.YEAR));
        assertEquals(expectedCal.get(Calendar.MONTH), resultCal.get(Calendar.MONTH));
        assertEquals(expectedCal.get(Calendar.DAY_OF_MONTH), resultCal.get(Calendar.DAY_OF_MONTH));
        assertEquals(0, resultCal.get(Calendar.HOUR_OF_DAY));
        assertEquals(0, resultCal.get(Calendar.MINUTE));
        assertEquals(0, resultCal.get(Calendar.SECOND));
        assertEquals(0, resultCal.get(Calendar.MILLISECOND));
    }

    /**
     * Test that getRecordTime returns correct date using system timezone
     * Method: getRecordTime
     * Scenario: Verify the returned date matches the expected date from LocalDate conversion
     */
    @Test
    public void testGetRecordTime_ShouldMatchLocalDateConversion() throws Throwable {
        // arrange
        LocalDateTime expectedDateTime = LocalDate.now().atStartOfDay();
        Date expected = Date.from(expectedDateTime.atZone(ZoneId.systemDefault()).toInstant());
        // act
        Date result = (Date) getRecordTimeMethod().invoke(executeWriteDomainService);
        // assert
        assertEquals(expected, result);
    }

    /**
     * Test that getRecordTime handles DST transition days correctly
     * Method: getRecordTime
     * Scenario: Verify the method works correctly on days with timezone transitions
     */
    @Test
    public void testGetRecordTime_ShouldHandleDSTTransition() throws Throwable {
        // arrange
        LocalDate today = LocalDate.now();
        LocalDateTime midnight = today.atStartOfDay();
        Date expected = Date.from(midnight.atZone(ZoneId.systemDefault()).toInstant());
        // act
        Date result = (Date) getRecordTimeMethod().invoke(executeWriteDomainService);
        // assert
        assertEquals(expected.getTime(), result.getTime());
    }
}
