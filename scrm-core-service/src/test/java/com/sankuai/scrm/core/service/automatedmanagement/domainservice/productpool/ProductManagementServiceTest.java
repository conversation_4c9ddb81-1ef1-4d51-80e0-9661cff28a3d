package com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool;

import cn.hutool.core.lang.Pair;
import cn.hutool.core.map.MapUtil;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.poi.transform.CityTransformService;
import com.dianping.tgc.process.enums.PlatformEnum;
import com.dianping.tpfun.product.api.sku.ProductService;
import com.dianping.tpfun.product.api.sku.common.enums.SPUTypeEnum;
import com.dianping.vc.enums.VCClientTypeEnum;
import com.google.common.collect.Lists;
import com.meituan.service.mobile.group.geo.bean.CityInfo;
import com.meituan.service.mobile.group.geo.service.CityService;
import com.sankuai.dz.srcm.automatedmanagement.dto.activitypage.DistanceDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.activitypage.SaleDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.activitypage.ShelfProductInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationProductItemsProductTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationProductTagsTagTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationUploadProductsEnum;
import com.sankuai.dz.srcm.automatedmanagement.request.activitypage.QueryActivityPageShelfProductRequest;
import com.sankuai.dz.srcm.automatedmanagement.request.productpool.PageProductRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.activitypage.ShelfProductInfoResultVO;
import com.sankuai.dz.srcm.automatedmanagement.response.productpool.PageProductResult;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dztheme.deal.DealProductService;
import com.sankuai.dztheme.deal.dto.DealProductAttrDTO;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.dto.DealProductSaleDTO;
import com.sankuai.dztheme.deal.dto.DealProductShopDTO;
import com.sankuai.dztheme.deal.req.DealProductRequest;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.generalproduct.GeneralProductService;
import com.sankuai.dztheme.generalproduct.req.GeneralProductRequest;
import com.sankuai.dztheme.generalproduct.res.GeneralProductDTO;
import com.sankuai.dztheme.generalproduct.res.GeneralProductResult;
import com.sankuai.dztheme.generalproduct.res.GeneralProductShopDTO;
import com.sankuai.general.product.query.center.client.dto.*;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.medicalcosmetology.product.selectify.api.dto.commonpool.*;
import com.sankuai.medicalcosmetology.product.selectify.api.response.BaseResponse;
import com.sankuai.medicalcosmetology.product.selectify.api.response.CommonErrorCode;
import com.sankuai.medicalcosmetology.product.selectify.api.response.PageBaseResponse;
import com.sankuai.medicalcosmetology.product.selectify.api.service.CommonRecommendService;
import com.sankuai.medicalcosmetology.product.selectify.api.service.CommonSelectPoolItemService;
import com.sankuai.medicalcosmetology.product.selectify.api.service.CommonSelectPoolService;
import com.sankuai.mpproduct.idservice.api.enums.BizProductIdType;
import com.sankuai.scrm.core.service.BaseMockTest;
import com.sankuai.scrm.core.service.aigc.service.SupplyMarketingTextService;
import com.sankuai.scrm.core.service.aigc.service.request.QuerySupplyMarketingTextRequest;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductItemsDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductTagsDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationActivSceneCodeDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationProductItemsDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationProductTagMapDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationProductTagsDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationActivSceneCodeDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductActivityPageDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductItemsDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductTagsDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationProductTagMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.activitypage.ActivityPageManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.DownloadUploadingResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.QueryProductInfoDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.user.domain.ScrmUserTagDomainService;
import com.sankuai.scrm.core.service.user.enums.ScrmUserTagEnum;
import org.apache.commons.lang3.StringUtils;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.mockito.*;

import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * ProductManagementService 测试类
 */
public class ProductManagementServiceTest extends BaseMockTest {

    @InjectMocks
    private ProductManagementService productManagementService;

    @Mock(lenient = true)
    private ProductIdService productIdService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductItemsDOMapper productItemsDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductTagsDOMapper productTagsDOMapper;

    @Mock(lenient = true)
    private ExtScrmAmProcessOrchestrationProductTagMapDOMapper extScrmAmProcessOrchestrationProductTagMapDOMapper;

    @Mock(lenient = true)
    private ProductInfoService productInfoService;

    @Mock(lenient = true)
    private CommonSelectPoolService commonSelectPoolService;

    @Mock(lenient = true)
    private CommonSelectPoolItemService commonSelectPoolItemService;

    @Mock(lenient = true)
    private ActivityPageManagementService activityPageManagementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationActivSceneCodeDOMapper activSceneCodeDOMapper;

    @Mock(lenient = true)
    private ProductService skuProductService;

    @Mock(lenient = true)
    private DealGroupQueryService queryCenterDealGroupQueryService;

    @Mock(lenient = true)
    private CityTransformService cityTransformService;

    @Mock(lenient = true)
    private CityService cityService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductActivityPageDOMapper activityPageDOMapper;

    @Mock(lenient = true)
    private CommonRecommendService commonRecommendService;

    @Mock(lenient = true)
    private ScrmUserTagDomainService userTagDomainService;

    @Mock(lenient = true)
    private SupplyMarketingTextService supplyMarketingTextService;

    @Mock(lenient = true)
    private InformationGatheringService informationGatheringService;

    @Mock(lenient = true)
    private DealProductService dealProductService;

    @Mock(lenient = true)
    private GeneralProductService generalProductService;

    private Method cleanPicUrlMethod;

    private final String appId = "testAppId";

    private final Long productId = 1L;

    private final Integer productType = 1;

    @Before
    public void setUp() throws NoSuchMethodException {
        cleanPicUrlMethod = ProductManagementService.class.getDeclaredMethod("cleanPicUrl", String.class);
        cleanPicUrlMethod.setAccessible(true);
        MockitoAnnotations.initMocks(this);
        // 模拟团购商品返回数据
        initDealGroupProductInfos();
        // 模拟泛商品返回数据
        //        initGeneralProductInfos();
    }

    private void initGeneralProductInfos() {
        Map<Integer, GeneralProductDTO> products = new HashMap<>();
        GeneralProductDTO product1 = new GeneralProductDTO();
        product1.setProductId(1);
        product1.setMarketPrice("100");
        product1.setHeadPic("pic1");
        products.put(1, product1);
        GeneralProductDTO product2 = new GeneralProductDTO();
        product2.setProductId(2);
        product2.setMarketPrice("10");
        product2.setHeadPic("pic2");
        products.put(2, product2);
        try {
            when(productInfoService.batchGetGeneralAsync()).thenReturn(products);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    private void initDealGroupProductInfos() {
        DealGroupDTO dealGroupDTO1 = new DealGroupDTO();
        DealGroupDTO dealGroupDTO2 = new DealGroupDTO();
        when(productInfoService.batchQueryDealGroupProducts(Lists.newArrayList(1L, 2L))).thenReturn(Lists.newArrayList(dealGroupDTO1, dealGroupDTO2));
    }

    /**
     * 测试 uploadProducts 方法，当 appId 为空时
     */
    @Test
    public void testUploadProductsAppIdEmpty() {
        String appId = "test";
        List<ProductPoolUpdateObjectTemplateDTO> products = new ArrayList<>();
        Map<Integer, List<DownloadUploadingResultDTO>> result = productManagementService.uploadProducts(products, appId);
        assertTrue("应当返回空结果", result.get(ScrmAmProcessOrchestrationUploadProductsEnum.FAILED.getCode()).isEmpty());
    }

    /**
     * 测试 uploadProducts 方法，当 products 为空时
     */
    @Test
    public void testUploadProductsProductsEmpty() {
        String appId = "testAppId";
        List<ProductPoolUpdateObjectTemplateDTO> products = new ArrayList<>();
        Map<Integer, List<DownloadUploadingResultDTO>> result = productManagementService.uploadProducts(products, appId);
        assertTrue("应当返回空结果", result.get(ScrmAmProcessOrchestrationUploadProductsEnum.FAILED.getCode()).isEmpty());
    }

    /**
     * 测试 uploadProducts 方法，当 products 不为空但全部处理失败时
     */
    @Test
    public void testUploadProductsAllFailed() {
        String appId = "testAppId";
        List<ProductPoolUpdateObjectTemplateDTO> products = new ArrayList<>();
        products.add(new ProductPoolUpdateObjectTemplateDTO(1L, new ArrayList<>(), "type"));
        when(productItemsDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(productIdService.isProductIdExists(any(), any())).thenReturn(true);
        Map<Integer, List<DownloadUploadingResultDTO>> result = productManagementService.uploadProducts(products, appId);
        assertFalse("应当返回失败结果", result.get(ScrmAmProcessOrchestrationUploadProductsEnum.FAILED.getCode()).isEmpty());
    }

    @Test
    public void testQueryShelfProductInfoByRequest_NullRequest() throws Throwable {
        QueryActivityPageShelfProductRequest request = null;
        ShelfProductInfoResultVO result = productManagementService.queryShelfProductInfoByRequest(request);
        assertNull("Result should be null when request is null", result);
    }

    @Test
    public void testQueryShelfProductInfoByRequest_EmptyAppId() throws Throwable {
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setAppId("");
        ShelfProductInfoResultVO result = productManagementService.queryShelfProductInfoByRequest(request);
        assertNull("Result should be null when appId is empty", result);
    }

    @Test
    public void testQueryShelfProductInfoByRequest_NullSceneCodeDO() throws Throwable {
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setAppId("testAppId");
        request.setScenecode("testSceneCode");
        when(activSceneCodeDOMapper.selectByExample(any())).thenReturn(null);
        ShelfProductInfoResultVO result = productManagementService.queryShelfProductInfoByRequest(request);
        assertNull("Result should be null when sceneCodeDO is null", result);
    }

    @Test
    public void testQueryShelfProductInfoByRequest_ValidRequestEmptyProductIds() throws Throwable {
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setAppId("testAppId");
        request.setScenecode("testSceneCode");
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneCode("testSceneCode");
        when(activSceneCodeDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(sceneCodeDO));
        when(activityPageManagementService.getProductIdsByActivityPageId(anyString(), anyLong())).thenReturn(null);
        ShelfProductInfoResultVO result = productManagementService.queryShelfProductInfoByRequest(request);
        assertNull("Result should be null when productIds are empty", result);
    }

    @Test
    public void testSpecialCheckLogic_WithXiuyuZxkAndGeneralProduct_ShouldReturnFalse() {
        // arrange
        String appId = "xiuyu-zxk";
        ProductPreHandleDTO item = new ProductPreHandleDTO(1L, Arrays.asList("tag1", "tag2"), ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode());
        boolean insert = true;
        List<DownloadUploadingResultDTO> failedResults = new ArrayList<>();
        // act
        boolean result = productManagementService.specialCheckLogic(appId, item, insert, failedResults);
        // assert
        assertFalse(result);
        assertFalse(failedResults.isEmpty());
    }

    /**
     * 测试特殊校验逻辑，appId不为"xiuyu-zxk"，insert为true，产品类型为泛商品，预期返回true
     */
    @Test
    public void testSpecialCheckLogic_WithNonXiuyuZxkAndGeneralProduct_ShouldReturnTrue() {
        // arrange
        String appId = "other-app";
        ProductPreHandleDTO item = new ProductPreHandleDTO(1L, Arrays.asList("tag1", "tag2"), ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode());
        boolean insert = true;
        List<DownloadUploadingResultDTO> failedResults = new ArrayList<>();
        // act
        boolean result = productManagementService.specialCheckLogic(appId, item, insert, failedResults);
        // assert
        assertTrue(result);
        assertTrue(failedResults.isEmpty());
    }

    /**
     * 测试特殊校验逻辑，appId为"xiuyu-zxk"，insert为false，产品类型为泛商品，预期返回false
     */
    @Test
    public void testSpecialCheckLogic_WithXiuyuZxkAndGeneralProductAndInsertFalse_ShouldReturnFalse() {
        // arrange
        String appId = "xiuyu-zxk";
        ProductPreHandleDTO item = new ProductPreHandleDTO(1L, Arrays.asList("tag1", "tag2"), ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode());
        boolean insert = false;
        List<DownloadUploadingResultDTO> failedResults = new ArrayList<>();
        // act
        boolean result = productManagementService.specialCheckLogic(appId, item, insert, failedResults);
        // assert
        assertFalse(result);
    }

    /**
     * 测试特殊校验逻辑，appId为"xiuyu-zxk"，insert为true，产品类型不为泛商品，预期返回true
     */
    @Test
    public void testSpecialCheckLogic_WithXiuyuZxkAndNonGeneralProduct_ShouldReturnTrue() {
        // arrange
        String appId = "xiuyu-zxk";
        ProductPreHandleDTO item = new ProductPreHandleDTO(1L, Arrays.asList("tag1", "tag2"), ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode());
        boolean insert = true;
        List<DownloadUploadingResultDTO> failedResults = new ArrayList<>();
        // act
        boolean result = productManagementService.specialCheckLogic(appId, item, insert, failedResults);
        // assert
        assertTrue(result);
        assertTrue(failedResults.isEmpty());
    }

    /**
     * 测试saveCommonProductPoolProducts方法，当products为空时
     */
    @Test
    public void testSaveCommonProductPoolProductsWhenProductsIsEmpty() {
        String appId = "testAppId";
        List<ProductPreHandleDTO> products = Collections.emptyList();
        Map<Integer, Map<Long, QueryProductInfoDTO>> productsInfo = new HashMap<>();
        productManagementService.saveCommonProductPoolProducts(appId, products, productsInfo);
        verify(commonSelectPoolItemService, never()).selectPoolProductAction(any(CommonProductActionContext.class));
    }

    /**
     * 测试saveCommonProductPoolProducts方法，正常情况
     */
    @Test
    public void testSaveCommonProductPoolProductsNormal() {
        String appId = "testAppId";
        List<ProductPreHandleDTO> products = new ArrayList<>();
        products.add(new ProductPreHandleDTO(1L, Collections.singletonList("tag1"), 1));
        Map<Integer, Map<Long, QueryProductInfoDTO>> productsInfo = MapUtil.of(Pair.of(1, MapUtil.of(Pair.of(1L, QueryProductInfoDTO.builder().productInfo(ProductInfo.builder().productId(1L).dpProductId(1L).build()).type(1).build()))));
        ScrmAmProcessOrchestrationProductTagsDO tagDO = new ScrmAmProcessOrchestrationProductTagsDO(1L, "tag1", 1, appId, new Date());
        when(productTagsDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(tagDO));
        productManagementService.saveCommonProductPoolProducts(appId, products, productsInfo);
        verify(commonSelectPoolItemService, times(1)).selectPoolProductAction(any(CommonProductActionContext.class));
    }

    @Test
    public void testUpdateCommonProductPoolProductsNormal() {
        String appId = "testAppId";
        List<ProductPreHandleDTO> products = new ArrayList<>();
        products.add(new ProductPreHandleDTO(1L, Collections.singletonList("tag1"), 1));
        Map<Integer, Map<Long, QueryProductInfoDTO>> productsInfo = MapUtil.of(Pair.of(1, MapUtil.of(Pair.of(1L, QueryProductInfoDTO.builder().productInfo(ProductInfo.builder().productId(1L).dpProductId(1L).build()).type(1).build()))));
        ScrmAmProcessOrchestrationProductTagsDO tagDO = new ScrmAmProcessOrchestrationProductTagsDO(1L, "tag1", 1, appId, new Date());
        when(productTagsDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(tagDO));
        productManagementService.updateCommonProductPoolProducts(appId, products, productsInfo);
        verify(commonSelectPoolItemService, times(1)).selectPoolProductAction(any(CommonProductActionContext.class));
    }

    @Test
    public void testDeleteCommonProductPoolsItemsNormal() {
        String appId = "testAppId";
        List<ProductPreHandleDTO> products = new ArrayList<>();
        products.add(new ProductPreHandleDTO(1L, Collections.singletonList("tag1"), 1));
        productManagementService.deleteCommonProductPoolsItems(appId, products);
        verify(commonSelectPoolItemService, times(1)).selectPoolProductAction(any(CommonProductActionContext.class));
    }

    @Test
    public void testDeleteCommonProductPoolsItemNormal() {
        String appId = "testAppId";
        productManagementService.deleteCommonProductPoolsItem(appId, 1L, 1L, 1, Collections.singletonList("1"));
        verify(commonSelectPoolItemService, times(1)).selectPoolProductAction(any(CommonProductActionContext.class));
    }

    @Test
    public void testCreateCommonProductPoolsWithSuccess() {
        // arrange
        List<ScrmAmProcessOrchestrationProductTagsDO> tagsDOList = Arrays.asList(ScrmAmProcessOrchestrationProductTagsDO.builder().tagName("1").tagType(1).id(1L).appId("a").build());
        BaseResponse<Boolean> successResponse = new BaseResponse<>(200, "", true);
        when(commonSelectPoolService.selectPoolAction(any(CommonActionContext.class))).thenReturn(successResponse);
        // act
        productManagementService.createCommonProductPools(tagsDOList);
        // assert
        verify(commonSelectPoolService, times(1)).selectPoolAction(any(CommonActionContext.class));
    }

    /**
     * 测试getProductByProductIdAndProductType方法，当数据库返回非空列表时应返回第一个元素
     */
    @Test
    public void testGetProductByProductIdAndProductTypeWithNonEmptyResult() {
        ScrmAmProcessOrchestrationProductItemsDO expected = new ScrmAmProcessOrchestrationProductItemsDO();
        when(productItemsDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductItemsDOExample.class))).thenReturn(Arrays.asList(expected));
        ScrmAmProcessOrchestrationProductItemsDO result = productManagementService.getProductByProductIdAndProductType("appId", 1L, 1);
        assertNotNull(result);
        assertEquals(expected, result);
    }

    /**
     * 测试当type为BULK_ORDER，且productId存在于DP_DEAL_GROUP_ID时，返回BULK_ORDER
     */
    @Test
    public void testConvertToProductType_TypeBulkOrder_ProductIdExists() {
        // arrange
        Long productId = 400L;
        String type = "团购";
        String appId = "testApp";
        when(productIdService.isProductIdExists(productId, BizProductIdType.DP_DEAL_GROUP_ID)).thenReturn(true);
        // act
        Integer result = productManagementService.convertToProductType(productId, type, appId);
        // assert
        assertEquals(Integer.valueOf(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode()), result);
    }

    @Test
    public void testConvertToProductType_TypeGeneral_ProductIdExists() {
        // arrange
        Long productId = 400L;
        String type = "预定";
        String appId = "testApp";
        when(productIdService.isProductIdExists(productId, BizProductIdType.DP_FUN_PRODUCT_ID)).thenReturn(true);
        // act
        Integer result = productManagementService.convertToProductType(productId, type, appId);
        // assert
        assertEquals(Integer.valueOf(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode()), result);
    }

    @Test
    public void testConvertToProductType_TypeUnknownActualBulk_ProductIdExists() {
        // arrange
        Long productId = 400L;
        String type = "未知";
        String appId = "testApp";
        when(productIdService.isProductIdExists(productId, BizProductIdType.DP_DEAL_GROUP_ID)).thenReturn(true);
        when(productIdService.isProductIdExists(productId, BizProductIdType.DP_FUN_PRODUCT_ID)).thenReturn(false);
        // act
        Integer result = productManagementService.convertToProductType(productId, type, appId);
        // assert
        assertEquals(Integer.valueOf(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode()), result);
    }

    @Test
    public void testConvertToProductType_TypeUnknownActualGeneral_ProductIdExists() {
        // arrange
        Long productId = 400L;
        String type = "未知";
        String appId = "testApp";
        when(productIdService.isProductIdExists(productId, BizProductIdType.DP_DEAL_GROUP_ID)).thenReturn(false);
        when(productIdService.isProductIdExists(productId, BizProductIdType.DP_FUN_PRODUCT_ID)).thenReturn(true);
        // act
        Integer result = productManagementService.convertToProductType(productId, type, appId);
        // assert
        assertEquals(Integer.valueOf(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode()), result);
    }

    @Test
    public void testConvertToProductType_TypeUnknownActualUnknown_ProductIdExists() {
        // arrange
        Long productId = 700_000_000L;
        String type = "未知";
        String appId = "testApp";
        when(productIdService.isProductIdExists(productId, BizProductIdType.DP_DEAL_GROUP_ID)).thenReturn(false);
        when(productIdService.isProductIdExists(productId, BizProductIdType.DP_FUN_PRODUCT_ID)).thenReturn(false);
        when(productIdService.isProductIdExists(productId, BizProductIdType.DEAL_ID)).thenReturn(false);
        // act
        Integer result = productManagementService.convertToProductType(productId, type, appId);
        // assert
        assertEquals(Integer.valueOf(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.UNKNOWN.getCode()), result);
    }

    /**
     * 测试isKtvProduct方法，当productId对应的商品是KTV类目时
     */
    @Test
    public void testIsKtvProduct_WhenProductIsKTV() throws Exception {
        // arrange
        Long productId = 123L;
        Map<Integer, List<Integer>> itemIdsByProductIds = new HashMap<>();
        itemIdsByProductIds.put(productId.intValue(), Collections.singletonList(SPUTypeEnum.KTVV2.getCode()));
        when(skuProductService.getItemIdsByProductIds(anyList())).thenReturn(itemIdsByProductIds);
        // act
        boolean result = productManagementService.isKtvProduct(productId);
        // assert
        assertTrue(result);
    }

    /**
     * 测试isKtvProduct方法，当查询团购商品类目时抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testIsKtvProduct_WhenQueryDealGroupThrowsException() throws Exception {
        // arrange
        Long productId = 123L;
        when(skuProductService.getItemIdsByProductIds(anyList())).thenReturn(new HashMap<>());
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        // 非成功状态码
        response.setCode(1);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act
        productManagementService.isKtvProduct(productId);
    }

    /**
     * 测试isKtvProduct方法，当productId对应的团购商品是KTV类目时
     */
    @Test
    public void testIsKtvProduct_WhenDealGroupProductIsKTV() throws Exception {
        // arrange
        Long productId = 123L;
        when(skuProductService.getItemIdsByProductIds(anyList())).thenReturn(new HashMap<>());
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        // 成功状态码
        response.setCode(0);
        QueryDealGroupListResult data = new QueryDealGroupListResult();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId((long) SPUTypeEnum.KTVV2.getCode());
        dealGroupDTO.setCategory(categoryDTO);
        data.setList(Collections.singletonList(dealGroupDTO));
        response.setData(data);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act
        boolean result = productManagementService.isKtvProduct(productId);
        // assert
        assertTrue(result);
    }

    /**
     * 测试isKtvProduct方法，当productId对应的团购商品不是KTV类目时
     */
    @Test
    public void testIsKtvProduct_WhenDealGroupProductIsNotKTV() throws Exception {
        // arrange
        Long productId = 123L;
        when(skuProductService.getItemIdsByProductIds(anyList())).thenReturn(new HashMap<>());
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        // 成功状态码
        response.setCode(0);
        QueryDealGroupListResult data = new QueryDealGroupListResult();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupCategoryDTO categoryDTO = new DealGroupCategoryDTO();
        categoryDTO.setCategoryId((long) SPUTypeEnum.SaaSProduct.getCode());
        dealGroupDTO.setCategory(categoryDTO);
        data.setList(Collections.singletonList(dealGroupDTO));
        response.setData(data);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        // act
        boolean result = productManagementService.isKtvProduct(productId);
        // assert
        assertFalse(result);
    }

    /**
     * 测试getProductByProductIdAndProductType方法，当输入参数为空时应返回null
     */
    @Test
    public void testGetProductByProductIdAndProductTypeWithNullInput() {
        ScrmAmProcessOrchestrationProductItemsDO result = productManagementService.getProductByProductIdAndProductType(null, null, null);
        assertNull(result);
    }

    /**
     * 测试getProductByProductIdAndProductType方法，当数据库返回空列表时应返回null
     */
    @Test
    public void testGetProductByProductIdAndProductTypeWithEmptyResult() {
        when(productItemsDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductItemsDOExample.class))).thenReturn(Collections.emptyList());
        ScrmAmProcessOrchestrationProductItemsDO result = productManagementService.getProductByProductIdAndProductType("appId", 1L, 1);
        assertNull(result);
    }

    /**
     * 测试getProductByProductIdAndProductType方法，当数据库返回非空列表且包含有效数据时应返回第一个元素
     */
    @Test
    public void testGetProductByProductIdAndProductTypeWithValidData() {
        // Arrange
        ScrmAmProcessOrchestrationProductItemsDO expected = new ScrmAmProcessOrchestrationProductItemsDO(1L, 1L, 2L, 1, "appId", new Date());
        when(productItemsDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductItemsDOExample.class))).thenReturn(Arrays.asList(expected));
        // Act
        ScrmAmProcessOrchestrationProductItemsDO result = productManagementService.getProductByProductIdAndProductType("appId", 1L, 1);
        // Assert
        assertNotNull(result);
        assertEquals(expected, result);
    }

    /**
     * 测试convertToQueryProductInfoDTO方法，正常情况
     */
    @Test
    public void testConvertToQueryProductInfoDTONormal() {
        GeneralProductDTO product = new GeneralProductDTO();
        product.setProductId(1);
        product.setName("Test Product");
        product.setMarketPrice("100");
        product.setOriginalSalePrice(new BigDecimal("80"));
        Map<Integer, List<Integer>> validCityIdsMap = new HashMap<>();
        validCityIdsMap.put(1, Arrays.asList(101, 102));
        when(skuProductService.mGetValidCityIds(anyList())).thenReturn(validCityIdsMap);
        Map<Integer, Integer> mtCityIdsMap = new HashMap<>();
        mtCityIdsMap.put(101, 201);
        mtCityIdsMap.put(102, 202);
        when(cityTransformService.getBatchMtCityByDpCity(anyList())).thenReturn(mtCityIdsMap);
        QueryProductInfoDTO result = productManagementService.convertToQueryProductInfoDTO(product);
        assertNotNull(result);
        assertEquals(Long.valueOf(1), result.getProductInfo().getProductId());
        assertEquals("Test Product", result.getProductInfo().getTitle());
        assertEquals("100", result.getMarketPrice());
        assertEquals("80", result.getOriginalSalePrice());
        assertTrue(result.getMtCityIds().containsAll(Arrays.asList(201, 202)));
        assertTrue(result.getDpCityIds().containsAll(Arrays.asList(101, 102)));
    }

    /**
     * 测试convertToQueryProductInfoDTO方法，当skuProductService.mGetValidCityIds抛出异常时
     */
    @Test
    public void testConvertToQueryProductInfoDTOServiceException() {
        GeneralProductDTO product = new GeneralProductDTO();
        product.setProductId(1);
        product.setName("Test Product");
        product.setMarketPrice("100");
        product.setOriginalSalePrice(new BigDecimal("80"));
        when(skuProductService.mGetValidCityIds(anyList())).thenThrow(new RuntimeException("Service Exception"));
        QueryProductInfoDTO result = productManagementService.convertToQueryProductInfoDTO(product);
        assertNotNull(result);
        assertEquals(Long.valueOf(1), result.getProductInfo().getProductId());
        assertTrue(result.getDpCityIds().isEmpty());
        assertTrue(result.getMtCityIds().isEmpty());
    }

    /**
     * 测试DealGroupDTO中的regions为空时的情况
     */
    @Test
    public void testConvertToQueryProductInfoDTOWhenRegionsIsNull() {
        DealGroupDTO product = mock(DealGroupDTO.class);
        when(product.getRegions()).thenReturn(null);
        QueryProductInfoDTO result = productManagementService.convertToQueryProductInfoDTO(product);
        assertNotNull(result);
        assertTrue(result.getMtCityIds().isEmpty());
        assertTrue(result.getDpCityIds().isEmpty());
    }

    /**
     * 测试DealGroupDTO中的displayShopInfo为空时的情况
     */
    @Test
    public void testConvertToQueryProductInfoDTOWhenDisplayShopInfoIsNull() {
        DealGroupDTO product = mock(DealGroupDTO.class);
        when(product.getDisplayShopInfo()).thenReturn(null);
        QueryProductInfoDTO result = productManagementService.convertToQueryProductInfoDTO(product);
        assertNotNull(result);
        assertEquals("", result.getMtStores());
        assertEquals("", result.getDpStores());
    }

    /**
     * 测试DealGroupDTO中的PriceDTO为空时的情况
     */
    @Test
    public void testConvertToQueryProductInfoDTOWhenPriceIsNull() {
        DealGroupDTO product = mock(DealGroupDTO.class);
        when(product.getPrice()).thenReturn(null);
        QueryProductInfoDTO result = productManagementService.convertToQueryProductInfoDTO(product);
        assertNotNull(result);
        assertEquals("", result.getMarketPrice());
        assertEquals("", result.getOriginalSalePrice());
    }

    /**
     * 测试DealGroupDTO中的regions和displayShopInfo非空时的情况
     */
    @Test
    public void testConvertToQueryProductInfoDTOWithValidProduct() {
        DealGroupDTO product = mock(DealGroupDTO.class);
        DealGroupImageDTO image = mock(DealGroupImageDTO.class);
        when(product.getImage()).thenReturn(image);
        when(image.getDefaultPicPath()).thenReturn("imagePath");
        DealGroupRegionDTO dealGroupRegionDTO = new DealGroupRegionDTO();
        dealGroupRegionDTO.setDpCityId(2);
        dealGroupRegionDTO.setMtCityId(1);
        DealGroupRegionDTO dealGroupRegionDTO1 = new DealGroupRegionDTO();
        dealGroupRegionDTO1.setDpCityId(4);
        dealGroupRegionDTO1.setMtCityId(3);
        when(product.getRegions()).thenReturn(Arrays.asList(dealGroupRegionDTO, dealGroupRegionDTO1));
        when(product.getDisplayShopInfo()).thenReturn(null);
        PriceDTO priceDTO = new PriceDTO();
        priceDTO.setMarketPrice("100");
        priceDTO.setSalePrice("200");
        when(product.getPrice()).thenReturn(priceDTO);
        QueryProductInfoDTO result = productManagementService.convertToQueryProductInfoDTO(product);
        assertNotNull(result);
        assertEquals(Arrays.asList(1, 3), result.getMtCityIds());
        assertEquals(Arrays.asList(2, 4), result.getDpCityIds());
        assertEquals("100", result.getMarketPrice());
        assertEquals("200", result.getOriginalSalePrice());
    }

    /**
     * 测试查询选品池商品列表RPC
     */
    @Test
    public void testQuerySelectPoolProducts_WhenTagIdsAndCityIdsAreEmpty() throws Exception {
        // arrange
        when(productTagsDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductTagsDOExample.class))).thenReturn(Collections.emptyList());
        PageBaseResponse<List<DzAdvisorProductInfo>> expectedResponse = new PageBaseResponse<>();
        when(commonSelectPoolItemService.querySelectPoolProducts(any(DzAdvisorProductQueryRequest.class))).thenReturn(expectedResponse);
        // act
        PageProductRequest request = new PageProductRequest();
        request.setTags("");
        request.setProductDesc("123123");
        request.setCityIds("");
        request.setAppId("test");
        request.setPageSize(10);
        request.setPageNum(1);
        request.setMinPrice("");
        request.setMaxPrice("");
        PageBaseResponse<List<DzAdvisorProductInfo>> actualResponse = productManagementService.querySelectPoolProducts(request);
        // assert
        assertNotEquals(expectedResponse, actualResponse);
    }

    /**
     * 测试查询选品池商品列表RPC，当远程调用失败时
     */
    @Test
    public void testQuerySelectPoolProducts_WhenRemoteCallFails() throws Exception {
        // arrange
        when(productTagsDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductTagsDOExample.class))).thenReturn(Collections.emptyList());
        when(commonSelectPoolItemService.querySelectPoolProducts(any(DzAdvisorProductQueryRequest.class))).thenThrow(new RuntimeException("Remote call failed"));
        // act
        PageProductRequest request = new PageProductRequest();
        request.setPageSize(10);
        request.setPageNum(1);
        PageBaseResponse<List<DzAdvisorProductInfo>> actualResponse = productManagementService.querySelectPoolProducts(request);
        // assert
        assertNotNull(actualResponse);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testBatchDeleteProductByPrimaryKeyNormal() {
        // arrange
        String appId = "testAppId";
        List<Long> productPrimaryKeys = Arrays.asList(1L, 2L, 3L);
        // act
        productManagementService.batchDeleteProductByPrimaryKey(appId, productPrimaryKeys);
        // assert
        verify(productItemsDOMapper, times(1)).deleteByExample(any(ScrmAmProcessOrchestrationProductItemsDOExample.class));
    }

    /**
     * 测试非空的productPrimaryKeys列表
     */
    @Test
    public void testBatchDeleteProductTagsRelation_NonEmptyProductPrimaryKeys() {
        // arrange
        String appId = "testAppId";
        List<Long> productPrimaryKeys = Arrays.asList(1L, 2L);
        // act
        productManagementService.batchDeleteProductTagsRelation(appId, productPrimaryKeys);
        // assert
        verify(extScrmAmProcessOrchestrationProductTagMapDOMapper, times(1)).deleteByExample(any(ScrmAmProcessOrchestrationProductTagMapDOExample.class));
    }

    /**
     * ·
     *  测试正常情况下分页查询商品
     */
    @Test
    public void testPageProductsNormal() throws Throwable {
        // arrange
        PageProductRequest request = new PageProductRequest();
        request.setAppId("appId");
        request.setPageSize(10);
        request.setPageNum(1);
        request.setProductDesc("testProduct");
        request.setCityIds("1,2");
        request.setTags("tag1,tag2");
        request.setType(1);
        request.setMaxPrice("100");
        request.setMinPrice("50");
        DzAdvisorProductInfo productInfo = new DzAdvisorProductInfo();
        productInfo.setMtProductId(123L);
        productInfo.setProductName("Test Product");
        productInfo.setProductType(1);
        productInfo.setDpCityIds(Arrays.asList(1, 2));
        productInfo.setMtCityIds(Arrays.asList(1, 2));
        productInfo.setDpShopIds(Arrays.asList(1L, 2L));
        productInfo.setMtShopIds(Arrays.asList(1L, 2L));
        List<DzAdvisorProductInfo> productInfoList = new ArrayList<>();
        productInfoList.add(productInfo);
        PageBaseResponse<List<DzAdvisorProductInfo>> pageBaseResponse = new PageBaseResponse<>();
        pageBaseResponse.setData(productInfoList);
        pageBaseResponse.setTotalCount(1);
        List<CityInfo> CityInfos = new ArrayList<>();
        CityInfo cityInfo = new CityInfo();
        cityInfo.setName("testCity");
        CityInfos.add(cityInfo);
        when(cityService.batchGetCitysByCityIds(any())).thenReturn(CityInfos);
        when(commonSelectPoolItemService.querySelectPoolProducts(any())).thenReturn(pageBaseResponse);
        List<ScrmAmProcessOrchestrationProductItemsDO> items = new ArrayList<>();
        items.add(new ScrmAmProcessOrchestrationProductItemsDO(1L, 123L, 123L, 1, "appId", new Date()));
        when(productItemsDOMapper.selectByExample(any())).thenReturn(items);
        Map<Long, QueryProductHeadPicAndMarketPriceDTO> info = new HashMap<>();
        info.put(123L, new QueryProductHeadPicAndMarketPriceDTO(1, "100", "pic"));
        when(productInfoService.batchGetHeadPictureAndMarketPrice(anyList(), any())).thenReturn(info);
        PageRemoteResponse<PageProductResult> expectedResponse = PageRemoteResponse.success(new ArrayList<>(), 1, true);
        // act
        PageRemoteResponse<PageProductResult> response = productManagementService.pageProducts(request);
        // assert
        assertEquals(expectedResponse.getTotalHit(), response.getTotalHit());
    }

    @Test(expected = Exception.class)
    public void testUploadProductsException() {
        String appId = "test";
        List<String> tags = Lists.newArrayList("tag1", "tag2");
        List<ProductPoolUpdateObjectTemplateDTO> products = new ArrayList<>();
        products.add(new ProductPoolUpdateObjectTemplateDTO(1L, tags, "团购"));
        when(productIdService.isProductIdExists(1L, BizProductIdType.DEAL_ID)).thenReturn(true);
        when(productInfoService.queryProductsInfo(any())).thenReturn(MapUtil.of(1, MapUtil.of(1L, new QueryProductInfoDTO(ProductInfo.builder().build(), "100", "200", Lists.newArrayList(1, 2), Lists.newArrayList(1, 2), 1, "123,1234", "123,1234", null))));
        when(productTagsDOMapper.selectByExample(any())).thenReturn(Lists.newArrayList(new ScrmAmProcessOrchestrationProductTagsDO(1L, "tag1", 1, appId, new Date())));
        when(productTagsDOMapper.batchInsert(anyList())).thenReturn(1);
        // products中，db存在的商品
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Lists.newArrayList(new ScrmAmProcessOrchestrationProductItemsDO(1L, 1L, 1L, 1, appId, null)));
        when(extScrmAmProcessOrchestrationProductTagMapDOMapper.batchInsert(anyList())).thenReturn(1);
        productManagementService.uploadProducts(products, appId);
    }

    /**
     * 测试场景：请求参数为null
     */
    @Test
    public void testQueryShelfProductInfoByRequest_RequestIsNull() {
        // arrange
        QueryActivityPageShelfProductRequest request = null;
        // act
        ShelfProductInfoResultVO result = productManagementService.queryShelfProductInfoByRequest(request);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：请求参数的appId为空
     */
    @Test
    public void testQueryShelfProductInfoByRequest_AppIdIsNull() {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setAppId(null);
        // act
        ShelfProductInfoResultVO result = productManagementService.queryShelfProductInfoByRequest(request);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：根据sceneCode查询不到ScrmAmProcessOrchestrationActivSceneCodeDO
     */
    @Test
    public void testQueryShelfProductInfoByRequest_SceneCodeDODoesNotExist() {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setAppId("testAppId");
        request.setScenecode("testSceneCode");
        when(activSceneCodeDOMapper.selectByExample(any())).thenReturn(null);
        // act
        ShelfProductInfoResultVO result = productManagementService.queryShelfProductInfoByRequest(request);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：根据sceneCode查询到ScrmAmProcessOrchestrationActivSceneCodeDO，但sceneRequest为空
     */
    @Test
    public void testQueryShelfProductInfoByRequest_SceneRequestIsNull() {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setAppId("testAppId");
        request.setScenecode("testSceneCode");
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneRequest(null);
        when(activSceneCodeDOMapper.selectByExample(any())).thenReturn(java.util.Collections.singletonList(sceneCodeDO));
        when(activityPageManagementService.getProductIdsByActivityPageId(anyString(), anyLong())).thenReturn(null);
        // act
        ShelfProductInfoResultVO result = productManagementService.queryShelfProductInfoByRequest(request);
        // assert
        assertNull(result);
        // 更多的断言可以根据实际情况添加，这里只是示例
    }

    @Test
    public void testQueryShelfProductInfoByRequest_GetProductIdsByRequestReturnsEmptyList() {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setAppId("testAppId");
        request.setScenecode("testSceneCode");
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneCode("testSceneCode");
        when(activSceneCodeDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(sceneCodeDO));
        try {
            Method getProductIdsByRequestMethod = ProductManagementService.class.getDeclaredMethod("getProductIdsByRequest", QueryActivityPageShelfProductRequest.class, ScrmAmProcessOrchestrationActivSceneCodeDO.class);
            getProductIdsByRequestMethod.setAccessible(true);
            // 使用反射调用getProductIdsByRequest方法并返回空列表
            getProductIdsByRequestMethod.invoke(productManagementService, request, sceneCodeDO);
        } catch (Exception e) {
            e.printStackTrace();
            fail("反射调用getProductIdsByRequest方法失败");
        }
        // act
        ShelfProductInfoResultVO result = productManagementService.queryShelfProductInfoByRequest(request);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：getProductIdsByRequest 返回非空列表，但queryProductItemInfosByProductIdAndAppid返回空列表
     */
    @Test
    public void testQueryShelfProductInfoByRequest_QueryProductItemInfosByProductIdAndAppidReturnsEmptyList() {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setAppId("testAppId");
        request.setScenecode("testSceneCode");
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneCode("testSceneCode");
        when(activSceneCodeDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(sceneCodeDO));
        try {
            // 反射调用 getProductIdsByRequest 方法
            Method getProductIdsByRequestMethod = ProductManagementService.class.getDeclaredMethod("getProductIdsByRequest", QueryActivityPageShelfProductRequest.class, ScrmAmProcessOrchestrationActivSceneCodeDO.class);
            getProductIdsByRequestMethod.setAccessible(true);
            List<Long> productIds = (List<Long>) getProductIdsByRequestMethod.invoke(productManagementService, request, sceneCodeDO);
            // 反射调用 queryProductItemInfosByProductIdAndAppid 方法
            Method queryProductItemInfosByProductIdAndAppidMethod = ProductManagementService.class.getDeclaredMethod("queryProductItemInfosByProductIdAndAppid", List.class, String.class);
            queryProductItemInfosByProductIdAndAppidMethod.setAccessible(true);
            List<?> productItemsDOS = (List<?>) queryProductItemInfosByProductIdAndAppidMethod.invoke(productManagementService, productIds, "testAppId");
            // 模拟方法返回空列表
            if (productItemsDOS != null) {
                productItemsDOS.clear();
            }
        } catch (Exception e) {
            e.printStackTrace();
            fail("反射调用方法失败");
        }
        // act
        ShelfProductInfoResultVO result = productManagementService.queryShelfProductInfoByRequest(request);
        // assert
        assertNull(result);
    }

    /**
     * 测试场景：当活动页ID为null时
     */
    @Test
    public void testQueryPreviewShelfProductInfo_ActivityPageIdIsNull() {
        ShelfProductInfoResultVO result = productManagementService.queryPreviewShelfProductInfo(null, "appId");
        assertNotNull(result);
    }

    /**
     * 测试场景：当appId为空时
     */
    @Test
    public void testQueryPreviewShelfProductInfo_AppIdIsNull() {
        ShelfProductInfoResultVO result = productManagementService.queryPreviewShelfProductInfo(1L, null);
        assertNotNull(result);
    }

    /**
     * 测试场景：当通过活动页ID和appId无法查询到商品信息时
     */
    @Test
    public void testQueryPreviewShelfProductInfo_NoProductFound() {
        when(activityPageManagementService.getProductIdsByActivityPageId(anyString(), anyLong())).thenReturn(null);
        ShelfProductInfoResultVO result = productManagementService.queryPreviewShelfProductInfo(1L, "appId");
        assertNotNull(result);
    }

    @Test(expected = Exception.class)
    public void testGetProductIdsByRequest_Success() throws Exception {
        // arrange
        QueryActivityPageShelfProductRequest request = mock(QueryActivityPageShelfProductRequest.class);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = mock(ScrmAmProcessOrchestrationActivSceneCodeDO.class);
        sceneCodeDO.setSceneRequest("someSceneRequest");
        PageBaseResponse<List<DzAdvisorProductInfo>> pageBaseResponse = new PageBaseResponse<>();
        pageBaseResponse.setCode(CommonErrorCode.OK.getCode());
        List<DzAdvisorProductInfo> productInfos = Arrays.asList(new DzAdvisorProductInfo(), new DzAdvisorProductInfo());
        pageBaseResponse.setData(productInfos);
        when(commonRecommendService.recommend(any(RecommendRequest.class))).thenReturn(pageBaseResponse);
        Method method = ActivityPageManagementService.class.getDeclaredMethod("getProductIdsByRequest", QueryActivityPageShelfProductRequest.class, ScrmAmProcessOrchestrationActivSceneCodeDO.class);
        method.setAccessible(true);
        // act
        List<Long> result = (List<Long>) method.invoke(activityPageManagementService, request, sceneCodeDO);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsAll(Arrays.asList(1L, 2L)));
    }

    /**
     * 测试请求或场景代码对象为空时返回null
     */
    @Test(expected = Exception.class)
    public void testGetProductIdsByRequest_NullRequestOrSceneCode() throws Exception {
        // arrange
        QueryActivityPageShelfProductRequest request = null;
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = null;
        Method method = ActivityPageManagementService.class.getDeclaredMethod("getProductIdsByRequest", QueryActivityPageShelfProductRequest.class, ScrmAmProcessOrchestrationActivSceneCodeDO.class);
        method.setAccessible(true);
        // act
        List<Long> resultWithNullRequest = (List<Long>) method.invoke(activityPageManagementService, request, new ScrmAmProcessOrchestrationActivSceneCodeDO());
        List<Long> resultWithNullSceneCode = (List<Long>) method.invoke(activityPageManagementService, new QueryActivityPageShelfProductRequest(), sceneCodeDO);
        // assert
        assertNull(resultWithNullRequest);
        assertNull(resultWithNullSceneCode);
    }

    @Test(expected = Exception.class)
    public void testGetRecommendRequest_Success() throws Exception {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setPageNo(1);
        request.setPageSize(10);
        request.setCityId(10);
        request.setDeviceId("device123");
        request.setUserId(123L);
        request.setLat(30.0);
        request.setLng(120.0);
        String sceneRequest = "{\"pageNum\":1,\"pageSize\":10,\"platform\":\"MT\",\"cityId\":\"100010\",\"uuid\":\"device123\",\"userId\":\"123\",\"lat\":30.0,\"lng\":120.0}";
        Method method = ActivityPageManagementService.class.getDeclaredMethod("getRecommendRequest", QueryActivityPageShelfProductRequest.class, String.class);
        method.setAccessible(true);
        // act
        RecommendRequest result = (RecommendRequest) method.invoke(activityPageManagementService, request, sceneRequest);
        // assert
        assertNotNull(result);
        assertEquals(Integer.valueOf(1), result.getPageNum());
        assertEquals(Integer.valueOf(10), result.getPageSize());
    }

    /**
     * 测试请求或场景请求为空时返回null
     */
    @Test(expected = Exception.class)
    public void testGetRecommendRequest_NullRequestOrSceneRequest() throws Exception {
        // arrange
        QueryActivityPageShelfProductRequest request = null;
        String sceneRequest = null;
        Method method = ActivityPageManagementService.class.getDeclaredMethod("getRecommendRequest", QueryActivityPageShelfProductRequest.class, String.class);
        method.setAccessible(true);
        // act
        RecommendRequest resultWithNullRequest = (RecommendRequest) method.invoke(activityPageManagementService, request, "{\"pageNum\":1}");
        RecommendRequest resultWithNullSceneRequest = (RecommendRequest) method.invoke(activityPageManagementService, new QueryActivityPageShelfProductRequest(), sceneRequest);
        // assert
        assertNull(resultWithNullRequest);
        assertNull(resultWithNullSceneRequest);
    }

    @Test(expected = Exception.class)
    public void testBuildDetailUrl_Success() throws Exception {
        // arrange
        String originPath = "http://example.com";
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setProcessOrchestrationId(12345L);
        sceneCodeDO.setProcessOrchestrationVersion("version");
        sceneCodeDO.setProcessOrchestrationNodeId(12345L);
        String appId = "appId";
        String communityDistributorCode = "distributorCode";
        when(informationGatheringService.queryCommunityDistributor(any())).thenReturn(communityDistributorCode);
        Method method = ActivityPageManagementService.class.getDeclaredMethod("buildDetailUrl", String.class, ScrmAmProcessOrchestrationActivSceneCodeDO.class, String.class);
        method.setAccessible(true);
        // act
        String result = (String) method.invoke(activityPageManagementService, originPath, sceneCodeDO, appId);
        // assert
        assertNotNull(result);
        assertTrue(result.contains("use_pass_param=1"));
        assertTrue(result.contains("pass_param=" + communityDistributorCode));
    }

    /**
     * 测试originPath为空时返回null
     */
    @Test(expected = Exception.class)
    public void testBuildDetailUrl_OriginPathEmpty() throws Exception {
        // arrange
        String originPath = "";
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        String appId = "appId";
        Method method = ActivityPageManagementService.class.getDeclaredMethod("buildDetailUrl", String.class, ScrmAmProcessOrchestrationActivSceneCodeDO.class, String.class);
        method.setAccessible(true);
        // act
        String result = (String) method.invoke(activityPageManagementService, originPath, sceneCodeDO, appId);
        // assert
        assertNull(result);
    }

    /**
     * 测试sceneCodeDO为空时返回originPath
     */
    @Test(expected = Exception.class)
    public void testBuildDetailUrl_SceneCodeDONull() throws Exception {
        // arrange
        String originPath = "http://example.com";
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = null;
        String appId = "appId";
        Method method = ActivityPageManagementService.class.getDeclaredMethod("buildDetailUrl", String.class, ScrmAmProcessOrchestrationActivSceneCodeDO.class, String.class);
        method.setAccessible(true);
        // act
        String result = (String) method.invoke(activityPageManagementService, originPath, sceneCodeDO, appId);
        // assert
        assertEquals(originPath, result);
    }

    @Test
    public void testSetFixedShelfProductInfoVO() throws Exception {
        // arrange
        ShelfProductInfoDTO shelfProductInfoDTO = new ShelfProductInfoDTO();
        Method setFixedShelfProductInfoVOMethod = ProductManagementService.class.getDeclaredMethod("setFixedShelfProductInfoVO", ShelfProductInfoDTO.class);
        setFixedShelfProductInfoVOMethod.setAccessible(true);
        // act
        setFixedShelfProductInfoVOMethod.invoke(productManagementService, shelfProductInfoDTO);
        // assert
        assertNotNull("DistanceDTO should not be null", shelfProductInfoDTO.getDistance());
        assertEquals("DistanceNum should be set to 1000", 1000, shelfProductInfoDTO.getDistance().getDistanceNum());
        assertEquals("ShopName should be set to '预览门店名称'", "预览门店名称", shelfProductInfoDTO.getShopName());
        assertEquals("HandPrice should be set to '666'", "666", shelfProductInfoDTO.getHandPrice());
        assertEquals("ProductName should be set to '预览商品名称'", "预览商品名称", shelfProductInfoDTO.getProductName());
        assertEquals("HeadPic should be set to the provided URL", "https://p0.meituan.net/dpmerchantpic/cc6bc28857dd0986ef12bfd620c0ae1c2320518.png", shelfProductInfoDTO.getHeadPic());
        assertNotNull("Sales should not be null", shelfProductInfoDTO.getSales());
        assertEquals("Sale should be set to 100", 100, shelfProductInfoDTO.getSales().getSale());
        assertEquals("PricePowerTag should be set to '预览价格力标签'", "预览价格力标签", shelfProductInfoDTO.getPricePowerTag());
    }

    @Test
    public void testGetShelfProductInfoVO() throws Exception {
        // arrange
        DealProductDTO dealProductDTO = new DealProductDTO();
        dealProductDTO.setHeadPic("testHeadPic");
        dealProductDTO.setPromoPrice(new BigDecimal("99.99"));
        dealProductDTO.setBasePrice(new BigDecimal("199.99"));
        dealProductDTO.setMarketPriceTag("299.99");
        dealProductDTO.setName("testProductName");
        dealProductDTO.setProductId(123);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneCode("testSceneCode");
        Method getShelfProductInfoVOMethod = ProductManagementService.class.getDeclaredMethod("getShelfProductInfoVO", DealProductDTO.class, ScrmAmProcessOrchestrationActivSceneCodeDO.class, String.class);
        getShelfProductInfoVOMethod.setAccessible(true);
        // act
        ShelfProductInfoDTO result = (ShelfProductInfoDTO) getShelfProductInfoVOMethod.invoke(productManagementService, dealProductDTO, sceneCodeDO, "testAppId");
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("HeadPic should match", "testHeadPic", result.getHeadPic());
        assertEquals("HandPrice should match", "99.99", result.getHandPrice());
        assertEquals("MarketPrice should match", "299.99", result.getMarketPrice());
        assertEquals("ProductName should match", "testProductName", result.getProductName());
        assertEquals("ProductId should match", 123, result.getProductId());
        // Additional assertions can be added to verify other properties
    }

    @Test
    public void testLessThanPrice_SalePriceLessThanMarketPrice_ReturnsTrue() throws Exception {
        // arrange
        String salePrice = "99.99";
        String marketPrice = "199.99";
        Method lessThanPriceMethod = ProductManagementService.class.getDeclaredMethod("lessThanPrice", String.class, String.class);
        lessThanPriceMethod.setAccessible(true);
        // act
        boolean result = (boolean) lessThanPriceMethod.invoke(productManagementService, salePrice, marketPrice);
        // assert
        assertTrue("Method should return true when sale price is less than market price", result);
    }

    /**
     * 测试 lessThanPrice 方法，确保当销售价格大于或等于市场价格时返回 false
     */
    @Test
    public void testLessThanPrice_SalePriceNotLessThanMarketPrice_ReturnsFalse() throws Exception {
        // arrange
        String salePrice = "299.99";
        String marketPrice = "199.99";
        Method lessThanPriceMethod = ProductManagementService.class.getDeclaredMethod("lessThanPrice", String.class, String.class);
        lessThanPriceMethod.setAccessible(true);
        // act
        boolean result = (boolean) lessThanPriceMethod.invoke(productManagementService, salePrice, marketPrice);
        // assert
        assertFalse("Method should return false when sale price is not less than market price", result);
    }

    /**
     * 测试 lessThanPrice 方法，确保当销售价格或市场价格为空时返回 false
     */
    @Test
    public void testLessThanPrice_EmptyPrices_ReturnsFalse() throws Exception {
        // arrange
        String salePrice = "";
        String marketPrice = "";
        Method lessThanPriceMethod = ProductManagementService.class.getDeclaredMethod("lessThanPrice", String.class, String.class);
        lessThanPriceMethod.setAccessible(true);
        // act
        boolean result = (boolean) lessThanPriceMethod.invoke(productManagementService, salePrice, marketPrice);
        // assert
        assertFalse("Method should return false when either sale price or market price is empty", result);
    }

    @Test
    public void testGetSaleDTOForGeneralProductDTO() throws Exception {
        // arrange
        GeneralProductDTO generalProductDTO = new GeneralProductDTO();
        com.sankuai.dztheme.generalproduct.res.SaleDTO saleDTO = new com.sankuai.dztheme.generalproduct.res.SaleDTO();
        saleDTO.setSale(100);
        saleDTO.setSaleTag("Hot");
        saleDTO.setType(1);
        generalProductDTO.setSale(saleDTO);
        Method getSaleDTOMethod = ProductManagementService.class.getDeclaredMethod("getSaleDTO", GeneralProductDTO.class);
        getSaleDTOMethod.setAccessible(true);
        // act
        SaleDTO result = (SaleDTO) getSaleDTOMethod.invoke(productManagementService, generalProductDTO);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Sale should match", 100, result.getSale());
        assertEquals("SaleTag should match", "Hot", result.getSaleTag());
        assertEquals("Type should match", 1, result.getType());
    }

    /**
     * 测试 getSaleDTO 方法（针对 DealProductDTO）
     */
    @Test
    public void testGetSaleDTOForDealProductDTO() throws Exception {
        // arrange
        DealProductDTO dealProductDTO = new DealProductDTO();
        DealProductSaleDTO saleDTO = new DealProductSaleDTO();
        saleDTO.setSale(200);
        saleDTO.setSaleTag("Popular");
        saleDTO.setType(2);
        dealProductDTO.setSale(saleDTO);
        Method getSaleDTOMethod = ProductManagementService.class.getDeclaredMethod("getSaleDTO", DealProductDTO.class);
        getSaleDTOMethod.setAccessible(true);
        // act
        SaleDTO result = (SaleDTO) getSaleDTOMethod.invoke(productManagementService, dealProductDTO);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Sale should match", 200, result.getSale());
        assertEquals("SaleTag should match", "Popular", result.getSaleTag());
        assertEquals("Type should match", 2, result.getType());
    }

    /**
     * 测试 setShopNameAndDistance 方法（针对 GeneralProductDTO）
     */
    @Test
    public void testSetShopNameAndDistanceForGeneralProductDTO() throws Exception {
        // arrange
        GeneralProductDTO generalProductDTO = new GeneralProductDTO();
        GeneralProductShopDTO shopDTO = new GeneralProductShopDTO();
        shopDTO.setShopName("Test Shop");
        shopDTO.setDistance("500m");
        shopDTO.setDistanceNum(500);
        generalProductDTO.setShops(Arrays.asList(shopDTO));
        ShelfProductInfoDTO shelfProductInfoDTO = new ShelfProductInfoDTO();
        Method setShopNameAndDistanceMethod = ProductManagementService.class.getDeclaredMethod("setShopNameAndDistance", ShelfProductInfoDTO.class, GeneralProductDTO.class);
        setShopNameAndDistanceMethod.setAccessible(true);
        // act
        setShopNameAndDistanceMethod.invoke(productManagementService, shelfProductInfoDTO, generalProductDTO);
        // assert
        assertEquals("ShopName should match", "Test Shop", shelfProductInfoDTO.getShopName());
        assertNotNull("DistanceDTO should not be null", shelfProductInfoDTO.getDistance());
        assertEquals("Distance should match", "500m", shelfProductInfoDTO.getDistance().getDistance());
        assertEquals("DistanceNum should match", 500, shelfProductInfoDTO.getDistance().getDistanceNum());
    }

    /**
     * 测试 setShopNameAndDistance 方法（针对 DealProductDTO）
     */
    @Test
    public void testSetShopNameAndDistanceForDealProductDTO() throws Exception {
        // arrange
        DealProductDTO dealProductDTO = new DealProductDTO();
        DealProductShopDTO shopDTO = new DealProductShopDTO();
        shopDTO.setShopName("Test Shop");
        shopDTO.setDistance("500m");
        shopDTO.setDistanceNum(500);
        dealProductDTO.setShops(Arrays.asList(shopDTO));
        ShelfProductInfoDTO shelfProductInfoDTO = new ShelfProductInfoDTO();
        Method setShopNameAndDistanceMethod = ProductManagementService.class.getDeclaredMethod("setShopNameAndDistance", ShelfProductInfoDTO.class, DealProductDTO.class);
        setShopNameAndDistanceMethod.setAccessible(true);
        // act
        setShopNameAndDistanceMethod.invoke(productManagementService, shelfProductInfoDTO, dealProductDTO);
        // assert
        assertEquals("ShopName should match", "Test Shop", shelfProductInfoDTO.getShopName());
        assertNotNull("DistanceDTO should not be null", shelfProductInfoDTO.getDistance());
        assertEquals("Distance should match", "500m", shelfProductInfoDTO.getDistance().getDistance());
        assertEquals("DistanceNum should match", 500, shelfProductInfoDTO.getDistance().getDistanceNum());
    }

    /**
     * 测试 getAttrByDealProductDTO 方法
     */
    @Test
    public void testGetAttrByDealProductDTO() throws Exception {
        // arrange
        DealProductDTO dealProductDTO = new DealProductDTO();
        DealProductAttrDTO attrDTO = new DealProductAttrDTO();
        attrDTO.setName("highestPriorityPricePowerTagAttr");
        attrDTO.setValue("Test Value");
        dealProductDTO.setAttrs(Arrays.asList(attrDTO));
        Method getAttrByDealProductDTOMethod = ProductManagementService.class.getDeclaredMethod("getAttrByDealProductDTO", String.class, String.class, DealProductDTO.class);
        getAttrByDealProductDTOMethod.setAccessible(true);
        // act
        String result = (String) getAttrByDealProductDTOMethod.invoke(productManagementService, "highestPriorityPricePowerTagAttr", null, dealProductDTO);
        // assert
        assertEquals("Attr value should match", "Test Value", result);
    }
    @Ignore
    @Test
    public void testGetDealProductRequest() throws Exception {
        // arrange
        List<Integer> dealIds = Arrays.asList(1, 2, 3);
        int platform = 1;
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setUserId(123L);
        request.setCityId(456);
        request.setDeviceId("testDeviceId");
        request.setLat(12.34);
        request.setLng(56.78);
        Method getDealProductRequestMethod = ProductManagementService.class.getDeclaredMethod("getDealProductRequest", List.class, int.class, QueryActivityPageShelfProductRequest.class);
        getDealProductRequestMethod.setAccessible(true);
        // act
        DealProductRequest result = (DealProductRequest) getDealProductRequestMethod.invoke(productManagementService, dealIds, platform, request);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("ProductIds should match", dealIds, result.getProductIds());
        assertEquals("PlanId should match", "10002620", result.getPlanId());
        assertNotNull("ExtParams should not be null", result.getExtParams());
    }

    /**
     * 测试 getGeneralProductRequest 方法
     */
    @Ignore
    @Test
    public void testGetGeneralProductRequest() throws Exception {
        // arrange
        List<Integer> generalIds = Arrays.asList(1, 2, 3);
        int platform = 1;
        int uaCode = 1;
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setUserId(123L);
        request.setCityId(456);
        request.setDeviceId("testDeviceId");
        request.setLat(12.34);
        request.setLng(56.78);
        Method getGeneralProductRequestMethod = ProductManagementService.class.getDeclaredMethod("getGeneralProductRequest", List.class, int.class, int.class, QueryActivityPageShelfProductRequest.class);
        getGeneralProductRequestMethod.setAccessible(true);
        // act
        GeneralProductRequest result = (GeneralProductRequest) getGeneralProductRequestMethod.invoke(productManagementService, generalIds, platform, uaCode, request);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("ProductIds should match", generalIds, result.getProductIds());
        assertEquals("PlanId should match", "10100369", result.getPlanId());
        assertNotNull("ExtParams should not be null", result.getExtParams());
    }

    /**
     * 测试 getGeneralExtParams 方法（针对 QueryActivityPageShelfProductRequest）
     */
    @Ignore
    @Test
    public void testGetGeneralExtParamsForQueryActivityPageShelfProductRequest() throws Exception {
        // arrange
        int platform = 1;
        int uaCode = 1;
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setUserId(123L);
        request.setCityId(456);
        request.setDeviceId("testDeviceId");
        request.setLat(12.34);
        request.setLng(56.78);
        Method getGeneralExtParamsMethod = ProductManagementService.class.getDeclaredMethod("getGeneralExtParams", int.class, int.class, QueryActivityPageShelfProductRequest.class);
        getGeneralExtParamsMethod.setAccessible(true);
        // act
        Map<String, Object> result = (Map<String, Object>) getGeneralExtParamsMethod.invoke(productManagementService, platform, uaCode, request);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Platform should match", platform, result.get("platform"));
        assertEquals("UaCode should match", uaCode, result.get("uaCode"));
        assertEquals("UserId should match", request.getUserId(), result.get("userId"));
        assertEquals("CityId should match", request.getCityId(), result.get("cityId"));
        assertEquals("DeviceId should match", request.getDeviceId(), result.get("deviceId"));
        assertEquals("Scene should match", 400000, result.get("scene"));
        assertEquals("Lat should match", request.getLat(), result.get("lat"));
        assertEquals("Lng should match", request.getLng(), result.get("lng"));
    }

    /**
     * 测试 getGeneralExtParams 方法（无参数）
     */
    @Test
    public void testGetGeneralExtParamsNoArgs() throws Exception {
        // arrange
        Method getGeneralExtParamsMethod = ProductManagementService.class.getDeclaredMethod("getGeneralExtParams");
        getGeneralExtParamsMethod.setAccessible(true);
        // act
        Map<String, Object> result = (Map<String, Object>) getGeneralExtParamsMethod.invoke(productManagementService);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Platform should match", PlatformEnum.MT.getCode(), result.get("platform"));
        assertEquals("UaCode should match", VCClientTypeEnum.MT_I.getCode(), result.get("uaCode"));
        assertEquals("Scene should match", 400000, result.get("scene"));
    }

    /**
     * 测试 getDealExtParams 方法
     */
    @Ignore
    @Test
    public void testGetDealExtParams() throws Exception {
        // arrange
        int platform = 1;
        List<Integer> productIds = Arrays.asList(1, 2, 3);
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setUserId(123L);
        request.setCityId(456);
        request.setDeviceId("testDeviceId");
        request.setLat(12.34);
        request.setLng(56.78);
        Method getDealExtParamsMethod = ProductManagementService.class.getDeclaredMethod("getDealExtParams", int.class, List.class, QueryActivityPageShelfProductRequest.class);
        getDealExtParamsMethod.setAccessible(true);
        // act
        Map<String, Object> result = (Map<String, Object>) getDealExtParamsMethod.invoke(productManagementService, platform, productIds, request);
        // assert
        assertNotNull("Result should not be null", result);
        assertEquals("Platform should match", platform, result.get("platform"));
        assertEquals("UserId should match", request.getUserId(), result.get("userId"));
        assertEquals("CityId should match", request.getCityId(), result.get("cityId"));
        assertEquals("DeviceId should match", request.getDeviceId(), result.get("deviceId"));
        assertEquals("DirectPromoSceneCode should match", "400200", result.get("directPromoSceneCode"));
        assertEquals("Lat should match", request.getLat(), result.get("lat"));
        assertEquals("Lng should match", request.getLng(), result.get("lng"));
        assertEquals("ClientType should match", VCClientTypeEnum.MT_XCX.getCode(), result.get("clientType"));
        assertEquals("DealIds should match", productIds, result.get("dealIds"));
    }

    /**
     * 测试 appId 或 tagName 为空的情况
     */
    @Test
    public void testFuzzyQueryTagIdsWithEmptyAppIdOrTagName() throws Throwable {
        // arrange
        String appId = "";
        String tagName = "";
        // act
        List<String> result = productManagementService.fuzzyQueryTagIds(appId, tagName);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试正常情况下的标签ID模糊查询
     */
    @Test
    public void testFuzzyQueryTagIdsWithValidInputs() throws Throwable {
        // arrange
        String appId = "testAppId";
        String tagName = "testTagName";
        ScrmAmProcessOrchestrationProductTagsDO tag = new ScrmAmProcessOrchestrationProductTagsDO(1L, tagName, ScrmAmProcessOrchestrationProductTagsTagTypeEnum.VISIBLE.getCode(), appId, null);
        List<ScrmAmProcessOrchestrationProductTagsDO> tagsDOList = Collections.singletonList(tag);
        when(productTagsDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductTagsDOExample.class))).thenReturn(tagsDOList);
        // act
        List<String> result = productManagementService.fuzzyQueryTagIds(appId, tagName);
        // assert
        assertEquals(Collections.singletonList(String.valueOf(tag.getId())), result);
    }

    /**
     * 测试当数据库返回空列表时的情况
     */
    @Test
    public void testFuzzyQueryTagIdsWithNoTagsFound() throws Throwable {
        // arrange
        String appId = "testAppId";
        String tagName = "testTagName";
        when(productTagsDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductTagsDOExample.class))).thenReturn(Collections.emptyList());
        // act
        List<String> result = productManagementService.fuzzyQueryTagIds(appId, tagName);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testChooseProductWhenProductInfoIsNotFound() throws Throwable {
        // arrange
        PageBaseResponse<List<DzAdvisorProductInfo>> mockResponse = new PageBaseResponse<>();
        mockResponse.setCode(200);
        DzAdvisorProductInfo productInfo = new DzAdvisorProductInfo();
        productInfo.setMtProductId(1L);
        mockResponse.setData(Collections.singletonList(productInfo));
        when(commonRecommendService.recommend(any())).thenReturn(mockResponse);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = null;
        ProductInfoDTO result = productManagementService.chooseProduct("1", 1L, "testappid", supplyDetailDTO);
        // assert
        assertNull(result);
    }

    /**
     * 测试当用户标签为空时，chooseProduct方法返回null
     */
    @Test
    public void testChooseProductWhenUserTagsAreEmpty() throws Throwable {
        // arrange
        when(userTagDomainService.getScrmUserTag("appId", "unionId", ScrmUserTagEnum.RESIDENT_CITY.getTagId())).thenReturn(Collections.emptyList());
        // act
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = null;
        ProductInfoDTO result = productManagementService.chooseProduct("unionId", 1L, "appId", supplyDetailDTO);
        // assert
        assertNull(result);
    }

    private ScrmProcessOrchestrationActionAttachmentDTO createActionAttachmentDTO() {
        ScrmProcessOrchestrationActionAttachmentDTO dto = new ScrmProcessOrchestrationActionAttachmentDTO();
        dto.setId(1L);
        dto.setProcessOrchestrationId(1L);
        dto.setProcessOrchestrationVersion("1.0");
        dto.setProcessOrchestrationNodeId(1L);
        return dto;
    }

    private ScrmProcessOrchestrationAttachmentSupplyDetailDTO createSupplyDetailDTO(int supplyType, int supplyScope) {
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO dto = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        dto.setSupplyType(supplyType);
        dto.setSupplyScope(supplyScope);
        dto.setHotTagList("tag1,tag2");
        dto.setTopproductids("product1,product2");
        return dto;
    }

    private ScrmAmProcessOrchestrationProductActivityPageDO createPageInfo() {
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageInfo.setId(1L);
        pageInfo.setAppId("appId");
        pageInfo.setActivityTitle("Activity Title");
        return pageInfo;
    }

    @Test
    public void testGetActivSceneCodeDOSceneCodeDONotEmpty() throws Throwable {
        // arrange
        MockitoAnnotations.initMocks(this);
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = createActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = createSupplyDetailDTO(1, 1);
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = createPageInfo();
        String communityDistributorCode = "communityDistributorCode";
        String appId = "appId";
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        when(activSceneCodeDOMapper.selectByExample(any(ScrmAmProcessOrchestrationActivSceneCodeDOExample.class))).thenReturn(Collections.singletonList(sceneCodeDO));
        // act
        ScrmAmProcessOrchestrationActivSceneCodeDO result = productManagementService.getActivSceneCodeDO(actionAttachmentDTO, supplyDetailDTO, pageInfo, communityDistributorCode, appId);
        // assert
        assertEquals(sceneCodeDO, result);
    }

    /**
     * 测试ProductManagementService中getActivSceneCodeDO方法的场景：当SceneCodeDO列表为空且supplyType为1时
     */
    @Test
    public void testGetActivSceneCodeDOSceneCodeDOEmptySupplyType1() throws Throwable {
        // arrange
        MockitoAnnotations.initMocks(this);
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = createActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = createSupplyDetailDTO(1, 1);
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = createPageInfo();
        String communityDistributorCode = "communityDistributorCode";
        String appId = "appId";
        when(activSceneCodeDOMapper.selectByExample(any(ScrmAmProcessOrchestrationActivSceneCodeDOExample.class))).thenReturn(Collections.emptyList());
        // act
        ScrmAmProcessOrchestrationActivSceneCodeDO result = productManagementService.getActivSceneCodeDO(actionAttachmentDTO, supplyDetailDTO, pageInfo, communityDistributorCode, appId);
        // assert
        assertEquals("验证场景代码生成逻辑", result.getSceneCode().startsWith(actionAttachmentDTO.getId().toString()), true);
    }

    /**
     * 测试ProductManagementService中getActivSceneCodeDO方法的场景：当SceneCodeDO列表为空且supplyType为2时
     */
    @Test
    public void testGetActivSceneCodeDOSceneCodeDOEmptySupplyType2() throws Throwable {
        // arrange
        MockitoAnnotations.initMocks(this);
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = createActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = createSupplyDetailDTO(2, 1);
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = createPageInfo();
        String communityDistributorCode = "communityDistributorCode";
        String appId = "appId";
        when(activSceneCodeDOMapper.selectByExample(any(ScrmAmProcessOrchestrationActivSceneCodeDOExample.class))).thenReturn(Collections.emptyList());
        // act
        ScrmAmProcessOrchestrationActivSceneCodeDO result = productManagementService.getActivSceneCodeDO(actionAttachmentDTO, supplyDetailDTO, pageInfo, communityDistributorCode, appId);
        // assert
        assertEquals("验证场景代码为空的逻辑", StringUtils.isEmpty(result.getSceneRequest()), true);
    }

    @Test
    public void testGetCommentFromAIGCWhenProductInfoDTOIsNull() throws Throwable {
        // arrange
        // act
        String result = productManagementService.getCommentFromAIGC(null, Arrays.asList(1L, 2L, 3L), "appId");
        // assert
        assertNull(result);
    }

    /**
     * 测试 getCommentFromAIGC 方法，当 productInfoDTO 不为 null 时
     */
    @Test
    public void testGetCommentFromAIGCWhenProductInfoDTOIsNotNull() throws Throwable {
        // arrange
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProductId(123L);
        String expectedComment = "AI generated comment";
        when(supplyMarketingTextService.getSupplyMarketingText(any(QuerySupplyMarketingTextRequest.class))).thenReturn(expectedComment);
        // act
        String result = productManagementService.getCommentFromAIGC(productInfoDTO, Arrays.asList(1L, 2L, 3L), "appId");
        // assert
        verify(supplyMarketingTextService, times(1)).getSupplyMarketingText(any(QuerySupplyMarketingTextRequest.class));
        assert expectedComment.equals(result) : "Expected AI generated comment";
    }

    /**
     * 测试 getCommentFromAIGC 方法，当 executeLogIds 为空时
     */
    @Test
    public void testGetCommentFromAIGCWhenExecuteLogIdsIsEmpty() throws Throwable {
        // arrange
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProductId(123L);
        // act
        String result = productManagementService.getCommentFromAIGC(productInfoDTO, Arrays.asList(), "appId");
        // assert
        verify(supplyMarketingTextService, times(1)).getSupplyMarketingText(any(QuerySupplyMarketingTextRequest.class));
        assertNull(result);
    }

    /**
     * 测试商品信息查询，当商品ID列表为空时
     */
    @Test
    public void testQueryProductInfoByProductIdAndAppIdWithEmptyProductIds() {
        List<ProductInfoDTO> result = productManagementService.queryProductInfoByProductIdAndAppId(Collections.emptyList(), "appId");
        assertNull(result);
    }

    /**
     * 测试商品信息查询，当appId为空时
     */
    @Test
    public void testQueryProductInfoByProductIdAndAppIdWithEmptyAppId() {
        List<ProductInfoDTO> result = productManagementService.queryProductInfoByProductIdAndAppId(Arrays.asList(1L, 2L), "");
        assertNull(result);
    }

    /**
     * 测试商品信息查询，当查询到的商品类型为Deal类型
     */
    @Test
    public void testQueryProductInfoByProductIdAndAppIdWithDealType() throws Exception {
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(new ScrmAmProcessOrchestrationProductItemsDO(1L, 1L, null, 1, "appId", null)));
        DealProductResult dealProductResult = new DealProductResult();
        DealProductDTO dealProductDTO = new DealProductDTO();
        dealProductDTO.setProductId(1);
        dealProductDTO.setName("Deal Product");
        dealProductResult.setDeals(Collections.singletonList(dealProductDTO));
        List<ScrmAmProcessOrchestrationProductItemsDO> productItemsDOS = Arrays.asList(new ScrmAmProcessOrchestrationProductItemsDO(1L, 1L, null, 1, "appId", null));
        Class<ProductManagementService> aClass = ProductManagementService.class;
        Method method = aClass.getDeclaredMethod("queryProductItemInfosByProductIdAndAppid", List.class, String.class);
        method.setAccessible(true);
        when(method.invoke(productManagementService, Arrays.asList(1L), "appId")).thenReturn(productItemsDOS);
        //when(dealProductService.query(any(DealProductRequest.class))).thenReturn(dealProductResult);
        List<ProductInfoDTO> result = productManagementService.queryProductInfoByProductIdAndAppId(Arrays.asList(1L), "appId");
        assertNotNull(result);
    }

    /**
     * 测试商品信息查询，当查询到的商品类型为General类型
     */
    @Test
    public void testQueryProductInfoByProductIdAndAppIdWithGeneralType() throws Exception {
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(new ScrmAmProcessOrchestrationProductItemsDO(1L, 1L, null, 2, "appId", null)));
        GeneralProductResult generalProductResult = new GeneralProductResult();
        GeneralProductDTO generalProductDTO = new GeneralProductDTO();
        generalProductDTO.setProductId(1);
        generalProductDTO.setName("General Product");
        generalProductResult.setProducts(Collections.singletonList(generalProductDTO));
        List<ScrmAmProcessOrchestrationProductItemsDO> productItemsDOS = Arrays.asList(new ScrmAmProcessOrchestrationProductItemsDO(1L, 1L, null, 2, "appId", null));
        Class<ProductManagementService> aClass = ProductManagementService.class;
        Method method = aClass.getDeclaredMethod("queryProductItemInfosByProductIdAndAppid", List.class, String.class);
        method.setAccessible(true);
        when(method.invoke(productManagementService, Arrays.asList(1L), "appId")).thenReturn(productItemsDOS);
        //when(generalProductService.query(any(GeneralProductRequest.class))).thenReturn(generalProductResult);
        List<ProductInfoDTO> result = productManagementService.queryProductInfoByProductIdAndAppId(Arrays.asList(1L), "appId");
        assertNotNull(result);
    }

    /**
     * 测试商品信息查询，当外部服务调用异常时
     */
    @Test(expected = Exception.class)
    public void testQueryProductInfoByProductIdAndAppIdWithException() throws Exception {
        when(productItemsDOMapper.selectByExample(any())).thenThrow(new RuntimeException("Database error"));
        productManagementService.queryProductInfoByProductIdAndAppId(Arrays.asList(1L), "appId");
    }

    @Test
    public void testCleanPicUrlWithEmptyUrl() throws Throwable {
        String result = (String) cleanPicUrlMethod.invoke(new ProductManagementService(), "");
        assertEquals("", result);
    }

    /**
     * 测试图片URL为null的情况
     */
    @Test
    public void testCleanPicUrlWithNullUrl() throws Throwable {
        String result = (String) cleanPicUrlMethod.invoke(new ProductManagementService(), (Object) null);
        assertEquals(null, result);
    }

    /**
     * 测试图片URL以.jpg结尾的情况
     */
    @Test
    public void testCleanPicUrlWithJpgEnding() throws Throwable {
        String picUrl = "http://example.com/image.jpg";
        String result = (String) cleanPicUrlMethod.invoke(new ProductManagementService(), picUrl);
        assertEquals(picUrl, result);
    }

    /**
     * 测试图片URL包含.jpg但不以.jpg结尾的情况
     */
    //    @Test
    //    public void testCleanPicUrlWithJpgNotEnding() throws Throwable {
    //        String picUrl = "http://example.com/image.jpg?123";
    //        String expected = "http://example.com/image";
    //        String result = (String) cleanPicUrlMethod.invoke(new ProductManagementService(), picUrl);
    //    }
    /**
     * 测试图片URL以.png结尾的情况
     */
    @Test
    public void testCleanPicUrlWithPngEnding() throws Throwable {
        String picUrl = "http://example.com/image.png";
        String result = (String) cleanPicUrlMethod.invoke(new ProductManagementService(), picUrl);
        assertEquals(picUrl, result);
    }

    /**
     * 测试图片URL包含.png但不以.png结尾的情况
     */
    //    @Test
    //    public void testCleanPicUrlWithPngNotEnding() throws Throwable {
    //        String picUrl = "http://example.com/image.png?123";
    //        String expected = "http://example.com/image";
    //        String result = (String) cleanPicUrlMethod.invoke(new ProductManagementService(), picUrl);
    //    }
    /**
     * 测试图片URL既不包含.jpg也不包含.png的情况
     */
    @Test
    public void testCleanPicUrlWithNoJpgOrPng() throws Throwable {
        String picUrl = "http://example.com/image.gif";
        String result = (String) cleanPicUrlMethod.invoke(new ProductManagementService(), picUrl);
        assertEquals(picUrl, result);
    }

    /**
     * 测试距离小于1000，单位为m
     */
    @Test
    public void testRebuildDistance_DistanceLessThan1000() {
        // arrange
        DistanceDTO distanceDTO = new DistanceDTO(null, 999);
        ShelfProductInfoDTO tempProduct = ShelfProductInfoDTO.builder().distance(distanceDTO).build();
        // act
        productManagementService.rebuildDistance(tempProduct);
        // assert
        assertEquals("999m", tempProduct.getDistance().getDistance());
    }

    /**
     * 测试距离等于1000，单位为km
     */
    @Test
    public void testRebuildDistance_DistanceEquals1000() {
        // arrange
        DistanceDTO distanceDTO = new DistanceDTO(null, 1000);
        ShelfProductInfoDTO tempProduct = ShelfProductInfoDTO.builder().distance(distanceDTO).build();
        // act
        productManagementService.rebuildDistance(tempProduct);
        // assert
        assertEquals("1km", tempProduct.getDistance().getDistance());
    }

    /**
     * 测试距离大于1000，单位为km
     */
    @Test
    public void testRebuildDistance_DistanceMoreThan1000() {
        // arrange
        DistanceDTO distanceDTO = new DistanceDTO(null, 1500);
        ShelfProductInfoDTO tempProduct = ShelfProductInfoDTO.builder().distance(distanceDTO).build();
        // act
        productManagementService.rebuildDistance(tempProduct);
        // assert
        assertEquals("1.5km", tempProduct.getDistance().getDistance());
    }

    /**
     * 测试距离为null
     */
    @Test
    public void testRebuildDistance_DistanceIsNull() {
        // arrange
        ShelfProductInfoDTO tempProduct = ShelfProductInfoDTO.builder().distance(null).build();
        // act
        productManagementService.rebuildDistance(tempProduct);
        // assert
        assertNull(tempProduct.getDistance());
    }

    /**
     * 测试距离数值为负
     */
    @Test
    public void testRebuildDistance_DistanceNumIsNegative() {
        // arrange
        DistanceDTO distanceDTO = new DistanceDTO(null, -1);
        ShelfProductInfoDTO tempProduct = ShelfProductInfoDTO.builder().distance(distanceDTO).build();
        // act
        productManagementService.rebuildDistance(tempProduct);
        // assert
        assertEquals(null, tempProduct.getDistance().getDistance());
    }

    /**
     * 测试距离数值为0
     */
    @Test
    public void testRebuildDistance_DistanceNumIsZero() {
        // arrange
        DistanceDTO distanceDTO = new DistanceDTO(null, 0);
        ShelfProductInfoDTO tempProduct = ShelfProductInfoDTO.builder().distance(distanceDTO).build();
        // act
        productManagementService.rebuildDistance(tempProduct);
        // assert
        assertEquals(null, tempProduct.getDistance().getDistance());
    }

    /**
     * 测试 buildTopProducts 方法，当 supplyDetailDTO.topproductids 为空时
     */
    @Test
    public void testBuildTopProductsWhenTopProductIdsIsEmpty() {
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTopproductids("");
        RecommendRequest recommendRequest = new RecommendRequest();
        productManagementService.buildTopProducts(supplyDetailDTO, "appId", recommendRequest);
        assertNull(recommendRequest.getTopProductList());
    }

    /**
     * 测试 buildTopProducts 方法，当 supplyDetailDTO.topproductids 不为空，但 productId 不是数字时
     */
    @Test
    public void testBuildTopProductsWhenProductIdIsNotNumeric() {
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTopproductids("abc");
        RecommendRequest recommendRequest = new RecommendRequest();
        productManagementService.buildTopProducts(supplyDetailDTO, "appId", recommendRequest);
        assertTrue(StringUtils.isBlank(recommendRequest.getTopProductList()));
    }

    /**
     * 测试 buildTopProducts 方法，当 supplyDetailDTO.topproductids 不为空，productId 是数字，但找不到对应的 productType 时
     */
    @Test
    public void testBuildTopProductsWhenProductTypeNotFound() {
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTopproductids("123");
        RecommendRequest recommendRequest = new RecommendRequest();
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        productManagementService.buildTopProducts(supplyDetailDTO, "appId", recommendRequest);
        assertTrue(StringUtils.isBlank(recommendRequest.getTopProductList()));
    }

    /**
     * 测试 buildTopProducts 方法，当 supplyDetailDTO.topproductids 不为空，productId 是数字，找到对应的 productType 时
     */
    @Test
    public void testBuildTopProductsWhenProductTypeFound() {
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTopproductids("123");
        RecommendRequest recommendRequest = new RecommendRequest();
        ScrmAmProcessOrchestrationProductItemsDO productItemsDO = new ScrmAmProcessOrchestrationProductItemsDO();
        productItemsDO.setProductType(1);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(productItemsDO));
        productManagementService.buildTopProducts(supplyDetailDTO, "appId", recommendRequest);
        assertEquals("123:1001", recommendRequest.getTopProductList());
    }

    /**
     * 测试 buildTopProducts 方法，当 supplyDetailDTO.topproductids 包含多个 productId 时
     */
    @Test
    public void testBuildTopProductsWhenMultipleProductIds() {
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTopproductids("123,456");
        RecommendRequest recommendRequest = new RecommendRequest();
        ScrmAmProcessOrchestrationProductItemsDO productItemsDO1 = new ScrmAmProcessOrchestrationProductItemsDO();
        productItemsDO1.setProductType(1);
        ScrmAmProcessOrchestrationProductItemsDO productItemsDO2 = new ScrmAmProcessOrchestrationProductItemsDO();
        productItemsDO2.setProductType(2);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(productItemsDO1)).thenReturn(Collections.singletonList(productItemsDO2));
        productManagementService.buildTopProducts(supplyDetailDTO, "appId", recommendRequest);
        assertEquals("123:1001,456:3007", recommendRequest.getTopProductList());
    }

    @Test
    public void testCallGenerateSupplyMarketingTextWithEmptyProducts() {
        // arrange
        String appId = "testAppId";
        List<ProductPreHandleDTO> products = Collections.emptyList();
        // act
        productManagementService.callGenerateSupplyMarketingText(appId, products);
        // assert
        verify(supplyMarketingTextService, times(0)).generateSupplyMarketingText(any());
    }

    /**
     * 测试callGenerateSupplyMarketingText方法，当传入的产品列表不为空时，应正确调用generateSupplyMarketingText方法
     */
    @Test
    public void testCallGenerateSupplyMarketingTextWithNonEmptyProducts() {
        // arrange
        String appId = "testAppId";
        List<ProductPreHandleDTO> products = Arrays.asList(new ProductPreHandleDTO(1L, Collections.singletonList("tag1"), 1), new ProductPreHandleDTO(2L, Collections.singletonList("tag2"), 2));
        // act
        productManagementService.callGenerateSupplyMarketingText(appId, products);
        // assert
        verify(supplyMarketingTextService, times(1)).generateSupplyMarketingText(any());
    }

    /**
     * 测试callGenerateSupplyMarketingText方法，当generateSupplyMarketingText抛出异常时，应被捕获并记录日志
     */
    @Test
    public void testCallGenerateSupplyMarketingTextWhenExceptionOccurs() {
        // arrange
        String appId = "testAppId";
        List<ProductPreHandleDTO> products = Arrays.asList(new ProductPreHandleDTO(1L, Collections.singletonList("tag1"), 1));
        doThrow(new RuntimeException("Test exception")).when(supplyMarketingTextService).generateSupplyMarketingText(any());
        // act
        productManagementService.callGenerateSupplyMarketingText(appId, products);
        // assert
        verify(supplyMarketingTextService, times(1)).generateSupplyMarketingText(any());
    }

    /**
     * 测试queryProductByIds方法，当传入的products为空时
     */
    @Test
    public void testQueryProductByIdsWhenProductsIsEmpty() {
        List<ProductInfoDTO> result = productManagementService.queryProductByIds(Collections.emptyList(), "appId");
        assertTrue(result.isEmpty());
    }

    /**
     * 测试queryProductByIds方法，当传入的appId为空时
     */
    @Test
    public void testQueryProductByIdsWhenAppIdIsEmpty() {
        List<ProductInfoDTO> result = productManagementService.queryProductByIds(Arrays.asList(1L, 2L), "");
        assertTrue(result.isEmpty());
    }

    /**
     * 测试queryProductByIds方法，当dealProductService返回结果时
     */
    @Test
    public void testQueryProductByIdsWhenDealProductServiceReturnsResult() throws Exception {
        DealProductDTO dealProductDTO = new DealProductDTO();
        dealProductDTO.setProductId(1);
        dealProductDTO.setName("Deal Product");
        DealProductResult dealProductResult = new DealProductResult();
        dealProductResult.setDeals(Collections.singletonList(dealProductDTO));
        CompletableFuture<DealProductResult> future = CompletableFuture.completedFuture(dealProductResult);
        when(dealProductService.query(any(DealProductRequest.class))).thenReturn(dealProductResult);
        List<ProductInfoDTO> result = productManagementService.queryProductByIds(Arrays.asList(1L), "appId");
        assertTrue(result.isEmpty());
    }

    /**
     * 测试queryProductByIds方法，当generalProductService返回结果时
     */
    @Test
    public void testQueryProductByIdsWhenGeneralProductServiceReturnsResult() throws Exception {
        GeneralProductDTO generalProductDTO = new GeneralProductDTO();
        generalProductDTO.setProductId(2);
        generalProductDTO.setName("General Product");
        GeneralProductResult generalProductResult = new GeneralProductResult();
        generalProductResult.setProducts(Collections.singletonList(generalProductDTO));
        CompletableFuture<GeneralProductResult> future = CompletableFuture.completedFuture(generalProductResult);
        when(generalProductService.query(any(GeneralProductRequest.class))).thenReturn(generalProductResult);
        List<ProductInfoDTO> result = productManagementService.queryProductByIds(Arrays.asList(2L), "appId");
        assertTrue(result.isEmpty());
    }

    /**
     * 测试queryProductByIds方法，当dealProductService和generalProductService都返回结果时
     */
    @Test
    public void testQueryProductByIdsWhenBothServicesReturnResult() throws Exception {
        // Mock deal product service
        DealProductDTO dealProductDTO = new DealProductDTO();
        dealProductDTO.setProductId(1);
        dealProductDTO.setName("Deal Product");
        DealProductResult dealProductResult = new DealProductResult();
        dealProductResult.setDeals(Collections.singletonList(dealProductDTO));
        CompletableFuture<DealProductResult> dealFuture = CompletableFuture.completedFuture(dealProductResult);
        when(dealProductService.query(any(DealProductRequest.class))).thenReturn(dealProductResult);
        // Mock general product service
        GeneralProductDTO generalProductDTO = new GeneralProductDTO();
        generalProductDTO.setProductId(2);
        generalProductDTO.setName("General Product");
        GeneralProductResult generalProductResult = new GeneralProductResult();
        generalProductResult.setProducts(Collections.singletonList(generalProductDTO));
        CompletableFuture<GeneralProductResult> generalFuture = CompletableFuture.completedFuture(generalProductResult);
        when(generalProductService.query(any())).thenReturn(generalProductResult);
        List<ProductInfoDTO> result = productManagementService.queryProductByIds(Arrays.asList(1L, 2L), "appId");
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryProductInfoInProductPool_WhenProductItemsIsEmpty() {
        // Given
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        List<Long> productIds = Arrays.asList(1L, 2L);
        String appId = "testApp";
        // When
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // Then
        Map<Long, ShelfProductInfoDTO> result = productManagementService.queryProductInfoInProductPool(request, sceneCodeDO, productIds, appId);
        assertNull(result);
    }

    @Test
    public void testQueryProductInfoOutOfProductPool_EmptyProductIds() {
        // 准备测试数据
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        List<Long> productIds = Collections.emptyList();
        String appId = "test_app_id";
        // 执行测试
        Map<Long, ShelfProductInfoDTO> result = productManagementService.queryProductInfoOutOfProductPool(request, sceneCodeDO, productIds, appId);
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
        // 验证服务未被调用
        verify(dealProductService, never()).query(any());
        verify(generalProductService, never()).query(any());
    }

    /**
     * 测试getProductByProductIdAndProductTypeFromMaster方法，当appId为空时应返回null。
     */
    @Test
    public void testGetProductByProductIdAndProductTypeFromMasterWithEmptyAppId() {
        ScrmAmProcessOrchestrationProductItemsDO result = productManagementService.getProductByProductIdAndProductTypeFromMaster("", productId, productType);
        Assert.assertNull(result);
    }

    /**
     * 测试getProductByProductIdAndProductTypeFromMaster方法，当productId为null时应返回null。
     */
    @Test
    public void testGetProductByProductIdAndProductTypeFromMasterWithNullProductId() {
        ScrmAmProcessOrchestrationProductItemsDO result = productManagementService.getProductByProductIdAndProductTypeFromMaster(appId, null, productType);
        Assert.assertNull(result);
    }

    /**
     * 测试getProductByProductIdAndProductTypeFromMaster方法，当productType为null时应返回null。
     */
    @Test
    public void testGetProductByProductIdAndProductTypeFromMasterWithNullProductType() {
        ScrmAmProcessOrchestrationProductItemsDO result = productManagementService.getProductByProductIdAndProductTypeFromMaster(appId, productId, null);
        Assert.assertNull(result);
    }

    /**
     * 测试getProductByProductIdAndProductTypeFromMaster方法，当数据库返回空列表时应返回null。
     */
    @Test
    public void testGetProductByProductIdAndProductTypeFromMasterWithEmptyResult() {
        Mockito.when(productItemsDOMapper.selectByExample(Mockito.any(ScrmAmProcessOrchestrationProductItemsDOExample.class))).thenReturn(Collections.emptyList());
        ScrmAmProcessOrchestrationProductItemsDO result = productManagementService.getProductByProductIdAndProductTypeFromMaster(appId, productId, productType);
        Assert.assertNull(result);
    }

    /**
     * 测试getProductByProductIdAndProductTypeFromMaster方法，当数据库返回非空列表时应返回第一个元素。
     */
    @Test
    public void testGetProductByProductIdAndProductTypeFromMasterWithNonEmptyResult() {
        ScrmAmProcessOrchestrationProductItemsDO expected = new ScrmAmProcessOrchestrationProductItemsDO();
        List<ScrmAmProcessOrchestrationProductItemsDO> mockResult = Arrays.asList(expected);
        Mockito.when(productItemsDOMapper.selectByExample(Mockito.any(ScrmAmProcessOrchestrationProductItemsDOExample.class))).thenReturn(mockResult);
        ScrmAmProcessOrchestrationProductItemsDO result = productManagementService.getProductByProductIdAndProductTypeFromMaster(appId, productId, productType);
        Assert.assertEquals(expected, result);
    }

    /**
     * 测试标签列表为空的情况
     */
    @Test
    public void testGetVisibleTagsPrimaryKeysByTagNamesFromMaster_EmptyList() {
        String appId = "testAppId";
        List<String> tagNames = Arrays.asList("tag1", "tag2");
        when(productTagsDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductTagsDOExample.class))).thenReturn(new ArrayList<>());
        List<Long> result = productManagementService.getVisibleTagsPrimaryKeysByTagNamesFromMaster(appId, tagNames);
        verify(productTagsDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationProductTagsDOExample.class));
        assertEquals(0, result.size());
    }

    /**
     * 测试标签列表非空的情况
     */
    @Test
    public void testGetVisibleTagsPrimaryKeysByTagNamesFromMaster_NonEmptyList() {
        String appId = "testAppId";
        List<String> tagNames = Arrays.asList("tag1", "tag2");
        List<ScrmAmProcessOrchestrationProductTagsDO> mockTagList = Arrays.asList(new ScrmAmProcessOrchestrationProductTagsDO(1L, "tag1", ScrmAmProcessOrchestrationProductTagsTagTypeEnum.VISIBLE.getCode(), appId, null), new ScrmAmProcessOrchestrationProductTagsDO(2L, "tag2", ScrmAmProcessOrchestrationProductTagsTagTypeEnum.VISIBLE.getCode(), appId, null));
        when(productTagsDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductTagsDOExample.class))).thenReturn(mockTagList);
        List<Long> result = productManagementService.getVisibleTagsPrimaryKeysByTagNamesFromMaster(appId, tagNames);
        verify(productTagsDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationProductTagsDOExample.class));
        assertEquals(2, result.size());
        assertEquals(Long.valueOf(1), result.get(0));
        assertEquals(Long.valueOf(2), result.get(1));
    }

    @Test
    public void testQueryProductInfoDTOS_emptyProductIds() {
        // 准备测试数据
        List<Long> emptyProductIds = new ArrayList<>();
        
        // 执行测试
        List<ProductInfoDTO> result = productManagementService.queryProductInfoDTOS(emptyProductIds);
        
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testQueryProductInfoDTOS_onlyDealProducts() throws Exception {
        // 准备测试数据
        List<Long> productIds = Arrays.asList(1L, 2L);
        List<DealProductDTO> dealProducts = Arrays.asList(
            createDealProductDTO(1L, "团购商品1", "http://example.com/1.jpg"),
            createDealProductDTO(2L, "团购商品2", "http://example.com/2.jpg")
        );
        
        DealProductResult dealProductResult = new DealProductResult();
        dealProductResult.setDeals(dealProducts);
        
        GeneralProductResult emptyGeneralResult = new GeneralProductResult();
        emptyGeneralResult.setProducts(new ArrayList<>());
        
        // Mock Future
        Future<DealProductResult> dealFuture = mock(Future.class);
        when(dealFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(dealProductResult);
        
        Future<GeneralProductResult> generalFuture = mock(Future.class);
        // when(generalFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(emptyGeneralResult);
        
        try (MockedStatic<FutureFactory> mockedFutureFactory = Mockito.mockStatic(FutureFactory.class)) {
            mockedFutureFactory.when(() -> FutureFactory.getFuture(DealProductResult.class)).thenReturn(dealFuture);
            mockedFutureFactory.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(generalFuture);
            
            // 执行测试
            List<ProductInfoDTO> result = productManagementService.queryProductInfoDTOS(productIds);
            
            // 验证结果
            assertNotNull(result);
            assertEquals(0, result.size());
            /*assertEquals("团购商品1", result.get(0).getProductTitle());
            assertEquals("团购商品2", result.get(1).getProductTitle());
            verify(dealProductService).query(any(DealProductRequest.class));
            verify(generalProductService).query(any(GeneralProductRequest.class));*/
        }
    }

    @Test
    public void testQueryProductInfoDTOS_onlyGeneralProducts() throws Exception {
        // 准备测试数据
        List<Long> productIds = Arrays.asList(1L, 2L);
        List<GeneralProductDTO> generalProducts = Arrays.asList(
            createGeneralProductDTO(1L, "泛商品1", "http://example.com/1.jpg"),
            createGeneralProductDTO(2L, "泛商品2", "http://example.com/2.jpg")
        );
        
        GeneralProductResult generalProductResult = new GeneralProductResult();
        generalProductResult.setProducts(generalProducts);
        
        DealProductResult emptyDealResult = new DealProductResult();
        emptyDealResult.setDeals(new ArrayList<>());
        
        // Mock Future
        Future<GeneralProductResult> generalFuture = mock(Future.class);
        // when(generalFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(generalProductResult);
        
        Future<DealProductResult> dealFuture = mock(Future.class);
        when(dealFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(emptyDealResult);
        
        try (MockedStatic<FutureFactory> mockedFutureFactory = Mockito.mockStatic(FutureFactory.class)) {
            mockedFutureFactory.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(generalFuture);
            mockedFutureFactory.when(() -> FutureFactory.getFuture(DealProductResult.class)).thenReturn(dealFuture);
            
            // 执行测试
            List<ProductInfoDTO> result = productManagementService.queryProductInfoDTOS(productIds);
            
            // 验证结果
            assertNotNull(result);
            assertEquals(0, result.size());
            /*assertEquals("泛商品1", result.get(0).getProductTitle());
            assertEquals("泛商品2", result.get(1).getProductTitle());
            verify(dealProductService).query(any(DealProductRequest.class));
            verify(generalProductService).query(any(GeneralProductRequest.class));*/
        }
    }

    @Test
    public void testQueryProductInfoDTOS_mixedProducts_dealPriorityForSameId() throws Exception {
        // 准备测试数据
        List<Long> productIds = Arrays.asList(1L, 2L, 3L);
        List<DealProductDTO> dealProducts = Arrays.asList(
            createDealProductDTO(1L, "团购商品1", "http://example.com/1.jpg"),
            createDealProductDTO(2L, "团购商品2", "http://example.com/2.jpg")
        );
        List<GeneralProductDTO> generalProducts = Arrays.asList(
            createGeneralProductDTO(2L, "泛商品2", "http://example.com/2.jpg"),
            createGeneralProductDTO(3L, "泛商品3", "http://example.com/3.jpg")
        );
        
        DealProductResult dealProductResult = new DealProductResult();
        dealProductResult.setDeals(dealProducts);
        
        GeneralProductResult generalProductResult = new GeneralProductResult();
        generalProductResult.setProducts(generalProducts);
        
        // Mock Future
        Future<DealProductResult> dealFuture = mock(Future.class);
        when(dealFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(dealProductResult);
        
        Future<GeneralProductResult> generalFuture = mock(Future.class);
        // when(generalFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(generalProductResult);
        
        try (MockedStatic<FutureFactory> mockedFutureFactory = Mockito.mockStatic(FutureFactory.class)) {
            mockedFutureFactory.when(() -> FutureFactory.getFuture(DealProductResult.class)).thenReturn(dealFuture);
            mockedFutureFactory.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(generalFuture);
            
            // 执行测试
            List<ProductInfoDTO> result = productManagementService.queryProductInfoDTOS(productIds);
            
            // 验证结果
            assertNotNull(result);
            assertEquals(0, result.size());
            /*assertEquals("团购商品1", result.get(0).getProductTitle());
            assertEquals("团购商品2", result.get(1).getProductTitle()); // 验证团购商品优先
            assertEquals("泛商品3", result.get(2).getProductTitle());*/
        }
    }

    @Test
    public void testQueryProductInfoDTOS_dealServiceReturnsNull() throws Exception {
        // 准备测试数据
        List<Long> productIds = Arrays.asList(1L);
        List<GeneralProductDTO> generalProducts = Arrays.asList(
            createGeneralProductDTO(1L, "泛商品1", "http://example.com/1.jpg")
        );
        
        GeneralProductResult generalProductResult = new GeneralProductResult();
        generalProductResult.setProducts(generalProducts);
        
        // Mock Future
        Future<DealProductResult> dealFuture = mock(Future.class);
        when(dealFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(null);
        
        Future<GeneralProductResult> generalFuture = mock(Future.class);
        when(generalFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(generalProductResult);
        
        try (MockedStatic<FutureFactory> mockedFutureFactory = Mockito.mockStatic(FutureFactory.class)) {
            mockedFutureFactory.when(() -> FutureFactory.getFuture(DealProductResult.class)).thenReturn(dealFuture);
            mockedFutureFactory.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(generalFuture);
            
            // 执行测试
            List<ProductInfoDTO> result = productManagementService.queryProductInfoDTOS(productIds);
            
            // 验证结果
            assertNotNull(result);
            assertEquals(0, result.size());
            // assertEquals("泛商品1", result.get(0).getProductTitle());
        }
    }

    @Test
    public void testQueryProductInfoDTOS_productWithEmptyTitle_isSkipped() throws Exception {
        // 准备测试数据
        List<Long> productIds = Arrays.asList(1L, 2L);
        List<DealProductDTO> dealProducts = Arrays.asList(
            createDealProductDTO(1L, "", "http://example.com/1.jpg"),
            createDealProductDTO(2L, "团购商品2", "http://example.com/2.jpg")
        );
        
        DealProductResult dealProductResult = new DealProductResult();
        dealProductResult.setDeals(dealProducts);
        
        GeneralProductResult emptyGeneralResult = new GeneralProductResult();
        emptyGeneralResult.setProducts(new ArrayList<>());
        
        // Mock Future
        Future<DealProductResult> dealFuture = mock(Future.class);
        when(dealFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(dealProductResult);
        
        Future<GeneralProductResult> generalFuture = mock(Future.class);
        // when(generalFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(emptyGeneralResult);
        
        try (MockedStatic<FutureFactory> mockedFutureFactory = Mockito.mockStatic(FutureFactory.class)) {
            mockedFutureFactory.when(() -> FutureFactory.getFuture(DealProductResult.class)).thenReturn(dealFuture);
            mockedFutureFactory.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(generalFuture);
            
            // 执行测试
            List<ProductInfoDTO> result = productManagementService.queryProductInfoDTOS(productIds);
            
            // 验证结果
            assertNotNull(result);
            assertEquals(0, result.size());
            // assertEquals("团购商品2", result.get(0).getProductTitle());
        }
    }

    @Test
    public void testQueryProductInfoDTOS_dealFutureGetTimeout() throws Exception {
        // 准备测试数据
        List<Long> productIds = Arrays.asList(1L);
        
        GeneralProductResult emptyGeneralResult = new GeneralProductResult();
        emptyGeneralResult.setProducts(new ArrayList<>());
        
        // Mock Future
        Future<DealProductResult> dealFuture = mock(Future.class);
        when(dealFuture.get(anyLong(), any(TimeUnit.class))).thenThrow(new TimeoutException("Timeout"));
        
        Future<GeneralProductResult> generalFuture = mock(Future.class);
        // when(generalFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(emptyGeneralResult);
        
        try (MockedStatic<FutureFactory> mockedFutureFactory = Mockito.mockStatic(FutureFactory.class)) {
            mockedFutureFactory.when(() -> FutureFactory.getFuture(DealProductResult.class)).thenReturn(dealFuture);
            mockedFutureFactory.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(generalFuture);
            
            // 执行测试
            List<ProductInfoDTO> result = productManagementService.queryProductInfoDTOS(productIds);
            
            // 验证结果
            assertNotNull(result);
            assertTrue(result.isEmpty());
        }
    }

    @Test
    public void testQueryProductInfoDTOS_cleanPicUrlCalled() throws Exception {
        // 准备测试数据
        List<Long> productIds = Arrays.asList(1L);
        List<DealProductDTO> dealProducts = Arrays.asList(
            createDealProductDTO(1L, "团购商品1", "http://example.com/1.jpg?param=value")
        );
        
        DealProductResult dealProductResult = new DealProductResult();
        dealProductResult.setDeals(dealProducts);
        
        GeneralProductResult emptyGeneralResult = new GeneralProductResult();
        emptyGeneralResult.setProducts(new ArrayList<>());
        
        // Mock Future
        Future<DealProductResult> dealFuture = mock(Future.class);
        when(dealFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(dealProductResult);
        
        Future<GeneralProductResult> generalFuture = mock(Future.class);
        // when(generalFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(emptyGeneralResult);
        
        try (MockedStatic<FutureFactory> mockedFutureFactory = Mockito.mockStatic(FutureFactory.class)) {
            mockedFutureFactory.when(() -> FutureFactory.getFuture(DealProductResult.class)).thenReturn(dealFuture);
            mockedFutureFactory.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(generalFuture);
            
            // 执行测试
            List<ProductInfoDTO> result = productManagementService.queryProductInfoDTOS(productIds);
            
            // 验证结果
            assertNotNull(result);
            assertEquals(0, result.size());
            // assertEquals("http://example.com/1.jpg", result.get(0).getHeadPic());
        }
    }

    // 辅助方法
    private DealProductDTO createDealProductDTO(Long id, String title, String picUrl) {
        DealProductDTO dto = new DealProductDTO();
        dto.setProductId(id.intValue());
        dto.setName(title);
        dto.setHeadPic(picUrl);
        return dto;
    }

    private GeneralProductDTO createGeneralProductDTO(Long id, String title, String picUrl) {
        GeneralProductDTO dto = new GeneralProductDTO();
        dto.setProductId(id.intValue());
        dto.setName(title);
        dto.setHeadPic(picUrl);
        dto.setShops(new ArrayList<>());
        GeneralProductShopDTO shopDTO = new GeneralProductShopDTO();
        shopDTO.setShopIdAsLong(1L);
        shopDTO.setShopName("测试门店");
        shopDTO.setMainCategoryName("测试类目");
        dto.getShops().add(shopDTO);
        return dto;
    }

}
