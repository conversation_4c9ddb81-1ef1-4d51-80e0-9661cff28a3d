package com.sankuai.scrm.core.service.infrastructure.acl.ds;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.service.fe.corp.wx.thrift.CorpWxService;
import com.sankuai.service.fe.corp.wx.thrift.GetQrCodeForWeekRequest;
import com.sankuai.service.fe.corp.wx.thrift.GetQrCodeForWeekResponse;
import com.sankuai.service.fe.corp.wx.thrift.QrCodeInfo;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.SendGroupMsgTRequest;
import com.sankuai.service.fe.corp.ds.TResponse.BaseTResponse;
import com.sankuai.service.fe.corp.ds.TResponse.openapi.msg.SendGroupMsgTResponse;
import com.sankuai.service.fe.corp.ds.tservice.openapi.msg.GroupMsgTService;
import org.apache.thrift.TException;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DsCorpGroupAclServiceQueryQrCodeForWeekTest {

    @Mock
    private CorpWxService.Iface corpWxService;

    @InjectMocks
    private DsCorpGroupAclService dsCorpGroupAclService;

    @Mock
    private GroupMsgTService groupMsgTService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test case for empty groupId
     */
    @Test
    public void testQueryQrCodeForWeekEmptyGroupId() throws Throwable {
        // arrange
        String groupId = "";
        boolean forceRefresh = false;
        String assistantUserId = "assistant1";
        // act
        QrCodeInfo result = dsCorpGroupAclService.queryQrCodeForWeek(groupId, forceRefresh, assistantUserId);
        // assert
        assertNull(result);
        verify(corpWxService, never()).getQrCodeForWeek(any());
    }

    /**
     * Test case for successful scenario with valid inputs
     */
    @Test
    public void testQueryQrCodeForWeekSuccess() throws Throwable {
        // arrange
        String groupId = "group1";
        boolean forceRefresh = true;
        String assistantUserId = "assistant1";
        QrCodeInfo expectedQrCodeInfo = new QrCodeInfo();
        expectedQrCodeInfo.setQrCodeUrl("http://example.com/qr");
        GetQrCodeForWeekResponse response = new GetQrCodeForWeekResponse();
        response.setCode(0);
        response.setData(expectedQrCodeInfo);
        when(corpWxService.getQrCodeForWeek(any())).thenReturn(response);
        // act
        QrCodeInfo result = dsCorpGroupAclService.queryQrCodeForWeek(groupId, forceRefresh, assistantUserId);
        // assert
        assertNotNull(result);
        assertEquals(expectedQrCodeInfo, result);
        verify(corpWxService).getQrCodeForWeek(any());
    }

    /**
     * Test case for null response from corpWxService
     */
    @Test
    public void testQueryQrCodeForWeekNullResponse() throws Throwable {
        // arrange
        String groupId = "group1";
        boolean forceRefresh = false;
        String assistantUserId = "assistant1";
        when(corpWxService.getQrCodeForWeek(any())).thenReturn(null);
        // act
        QrCodeInfo result = dsCorpGroupAclService.queryQrCodeForWeek(groupId, forceRefresh, assistantUserId);
        // assert
        assertNull(result);
        verify(corpWxService).getQrCodeForWeek(any());
    }

    /**
     * Test case for response with non-zero code
     */
    @Test
    public void testQueryQrCodeForWeekNonZeroCode() throws Throwable {
        // arrange
        String groupId = "group1";
        boolean forceRefresh = false;
        String assistantUserId = "assistant1";
        GetQrCodeForWeekResponse response = new GetQrCodeForWeekResponse();
        response.setCode(1);
        response.setData(new QrCodeInfo());
        when(corpWxService.getQrCodeForWeek(any())).thenReturn(response);
        // act
        QrCodeInfo result = dsCorpGroupAclService.queryQrCodeForWeek(groupId, forceRefresh, assistantUserId);
        // assert
        assertNull(result);
        verify(corpWxService).getQrCodeForWeek(any());
    }

    /**
     * Test case for response with null data
     */
    @Test
    public void testQueryQrCodeForWeekNullData() throws Throwable {
        // arrange
        String groupId = "group1";
        boolean forceRefresh = false;
        String assistantUserId = "assistant1";
        GetQrCodeForWeekResponse response = new GetQrCodeForWeekResponse();
        response.setCode(0);
        response.setData(null);
        when(corpWxService.getQrCodeForWeek(any())).thenReturn(response);
        // act
        QrCodeInfo result = dsCorpGroupAclService.queryQrCodeForWeek(groupId, forceRefresh, assistantUserId);
        // assert
        assertNull(result);
        verify(corpWxService).getQrCodeForWeek(any());
    }

    /**
     * Test case for exception thrown by corpWxService
     */
    @Test
    public void testQueryQrCodeForWeekException() throws Throwable {
        // arrange
        String groupId = "group1";
        boolean forceRefresh = false;
        String assistantUserId = "assistant1";
        when(corpWxService.getQrCodeForWeek(any())).thenThrow(new RuntimeException("Service error"));
        // act
        QrCodeInfo result = dsCorpGroupAclService.queryQrCodeForWeek(groupId, forceRefresh, assistantUserId);
        // assert
        assertNull(result);
        verify(corpWxService).getQrCodeForWeek(any());
    }

    @Test
    public void testSendGroupMsgRequestIsNull() throws Throwable {
        // arrange
        SendGroupMsgTRequest request = null;
        // act
        BaseTResponse<SendGroupMsgTResponse> result = dsCorpGroupAclService.sendGroupMsg(request);
        // assert
        assertNull(result);
    }

    @Test
    public void testSendGroupMsgRequestIsValidAndServiceSuccess() throws Throwable {
        // arrange
        SendGroupMsgTRequest request = new SendGroupMsgTRequest();
        BaseTResponse<SendGroupMsgTResponse> expectedResponse = new BaseTResponse<>();
        when(groupMsgTService.sendGroupMsg(request)).thenReturn(expectedResponse);
        // act
        BaseTResponse<SendGroupMsgTResponse> result = dsCorpGroupAclService.sendGroupMsg(request);
        // assert
        assertSame(expectedResponse, result);
        verify(groupMsgTService).sendGroupMsg(request);
    }

    @Test
    public void testSendGroupMsgRequestIsValidAndServiceThrowsException() throws Throwable {
        // arrange
        SendGroupMsgTRequest request = new SendGroupMsgTRequest();
        when(groupMsgTService.sendGroupMsg(request)).thenThrow(TException.class);
        // act
        BaseTResponse<SendGroupMsgTResponse> result = dsCorpGroupAclService.sendGroupMsg(request);
        // assert
        assertNull(result);
        verify(groupMsgTService).sendGroupMsg(request);
    }

    @Test
    public void testSendGroupMsgRequestIsValidAndServiceReturnsNull() throws Throwable {
        // arrange
        SendGroupMsgTRequest request = new SendGroupMsgTRequest();
        when(groupMsgTService.sendGroupMsg(request)).thenReturn(null);
        // act
        BaseTResponse<SendGroupMsgTResponse> result = dsCorpGroupAclService.sendGroupMsg(request);
        // assert
        assertNull(result);
        verify(groupMsgTService).sendGroupMsg(request);
    }
}
