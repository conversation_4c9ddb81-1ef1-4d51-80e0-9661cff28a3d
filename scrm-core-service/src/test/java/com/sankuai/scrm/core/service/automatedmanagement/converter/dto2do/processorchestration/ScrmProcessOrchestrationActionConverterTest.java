package com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration;

import static org.junit.Assert.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActionDO;
import java.util.Date;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ScrmProcessOrchestrationActionConverterTest {

    private ScrmProcessOrchestrationActionConverter converter = new ScrmProcessOrchestrationActionConverter();

    /**
     * 测试 convertToDTO 方法，当输入为 null 时，应返回 null
     */
    @Test
    public void testConvertToDTONullInput() {
        // arrange
        ScrmAmProcessOrchestrationActionDO input = null;
        // act
        ScrmProcessOrchestrationActionDTO result = converter.convertToDTO(input);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试 convertToDTO 方法，当输入不为 null 时，应返回一个新的 ScrmProcessOrchestrationActionDTO 对象，其属性值与输入对象的属性值相同
     */
    @Test
    public void testConvertToDTONonNullInput() {
        // arrange
        ScrmAmProcessOrchestrationActionDO input = new ScrmAmProcessOrchestrationActionDO();
        input.setId(1L);
        input.setActionId(2);
        input.setProcessOrchestrationId(3L);
        input.setProcessOrchestrationVersion("4");
        input.setProcessOrchestrationNodeId(5L);
        input.setActionType((byte) 6);
        input.setUpdateTime(new Date());
        input.setActionSubType(7);
        input.setContentType((byte) 8);
        // act
        ScrmProcessOrchestrationActionDTO result = converter.convertToDTO(input);
        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(input.getId(), result.getId());
        Assert.assertEquals(input.getActionId(), result.getActionId());
        Assert.assertEquals(input.getProcessOrchestrationId(), result.getProcessOrchestrationId());
        Assert.assertEquals(input.getProcessOrchestrationVersion(), result.getProcessOrchestrationVersion());
        Assert.assertEquals(input.getProcessOrchestrationNodeId(), result.getProcessOrchestrationNodeId());
        Assert.assertEquals(input.getActionType(), result.getActionType());
        Assert.assertEquals(input.getUpdateTime(), result.getUpdateTime());
        Assert.assertEquals(input.getActionSubType(), result.getActionSubType());
        Assert.assertEquals(input.getContentType().intValue(), result.getContentType().intValue());
    }

    /**
     * 测试 convertToDO 方法，当输入为 null 时，应返回 null
     */
    @Test
    public void testConvertToDONullInput() {
        // arrange
        ScrmProcessOrchestrationActionDTO resource = null;
        // act
        ScrmAmProcessOrchestrationActionDO result = converter.convertToDO(resource);
        // assert
        assertNull(result);
    }

    /**
     * 测试 convertToDO 方法，当输入不为 null 时，应返回一个新的 ScrmAmProcessOrchestrationActionDO 对象，其属性值与输入对象的属性值相同
     */
    @Test
    public void testConvertToDONonNullInput() {
        // arrange
        ScrmProcessOrchestrationActionDTO resource = new ScrmProcessOrchestrationActionDTO();
        resource.setId(1L);
        resource.setActionId(1);
        resource.setProcessOrchestrationId(1L);
        resource.setProcessOrchestrationVersion("1");
        resource.setProcessOrchestrationNodeId(1L);
        resource.setActionType((byte) 1);
        resource.setActionSubType(1);
        resource.setUpdateTime(new java.util.Date());
        resource.setContentType(1);
        // act
        ScrmAmProcessOrchestrationActionDO result = converter.convertToDO(resource);
        // assert
        assertNotNull(result);
        assertEquals(resource.getId(), result.getId());
        assertEquals(resource.getActionId(), result.getActionId());
        assertEquals(resource.getProcessOrchestrationId(), result.getProcessOrchestrationId());
        assertEquals(resource.getProcessOrchestrationVersion(), result.getProcessOrchestrationVersion());
        assertEquals(resource.getProcessOrchestrationNodeId(), result.getProcessOrchestrationNodeId());
        assertEquals(resource.getActionType(), result.getActionType());
        assertEquals(resource.getActionSubType(), result.getActionSubType());
        assertEquals(resource.getUpdateTime(), result.getUpdateTime());
        assertEquals(resource.getContentType().byteValue(), result.getContentType().byteValue());
    }
}
