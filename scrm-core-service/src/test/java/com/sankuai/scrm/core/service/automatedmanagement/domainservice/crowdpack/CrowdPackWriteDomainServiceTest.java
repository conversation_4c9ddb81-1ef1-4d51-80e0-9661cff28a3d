package com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack;

import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmConfigurationChangeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackBaseInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackDetailInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackDetailInfoDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmConfigurationChangeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackBaseInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackDetailInfoDOMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CrowdPackWriteDomainServiceTest {

    @InjectMocks
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock
    private ExtScrmAmCrowdPackDetailInfoDOMapper detailInfoDOMapper;

    @Mock
    private ExtScrmAmCrowdPackBaseInfoDOMapper extScrmAmCrowdPackBaseInfoDOMapper;

    @Mock
    private ScrmAmConfigurationChangeLogDOMapper configurationChangeLogDOMapper;

    @InjectMocks
    private CrowdPackWriteDomainService service;

    /**
     * Test case: delete with null packId
     * Expected: should return without calling mapper
     */
    @Test
    public void testDeleteCrowdPackDetailInfoByPackIdAndVersion_NullPackId() {
        // arrange
        Long packId = null;
        String packVersion = "v1.0";
        // act
        crowdPackWriteDomainService.deleteCrowdPackDetailInfoByPackIdAndVersion(packId, packVersion);
        // assert
        verify(detailInfoDOMapper, never()).deleteByExample(any());
    }

    /**
     * Test case: delete with zero packId
     * Expected: should return without calling mapper
     */
    @Test
    public void testDeleteCrowdPackDetailInfoByPackIdAndVersion_ZeroPackId() {
        // arrange
        Long packId = 0L;
        String packVersion = "v1.0";
        // act
        crowdPackWriteDomainService.deleteCrowdPackDetailInfoByPackIdAndVersion(packId, packVersion);
        // assert
        verify(detailInfoDOMapper, never()).deleteByExample(any());
    }

    /**
     * Test case: delete with negative packId
     * Expected: should return without calling mapper
     */
    @Test
    public void testDeleteCrowdPackDetailInfoByPackIdAndVersion_NegativePackId() {
        // arrange
        Long packId = -1L;
        String packVersion = "v1.0";
        // act
        crowdPackWriteDomainService.deleteCrowdPackDetailInfoByPackIdAndVersion(packId, packVersion);
        // assert
        verify(detailInfoDOMapper, never()).deleteByExample(any());
    }

    /**
     * Test case: delete with null packVersion
     * Expected: should return without calling mapper
     */
    @Test
    public void testDeleteCrowdPackDetailInfoByPackIdAndVersion_NullVersion() {
        // arrange
        Long packId = 1L;
        String packVersion = null;
        // act
        crowdPackWriteDomainService.deleteCrowdPackDetailInfoByPackIdAndVersion(packId, packVersion);
        // assert
        verify(detailInfoDOMapper, never()).deleteByExample(any());
    }

    /**
     * Test case: delete with empty packVersion
     * Expected: should return without calling mapper
     */
    @Test
    public void testDeleteCrowdPackDetailInfoByPackIdAndVersion_EmptyVersion() {
        // arrange
        Long packId = 1L;
        String packVersion = "";
        // act
        crowdPackWriteDomainService.deleteCrowdPackDetailInfoByPackIdAndVersion(packId, packVersion);
        // assert
        verify(detailInfoDOMapper, never()).deleteByExample(any());
    }

    /**
     * Test case: delete with blank packVersion
     * Expected: should return without calling mapper
     */
    @Test
    public void testDeleteCrowdPackDetailInfoByPackIdAndVersion_BlankVersion() {
        // arrange
        Long packId = 1L;
        String packVersion = "   ";
        // act
        crowdPackWriteDomainService.deleteCrowdPackDetailInfoByPackIdAndVersion(packId, packVersion);
        // assert
        verify(detailInfoDOMapper, never()).deleteByExample(any());
    }

    /**
     * Test case: delete with valid parameters
     * Expected: should call mapper with correct example
     */
    @Test
    public void testDeleteCrowdPackDetailInfoByPackIdAndVersion_ValidParameters() {
        // arrange
        Long packId = 1L;
        String packVersion = "v1.0";
        ArgumentCaptor<ScrmAmCrowdPackDetailInfoDOExample> exampleCaptor = ArgumentCaptor.forClass(ScrmAmCrowdPackDetailInfoDOExample.class);
        // act
        crowdPackWriteDomainService.deleteCrowdPackDetailInfoByPackIdAndVersion(packId, packVersion);
        // assert
        verify(detailInfoDOMapper, times(1)).deleteByExample(exampleCaptor.capture());
        ScrmAmCrowdPackDetailInfoDOExample capturedExample = exampleCaptor.getValue();
        assertEquals(1, capturedExample.getOredCriteria().size());
    }

    @Test
    public void testInsertPersonaCrowdPackBaseInfo_Success() throws Throwable {
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setId(1L);
        baseInfoDO.setValidPackVersion("1.0");
        baseInfoDO.setLastUpdaterId("user123");
        when(extScrmAmCrowdPackBaseInfoDOMapper.insert(any(ScrmAmCrowdPackBaseInfoDO.class))).thenReturn(1);
        Long result = crowdPackWriteDomainService.insertCrowdPackBaseInfo(baseInfoDO);
        assertEquals(Long.valueOf(1L), result);
    }

    @Test(expected = NullPointerException.class)
    public void testInsertPersonaCrowdPackBaseInfo_NullInput() throws Throwable {
        crowdPackWriteDomainService.insertCrowdPackBaseInfo(null);
    }

    @Test
    public void testInsertPersonaCrowdPackBaseInfo_InsertFailure() throws Throwable {
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setId(1L);
        when(extScrmAmCrowdPackBaseInfoDOMapper.insert(any(ScrmAmCrowdPackBaseInfoDO.class))).thenReturn(0);
        Long result = crowdPackWriteDomainService.insertCrowdPackBaseInfo(baseInfoDO);
        // Corrected expectation
        assertEquals(Long.valueOf(1L), result);
    }

    @Test
    public void testInsertPersonaCrowdPackBaseInfo_MinimalFields() throws Throwable {
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setId(1L);
        when(extScrmAmCrowdPackBaseInfoDOMapper.insert(any(ScrmAmCrowdPackBaseInfoDO.class))).thenReturn(1);
        Long result = crowdPackWriteDomainService.insertCrowdPackBaseInfo(baseInfoDO);
        assertNotNull(result);
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试正常情况下的方法执行
     */
    @Test
    public void testUpdatePersonaOrExcelCrowdPackNormal() throws Throwable {
        // arrange
        ScrmCrowdPackDTO scrmCrowdPackDTO = new ScrmCrowdPackDTO();
        scrmCrowdPackDTO.setId(1L);
        scrmCrowdPackDTO.setName("Test Pack");
        scrmCrowdPackDTO.setRemark("Test Remark");
        scrmCrowdPackDTO.setValidPackVersion("v2");
        scrmCrowdPackDTO.setLastUpdaterId("user123");
        String preVersion = "v1";
        // act
        service.updatePersonaOrExcelCrowdPack(scrmCrowdPackDTO, preVersion);
        // assert
        verify(extScrmAmCrowdPackBaseInfoDOMapper, times(1)).updateByPrimaryKeySelective(any(ScrmAmCrowdPackBaseInfoDO.class));
        verify(detailInfoDOMapper, times(1)).updateByExampleSelective(any(ScrmAmCrowdPackDetailInfoDO.class), any(ScrmAmCrowdPackDetailInfoDOExample.class));
        verify(configurationChangeLogDOMapper, times(1)).insert(any(ScrmAmConfigurationChangeLogDO.class));
    }

    /**
     * 测试传入空的ScrmCrowdPackDTO时的行为
     */
    @Test(expected = NullPointerException.class)
    public void testUpdatePersonaOrExcelCrowdPackWithNullDTO() throws Throwable {
        // arrange
        ScrmCrowdPackDTO scrmCrowdPackDTO = null;
        String preVersion = "v1";
        // act
        service.updatePersonaOrExcelCrowdPack(scrmCrowdPackDTO, preVersion);
        // assert
        // Expected NullPointerException
    }
}
