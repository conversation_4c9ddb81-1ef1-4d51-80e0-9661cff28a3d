package com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool;

import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductInfo;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductPreHandleDTO;
import com.sankuai.medicalcosmetology.product.selectify.api.service.CommonSelectPoolItemService;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductTagsDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationProductTagsDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductTagsDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.QueryProductInfoDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductManagementService_GetVisibleTagsPrimaryKeysByTagNamesNewTest {

    @InjectMocks
    private ProductManagementService productManagementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductTagsDOMapper productTagsDOMapper;

    private String appId;

    private List<String> tagNames;

    private ScrmAmProcessOrchestrationProductTagsDOExample example;

    private List<ScrmAmProcessOrchestrationProductTagsDO> tagList;

    @Mock(lenient = true)
    private CommonSelectPoolItemService commonSelectPoolItemService;

    private List<ProductPreHandleDTO> products;

    private Map<Integer, Map<Long, QueryProductInfoDTO>> productsInfo;

    @Before
    public void setUp() {
        appId = "testAppId";
        tagNames = Arrays.asList("tag1", "tag2");
        example = new ScrmAmProcessOrchestrationProductTagsDOExample();
        example.createCriteria().andAppIdEqualTo(appId).andTagNameIn(tagNames).andTagTypeEqualTo(1);
        ScrmAmProcessOrchestrationProductTagsDO tag1 = new ScrmAmProcessOrchestrationProductTagsDO();
        tag1.setTagName("tag1");
        tag1.setId(1L);
        ScrmAmProcessOrchestrationProductTagsDO tag2 = new ScrmAmProcessOrchestrationProductTagsDO();
        tag2.setTagName("tag2");
        tag2.setId(2L);
        tagList = Arrays.asList(tag1, tag2);
    }

    private QueryProductInfoDTO createQueryProductInfoDTO(Long productId) {
        QueryProductInfoDTO dto = new QueryProductInfoDTO();
        ProductInfo productInfo = new ProductInfo();
        productInfo.setProductId(productId);
        dto.setProductInfo(productInfo);
        return dto;
    }

    @Test
    public void testGetVisibleTagsPrimaryKeysByTagNamesNew_AppIdOrTagNamesIsNull() {
        Map<String, Long> result = productManagementService.getVisibleTagsPrimaryKeysByTagNamesNew(null, tagNames);
        assertEquals(Collections.emptyMap(), result);
        result = productManagementService.getVisibleTagsPrimaryKeysByTagNamesNew(appId, null);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetVisibleTagsPrimaryKeysByTagNamesNew_TagListIsEmpty() {
        when(productTagsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        Map<String, Long> result = productManagementService.getVisibleTagsPrimaryKeysByTagNamesNew(appId, tagNames);
        assertEquals(Collections.emptyMap(), result);
    }

    @Test
    public void testGetVisibleTagsPrimaryKeysByTagNamesNew_TagListIsNotEmpty() {
        when(productTagsDOMapper.selectByExample(any())).thenReturn(tagList);
        Map<String, Long> result = productManagementService.getVisibleTagsPrimaryKeysByTagNamesNew(appId, tagNames);
        assertEquals(2, result.size());
        assertEquals(Long.valueOf(1), result.get("tag1"));
        assertEquals(Long.valueOf(2), result.get("tag2"));
    }

    @Test
    public void testUpdateCommonProductPoolProductsWhenProductsIsEmpty() throws Throwable {
        appId = "testAppId";
        products = new ArrayList<>();
        productsInfo = new HashMap<>();
        productManagementService.updateCommonProductPoolProducts(appId, products, productsInfo);
        verify(commonSelectPoolItemService, never()).selectPoolProductAction(any());
    }

    @Test
    public void testUpdateCommonProductPoolProductsWhenProductsIsNotEmptyButTagName2ProductIsEmpty() throws Throwable {
        appId = "testAppId";
        products = new ArrayList<>();
        int productType = 1;
        long productId = 1L;
        ProductPreHandleDTO product = new ProductPreHandleDTO();
        product.setProductType(productType);
        product.setProductId(productId);
        products.add(product);
        productsInfo = new HashMap<>();
        productsInfo.put(productType, new HashMap<>());
        productsInfo.get(productType).put(productId, createQueryProductInfoDTO(productId));
        productManagementService.updateCommonProductPoolProducts(appId, products, productsInfo);
        verify(commonSelectPoolItemService, never()).selectPoolProductAction(any());
    }
}
