package com.sankuai.scrm.core.service.automatedmanagement.converter.dto2vo.corwdpack;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationGoalDTO;
import com.sankuai.dz.srcm.automatedmanagement.response.QueryProcessOrchestrationDetailInfoByIdResultVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationInfoVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationNodeVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationGoalVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmCrowdPackUpdateStrategyInfoVO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import org.junit.*;
import static org.mockito.Mockito.*;
import java.io.IOException;

@RunWith(MockitoJUnitRunner.class)
public class QueryProcessOrchestrationDetailInfoByIdResultVOConverter_ConvertToDTOTest {

    @InjectMocks
    private QueryProcessOrchestrationDetailInfoByIdResultVOConverter converter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationInfoVOConverter scrmProcessOrchestrationInfoVOConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationNodeVOConverter scrmProcessOrchestrationNodeVOConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationGoalVOConverter scrmProcessOrchestrationGoalVOConverter;

    @Mock(lenient = true)
    private ScrmCrowdPackUpdateStrategyInfoVOConverter scrmCrowdPackUpdateStrategyInfoVOConverter;

    @Mock(lenient = true)
    private JsonUtils jsonUtils;

    private QueryProcessOrchestrationDetailInfoByIdResultVO resource;

    @Before
    public void setUp() {
        resource = new QueryProcessOrchestrationDetailInfoByIdResultVO();
        resource.setProcessOrchestrationInfo(new ScrmProcessOrchestrationInfoVO());
        resource.setProcessOrchestrationNodeList(new ArrayList<>());
        resource.setGroupIdInfoList(new ArrayList<>());
        resource.setGoal(new ScrmProcessOrchestrationGoalVO());
        resource.setScrmCrowdPackUpdateStrategyInfoVO(new ScrmCrowdPackUpdateStrategyInfoVO());
        resource.setCrowdPackType(1);
        // Mock scrmProcessOrchestrationInfoVOConverter to return a non-null ScrmProcessOrchestrationDTO
        when(scrmProcessOrchestrationInfoVOConverter.convertToDTO(any())).thenReturn(new ScrmProcessOrchestrationDTO());
    }

    @Test
    public void testConvertToDTOWhenResourceIsNull() throws Throwable {
        ScrmProcessOrchestrationDTO result = converter.convertToDTO(null);
        assertNull(result);
    }

    @Test
    public void testConvertToDTOWhenProcessOrchestrationNodeListIsEmpty() throws Throwable {
        resource.setProcessOrchestrationNodeList(new ArrayList<>());
        ScrmProcessOrchestrationDTO result = converter.convertToDTO(resource);
        assertNotNull(result);
        assertNull(result.getNodeMediumDTO());
    }

    @Test
    public void testConvertToDTOWhenGroupIdInfoListIsEmpty() throws Throwable {
        resource.setGroupIdInfoList(new ArrayList<>());
        ScrmProcessOrchestrationDTO result = converter.convertToDTO(resource);
        assertNotNull(result);
        assertEquals(resource.getGroupIdList(), result.getGroupIdList());
    }

    @Test
    public void testConvertToDTOWhenGoalIsNull() throws Throwable {
        resource.setGoal(null);
        ScrmProcessOrchestrationDTO result = converter.convertToDTO(resource);
        assertNotNull(result);
        assertNull(result.getGoalDTO());
    }

    @Test
    public void testConvertToDTOWhenScrmCrowdPackUpdateStrategyInfoVOIsNull() throws Throwable {
        resource.setScrmCrowdPackUpdateStrategyInfoVO(null);
        ScrmProcessOrchestrationDTO result = converter.convertToDTO(resource);
        assertNotNull(result);
        assertNull(result.getCrowdPackUpdateStrategyInfoDTO());
    }
}
