package com.sankuai.scrm.core.service.automatedmanagement.mq.consumer;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import java.lang.reflect.Field;
import java.util.Properties;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmPackUserBatchAddCorpTagConsumerTest {

    @Mock
    private IConsumerProcessor consumer;

    private ScrmPackUserBatchAddCorpTagConsumer scrmPackUserBatchAddCorpTagConsumer;

    @InjectMocks
    private ScrmPackUserBatchAddCorpTagConsumer consumerUnderTest;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        scrmPackUserBatchAddCorpTagConsumer = new ScrmPackUserBatchAddCorpTagConsumer();
        // Use reflection to set the consumer field
        Field consumerField = ScrmPackUserBatchAddCorpTagConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(scrmPackUserBatchAddCorpTagConsumer, consumer);
    }

    /**
     * 测试destroy方法，consumer不为空的情况
     */
    @Test
    public void testDestroyConsumerNotNull() throws Throwable {
        // act
        scrmPackUserBatchAddCorpTagConsumer.destroy();
        // assert
        verify(consumer, times(1)).close();
    }

    /**
     * 测试destroy方法，consumer为空的情况
     */
    @Test
    public void testDestroyConsumerNull() throws Throwable {
        // Use reflection to set the consumer field to null
        Field consumerField = ScrmPackUserBatchAddCorpTagConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(scrmPackUserBatchAddCorpTagConsumer, null);
        // act
        scrmPackUserBatchAddCorpTagConsumer.destroy();
        // assert
        verify(consumer, times(0)).close();
    }

    /**
     * 测试destroy方法，consumer的close方法抛出异常的情况
     */
    @Test(expected = Exception.class)
    public void testDestroyConsumerCloseThrowException() throws Throwable {
        // arrange
        doThrow(new Exception()).when(consumer).close();
        // act
        scrmPackUserBatchAddCorpTagConsumer.destroy();
        // assert
        verify(consumer, times(1)).close();
    }

    /**
     * Tests the afterPropertiesSet method under normal conditions.
     */
    @Test
    public void testAfterPropertiesSetNormal() throws Throwable {
        // Arrange
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
        properties.setProperty(ConsumerConstants.SubscribeGroup, "scrm.pack.user.add.corp.tag.msg.consumer");
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            // Mock the MafkaClient.buildConsumerFactory method to return the mocked consumer
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(properties, "scrm.pack.user.add.corp.tag.msg")).thenReturn(consumer);
            // Use reflection to set the consumer field in the consumerUnderTest
            Field consumerField = ScrmPackUserBatchAddCorpTagConsumer.class.getDeclaredField("consumer");
            consumerField.setAccessible(true);
            consumerField.set(consumerUnderTest, consumer);
            // Act
            consumerUnderTest.afterPropertiesSet();
            // Assert
            verify(consumer, times(1)).recvMessageWithParallel(eq(String.class), any());
        }
    }

    /**
     * Tests the afterPropertiesSet method under exceptional conditions.
     */
    @Test(expected = Exception.class)
    public void testAfterPropertiesSetException() throws Throwable {
        // Arrange
        Properties properties = new Properties();
        properties.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        properties.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
        properties.setProperty(ConsumerConstants.SubscribeGroup, "scrm.pack.user.add.corp.tag.msg.consumer");
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            // Mock the MafkaClient.buildConsumerFactory method to return the mocked consumer
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(properties, "scrm.pack.user.add.corp.tag.msg")).thenReturn(consumer);
            // Mock the consumer to throw an exception
            doThrow(new Exception()).when(consumer).recvMessageWithParallel(eq(String.class), any());
            // Use reflection to set the consumer field in the consumerUnderTest
            Field consumerField = ScrmPackUserBatchAddCorpTagConsumer.class.getDeclaredField("consumer");
            consumerField.setAccessible(true);
            consumerField.set(consumerUnderTest, consumer);
            // Act
            consumerUnderTest.afterPropertiesSet();
            // Assert
            verify(consumer, times(1)).recvMessageWithParallel(eq(String.class), any());
        }
    }
}
