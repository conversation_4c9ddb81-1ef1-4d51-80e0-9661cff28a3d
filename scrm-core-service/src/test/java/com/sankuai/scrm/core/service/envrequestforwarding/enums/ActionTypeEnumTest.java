package com.sankuai.scrm.core.service.envrequestforwarding.enums;

import com.sankuai.dz.srcm.envrequestforwarding.enums.ActionTypeEnum;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class ActionTypeEnumTest {

    /**
     * 测试 getActionTypeByCode 方法，当 code 为 null 时
     */
    @Test
    public void testGetActionTypeByCodeWhenCodeIsNull() throws Throwable {
        // arrange
        Integer code = null;
        // act
        ActionTypeEnum result = ActionTypeEnum.getActionTypeByCode(code);
        // assert
        assertEquals(ActionTypeEnum.UNKNOWN, result);
    }

    /**
     * 测试 getActionTypeByCode 方法，当 code 对应的枚举类型存在时
     */
    @Test
    public void testGetActionTypeByCodeWhenCodeIsExist() throws Throwable {
        // arrange
        Integer code = 1;
        // act
        ActionTypeEnum result = ActionTypeEnum.getActionTypeByCode(code);
        // assert
        assertEquals(ActionTypeEnum.ONLINE, result);
    }

    /**
     * 测试 getActionTypeByCode 方法，当 code 对应的枚举类型不存在时
     */
    @Test
    public void testGetActionTypeByCodeWhenCodeIsNotExist() throws Throwable {
        // arrange
        Integer code = 9999;
        // act
        ActionTypeEnum result = ActionTypeEnum.getActionTypeByCode(code);
        // assert
        assertEquals(ActionTypeEnum.UNKNOWN, result);
    }
}
