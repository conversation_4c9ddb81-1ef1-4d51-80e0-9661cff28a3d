package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

public class AbstractWxHandlerStopAndLogRequestTest {

    private final AbstractWxHandler handler = Mockito.mock(AbstractWxHandler.class, Mockito.CALLS_REAL_METHODS);

    private MockedStatic<Environment> mockedEnvironment;

    private MockedStatic<Lion> mockedLion;

    @BeforeEach
    public void setUp() {
        mockedEnvironment = Mockito.mockStatic(Environment.class);
        mockedLion = Mockito.mockStatic(Lion.class);
    }

    @AfterEach
    public void tearDown() {
        mockedEnvironment.close();
        mockedLion.close();
    }

    /**
     * Test stopAndLogRequest when the environment is production.
     */
    @Test
    public void testStopAndLogRequest_EnvironmentIsProduction() throws Throwable {
        // arrange
        mockedEnvironment.when(Environment::isProductEnv).thenReturn(true);
        String request = "request";
        String executorId = "executor";
        String appId = "appId";
        // act
        boolean result = handler.stopAndLogRequest(request, executorId, appId);
        // assert
        Assertions.assertFalse(result);
        verify(handler, never()).logRequest(anyString(), anyString());
    }

    /**
     * Test stopAndLogRequest when the executorId is in the whitelist.
     */
    @Test
    public void testStopAndLogRequest_ExecutorIdInWhitelist() throws Throwable {
        // arrange
        mockedEnvironment.when(Environment::isProductEnv).thenReturn(false);
        mockedEnvironment.when(Environment::getAppName).thenReturn("testApp");
        mockedLion.when(() -> Lion.getString("testApp", "automatedManagement.call.wx.service.mock.switch", "WangXueFei")).thenReturn("WangXueFei");
        String request = "request";
        String executorId = "WangXueFei";
        String appId = "appId";
        // act
        boolean result = handler.stopAndLogRequest(request, executorId, appId);
        // assert
        Assertions.assertFalse(result);
        verify(handler, never()).logRequest(anyString(), anyString());
    }

    /**
     * Test stopAndLogRequest when the executorId is not in the whitelist and environment is not production.
     */
    @Test
    public void testStopAndLogRequest_ExecutorIdNotInWhitelist() throws Throwable {
        // arrange
        mockedEnvironment.when(Environment::isProductEnv).thenReturn(false);
        mockedEnvironment.when(Environment::getAppName).thenReturn("testApp");
        mockedLion.when(() -> Lion.getString("testApp", "automatedManagement.call.wx.service.mock.switch", "WangXueFei")).thenReturn("WangXueFei");
        String request = "request";
        String executorId = "executor";
        String appId = "appId";
        doNothing().when(handler).logRequest(request, appId);
        // act
        boolean result = handler.stopAndLogRequest(request, executorId, appId);
        // assert
        Assertions.assertTrue(result);
        verify(handler).logRequest(request, appId);
    }
}
