package com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActionContentDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationBranchExeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationNodeInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.*;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackReadDomainService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class ProcessOrchestrationReadDomainServiceTest {

    @InjectMocks
    private ProcessOrchestrationReadDomainService service;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationNodeInfoDOMapper scrmAmProcessOrchestrationNodeInfoDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationConditionDOMapper scrmAmProcessOrchestrationConditionDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationActionDOMapper scrmAmProcessOrchestrationActionDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationActionContentDOMapper scrmAmProcessOrchestrationActionContentDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationNodeExecuteLogDOMapper scrmAmProcessOrchestrationNodeExecuteLogDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationActionAttachmentDOMapper scrmAmProcessOrchestrationActionAttachmentDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationGoalDOMapper scrmAmProcessOrchestrationGoalDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationGoalDetailDOMapper scrmAmProcessOrchestrationGoalDetailDOMapper;

    @Mock(lenient = true)
    private ScrmAmCrowdPackAndProcessMapDOMapper scrmAmCrowdPackAndProcessMapDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecutorLimitDOMapper scrmAmProcessOrchestrationExecutorLimitDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationBranchExeLogDOMapper scrmAmProcessOrchestrationBranchExeLogDOMapper;

    @Mock(lenient = true)
    private ScrmAMRealtimeSceneAndProcessMapDOMapper realtimeSceneAndProcessMapDOMapper;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationConverter scrmProcessOrchestrationConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationGoalConverter scrmProcessOrchestrationGoalConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationGoalDetailConverter scrmProcessOrchestrationGoalDetailConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationNodeConverter scrmProcessOrchestrationNodeConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationConditionDetailConverter scrmProcessOrchestrationConditionDetailConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationActionConverter scrmProcessOrchestrationActionConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationActionContentConverter scrmProcessOrchestrationActionContentConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationActionAttachmentConverter scrmProcessOrchestrationActionAttachmentConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationExecutorConverter scrmProcessOrchestrationExecutorConverter;

    @Mock(lenient = true)
    private CrowdPackReadDomainService crowdPackReadDomainService;

    public ProcessOrchestrationReadDomainServiceTest() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testGetScrmProcessOrchestrationDTOCompletableFutureWithOutCheck_ExceptionInFuture() throws Throwable {
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setAppId("testApp");
        infoDO.setValidVersion("1.0");
        when(scrmAmCrowdPackAndProcessMapDOMapper.selectByExample(any()))
                .thenThrow(new RuntimeException("Test exception"));
        when(realtimeSceneAndProcessMapDOMapper.selectByExample(any()))
                .thenReturn(new ArrayList<>());
        CompletableFuture<ScrmProcessOrchestrationDTO> future = service
                .getScrmProcessOrchestrationDTOCompletableFutureWithOutCheck(infoDO);
        ScrmProcessOrchestrationDTO result = future.get();
        assertNull(result);
    }

    /**
     * 测试queryStatedBranchExeLogs方法，期望返回非空列表
     */
    @Test
    public void testQueryStatedBranchExeLogs_ReturnsNonEmptyList() {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "1.0";

        ScrmAmProcessOrchestrationBranchExeLogDO logDO = new ScrmAmProcessOrchestrationBranchExeLogDO();
        logDO.setId(1L);
        logDO.setProcessOrchestrationId(processOrchestrationId);
        logDO.setProcessOrchestrationVersion(processOrchestrationVersion);
        logDO.setIsPlaceOrder((byte)1);

        when(scrmAmProcessOrchestrationBranchExeLogDOMapper.selectByExample(any())).thenReturn(Arrays.asList(logDO));

        // act
        List<ScrmAmProcessOrchestrationBranchExeLogDO> result = service.queryStatedBranchExeLogs(processOrchestrationId,
                processOrchestrationVersion);

        // assert
        assertEquals(1, result.size());
        assertEquals(processOrchestrationId, result.get(0).getProcessOrchestrationId());
        assertEquals(processOrchestrationVersion, result.get(0).getProcessOrchestrationVersion());
    }

    /**
     * 测试queryStatedBranchExeLogs方法，期望返回空列表
     */
    @Test
    public void testQueryStatedBranchExeLogs_ReturnsEmptyList() {
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "1.0";

        ScrmAmProcessOrchestrationBranchExeLogDO logDO = new ScrmAmProcessOrchestrationBranchExeLogDO();
        logDO.setId(1L);
        logDO.setProcessOrchestrationId(processOrchestrationId);
        logDO.setProcessOrchestrationVersion(processOrchestrationVersion);
        logDO.setIsPlaceOrder((byte)1);

        // arrange
        when(scrmAmProcessOrchestrationBranchExeLogDOMapper.selectByExample(any())).thenReturn(Arrays.asList());

        // act
        List<ScrmAmProcessOrchestrationBranchExeLogDO> result = service
                .queryStatedBranchExeLogs(processOrchestrationId, processOrchestrationVersion);

        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试queryStatedBranchExeLogs方法，当processOrchestrationId为null时
     */
    @Test(expected = RuntimeException.class)
    public void testQueryStatedBranchExeLogs_WithNullProcessOrchestrationId() {
        // arrange
        String processOrchestrationVersion = "1.0";

        Long invalidProcessOrchestrationId = null;

        // act
        service.queryStatedBranchExeLogs(invalidProcessOrchestrationId, processOrchestrationVersion);

        // assert
        // IllegalArgumentException is expected
    }

    /**
     * 测试queryStatedBranchExeLogs方法，当processOrchestrationVersion为null时
     */
    @Test(expected = RuntimeException.class)
    public void testQueryStatedBranchExeLogs_WithNullProcessOrchestrationVersion() {
        // arrange
        Long processOrchestrationId = 1L;
        String invalidProcessOrchestrationVersion = null;

        // act
        service.queryStatedBranchExeLogs(processOrchestrationId, invalidProcessOrchestrationVersion);

        // assert
        // IllegalArgumentException is expected
    }

    /**
     * 测试queryStatedBranchExeLogs方法，当processOrchestrationVersion为空字符串时
     */
    @Test
    public void testQueryStatedBranchExeLogs_WithEmptyProcessOrchestrationVersion() {
        // arrange
        Long processOrchestrationId = 1L;
        String invalidProcessOrchestrationVersion = "";

        // act
        List<ScrmAmProcessOrchestrationBranchExeLogDO> result = service.queryStatedBranchExeLogs(processOrchestrationId,
                invalidProcessOrchestrationVersion);

        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试queryStatedBranchExeLogs方法，当数据库操作异常时
     */
    @Test(expected = RuntimeException.class)
    public void testQueryStatedBranchExeLogs_WhenDatabaseOperationFails() {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "1.0";

        ScrmAmProcessOrchestrationBranchExeLogDO logDO = new ScrmAmProcessOrchestrationBranchExeLogDO();
        logDO.setId(1L);
        logDO.setProcessOrchestrationId(processOrchestrationId);
        logDO.setProcessOrchestrationVersion(processOrchestrationVersion);
        logDO.setIsPlaceOrder((byte)1);
        when(scrmAmProcessOrchestrationBranchExeLogDOMapper.selectByExample(any()))
                .thenThrow(new RuntimeException("Database operation failed"));

        // act
        service.queryStatedBranchExeLogs(processOrchestrationId, processOrchestrationVersion);

        // assert
        // RuntimeException is expected
    }

    @Test
    public void queryNodesStatisticsFuture() {
        List<ScrmAmProcessOrchestrationNodeInfoDO> nodeInfoDOS = new ArrayList<>();
        nodeInfoDOS.add(new ScrmAmProcessOrchestrationNodeInfoDO());
        when(scrmAmProcessOrchestrationNodeInfoDOMapper.selectByExample(any())).thenReturn(nodeInfoDOS);
        List<ScrmAmProcessOrchestrationActionContentDO> actionContentDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationActionContentDO contentDO = new ScrmAmProcessOrchestrationActionContentDO();
        contentDO.setActionId(1);
        contentDO.setProcessOrchestrationNodeId(1L);
        actionContentDOS.add(contentDO);
        when(scrmAmProcessOrchestrationActionContentDOMapper.selectByExample(any())).thenReturn(actionContentDOS);
        when(scrmAmProcessOrchestrationActionDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(scrmAmProcessOrchestrationConditionDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(scrmAmProcessOrchestrationNodeExecuteLogDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(scrmAmProcessOrchestrationBranchExeLogDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setValidVersion("1.0");
        CompletableFuture<ScrmProcessOrchestrationNodeMediumDTO> futures = service.queryNodesStatisticsFuture(infoDO);
        assertNotNull(futures);
    }
}
