package com.sankuai.scrm.core.service.external.contact.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.inf.kms.pangolin.api.service.IEncryptService;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ScrmMobileAddFriendTaskDO;
import com.sankuai.scrm.core.service.external.contact.dal.example.ScrmMobileAddFriendTaskDOExample;
import com.sankuai.scrm.core.service.external.contact.dal.mapper.ext.ExtScrmMobileAddFriendTaskDOMapper;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.MobileAddFriendRealTimeEntity;
import java.security.GeneralSecurityException;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MobileAddFriendRealTimeDomainServiceGetRpcParamsByIdTest {

    @Mock
    private ExtScrmMobileAddFriendTaskDOMapper mobileAddFriendTaskDOMapper;

    @Mock
    private IEncryptService phoneEncryptService;

    @InjectMocks
    private MobileAddFriendRealTimeDomainService mobileAddFriendRealTimeDomainService;

    /**
     * Test case for successful conversion of task to entity
     */
    @Test
    public void testGetRpcParamsByIdSuccess() throws Throwable {
        // arrange
        Long id = 123L;
        ScrmMobileAddFriendTaskDO task = new ScrmMobileAddFriendTaskDO();
        task.setId(id);
        task.setAddNumber("encryptedNumber");
        task.setNumberType(1);
        task.setAppId("testApp");
        task.setWelcomeContent("welcome");
        task.setAccountId("account123");
        List<ScrmMobileAddFriendTaskDO> taskList = Collections.singletonList(task);
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(taskList);
        when(phoneEncryptService.decryptUTF8String("encryptedNumber")).thenReturn("decryptedNumber");
        // act
        MobileAddFriendRealTimeEntity result = mobileAddFriendRealTimeDomainService.getRpcParamsById(id);
        // assert
        assertNotNull(result);
        assertEquals("decryptedNumber", result.getAddNumber());
        assertEquals(1, result.getNumberType());
        assertEquals("testApp", result.getAppId());
        assertEquals("welcome", result.getWelcomeContent());
        assertEquals("account123", result.getAccountId());
    }

    /**
     * Test case when task is not found
     */
    @Test
    public void testGetRpcParamsByIdTaskNotFound() throws Throwable {
        // arrange
        Long id = 123L;
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(Collections.emptyList());
        // act
        MobileAddFriendRealTimeEntity result = mobileAddFriendRealTimeDomainService.getRpcParamsById(id);
        // assert
        assertNull(result);
    }

    /**
     * Test case when decryption fails (handled in queryMobileAddFriendRealTimeTaskById)
     */
    @Test
    public void testGetRpcParamsByIdDecryptionFailure() throws Throwable {
        // arrange
        Long id = 123L;
        ScrmMobileAddFriendTaskDO task = new ScrmMobileAddFriendTaskDO();
        task.setId(id);
        task.setAddNumber("encryptedNumber");
        List<ScrmMobileAddFriendTaskDO> taskList = Collections.singletonList(task);
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(taskList);
        when(phoneEncryptService.decryptUTF8String("encryptedNumber")).thenThrow(new GeneralSecurityException("Decryption failed"));
        // act
        MobileAddFriendRealTimeEntity result = mobileAddFriendRealTimeDomainService.getRpcParamsById(id);
        // assert
        assertNull(result);
    }

    /**
     * Test case with null input id - should throw exception
     */
    @Test
    public void testGetRpcParamsByIdNullInput() throws Throwable {
        // arrange
        Long id = null;
        // act & assert
        assertThrows(RuntimeException.class, () -> mobileAddFriendRealTimeDomainService.getRpcParamsById(id));
    }

    /**
     * Test case with very large id value
     */
    @Test
    public void testGetRpcParamsByIdLargeId() throws Throwable {
        // arrange
        Long id = Long.MAX_VALUE;
        ScrmMobileAddFriendTaskDO task = new ScrmMobileAddFriendTaskDO();
        task.setId(id);
        task.setAddNumber("encryptedNumber");
        task.setNumberType(1);
        task.setAppId("testApp");
        task.setWelcomeContent("welcome");
        task.setAccountId("account123");
        List<ScrmMobileAddFriendTaskDO> taskList = Collections.singletonList(task);
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(taskList);
        when(phoneEncryptService.decryptUTF8String("encryptedNumber")).thenReturn("decryptedNumber");
        // act
        MobileAddFriendRealTimeEntity result = mobileAddFriendRealTimeDomainService.getRpcParamsById(id);
        // assert
        assertNotNull(result);
        assertEquals("decryptedNumber", result.getAddNumber());
        assertEquals(1, result.getNumberType());
        assertEquals("testApp", result.getAppId());
        assertEquals("welcome", result.getWelcomeContent());
        assertEquals("account123", result.getAccountId());
    }

    /**
     * Test case with negative id value
     */
    @Test
    public void testGetRpcParamsByIdNegativeId() throws Throwable {
        // arrange
        Long id = -1L;
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(Collections.emptyList());
        // act
        MobileAddFriendRealTimeEntity result = mobileAddFriendRealTimeDomainService.getRpcParamsById(id);
        // assert
        assertNull(result);
    }
}
