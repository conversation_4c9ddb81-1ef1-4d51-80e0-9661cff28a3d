package com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool;

import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductInfoDTO;
import com.sankuai.dztheme.deal.DealProductService;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.generalproduct.GeneralProductService;
import com.sankuai.dztheme.generalproduct.res.GeneralProductDTO;
import com.sankuai.dztheme.generalproduct.res.GeneralProductResult;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ProductManagementService_QueryProductInfoDTOS_Test {
    @InjectMocks
    private ProductManagementService productManagementService;
    @Mock
    private DealProductService dealProductService;
    @Mock
    private GeneralProductService generalProductService;
    @Mock
    private Future<DealProductResult> dealFuture;
    @Mock
    private Future<GeneralProductResult> generalFuture;
    private MockedStatic<FutureFactory> futureFactoryMockedStatic;

    @BeforeEach
    void setUp() {
        futureFactoryMockedStatic = Mockito.mockStatic(FutureFactory.class);
    }
    @AfterEach
    void tearDown() {
        futureFactoryMockedStatic.close();
    }
    private DealProductDTO createDealProductDTO(int id, String name, String headPic) {
        DealProductDTO dto = new DealProductDTO();
        dto.setProductId(id);
        dto.setName(name);
        dto.setHeadPic(headPic);
        dto.setBasePrice(BigDecimal.valueOf(100));
        return dto;
    }
    private GeneralProductDTO createGeneralProductDTO(int id, String name, String headPic) {
        GeneralProductDTO dto = new GeneralProductDTO();
        dto.setProductId(id);
        dto.setName(name);
        dto.setHeadPic(headPic);
        dto.setOriginalSalePrice(BigDecimal.valueOf(200));
        return dto;
    }
    @Test
    void testQueryProductInfoDTOS_emptyProductIds() {
        List<ProductInfoDTO> result = productManagementService.queryProductInfoDTOS(Collections.emptyList());
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
    @Test
    void testQueryProductInfoDTOS_onlyDealProducts() throws Exception {
        List<Long> productIds = Arrays.asList(1L, 2L);
        List<DealProductDTO> dealProducts = Arrays.asList(
                createDealProductDTO(1, "团购商品1", "http://example.com/1.jpg"),
                createDealProductDTO(2, "团购商品2", "http://example.com/2.jpg")
        );
        DealProductResult dealProductResult = new DealProductResult();
        dealProductResult.setDeals(dealProducts);
        when(dealFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(dealProductResult);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(DealProductResult.class)).thenReturn(dealFuture);
        GeneralProductResult emptyGeneralResult = new GeneralProductResult();
        emptyGeneralResult.setProducts(new ArrayList<>());
        when(generalFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(emptyGeneralResult);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(generalFuture);
        List<ProductInfoDTO> result = productManagementService.queryProductInfoDTOS(productIds);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("团购商品1", result.get(0).getProductTitle());
        assertEquals(1, result.get(0).getProductType());
        assertEquals(1L, result.get(0).getProductId());
        assertEquals("团购商品2", result.get(1).getProductTitle());
        assertEquals(1, result.get(1).getProductType());
        assertEquals(2L, result.get(1).getProductId());
    }
    @Test
    void testQueryProductInfoDTOS_onlyGeneralProducts() throws Exception {
        List<Long> productIds = Arrays.asList(1L, 2L);
        List<GeneralProductDTO> generalProducts = Arrays.asList(
                createGeneralProductDTO(1, "泛商品1", "http://example.com/1.jpg"),
                createGeneralProductDTO(2, "泛商品2", "http://example.com/2.jpg")
        );
        GeneralProductResult generalProductResult = new GeneralProductResult();
        generalProductResult.setProducts(generalProducts);

        DealProductResult emptyDealResult = new DealProductResult();
        emptyDealResult.setDeals(new ArrayList<>()); // 确保deals列表不是null

        when(dealFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(emptyDealResult);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(DealProductResult.class)).thenReturn(dealFuture);
        when(generalFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(generalProductResult);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(generalFuture);

        List<ProductInfoDTO> result = productManagementService.queryProductInfoDTOS(productIds);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("泛商品1", result.get(0).getProductTitle());
        assertEquals(2, result.get(0).getProductType());
        assertEquals(1L, result.get(0).getProductId());
        assertEquals("泛商品2", result.get(1).getProductTitle());
        assertEquals(2, result.get(1).getProductType());
        assertEquals(2L, result.get(1).getProductId());
    }
    @Test
    void testQueryProductInfoDTOS_mixedProducts_dealPriorityForSameId() throws Exception {
        List<Long> productIds = Arrays.asList(1L, 2L, 3L);
        List<DealProductDTO> dealProducts = Arrays.asList(
                createDealProductDTO(1, "团购商品1", "http://example.com/1.jpg"),
                createDealProductDTO(2, "团购商品2", "http://example.com/2.jpg")
        );
        List<GeneralProductDTO> generalProducts = Arrays.asList(
                createGeneralProductDTO(2, "泛商品2-应被覆盖", "http://example.com/2.jpg"), // ID与团购商品冲突
                createGeneralProductDTO(3, "泛商品3", "http://example.com/3.jpg")
        );

        DealProductResult dealProductResult = new DealProductResult();
        dealProductResult.setDeals(dealProducts);

        GeneralProductResult generalProductResult = new GeneralProductResult();
        generalProductResult.setProducts(generalProducts);

        when(dealFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(dealProductResult);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(DealProductResult.class)).thenReturn(dealFuture);
        when(generalFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(generalProductResult);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(generalFuture);

        List<ProductInfoDTO> result = productManagementService.queryProductInfoDTOS(productIds);
        assertNotNull(result);
        assertEquals(3, result.size());
        // 验证顺序和内容，团购优先
        assertEquals("团购商品1", result.get(0).getProductTitle());
        assertEquals(1, result.get(0).getProductType());
        assertEquals(1L, result.get(0).getProductId());

        assertEquals("团购商品2", result.get(1).getProductTitle());
        assertEquals(1, result.get(1).getProductType());
        assertEquals(2L, result.get(1).getProductId());

        assertEquals("泛商品3", result.get(2).getProductTitle());
        assertEquals(2, result.get(2).getProductType());
        assertEquals(3L, result.get(2).getProductId());
    }
    @Test
    void testQueryProductInfoDTOS_dealServiceReturnsNull() throws Exception {
        List<Long> productIds = Arrays.asList(1L);
        GeneralProductDTO generalProduct = createGeneralProductDTO(1, "泛商品1", "http://example.com/1.jpg");
        GeneralProductResult generalProductResult = new GeneralProductResult();
        generalProductResult.setProducts(Collections.singletonList(generalProduct));
        
        when(dealFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(null);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(DealProductResult.class)).thenReturn(dealFuture);
        when(generalFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(generalProductResult);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(generalFuture);
        
        List<ProductInfoDTO> result = productManagementService.queryProductInfoDTOS(productIds);
        
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("泛商品1", result.get(0).getProductTitle());
        assertEquals(2, result.get(0).getProductType());
        assertEquals(1L, result.get(0).getProductId());
    }
    @Test
    void testQueryProductInfoDTOS_productWithEmptyTitle_isSkipped() throws Exception {
        List<Long> productIds = Arrays.asList(1L, 2L);
        List<DealProductDTO> dealProducts = Arrays.asList(
                createDealProductDTO(1, "", "http://example.com/1.jpg"),
                createDealProductDTO(2, "团购商品2", "http://example.com/2.jpg")
        );
        DealProductResult dealProductResult = new DealProductResult();
        dealProductResult.setDeals(dealProducts);
        when(dealFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(dealProductResult);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(DealProductResult.class)).thenReturn(dealFuture);
        GeneralProductResult emptyGeneralResult = new GeneralProductResult();
        emptyGeneralResult.setProducts(new ArrayList<>());
        when(generalFuture.get(anyLong(), any(TimeUnit.class))).thenReturn(emptyGeneralResult);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(generalFuture);
        List<ProductInfoDTO> result = productManagementService.queryProductInfoDTOS(productIds);
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("团购商品2", result.get(0).getProductTitle());
        assertEquals(1, result.get(0).getProductType());
        assertEquals(2L, result.get(0).getProductId());
    }
} 