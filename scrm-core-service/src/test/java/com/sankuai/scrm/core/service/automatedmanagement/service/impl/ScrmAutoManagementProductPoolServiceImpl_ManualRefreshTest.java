package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductInfoDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmRefreshRateLimitDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmRefreshRateLimitDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.ManualRefreshProductInfoProducer;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmAutoManagementProductPoolServiceImpl_ManualRefreshTest {

    @InjectMocks
    private ScrmAutoManagementProductPoolServiceImpl scrmAutoManagementProductPoolService;

    @Mock(lenient = true)
    private ProductManagementService productManagementService;

    @Mock(lenient = true)
    private ExtScrmAmRefreshRateLimitDOMapper extRateLimitDOMapper;

    @Mock(lenient = true)
    private ManualRefreshProductInfoProducer manualRefreshProductInfoProducer;

    @Before
    public void setUp() {
        when(extRateLimitDOMapper.countByExampleForUpdate(any(ScrmAmRefreshRateLimitDOExample.class))).thenReturn(0L);
    }

    @Test
    public void testManualRefreshAppIdIsNull() {
        String appId = null;
        RemoteResponse response = scrmAutoManagementProductPoolService.manualRefresh(appId);
        assertEquals("请求参数错误", response.getMsg());
    }

    @Test
    public void testManualRefreshAlreadyRefreshed() {
        String appId = "testAppId";
        when(extRateLimitDOMapper.countByExampleForUpdate(any(ScrmAmRefreshRateLimitDOExample.class))).thenReturn(1L);
        RemoteResponse response = scrmAutoManagementProductPoolService.manualRefresh(appId);
        assertEquals("提示：数据每半小时内只能同步一次", response.getMsg());
    }

    @Test
    public void testManualRefreshSuccess() throws Exception {
        String appId = "testAppId";
        doNothing().when(manualRefreshProductInfoProducer).sendMessage(appId);
        RemoteResponse response = scrmAutoManagementProductPoolService.manualRefresh(appId);
        assertEquals("success", response.getMsg());
    }

    @Test
    public void testManualRefreshFail() throws Exception {
        String appId = "testAppId";
        doThrow(new RuntimeException()).when(manualRefreshProductInfoProducer).sendMessage(appId);
        RemoteResponse response = scrmAutoManagementProductPoolService.manualRefresh(appId);
        assertEquals("手动刷新失败", response.getMsg());
    }

    /**
     * 测试queryProductById方法，当appId为空时
     */
    @Test
    public void testQueryProductByIdAppIdIsEmpty() {
        // arrange
        Long productId = 1L;
        String appId = "";

        // act
        RemoteResponse<ProductInfoDTO> result = scrmAutoManagementProductPoolService.queryProductById(productId, appId);

        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("请求参数错误", result.getMsg());
    }

    /**
     * 测试queryProductById方法，当productId为null时
     */
    @Test
    public void testQueryProductByIdProductIdIsNull() {
        // arrange
        Long productId = null;
        String appId = "testAppId";

        // act
        RemoteResponse<ProductInfoDTO> result = scrmAutoManagementProductPoolService.queryProductById(productId, appId);

        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("请求参数错误", result.getMsg());
    }

    /**
     * 测试queryProductById方法，当productId小于等于0时
     */
    @Test
    public void testQueryProductByIdProductIdIsInvalid() {
        // arrange
        Long productId = 0L;
        String appId = "testAppId";

        // act
        RemoteResponse<ProductInfoDTO> result = scrmAutoManagementProductPoolService.queryProductById(productId, appId);

        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("请求参数错误", result.getMsg());
    }

    /**
     * 测试queryProductById方法，当未查询到商品时
     */
    @Test
    public void testQueryProductByIdProductNotFound() {
        // arrange
        Long productId = 1L;
        String appId = "testAppId";
        when(productManagementService.queryProductById(productId, appId)).thenReturn(null);

        // act
        RemoteResponse<ProductInfoDTO> result = scrmAutoManagementProductPoolService.queryProductById(productId, appId);

        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
        assertEquals("未查询到商品", result.getMsg());
    }

    /**
     * 测试queryProductById方法，正常情况
     */
    @Test
    public void testQueryProductByIdSuccess() {
        // arrange
        Long productId = 1L;
        String appId = "testAppId";
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        when(productManagementService.queryProductById(productId, appId)).thenReturn(productInfoDTO);

        // act
        RemoteResponse<ProductInfoDTO> result = scrmAutoManagementProductPoolService.queryProductById(productId, appId);

        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(productInfoDTO, result.getData());
    }
}
