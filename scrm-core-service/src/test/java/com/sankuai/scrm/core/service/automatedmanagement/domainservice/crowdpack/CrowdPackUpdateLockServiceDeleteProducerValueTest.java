package com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CrowdPackUpdateLockServiceDeleteProducerValueTest {

    @Mock
    private RedisStoreClient redisClient;

    @InjectMocks
    private CrowdPackUpdateLockService crowdPackUpdateLockService;

    // 直接使用与实现类相同的常量值
    private static final String SCRM_CROWD_PACK_UPDATE_LOCK_CATEGORY = "crowd_pack_update_category";

    private static final String PRODUCER_NUM_LOCK = "producer";

    /**
     * 测试正常删除生产者值成功的情况
     */
    @Test
    public void testDeleteProducerValueSuccess() throws Throwable {
        // arrange
        Long packId = 12345L;
        StoreKey expectedStoreKey = new StoreKey(SCRM_CROWD_PACK_UPDATE_LOCK_CATEGORY, packId, PRODUCER_NUM_LOCK);
        when(redisClient.delete(expectedStoreKey)).thenReturn(true);
        // act
        boolean result = crowdPackUpdateLockService.deleteProducerValue(packId);
        // assert
        assertTrue(result);
        verify(redisClient).delete(expectedStoreKey);
    }

    /**
     * 测试删除生产者值失败的情况
     */
    @Test
    public void testDeleteProducerValueFailure() throws Throwable {
        // arrange
        Long packId = 12345L;
        StoreKey expectedStoreKey = new StoreKey(SCRM_CROWD_PACK_UPDATE_LOCK_CATEGORY, packId, PRODUCER_NUM_LOCK);
        when(redisClient.delete(expectedStoreKey)).thenReturn(false);
        // act
        boolean result = crowdPackUpdateLockService.deleteProducerValue(packId);
        // assert
        assertFalse(result);
        verify(redisClient).delete(expectedStoreKey);
    }

    /**
     * 测试传入null packId的情况
     */
    @Test
    public void testDeleteProducerValueWithNullPackId() throws Throwable {
        // arrange
        Long packId = null;
        // 明确指定要模拟的delete方法版本
        doThrow(new NullPointerException()).when(redisClient).delete(any(StoreKey.class));
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            crowdPackUpdateLockService.deleteProducerValue(packId);
        });
    }

    /**
     * 测试Redis操作抛出异常的情况
     */
    @Test
    public void testDeleteProducerValueWithRedisException() throws Throwable {
        // arrange
        Long packId = 12345L;
        StoreKey expectedStoreKey = new StoreKey(SCRM_CROWD_PACK_UPDATE_LOCK_CATEGORY, packId, PRODUCER_NUM_LOCK);
        when(redisClient.delete(expectedStoreKey)).thenThrow(new RuntimeException("Redis error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            crowdPackUpdateLockService.deleteProducerValue(packId);
        });
        verify(redisClient).delete(expectedStoreKey);
    }
}
