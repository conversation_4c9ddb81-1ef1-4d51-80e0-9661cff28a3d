package com.sankuai.scrm.core.service.automatedmanagement.mq.producer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.dz.srcm.automatedmanagement.dto.mq.RefinementOperationExecuteMessage;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Field;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class UpdateAllCrowdPackMessageProducerSendCrowdPackUpdateDBTaskExecuteMessageTest {

    private IProducerProcessor producerMock;

    private MockedStatic<JsonUtils> jsonUtilsMock;

    @InjectMocks
    private UpdateAllCrowdPackMessageProducer producer;

    @BeforeEach
    void setUp() throws Exception {
        // 创建模拟对象
        producerMock = mock(IProducerProcessor.class);
        // 使用反射设置静态字段
        Field producerField = UpdateAllCrowdPackMessageProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, producerMock);
        // 模拟JsonUtils.toStr方法
        jsonUtilsMock = mockStatic(JsonUtils.class);
        jsonUtilsMock.when(() -> JsonUtils.toStr(any())).thenReturn("mocked-json-string");
    }

    @AfterEach
    void tearDown() throws Exception {
        // 关闭静态模拟
        jsonUtilsMock.close();
        // 清除静态字段
        Field producerField = UpdateAllCrowdPackMessageProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, null);
    }

    /**
     * 测试正常场景 - 所有参数有效
     */
    @Test
    public void testSendCrowdPackUpdateDBTaskExecuteMessage_NormalCase() throws Throwable {
        // arrange
        String appId = "testApp";
        Set<Long> mtUserIds = new HashSet<>(Collections.singletonList(123L));
        Long packId = 456L;
        String crowdPackVersion = "v1.0";
        ProducerResult mockResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(producerMock.sendMessage(anyString())).thenReturn(mockResult);
        // act
        producer.sendCrowdPackUpdateDBTaskExecuteMessage(appId, mtUserIds, packId, crowdPackVersion);
        // assert
        verify(producerMock, times(1)).sendMessage(anyString());
        // 验证消息内容
        ArgumentCaptor<RefinementOperationExecuteMessage> messageCaptor = ArgumentCaptor.forClass(RefinementOperationExecuteMessage.class);
        jsonUtilsMock.verify(() -> JsonUtils.toStr(messageCaptor.capture()));
        RefinementOperationExecuteMessage capturedMessage = messageCaptor.getValue();
        assertEquals(ScrmProcessOrchestrationTaskTypeEnum.PERSONA_CROWD_PACK_UPDATE_DB.getValue(), capturedMessage.getTaskType());
        assertEquals(appId, capturedMessage.getAppId());
        assertEquals(packId, capturedMessage.getCrowdPackId());
        assertEquals(crowdPackVersion, capturedMessage.getCrowdPackVersion());
        assertEquals(mtUserIds, capturedMessage.getMtUserIds());
    }

    /**
     * 测试边界场景 - mtUserIds为空集合
     */
    @Test
    public void testSendCrowdPackUpdateDBTaskExecuteMessage_EmptyMtUserIds() throws Throwable {
        // arrange
        String appId = "testApp";
        Set<Long> mtUserIds = Collections.emptySet();
        Long packId = 456L;
        String crowdPackVersion = "v1.0";
        ProducerResult mockResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(producerMock.sendMessage(anyString())).thenReturn(mockResult);
        // act
        producer.sendCrowdPackUpdateDBTaskExecuteMessage(appId, mtUserIds, packId, crowdPackVersion);
        // assert
        verify(producerMock, times(1)).sendMessage(anyString());
        // 验证消息内容
        ArgumentCaptor<RefinementOperationExecuteMessage> messageCaptor = ArgumentCaptor.forClass(RefinementOperationExecuteMessage.class);
        jsonUtilsMock.verify(() -> JsonUtils.toStr(messageCaptor.capture()));
        RefinementOperationExecuteMessage capturedMessage = messageCaptor.getValue();
        assertEquals(mtUserIds, capturedMessage.getMtUserIds());
    }

    /**
     * 测试边界场景 - mtUserIds为null
     */
    @Test
    public void testSendCrowdPackUpdateDBTaskExecuteMessage_NullMtUserIds() throws Throwable {
        // arrange
        String appId = "testApp";
        Long packId = 456L;
        String crowdPackVersion = "v1.0";
        ProducerResult mockResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(producerMock.sendMessage(anyString())).thenReturn(mockResult);
        // act
        producer.sendCrowdPackUpdateDBTaskExecuteMessage(appId, null, packId, crowdPackVersion);
        // assert
        verify(producerMock, times(1)).sendMessage(anyString());
        // 验证消息内容
        ArgumentCaptor<RefinementOperationExecuteMessage> messageCaptor = ArgumentCaptor.forClass(RefinementOperationExecuteMessage.class);
        jsonUtilsMock.verify(() -> JsonUtils.toStr(messageCaptor.capture()));
        RefinementOperationExecuteMessage capturedMessage = messageCaptor.getValue();
        assertNull(capturedMessage.getMtUserIds());
    }

    /**
     * 测试异常场景 - packId为null
     */
    @Test
    public void testSendCrowdPackUpdateDBTaskExecuteMessage_NullPackId() throws Throwable {
        // arrange
        String appId = "testApp";
        Set<Long> mtUserIds = new HashSet<>(Collections.singletonList(123L));
        String crowdPackVersion = "v1.0";
        ProducerResult mockResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(producerMock.sendMessage(anyString())).thenReturn(mockResult);
        // act
        producer.sendCrowdPackUpdateDBTaskExecuteMessage(appId, mtUserIds, null, crowdPackVersion);
        // assert
        verify(producerMock, times(1)).sendMessage(anyString());
        // 验证消息内容
        ArgumentCaptor<RefinementOperationExecuteMessage> messageCaptor = ArgumentCaptor.forClass(RefinementOperationExecuteMessage.class);
        jsonUtilsMock.verify(() -> JsonUtils.toStr(messageCaptor.capture()));
        RefinementOperationExecuteMessage capturedMessage = messageCaptor.getValue();
        assertNull(capturedMessage.getCrowdPackId());
    }

    /**
     * 测试异常场景 - crowdPackVersion为空字符串
     */
    @Test
    public void testSendCrowdPackUpdateDBTaskExecuteMessage_EmptyCrowdPackVersion() throws Throwable {
        // arrange
        String appId = "testApp";
        Set<Long> mtUserIds = new HashSet<>(Collections.singletonList(123L));
        Long packId = 456L;
        String crowdPackVersion = "";
        ProducerResult mockResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(producerMock.sendMessage(anyString())).thenReturn(mockResult);
        // act
        producer.sendCrowdPackUpdateDBTaskExecuteMessage(appId, mtUserIds, packId, crowdPackVersion);
        // assert
        verify(producerMock, times(1)).sendMessage(anyString());
        // 验证消息内容
        ArgumentCaptor<RefinementOperationExecuteMessage> messageCaptor = ArgumentCaptor.forClass(RefinementOperationExecuteMessage.class);
        jsonUtilsMock.verify(() -> JsonUtils.toStr(messageCaptor.capture()));
        RefinementOperationExecuteMessage capturedMessage = messageCaptor.getValue();
        assertEquals("", capturedMessage.getCrowdPackVersion());
    }

    /**
     * 测试异常场景 - appId为null
     */
    @Test
    public void testSendCrowdPackUpdateDBTaskExecuteMessage_NullAppId() throws Throwable {
        // arrange
        Set<Long> mtUserIds = new HashSet<>(Collections.singletonList(123L));
        Long packId = 456L;
        String crowdPackVersion = "v1.0";
        ProducerResult mockResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(producerMock.sendMessage(anyString())).thenReturn(mockResult);
        // act
        producer.sendCrowdPackUpdateDBTaskExecuteMessage(null, mtUserIds, packId, crowdPackVersion);
        // assert
        verify(producerMock, times(1)).sendMessage(anyString());
        // 验证消息内容
        ArgumentCaptor<RefinementOperationExecuteMessage> messageCaptor = ArgumentCaptor.forClass(RefinementOperationExecuteMessage.class);
        jsonUtilsMock.verify(() -> JsonUtils.toStr(messageCaptor.capture()));
        RefinementOperationExecuteMessage capturedMessage = messageCaptor.getValue();
        assertNull(capturedMessage.getAppId());
    }

    /**
     * 测试消息发送失败重试逻辑
     */
    @Test
    public void testSendCrowdPackUpdateDBTaskExecuteMessage_RetryOnFailure() throws Throwable {
        // arrange
        String appId = "testApp";
        Set<Long> mtUserIds = new HashSet<>(Collections.singletonList(123L));
        Long packId = 456L;
        String crowdPackVersion = "v1.0";
        ProducerResult mockFailureResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        ProducerResult mockSuccessResult = new ProducerResult(ProducerStatus.SEND_OK);
        // 第一次失败，第二次成功
        when(producerMock.sendMessage(anyString())).thenReturn(mockFailureResult).thenReturn(mockSuccessResult);
        // act
        producer.sendCrowdPackUpdateDBTaskExecuteMessage(appId, mtUserIds, packId, crowdPackVersion);
        // assert
        verify(producerMock, times(2)).sendMessage(anyString());
    }

    /**
     * 测试消息发送失败后重试3次仍然失败的情况
     */
    @Test
    public void testSendCrowdPackUpdateDBTaskExecuteMessage_RetryFailure() throws Throwable {
        // arrange
        String appId = "testApp";
        Set<Long> mtUserIds = new HashSet<>(Collections.singletonList(123L));
        Long packId = 456L;
        String crowdPackVersion = "v1.0";
        ProducerResult mockFailureResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        // 所有尝试都失败
        when(producerMock.sendMessage(anyString())).thenReturn(mockFailureResult);
        // act
        producer.sendCrowdPackUpdateDBTaskExecuteMessage(appId, mtUserIds, packId, crowdPackVersion);
        // assert
        verify(producerMock, times(3)).sendMessage(anyString());
    }

    /**
     * 测试发送消息时抛出异常的情况
     */
    @Test
    public void testSendCrowdPackUpdateDBTaskExecuteMessage_Exception() throws Throwable {
        // arrange
        String appId = "testApp";
        Set<Long> mtUserIds = new HashSet<>(Collections.singletonList(123L));
        Long packId = 456L;
        String crowdPackVersion = "v1.0";
        // 模拟抛出异常
        when(producerMock.sendMessage(anyString())).thenThrow(new RuntimeException("Test exception"));
        // act - 不应该抛出异常
        producer.sendCrowdPackUpdateDBTaskExecuteMessage(appId, mtUserIds, packId, crowdPackVersion);
        // assert - 应该尝试3次
        verify(producerMock, times(3)).sendMessage(anyString());
    }
}
