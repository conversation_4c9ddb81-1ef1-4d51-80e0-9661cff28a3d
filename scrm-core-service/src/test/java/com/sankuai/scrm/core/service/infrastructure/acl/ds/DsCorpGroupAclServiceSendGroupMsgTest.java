package com.sankuai.scrm.core.service.infrastructure.acl.ds;

import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.SendGroupMsgTRequest;
import com.sankuai.service.fe.corp.ds.TResponse.BaseTResponse;
import com.sankuai.service.fe.corp.ds.TResponse.openapi.msg.SendGroupMsgTResponse;
import com.sankuai.service.fe.corp.ds.tservice.openapi.msg.GroupMsgTService;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DsCorpGroupAclServiceSendGroupMsgTest {

    @InjectMocks
    private DsCorpGroupAclService dsCorpGroupAclService;

    @Mock
    private GroupMsgTService groupMsgTService;

    private SendGroupMsgTRequest request;

    private BaseTResponse<SendGroupMsgTResponse> response;

    @Before
    public void setUp() {
        request = new SendGroupMsgTRequest();
        response = BaseTResponse.buildSuccess();
    }

    /**
     * 测试sendGroupMsg方法，当request为null时，应返回null
     */
    @Test
    public void testSendGroupMsgRequestIsNull() throws Throwable {
        assertNull(dsCorpGroupAclService.sendGroupMsg(null));
    }

    /**
     * 测试sendGroupMsg方法，当request不为null且groupMsgTService.sendGroupMsg(request)正常返回时，应返回response
     */
    @Test
    public void testSendGroupMsgRequestIsNotNullAndServiceReturnsNormally() throws Throwable {
        when(groupMsgTService.sendGroupMsg(request)).thenReturn(response);
        assertSame(response, dsCorpGroupAclService.sendGroupMsg(request));
    }

    /**
     * 测试sendGroupMsg方法，当request不为null且groupMsgTService.sendGroupMsg(request)抛出异常时，应捕获异常并返回null
     */
    @Test
    public void testSendGroupMsgRequestIsNotNullAndServiceThrowsException() throws Throwable {
        when(groupMsgTService.sendGroupMsg(request)).thenThrow(TException.class);
        assertNull(dsCorpGroupAclService.sendGroupMsg(request));
    }
}
