package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.fundamental.light.remote.ResponseEnum;
import com.sankuai.dz.srcm.automatedmanagement.request.CrowdPackUserAddCorpTagRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.CrowdPackUserAddCorpTagResult;
import com.sankuai.scrm.core.service.infrastructure.acl.sso.SsosvOpenApi;
import com.sankuai.scrm.core.service.infrastructure.acl.sso.dto.SsoUserInfoDTO;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.CrowdPackInnerService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

public class ScrmAutoManagementServiceImplCrowdPackUserBatchAddCorpTagTest {

    @InjectMocks
    private ScrmAutoManagementServiceImpl scrmAutoManagementService;

    @Mock
    private CrowdPackInnerService crowdPackInnerService;

    @Mock
    private SsosvOpenApi ssosvOpenApi;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试正常场景：所有参数都有效，crowdPackInnerService.crowdPackUserBatchAddCorpTag 方法成功执行。
     */
    @Test
    public void testCrowdPackUserBatchAddCorpTagSuccess() throws Throwable {
        // arrange
        CrowdPackUserAddCorpTagRequest request = new CrowdPackUserAddCorpTagRequest();
        request.setAppId("appId");
        request.setPackId(1L);
        request.setTagId("tagId");
        request.setToken("validToken");
        SsoUserInfoDTO ssoUserInfoDTO = new SsoUserInfoDTO();
        ssoUserInfoDTO.setLoginName("loginName");
        when(ssosvOpenApi.getUserInfoByAccessToken("validToken")).thenReturn(ssoUserInfoDTO);
        CrowdPackUserAddCorpTagResult result = new CrowdPackUserAddCorpTagResult();
        when(crowdPackInnerService.crowdPackUserBatchAddCorpTag(request)).thenReturn(result);
        // act
        RemoteResponse<CrowdPackUserAddCorpTagResult> response = scrmAutoManagementService.crowdPackUserBatchAddCorpTag(request);
        // assert
        assertEquals(ResponseEnum.SUCCESS.code, response.getCode());
        assertEquals("success", response.getMsg());
        assertEquals(result, response.getData());
    }


    @Test
    public void testCrowdPackUserBatchAddCorpTagRequestIsNull() throws Throwable {
        // arrange
        CrowdPackUserAddCorpTagRequest request = null;
        // act
        RemoteResponse<CrowdPackUserAddCorpTagResult> response = scrmAutoManagementService.crowdPackUserBatchAddCorpTag(request);
        // assert
        assertEquals("请求参数不能为空", response.getMsg());
    }

    /**
     * 测试异常场景：appId为空。
     */
    @Test
    public void testCrowdPackUserBatchAddCorpTagAppIdIsEmpty() throws Throwable {
        // arrange
        CrowdPackUserAddCorpTagRequest request = new CrowdPackUserAddCorpTagRequest();
        request.setAppId("");
        request.setPackId(1L);
        request.setTagId("tagId");
        request.setToken("validToken");
        // act
        RemoteResponse<CrowdPackUserAddCorpTagResult> response = scrmAutoManagementService.crowdPackUserBatchAddCorpTag(request);
        // assert
        assertEquals("appId不能为空", response.getMsg());
    }

    /**
     * 测试异常场景：packId为null。
     */
    @Test
    public void testCrowdPackUserBatchAddCorpTagPackIdIsNull() throws Throwable {
        // arrange
        CrowdPackUserAddCorpTagRequest request = new CrowdPackUserAddCorpTagRequest();
        request.setAppId("appId");
        request.setPackId(null);
        request.setTagId("tagId");
        request.setToken("validToken");
        // act
        RemoteResponse<CrowdPackUserAddCorpTagResult> response = scrmAutoManagementService.crowdPackUserBatchAddCorpTag(request);
        // assert
        assertEquals("packId不能为空", response.getMsg());
    }

    /**
     * 测试异常场景：tagId为空。
     */
    @Test
    public void testCrowdPackUserBatchAddCorpTagTagIdIsEmpty() throws Throwable {
        // arrange
        CrowdPackUserAddCorpTagRequest request = new CrowdPackUserAddCorpTagRequest();
        request.setAppId("appId");
        request.setPackId(1L);
        request.setTagId("");
        request.setToken("validToken");
        // act
        RemoteResponse<CrowdPackUserAddCorpTagResult> response = scrmAutoManagementService.crowdPackUserBatchAddCorpTag(request);
        // assert
        assertEquals("TagId不能为空", response.getMsg());
    }

    /**
     * 测试异常场景：token为空。
     */
    @Test
    public void testCrowdPackUserBatchAddCorpTagTokenIsEmpty() throws Throwable {
        // arrange
        CrowdPackUserAddCorpTagRequest request = new CrowdPackUserAddCorpTagRequest();
        request.setAppId("appId");
        request.setPackId(1L);
        request.setTagId("tagId");
        request.setToken("");
        // act
        RemoteResponse<CrowdPackUserAddCorpTagResult> response = scrmAutoManagementService.crowdPackUserBatchAddCorpTag(request);
        // assert
        assertEquals("token不能为空", response.getMsg());
    }
}
