package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionContentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGetMomentSendResultAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGetMomentTaskResultAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxMomentSendRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGetMomentSendResultResponse;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGetMomentTaskResultResponse;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.dto.ResultDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class StaffMomentTouchAction_CheckStatusTest {

    @InjectMocks
    private StaffMomentTouchAction staffMomentTouchAction;

    @Mock(lenient = true)
    private ExecuteManagementService executeManagementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock(lenient = true)
    private WxGetMomentSendResultAcl wxGetMomentSendResultAcl;

    @Mock(lenient = true)
    private WxGetMomentTaskResultAcl wxGetMomentTaskResultAcl;

    @Mock(lenient = true)
    private InformationGatheringService informationGatheringService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    private ScrmProcessOrchestrationDTO validDTO;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationActionDTO actionDTO;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        validDTO = new ScrmProcessOrchestrationDTO();
        validDTO.setId(1L);
        validDTO.setValidVersion("1.0");
        validDTO.setAppId("testAppId");
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(new ArrayList<>());
        validDTO.setNodeMediumDTO(nodeMediumDTO);
    }

    private ScrmProcessOrchestrationDTO createValidProcessOrchestrationDTO() {
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setId(1L);
        dto.setValidVersion("1.0");
        dto.setAppId("testAppId");
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(new ArrayList<>());
        dto.setNodeMediumDTO(nodeMediumDTO);
        return dto;
    }

    @Test(expected = Exception.class)
    public void testCheckStatus_NullDTO() throws Throwable {
        staffMomentTouchAction.checkStatus(null);
    }

    @Test
    public void testCheckStatus_EmptyInvokeLogList() throws Throwable {
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(executeManagementService.tryStartStatusCheck(any())).thenReturn(true);
        staffMomentTouchAction.checkStatus(validDTO);
        verify(wxInvokeLogDOMapper).selectByExample(any());
        verifyNoMoreInteractions(wxGetMomentSendResultAcl, wxGetMomentTaskResultAcl, informationGatheringService, executeLogDOMapper);
    }

    @Test
    public void testCheckStatus_NoRelevantStatus() throws Throwable {
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> logs = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeLogDO log = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        log.setStatus((byte) 3);
        logs.add(log);
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(logs);
        log.setUpdateTime(new Date());
        when(executeManagementService.tryStartStatusCheck(any())).thenReturn(true);
        staffMomentTouchAction.checkStatus(validDTO);
        verify(wxInvokeLogDOMapper).selectByExample(any());
        verifyNoMoreInteractions(wxGetMomentSendResultAcl, wxGetMomentTaskResultAcl, informationGatheringService, executeLogDOMapper);
    }

    @Test
    public void testCheckStatus_WaitForSend_Success() throws Throwable {
        ScrmAmProcessOrchestrationWxInvokeLogDO log = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        log.setStatus((byte) 5);
        log.setJobid("testJobId");
        log.setExecutorId("testExecutorId");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, -9);
        log.setUpdateTime(calendar.getTime());
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(log));
        when(wxInvokeLogDOMapper.selectByPrimaryKey(any())).thenReturn(log);
        WxGetMomentSendResultResponse response = new WxGetMomentSendResultResponse();
        response.setErrcode(0);
        when(wxGetMomentSendResultAcl.getMomentSendResult(any(), eq("testAppId"))).thenReturn(response);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(executeManagementService.tryStartStatusCheck(any())).thenReturn(true);
        staffMomentTouchAction.checkStatus(validDTO);
        verify(wxInvokeLogDOMapper).selectByExample(any());
        verify(wxGetMomentSendResultAcl).getMomentSendResult(any(), eq("testAppId"));
        verify(wxInvokeLogDOMapper).updateByPrimaryKey(any());
        verify(executeLogDOMapper).updateByExampleSelective(any(), any());
    }

    @Test
    public void testCheckStatus_WaitForSend_Failure() throws Throwable {
        ScrmAmProcessOrchestrationWxInvokeLogDO log = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        log.setStatus((byte) 5);
        log.setJobid("testJobId");
        log.setExecutorId("testExecutorId");
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, -9);
        log.setUpdateTime(calendar.getTime());
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(log));
        WxGetMomentSendResultResponse response = new WxGetMomentSendResultResponse();
        response.setErrcode(1);
        when(wxGetMomentSendResultAcl.getMomentSendResult(any(), eq("testAppId"))).thenReturn(response);
        when(wxInvokeLogDOMapper.selectByPrimaryKey(any())).thenReturn(log);
        when(executeManagementService.tryStartStatusCheck(any())).thenReturn(true);
        staffMomentTouchAction.checkStatus(validDTO);
        verify(wxInvokeLogDOMapper).selectByExample(any());
        verify(wxGetMomentSendResultAcl).getMomentSendResult(any(), eq("testAppId"));
        verify(wxInvokeLogDOMapper, never()).updateByPrimaryKey(any());
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    @Test
    public void testCheckStatus_WaitForCreateResult_Success() throws Throwable {
        ScrmAmProcessOrchestrationWxInvokeLogDO log = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        log.setStatus((byte) 2);
        log.setJobid("testJobId");
        log.setUpdateTime(new Date());
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(log));
        WxGetMomentTaskResultResponse response = new WxGetMomentTaskResultResponse();
        response.setErrcode(0);
        ResultDTO resultDTO = new ResultDTO();
        resultDTO.setMomentId("testMomentId");
        response.setResult(resultDTO);
        when(wxGetMomentTaskResultAcl.getMomentTaskResult(eq("testJobId"), eq("testAppId"))).thenReturn(response);
        when(executeManagementService.tryStartStatusCheck(any())).thenReturn(true);
        staffMomentTouchAction.checkStatus(validDTO);
        verify(wxInvokeLogDOMapper).selectByExample(any());
        verify(wxGetMomentTaskResultAcl).getMomentTaskResult(eq("testJobId"), eq("testAppId"));
        verify(wxInvokeLogDOMapper).updateByPrimaryKey(any());
    }

    @Test
    public void testCheckStatus_WaitForCreateResult_Failure() throws Throwable {
        ScrmAmProcessOrchestrationWxInvokeLogDO log = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        log.setStatus((byte) 2);
        log.setJobid("testJobId");
        log.setUpdateTime(new Date());
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(log));
        WxGetMomentTaskResultResponse response = new WxGetMomentTaskResultResponse();
        response.setErrcode(1);
        when(wxGetMomentTaskResultAcl.getMomentTaskResult(eq("testJobId"), eq("testAppId"))).thenReturn(response);
        when(executeManagementService.tryStartStatusCheck(any())).thenReturn(true);
        staffMomentTouchAction.checkStatus(validDTO);
        verify(wxInvokeLogDOMapper).selectByExample(any());
        verify(wxGetMomentTaskResultAcl).getMomentTaskResult(eq("testJobId"), eq("testAppId"));
        verify(wxInvokeLogDOMapper, never()).updateByPrimaryKey(any());
    }

    @Test(expected = Exception.class)
    public void testCheckStatusProcessOrchestrationDTOIsNull() throws Throwable {
        when(executeManagementService.tryStartStatusCheck(any())).thenReturn(true);
        staffMomentTouchAction.checkStatus(null);
    }

    @Test(expected = Exception.class)
    public void testCheckStatusProcessOrchestrationDTOFieldsAreNull() throws Throwable {
        when(executeManagementService.tryStartStatusCheck(any())).thenReturn(true);
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        staffMomentTouchAction.checkStatus(processOrchestrationDTO);
    }

    @Test
    public void testCheckStatusWxInvokeLogDOMapperReturnsNonEmptyListAndExistStatusIsWaitForCreateResult() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createValidProcessOrchestrationDTO();
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> invokeLogDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeLogDO logDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        logDO.setStatus((byte) 2);
        logDO.setJobid("mockJobId");
        logDO.setUpdateTime(new Date());
        invokeLogDOS.add(logDO);
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(invokeLogDOS);
        WxGetMomentTaskResultResponse mockResponse = new WxGetMomentTaskResultResponse();
        mockResponse.setErrcode(0);
        ResultDTO resultDTO = new ResultDTO();
        resultDTO.setMomentId("mockMomentId");
        mockResponse.setResult(resultDTO);
        when(executeManagementService.tryStartStatusCheck(any())).thenReturn(true);
        when(wxGetMomentTaskResultAcl.getMomentTaskResult(any(String.class), eq("testAppId"))).thenReturn(mockResponse);
        staffMomentTouchAction.checkStatus(processOrchestrationDTO);
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        verify(wxGetMomentTaskResultAcl, times(1)).getMomentTaskResult(any(String.class), eq("testAppId"));
        verify(wxInvokeLogDOMapper).updateByPrimaryKey(any());
    }

    @Test
    public void testCheckStatusWxInvokeLogDOMapperReturnsEmptyList() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createValidProcessOrchestrationDTO();
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(Collections.emptyList());
        when(executeManagementService.tryStartStatusCheck(any())).thenReturn(true);
        staffMomentTouchAction.checkStatus(processOrchestrationDTO);
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        //verifyNoInteractions(executeManagementService, wxGetMomentSendResultAcl, wxGetMomentTaskResultAcl, informationGatheringService, executeLogDOMapper);
    }

    @Test
    public void testCheckStatusWxInvokeLogDOMapperReturnsNonEmptyListButAllStatusAreNotWaitForSendAndWaitForCreateResult() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createValidProcessOrchestrationDTO();
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> invokeLogDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeLogDO logDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        logDO.setStatus((byte) 3);
        invokeLogDOS.add(logDO);
        logDO.setUpdateTime(new Date());
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(invokeLogDOS);
        when(executeManagementService.tryStartStatusCheck(any())).thenReturn(true);
        staffMomentTouchAction.checkStatus(processOrchestrationDTO);
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        // verifyNoInteractions(executeManagementService, wxGetMomentSendResultAcl, wxGetMomentTaskResultAcl, informationGatheringService, executeLogDOMapper);
    }

    @Test
    public void testCheckStatusWxInvokeLogDOMapperReturnsNonEmptyListAndExistStatusIsWaitForSend() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createValidProcessOrchestrationDTO();
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> invokeLogDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeLogDO logDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        logDO.setStatus((byte) 5);
        logDO.setJobid("mockJobId");
        logDO.setExecutorId("mockExecutorId");
        invokeLogDOS.add(logDO);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, -9);
        logDO.setUpdateTime(calendar.getTime());
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(invokeLogDOS);
        when(wxInvokeLogDOMapper.selectByPrimaryKey(any())).thenReturn(logDO);
        WxGetMomentSendResultResponse mockResponse = new WxGetMomentSendResultResponse();
        mockResponse.setErrcode(0);
        when(wxGetMomentSendResultAcl.getMomentSendResult(any(), eq("testAppId"))).thenReturn(mockResponse);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(executeManagementService.tryStartStatusCheck(any())).thenReturn(true);
        staffMomentTouchAction.checkStatus(processOrchestrationDTO);
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        verify(wxGetMomentSendResultAcl, times(1)).getMomentSendResult(any(), eq("testAppId"));
        verify(executeLogDOMapper).updateByExampleSelective(any(), any());
    }

    @Test
    public void testCheckStatus_MultipleInvokeLogs() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createValidProcessOrchestrationDTO();
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> invokeLogDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeLogDO logDO1 = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        logDO1.setStatus((byte) 5);
        logDO1.setJobid("mockJobId1");
        logDO1.setExecutorId("mockExecutorId1");
        invokeLogDOS.add(logDO1);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.HOUR, -9);
        logDO1.setUpdateTime(calendar.getTime());
        ScrmAmProcessOrchestrationWxInvokeLogDO logDO2 = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        logDO2.setStatus((byte) 2);
        logDO2.setJobid("mockJobId2");
        logDO2.setUpdateTime(calendar.getTime());
        invokeLogDOS.add(logDO2);
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(invokeLogDOS);
        when(wxInvokeLogDOMapper.selectByPrimaryKey(any())).thenReturn(logDO1);
        WxGetMomentSendResultResponse mockSendResponse = new WxGetMomentSendResultResponse();
        mockSendResponse.setErrcode(0);
        when(wxGetMomentSendResultAcl.getMomentSendResult(any(), eq("testAppId"))).thenReturn(mockSendResponse);
        WxGetMomentTaskResultResponse mockTaskResponse = new WxGetMomentTaskResultResponse();
        mockTaskResponse.setErrcode(0);
        ResultDTO resultDTO = new ResultDTO();
        resultDTO.setMomentId("mockMomentId");
        mockTaskResponse.setResult(resultDTO);
        when(wxGetMomentTaskResultAcl.getMomentTaskResult(eq("mockJobId2"), eq("testAppId"))).thenReturn(mockTaskResponse);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(executeManagementService.tryStartStatusCheck(any())).thenReturn(true);
        staffMomentTouchAction.checkStatus(processOrchestrationDTO);
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        verify(wxGetMomentSendResultAcl, times(1)).getMomentSendResult(any(), eq("testAppId"));
        verify(wxGetMomentTaskResultAcl, times(1)).getMomentTaskResult(eq("mockJobId2"), eq("testAppId"));
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(), any());
        verify(wxInvokeLogDOMapper, times(2)).updateByPrimaryKey(any());
    }

    /**
     * Test when wxMomentSendRequest is not null and has complete object hierarchy
     */
    @Test
    public void testGetWxMomentSendRequest_WithCompleteHierarchy() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("Test Content");
        contentDTOS.add(contentDTO);
        List<String> tagList = new ArrayList<>();
        tagList.add("tag1");
        tagList.add("tag2");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(null, 1, processOrchestrationDTO.getAppId(), processOrchestrationDTO.getExecutorList().stream().map(o->o.getExecutorId()).collect(Collectors.toList()), 2);
        executeManagementDTO.getStaffLimitSet().add("executor1");
        when(actionDTO.getContentType()).thenReturn(0);
        when(executeLogDO.getExecutorId()).thenReturn("executor1");
        when(processOrchestrationDTO.getNodeMediumDTO()).thenReturn(nodeMediumDTO);
        when(nodeMediumDTO.getActionAttachmentDTOList(any())).thenReturn(new ArrayList<>());
        when(nodeMediumDTO.getProcessOrchestrationNodeDTOList()).thenReturn(new ArrayList<>());
        when(processOrchestrationDTO.getNodeMediumDTO().getProcessOrchestrationNodeDTOList()).thenReturn(new ArrayList<>());
        // act
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        WxMomentSendRequest result = staffMomentTouchAction.getWxMomentSendRequestWithTagList(processOrchestrationDTO, executeLogDO, contentDTOS, actionDTO, tagList, executeManagementDTO, stepExecuteResultDTO);
        // assert
        assertNotNull(result);
        assertNotNull(result.getVisible_range());
        assertNotNull(result.getVisible_range().getSender_list());
        assertNotNull(result.getVisible_range().getSender_list().getUser_list());
        assertEquals("executor1", result.getVisible_range().getSender_list().getUser_list().get(0));
        assertEquals("Test Content", result.getText().getContent());
    }

    /**
     * Test when visible_range is null
     */
    @Test
    public void testGetWxMomentSendRequest_WhenVisibleRangeIsNull() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("Test Content");
        contentDTOS.add(contentDTO);
        List<String> tagList = new ArrayList<>();
        tagList.add("tag1");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(null, 1, processOrchestrationDTO.getAppId(), processOrchestrationDTO.getExecutorList().stream().map(o->o.getExecutorId()).collect(Collectors.toList()), 2);
        executeManagementDTO.getStaffLimitSet().add("executor1");
        when(actionDTO.getContentType()).thenReturn(0);
        when(executeLogDO.getExecutorId()).thenReturn("executor1");
        when(processOrchestrationDTO.getNodeMediumDTO()).thenReturn(nodeMediumDTO);
        when(nodeMediumDTO.getActionAttachmentDTOList(any())).thenReturn(new ArrayList<>());
        when(nodeMediumDTO.getProcessOrchestrationNodeDTOList()).thenReturn(new ArrayList<>());
        when(processOrchestrationDTO.getNodeMediumDTO().getProcessOrchestrationNodeDTOList()).thenReturn(new ArrayList<>());
        // act
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        WxMomentSendRequest result = staffMomentTouchAction.getWxMomentSendRequestWithTagList(processOrchestrationDTO, executeLogDO, contentDTOS, actionDTO, tagList, executeManagementDTO, stepExecuteResultDTO);
        result.setVisible_range(null);
        // assert
        assertNull(result.getVisible_range());
    }

    /**
     * Test when external_contact_list is null
     */
    @Test
    public void testGetWxMomentSendRequest_WhenExternalContactListIsNull() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("Test Content");
        contentDTOS.add(contentDTO);
        List<String> tagList = new ArrayList<>();
        tagList.add("tag1");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(null, 1, processOrchestrationDTO.getAppId(), processOrchestrationDTO.getExecutorList().stream().map(o->o.getExecutorId()).collect(Collectors.toList()), 2);
        executeManagementDTO.getStaffLimitSet().add("executor1");
        when(actionDTO.getContentType()).thenReturn(0);
        when(executeLogDO.getExecutorId()).thenReturn("executor1");
        when(processOrchestrationDTO.getNodeMediumDTO()).thenReturn(nodeMediumDTO);
        when(nodeMediumDTO.getActionAttachmentDTOList(any())).thenReturn(new ArrayList<>());
        when(nodeMediumDTO.getProcessOrchestrationNodeDTOList()).thenReturn(new ArrayList<>());
        when(processOrchestrationDTO.getNodeMediumDTO().getProcessOrchestrationNodeDTOList()).thenReturn(new ArrayList<>());
        // act
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        WxMomentSendRequest result = staffMomentTouchAction.getWxMomentSendRequestWithTagList(processOrchestrationDTO, executeLogDO, contentDTOS, actionDTO, tagList, executeManagementDTO, stepExecuteResultDTO);
        result.getVisible_range().setExternal_contact_list(null);
        // assert
        assertNull(result.getVisible_range().getExternal_contact_list());
    }

    /**
     * 测试failedInvokeLog方法，正常情况
     */
    @Test
    public void testFailedInvokeLogNormal() {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setId(1L);
        List<Byte> statusList = Arrays.asList((byte) 1, (byte) 2);

        // act
        staffMomentTouchAction.failedInvokeLog(invokeLogDO, statusList);

        // assert
        verify(wxInvokeLogDOMapper, times(1)).updateByPrimaryKey(invokeLogDO);
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any());
    }

    /**
     * 测试failedInvokeLog方法，传入空的statusList
     */
    @Test
    public void testFailedInvokeLogEmptyStatusList() {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setId(1L);
        List<Byte> statusList = Arrays.asList();

        // act
        staffMomentTouchAction.failedInvokeLog(invokeLogDO, statusList);

        // assert
        verify(wxInvokeLogDOMapper, times(1)).updateByPrimaryKey(invokeLogDO);
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any());
    }

    /**
     * 测试failedInvokeLog方法，invokeLogDO为null
     */
    @Test(expected = NullPointerException.class)
    public void testFailedInvokeLogNullInvokeLogDO() {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = null;
        List<Byte> statusList = Arrays.asList((byte) 1, (byte) 2);

        // act
        staffMomentTouchAction.failedInvokeLog(invokeLogDO, statusList);

        // assert
        // 期望抛出NullPointerException
    }
}
