package com.sankuai.scrm.core.service.external.contact.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.meituan.image.client.utils.Md5Utils;
import com.sankuai.dz.srcm.external.contact.enums.MobileAddFriendRealTimeTaskFailTypeEnum;
import com.sankuai.dz.srcm.external.contact.enums.MobileAddFriendRealTimeTaskStatuEnum;
import com.sankuai.inf.kms.pangolin.api.service.IEncryptService;
import com.sankuai.scrm.core.service.external.contact.bo.MobileAddFriendRealTimeEditBO;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ScrmMobileAddFriendTaskDO;
import com.sankuai.scrm.core.service.external.contact.dal.example.ScrmMobileAddFriendTaskDOExample;
import com.sankuai.scrm.core.service.external.contact.dal.mapper.ext.ExtScrmMobileAddFriendTaskDOMapper;
import com.sankuai.scrm.core.service.external.contact.mq.producer.MobileAddFriendRealTimeMsgProducer;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsAddFriendAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.MobileAddFriendRealTimeEntity;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.service.fe.corp.ds.dto.addfriend.AddFriendByMobileResultDTO;
import java.security.GeneralSecurityException;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

@ExtendWith(MockitoExtension.class)
class MobileAddFriendRealTimeDomainServiceExecuteMobileAddFriendRealTimeTaskTest {

    @Mock
    private ExtScrmMobileAddFriendTaskDOMapper mobileAddFriendTaskDOMapper;

    @Mock
    private DsAddFriendAcl dsAddFriendAcl;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private MobileAddFriendRealTimeMsgProducer mobileAddFriendRealTimeMsgProducer;

    @Mock
    private IEncryptService phoneEncryptService;

    @Mock
    private Logger log;

    @InjectMocks
    private MobileAddFriendRealTimeDomainService mobileAddFriendRealTimeDomainService;

    private final Long testId = 123L;

    /**
     * Test when entity is null (decryption fails)
     */
    @Test
    public void testExecuteMobileAddFriendRealTimeTaskWhenEntityIsNull() throws Throwable {
        // arrange
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(Arrays.asList());
        // act
        mobileAddFriendRealTimeDomainService.executeMobileAddFriendRealTimeTask(testId);
        // assert
        verify(mobileAddFriendTaskDOMapper).updateByPrimaryKeySelective(argThat(taskDO -> taskDO.getId().equals(testId) && taskDO.getTaskStatus().equals(MobileAddFriendRealTimeTaskStatuEnum.FAILED.getCode()) && taskDO.getFailType().equals(MobileAddFriendRealTimeTaskFailTypeEnum.DECRYPT_FAIL.getCode())));
    }

    /**
     * Test when RPC call returns null taskId (RPC fails)
     */
    @Test
    public void testExecuteMobileAddFriendRealTimeTaskWhenRpcFails() throws Throwable {
        // arrange
        ScrmMobileAddFriendTaskDO taskDO = new ScrmMobileAddFriendTaskDO();
        taskDO.setId(testId);
        taskDO.setAddNumber("encryptedNumber");
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(Arrays.asList(taskDO));
        when(phoneEncryptService.decryptUTF8String(any())).thenReturn("decryptedNumber");
        when(dsAddFriendAcl.dsMobileAddFriendRealTime(any())).thenReturn(null);
        // act
        mobileAddFriendRealTimeDomainService.executeMobileAddFriendRealTimeTask(testId);
        // assert
        verify(mobileAddFriendTaskDOMapper).updateByPrimaryKeySelective(argThat(updatedTask -> updatedTask.getId().equals(testId) && updatedTask.getTaskStatus().equals(MobileAddFriendRealTimeTaskStatuEnum.FAILED.getCode()) && updatedTask.getFailType().equals(MobileAddFriendRealTimeTaskFailTypeEnum.RPC_FAIL.getCode())));
    }

    /**
     * Test successful case where RPC returns valid taskId
     */
    @Test
    public void testExecuteMobileAddFriendRealTimeTaskSuccess() throws Throwable {
        // arrange
        ScrmMobileAddFriendTaskDO taskDO = new ScrmMobileAddFriendTaskDO();
        taskDO.setId(testId);
        taskDO.setAddNumber("encryptedNumber");
        taskDO.setNumberType(1);
        taskDO.setAppId("testApp");
        taskDO.setWelcomeContent("welcome");
        taskDO.setAccountId("testAccount");
        String expectedTaskId = "testTaskId";
        String expectedPrefixedTaskId = "0_testTaskId";
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(Arrays.asList(taskDO));
        when(phoneEncryptService.decryptUTF8String(any())).thenReturn("decryptedNumber");
        when(dsAddFriendAcl.dsMobileAddFriendRealTime(any())).thenReturn(expectedTaskId);
        // act
        mobileAddFriendRealTimeDomainService.executeMobileAddFriendRealTimeTask(testId);
        // assert
        verify(mobileAddFriendTaskDOMapper).updateByPrimaryKeySelective(argThat(updatedTask -> updatedTask.getId().equals(testId) && updatedTask.getTaskStatus().equals(MobileAddFriendRealTimeTaskStatuEnum.IN_PROGRESS.getCode()) && updatedTask.getDsTaskId().equals(expectedPrefixedTaskId)));
    }

    /**
     * Test when decryption throws GeneralSecurityException
     */
    @Test
    public void testExecuteMobileAddFriendRealTimeTaskWhenDecryptionFails() throws Throwable {
        // arrange
        ScrmMobileAddFriendTaskDO taskDO = new ScrmMobileAddFriendTaskDO();
        taskDO.setId(testId);
        taskDO.setAddNumber("encryptedNumber");
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(Arrays.asList(taskDO));
        when(phoneEncryptService.decryptUTF8String(any())).thenThrow(new GeneralSecurityException("decryption failed"));
        // act
        mobileAddFriendRealTimeDomainService.executeMobileAddFriendRealTimeTask(testId);
        // assert
        verify(mobileAddFriendTaskDOMapper).updateByPrimaryKeySelective(argThat(updatedTask -> updatedTask.getId().equals(testId) && updatedTask.getTaskStatus().equals(MobileAddFriendRealTimeTaskStatuEnum.FAILED.getCode()) && updatedTask.getFailType().equals(MobileAddFriendRealTimeTaskFailTypeEnum.DECRYPT_FAIL.getCode())));
    }
}
