package com.sankuai.scrm.core.service.external.contact.crane;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxContactAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxStaffAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxBatchContactUserDetail;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxContactUserDetailListResponse;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.*;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * 单测覆盖所有分支，利用系统属性和反射设置静态字段，保证分支可控
 */
@ExtendWith(MockitoExtension.class)
class RefreshHistoryExternalUserTaskRefreshExternalUserTest {

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private CorpWxStaffAcl corpWxStaffAcl;

    @Mock
    private CorpWxContactAcl corpWxContactAcl;

    @Mock
    private ContactUserDomain contactUserDomain;

    private String oldAppName;

    private String oldAppId;

    @InjectMocks
    private RefreshHistoryExternalUserTask refreshHistoryExternalUserTask;

    @BeforeEach
    void setUp() throws Exception {
        // 记录原始appName
        Field appNameField = Class.forName("com.dianping.lion.Environment").getDeclaredField("appName");
        appNameField.setAccessible(true);
        oldAppName = (String) appNameField.get(null);
        oldAppId = System.getProperty("scrm.core.refresh.appId");
    }

    private void setAppName(String appName) throws Exception {
        Field appNameField = Class.forName("com.dianping.lion.Environment").getDeclaredField("appName");
        appNameField.setAccessible(true);
        appNameField.set(null, appName);
    }

    private void resetAppName() throws Exception {
        setAppName(oldAppName);
    }

    private void setAppId(String appId) {
        if (appId == null) {
            System.clearProperty("scrm.core.refresh.appId");
        } else {
            System.setProperty("scrm.core.refresh.appId", appId);
        }
    }

    private void resetAppId() {
        if (oldAppId == null) {
            System.clearProperty("scrm.core.refresh.appId");
        } else {
            System.setProperty("scrm.core.refresh.appId", oldAppId);
        }
    }

    @AfterEach
    void tearDown() throws Exception {
        resetAppName();
        resetAppId();
    }

    /**
     * Test when appId is blank from Lion config
     */
    @Test
    public void testRefreshExternalUserWhenAppIdIsBlank() throws Throwable {
        // arrange
        setAppName("testAppName1");
        // Lion.getString("testAppName1", "scrm.core.refresh.appId") 返回""
        setAppId("");
        // act
        refreshHistoryExternalUserTask.refreshExternalUser();
        // assert
        verify(appConfigRepository, never()).getCorpIdByAppId(anyString());
        verify(corpWxStaffAcl, never()).getFollowUserList(anyString());
    }

    /**
     * Test when corpId is blank from repository
     */
    @Test
    public void testRefreshExternalUserWhenCorpIdIsBlank() throws Throwable {
        // arrange
        setAppName("testAppName2");
        setAppId("appid2");
        when(appConfigRepository.getCorpIdByAppId("appid2")).thenReturn(null);
        // act
        refreshHistoryExternalUserTask.refreshExternalUser();
        // assert
        verify(corpWxStaffAcl, never()).getFollowUserList(anyString());
    }

    /**
     * Test when staffIdList is empty
     */
    @Test
    public void testRefreshExternalUserWhenStaffIdListIsEmpty() throws Throwable {
        // arrange
        setAppName("testAppName3");
        setAppId("appid3");
        String corpId = "corpId3";
        when(appConfigRepository.getCorpIdByAppId("appid3")).thenReturn(corpId);
        when(corpWxStaffAcl.getFollowUserList(corpId)).thenReturn(Collections.emptyList());
        // act
        refreshHistoryExternalUserTask.refreshExternalUser();
        // assert
        verify(corpWxContactAcl, never()).batchGetUserDetail(anyString(), anyList(), anyString());
    }

    /**
     * Test when exception occurs during execution
     */
    @Test
    public void testRefreshExternalUserWhenExceptionOccurs() {
        String appId = "testAppId";
        String corpId = "testCorpId";

        try (MockedStatic<Environment> envMock = mockStatic(Environment.class);
             MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {

            envMock.when(Environment::getAppName).thenReturn("scrm-core-service");
            lionMock.when(() -> Lion.getString("scrm-core-service", "scrm.core.refresh.appId"))
                    .thenReturn(appId);

            when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
            when(corpWxStaffAcl.getFollowUserList(corpId))
                    .thenThrow(new RuntimeException("Test exception"));

            // 修改：不期望抛出异常，而是验证异常被正确处理
            assertDoesNotThrow(() -> {
                refreshHistoryExternalUserTask.refreshExternalUser();
            });

            // 验证方法被调用到异常点
            verify(appConfigRepository).getCorpIdByAppId(appId);
            verify(corpWxStaffAcl).getFollowUserList(corpId);

            // 验证后续方法没有被调用（因为异常中断了流程）
            verify(corpWxContactAcl, never()).batchGetUserDetail(any(), any(), any());
        }
    }
}