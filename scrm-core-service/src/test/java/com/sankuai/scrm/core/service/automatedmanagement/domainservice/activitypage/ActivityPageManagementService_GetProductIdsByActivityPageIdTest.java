package com.sankuai.scrm.core.service.automatedmanagement.domainservice.activitypage;

import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationProductActivityPageDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductActivityPageDOMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ActivityPageManagementService_GetProductIdsByActivityPageIdTest {

    @InjectMocks
    private ActivityPageManagementService activityPageManagementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductActivityPageDOMapper activityPageDOMapper;

    private String appId;

    private Long activityPageId;

    private List<ScrmAmProcessOrchestrationProductActivityPageDO> activityPageDOS;

    @Before
    public void setUp() {
        appId = "testAppId";
        activityPageId = 1L;
        activityPageDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationProductActivityPageDO activityPageDO = new ScrmAmProcessOrchestrationProductActivityPageDO();
        activityPageDO.setRelatedProductIds("1,2,3");
        activityPageDOS.add(activityPageDO);
    }

    @Test
    public void testGetProductIdsByActivityPageIdAppIdIsNull() {
        List<Long> result = activityPageManagementService.getProductIdsByActivityPageId(null, activityPageId);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testGetProductIdsByActivityPageIdActivityPageIdIsNull() {
        List<Long> result = activityPageManagementService.getProductIdsByActivityPageId(appId, null);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testGetProductIdsByActivityPageIdActivityPageDOSIsEmpty() {
        when(activityPageDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductActivityPageDOExample.class))).thenReturn(new ArrayList<>());
        List<Long> result = activityPageManagementService.getProductIdsByActivityPageId(appId, activityPageId);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testGetProductIdsByActivityPageIdRelatedProductIdsIsNull() {
        activityPageDOS.get(0).setRelatedProductIds(null);
        when(activityPageDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductActivityPageDOExample.class))).thenReturn(activityPageDOS);
        List<Long> result = activityPageManagementService.getProductIdsByActivityPageId(appId, activityPageId);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testGetProductIdsByActivityPageIdNormal() {
        when(activityPageDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductActivityPageDOExample.class))).thenReturn(activityPageDOS);
        List<Long> result = activityPageManagementService.getProductIdsByActivityPageId(appId, activityPageId);
        assertEquals(Arrays.asList(1L, 2L, 3L), result);
    }
}
