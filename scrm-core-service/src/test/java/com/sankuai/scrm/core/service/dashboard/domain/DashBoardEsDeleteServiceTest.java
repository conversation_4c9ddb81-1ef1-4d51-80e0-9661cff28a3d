package com.sankuai.scrm.core.service.dashboard.domain;

import com.sankuai.meituan.poros.client.PorosRestHighLevelClient;
import org.elasticsearch.client.RequestOptions;
import org.elasticsearch.index.reindex.DeleteByQueryRequest;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.slf4j.Logger;
import java.lang.reflect.Field;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import org.mockito.InjectMocks;
import static org.mockito.Mockito.*;
import org.elasticsearch.action.update.UpdateRequest;
import org.elasticsearch.common.xcontent.XContentType;
import org.elasticsearch.index.query.QueryBuilders;
import org.elasticsearch.index.query.TermQueryBuilder;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DashBoardEsDeleteServiceTest {

    @Mock
    private PorosRestHighLevelClient porosRestHighLevelClient;

    @Mock
    private Logger log;

    private DashBoardEsDeleteService dashBoardEsDeleteService;

    private Field porosRestHighLevelClientField;

    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        dashBoardEsDeleteService = new DashBoardEsDeleteService();
        porosRestHighLevelClientField = DashBoardEsDeleteService.class.getDeclaredField("porosRestHighLevelClient");
        porosRestHighLevelClientField.setAccessible(true);
        porosRestHighLevelClientField.set(dashBoardEsDeleteService, porosRestHighLevelClient);
    }

    /**
     * 测试正常情况下的删除操作
     */
    @Test
    public void testDeleteEsData_Success() throws Throwable {
        // arrange
        String indexName = "testIndex";
        // act
        dashBoardEsDeleteService.deleteEsData(indexName);
        // assert
        verify(porosRestHighLevelClient, times(1)).deleteByQuery(any(DeleteByQueryRequest.class), eq(RequestOptions.DEFAULT));
    }

    /**
     * 测试删除操作抛出异常的情况
     */
    @Test
    public void testDeleteEsData_Exception() throws Throwable {
        // arrange
        String indexName = "testIndex";
        doThrow(new RuntimeException("Delete failed")).when(porosRestHighLevelClient).deleteByQuery(any(DeleteByQueryRequest.class), eq(RequestOptions.DEFAULT));
        // act
        dashBoardEsDeleteService.deleteEsData(indexName);
        // assert
        verify(porosRestHighLevelClient, times(1)).deleteByQuery(any(DeleteByQueryRequest.class), eq(RequestOptions.DEFAULT));
    }

    /**
     * 测试索引名为 null 的情况
     */
    @Test(expected = NullPointerException.class)
    public void testDeleteEsData_NullIndexName() throws Throwable {
        // act
        dashBoardEsDeleteService.deleteEsData(null);
    }

    /**
     * 测试索引名为空字符串的情况
     */
    @Test
    public void testDeleteEsData_EmptyIndexName() throws Throwable {
        // arrange
        String indexName = "";
        // act
        dashBoardEsDeleteService.deleteEsData(indexName);
        // assert
        verify(porosRestHighLevelClient, times(1)).deleteByQuery(any(DeleteByQueryRequest.class), eq(RequestOptions.DEFAULT));
    }
}
