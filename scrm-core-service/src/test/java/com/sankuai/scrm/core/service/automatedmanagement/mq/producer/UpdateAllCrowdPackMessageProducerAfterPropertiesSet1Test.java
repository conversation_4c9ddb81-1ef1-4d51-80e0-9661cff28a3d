package com.sankuai.scrm.core.service.automatedmanagement.mq.producer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import java.lang.reflect.Field;
import java.util.Properties;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class UpdateAllCrowdPackMessageProducerAfterPropertiesSet1Test {

    private UpdateAllCrowdPackMessageProducer producer;

    private MockedStatic<MafkaClient> mockedMafkaClient;

    @BeforeEach
    void setUp() throws Exception {
        producer = new UpdateAllCrowdPackMessageProducer();
        // 使用反射重置静态producer字段
        setPrivateStaticField(UpdateAllCrowdPackMessageProducer.class, "producer", null);
    }

    @AfterEach
    void tearDown() {
        if (mockedMafkaClient != null) {
            mockedMafkaClient.close();
        }
    }

    // 辅助方法：通过反射获取私有静态字段
    @SuppressWarnings("unchecked")
    private static <T> T getPrivateStaticField(Class<?> clazz, String fieldName) throws Exception {
        Field field = clazz.getDeclaredField(fieldName);
        field.setAccessible(true);
        return (T) field.get(null);
    }

    // 辅助方法：通过反射设置私有静态字段
    private static void setPrivateStaticField(Class<?> clazz, String fieldName, Object value) throws Exception {
        Field field = clazz.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }

    /**
     * 测试正常场景 - 成功创建producer
     */
    @Test
    void testAfterPropertiesSetSuccess() throws Throwable {
        // arrange
        IProducerProcessor mockProducer = mock(IProducerProcessor.class);
        mockedMafkaClient = mockStatic(MafkaClient.class);
        Properties expectedProps = new Properties();
        expectedProps.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        expectedProps.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
        mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(expectedProps, "scrm.update.all.crowd.pack.msg")).thenReturn(mockProducer);
        // act
        producer.afterPropertiesSet();
        // assert
        mockedMafkaClient.verify(() -> MafkaClient.buildProduceFactory(expectedProps, "scrm.update.all.crowd.pack.msg"));
        IProducerProcessor actualProducer = getPrivateStaticField(UpdateAllCrowdPackMessageProducer.class, "producer");
        assertNotNull(actualProducer);
        assertEquals(mockProducer, actualProducer);
    }

    /**
     * 测试异常场景 - MafkaClient.buildProduceFactory抛出异常
     */
    @Test
    void testAfterPropertiesSetWhenBuildFactoryThrowsException() throws Throwable {
        // arrange
        mockedMafkaClient = mockStatic(MafkaClient.class);
        Properties expectedProps = new Properties();
        expectedProps.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
        expectedProps.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
        mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(expectedProps, "scrm.update.all.crowd.pack.msg")).thenThrow(new RuntimeException("Build factory failed"));
        // act & assert
        Exception exception = assertThrows(RuntimeException.class, () -> producer.afterPropertiesSet());
        assertEquals("Build factory failed", exception.getMessage());
        IProducerProcessor actualProducer = getPrivateStaticField(UpdateAllCrowdPackMessageProducer.class, "producer");
        assertNull(actualProducer);
    }
}
