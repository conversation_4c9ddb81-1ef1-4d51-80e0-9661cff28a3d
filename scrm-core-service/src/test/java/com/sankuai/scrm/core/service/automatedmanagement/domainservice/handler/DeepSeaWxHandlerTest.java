package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentContentDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationInformationGatheringEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationAttachmentTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationContentTypeEnum;
import com.sankuai.scrm.core.service.aigc.service.domainservice.ScrmGroupRetailUserCouponRecordDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.BizDistributorServiceKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.group.dynamiccode.constant.GroupDynamicCodeConstants;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsAssistantAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsFriendAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.AssistantInfo;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.FriendSendMsgResponse;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import com.sankuai.scrm.core.service.message.push.request.MsgPushRequest;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import com.sankuai.service.fe.corp.ds.enums.msg.MsgResultTypeTEnum;
import com.sankuai.service.fe.corp.ds.event.msg.MsgResultEvent;
import com.sankuai.service.fe.corp.ds.event.msg.MsgTaskResultDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/12/3
 */
@ExtendWith(MockitoExtension.class)
public class DeepSeaWxHandlerTest {

    @InjectMocks
    private DeepSeaWxHandler deepSeaWxHandler;

    @Mock(lenient = true)
    private ProductManagementService productManagementService;

    @Mock(lenient = true)
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    @Mock(lenient = true)
    private InvokeDetailKeyObject invokeDetailKeyObject;

    @Mock(lenient = true)
    private CorpAppConfigRepository appConfigRepository;

    @Mock(lenient = true)
    private DsAssistantAcl dsAssistantAcl;

    @Mock(lenient = true)
    private DsFriendAcl dsFriendAcl;

    @Mock(lenient = true)
    private AssistantInfo assistantInfo;

    @Mock(lenient = true)
    private ShortLinkUtils shortLinkUtils;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock(lenient = true)
    private InformationGatheringService informationGatheringService;

    @Mock
    private ScrmGroupRetailUserCouponRecordDomainService scrmGroupRetailUserCouponRecordDomainService;

    private MsgResultEvent msgResultEvent;

    private ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOList;

    String processOrchestrationMocks = "{\n" + "  \"id\" : 192,\n" + "  \"name\" : \"自动推品+全部商品+团购+群\",\n" + "  \"processOrchestrationType\" : 4,\n" + "  \"cron\" : \"\",\n" + "  \"beginTime\" : 1725702000000,\n" + "  \"endTime\" : 1725874800000,\n" + "  \"status\" : 1,\n" + "  \"validVersion\" : \"1725788339717\",\n" + "  \"updateTime\" : 1725788339000,\n" + "  \"creatorId\" : \"wangxuefei05\",\n" + "  \"lastUpdaterId\" : \"wangxuefei05\",\n" + "  \"participationRestrict\" : true,\n" + "  \"participationRestrictionsCycle\" : 0,\n" + "  \"participationRestrictionsTimes\" : 0,\n" + "  \"appId\" : \"yimei\",\n" + "  \"previewPic\" : \"\",\n" + "  \"cronComment\" : \"2024-09-08 17:40:00\",\n" + "  \"executorType\" : 1,\n" + "  \"executorList\" : [ {\n" + "    \"executorId\" : \"WangXueFei\",\n" + "    \"executorName\" : \"王雪飞\",\n" + "    \"executorType\" : 2\n" + "  } ],\n" + "  \"crowdPackType\" : 3,\n" + "  \"crowdPackIdList\" : [ ],\n" + "  \"groupIdList\" : [ \"wrb_61EQAACdwDGbhFsGznPHZMX3i3_A\" ],\n" + "  \"groupInfoList\" : [ {\n" + "    \"groupName\" : \"军师测试25\",\n" + "    \"owner\" : \"WangXueFei\",\n" + "    \"robotList\" : null,\n" + "    \"groupId\" : \"wrb_61EQAACdwDGbhFsGznPHZMX3i3_A\",\n" + "    \"createDate\" : 1725788266000,\n" + "    \"memberCount\" : 3\n" + "  } ],\n" + "  \"crowdPackUpdateStrategyInfoDTO\" : null,\n" + "  \"goalDTO\" : {\n" + "    \"id\" : 554,\n" + "    \"checkTime\" : \"3\",\n" + "    \"checkTimeUnit\" : \"2\",\n" + "    \"status\" : 1,\n" + "    \"processOrchestrationId\" : 192,\n" + "    \"processOrchestrationVersion\" : \"1725788339717\",\n" + "    \"goalType\" : 1,\n" + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003 ],\n" + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n" + "  \"negativeGoalDTO\" : {\n" + "    \"id\" : 553,\n" + "    \"checkTime\" : \"3\",\n" + "    \"checkTimeUnit\" : \"2\",\n" + "    \"status\" : 1,\n" + "    \"processOrchestrationId\" : 192,\n" + "    \"processOrchestrationVersion\" : \"1725788339717\",\n" + "    \"goalType\" : 2,\n" + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003 ],\n" + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n" + "  \"nodeMediumDTO\" : {\n" + "    \"processOrchestrationNodeDTOList\" : [ {\n" + "      \"nodeId\" : 0,\n" + "      \"preNodeId\" : -1,\n" + "      \"nodeType\" : 4,\n" + "      \"id\" : 750,\n" + "      \"processOrchestrationId\" : 192,\n" + "      \"processOrchestrationVersion\" : \"1725788339717\",\n" + "      \"childrenNodes\" : [ 1725778802191 ]\n" + "    }, {\n" + "      \"nodeId\" : 1725778802191,\n" + "      \"preNodeId\" : 0,\n" + "      \"nodeType\" : 2,\n" + "      \"id\" : 751,\n" + "      \"processOrchestrationId\" : 192,\n" + "      \"processOrchestrationVersion\" : \"1725788339717\",\n" + "      \"childrenNodes\" : [ ]\n" + "    } ],\n" + "    \"conditionMap\" : { },\n" + "    \"actionMap\" : {\n" + "      \"1725778802191\" : {\n" + "        \"id\" : 343,\n" + "        \"actionId\" : 1,\n" + "        \"processOrchestrationId\" : 192,\n" + "        \"processOrchestrationVersion\" : \"1725788339717\",\n" + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"actionType\" : 7,\n" + "        \"actionSubType\" : 5,\n" + "        \"updateTime\" : 1725788339000,\n" + "        \"contentType\" : 1,\n" + "        \"contentList\" : null\n" + "      }\n" + "    },\n" + "    \"actionContentMap\" : {\n" + "      \"1725778802191-1\" : [ {\n" + "        \"id\" : 342,\n" + "        \"actionId\" : 1,\n" + "        \"contentId\" : 1,\n" + "        \"processOrchestrationId\" : 192,\n" + "        \"processOrchestrationVersion\" : \"1725788339717\",\n" + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"content\" : \"\",\n" + "        \"updateTime\" : 1725788339000,\n" + "        \"contentType\" : 1,\n" + "        \"attachmentDTOList\" : null\n" + "      } ]\n" + "    },\n" + "    \"actionAttachmentMap\" : {\n" + "      \"1725778802191-1-1\" : [ {\n" + "        \"id\" : 422,\n" + "        \"contentId\" : 1,\n" + "        \"actionId\" : 1,\n" + "        \"attachmentTypeId\" : 7,\n" + "        \"attachmentContent\" : \"{\\\"supplyType\\\":2,\\\"productType\\\":1,\\\"shelfName\\\":\\\"\\\",\\\"jumpPageType\\\":\\\"\\\",\\\"jumpUrl\\\":\\\"\\\",\\\"productId\\\":\\\"738067238,753099031,427691503\\\",\\\"supplyScope\\\":\\\"\\\",\\\"marketingCopy\\\":\\\"手动推品+团购\\\",\\\"marketingCopySource\\\":2,\\\"hotTagList\\\":\\\"\\\",\\\"topproductids\\\":\\\"\\\",\\\"headpicUrl\\\":\\\"\\\"}\",\n" + "        \"processOrchestrationId\" : 192,\n" + "        \"processOrchestrationVersion\" : \"1725788339717\",\n" + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"updateTime\" : null,\n" + "        \"attachmentSupplyDetailDTO\" : {\n" + "          \"supplyType\" : 2,\n" + "          \"productId\" : \"738067238,753099031,427691503\",\n" + "          \"productType\" : 1,\n" + "          \"marketingCopy\" : \"手动推品+团购\",\n" + "          \"marketingCopySource\" : 2,\n" + "          \"supplyScope\" : null,\n" + "          \"hotTagList\" : \"\",\n" + "          \"shelfName\" : \"\",\n" + "          \"jumpPageType\" : null,\n" + "          \"jumpUrl\" : \"\",\n" + "          \"topproductids\" : \"\",\n" + "          \"headpicUrl\" : \"\"\n" + "        },\n" + "        \"attachmentContentDetailDTO\" : null\n" + "      } ]\n" + "    }\n" + "  },\n" + "  \"executePlanDTO\" : null\n" + "}";

    String couponMocks = "{\n" 
        + "  \"id\" : 446,\n"
        + "  \"name\" : \"复制QA测试0106-1:定时任务+客户群群发+新增节点\",\n"
        + "  \"processOrchestrationType\" : 4,\n"
        + "  \"cron\" : \"\",\n"
        + "  \"beginTime\" : 1736151000000,\n"
        + "  \"endTime\" : 1736323800000,\n"
        + "  \"status\" : 1,\n"
        + "  \"validVersion\" : \"1736236740459\",\n"
        + "  \"updateTime\" : 1736236740000,\n"
        + "  \"creatorId\" : \"wangyonghao02\",\n"
        + "  \"lastUpdaterId\" : \"wangyonghao02\"\n"
        + "}";

    @Mock
    private MsgUnifiedPushService msgUnifiedPushService;

    private DeepSeaWxHandler handler = new DeepSeaWxHandler();

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        wxInvokeDetailDOList = new ArrayList<>();
        msgResultEvent = new MsgResultEvent();
        msgResultEvent.setBusinessCode("daozongxiuxianwanleguanfang");
        msgResultEvent.setTaskId(1866859861527715900L);
        msgResultEvent.setTaskSource("taskSource");
        msgResultEvent.setBusinessScene(6);
        msgResultEvent.setMsgResultType("msg.result.friend.task");
        msgResultEvent.setMsgTaskDTO(new MsgTaskResultDTO());
        msgResultEvent.getMsgTaskDTO().setDetailCount(2);
        msgResultEvent.getMsgTaskDTO().setSuccessCount(2);
        msgResultEvent.getMsgTaskDTO().setFailCount(0);
        msgResultEvent.getMsgTaskDTO().setFinishTime(0L);
        invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setId(1L);
        invokeLogDO.setProcessOrchestrationId(1L);
        invokeLogDO.setProcessOrchestrationVersion("1.0");
        invokeLogDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
    }

    /**
     * 测试dealDeepSeaWxMessage方法，当contentType为NON_SUPPLY时
     */
    @Test
    public void testDealDeepSeaWxMessageWithContentTypeNonSupply() {
        // arrange
        byte nonSupply = ScrmProcessOrchestrationContentTypeEnum.NON_SUPPLY.getValue().byteValue();
        when(invokeDetailKeyObject.getContentType()).thenReturn(nonSupply);
        AbstractMap.SimpleEntry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(invokeDetailKeyObject, wxInvokeDetailDOList);
        // act
        deepSeaWxHandler.dealDeepSeaWxMessage(processOrchestrationDTO, "executorId", entry, null);
        // assert
        verify(invokeDetailKeyObject, times(1)).getContentType();
    }

    /**
     * 测试dealDeepSeaWxMessage方法，当contentType为SUPPLY时
     */
    @Test
    public void testDealDeepSeaWxMessageWithContentTypeSupply() {
        // arrange
        byte supply = ScrmProcessOrchestrationContentTypeEnum.SUPPLY.getValue().byteValue();
        when(invokeDetailKeyObject.getContentType()).thenReturn(supply);
        AbstractMap.SimpleEntry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(invokeDetailKeyObject, wxInvokeDetailDOList);
        // act
        deepSeaWxHandler.dealDeepSeaWxMessage(processOrchestrationDTO, "executorId", entry, null);
        // assert
        verify(invokeDetailKeyObject, times(2)).getContentType();
    }

    /**
     * 测试dealDeepSeaWxMessage方法，当contentType为未知类型时
     */
    @Test
    public void testDealDeepSeaWxMessageWithUnknownContentType() {
        // arrange
        byte unknownType = 100;
        when(invokeDetailKeyObject.getContentType()).thenReturn(unknownType);
        AbstractMap.SimpleEntry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(invokeDetailKeyObject, wxInvokeDetailDOList);
        // act
        deepSeaWxHandler.dealDeepSeaWxMessage(processOrchestrationDTO, "executorId", entry, null);
        // assert
        verify(invokeDetailKeyObject, times(2)).getContentType();
    }

    /**
     * 测试dealRealTimeDeepSeaWxMessage方法，当totalInvokeDetailDOS为空时，不执行任何操作
     */
    @Test
    public void testDealRealTimeDeepSeaWxMessageWithEmptyTotalInvokeDetailDOS() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = mock(InvokeDetailKeyObject.class);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        // act
        deepSeaWxHandler.dealRealTimeDeepSeaWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        // assert
        verify(appConfigRepository, never()).getConfigByAppId(anyString());
    }

    /**
     * 测试dealRealTimeDeepSeaWxMessage方法，当assistantInfo为null时，不执行任何操作
     */
    @Test
    public void testDealRealTimeDeepSeaWxMessageWithNullAssistantInfo() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationMocks,
                                                                                 ScrmProcessOrchestrationDTO.class);
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = mock(InvokeDetailKeyObject.class);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        totalInvokeDetailDOS.add(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        totalInvokeDetailDOS.get(0).setProcessOrchestrationNodeId(1L);
        totalInvokeDetailDOS.get(0).setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_CREATE.getValue().byteValue());

        when(wxInvokeDetailDOMapper.selectByPrimaryKey(anyLong())).thenReturn(totalInvokeDetailDOS.get(0));
        when(appConfigRepository.getConfigByAppId(anyString())).thenReturn(new CorpAppConfig());

        // act
        deepSeaWxHandler.dealRealTimeDeepSeaWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);

        // assert
        verify(appConfigRepository, times(1)).getConfigByAppId(anyString());
    }

    /**
     * 测试dealRealTimeDeepSeaWxMessage方法，正常情况下的执行流程
     */
    @Test
    public void testDealRealTimeDeepSeaWxMessageNormalFlow() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationMocks,
                                                                                 ScrmProcessOrchestrationDTO.class);
        processOrchestrationDTO.getNodeMediumDTO().getActionContentMap().values().stream().forEach(o-> o.forEach(p -> p.setContent("test")));
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = mock(InvokeDetailKeyObject.class);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        totalInvokeDetailDOS.add(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        totalInvokeDetailDOS.get(0).setProcessOrchestrationNodeId(1725778802191L);
        totalInvokeDetailDOS.get(0).setTargetId("targetId");
        totalInvokeDetailDOS.get(0).setExecuteLogId(1L);
        totalInvokeDetailDOS.get(0).setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_CREATE.getValue().byteValue());

        when(appConfigRepository.getConfigByAppId(anyString())).thenReturn(new CorpAppConfig());
        when(assistantInfo.getAssistantId()).thenReturn(1L);
        when(dsAssistantAcl.getAssistantByAccount(anyString(), anyString())).thenReturn(new AssistantInfo());
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(wxInvokeLogDOMapper.insertSelective(any())).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(),any())).thenReturn(1);
        when(wxInvokeDetailDOMapper.selectByPrimaryKey(anyLong())).thenReturn(totalInvokeDetailDOS.get(0));

        FriendSendMsgResponse friendSendMsgResponse = new FriendSendMsgResponse();
        friendSendMsgResponse.setTaskId(1L);
        friendSendMsgResponse.setContentIdList(Lists.newArrayList(1L));
        when(dsFriendAcl.sendFriendMsgByAI(anyString(), anyLong(), anyString(), anyList())).thenReturn(friendSendMsgResponse);

        // act
        deepSeaWxHandler.dealRealTimeDeepSeaWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);

        // assert
        verify(appConfigRepository, times(1)).getConfigByAppId(anyString());
    }

    /**
     * 测试场景：处理成功结果，消息任务类型为FRIEND_TASK，所有消息发送成功
     */
    @Test
    public void testCheckDeepSeaWxStatus_FriendTaskAllSuccess() {
        // arrange
        msgResultEvent.setMsgResultType(MsgResultTypeTEnum.FRIEND_TASK.getCode());
        MsgTaskResultDTO msgTaskResultDTO = new MsgTaskResultDTO();
        msgTaskResultDTO.setDetailCount(2);
        msgTaskResultDTO.setSuccessCount(2);
        msgResultEvent.setMsgTaskDTO(msgTaskResultDTO);
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> logDOS = Lists.newArrayList(invokeLogDO);
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(logDOS);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOS = new ArrayList<>();
        detailDOS.add(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(detailDOS);
        // act
        deepSeaWxHandler.checkDeepSeaWxStatusSync(msgResultEvent);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any());
    }

    /**
     * 测试场景：处理失败结果，消息任务类型为FRIEND_TASK，部分消息发送失败
     */
    @Test
    public void testCheckDeepSeaWxStatus_FriendTaskPartialSuccess() {
        // arrange
        msgResultEvent.setMsgResultType(MsgResultTypeTEnum.FRIEND_TASK.getCode());
        MsgTaskResultDTO msgTaskResultDTO = new MsgTaskResultDTO();
        msgTaskResultDTO.setDetailCount(3);
        msgTaskResultDTO.setSuccessCount(2);
        msgResultEvent.setMsgTaskDTO(msgTaskResultDTO);
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> logDOS = Lists.newArrayList(invokeLogDO);
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(logDOS);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOS = new ArrayList<>();
        detailDOS.add(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(detailDOS);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(wxInvokeLogDOMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        doNothing().when(informationGatheringService).logUserStatusChangeDetail(any(), any());
        // act
        deepSeaWxHandler.checkDeepSeaWxStatusSync(msgResultEvent);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any());
    }

    /**
     * 测试场景：处理成功结果，消息任务类型为GROUP_TASK，所有消息发送成功
     */
    @Test
    public void testCheckDeepSeaWxStatus_GroupTaskAllSuccess() {
        // arrange
        msgResultEvent.setMsgResultType(MsgResultTypeTEnum.GROUP_TASK.getCode());
        MsgTaskResultDTO msgTaskResultDTO = new MsgTaskResultDTO();
        msgTaskResultDTO.setDetailCount(2);
        msgTaskResultDTO.setSuccessCount(2);
        msgResultEvent.setMsgTaskDTO(msgTaskResultDTO);
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> logDOS = Lists.newArrayList(invokeLogDO);
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(logDOS);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOS = new ArrayList<>();
        detailDOS.add(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(detailDOS);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(wxInvokeLogDOMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        // act
        deepSeaWxHandler.checkDeepSeaWxStatusSync(msgResultEvent);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any());
    }

    /**
     * 测试场景：处理失败结果，消息任务类型为GROUP_TASK，部分消息发送失败
     */
    @Test
    public void testCheckDeepSeaWxStatus_GroupTaskPartialSuccess() {
        // arrange
        msgResultEvent.setMsgResultType(MsgResultTypeTEnum.GROUP_TASK.getCode());
        MsgTaskResultDTO msgTaskResultDTO = new MsgTaskResultDTO();
        msgTaskResultDTO.setDetailCount(3);
        msgTaskResultDTO.setSuccessCount(2);
        msgResultEvent.setMsgTaskDTO(msgTaskResultDTO);
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> logDOS = Lists.newArrayList(invokeLogDO);
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(logDOS);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOS = new ArrayList<>();
        detailDOS.add(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(detailDOS);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(wxInvokeLogDOMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        // act
        deepSeaWxHandler.checkDeepSeaWxStatusSync(msgResultEvent);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any());
    }

    /**
     * 测试场景：无匹配的消息任务类型
     */
    @Test
    public void testCheckDeepSeaWxStatus_NoMatchingMsgResultType() {
        // arrange
        msgResultEvent.setMsgResultType("NO_MATCH");
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> logDOS = Lists.newArrayList(invokeLogDO);
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(logDOS);
        // act
        deepSeaWxHandler.checkDeepSeaWxStatus("", msgResultEvent);
        // assert
        verify(wxInvokeLogDOMapper, never()).updateByPrimaryKey(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
        verify(wxInvokeDetailDOMapper, never()).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * 测试场景：消息任务类型为FRIEND_TASK，但没有找到对应的日志记录
     */
    @Test
    public void testCheckDeepSeaWxStatus_FriendTaskNoLogFound() {
        // arrange
        msgResultEvent.setMsgResultType(MsgResultTypeTEnum.FRIEND_TASK.getCode());
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        // act
        deepSeaWxHandler.checkDeepSeaWxStatus("", msgResultEvent);
        // assert
        verify(wxInvokeLogDOMapper, never()).updateByPrimaryKey(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
        verify(wxInvokeDetailDOMapper, never()).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * 测试场景：消息任务类型为GROUP_TASK，但没有找到对应的日志记录
     */
    @Test
    public void testCheckDeepSeaWxStatus_GroupTaskNoLogFound() {
        // arrange
        msgResultEvent.setMsgResultType(MsgResultTypeTEnum.GROUP_TASK.getCode());
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        // act
        deepSeaWxHandler.checkDeepSeaWxStatus("", msgResultEvent);
        // assert
        verify(wxInvokeLogDOMapper, never()).updateByPrimaryKey(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
        verify(wxInvokeDetailDOMapper, never()).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * 测试场景：消息任务类型为FRIEND_TASK，找到日志记录但没有对应的详细记录
     */
    @Test
    public void testCheckDeepSeaWxStatus_FriendTaskLogFoundNoDetail() {
        // arrange
        msgResultEvent.setMsgResultType(MsgResultTypeTEnum.FRIEND_TASK.getCode());
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> logDOS = Lists.newArrayList(invokeLogDO);
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(logDOS);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        // act
        deepSeaWxHandler.checkDeepSeaWxStatus("", msgResultEvent);
        // assert
        verify(wxInvokeLogDOMapper, never()).updateByPrimaryKey(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
        verify(wxInvokeDetailDOMapper, never()).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * 测试场景：消息任务类型为GROUP_TASK，找到日志记录但没有对应的详细记录
     */
    @Test
    public void testCheckDeepSeaWxStatus_GroupTaskLogFoundNoDetail() {
        // arrange
        msgResultEvent.setMsgResultType(MsgResultTypeTEnum.GROUP_TASK.getCode());
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> logDOS = Lists.newArrayList(invokeLogDO);
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(logDOS);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        // act
        deepSeaWxHandler.checkDeepSeaWxStatus("", msgResultEvent);
        // assert
        verify(wxInvokeLogDOMapper, never()).updateByPrimaryKey(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
        verify(wxInvokeDetailDOMapper, never()).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * 测试dealFailedResult方法，当wxInvokeDetailDOMapper.selectByExample抛出异常时
     */
    @Test
    public void testDealFailedResultWhenSelectByExampleThrowsException() {
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        MsgResultEvent msgResultEvent = new MsgResultEvent();
        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenThrow(new RuntimeException());
        assertThrows(RuntimeException.class, () -> {
            deepSeaWxHandler.dealFailedResult(invokeLogDO, msgResultEvent);
        });
        // 由于期望抛出异常，所以不需要断言
    }

    /**
     * 测试dealFailedResult方法，当wxInvokeDetailDOMapper.updateByExampleSelective抛出异常时
     */
    @Test
    public void testDealFailedResultWhenUpdateByExampleSelectiveThrowsException() {
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        MsgResultEvent msgResultEvent = new MsgResultEvent();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(1L);
        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(Collections.singletonList(detailDO));
        doThrow(new RuntimeException()).when(wxInvokeDetailDOMapper).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        assertThrows(RuntimeException.class, () -> {
            deepSeaWxHandler.dealFailedResult(invokeLogDO, msgResultEvent);
        });
        // 由于期望抛出异常，所以不需要断言
    }

    /**
     * 测试dealFailedResult方法，当failedDetailDOList为空时，方法应该直接返回，不进行任何数据库操作
     */
    @Test
    public void testDealFailedResultWithEmptyFailedDetailDOList() {
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setProcessOrchestrationId(1L);
        invokeLogDO.setProcessOrchestrationVersion("1.0");
        invokeLogDO.setId(1L);
        MsgResultEvent msgResultEvent = new MsgResultEvent();
        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(Collections.emptyList());
        deepSeaWxHandler.dealFailedResult(invokeLogDO, msgResultEvent);
        verify(wxInvokeDetailDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verifyNoMoreInteractions(executeLogDOMapper);
    }

    /**
     * 测试dealFailedResult方法，当failedDetailDOList非空时，应该执行更新操作
     */
    @Test
    public void testDealFailedResultWithNonEmptyFailedDetailDOList() {
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setProcessOrchestrationId(1L);
        invokeLogDO.setProcessOrchestrationVersion("1.0");
        invokeLogDO.setId(1L);
        MsgResultEvent msgResultEvent = new MsgResultEvent();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(1L);
        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(Collections.singletonList(detailDO));
        deepSeaWxHandler.dealFailedResult(invokeLogDO, msgResultEvent);
        verify(wxInvokeDetailDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class));
    }

    /**
     * 测试dealSuccessResult方法，当没有待发送的详情时
     */
    @Test
    public void testDealSuccessResultWhenNoDetails() {
        // arrange
        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(Collections.emptyList());
        // act
        deepSeaWxHandler.dealSuccessResult(invokeLogDO, msgResultEvent);
        // assert
        verify(wxInvokeDetailDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verifyNoMoreInteractions(wxInvokeDetailDOMapper, executeLogDOMapper, informationGatheringService);
    }

    /**
     * 测试dealSuccessResult方法，当存在待发送的详情时
     */
    @Test
    public void testDealSuccessResultWithDetails() {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setId(1L);
        detailDO.setExecuteLogId(1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOList = Arrays.asList(detailDO);
        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(detailDOList);
        // act
        deepSeaWxHandler.dealSuccessResult(invokeLogDO, msgResultEvent);
        // assert
        verify(wxInvokeDetailDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(informationGatheringService, times(1)).logUserStatusChangeDetail(eq(detailDOList), eq(ProcessOrchestrationInformationGatheringEnum.MESSAGE_RECEIVED));
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class));
    }

    /**
     * 测试dealSuccessResult方法，当更新详情状态时发生异常
     */
    @Test
    public void testDealSuccessResultWhenUpdateDetailThrowsException() {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setId(1L);
        detailDO.setExecuteLogId(1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOList = Arrays.asList(detailDO);
        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(detailDOList);
        doThrow(RuntimeException.class).when(wxInvokeDetailDOMapper).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        // act
        // assert
        // Expected exception
        assertThrows(RuntimeException.class, () -> {
            deepSeaWxHandler.dealSuccessResult(invokeLogDO, msgResultEvent);
        });
    }

    /**
     * 测试checkMessageNotSend方法，当传入的detailDO为null时
     */
    @Test
    public void testCheckMessageNotSendWithNullDetailDO() {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = null;
        // act
        boolean result = deepSeaWxHandler.checkMessageNotSend(detailDO);
        // assert
        assertFalse("当detailDO为null时，应返回false", result);
    }

    /**
     * 测试checkMessageNotSend方法，当数据库中找不到对应记录时
     */
    @Test
    public void testCheckMessageNotSendWithNonExistentRecord() {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setId(1L);
        when(wxInvokeDetailDOMapper.selectByPrimaryKey(detailDO.getId())).thenReturn(null);
        // act
        boolean result = deepSeaWxHandler.checkMessageNotSend(detailDO);
        // assert
        assertFalse("当数据库中找不到对应记录时，应返回false", result);
    }

    /**
     * 测试checkMessageNotSend方法，当记录状态不是WAIT_FOR_CREATE时
     */
    @Test
    public void testCheckMessageNotSendWithStatusNotWaitForCreate() {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setId(1L);
        ScrmAmProcessOrchestrationWxInvokeDetailDO dbDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        // 任意非WAIT_FOR_CREATE的状态
        dbDetailDO.setStatus((byte) 99);
        when(wxInvokeDetailDOMapper.selectByPrimaryKey(detailDO.getId())).thenReturn(dbDetailDO);
        // act
        boolean result = deepSeaWxHandler.checkMessageNotSend(detailDO);
        // assert
        assertFalse("当记录状态不是WAIT_FOR_CREATE时，应返回false", result);
    }

    /**
     * 测试checkMessageNotSend方法，当记录状态是WAIT_FOR_CREATE时
     */
    @Test
    public void testCheckMessageNotSendWithStatusWaitForCreate() {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setId(1L);
        ScrmAmProcessOrchestrationWxInvokeDetailDO dbDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        dbDetailDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_CREATE.getValue().byteValue());
        when(wxInvokeDetailDOMapper.selectByPrimaryKey(detailDO.getId())).thenReturn(dbDetailDO);
        // act
        boolean result = deepSeaWxHandler.checkMessageNotSend(detailDO);
        // assert
        assertTrue("当记录状态是WAIT_FOR_CREATE时，应返回true", result);
    }


    /**
     * 测试getCouponPageAttachmentVO方法，当supplyDetailDTO为null时
     */
    @Test
    public void testGetCouponPageAttachmentVOWhenSupplyDetailDTOIsNull() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = mock(ScrmProcessOrchestrationActionAttachmentDTO.class);
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = null;
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = mock(ScrmAmProcessOrchestrationProductActivityPageDO.class);
        // act
        // assert
        // Expecting a NullPointerException to be thrown
        assertThrows(RuntimeException.class, () -> {
            deepSeaWxHandler.getCouponPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo);
        });
    }



    @Test
    public void testDealRealTimeDeepSeaWxMessageV2() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationMocks,
                ScrmProcessOrchestrationDTO.class);
        processOrchestrationDTO.getNodeMediumDTO().getActionContentMap().values().stream().forEach(o-> o.forEach(p -> p.setContent("test")));
        List<String> executorId = Lists.newArrayList("executorId", "2");
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("type", (byte) 1, (byte) 1, 1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        totalInvokeDetailDOS.add(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        totalInvokeDetailDOS.get(0).setProcessOrchestrationNodeId(1725778802191L);
        totalInvokeDetailDOS.get(0).setTargetId("targetId");
        totalInvokeDetailDOS.get(0).setExecuteLogId(1L);
        totalInvokeDetailDOS.get(0).setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_CREATE.getValue().byteValue());

        when(appConfigRepository.getConfigByAppId(anyString())).thenReturn(new CorpAppConfig());
        when(assistantInfo.getAssistantId()).thenReturn(1L);
        when(dsAssistantAcl.getAssistantByAccount(anyString(), anyString())).thenReturn(new AssistantInfo());
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(wxInvokeLogDOMapper.insertSelective(any())).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(),any())).thenReturn(1);
        when(wxInvokeDetailDOMapper.selectByPrimaryKey(anyLong())).thenReturn(totalInvokeDetailDOS.get(0));

        FriendSendMsgResponse friendSendMsgResponse = new FriendSendMsgResponse();
        friendSendMsgResponse.setTaskId(1L);
        friendSendMsgResponse.setContentIdList(Lists.newArrayList(1L));
        when(dsFriendAcl.sendFriendMsgByAI(anyString(), anyLong(), anyString(), anyList())).thenReturn(friendSendMsgResponse);

        deepSeaWxHandler.dealRealTimeDeepSeaWxMessageV2(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        verify(msgUnifiedPushService, never()).saveMsgPushTask(any(MsgPushRequest.class));
    }

    /**
     * Test building image message content
     */
    @Test
    public void testBuildOfficialMsgDTOV2ImageType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachment = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachment.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.PICTURE.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetail = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetail.setPicUrl("http://example.com/image.jpg");
        attachment.setAttachmentContentDetailDTO(contentDetail);
        // act
        MsgPushContentDTO result = handler.buildOfficialMsgDTOV2(attachment);
        // assert
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.IMAGE, result.getContentTypeTEnum());
        assertNotNull(result.getImageDTO());
        assertEquals("http://example.com/image.jpg", result.getImageDTO().getUrl());
    }

    /**
     * Test building link message content
     */
    @Test
    public void testBuildOfficialMsgDTOV2LinkType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachment = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachment.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.LINK.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetail = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetail.setContentUrl("http://example.com");
        contentDetail.setTitle("Example Title");
        contentDetail.setPicUrl("http://example.com/thumb.jpg");
        contentDetail.setDesc("Example Description");
        attachment.setAttachmentContentDetailDTO(contentDetail);
        // act
        MsgPushContentDTO result = handler.buildOfficialMsgDTOV2(attachment);
        // assert
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.LINK, result.getContentTypeTEnum());
        assertNotNull(result.getLinkDTO());
        assertEquals("http://example.com", result.getLinkDTO().getUrl());
        assertEquals("Example Title", result.getLinkDTO().getTitle());
        assertEquals("http://example.com/thumb.jpg", result.getLinkDTO().getThumbUrl());
        assertEquals("Example Description", result.getLinkDTO().getDescription());
    }

    /**
     * Test building mini-program message content
     */
    @Test
    public void testBuildOfficialMsgDTOV2MiniProgramType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachment = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachment.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.MINI_PROGRAM.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetail = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetail.setOriginAppId("originAppId");
        contentDetail.setAppId("appId");
        contentDetail.setDesc("Example Description");
        contentDetail.setPicUrl("http://example.com/thumb.jpg");
        contentDetail.setContentUrl("pages/index/index");
        attachment.setAttachmentContentDetailDTO(contentDetail);
        // act
        MsgPushContentDTO result = handler.buildOfficialMsgDTOV2(attachment);
        // assert
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.MINI_PROGRAM, result.getContentTypeTEnum());
        assertNotNull(result.getMiniProgramDTO());
        assertEquals("originAppId", result.getMiniProgramDTO().getOriginAppId());
        assertEquals("appId", result.getMiniProgramDTO().getAppId());
        assertEquals("Example Description", result.getMiniProgramDTO().getDescription());
        assertEquals("http://example.com/thumb.jpg", result.getMiniProgramDTO().getThumbnail());
        assertEquals("pages/index/index", result.getMiniProgramDTO().getPagePath());
    }

    /**
     * Test building video message content
     */
    @Test
    public void testBuildOfficialMsgDTOV2VideoType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachment = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachment.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.VIDEO.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetail = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetail.setContentUrl("http://example.com/video.mp4");
        attachment.setAttachmentContentDetailDTO(contentDetail);
        // act
        MsgPushContentDTO result = handler.buildOfficialMsgDTOV2(attachment);
        // assert
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.VIDEO, result.getContentTypeTEnum());
        assertNotNull(result.getVideoDTO());
        assertEquals("http://example.com/video.mp4", result.getVideoDTO().getUrl());
    }

    /**
     * Test building text message content
     */
    @Test
    public void testBuildOfficialMsgDTOV2TextType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachment = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachment.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.TEXT.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetail = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetail.setContentUrl("Example text content");
        attachment.setAttachmentContentDetailDTO(contentDetail);
        // act
        MsgPushContentDTO result = handler.buildOfficialMsgDTOV2(attachment);
        // assert
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.TEXT, result.getContentTypeTEnum());
        assertNotNull(result.getTextDTO());
        assertEquals("Example text content", result.getTextDTO().getContent());
    }

    /**
     * Test null input handling
     */
    @Test
    public void testBuildOfficialMsgDTOV2NullInput() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachment = null;
        // act
        // assert
        // The test will pass if a NullPointerException is thrown
        assertThrows(NullPointerException.class, () -> {
            handler.buildOfficialMsgDTOV2(attachment);
        });
    }

    /**
     * Test invalid attachment type handling
     */
    @Test
    public void testBuildOfficialMsgDTOV2InvalidType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO attachment = new ScrmProcessOrchestrationActionAttachmentDTO();
        // Invalid type
        attachment.setAttachmentTypeId(-1);
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetail = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        attachment.setAttachmentContentDetailDTO(contentDetail);
        // act
        MsgPushContentDTO result = handler.buildOfficialMsgDTOV2(attachment);
        // assert
        assertNull(result);
    }

    @Test
    void testBuildAiSceneProductPushContent_GeneralProduct_Success() throws UnsupportedEncodingException {
        // 准备测试数据
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(123L);
        processOrchestrationDTO.setValidVersion("1.0");
        processOrchestrationDTO.setAppId("test_app_id");
        processOrchestrationDTO.setAiScene(true);
        
        IntelligentFollowResultDTO aiSceneContent = new IntelligentFollowResultDTO();
        aiSceneContent.setProductId(123L);
        processOrchestrationDTO.setAiSceneContent(aiSceneContent);

        // Mock getDefaultProductType 返回一般商品类型
        when(productManagementService.getDefaultProductType(123L, "test_app_id"))
            .thenReturn(1); // 1 表示一般商品

        // Mock queryProductInfoDTOS 返回商品信息
        List<Long> productIds = Collections.singletonList(123L);
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProductId(123L);
        productInfoDTO.setProductTitle("测试商品");
        productInfoDTO.setHeadPic("http://test.com/pic.jpg");
        productInfoDTO.setH5Url("http://test.com/h5");
        productInfoDTO.setProductBasePrice(new BigDecimal("100.00"));
        
        when(productManagementService.queryProductInfoDTOS(eq(productIds)))
            .thenReturn(Collections.singletonList(productInfoDTO));

        // Mock queryCommunityDistributor
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class)))
            .thenReturn("test_distributor_code");

        // Mock ShortLinkUtils to prevent NullPointerException
        when(shortLinkUtils.getShortLink(anyString(), anyInt()))
            .thenReturn("http://short.url/mock_general");

        // 执行测试
        MsgPushContentDTO result = deepSeaWxHandler.buildAiSceneProductPushContent(processOrchestrationDTO, 1L);

        // 验证结果
        assertNotNull("结果不应为空", result);
        assertEquals("消息类型应为小程序", ContentTypeTEnum.MINI_PROGRAM, result.getContentTypeTEnum());
        assertNotNull("小程序DTO不应为空", result.getMiniProgramDTO());
        assertEquals("商品标题不匹配", "测试商品", result.getMiniProgramDTO().getDescription());
        assertEquals("商品图片不匹配", "http://test.com/pic.jpg", result.getMiniProgramDTO().getThumbnail());
        assertEquals("原始AppID不匹配", GroupDynamicCodeConstants.ORIGIN_MX_MINIP_APPID, result.getMiniProgramDTO().getOriginAppId());
        assertEquals("AppID不匹配", GroupDynamicCodeConstants.MX_MINIP_APPID, result.getMiniProgramDTO().getAppId());
        
        String pagePath = result.getMiniProgramDTO().getPagePath();
        assertNotNull("页面路径不应为空", pagePath);
        
        // 检查页面路径是否包含必要的参数
        String decodedPath = java.net.URLDecoder.decode(pagePath, "UTF-8");
        assertTrue("页面路径应包含weburl参数", decodedPath.contains("weburl="));
    }

    @Test
    void testBuildAiSceneProductPushContent_SpecialProduct_Success() throws UnsupportedEncodingException {
        // 准备测试数据
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(123L);
        processOrchestrationDTO.setValidVersion("1.0");
        processOrchestrationDTO.setAppId("test_app_id");
        processOrchestrationDTO.setAiScene(true);
        
        IntelligentFollowResultDTO aiSceneContent = new IntelligentFollowResultDTO();
        aiSceneContent.setProductId(123L);
        processOrchestrationDTO.setAiSceneContent(aiSceneContent);

        // Mock getDefaultProductType 返回特殊商品类型
        when(productManagementService.getDefaultProductType(123L, "test_app_id"))
            .thenReturn(2); // 2 表示特殊商品

        // Mock queryProductInfoDTOS 返回商品信息
        List<Long> productIds = Collections.singletonList(123L);
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProductId(123L);
        productInfoDTO.setProductTitle("测试特殊商品");
        productInfoDTO.setHeadPic("http://test.com/special_pic.jpg");
        productInfoDTO.setH5Url("http://test.com/special_h5");
        productInfoDTO.setProductBasePrice(new BigDecimal("200.00"));
        
        when(productManagementService.queryProductInfoDTOS(eq(productIds)))
            .thenReturn(Collections.singletonList(productInfoDTO));

        // Mock queryCommunityDistributor
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class)))
            .thenReturn("test_special_distributor_code");

        // Mock ShortLinkUtils to prevent NullPointerException
        when(shortLinkUtils.getShortLink(anyString(), anyInt()))
            .thenReturn("http://short.url/mock_special");

        // 执行测试
        MsgPushContentDTO result = deepSeaWxHandler.buildAiSceneProductPushContent(processOrchestrationDTO, 1L);

        // 验证结果
        assertNotNull("结果不应为空", result);
        assertEquals("消息类型应为小程序", ContentTypeTEnum.MINI_PROGRAM, result.getContentTypeTEnum());
        assertNotNull("小程序DTO不应为空", result.getMiniProgramDTO());
        assertEquals("商品标题不匹配", "测试特殊商品", result.getMiniProgramDTO().getDescription());
        assertEquals("商品图片不匹配", "http://test.com/special_pic.jpg", result.getMiniProgramDTO().getThumbnail());
        assertEquals("原始AppID不匹配", GroupDynamicCodeConstants.ORIGIN_MX_MINIP_APPID, result.getMiniProgramDTO().getOriginAppId());
        assertEquals("AppID不匹配", GroupDynamicCodeConstants.MX_MINIP_APPID, result.getMiniProgramDTO().getAppId());
        
        String pagePath = result.getMiniProgramDTO().getPagePath();
        assertNotNull("页面路径不应为空", pagePath);
        
        // 检查页面路径是否包含必要的参数
        String decodedPath = java.net.URLDecoder.decode(pagePath, "UTF-8");
        assertTrue("页面路径应包含weburl参数", decodedPath.contains("weburl="));
    }

    @Test
    void testBuildAiSceneProductPushContent_ProductInfoNotFound() {
        // 准备测试数据
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(123L);
        processOrchestrationDTO.setValidVersion("1.0");
        processOrchestrationDTO.setAppId("test_app_id");
        processOrchestrationDTO.setAiScene(true);
        
        IntelligentFollowResultDTO aiSceneContent = new IntelligentFollowResultDTO();
        aiSceneContent.setProductId(123L);
        processOrchestrationDTO.setAiSceneContent(aiSceneContent);

        // Mock getDefaultProductType 返回一般商品类型
        when(productManagementService.getDefaultProductType(123L, "test_app_id"))
            .thenReturn(1); // 1 表示一般商品

        // Mock queryProductInfoDTOS 返回空列表
        List<Long> productIds = Collections.singletonList(123L);
        // 修改为单参数版本的 mock
        when(productManagementService.queryProductInfoDTOS(eq(productIds)))
            .thenReturn(Collections.emptyList());

        // 执行测试
        MsgPushContentDTO result = deepSeaWxHandler.buildAiSceneProductPushContent(processOrchestrationDTO, 1L);

        // 验证结果
        assertNull("如果商品信息未找到，方法应返回 null", result);
    }

    @Test
    void testGetAiSceneActivPageMsgPushContentDTO_EmptyAttachmentList() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        List<ScrmProcessOrchestrationActionAttachmentDTO> actionAttachmentDTOList = new ArrayList<>();
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> partWxInvokeDetailDOS = new ArrayList<>();
        IntelligentFollowResultDTO aiSceneContent = new IntelligentFollowResultDTO();

        // act
        MsgPushContentDTO result = deepSeaWxHandler.getAiSceneActivPageMsgPushContentDTO(
                processOrchestrationDTO,
                executeManagementDTO,
                actionAttachmentDTOList,
                partWxInvokeDetailDOS,
                aiSceneContent
        );

        // assert
        assertNull("当 actionAttachmentDTOList 为空时应返回 null", result);
    }

    @Test
    void testGetAiSceneActivPageMsgPushContentDTO_InsertRecordFailed() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("test_app_id");
        
        // 初始化 aiSceneContent
        IntelligentFollowResultDTO aiSceneContent = new IntelligentFollowResultDTO();
        aiSceneContent.setShopId(123L);
        aiSceneContent.setCouponId("456");
        processOrchestrationDTO.setAiSceneContent(aiSceneContent);
        
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        
        // 准备 actionAttachmentDTO
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTitle("测试标题");
        supplyDetailDTO.setHeadpicUrl("http://test.com/image.jpg");
        actionAttachmentDTO.setAttachmentSupplyDetailDTO(supplyDetailDTO);
        List<ScrmProcessOrchestrationActionAttachmentDTO> actionAttachmentDTOList = Collections.singletonList(actionAttachmentDTO);
        
        // 准备 invokeDetailDO
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO.setExecuteLogId(1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> partWxInvokeDetailDOS = Collections.singletonList(invokeDetailDO);
        
        // 准备 executeLogDO
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setTargetMtUserId(1L);
        executeLogDO.setAppId("test_app_id");
        
        // 准备 sceneCodeDO
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneCode("test_scene_code");
        sceneCodeDO.setPoiRestrict(0);

        // Mock 依赖
        when(executeLogDOMapper.selectByPrimaryKey(1L)).thenReturn(executeLogDO);
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class)))
                .thenReturn("test_distributor");
        when(productManagementService.getActivSceneCodeDO(
                eq(actionAttachmentDTO),
                eq(supplyDetailDTO),
                isNull(),
                eq("test_distributor"),
                eq("test_app_id")
        )).thenReturn(sceneCodeDO);
        when(scrmGroupRetailUserCouponRecordDomainService.insertRecord(
                eq(1L),
                eq("test_app_id"),
                eq("456"),
                eq(2),
                eq(123L),
                eq("test_scene_code")
        )).thenReturn(0); // 模拟插入记录失败

        when(shortLinkUtils.getShortLink(anyString(), anyInt()))
                .thenReturn("http://short.url/mock_general");
        // act
        MsgPushContentDTO result = deepSeaWxHandler.getAiSceneActivPageMsgPushContentDTO(
                processOrchestrationDTO,
                executeManagementDTO,
                actionAttachmentDTOList,
                partWxInvokeDetailDOS,
                aiSceneContent
        );

        // assert
        assertNotNull("当 insertRecord 失败时正常推送", result);
        verify(scrmGroupRetailUserCouponRecordDomainService).insertRecord(
                eq(1L),
                eq("test_app_id"),
                eq("456"),
                eq(2),
                eq(123L),
                eq("test_scene_code")
        );
    }

    @Test
    void testGetAiSceneActivPageMsgPushContentDTO_Success() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("test_app_id");
        
        // 初始化 aiSceneContent 并设置到 processOrchestrationDTO
        IntelligentFollowResultDTO aiSceneContent = new IntelligentFollowResultDTO();
        aiSceneContent.setShopId(123L);
        aiSceneContent.setCouponId("456");
        processOrchestrationDTO.setAiSceneContent(aiSceneContent);
        
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        
        // 准备 actionAttachmentDTO
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTitle("测试标题");
        supplyDetailDTO.setHeadpicUrl("http://test.com/image.jpg");
        actionAttachmentDTO.setAttachmentSupplyDetailDTO(supplyDetailDTO);
        List<ScrmProcessOrchestrationActionAttachmentDTO> actionAttachmentDTOList = Collections.singletonList(actionAttachmentDTO);
        
        // 准备 invokeDetailDO
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO.setExecuteLogId(1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> partWxInvokeDetailDOS = Collections.singletonList(invokeDetailDO);
        
        // 准备 executeLogDO
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setTargetMtUserId(1L);
        executeLogDO.setAppId("test_app_id");
        
        // 准备 sceneCodeDO
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneCode("test_scene_code");

        // Mock 依赖
        when(executeLogDOMapper.selectByPrimaryKey(1L)).thenReturn(executeLogDO);
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class)))
                .thenReturn("test_distributor");
        when(productManagementService.getActivSceneCodeDO(
                eq(actionAttachmentDTO),
                eq(supplyDetailDTO),
                isNull(),
                eq("test_distributor"),
                eq("test_app_id")
        )).thenReturn(sceneCodeDO);
        when(scrmGroupRetailUserCouponRecordDomainService.insertRecord(
                eq(1L),
                eq("test_app_id"),
                eq("456"),
                eq(2),
                eq(123L),
                eq("test_scene_code")
        )).thenReturn(1); // 模拟插入记录成功
        
        // Mock buildPageUrl 方法
        DeepSeaWxHandler spyHandler = spy(deepSeaWxHandler);
        doReturn("test_page_url").when(spyHandler).buildPageUrl(
                eq(sceneCodeDO),
                eq("test_distributor"),
                eq("test_app_id"),
                eq(false),
                eq(false),
                eq(executeManagementDTO)
        );

        // act
        MsgPushContentDTO result = spyHandler.getAiSceneActivPageMsgPushContentDTO(
                processOrchestrationDTO,
                executeManagementDTO,
                actionAttachmentDTOList,
                partWxInvokeDetailDOS,
                aiSceneContent
        );

        // assert
        assertNotNull("返回结果不应为 null", result);
        assertEquals("消息类型应为小程序", ContentTypeTEnum.MINI_PROGRAM, result.getContentTypeTEnum());
        assertNotNull("小程序DTO不应为 null", result.getMiniProgramDTO());
        assertEquals("原始AppID不匹配", GroupDynamicCodeConstants.ORIGIN_MX_MINIP_APPID, result.getMiniProgramDTO().getOriginAppId());
        assertEquals("AppID不匹配", GroupDynamicCodeConstants.MX_MINIP_APPID, result.getMiniProgramDTO().getAppId());
        assertEquals("描述不匹配", "测试标题", result.getMiniProgramDTO().getDescription());
        assertEquals("缩略图不匹配", "http://test.com/image.jpg", result.getMiniProgramDTO().getThumbnail());
        assertEquals("页面路径不匹配", "test_page_url", result.getMiniProgramDTO().getPagePath());
        
        // 验证调用
        verify(executeLogDOMapper).selectByPrimaryKey(1L);
        verify(informationGatheringService).queryCommunityDistributor(any(BizDistributorServiceKeyObject.class));
        verify(productManagementService).getActivSceneCodeDO(
                eq(actionAttachmentDTO),
                eq(supplyDetailDTO),
                isNull(),
                eq("test_distributor"),
                eq("test_app_id")
        );
        verify(scrmGroupRetailUserCouponRecordDomainService).insertRecord(
                eq(1L),
                eq("test_app_id"),
                eq("456"),
                eq(2),
                eq(123L),
                eq("test_scene_code")
        );
    }
}
