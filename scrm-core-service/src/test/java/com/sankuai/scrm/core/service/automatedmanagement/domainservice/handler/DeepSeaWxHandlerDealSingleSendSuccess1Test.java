package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

class DeepSeaWxHandlerDealSingleSendSuccess1Test {

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @InjectMocks
    private DeepSeaWxHandler deepSeaWxHandler;

    private Method dealSingleSendSuccessMethod;

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        dealSingleSendSuccessMethod = DeepSeaWxHandler.class.getDeclaredMethod("dealSingleSendSuccess", String.class, Long.class, ScrmAmProcessOrchestrationWxInvokeLogDO.class, String.class);
        dealSingleSendSuccessMethod.setAccessible(true);
    }

    /**
     * Test normal execution where all operations complete successfully.
     */
    @Test
    void testDealSingleSendSuccessNormalExecution() throws Throwable {
        // arrange
        String executorId = "executorId";
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(1L);
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        String taskIdStr = "taskId";
        when(wxInvokeLogDOMapper.insertSelective(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class))).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class))).thenReturn(1);
        // act
        dealSingleSendSuccessMethod.invoke(deepSeaWxHandler, executorId, detailDO.getExecuteLogId(), wxInvokeLogDO, taskIdStr);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).insertSelective(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class));
    }

    /**
     * Test database insert failure when inserting wxInvokeLogDO.
     */
    @Test
    void testDealSingleSendSuccessDatabaseInsertFailure() throws Throwable {
        // arrange
        String executorId = "executorId";
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(1L);
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        String taskIdStr = "taskId";
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class))).thenReturn(1);
        // act
        dealSingleSendSuccessMethod.invoke(deepSeaWxHandler, executorId, detailDO.getExecuteLogId(), wxInvokeLogDO, taskIdStr);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).insertSelective(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class));
    }

    /**
     * Test database update failure when updating wxInvokeDetailDO.
     */
    @Test
    void testDealSingleSendSuccessDatabaseUpdateFailure() throws Throwable {
        // arrange
        String executorId = "executorId";
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(1L);
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        String taskIdStr = "taskId";
        when(wxInvokeLogDOMapper.insertSelective(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class))).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(0);
        when(executeLogDOMapper.updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class))).thenReturn(1);
        // act
        dealSingleSendSuccessMethod.invoke(deepSeaWxHandler, executorId, detailDO.getExecuteLogId(), wxInvokeLogDO, taskIdStr);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).insertSelective(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class));
    }

    /**
     * Test null inputs.
     */
    @Test
    void testDealSingleSendSuccessNullInputs() throws Throwable {
        // arrange
        String executorId = null;
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = null;
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = null;
        String taskIdStr = null;
        // act & assert
        org.junit.jupiter.api.Assertions.assertThrows(Exception.class, () -> {
            dealSingleSendSuccessMethod.invoke(deepSeaWxHandler, executorId, null, wxInvokeLogDO, taskIdStr);
        });
    }
}
