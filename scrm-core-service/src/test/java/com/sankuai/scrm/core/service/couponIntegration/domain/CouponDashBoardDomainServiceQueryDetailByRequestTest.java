package com.sankuai.scrm.core.service.couponIntegration.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.couponIntegration.dto.CouponDataDetailDTO;
import com.sankuai.dz.srcm.couponIntegration.enums.CreationSceneEnum;
import com.sankuai.dz.srcm.couponIntegration.enums.StatusEnum;
import com.sankuai.dz.srcm.couponIntegration.request.CouponDataDetailDownloadRequest;
import com.sankuai.dz.srcm.couponIntegration.request.CouponDataDetailQueryRequest;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmCouponDataSummaryDO;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmCouponDataSummaryDOMapper;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.couponIntegration.utils.DateUtils;
import com.sankuai.scrm.core.service.dashboard.domain.Utils;
import com.sankuai.scrm.core.service.infrastructure.acl.sso.SsosvOpenApi;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CouponDashBoardDomainServiceQueryDetailByRequestTest {

    @InjectMocks
    private CouponDashBoardDomainService couponDashBoardDomainService;

    @Mock
    private SsosvOpenApi ssosvOpenApi;

    @Mock
    private Utils utils;

    @Mock
    private ScrmSceneCouponRecordsMapper sceneCouponRecordDOMapper;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private ScrmCouponDataSummaryDOMapper couponDataSummaryDOMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test case for null request
     */
    @Test
    public void testQueryDetailByRequestWithNullRequest() throws Throwable {
        // arrange
        CouponDataDetailDownloadRequest request = null;
        // act
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 corpId 为空的情况
     */
    /**
     * Test case for empty result from database
     */
    @Test
    public void testQueryDetailByRequestWithEmptyResult() throws Throwable {
        // arrange
        CouponDataDetailDownloadRequest request = new CouponDataDetailDownloadRequest();
        request.setAccessToken("mockToken");
        request.setPathAppId("mockAppId");
        when(sceneCouponRecordDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        // act
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for successful query and conversion
     */
    @Test
    public void testQueryDetailByRequestSuccessful() throws Throwable {
        // arrange
        CouponDataDetailDownloadRequest request = new CouponDataDetailDownloadRequest();
        request.setAccessToken("mockToken");
        request.setPathAppId("mockAppId");
        List<ScrmSceneCouponRecords> mockRecords = new ArrayList<>();
        ScrmSceneCouponRecords record = new ScrmSceneCouponRecords();
        record.setCoupongroupid("group1");
        record.setUnifiedcouponid("coupon1");
        record.setUserid(123L);
        record.setAppid("app1");
        record.setCouponamount(new BigDecimal("100.00"));
        record.setAddTime(new Date());
        record.setScenetype(1);
        mockRecords.add(record);
        when(sceneCouponRecordDOMapper.selectByExample(any())).thenReturn(mockRecords);
        when(utils.getCorpNameByAppId(anyString())).thenReturn("TestCorp");
        // act
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        // assert
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
        CouponDataDetailDTO dto = result.get(0);
        assertEquals("group1", dto.getCouponGroupId());
        assertEquals("coupon1", dto.getCouponId());
        assertEquals(123L, dto.getUserId());
        assertEquals("TestCorp", dto.getCorpName());
        assertEquals("100.00", dto.getCouponValue());
    }

    /**
     * Test case for exception during database query
     */
    @Test
    public void testQueryDetailByRequestDatabaseException() throws Throwable {
        // arrange
        CouponDataDetailDownloadRequest request = new CouponDataDetailDownloadRequest();
        request.setAccessToken("mockToken");
        request.setPathAppId("mockAppId");
        when(sceneCouponRecordDOMapper.selectByExample(any())).thenThrow(new RuntimeException("Database error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> couponDashBoardDomainService.queryDetailByRequest(request));
    }

    /**
     * Test case: Empty corpId should return early without processing
     */
    @Test
    public void testDailyCouponDataSummary_EmptyCorpId() {
        // arrange
        String corpId = "";
        Date startTime = new Date();
        Date endTime = new Date();
        // act
        couponDashBoardDomainService.dailyCouponDataSummary(corpId, startTime, endTime);
        // assert
        verify(corpAppConfigRepository, never()).getAppIdByCorpId(any());
        verify(sceneCouponRecordDOMapper, never()).selectByExample(any());
        verify(couponDataSummaryDOMapper, never()).insertSelective(any());
    }

    /**
     * Test case: When appId lookup fails, should return early
     */
    @Test
    public void testDailyCouponDataSummary_AppIdLookupFails() {
        // arrange
        String corpId = "testCorpId";
        Date startTime = new Date();
        Date endTime = new Date();
        when(corpAppConfigRepository.getAppIdByCorpId(corpId)).thenReturn(null);
        // act
        couponDashBoardDomainService.dailyCouponDataSummary(corpId, startTime, endTime);
        // assert
        verify(corpAppConfigRepository).getAppIdByCorpId(corpId);
        verify(sceneCouponRecordDOMapper, never()).selectByExample(any());
        verify(couponDataSummaryDOMapper, never()).insertSelective(any());
    }

    /**
     * Test case: When no coupon records found, should insert zero summary records
     */
    @Test
    public void testDailyCouponDataSummary_NoCouponRecords() {
        // arrange
        String corpId = "testCorpId";
        String appId = "testAppId";
        Date startTime = new Date();
        Date endTime = new Date();
        when(corpAppConfigRepository.getAppIdByCorpId(corpId)).thenReturn(appId);
        when(sceneCouponRecordDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        ArgumentCaptor<ScrmCouponDataSummaryDO> summaryCaptor = ArgumentCaptor.forClass(ScrmCouponDataSummaryDO.class);
        // act
        couponDashBoardDomainService.dailyCouponDataSummary(corpId, startTime, endTime);
        // assert
        verify(couponDataSummaryDOMapper, times(2)).insertSelective(summaryCaptor.capture());
        List<ScrmCouponDataSummaryDO> capturedSummaries = summaryCaptor.getAllValues();
        assertEquals(2, capturedSummaries.size());
        // Verify both sent and used summaries
        for (ScrmCouponDataSummaryDO summary : capturedSummaries) {
            assertEquals(corpId, summary.getCorpid());
            assertEquals(BigDecimal.ZERO, summary.getCouponAmount());
            assertEquals(0L, summary.getCouponCount());
        }
    }

    //    public void testDailyCouponDataSummaryAppIdIsNull() {
    //        // arrange
    //        String corpId = "corpId";
    //        when(corpAppConfigRepository.getAppIdByCorpId(corpId)).thenReturn(null);
    //        // act
    //        couponDashBoardDomainService.dailyCouponDataSummary(corpId);
    //        // assert
    //        verify(sceneCouponRecordDOMapper, never()).selectByExample(any());
    //        verify(couponDataSummaryDOMapper, never()).insertSelective(any(ScrmCouponDataSummaryDO.class));
    //    }
    /**
     * 测试 corpId 和 appId 都不为空，但查询 ScrmSceneCouponRecordDO 对象列表为空的情况
     */
    //    @Test
    //    public void testDailyCouponDataSummaryCouponRecordDOListIsEmpty() {
    //        // arrange
    //        String corpId = "corpId";
    //        when(sceneCouponRecordDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
    //        // act
    //        couponDashBoardDomainService.dailyCouponDataSummary(corpId);
    //        // assert
    //        verify(couponDataSummaryDOMapper, times(2)).insertSelective(any(ScrmCouponDataSummaryDO.class));
    //    }
    /**
     * 测试 corpId、appId 和查询 ScrmSceneCouponRecordDO 对象列表都不为空的情况
     */
    //    @Test
    //    public void testDailyCouponDataSummaryNormal() {
    /**
     * Test case: When coupon records exist, should calculate and insert correct summaries
     */
    @Test
    public void testDailyCouponDataSummary_WithCouponRecords() {
        // arrange
        String corpId = "testCorpId";
        String appId = "testAppId";
        Date startTime = new Date();
        Date endTime = new Date();
        // Create test coupon records
        List<ScrmSceneCouponRecords> sentRecords = new ArrayList<>();
        ScrmSceneCouponRecords record1 = new ScrmSceneCouponRecords();
        record1.setCouponamount(new BigDecimal("10.5"));
        sentRecords.add(record1);
        List<ScrmSceneCouponRecords> usedRecords = new ArrayList<>();
        ScrmSceneCouponRecords record2 = new ScrmSceneCouponRecords();
        record2.setCouponamount(new BigDecimal("5.5"));
        usedRecords.add(record2);
        when(corpAppConfigRepository.getAppIdByCorpId(corpId)).thenReturn(appId);
        when(sceneCouponRecordDOMapper.selectByExample(any())).thenReturn(sentRecords).thenReturn(usedRecords);
        ArgumentCaptor<ScrmCouponDataSummaryDO> summaryCaptor = ArgumentCaptor.forClass(ScrmCouponDataSummaryDO.class);
        // act
        couponDashBoardDomainService.dailyCouponDataSummary(corpId, startTime, endTime);
        // assert
        verify(couponDataSummaryDOMapper, times(2)).insertSelective(summaryCaptor.capture());
        List<ScrmCouponDataSummaryDO> capturedSummaries = summaryCaptor.getAllValues();
        assertEquals(2, capturedSummaries.size());
        // Verify sent summary
        assertEquals(corpId, capturedSummaries.get(0).getCorpid());
        assertEquals(new BigDecimal("10.5"), capturedSummaries.get(0).getCouponAmount());
        assertEquals(1L, capturedSummaries.get(0).getCouponCount());
        // Verify used summary
        assertEquals(corpId, capturedSummaries.get(1).getCorpid());
        assertEquals(new BigDecimal("5.5"), capturedSummaries.get(1).getCouponAmount());
        assertEquals(1L, capturedSummaries.get(1).getCouponCount());
    }

    /**
     * 测试当请求为 null 时，返回 0L
     */
    @Test
    public void testCountDetailByRequestWhenRequestIsNull() throws Throwable {
        // arrange
        CouponDataDetailQueryRequest request = null;
        // act
        Long result = couponDashBoardDomainService.countDetailByRequest(request);
        // assert
        assertEquals(Long.valueOf(0), result);
    }

    /**
     * 测试当 pageSize 小于等于 0 时，返回 0L
     */
    @Test
    public void testCountDetailByRequestWhenPageSizeIsInvalid() throws Throwable {
        // arrange
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(0);
        request.setPageNumber(1);
        // act
        Long result = couponDashBoardDomainService.countDetailByRequest(request);
        // assert
        assertEquals(Long.valueOf(0), result);
    }

    //    @Test
    /**
     * 测试当 pageNumber 小于等于 0 时，返回 0L
     */
    @Test
    public void testCountDetailByRequestWhenPageNumberIsInvalid() throws Throwable {
        // arrange
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(10);
        request.setPageNumber(0);
        // act
        Long result = couponDashBoardDomainService.countDetailByRequest(request);
        // assert
        assertEquals(Long.valueOf(0), result);
    }

    /**
     * 测试当 getCouponRecordDOExample 返回 null 时，返回 0L
     */
    @Test
    public void testCountDetailByRequestWhenCouponRecordDOExampleIsNull() throws Throwable {
        // arrange
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(10);
        request.setPageNumber(1);
        request.setPathAppId("testAppId");
        request.setAccessToken("testAccessToken");
        // Mock getAuthenticationedAppIds to return an empty list
        when(ssosvOpenApi.queryLoginNameByAccessToken(any())).thenReturn("testMisId");
        when(utils.authentication(any(), any())).thenReturn(Collections.emptyList());
        // Mock countByExample to return 0L
        when(sceneCouponRecordDOMapper.countByExample(any())).thenReturn(0L);
        // act
        Long result = couponDashBoardDomainService.countDetailByRequest(request);
        // assert
        assertEquals(Long.valueOf(0), result);
    }

    //        // arrange
    //        String corpId = "corpId";
    //        ScrmSceneCouponRecordDO scrmSceneCouponRecordDO = new ScrmSceneCouponRecordDO();
    //        scrmSceneCouponRecordDO.setCouponamount(new BigDecimal("100"));
    /**
     * 测试当 countByExample 返回大于 0 的值时，返回正确的统计结果
     */
    @Test
    public void testCountDetailByRequestWhenCountByExampleReturnsNonZero() throws Throwable {
        // arrange
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(10);
        request.setPageNumber(1);
        request.setPathAppId("testAppId");
        request.setAccessToken("testAccessToken");
        // Mock getAuthenticationedAppIds to return a list of appIds
        when(ssosvOpenApi.queryLoginNameByAccessToken(any())).thenReturn("testMisId");
        when(utils.authentication(any(), any())).thenReturn(Arrays.asList("corpId1", "corpId2"));
        when(corpAppConfigRepository.getAppIdByCorpId("corpId1")).thenReturn("appId1");
        when(corpAppConfigRepository.getAppIdByCorpId("corpId2")).thenReturn("appId2");
        // Mock countByExample to return 10L
        when(sceneCouponRecordDOMapper.countByExample(any())).thenReturn(10L);
        // act
        Long result = couponDashBoardDomainService.countDetailByRequest(request);
        // assert
        assertEquals(Long.valueOf(10), result);
    }

    /**
     * Test case: Normal query with valid data
     * Expected: Return list with converted DTOs
     */
    @Test
    public void testQueryDetailByRequest_WhenQuerySuccessful() throws Throwable {
        // arrange
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setAppId("testAppId");
        ScrmSceneCouponRecords record = new ScrmSceneCouponRecords();
        record.setCoupongroupid("group1");
        record.setUnifiedcouponid("coupon1");
        record.setUserid(123L);
        record.setAppid("testAppId");
        record.setCouponamount(new BigDecimal("100"));
        record.setAddTime(new Date());
        record.setScenetype(CreationSceneEnum.MANUAL_UPLOAD.getCode());
        record.setStatisticstatus(StatusEnum.COUNTING.getCode());
        when(sceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(Arrays.asList(record));
        when(utils.getCorpNameByAppId("testAppId")).thenReturn("TestCorp");
        // act
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        // assert
        assertFalse(CollectionUtils.isEmpty(result));
        assertEquals(1, result.size());
        CouponDataDetailDTO dto = result.get(0);
        assertEquals("group1", dto.getCouponGroupId());
        assertEquals("coupon1", dto.getCouponId());
        assertEquals("TestCorp", dto.getCorpName());
        assertEquals("100", dto.getCouponValue());
        assertEquals("否", dto.getIsUsed());
    }

    //    public void testDailyCouponDataSummaryCorpIdIsNull() {
    //        // arrange
    //        String corpId = null;
    //        // act
    //        couponDashBoardDomainService.dailyCouponDataSummary(corpId);
    //        // assert
    //        verify(corpAppConfigRepository, never()).getAppIdByCorpId(anyString());
    //    }
    /**
     * 测试 corpId 不为空，但 appId 为空的情况
     */
    //    @Test
    /**
     * Test case: AppId is empty, using authenticated appIds
     * Expected: Query with authenticated appIds
     */
    @Test
    public void testQueryDetailByRequest_WhenAppIdIsEmpty() throws Throwable {
        // arrange
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setPathAppId("pathAppId");
        request.setAccessToken("token");
        List<String> corpIds = Arrays.asList("corp1", "corp2");
        List<String> appIds = Arrays.asList("app1", "app2");
        when(ssosvOpenApi.queryLoginNameByAccessToken("token")).thenReturn("misId");
        when(utils.authentication("misId", "pathAppId")).thenReturn(corpIds);
        when(corpAppConfigRepository.getAppIdByCorpId("corp1")).thenReturn("app1");
        when(corpAppConfigRepository.getAppIdByCorpId("corp2")).thenReturn("app2");
        when(sceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(new ArrayList<>());
        // act
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
        ArgumentCaptor<ScrmSceneCouponRecordsExample> exampleCaptor = ArgumentCaptor.forClass(ScrmSceneCouponRecordsExample.class);
        verify(sceneCouponRecordDOMapper).selectByExample(exampleCaptor.capture());
        ScrmSceneCouponRecordsExample capturedExample = exampleCaptor.getValue();
        assertNotNull(capturedExample);
        assertFalse(capturedExample.getOredCriteria().isEmpty());
    }

    /**
     * Test case: Query with date range conditions
     * Expected: Query includes date range criteria
     */
    @Test
    public void testQueryDetailByRequest_WithDateRangeConditions() throws Throwable {
        // arrange
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setAppId("testAppId");
        request.setSendStartTime("2023-01-01");
        request.setSendEndTime("2023-12-31");
        request.setUseStartTime("2023-01-01");
        request.setUseEndTime("2023-12-31");
        when(sceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(new ArrayList<>());
        // act
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
        ArgumentCaptor<ScrmSceneCouponRecordsExample> exampleCaptor = ArgumentCaptor.forClass(ScrmSceneCouponRecordsExample.class);
        verify(sceneCouponRecordDOMapper).selectByExample(exampleCaptor.capture());
        ScrmSceneCouponRecordsExample capturedExample = exampleCaptor.getValue();
        assertNotNull(capturedExample);
        assertFalse(capturedExample.getOredCriteria().isEmpty());
    }

    /**
     * Test case: Query with coupon group id condition
     * Expected: Query includes coupon group id criteria
     */
    @Test
    public void testQueryDetailByRequest_WithCouponGroupIdCondition() throws Throwable {
        // arrange
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setAppId("testAppId");
        request.setCouponGroupId("testGroupId");
        when(sceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(new ArrayList<>());
        // act
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
        ArgumentCaptor<ScrmSceneCouponRecordsExample> exampleCaptor = ArgumentCaptor.forClass(ScrmSceneCouponRecordsExample.class);
        verify(sceneCouponRecordDOMapper).selectByExample(exampleCaptor.capture());
        ScrmSceneCouponRecordsExample capturedExample = exampleCaptor.getValue();
        assertNotNull(capturedExample);
        assertFalse(capturedExample.getOredCriteria().isEmpty());
    }

    /**
     * Test case: Query with user id condition
     * Expected: Query includes user id criteria
     */
    @Test
    public void testQueryDetailByRequest_WithUserIdCondition() throws Throwable {
        // arrange
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setAppId("testAppId");
        request.setUserId(123L);
        when(sceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(new ArrayList<>());
        // act
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
        ArgumentCaptor<ScrmSceneCouponRecordsExample> exampleCaptor = ArgumentCaptor.forClass(ScrmSceneCouponRecordsExample.class);
        verify(sceneCouponRecordDOMapper).selectByExample(exampleCaptor.capture());
        ScrmSceneCouponRecordsExample capturedExample = exampleCaptor.getValue();
        assertNotNull(capturedExample);
        assertFalse(capturedExample.getOredCriteria().isEmpty());
    }
}
