package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.beauty.fundamental.light.remote.ResponseEnum;
import com.sankuai.dz.srcm.automatedmanagement.dto.config.CityInfosDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.request.*;
import com.sankuai.dz.srcm.automatedmanagement.response.*;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationBranchStatisticsVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationNodeVO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.ConfigInnerService;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.CrowdPackInnerService;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.ProcessOrchestrationInnerService;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.exception.PersonaCrowdPackException;
import com.sankuai.scrm.core.service.pchat.service.activity.DownloadExcelDataService;
import java.math.BigDecimal;
import java.text.MessageFormat;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmAutoManagementServiceImplTest {

    private static final String SIGNIFICANT_DIFFERENCE_NOT_EXIST = "显著性差异不明显";
    private static final String BEST_STRATEGY_EXIST = "存在最佳策略";
    private static final String BEST_STRATEGY_NOT_EXIST = "不存在最佳策略";
    private static final String N_N_RESULT_Str = "转化显著：否  流失显著：否\n" + "虽然我们观察到了转化率和流失率的一些差异，但这些差异在统计学上并不显著。这可能意味着不同的推送策略对转化率的影响并不如预期的那么大。\n" + "可能需要进行更大规模的测试，增加样本量，以发现更显著的差异";
    private static final String Y_N_RESULT_Str = "转化显著：是  流失显著：否\n" + "转化率的差异是统计学上显著的，这表明不同的推送策略确实对用户转化有不同的影响。【{0}】策略产生了最高的转化率，可以对这种方案进行倾斜。\n" + "虽然我们观察到了流失率的一些差异，但这些差异在统计学上并不显著。这可能意味着不同的推送策略对流失率的影响并不如预期的那么大。";
    private static final String N_Y_RESULT_Str = "转化显著：否  流失显著：是\n" + "虽然我们观察到了转化率的一些差异，但这些差异在统计学上并不显著。这可能意味着不同的推送策略对转化率的影响并不如预期的那么大。\n" + "流失率的差异是统计学上显著的，这表明不同的推送策略确实对用户流失有不同的影响。【{0}】策略导致了最高的流失率，需要仔细评估这种推送策略是否具备优化空间。";
    private static final String Y_Y_RESULT_Str = "转化显著：是  流失显著：是\n" + "转化率和流失率的差异统在计学上都是显著的，我们的推送策略在这两个关键指标上都产生了明显的影响。【{0}】转化率最高，【{1}】流失率最低，可以综合两套方案尝试对策略进一步优化，使得转化率和流失率之间有更好的平衡";
    private static final String Y_Y_BEST_STRATEGY_RESULT_Str = "转化显著：是  流失显著：是\n" + "转化率和流失率的差异统在计学上都是显著的，我们的推送策略在这两个关键指标上都产生了明显的影响。其中【{0}】转化率高且流失最低，是当前的最佳方案";

    @InjectMocks
    private ScrmAutoManagementServiceImpl scrmAutoManagementService;

    @Mock(lenient = true)
    private InformationGatheringService informationGatheringService;

    @Mock(lenient = true)
    private ConfigInnerService configInnerService;

    @Mock(lenient = true)
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock(lenient = true)
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock(lenient = true)
    private DownloadExcelDataService downloadExcelDataService;

    @Mock(lenient = true)
    private ExecuteReadDomainService executeReadDomainService;

    @Mock(lenient = true)
    private ProcessOrchestrationInnerService processOrchestrationInnerService;

    @Mock(lenient = true)
    private ProcessOrchestrationWriteDomainService processOrchestrationWriteDomainService;

    @Mock
    private CrowdPackInnerService crowdPackInnerService;

    public ScrmAutoManagementServiceImplTest() {
        MockitoAnnotations.initMocks(this);
    }

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private ExecuteProcessOrchestrationRequest createRequestWithAppId(String appId) {
        ExecuteProcessOrchestrationRequest request = new ExecuteProcessOrchestrationRequest();
        request.setAppId(appId);
        return request;
    }

    @Test
    public void testProcessOrchestrationInformationGatheringRequestIsNull() throws Throwable {
        RemoteResponse<ProcessOrchestrationInformationGatheringResultVO> response = scrmAutoManagementService.processOrchestrationInformationGathering(null);
        assertEquals("请求参数不能为空", response.getMsg());
    }

    @Test
    public void testProcessOrchestrationInformationGatheringCodeIsNull() throws Throwable {
        ProcessOrchestrationInformationGatheringRequest request = new ProcessOrchestrationInformationGatheringRequest();
        RemoteResponse<ProcessOrchestrationInformationGatheringResultVO> response = scrmAutoManagementService.processOrchestrationInformationGathering(request);
        assertEquals("打点信息不能为空", response.getMsg());
    }

    @Test
    public void testProcessOrchestrationInformationGatheringMtUserIdIsNull() throws Throwable {
        ProcessOrchestrationInformationGatheringRequest request = new ProcessOrchestrationInformationGatheringRequest();
        request.setCode("test");
        RemoteResponse<ProcessOrchestrationInformationGatheringResultVO> response = scrmAutoManagementService.processOrchestrationInformationGathering(request);
        assertEquals("系统异常", response.getMsg());
    }

    @Test
    public void testProcessOrchestrationInformationGatheringInformationGatheringTypeIsNull() throws Throwable {
        ProcessOrchestrationInformationGatheringRequest request = new ProcessOrchestrationInformationGatheringRequest();
        request.setCode("test");
        request.setMtUserId(1L);
        RemoteResponse<ProcessOrchestrationInformationGatheringResultVO> response = scrmAutoManagementService.processOrchestrationInformationGathering(request);
        assertEquals("系统异常", response.getMsg());
    }

    @Test
    public void testProcessOrchestrationInformationGatheringSuccess() throws Throwable {
        ProcessOrchestrationInformationGatheringRequest request = new ProcessOrchestrationInformationGatheringRequest();
        request.setCode("test");
        request.setMtUserId(1L);
        request.setInformationGatheringType(1L);
        when(informationGatheringService.logUserStatusChangeDetail(request)).thenReturn(true);
        RemoteResponse<ProcessOrchestrationInformationGatheringResultVO> response = scrmAutoManagementService.processOrchestrationInformationGathering(request);
        assertTrue(response.getData().isSuccess());
    }

    @Test
    public void testProcessOrchestrationInformationGatheringFail() throws Throwable {
        ProcessOrchestrationInformationGatheringRequest request = new ProcessOrchestrationInformationGatheringRequest();
        request.setCode("test");
        request.setMtUserId(1L);
        request.setInformationGatheringType(1L);
        when(informationGatheringService.logUserStatusChangeDetail(request)).thenReturn(false);
        RemoteResponse<ProcessOrchestrationInformationGatheringResultVO> response = scrmAutoManagementService.processOrchestrationInformationGathering(request);
        assertFalse(response.getData().isSuccess());
    }

    @Test
    public void testProcessOrchestrationInformationGatheringException() throws Throwable {
        ProcessOrchestrationInformationGatheringRequest request = new ProcessOrchestrationInformationGatheringRequest();
        request.setCode("test");
        request.setMtUserId(1L);
        request.setInformationGatheringType(1L);
        when(informationGatheringService.logUserStatusChangeDetail(request)).thenThrow(new RuntimeException());
        RemoteResponse<ProcessOrchestrationInformationGatheringResultVO> response = scrmAutoManagementService.processOrchestrationInformationGathering(request);
        assertEquals("系统异常", response.getMsg());
    }

    /**
     * 测试 queryCityInfos 方法，正常情况
     */
    @Test
    public void testQueryCityInfosNormal() throws Throwable {
        // arrange
        QueryCityInfosRequest request = new QueryCityInfosRequest();
        request.setCityName("北京");
        List<CityInfosDTO> cityInfos = Arrays.asList(new CityInfosDTO(), new CityInfosDTO());
        when(configInnerService.getCityInfos(anyString())).thenReturn(cityInfos);
        // act
        RemoteResponse<QueryCityInfosResultVO> response = scrmAutoManagementService.queryCityInfos(request);
        // assert
        assertEquals(cityInfos, response.getData().getCityInfosDTOList());
        verify(configInnerService, times(1)).getCityInfos(anyString());
    }

    /**
     * 测试 queryCityInfos 方法，异常情况
     */
    @Test
    public void testQueryCityInfosException() throws Throwable {
        // arrange
        QueryCityInfosRequest request = new QueryCityInfosRequest();
        request.setCityName("北京");
        when(configInnerService.getCityInfos(anyString())).thenThrow(new RuntimeException());
        // act
        RemoteResponse<QueryCityInfosResultVO> response = scrmAutoManagementService.queryCityInfos(request);
        // assert
        assertEquals("系统异常", response.getMsg());
        verify(configInnerService, times(1)).getCityInfos(anyString());
    }

    /**
     * 测试 queryCityInfos 方法，边界情况
     */
    @Test
    public void testQueryCityInfosBoundary() throws Throwable {
        // arrange
        QueryCityInfosRequest request = new QueryCityInfosRequest();
        request.setCityName("北京");
        when(configInnerService.getCityInfos(anyString())).thenReturn(null);
        // act
        RemoteResponse<QueryCityInfosResultVO> response = scrmAutoManagementService.queryCityInfos(request);
        // assert
        assertEquals(null, response.getData().getCityInfosDTOList());
        verify(configInnerService, times(1)).getCityInfos(anyString());
    }

    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionIdRequestIsNull() throws Throwable {
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(null);
        assertEquals("请求参数不能为空", response.getMsg());
    }

    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionIdAppIdIsNull() throws Throwable {
        ExecuteProcessOrchestrationRequest request = new ExecuteProcessOrchestrationRequest();
        request.setAppId(null);
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        assertEquals("appId不能为空", response.getMsg());
    }

    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionIdRealTimeProcessOrchestration() throws Throwable {
        ExecuteProcessOrchestrationRequest request = createRequestWithAppId("testAppId");
        request.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue().intValue());
        request.setUnionId("testUnionId");
        when(executeWriteDomainService.executeByUserUnionId(anyString(), any())).thenReturn(true);
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        assertEquals("success", response.getMsg());
    }

    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionIdManualCrowdPackUpdate() throws Throwable {
        ExecuteProcessOrchestrationRequest request = createRequestWithAppId("testAppId");
        request.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.MANUAL_CROWD_PACK_UPDATE.getValue().intValue());
        request.setUnionId("testUnionId");
        request.setCrowdPackId(1L);
        when(executeWriteDomainService.updateCrowdPackManualSubTask(anyList(), anyLong(), anyString())).thenReturn(true);
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        assertEquals("success", response.getMsg());
    }

    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionIdDeletedTimedProcessOrchestration() throws Throwable {
        ExecuteProcessOrchestrationRequest request = createRequestWithAppId("testAppId");
        request.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.DELETED_TIMED_PROCESS_ORCHESTRATION.getValue().intValue());
        request.setProcessOrchestrationId(1L);
        request.setProcessOrchestrationVersion("1.0");
        when(executeWriteDomainService.executeWxOfficialService(anyLong(), anyString(), anyString())).thenReturn(true);
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        assertEquals("success", response.getMsg());
    }

    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionIdException() throws Throwable {
        ExecuteProcessOrchestrationRequest request = createRequestWithAppId("testAppId");
        request.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue().intValue());
        request.setUnionId("testUnionId");
        when(executeWriteDomainService.executeByUserUnionId(anyString(), any())).thenThrow(new RuntimeException());
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        assertEquals("系统异常", response.getMsg());
    }

    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionIdNormal() throws Throwable {
        ExecuteProcessOrchestrationRequest request = createRequestWithAppId("testAppId");
        request.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue().intValue());
        request.setUnionId("testUnionId");
        when(executeWriteDomainService.executeByUserUnionId(anyString(), any())).thenReturn(true);
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        assertEquals("success", response.getMsg());
    }

    /**
     * 测试请求参数为null时的场景
     */
    @Test
    public void testBatchDownloadProcessOrchestrationSuccessRateStatisticsRequestIsNull() {
        // arrange
        BatchGetProcessOrchestrationSuccessRateStatisticsRequest request = null;

        // act
        RemoteResponse<BatchGetProcessOrchestrationSuccessRateStatisticsResultVO> response = scrmAutoManagementService.batchDownloadProcessOrchestrationSuccessRateStatistics(request);

        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("请求参数不能为空", response.getMsg());
    }

    /**
     * 测试编排信息列表为空时的场景
     */
    @Test
    public void testBatchDownloadProcessOrchestrationSuccessRateStatisticsProcessOrchestrationListIsEmpty() {
        // arrange
        BatchGetProcessOrchestrationSuccessRateStatisticsRequest request = new BatchGetProcessOrchestrationSuccessRateStatisticsRequest();
        request.setProcessOrchestrationList(Collections.emptyList());
        request.setAppId("testAppId");
        request.setName("testName");

        when(processOrchestrationReadDomainService.queryProcessOrchestrationDOList(any(), anyInt(), anyInt())).thenReturn(Collections.emptyList());

        // act
        RemoteResponse<BatchGetProcessOrchestrationSuccessRateStatisticsResultVO> response = scrmAutoManagementService.batchDownloadProcessOrchestrationSuccessRateStatistics(request);

        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("编排信息不能为空", response.getMsg());
    }

    /**
     * 测试正常情况下的场景
     */
    @Test
    public void testBatchDownloadProcessOrchestrationSuccessRateStatisticsNormal() {
        // arrange
        BatchGetProcessOrchestrationSuccessRateStatisticsRequest request = new BatchGetProcessOrchestrationSuccessRateStatisticsRequest();
        request.setProcessOrchestrationList(new ArrayList<>());
        request.setAppId("testAppId");
        request.setName("testName");

        ScrmAmProcessOrchestrationInfoDO scrmAmProcessOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        scrmAmProcessOrchestrationInfoDO.setId(1L);
        scrmAmProcessOrchestrationInfoDO.setAppId("testAppId");
        scrmAmProcessOrchestrationInfoDO.setName("testName");

        when(processOrchestrationReadDomainService.queryProcessOrchestrationDOList(any(), anyInt(), anyInt())).thenReturn(Collections.singletonList(scrmAmProcessOrchestrationInfoDO));
        QueryProcessOrchestrationSuccessRateStatisticsResultVO successRateStatisticsResultVO = new QueryProcessOrchestrationSuccessRateStatisticsResultVO();
        successRateStatisticsResultVO.setSuccessRate("1");
        successRateStatisticsResultVO.setNegativeRate("1");
        successRateStatisticsResultVO.setTotalAmount(new BigDecimal(1));
        successRateStatisticsResultVO.setTotalCount(1L);
        QueryProcessOrchestrationSuccessRateStatisticsResultDetailVO resultDetailVO = new QueryProcessOrchestrationSuccessRateStatisticsResultDetailVO();
        resultDetailVO.setTotalCount(1L);
        resultDetailVO.setReceivedCount(1L);
        resultDetailVO.setTouchCount(1L);
        resultDetailVO.setUserReactRate("0");
        resultDetailVO.setUserReactCount(1L);
        successRateStatisticsResultVO.setMainDetail(resultDetailVO);
        successRateStatisticsResultVO.setProcessOrchestrationId(1L);
        successRateStatisticsResultVO.setProcessOrchestrationName("testName");
        when(executeReadDomainService.queryProcessOrchestrationSuccessRateStatistics(any(), any(), any())).thenReturn(successRateStatisticsResultVO);
        when(downloadExcelDataService.download(any(), any())).thenReturn("testDownloadUrl");

        // act
        RemoteResponse<BatchGetProcessOrchestrationSuccessRateStatisticsResultVO> response = scrmAutoManagementService.batchDownloadProcessOrchestrationSuccessRateStatistics(request);

        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals("testDownloadUrl", response.getData().getDownloadUrl());
    }

    /**
     * 测试请求参数为空的情况
     */
    @Test
    public void testQueryProcessOrchestrationBranchStatisticsRequestIsNull() {
        RemoteResponse<QueryProcessOrchestrationBranchStatisticsResultVO> response = scrmAutoManagementService.queryProcessOrchestrationBranchStatistics(null);
        assertNotNull(response);
        assertEquals("请求参数不能为空", response.getMsg());
    }

    /**
     * 测试appId为空的情况
     */
    @Test
    public void testQueryProcessOrchestrationBranchStatisticsAppIdIsNull() {
        QueryProcessOrchestrationBranchStatisticsRequest request;
        List<ScrmProcessOrchestrationNodeVO> nodeVOList;
        request = new QueryProcessOrchestrationBranchStatisticsRequest();
        request.setAppId("testAppId");
        request.setProcessOrchestrationId(1L);
        nodeVOList = new ArrayList<>();
        ScrmProcessOrchestrationNodeVO nodeVO = new ScrmProcessOrchestrationNodeVO();
        ScrmProcessOrchestrationBranchStatisticsVO branchStatisticsVO = new ScrmProcessOrchestrationBranchStatisticsVO();
        branchStatisticsVO.setLeafNode(true);
        branchStatisticsVO.setUserCount(100L);
        branchStatisticsVO.setOrderCount(50L);
        branchStatisticsVO.setLossCount(20L);
        nodeVO.setBranchStatisticsVO(branchStatisticsVO);
        nodeVOList.add(nodeVO);
        request.setAppId(null);
        RemoteResponse<QueryProcessOrchestrationBranchStatisticsResultVO> response = scrmAutoManagementService.queryProcessOrchestrationBranchStatistics(request);
        assertNotNull(response);
        assertEquals("appId不能为空", response.getMsg());
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testQueryProcessOrchestrationBranchStatisticsNormal() {
        QueryProcessOrchestrationBranchStatisticsRequest request;
        List<ScrmProcessOrchestrationNodeVO> nodeVOList;
        request = new QueryProcessOrchestrationBranchStatisticsRequest();
        request.setAppId("testAppId");
        request.setProcessOrchestrationId(1L);
        nodeVOList = new ArrayList<>();
        ScrmProcessOrchestrationNodeVO nodeVO = new ScrmProcessOrchestrationNodeVO();
        ScrmProcessOrchestrationBranchStatisticsVO branchStatisticsVO = new ScrmProcessOrchestrationBranchStatisticsVO();
        branchStatisticsVO.setLeafNode(true);
        branchStatisticsVO.setUserCount(100L);
        branchStatisticsVO.setOrderCount(50L);
        branchStatisticsVO.setLossCount(20L);
        nodeVO.setBranchStatisticsVO(branchStatisticsVO);
        nodeVOList.add(nodeVO);
        ScrmProcessOrchestrationNodeVO nodeVO2 = new ScrmProcessOrchestrationNodeVO();
        ScrmProcessOrchestrationBranchStatisticsVO branchStatisticsVO2 = new ScrmProcessOrchestrationBranchStatisticsVO();
        branchStatisticsVO2.setLeafNode(true);
        branchStatisticsVO2.setUserCount(500L);
        branchStatisticsVO2.setOrderCount(30L);
        branchStatisticsVO2.setLossCount(100L);
        nodeVO2.setBranchStatisticsVO(branchStatisticsVO2);
        nodeVOList.add(nodeVO2);
        when(processOrchestrationInnerService.queryBranchStatisticsInfoById(any(QueryProcessOrchestrationBranchStatisticsRequest.class))).thenReturn(nodeVOList);
        RemoteResponse<QueryProcessOrchestrationBranchStatisticsResultVO> response = scrmAutoManagementService.queryProcessOrchestrationBranchStatistics(request);
        assertNotNull(response);
        // assertTrue(response.isSuccess());
        // assertNotNull(response.getData());
        // assertEquals(1, response.getData().getNodeList().size());
    }

    /**
     * 测试内部服务异常情况
     */
    @Test
    public void testQueryProcessOrchestrationBranchStatisticsInnerServiceException() {
        QueryProcessOrchestrationBranchStatisticsRequest request;
        List<ScrmProcessOrchestrationNodeVO> nodeVOList;
        request = new QueryProcessOrchestrationBranchStatisticsRequest();
        request.setAppId("testAppId");
        request.setProcessOrchestrationId(1L);
        nodeVOList = new ArrayList<>();
        ScrmProcessOrchestrationNodeVO nodeVO = new ScrmProcessOrchestrationNodeVO();
        ScrmProcessOrchestrationBranchStatisticsVO branchStatisticsVO = new ScrmProcessOrchestrationBranchStatisticsVO();
        branchStatisticsVO.setLeafNode(true);
        branchStatisticsVO.setUserCount(100L);
        branchStatisticsVO.setOrderCount(50L);
        branchStatisticsVO.setLossCount(20L);
        nodeVO.setBranchStatisticsVO(branchStatisticsVO);
        nodeVOList.add(nodeVO);
        when(processOrchestrationInnerService.queryBranchStatisticsInfoById(any(QueryProcessOrchestrationBranchStatisticsRequest.class))).thenThrow(new RuntimeException("内部服务异常"));
        RemoteResponse<QueryProcessOrchestrationBranchStatisticsResultVO> response = scrmAutoManagementService.queryProcessOrchestrationBranchStatistics(request);
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("系统异常", response.getMsg());
    }

    /**
     * 测试场景：不存在显著性差异的转化率和流失率
     */
    @Test
    public void testBuildResultNoSignificantDifference() {
        QueryProcessOrchestrationBranchStatisticsResultVO resultVO;
        boolean existSignificantDifferenceOfConversions;
        boolean existSignificantDifferenceOfLosses;
        String[] leafNodeNames;
        Double[] conversionsRate;
        Double[] lossesRate;
        resultVO = new QueryProcessOrchestrationBranchStatisticsResultVO();
        leafNodeNames = new String[] { "策略A", "策略B" };
        conversionsRate = new Double[] { 0.1, 0.2 };
        lossesRate = new Double[] { 0.1, 0.05 };
        existSignificantDifferenceOfConversions = false;
        existSignificantDifferenceOfLosses = false;
        ScrmAutoManagementServiceImpl.buildResult(resultVO, existSignificantDifferenceOfConversions, existSignificantDifferenceOfLosses, leafNodeNames, conversionsRate, lossesRate);
        assertEquals(SIGNIFICANT_DIFFERENCE_NOT_EXIST, resultVO.getResultTitle());
        assertEquals(N_N_RESULT_Str, resultVO.getResultDesc());
    }

    /**
     * 测试场景：存在显著性差异的转化率，不存在显著性差异的流失率
     */
    @Test
    public void testBuildResultSignificantDifferenceConversionOnly() {
        QueryProcessOrchestrationBranchStatisticsResultVO resultVO;
        boolean existSignificantDifferenceOfConversions;
        boolean existSignificantDifferenceOfLosses;
        String[] leafNodeNames;
        Double[] conversionsRate;
        Double[] lossesRate;
        resultVO = new QueryProcessOrchestrationBranchStatisticsResultVO();
        leafNodeNames = new String[] { "策略A", "策略B" };
        conversionsRate = new Double[] { 0.1, 0.2 };
        lossesRate = new Double[] { 0.1, 0.05 };
        existSignificantDifferenceOfConversions = true;
        existSignificantDifferenceOfLosses = false;
        ScrmAutoManagementServiceImpl.buildResult(resultVO, existSignificantDifferenceOfConversions, existSignificantDifferenceOfLosses, leafNodeNames, conversionsRate, lossesRate);
        assertEquals(SIGNIFICANT_DIFFERENCE_NOT_EXIST, resultVO.getResultTitle());
        assertEquals(MessageFormat.format(Y_N_RESULT_Str, leafNodeNames[1]), resultVO.getResultDesc());
    }

    /**
     * 测试场景：不存在显著性差异的转化率，存在显著性差异的流失率
     */
    @Test
    public void testBuildResultSignificantDifferenceLossOnly() {
        QueryProcessOrchestrationBranchStatisticsResultVO resultVO;
        boolean existSignificantDifferenceOfConversions;
        boolean existSignificantDifferenceOfLosses;
        String[] leafNodeNames;
        Double[] conversionsRate;
        Double[] lossesRate;
        resultVO = new QueryProcessOrchestrationBranchStatisticsResultVO();
        leafNodeNames = new String[] { "策略A", "策略B" };
        conversionsRate = new Double[] { 0.1, 0.2 };
        lossesRate = new Double[] { 0.1, 0.05 };
        existSignificantDifferenceOfConversions = false;
        existSignificantDifferenceOfLosses = true;
        ScrmAutoManagementServiceImpl.buildResult(resultVO, existSignificantDifferenceOfConversions, existSignificantDifferenceOfLosses, leafNodeNames, conversionsRate, lossesRate);
        assertEquals(SIGNIFICANT_DIFFERENCE_NOT_EXIST, resultVO.getResultTitle());
        assertEquals(MessageFormat.format(N_Y_RESULT_Str, leafNodeNames[0]), resultVO.getResultDesc());
    }

    /**
     * 测试场景：转化率和流失率的显著性差异都存在，但没有最佳策略
     */
    @Test
    public void testBuildResultSignificantDifferenceBothNoBestStrategy() {
        QueryProcessOrchestrationBranchStatisticsResultVO resultVO;
        boolean existSignificantDifferenceOfConversions;
        boolean existSignificantDifferenceOfLosses;
        String[] leafNodeNames;
        Double[] conversionsRate;
        Double[] lossesRate;
        resultVO = new QueryProcessOrchestrationBranchStatisticsResultVO();
        leafNodeNames = new String[] { "策略A", "策略B" };
        conversionsRate = new Double[] { 0.1, 0.2 };
        lossesRate = new Double[] { 0.1, 0.05 };
        existSignificantDifferenceOfConversions = true;
        existSignificantDifferenceOfLosses = true;
        // 修改数据以确保没有最佳策略
        conversionsRate = new Double[] { 0.2, 0.1 };
        lossesRate = new Double[] { 0.05, 0.1 };
        ScrmAutoManagementServiceImpl.buildResult(resultVO, existSignificantDifferenceOfConversions, existSignificantDifferenceOfLosses, leafNodeNames, conversionsRate, lossesRate);
        assertEquals(BEST_STRATEGY_EXIST, resultVO.getResultTitle());
        assertEquals(MessageFormat.format(Y_Y_BEST_STRATEGY_RESULT_Str, leafNodeNames[0], leafNodeNames[0]), resultVO.getResultDesc());
    }

    /**
     * 测试场景：转化率和流失率的显著性差异都存在，且存在最佳策略
     */
    @Test
    public void testBuildResultSignificantDifferenceBothWithBestStrategy() {
        QueryProcessOrchestrationBranchStatisticsResultVO resultVO;
        boolean existSignificantDifferenceOfConversions;
        boolean existSignificantDifferenceOfLosses;
        String[] leafNodeNames;
        Double[] conversionsRate;
        Double[] lossesRate;
        resultVO = new QueryProcessOrchestrationBranchStatisticsResultVO();
        leafNodeNames = new String[] { "策略A", "策略B" };
        conversionsRate = new Double[] { 0.1, 0.2 };
        lossesRate = new Double[] { 0.1, 0.05 };
        existSignificantDifferenceOfConversions = true;
        existSignificantDifferenceOfLosses = true;
        ScrmAutoManagementServiceImpl.buildResult(resultVO, existSignificantDifferenceOfConversions, existSignificantDifferenceOfLosses, leafNodeNames, conversionsRate, lossesRate);
        assertEquals(BEST_STRATEGY_EXIST, resultVO.getResultTitle());
        assertEquals(MessageFormat.format(Y_Y_BEST_STRATEGY_RESULT_Str, leafNodeNames[1]), resultVO.getResultDesc());
    }

    /**
     * 测试请求参数为null时的场景
     */
    @Test
    public void testCheckRealTimeTaskTimeWindowRequestIsNull() {
        // arrange
        CheckRealTimeTaskTimeWindowRequest request = null;
        // act
        RemoteResponse<CheckRealTimeTaskTimeWindowResultVO> response = scrmAutoManagementService.checkRealTimeTaskTimeWindow(request);
        // assert
        assert "请求参数不能为空".equals(response.getMsg());
    }

    /**
     * 测试realTimeSceneId无效时的场景
     */
    @Test
    public void testCheckRealTimeTaskTimeWindowInvalidRealTimeSceneId() {
        // arrange
        CheckRealTimeTaskTimeWindowRequest request = new CheckRealTimeTaskTimeWindowRequest();
        request.setRealTimeSceneId(-1L);
        // act
        RemoteResponse<CheckRealTimeTaskTimeWindowResultVO> response = scrmAutoManagementService.checkRealTimeTaskTimeWindow(request);
        // assert
        assert "realTimeSceneId无效".equals(response.getMsg());
    }

    /**
     * 测试开始时间无效时的场景
     */
    @Test
    public void testCheckRealTimeTaskTimeWindowInvalidBeginTime() {
        // arrange
        CheckRealTimeTaskTimeWindowRequest request = new CheckRealTimeTaskTimeWindowRequest();
        request.setRealTimeSceneId(1L);
        request.setBeginTime("");
        // act
        RemoteResponse<CheckRealTimeTaskTimeWindowResultVO> response = scrmAutoManagementService.checkRealTimeTaskTimeWindow(request);
        // assert
        assert "开始时间无效".equals(response.getMsg());
    }

    /**
     * 测试结束时间无效时的场景
     */
    @Test
    public void testCheckRealTimeTaskTimeWindowInvalidEndTime() {
        // arrange
        CheckRealTimeTaskTimeWindowRequest request = new CheckRealTimeTaskTimeWindowRequest();
        request.setRealTimeSceneId(1L);
        request.setBeginTime("2023-04-01 00:00:00");
        request.setEndTime("");
        // act
        RemoteResponse<CheckRealTimeTaskTimeWindowResultVO> response = scrmAutoManagementService.checkRealTimeTaskTimeWindow(request);
        // assert
        assert "结束时间无效".equals(response.getMsg());
    }

    /**
     * 测试窗口期校验返回消息时的场景
     */
    @Test
    public void testCheckRealTimeTaskTimeWindowWithMessage() {
        // arrange
        CheckRealTimeTaskTimeWindowRequest request = new CheckRealTimeTaskTimeWindowRequest();
        request.setRealTimeSceneId(1L);
        request.setBeginTime("2023-04-01 00:00:00");
        request.setEndTime("2023-04-02 00:00:00");
        when(processOrchestrationWriteDomainService.checkRealTimeSceneTimeWindow(any(), any(), any(), any())).thenReturn("时间窗口冲突");
        // act
        RemoteResponse<CheckRealTimeTaskTimeWindowResultVO> response = scrmAutoManagementService.checkRealTimeTaskTimeWindow(request);
        // assert
        assert "时间窗口冲突".equals(response.getData().getMessage());
        assert !response.getData().isCanCreate();
    }

    /**
     * 测试窗口期校验通过时的场景
     */
    @Test
    public void testCheckRealTimeTaskTimeWindowSuccess() {
        // arrange
        CheckRealTimeTaskTimeWindowRequest request = new CheckRealTimeTaskTimeWindowRequest();
        request.setRealTimeSceneId(1L);
        request.setBeginTime("2023-04-01 00:00:00");
        request.setEndTime("2023-04-02 00:00:00");
        when(processOrchestrationWriteDomainService.checkRealTimeSceneTimeWindow(any(), any(), any(), any())).thenReturn("");
        // act
        RemoteResponse<CheckRealTimeTaskTimeWindowResultVO> response = scrmAutoManagementService.checkRealTimeTaskTimeWindow(request);
        // assert
        assert response.getData().isCanCreate();
    }

    /**
     * Test successful query with valid request
     */
    @Test
    public void testQueryPersonaCrowdPackDetail_Success() throws Throwable {
        // arrange
        QueryPersonaCrowdInfoRequest request = new QueryPersonaCrowdInfoRequest();
        request.setPersonaCrowdId(123);
        QueryPersonaCrowdPackDetailResultVO expectedResult = new QueryPersonaCrowdPackDetailResultVO();
        when(crowdPackInnerService.queryPersonaCrowdPackDetail(request)).thenReturn(expectedResult);
        // act
        RemoteResponse<QueryPersonaCrowdPackDetailResultVO> response = scrmAutoManagementService.queryPersonaCrowdPackDetail(request);
        // assert
        assertTrue(response.isSuccess());
        assertEquals(expectedResult, response.getData());
        verify(crowdPackInnerService).queryPersonaCrowdPackDetail(request);
    }

    /**
     * Test with null request
     */
    @Test
    public void testQueryPersonaCrowdPackDetail_NullRequest() throws Throwable {
        // act
        RemoteResponse<QueryPersonaCrowdPackDetailResultVO> response = scrmAutoManagementService.queryPersonaCrowdPackDetail(null);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("请求参数错误", response.getMsg());
        verifyNoInteractions(crowdPackInnerService);
    }

    /**
     * Test with null personaCrowdId
     */
    @Test
    public void testQueryPersonaCrowdPackDetail_NullPersonaCrowdId() throws Throwable {
        // arrange
        QueryPersonaCrowdInfoRequest request = new QueryPersonaCrowdInfoRequest();
        request.setPersonaCrowdId(null);
        // act
        RemoteResponse<QueryPersonaCrowdPackDetailResultVO> response = scrmAutoManagementService.queryPersonaCrowdPackDetail(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("请求参数错误", response.getMsg());
        verifyNoInteractions(crowdPackInnerService);
    }

    /**
     * Test when PersonaCrowdPackException occurs
     */
    @Test
    public void testQueryPersonaCrowdPackDetail_PersonaCrowdPackException() throws Throwable {
        // arrange
        QueryPersonaCrowdInfoRequest request = new QueryPersonaCrowdInfoRequest();
        request.setPersonaCrowdId(123);
        String errorMessage = "Persona crowd pack error";
        when(crowdPackInnerService.queryPersonaCrowdPackDetail(request)).thenThrow(new PersonaCrowdPackException(errorMessage));
        // act
        RemoteResponse<QueryPersonaCrowdPackDetailResultVO> response = scrmAutoManagementService.queryPersonaCrowdPackDetail(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals(errorMessage, response.getMsg());
        verify(crowdPackInnerService).queryPersonaCrowdPackDetail(request);
    }

    /**
     * Test when general exception occurs
     */
    @Test
    public void testQueryPersonaCrowdPackDetail_GeneralException() throws Throwable {
        // arrange
        QueryPersonaCrowdInfoRequest request = new QueryPersonaCrowdInfoRequest();
        request.setPersonaCrowdId(123);
        when(crowdPackInnerService.queryPersonaCrowdPackDetail(request)).thenThrow(new RuntimeException("Unexpected error"));
        // act
        RemoteResponse<QueryPersonaCrowdPackDetailResultVO> response = scrmAutoManagementService.queryPersonaCrowdPackDetail(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("系统异常", response.getMsg());
        verify(crowdPackInnerService).queryPersonaCrowdPackDetail(request);
    }

    /**
     * 测试getCreateProcessOrchestrationResultVORemoteResponse方法，当创建流程编排成功时。
     */
    @Test
    public void testGetCreateProcessOrchestrationResultVORemoteResponseSuccess() throws IllegalAccessException {
        // arrange
        CreateProcessOrchestrationRequest request = new CreateProcessOrchestrationRequest();
        CreateProcessOrchestrationResultVO mockResultVO = new CreateProcessOrchestrationResultVO();
        mockResultVO.setSuccess(true);
        when(processOrchestrationInnerService.createProcessOrchestration(request)).thenReturn(mockResultVO);

        // act
        RemoteResponse<CreateProcessOrchestrationResultVO> response = scrmAutoManagementService.getCreateProcessOrchestrationResultVORemoteResponse(request);

        // assert
        assertTrue(response.getData().getSuccess());
        verify(processOrchestrationInnerService, times(1)).createProcessOrchestration(request);
    }
}
