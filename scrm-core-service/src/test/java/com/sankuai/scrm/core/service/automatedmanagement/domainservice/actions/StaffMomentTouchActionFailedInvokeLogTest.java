package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationConditionDetailDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.user.enums.ScrmUserTagEnum;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class StaffMomentTouchActionFailedInvokeLogTest {

    @InjectMocks
    private StaffMomentTouchAction staffMomentTouchAction;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    public StaffMomentTouchActionFailedInvokeLogTest() {
        MockitoAnnotations.openMocks(this);
    }

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试正常情况：invokeLogDO 和 statusList 都不为空，且更新操作成功
     */
    @Test
    void testFailedInvokeLogNormal() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setId(1L);
        List<Byte> statusList = Arrays.asList((byte) 1, (byte) 2);
        // act
        staffMomentTouchAction.failedInvokeLog(invokeLogDO, statusList);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).updateByPrimaryKey(invokeLogDO);
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any());
    }

    /**
     * 测试异常情况：invokeLogDO 为空
     */
    @Test
    void testFailedInvokeLogWithNullInvokeLogDO() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = null;
        List<Byte> statusList = Arrays.asList((byte) 1, (byte) 2);
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            staffMomentTouchAction.failedInvokeLog(invokeLogDO, statusList);
        });
    }

    /**
     * 测试异常情况：statusList 为空
     */
    @Test
    void testFailedInvokeLogWithEmptyStatusList() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setId(1L);
        List<Byte> statusList = Collections.emptyList();
        // act
        staffMomentTouchAction.failedInvokeLog(invokeLogDO, statusList);
        // assert
        verify(wxInvokeLogDOMapper, times(1)).updateByPrimaryKey(invokeLogDO);
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any());
    }

    /**
     * 测试异常情况：wxInvokeLogDOMapper.updateByPrimaryKey 抛出异常
     */
    @Test
    void testFailedInvokeLogWithUpdateByPrimaryKeyException() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setId(1L);
        List<Byte> statusList = Arrays.asList((byte) 1, (byte) 2);
        doThrow(new RuntimeException("Update failed")).when(wxInvokeLogDOMapper).updateByPrimaryKey(invokeLogDO);
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            staffMomentTouchAction.failedInvokeLog(invokeLogDO, statusList);
        });
        verify(wxInvokeLogDOMapper, times(1)).updateByPrimaryKey(invokeLogDO);
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * 测试异常情况：executeLogDOMapper.updateByExampleSelective 抛出异常
     */
    @Test
    void testFailedInvokeLogWithUpdateByExampleSelectiveException() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setId(1L);
        List<Byte> statusList = Arrays.asList((byte) 1, (byte) 2);
        doThrow(new RuntimeException("Update failed")).when(executeLogDOMapper).updateByExampleSelective(any(), any());
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            staffMomentTouchAction.failedInvokeLog(invokeLogDO, statusList);
        });
        verify(wxInvokeLogDOMapper, times(1)).updateByPrimaryKey(invokeLogDO);
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(), any());
    }

    /**
     * Test case for null input
     */
    @Test
    public void testComputeValidCorpTagSetWithNullInput() throws Throwable {
        // act & assert
        assertThrows(NullPointerException.class, () -> staffMomentTouchAction.computeValidCorpTagSet(null));
    }

    /**
     * Test case for single set in list
     */
    @Test
    public void testComputeValidCorpTagSetWithSingleSet() throws Throwable {
        // arrange
        List<Set<String>> singleSetList = new ArrayList<>();
        Set<String> set1 = new HashSet<>(Arrays.asList("tag1", "tag2"));
        singleSetList.add(set1);
        // act
        Set<String> result = staffMomentTouchAction.computeValidCorpTagSet(singleSetList);
        // assert
        assertEquals(set1, result);
    }

    /**
     * Test case for multiple sets with all matching tags
     */
    @Test
    public void testComputeValidCorpTagSetWithMatchingTags() throws Throwable {
        // arrange
        List<Set<String>> matchingList = new ArrayList<>();
        Set<String> set1 = new HashSet<>(Arrays.asList("tag1", "tag2"));
        Set<String> set2 = new HashSet<>(Arrays.asList("tag1", "tag2"));
        Set<String> set3 = new HashSet<>(Arrays.asList("tag1", "tag2"));
        matchingList.add(set1);
        matchingList.add(set2);
        matchingList.add(set3);
        // act
        Set<String> result = staffMomentTouchAction.computeValidCorpTagSet(matchingList);
        // assert
        assertEquals(set1, result);
    }

    /**
     * Test case for multiple sets with mismatched tags
     */
    @Test
    public void testComputeValidCorpTagSetWithMismatchedTags() throws Throwable {
        // arrange
        List<Set<String>> mismatchedList = new ArrayList<>();
        Set<String> set1 = new HashSet<>(Arrays.asList("tag1", "tag2"));
        // missing tag1
        Set<String> set2 = new HashSet<>(Arrays.asList("tag2", "tag3"));
        mismatchedList.add(set1);
        mismatchedList.add(set2);
        // act
        Set<String> result = staffMomentTouchAction.computeValidCorpTagSet(mismatchedList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for multiple sets with empty set
     */
    @Test
    public void testComputeValidCorpTagSetWithEmptySet() throws Throwable {
        // arrange
        List<Set<String>> listWithEmptySet = new ArrayList<>();
        Set<String> set1 = new HashSet<>(Arrays.asList("tag1", "tag2"));
        Set<String> set2 = new HashSet<>();
        listWithEmptySet.add(set1);
        listWithEmptySet.add(set2);
        // act
        Set<String> result = staffMomentTouchAction.computeValidCorpTagSet(listWithEmptySet);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for multiple sets with subset matching
     */
    @Test
    public void testComputeValidCorpTagSetWithSubsetMatching() throws Throwable {
        // arrange
        List<Set<String>> subsetList = new ArrayList<>();
        Set<String> set1 = new HashSet<>(Arrays.asList("tag1", "tag2"));
        Set<String> set2 = new HashSet<>(Arrays.asList("tag1", "tag2", "tag3"));
        subsetList.add(set1);
        subsetList.add(set2);
        // act
        Set<String> result = staffMomentTouchAction.computeValidCorpTagSet(subsetList);
        // assert
        assertEquals(set1, result);
    }

    /**
     * Test case for multiple sets with exact matching tags in different order
     */
    @Test
    public void testComputeValidCorpTagSetWithDifferentOrder() throws Throwable {
        // arrange
        List<Set<String>> differentOrderList = new ArrayList<>();
        Set<String> set1 = new HashSet<>(Arrays.asList("tag1", "tag2"));
        Set<String> set2 = new HashSet<>(Arrays.asList("tag2", "tag1"));
        differentOrderList.add(set1);
        differentOrderList.add(set2);
        // act
        Set<String> result = staffMomentTouchAction.computeValidCorpTagSet(differentOrderList);
        // assert
        assertEquals(set1, result);
    }

    /**
     * Test case for empty input list.
     */
    @Test
    public void testGetSingleNodeCorpTagListEmptyInput() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationConditionDetailDTO> currentNodeConditionList = Collections.emptyList();
        // act
        Set<String> result = staffMomentTouchAction.getSingleNodeCorpTagList(currentNodeConditionList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for single group with no CORP_TAG condition.
     */
    @Test
    public void testGetSingleNodeCorpTagListSingleGroupNoCorpTag() throws Throwable {
        // arrange
        ScrmProcessOrchestrationConditionDetailDTO condition = new ScrmProcessOrchestrationConditionDetailDTO();
        condition.setFilterFieldId(ScrmUserTagEnum.IS_FRIEND.getTagId());
        condition.setParam(Arrays.asList("true"));
        condition.setGroupId(1);
        List<ScrmProcessOrchestrationConditionDetailDTO> currentNodeConditionList = Collections.singletonList(condition);
        // act
        Set<String> result = staffMomentTouchAction.getSingleNodeCorpTagList(currentNodeConditionList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for single group with a single CORP_TAG condition.
     */
    @Test
    public void testGetSingleNodeCorpTagListSingleGroupSingleCorpTag() throws Throwable {
        // arrange
        ScrmProcessOrchestrationConditionDetailDTO condition = new ScrmProcessOrchestrationConditionDetailDTO();
        condition.setFilterFieldId(ScrmUserTagEnum.CORP_TAG.getTagId());
        condition.setParam(Arrays.asList("tag1"));
        condition.setGroupId(1);
        List<ScrmProcessOrchestrationConditionDetailDTO> currentNodeConditionList = Collections.singletonList(condition);
        // act
        Set<String> result = staffMomentTouchAction.getSingleNodeCorpTagList(currentNodeConditionList);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains("tag1"));
    }

    /**
     * Test case for single group with multiple CORP_TAG conditions.
     */
    @Test
    public void testGetSingleNodeCorpTagListSingleGroupMultipleCorpTag() throws Throwable {
        // arrange
        ScrmProcessOrchestrationConditionDetailDTO condition1 = new ScrmProcessOrchestrationConditionDetailDTO();
        condition1.setFilterFieldId(ScrmUserTagEnum.CORP_TAG.getTagId());
        condition1.setParam(Arrays.asList("tag1"));
        condition1.setGroupId(1);
        ScrmProcessOrchestrationConditionDetailDTO condition2 = new ScrmProcessOrchestrationConditionDetailDTO();
        condition2.setFilterFieldId(ScrmUserTagEnum.CORP_TAG.getTagId());
        condition2.setParam(Arrays.asList("tag2"));
        condition2.setGroupId(1);
        List<ScrmProcessOrchestrationConditionDetailDTO> currentNodeConditionList = Arrays.asList(condition1, condition2);
        // act
        Set<String> result = staffMomentTouchAction.getSingleNodeCorpTagList(currentNodeConditionList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for single group with CORP_TAG and other conditions.
     */
    @Test
    public void testGetSingleNodeCorpTagListSingleGroupCorpTagAndOtherConditions() throws Throwable {
        // arrange
        ScrmProcessOrchestrationConditionDetailDTO condition1 = new ScrmProcessOrchestrationConditionDetailDTO();
        condition1.setFilterFieldId(ScrmUserTagEnum.CORP_TAG.getTagId());
        condition1.setParam(Arrays.asList("tag1"));
        condition1.setGroupId(1);
        ScrmProcessOrchestrationConditionDetailDTO condition2 = new ScrmProcessOrchestrationConditionDetailDTO();
        condition2.setFilterFieldId(ScrmUserTagEnum.IS_FRIEND.getTagId());
        condition2.setParam(Arrays.asList("true"));
        condition2.setGroupId(1);
        List<ScrmProcessOrchestrationConditionDetailDTO> currentNodeConditionList = Arrays.asList(condition1, condition2);
        // act
        Set<String> result = staffMomentTouchAction.getSingleNodeCorpTagList(currentNodeConditionList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for multiple groups with no CORP_TAG condition.
     */
    @Test
    public void testGetSingleNodeCorpTagListMultipleGroupsNoCorpTag() throws Throwable {
        // arrange
        ScrmProcessOrchestrationConditionDetailDTO condition1 = new ScrmProcessOrchestrationConditionDetailDTO();
        condition1.setFilterFieldId(ScrmUserTagEnum.IS_FRIEND.getTagId());
        condition1.setParam(Arrays.asList("true"));
        condition1.setGroupId(1);
        ScrmProcessOrchestrationConditionDetailDTO condition2 = new ScrmProcessOrchestrationConditionDetailDTO();
        condition2.setFilterFieldId(ScrmUserTagEnum.IS_IN_GROUP.getTagId());
        condition2.setParam(Arrays.asList("true"));
        condition2.setGroupId(2);
        List<ScrmProcessOrchestrationConditionDetailDTO> currentNodeConditionList = Arrays.asList(condition1, condition2);
        // act
        Set<String> result = staffMomentTouchAction.getSingleNodeCorpTagList(currentNodeConditionList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for multiple groups with some having a single CORP_TAG condition.
     */
    @Test
    public void testGetSingleNodeCorpTagListMultipleGroupsSomeWithSingleCorpTag() throws Throwable {
        // arrange
        ScrmProcessOrchestrationConditionDetailDTO condition1 = new ScrmProcessOrchestrationConditionDetailDTO();
        condition1.setFilterFieldId(ScrmUserTagEnum.CORP_TAG.getTagId());
        condition1.setParam(Arrays.asList("tag1"));
        condition1.setGroupId(1);
        ScrmProcessOrchestrationConditionDetailDTO condition2 = new ScrmProcessOrchestrationConditionDetailDTO();
        condition2.setFilterFieldId(ScrmUserTagEnum.IS_FRIEND.getTagId());
        condition2.setParam(Arrays.asList("true"));
        condition2.setGroupId(2);
        List<ScrmProcessOrchestrationConditionDetailDTO> currentNodeConditionList = Arrays.asList(condition1, condition2);
        // act
        Set<String> result = staffMomentTouchAction.getSingleNodeCorpTagList(currentNodeConditionList);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains("tag1"));
    }

    /**
     * Test case for multiple groups with some having multiple CORP_TAG conditions.
     */
    @Test
    public void testGetSingleNodeCorpTagListMultipleGroupsSomeWithMultipleCorpTag() throws Throwable {
        // arrange
        ScrmProcessOrchestrationConditionDetailDTO condition1 = new ScrmProcessOrchestrationConditionDetailDTO();
        condition1.setFilterFieldId(ScrmUserTagEnum.CORP_TAG.getTagId());
        condition1.setParam(Arrays.asList("tag1"));
        condition1.setGroupId(1);
        ScrmProcessOrchestrationConditionDetailDTO condition2 = new ScrmProcessOrchestrationConditionDetailDTO();
        condition2.setFilterFieldId(ScrmUserTagEnum.CORP_TAG.getTagId());
        condition2.setParam(Arrays.asList("tag2"));
        condition2.setGroupId(1);
        ScrmProcessOrchestrationConditionDetailDTO condition3 = new ScrmProcessOrchestrationConditionDetailDTO();
        condition3.setFilterFieldId(ScrmUserTagEnum.IS_FRIEND.getTagId());
        condition3.setParam(Arrays.asList("true"));
        condition3.setGroupId(2);
        List<ScrmProcessOrchestrationConditionDetailDTO> currentNodeConditionList = Arrays.asList(condition1, condition2, condition3);
        // act
        Set<String> result = staffMomentTouchAction.getSingleNodeCorpTagList(currentNodeConditionList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for multiple groups with some having CORP_TAG and other conditions.
     */
    @Test
    public void testGetSingleNodeCorpTagListMultipleGroupsSomeWithCorpTagAndOtherConditions() throws Throwable {
        // arrange
        // Group 1: Contains both CORP_TAG and IS_FRIEND (should be skipped)
        ScrmProcessOrchestrationConditionDetailDTO condition1 = new ScrmProcessOrchestrationConditionDetailDTO();
        condition1.setFilterFieldId(ScrmUserTagEnum.CORP_TAG.getTagId());
        condition1.setParam(Arrays.asList("tag1"));
        condition1.setGroupId(1);
        ScrmProcessOrchestrationConditionDetailDTO condition2 = new ScrmProcessOrchestrationConditionDetailDTO();
        condition2.setFilterFieldId(ScrmUserTagEnum.IS_FRIEND.getTagId());
        condition2.setParam(Arrays.asList("true"));
        condition2.setGroupId(1);
        // Group 2: Contains only CORP_TAG (should be included)
        ScrmProcessOrchestrationConditionDetailDTO condition3 = new ScrmProcessOrchestrationConditionDetailDTO();
        condition3.setFilterFieldId(ScrmUserTagEnum.CORP_TAG.getTagId());
        condition3.setParam(Arrays.asList("tag2"));
        condition3.setGroupId(2);
        // Group 3: Contains only IS_FRIEND (should be skipped)
        ScrmProcessOrchestrationConditionDetailDTO condition4 = new ScrmProcessOrchestrationConditionDetailDTO();
        condition4.setFilterFieldId(ScrmUserTagEnum.IS_FRIEND.getTagId());
        condition4.setParam(Arrays.asList("true"));
        condition4.setGroupId(3);
        List<ScrmProcessOrchestrationConditionDetailDTO> currentNodeConditionList = Arrays.asList(condition1, condition2, condition3, condition4);
        // act
        Set<String> result = staffMomentTouchAction.getSingleNodeCorpTagList(currentNodeConditionList);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains("tag2"));
    }

    /**
     * Test case for null input list.
     */
    @Test
    public void testGetSingleNodeCorpTagListNullInput() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationConditionDetailDTO> currentNodeConditionList = null;
        // act
        Set<String> result = staffMomentTouchAction.getSingleNodeCorpTagList(currentNodeConditionList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for group with empty CORP_TAG parameters.
     */
    @Test
    public void testGetSingleNodeCorpTagListGroupWithEmptyCorpTagParameters() throws Throwable {
        // arrange
        ScrmProcessOrchestrationConditionDetailDTO condition = new ScrmProcessOrchestrationConditionDetailDTO();
        condition.setFilterFieldId(ScrmUserTagEnum.CORP_TAG.getTagId());
        condition.setParam(Collections.emptyList());
        condition.setGroupId(1);
        List<ScrmProcessOrchestrationConditionDetailDTO> currentNodeConditionList = Collections.singletonList(condition);
        // act
        Set<String> result = staffMomentTouchAction.getSingleNodeCorpTagList(currentNodeConditionList);
        // assert
        assertTrue(result.isEmpty());
    }
}
