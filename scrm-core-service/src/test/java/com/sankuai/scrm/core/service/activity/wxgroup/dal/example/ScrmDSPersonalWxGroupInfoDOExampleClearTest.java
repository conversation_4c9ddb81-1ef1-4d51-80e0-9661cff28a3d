package com.sankuai.scrm.core.service.activity.wxgroup.dal.example;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for ScrmDSPersonalWxGroupMemberMatchRecordDOExample clear() method
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ScrmDSPersonalWxGroupMemberMatchRecordDOExample Clear Method Tests")
public class ScrmDSPersonalWxGroupInfoDOExampleClearTest {

    @Spy
    private ScrmDSPersonalWxGroupInfoDOExample example = new ScrmDSPersonalWxGroupInfoDOExample();

    /**
     * Test scenario: Clear method when all fields are populated
     * Expected: All fields should be reset to their default values
     */
    @Test
    @DisplayName("Should clear all populated fields")
    public void testClear_WhenFieldsArePopulated() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        example.setOrderByClause("id DESC");
        example.setDistinct(true);
        example.setRows(10);
        example.setOffset(20);
        example.createCriteria().andIdEqualTo(1L);
        // act
        example.clear();
        // assert
        assertAll(() -> assertNull(example.getOrderByClause(), "orderByClause should be null"), () -> assertFalse(example.isDistinct(), "distinct should be false"), () -> assertNull(example.getRows(), "rows should be null"), () -> assertNull(example.getOffset(), "offset should be null"), () -> assertTrue(example.getOredCriteria().isEmpty(), "oredCriteria should be empty"));
    }

    /**
     * Test scenario: Clear method when fields are already in default state
     * Expected: Fields should remain in their default state
     */
    @Test
    @DisplayName("Should handle clearing already default state")
    public void testClear_WhenFieldsAreDefault() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        // act
        example.clear();
        // assert
        assertAll(() -> assertNull(example.getOrderByClause(), "orderByClause should be null"), () -> assertFalse(example.isDistinct(), "distinct should be false"), () -> assertNull(example.getRows(), "rows should be null"), () -> assertNull(example.getOffset(), "offset should be null"), () -> assertTrue(example.getOredCriteria().isEmpty(), "oredCriteria should be empty"));
    }

    /**
     * Test scenario: Clear method with multiple criteria
     * Expected: All criteria should be cleared
     */
    @Test
    @DisplayName("Should clear multiple criteria")
    public void testClear_WithMultipleCriteria() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        example.createCriteria().andIdEqualTo(1L);
        example.or().andVidEqualTo(2L);
        example.or().andCorpIdEqualTo("testCorpId");
        // verify initial state
        assertEquals(3, example.getOredCriteria().size(), "Should have 3 criteria before clear");
        // act
        example.clear();
        // assert
        assertAll(() -> assertTrue(example.getOredCriteria().isEmpty(), "oredCriteria should be empty"), () -> assertEquals(0, example.getOredCriteria().size(), "Should have no criteria after clear"));
    }

    /**
     * Test scenario: Multiple consecutive clear calls
     * Expected: Object should maintain cleared state after multiple clear calls
     */
    @Test
    @DisplayName("Should handle multiple clear calls")
    public void testClear_MultipleCalls() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        example.setOrderByClause("id DESC");
        example.setDistinct(true);
        example.createCriteria().andIdEqualTo(1L);
        // act
        example.clear();
        // second clear call
        example.clear();
        // assert
        assertAll(() -> assertNull(example.getOrderByClause(), "orderByClause should be null"), () -> assertFalse(example.isDistinct(), "distinct should be false"), () -> assertNull(example.getRows(), "rows should be null"), () -> assertNull(example.getOffset(), "offset should be null"), () -> assertTrue(example.getOredCriteria().isEmpty(), "oredCriteria should be empty"));
    }

    /**
     * Test scenario: Clear method behavior with long string value
     * Expected: Should handle long string properly
     */
    @Test
    @DisplayName("Should handle long string values")
    public void testClear_WithLongString() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        StringBuilder longString = new StringBuilder("VERY_LONG_STRING_");
        for (int i = 0; i < 1000; i++) {
            longString.append("a");
        }
        example.setOrderByClause(longString.toString());
        // act
        example.clear();
        // assert
        assertNull(example.getOrderByClause(), "orderByClause should be null");
    }

    /**
     * 测试正常情况 - 传入正整数
     * 验证: 正确设置rows值并返回当前实例
     */
    @Test
    public void testLimitWithPositiveNumber() {
        // arrange
        ScrmDSPersonalWxGroupInfoDOExample example = spy(new ScrmDSPersonalWxGroupInfoDOExample());
        int rows = 10;
        // act
        ScrmDSPersonalWxGroupInfoDOExample result = example.limit(rows);
        // assert
        verify(example, times(1)).limit(rows);
        assertEquals(rows, example.getRows());
        assertSame(example, result);
    }

    /**
     * 测试边界情况 - 传入0
     * 验证: 可以接受0值并正确设置
     */
    @Test
    public void testLimitWithZero() {
        // arrange
        ScrmDSPersonalWxGroupInfoDOExample example = spy(new ScrmDSPersonalWxGroupInfoDOExample());
        int rows = 0;
        // act
        ScrmDSPersonalWxGroupInfoDOExample result = example.limit(rows);
        // assert
        verify(example, times(1)).limit(rows);
        assertEquals(rows, example.getRows());
        assertSame(example, result);
    }

    /**
     * 测试边界情况 - 传入null
     * 验证: 可以接受null值并正确设置
     */
    @Test
    public void testLimitWithNull() {
        // arrange
        ScrmDSPersonalWxGroupInfoDOExample example = spy(new ScrmDSPersonalWxGroupInfoDOExample());
        // act
        ScrmDSPersonalWxGroupInfoDOExample result = example.limit(null);
        // assert
        verify(example, times(1)).limit(null);
        assertNull(example.getRows());
        assertSame(example, result);
    }

    /**
     * 测试边界情况 - 传入负数
     * 验证: 可以接受负数值并正确设置
     */
    @Test
    public void testLimitWithNegativeNumber() {
        // arrange
        ScrmDSPersonalWxGroupInfoDOExample example = spy(new ScrmDSPersonalWxGroupInfoDOExample());
        int rows = -5;
        // act
        ScrmDSPersonalWxGroupInfoDOExample result = example.limit(rows);
        // assert
        verify(example, times(1)).limit(rows);
        assertEquals(rows, example.getRows());
        assertSame(example, result);
    }

    /**
     * 测试方法链式调用
     * 验证: 可以连续调用limit方法并正确设置最后的值
     */
    @Test
    public void testLimitMethodChaining() {
        // arrange
        ScrmDSPersonalWxGroupInfoDOExample example = spy(new ScrmDSPersonalWxGroupInfoDOExample());
        // act
        ScrmDSPersonalWxGroupInfoDOExample result = example.limit(10).limit(20).limit(30);
        // assert
        verify(example, times(1)).limit(10);
        verify(example, times(1)).limit(20);
        verify(example, times(1)).limit(30);
        assertEquals(30, example.getRows());
        assertSame(example, result);
    }

    /**
     * Test case: Verify or() method creates and adds new criteria successfully
     * Expected: Should create new criteria and add it to oredCriteria list
     */
    @Test
    @DisplayName("Should create and add new criteria successfully")
    public void testOr_ShouldCreateAndAddNewCriteria() throws Throwable {
        // arrange
        int initialSize = example.getOredCriteria().size();
        // act
        ScrmDSPersonalWxGroupInfoDOExample.Criteria result = example.or();
        // assert
        assertNotNull(result, "Returned Criteria should not be null");
        assertEquals(initialSize + 1, example.getOredCriteria().size(), "OredCriteria list should increase by 1");
        assertTrue(example.getOredCriteria().contains(result), "OredCriteria list should contain the returned criteria");
        verify(example).createCriteriaInternal();
    }

    /**
     * Test case: Verify multiple calls to or() method create distinct criteria instances
     * Expected: Should create separate criteria instances and add them to list
     */
    @Test
    @DisplayName("Multiple calls should create distinct criteria")
    public void testOr_MultipleCalls_ShouldCreateDistinctCriteria() throws Throwable {
        // arrange
        example.clear();
        // act
        ScrmDSPersonalWxGroupInfoDOExample.Criteria criteria1 = example.or();
        ScrmDSPersonalWxGroupInfoDOExample.Criteria criteria2 = example.or();
        // assert
        assertNotNull(criteria1, "First criteria should not be null");
        assertNotNull(criteria2, "Second criteria should not be null");
        assertNotSame(criteria1, criteria2, "Should create distinct criteria instances");
        assertEquals(2, example.getOredCriteria().size(), "Should have two criteria in the list");
        assertTrue(example.getOredCriteria().contains(criteria1), "First criteria should be in the list");
        assertTrue(example.getOredCriteria().contains(criteria2), "Second criteria should be in the list");
        // Changed to expect 2 calls
        verify(example, times(2)).createCriteriaInternal();
    }

    /**
     * Test case: Verify or() method maintains correct order of criteria
     * Expected: Should maintain the order of criteria as they are added
     */
    @Test
    @DisplayName("Should maintain correct order of criteria")
    public void testOr_ShouldMaintainCriteriaOrder() throws Throwable {
        // arrange
        example.clear();
        // act
        ScrmDSPersonalWxGroupInfoDOExample.Criteria criteria1 = example.or();
        ScrmDSPersonalWxGroupInfoDOExample.Criteria criteria2 = example.or();
        ScrmDSPersonalWxGroupInfoDOExample.Criteria criteria3 = example.or();
        // assert
        assertEquals(3, example.getOredCriteria().size(), "Should have three criteria in the list");
        assertEquals(criteria1, example.getOredCriteria().get(0), "First criteria should be at index 0");
        assertEquals(criteria2, example.getOredCriteria().get(1), "Second criteria should be at index 1");
        assertEquals(criteria3, example.getOredCriteria().get(2), "Third criteria should be at index 2");
    }

    /**
     * Test case: Verify or() method behavior after clearing the example
     * Expected: Should start fresh after clearing
     */
    @Test
    @DisplayName("Should work correctly after clearing")
    public void testOr_AfterClear_ShouldStartFresh() throws Throwable {
        // arrange
        // Add initial criteria
        example.or();
        // Clear all criteria
        example.clear();
        // act
        ScrmDSPersonalWxGroupInfoDOExample.Criteria result = example.or();
        // assert
        assertNotNull(result, "Returned Criteria should not be null");
        assertEquals(1, example.getOredCriteria().size(), "Should have exactly one criteria after clear and or()");
        assertTrue(example.getOredCriteria().contains(result), "OredCriteria list should contain the new criteria");
    }
}
