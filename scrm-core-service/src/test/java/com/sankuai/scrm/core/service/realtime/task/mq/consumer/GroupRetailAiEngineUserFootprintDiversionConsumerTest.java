package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.aigc.service.config.AISceneABTestConfig;
import com.sankuai.scrm.core.service.realtime.task.domainservice.ScrmGrowthUserInfoDomainService;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineMqMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.dto.ScrmDzBizDataUserTrackDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineABTestRecordMessageProducer;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineUserFootprintLandProducer;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;
import com.dianping.cat.Cat;
import com.sankuai.scrm.core.service.aigc.service.enums.AISceneABTestStatusEnum;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import java.util.Arrays;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
public class GroupRetailAiEngineUserFootprintDiversionConsumerTest {

    @InjectMocks
    private GroupRetailAiEngineUserFootprintDiversionConsumer consumer;

    @Mock
    private IConsumerProcessor mockConsumerProcessor;

    @Mock
    private RealTimeTaskConsumerConfig consumerConfig;

    @Mock
    private AISceneABTestConfig aiSceneABTestConfig;

    @Mock
    private GroupRetailAiEngineUserFootprintLandProducer userFootprintLandProducer;

    @Mock
    private GroupRetailAiEngineABTestRecordMessageProducer abTestRecordMessageProducer;

    @Mock
    private ScrmGrowthUserInfoDomainService domainService;

    private Method recvMessageMethod;

    /**
     * Test successful initialization of consumer
     */
    @Test
    public void testAfterPropertiesSet_Success() throws Exception {
        // arrange
        try (MockedStatic<MafkaClient> mockedStatic = mockStatic(MafkaClient.class)) {
            mockedStatic.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), eq("scrm.group.retail.ai.engine.user.footprint.diversion"))).thenReturn(mockConsumerProcessor);
            // act
            consumer.afterPropertiesSet();
            // assert
            // Verify properties were set correctly
            verify(mockConsumerProcessor).recvMessageWithParallel(eq(String.class), any());
        }
    }

    /**
     * Test exception during consumer initialization
     */
    @Test
    public void testAfterPropertiesSet_WhenMafkaClientThrowsException() {
        // arrange
        try (MockedStatic<MafkaClient> mockedStatic = mockStatic(MafkaClient.class)) {
            mockedStatic.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), eq("scrm.group.retail.ai.engine.user.footprint.diversion"))).thenThrow(new RuntimeException("Mafka client error"));
            // act & assert
            Exception exception = assertThrows(Exception.class, () -> {
                consumer.afterPropertiesSet();
            });
            assertEquals("Mafka client error", exception.getMessage());
        }
    }

    /**
     * Test properties configuration
     */
    @Test
    public void testAfterPropertiesSet_VerifyProperties() throws Exception {
        // arrange
        try (MockedStatic<MafkaClient> mockedStatic = mockStatic(MafkaClient.class)) {
            mockedStatic.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), eq("scrm.group.retail.ai.engine.user.footprint.diversion"))).thenAnswer(invocation -> {
                Properties props = invocation.getArgument(0);
                assertEquals("daozong", props.getProperty(ConsumerConstants.MafkaBGNamespace));
                assertEquals("com.sankuai.medicalcosmetology.scrm.core", props.getProperty(ConsumerConstants.MafkaClientAppkey));
                assertEquals("scrm.group.retail.ai.engine.user.footprint.diversion.consumer", props.getProperty(ConsumerConstants.SubscribeGroup));
                return mockConsumerProcessor;
            });
            // act
            consumer.afterPropertiesSet();
            // assert
            verify(mockConsumerProcessor).recvMessageWithParallel(eq(String.class), any());
        }
    }

    @BeforeEach
    void setUp() throws Exception {
        recvMessageMethod = GroupRetailAiEngineUserFootprintDiversionConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        recvMessageMethod.setAccessible(true);
    }

    @Test
    void testRecvMessageWhenMessageBodyIsNull() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);
        when(message.getBody()).thenReturn(null);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(userFootprintLandProducer, abTestRecordMessageProducer);
    }

    @Test
    void testRecvMessageWhenMessageParseFails() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);
        when(message.getBody()).thenReturn("invalid json");
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(userFootprintLandProducer, abTestRecordMessageProducer);
    }

    @Test
    void testRecvMessageWhenUserTrackDTOIsNull() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);
        GroupRetailAiEngineMqMessageDTO dto = new GroupRetailAiEngineMqMessageDTO();
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(userFootprintLandProducer, abTestRecordMessageProducer);
    }

    @Test
    void testRecvMessageWhenUserIdListIsEmpty() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);
        GroupRetailAiEngineMqMessageDTO dto = new GroupRetailAiEngineMqMessageDTO();
        ScrmDzBizDataUserTrackDTO userTrackDTO = new ScrmDzBizDataUserTrackDTO();
        userTrackDTO.setUserid(new ArrayList<>());
        dto.setUserTrackDTO(userTrackDTO);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(userFootprintLandProducer, abTestRecordMessageProducer);
    }

    @Test
    void testRecvMessageWhenPlatformIsDpAndConversionSucceeds() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);
        GroupRetailAiEngineMqMessageDTO dto = new GroupRetailAiEngineMqMessageDTO();
        ScrmDzBizDataUserTrackDTO userTrackDTO = new ScrmDzBizDataUserTrackDTO();
        userTrackDTO.setUserid(Collections.singletonList(123L));
        userTrackDTO.setPlatformlist(Collections.singletonList("dp"));
        dto.setUserTrackDTO(userTrackDTO);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(domainService.queryMtUserIdByDpUserId(123L)).thenReturn(456L);
        when(consumerConfig.isInWhitelist(anyList())).thenReturn(true);
        when(consumerConfig.getValidAppId(anyLong(), any())).thenReturn("app123");
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootprintLandProducer).sendMessage(any());
        verify(abTestRecordMessageProducer, never()).sendMessage(any());
    }

    @Test
    void testRecvMessageWhenPlatformIsDpButConversionFails() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);
        GroupRetailAiEngineMqMessageDTO dto = new GroupRetailAiEngineMqMessageDTO();
        ScrmDzBizDataUserTrackDTO userTrackDTO = new ScrmDzBizDataUserTrackDTO();
        userTrackDTO.setUserid(Collections.singletonList(123L));
        userTrackDTO.setPlatformlist(Collections.singletonList("dp"));
        dto.setUserTrackDTO(userTrackDTO);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(domainService.queryMtUserIdByDpUserId(123L)).thenReturn(null);
        when(consumerConfig.isInWhitelist(anyList())).thenReturn(false);
        when(aiSceneABTestConfig.getTestVersion()).thenReturn("v1");
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(abTestRecordMessageProducer).sendMessage(any());
        verify(userFootprintLandProducer, never()).sendMessage(any());
    }

    @Test
    void testRecvMessageWhenUserInWhitelistButValidAppIdIsBlank() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);
        GroupRetailAiEngineMqMessageDTO dto = new GroupRetailAiEngineMqMessageDTO();
        ScrmDzBizDataUserTrackDTO userTrackDTO = new ScrmDzBizDataUserTrackDTO();
        userTrackDTO.setUserid(Collections.singletonList(123L));
        userTrackDTO.setPlatformlist(Collections.singletonList("other"));
        dto.setUserTrackDTO(userTrackDTO);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(consumerConfig.isInWhitelist(anyList())).thenReturn(true);
        when(consumerConfig.getValidAppId(anyLong(), any())).thenReturn("");
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootprintLandProducer, never()).sendMessage(any());
        verify(abTestRecordMessageProducer, never()).sendMessage(any());
    }

    @Test
    void testRecvMessageWhenUserNotInWhitelist() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);
        GroupRetailAiEngineMqMessageDTO dto = new GroupRetailAiEngineMqMessageDTO();
        ScrmDzBizDataUserTrackDTO userTrackDTO = new ScrmDzBizDataUserTrackDTO();
        userTrackDTO.setUserid(Collections.singletonList(123L));
        userTrackDTO.setPlatformlist(Collections.singletonList("other"));
        dto.setUserTrackDTO(userTrackDTO);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(consumerConfig.isInWhitelist(anyList())).thenReturn(false);
        when(aiSceneABTestConfig.getTestVersion()).thenReturn("v1");
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(abTestRecordMessageProducer).sendMessage(any());
        verify(userFootprintLandProducer, never()).sendMessage(any());
    }

    @Test
    public void testRecvMessageNullMessageBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, null, null);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(userFootprintLandProducer, abTestRecordMessageProducer);
    }

    @Test
    public void testRecvMessageInvalidMessageStructure() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, null, "{}");
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(userFootprintLandProducer, abTestRecordMessageProducer);
    }

    @Test
    public void testRecvMessageDpPlatformUserConversion() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO dto = new GroupRetailAiEngineMqMessageDTO();
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setPlatformlist(Arrays.asList("dp"));
        dto.setUserTrackDTO(trackDTO);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, null, JsonUtils.toStr(dto));
        when(domainService.queryMtUserIdByDpUserId(123L)).thenReturn(456L);
        when(consumerConfig.isInWhitelist(anyList())).thenReturn(true);
        when(consumerConfig.getValidAppId(eq(456L), any())).thenReturn("app123");
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootprintLandProducer).sendMessage(any());
        verifyNoInteractions(abTestRecordMessageProducer);
    }

    @Test
    public void testRecvMessageWhitelistUserValidAppId() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO dto = new GroupRetailAiEngineMqMessageDTO();
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setPlatformlist(Arrays.asList("other"));
        dto.setUserTrackDTO(trackDTO);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, null, JsonUtils.toStr(dto));
        when(consumerConfig.isInWhitelist(anyList())).thenReturn(true);
        when(consumerConfig.getValidAppId(eq(123L), any())).thenReturn("app123");
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootprintLandProducer).sendMessage(any());
        verifyNoInteractions(abTestRecordMessageProducer);
    }

    @Test
    public void testRecvMessageWhitelistUserInvalidAppId() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO dto = new GroupRetailAiEngineMqMessageDTO();
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setPlatformlist(Arrays.asList("other"));
        dto.setUserTrackDTO(trackDTO);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, null, JsonUtils.toStr(dto));
        when(consumerConfig.isInWhitelist(anyList())).thenReturn(true);
        when(consumerConfig.getValidAppId(eq(123L), any())).thenReturn("");
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(userFootprintLandProducer);
        verifyNoInteractions(abTestRecordMessageProducer);
    }

    @Test
    public void testRecvMessageNonWhitelistUser() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO dto = new GroupRetailAiEngineMqMessageDTO();
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setPlatformlist(Arrays.asList("other"));
        dto.setUserTrackDTO(trackDTO);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, null, JsonUtils.toStr(dto));
        when(consumerConfig.isInWhitelist(anyList())).thenReturn(false);
        when(aiSceneABTestConfig.getTestVersion()).thenReturn("v1");
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(abTestRecordMessageProducer).sendMessage(any());
        verifyNoInteractions(userFootprintLandProducer);
    }

    @Test
    public void testRecvMessageProcessingException() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, null, "invalid json");
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(userFootprintLandProducer, abTestRecordMessageProducer);
    }

    @Test
    public void testRecvMessageEmptyUserIdList() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO dto = new GroupRetailAiEngineMqMessageDTO();
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setUserid(new ArrayList<>());
        dto.setUserTrackDTO(trackDTO);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, null, JsonUtils.toStr(dto));
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(userFootprintLandProducer, abTestRecordMessageProducer);
    }

    @Test
    public void testRecvMessageDpPlatformUserNoMapping() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO dto = new GroupRetailAiEngineMqMessageDTO();
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setPlatformlist(Arrays.asList("dp"));
        dto.setUserTrackDTO(trackDTO);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, null, JsonUtils.toStr(dto));
        when(domainService.queryMtUserIdByDpUserId(123L)).thenReturn(null);
        when(consumerConfig.isInWhitelist(anyList())).thenReturn(false);
        when(aiSceneABTestConfig.getTestVersion()).thenReturn("v1");
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(abTestRecordMessageProducer).sendMessage(any());
        verifyNoInteractions(userFootprintLandProducer);
    }
}
