package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.automatedmanagement.request.coupon.attribution.ScrmCouponAttributionRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.coupon.attribution.ScrmCouponAttributionResponse;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class ScrmCouponAttributionServiceImplQueryCouponAttributionOnlyByUserIdTest {

    @InjectMocks
    private ScrmCouponAttributionServiceImpl scrmCouponAttributionService;

    @Mock
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordDOMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试场景：mtUserID 不为 null，且查询结果不为空
     */
    @Test
    void testQueryCouponAttributionOnlyByUserIdWithRecordFound() {
        // arrange
        ScrmCouponAttributionRequest request = new ScrmCouponAttributionRequest();
        request.setMtUserID(123L);
        List<ScrmSceneCouponRecords> recordList = new ArrayList<>();
        recordList.add(new ScrmSceneCouponRecords());
        when(scrmSceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(recordList);
        // act
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttributionOnlyByUserId(request);
        // assert
        assertNotNull(response);
        assertTrue(response.getData().isCommunityChannel());
    }

    /**
     * 测试场景：mtUserID 不为 null，且查询结果为空
     */
    @Test
    void testQueryCouponAttributionOnlyByUserIdWithNoRecordFound() {
        // arrange
        ScrmCouponAttributionRequest request = new ScrmCouponAttributionRequest();
        request.setMtUserID(123L);
        when(scrmSceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(new ArrayList<>());
        // act
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttributionOnlyByUserId(request);
        // assert
        assertNotNull(response);
        assertFalse(response.getData().isCommunityChannel());
    }

    /**
     * 测试场景：mtUserID 为 null
     */
    @Test
    void testQueryCouponAttributionOnlyByUserIdWithNullMtUserID() {
        // arrange
        ScrmCouponAttributionRequest request = new ScrmCouponAttributionRequest();
        request.setMtUserID(null);
        // act
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttributionOnlyByUserId(request);
        // assert
        assertNotNull(response);
        assertEquals("参数错误", response.getMsg());
    }
}
