package com.sankuai.scrm.core.service.infrastructure.acl.ds;

import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.SendGroupMsgTRequest;
import com.sankuai.service.fe.corp.ds.TResponse.BaseTResponse;
import com.sankuai.service.fe.corp.ds.TResponse.openapi.msg.SendGroupMsgTResponse;
import com.sankuai.service.fe.corp.ds.tservice.openapi.msg.GroupMsgTService;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertNull;
import static org.junit.Assert.assertSame;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DsGroupMessageAclTest {

    @InjectMocks
    private DsGroupMessageAcl dsGroupMessageAcl;

    @Mock(lenient = true)
    private GroupMsgTService groupMsgTService;

    private SendGroupMsgTRequest request;

    private BaseTResponse<SendGroupMsgTResponse> response;

    @Before
    public void setUp() {
        request = new SendGroupMsgTRequest();
        response = BaseTResponse.buildSuccess(new SendGroupMsgTResponse());
    }

    @Test
    public void testSendGroupMsgRequestIsNull() throws Throwable {
        assertNull(dsGroupMessageAcl.sendGroupMsg(null));
    }

    @Test
    public void testSendGroupMsgResponseIsNull() throws Throwable {
        when(groupMsgTService.sendGroupMsg(request)).thenReturn(null);
        assertNull(dsGroupMessageAcl.sendGroupMsg(request));
    }

    @Test
    public void testSendGroupMsgResponseCodeIsNotZero() throws Throwable {
        response.setCode(1);
        when(groupMsgTService.sendGroupMsg(request)).thenReturn(response);
        assertNull(dsGroupMessageAcl.sendGroupMsg(request));
    }

    @Test
    public void testSendGroupMsgResponseCodeIsZero() throws Throwable {
        response.setCode(0);
        when(groupMsgTService.sendGroupMsg(request)).thenReturn(response);
        assertSame(response.getData(), dsGroupMessageAcl.sendGroupMsg(request));
    }

    @Test
    public void testSendGroupMsgThrowsTException() throws Throwable {
        when(groupMsgTService.sendGroupMsg(request)).thenThrow(TException.class);
        assertNull(dsGroupMessageAcl.sendGroupMsg(request));
    }
}
