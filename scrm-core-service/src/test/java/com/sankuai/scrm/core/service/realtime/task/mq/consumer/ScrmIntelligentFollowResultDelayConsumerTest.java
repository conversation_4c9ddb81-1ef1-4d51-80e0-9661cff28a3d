package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.dashboard.dal.dto.EsFridayIntelligentFollowLog;
import com.sankuai.scrm.core.service.dashboard.domain.DashBoardESWriteDomainService;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmFridayIntelligentFollowLogDO;
import com.sankuai.scrm.core.service.realtime.task.dal.example.ScrmFridayIntelligentFollowLogDOExample;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmFridayIntelligentFollowLogDOMapper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Field;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ScrmIntelligentFollowResultDelayConsumerTest {

    //     // This test case is removed as it's not meaningful
    @InjectMocks
    private ScrmIntelligentFollowResultDelayConsumer consumer;

    @Mock
    private IConsumerProcessor mockConsumerProcessor;

    @Mock
    private DashBoardESWriteDomainService dashBoardESWriteDomainService;

    @Mock
    private ScrmFridayIntelligentFollowLogDOMapper fridayIntelligentFollowLogDOMapper;

    private MockedStatic<MafkaClient> mockedMafkaClient;

    @BeforeEach
    void setUp() {
        mockedMafkaClient = mockStatic(MafkaClient.class);
    }

    @AfterEach
    void tearDown() {
        mockedMafkaClient.close();
    }

    private void setConsumerField(ScrmIntelligentFollowResultDelayConsumer instance, IConsumerProcessor value) throws Exception {
        Field consumerField = ScrmIntelligentFollowResultDelayConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(instance, value);
    }

    /**
     * Test successful initialization of consumer
     */
    @Test
    public void testAfterPropertiesSet_Success() throws Throwable {
        // arrange
        mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), eq("scrm_intelligent_follow_result_delay_msg"))).thenReturn(mockConsumerProcessor);
        // act
        consumer.afterPropertiesSet();
        // assert
        mockedMafkaClient.verify(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), eq("scrm_intelligent_follow_result_delay_msg")));
        verify(mockConsumerProcessor).recvMessageWithParallel(eq(String.class), any());
    }

    /**
     * Test consumer creation failure
     */
    @Test
    public void testAfterPropertiesSet_ConsumerCreationFailure() throws Throwable {
        // arrange
        mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), any(String.class))).thenThrow(new RuntimeException("Failed to create consumer"));
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> consumer.afterPropertiesSet());
        assertEquals("Failed to create consumer", exception.getMessage());
    }

    /**
     * Test message receiving setup failure
     */
    @Test
    public void testAfterPropertiesSet_MessageReceivingSetupFailure() throws Throwable {
        // arrange
        mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), any(String.class))).thenReturn(mockConsumerProcessor);
        doThrow(new RuntimeException("Failed to setup message receiving")).when(mockConsumerProcessor).recvMessageWithParallel(any(), any());
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> consumer.afterPropertiesSet());
        assertEquals("Failed to setup message receiving", exception.getMessage());
    }

    /**
     * Test with null consumer processor - removed as it's not a valid test case
     * The actual implementation doesn't handle null consumer case,
     * so testing it would just be testing Java's NPE behavior
     */
    // @Test
    // public void testAfterPropertiesSet_NullConsumerProcessor() {
    /**
     * Test case when consumer is null - should do nothing
     */
    @Test
    public void testDestroyWhenConsumerIsNull() throws Throwable {
        // arrange
        ScrmIntelligentFollowResultDelayConsumer consumer = new ScrmIntelligentFollowResultDelayConsumer();
        setConsumerField(consumer, null);
        // act & assert
        assertDoesNotThrow(() -> consumer.destroy());
    }

    /**
     * Test case when consumer is not null - should call close
     */
    @Test
    public void testDestroyWhenConsumerIsNotNull() throws Throwable {
        // arrange
        ScrmIntelligentFollowResultDelayConsumer consumer = new ScrmIntelligentFollowResultDelayConsumer();
        setConsumerField(consumer, mockConsumerProcessor);
        // act
        consumer.destroy();
        // assert
        verify(mockConsumerProcessor).close();
        verifyNoMoreInteractions(mockConsumerProcessor);
    }

    /**
     * Test case when consumer close throws exception - should propagate exception
     */
    @Test
    public void testDestroyWhenConsumerCloseThrowsException() throws Throwable {
        // arrange
        ScrmIntelligentFollowResultDelayConsumer consumer = new ScrmIntelligentFollowResultDelayConsumer();
        setConsumerField(consumer, mockConsumerProcessor);
        Exception expectedException = new Exception("Close failed");
        doThrow(expectedException).when(mockConsumerProcessor).close();
        // act & assert
        Exception actualException = assertThrows(Exception.class, () -> consumer.destroy());
        verify(mockConsumerProcessor).close();
        verifyNoMoreInteractions(mockConsumerProcessor);
    }

    @Test
    public void testWriteLogStepExecuteResultDTONull() throws Throwable {
        // arrange
        IntelligentFollowResultDTO mqMessageDTO = new IntelligentFollowResultDTO();

        // act
        ReflectionTestUtils.invokeMethod(consumer, "writeLog", mqMessageDTO, null);

        // assert
        // 无任何操作，验证未调用任何方法
        verifyNoInteractions(dashBoardESWriteDomainService, fridayIntelligentFollowLogDOMapper);
    }

    /**
     * 测试正常场景
     */
    @Test
    public void testWriteLogNormalCase() throws Throwable {
        // arrange
        IntelligentFollowResultDTO mqMessageDTO = new IntelligentFollowResultDTO();
        mqMessageDTO.setEsId("es123");
        mqMessageDTO.setUserId(123L);
        mqMessageDTO.setAppId("app123");

        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        stepExecuteResultDTO.setCode(200);
        stepExecuteResultDTO.setMsg("Success");
        stepExecuteResultDTO.setSuccess(true);

        // act
        ReflectionTestUtils.invokeMethod(consumer, "writeLog", mqMessageDTO, stepExecuteResultDTO);

        // assert
        verify(dashBoardESWriteDomainService, times(1)).updateESDocByIdSelective(eq("scrm_friday_intelligent_follow_log"), any(EsFridayIntelligentFollowLog.class), eq("es123"));
        verify(fridayIntelligentFollowLogDOMapper, times(1)).updateByExampleSelective(any(ScrmFridayIntelligentFollowLogDO.class), any(ScrmFridayIntelligentFollowLogDOExample.class));
    }

    /**
     * 测试 fridayIntelligentFollowLogDOMapper 抛出异常的场景
     */
    @Test
    public void testWriteLogMapperException() throws Throwable {
        // arrange
        IntelligentFollowResultDTO mqMessageDTO = new IntelligentFollowResultDTO();
        mqMessageDTO.setEsId("es123");
        mqMessageDTO.setUserId(123L);
        mqMessageDTO.setAppId("app123");

        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        stepExecuteResultDTO.setCode(200);
        stepExecuteResultDTO.setMsg("Success");
        stepExecuteResultDTO.setSuccess(true);

        doThrow(new RuntimeException("Mapper update error")).when(fridayIntelligentFollowLogDOMapper).updateByExampleSelective(any(ScrmFridayIntelligentFollowLogDO.class), any(ScrmFridayIntelligentFollowLogDOExample.class));

        // act
        ReflectionTestUtils.invokeMethod(consumer, "writeLog", mqMessageDTO, stepExecuteResultDTO);

        // assert
        verify(dashBoardESWriteDomainService, times(1)).updateESDocByIdSelective(eq("scrm_friday_intelligent_follow_log"), any(EsFridayIntelligentFollowLog.class), eq("es123"));
        verify(fridayIntelligentFollowLogDOMapper, times(1)).updateByExampleSelective(any(ScrmFridayIntelligentFollowLogDO.class), any(ScrmFridayIntelligentFollowLogDOExample.class));
    }
}
