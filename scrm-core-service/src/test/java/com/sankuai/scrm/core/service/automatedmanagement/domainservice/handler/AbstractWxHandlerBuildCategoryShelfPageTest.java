package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyString;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;

@ExtendWith(MockitoExtension.class)
public class AbstractWxHandlerBuildCategoryShelfPageTest {

    private final TestWxHandler testWxHandler = new TestWxHandler();

    private TestableAbstractWxHandler handler;

    private final AbstractWxHandler abstractWxHandler = new AbstractWxHandler() {

        @Override
        protected void logRequest(String request, String appId) {
        }

        @Override
        protected String getShorUrl(String coreUrl, boolean forceShortUrl) {
            return coreUrl.replace("http:", "https:");
        }
    };

    @BeforeEach
    void setUp() {
        handler = spy(new TestableAbstractWxHandler());
    }


    // Test implementation of AbstractWxHandler
    private static class TestWxHandler extends AbstractWxHandler {

        @Override
        protected void logRequest(String request, String appId) {
            // Do nothing for testing
        }

        @Override
        protected String getShorUrl(String coreUrl, boolean forceShortUrl) {
            return coreUrl;
        }

        @Override
        public String buildProductUrl(String originpath, Long ProductId, String communityDistributorCode, String appId, boolean isDeepSea, boolean isForQrcode) {
            return null;
        }

        @Override
        public String buildPageUrl(ScrmAmProcessOrchestrationActivSceneCodeDO scrmAmProcessOrchestrationActivSceneCodeDO, String communityDistributorCode, String appId, boolean isDeepSea, boolean isForQrcode) {
            return null;
        }

        @Override
        public String buildPageUrl(ScrmProcessOrchestrationAttachmentSupplyDetailDTO detailDTO, String communityDistributorCode, String appId, boolean isDeepSea, boolean isForQrcode) {
            return null;
        }
    }

    /**
     * Create a testable implementation of AbstractWxHandler
     */
    private static class TestableAbstractWxHandler extends AbstractWxHandler {

        @Override
        protected void logRequest(String request, String appId) {
            // Not needed for these tests
        }

        @Override
        protected String getShorUrl(String coreUrl, boolean forceShortUrl) {
            // Not needed for these tests
            return null;
        }
    }

    /**
     * Test case: URL starts with "http:" should be converted to "https:"
     */
    @Test
    void testDealShortUrlWithHttpProtocol() {
        // arrange
        String input = "http://example.com/path?param=value";
        // act
        String result = handler.dealShortUrl(input);
        // assert
        assertEquals("https://example.com/path?param=value", result);
        verify(handler, times(1)).dealShortUrl(input);
    }

    /**
     * Test case: URL starts with "https:" should remain unchanged
     */
    @Test
    void testDealShortUrlWithHttpsProtocol() {
        // arrange
        String input = "https://example.com/path?param=value";
        // act
        String result = handler.dealShortUrl(input);
        // assert
        assertEquals("https://example.com/path?param=value", result);
        verify(handler, times(1)).dealShortUrl(input);
    }

    /**
     * Test case: Empty string should return empty string
     */
    @Test
    void testDealShortUrlWithEmptyString() {
        // arrange
        String input = "";
        // act
        String result = handler.dealShortUrl(input);
        // assert
        assertEquals("", result);
        verify(handler, times(1)).dealShortUrl(input);
    }

    /**
     * Test case: Null input should return null
     */
    @Test
    void testDealShortUrlWithNull() {
        // arrange
        String input = null;
        // act
        String result = handler.dealShortUrl(input);
        // assert
        assertNull(result);
        verify(handler, times(1)).dealShortUrl(input);
    }

    /**
     * Test case: Non-URL string should remain unchanged
     */
    @Test
    void testDealShortUrlWithNonUrlString() {
        // arrange
        String input = "regular text without protocol";
        // act
        String result = handler.dealShortUrl(input);
        // assert
        assertEquals("regular text without protocol", result);
        verify(handler, times(1)).dealShortUrl(input);
    }

    @Test
    void testBuildPageUrl_PoiRestrictZero_ExcludesPoiId() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneDO.setPoiRestrict(0);
        sceneDO.setSceneCode("testScene");
        ExecuteManagementDTO executeDTO = new ExecuteManagementDTO();
        executeDTO.setPoiId("12345");
        try (MockedStatic<Environment> envMock = Mockito.mockStatic(Environment.class)) {
            envMock.when(Environment::isProductEnv).thenReturn(false);
            // act
            String result = abstractWxHandler.buildPageUrl(sceneDO, "distributor", "appId", false, false, executeDTO);
            // assert
            assertNotNull(result);
            assertTrue(result.startsWith("/index/pages/h5/gnc/h5.html?weburl="));
            String decodedResult = URLDecoder.decode(result, StandardCharsets.UTF_8.toString());
            assertTrue(decodedResult.contains("testScene"));
            assertTrue(decodedResult.contains("distributor"));
            assertFalse(decodedResult.contains("poiId=12345"));
        }
    }

    @Test
    void testBuildPageUrl_ProductionEnv_UsesProductionDomain() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneDO.setPoiRestrict(1);
        sceneDO.setSceneCode("prodScene");
        ExecuteManagementDTO executeDTO = new ExecuteManagementDTO();
        executeDTO.setPoiId("67890");
        try (MockedStatic<Environment> envMock = Mockito.mockStatic(Environment.class)) {
            envMock.when(Environment::isProductEnv).thenReturn(true);
            // act
            String result = abstractWxHandler.buildPageUrl(sceneDO, "prodDist", "prodApp", false, false, executeDTO);
            // assert
            assertNotNull(result);
            assertTrue(result.startsWith("/index/pages/h5/gnc/h5.html?weburl="));
            String decodedResult = URLDecoder.decode(result, StandardCharsets.UTF_8.toString());
            assertTrue(decodedResult.contains("g.meituan.com"));
            assertTrue(decodedResult.contains("prodScene"));
            assertTrue(decodedResult.contains("67890"));
        }
    }

    @Test
    void testBuildPageUrl_NonProductionEnv_UsesTestDomain() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneDO.setPoiRestrict(1);
        sceneDO.setSceneCode("testScene");
        ExecuteManagementDTO executeDTO = new ExecuteManagementDTO();
        executeDTO.setPoiId("12345");
        try (MockedStatic<Environment> envMock = Mockito.mockStatic(Environment.class)) {
            envMock.when(Environment::isProductEnv).thenReturn(false);
            // act
            String result = abstractWxHandler.buildPageUrl(sceneDO, "testDist", "testApp", false, false, executeDTO);
            // assert
            assertNotNull(result);
            assertTrue(result.startsWith("/index/pages/h5/gnc/h5.html?weburl="));
            String decodedResult = URLDecoder.decode(result, StandardCharsets.UTF_8.toString());
            assertTrue(decodedResult.contains("test-g.meituan.com"));
            assertTrue(decodedResult.contains("testScene"));
            assertTrue(decodedResult.contains("12345"));
        }
    }

    @Test
    void testBuildPageUrl_ForQrcode_NonDeepSea() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneDO.setPoiRestrict(1);
        sceneDO.setSceneCode("qrScene");
        ExecuteManagementDTO executeDTO = new ExecuteManagementDTO();
        executeDTO.setPoiId("11111");
        try (MockedStatic<Environment> envMock = Mockito.mockStatic(Environment.class)) {
            envMock.when(Environment::isProductEnv).thenReturn(false);
            // act
            String result = abstractWxHandler.buildPageUrl(sceneDO, "qrDist", "qrApp", false, true, executeDTO);
            // assert
            assertNotNull(result);
            assertTrue(result.startsWith("/index/pages/h5/gnc/h5?weburl="));
            String decodedResult = URLDecoder.decode(result, StandardCharsets.UTF_8.toString());
            assertTrue(decodedResult.contains("qrScene"));
            assertTrue(decodedResult.contains("11111"));
            assertTrue(decodedResult.contains("f_userId=1"));
        }
    }

    @Test
    void testBuildPageUrl_NullExecuteDTO_HandlesGracefully() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneDO.setPoiRestrict(1);
        sceneDO.setSceneCode("nullTestScene");
        try (MockedStatic<Environment> envMock = Mockito.mockStatic(Environment.class)) {
            envMock.when(Environment::isProductEnv).thenReturn(false);
            // act
            String result = abstractWxHandler.buildPageUrl(sceneDO, "nullDist", "nullApp", false, false, null);
            // assert
            assertNotNull(result);
            assertTrue(result.startsWith("/index/pages/h5/gnc/h5.html?weburl="));
            String decodedResult = URLDecoder.decode(result, StandardCharsets.UTF_8.toString());
            assertTrue(decodedResult.contains("nullTestScene"));
            assertFalse(decodedResult.contains("poiId="));
        }
    }
}
