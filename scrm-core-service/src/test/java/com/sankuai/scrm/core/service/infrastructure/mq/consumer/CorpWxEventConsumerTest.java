package com.sankuai.scrm.core.service.infrastructure.mq.consumer;

import com.dianping.lion.Environment;
import com.sankuai.dz.srcm.envrequestforwarding.enums.ResultTypeEnum;
import com.sankuai.dz.srcm.envrequestforwarding.request.DispatchedOnlineDataRequest;
import com.sankuai.scrm.core.service.envrequestforwarding.mq.consumer.OfflineDataSyncAbstractConsumer;
import com.sankuai.scrm.core.service.infrastructure.mq.message.MessageSender;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.scrm.core.service.util.XmlUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CorpWxEventConsumerTest {

    @Spy
    @InjectMocks
    private CorpWxEventConsumer corpWxEventConsumer;

    @Mock
    private List<MessageSender> messageSenderList;

    private MockedStatic<Environment> mockedEnvironment;

    private MockedStatic<OfflineDataSyncAbstractConsumer> mockedAbstractConsumer;

    @Before
    public void setUp() {
        // Mock Environment
        mockedEnvironment = Mockito.mockStatic(Environment.class);
        mockedEnvironment.when(Environment::isProductEnv).thenReturn(false);
        // Mock OfflineDataSyncAbstractConsumer
        mockedAbstractConsumer = Mockito.mockStatic(OfflineDataSyncAbstractConsumer.class);
        mockedAbstractConsumer.when(() -> OfflineDataSyncAbstractConsumer.offlineAndCorpIdCheck(anyString())).thenReturn(true);
    }

    @After
    public void tearDown() {
        if (mockedEnvironment != null) {
            mockedEnvironment.close();
        }
        if (mockedAbstractConsumer != null) {
            mockedAbstractConsumer.close();
        }
    }


    /**
     * 测试消息为空的情况
     */
    @Test
    public void testOfflineSyncHandleEmptyMsg() throws Throwable {
        // arrange
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        request.setCorpId("allowedCorpId");
        //request.setMsgContent("{\"decryptXml\":\"\"}");
        //when(corpWxEventConsumer.offlineAndCorpIdCheck(anyString())).thenReturn(true);
        // act
        ResultTypeEnum result = corpWxEventConsumer.offlineSyncHandle(request);

        // assert
        assertEquals(ResultTypeEnum.EMPTY_MSG, result);
    }

    /**
     * 测试消息不合法的情况
     */
    @Test
    public void testOfflineSyncHandleInvalidMsg() throws Throwable {
        // arrange
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        request.setCorpId("allowedCorpId");
        request.setMsgContent("dfafafa");

        // act
        ResultTypeEnum result = corpWxEventConsumer.offlineSyncHandle(request);

        // assert
        assertEquals(ResultTypeEnum.EXCEPTION, result);
    }

    /**
     * 测试消息转发成功的情况
     */
    @Test
    public void testOfflineSyncHandleSuccess() throws Throwable {
        // arrange
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        request.setCorpId("allowedCorpId");
        request.setMsgContent("{\"timeStamp\":\"1731652450\",\"decryptXml\":\"<xml><WelcomeCode>yC3UT2Vd1jCIDB5FSkFskeZBbGLtG_yAsN6pd0-ra9c</WelcomeCode><State>47893</State><UserID>LiuAo</UserID><FromUserName>sys</FromUserName><ToUserName>ww4c59cf2eafaf02fd</ToUserName><CreateTime>1731652450</CreateTime><Event>change_external_contact</Event><MsgType>event</MsgType><ExternalUserID>wm8sspCgAAdrkGT5eDWuaDIcN8VGG5FA</ExternalUserID><ChangeType>add_external_contact</ChangeType></xml>\"}");

        MessageSender mockMessageSender = Mockito.mock(MessageSender.class);
//        when(mockMessageSender.support(Mockito.anyMap())).thenReturn(true);
       // when(messageSenderList.stream()).thenReturn(Arrays.asList(mockMessageSender).stream());

        // act
        ResultTypeEnum result = corpWxEventConsumer.offlineSyncHandle(request);

        // assert
        assertEquals(ResultTypeEnum.INVALID_MSG, result);
    }

    /**
     * 测试异常情况
     */
    @Test
    public void testOfflineSyncHandleException() throws Throwable {
        // arrange
        DispatchedOnlineDataRequest request = null;

        // act
        ResultTypeEnum result = corpWxEventConsumer.offlineSyncHandle(request);

        // assert
        assertEquals(ResultTypeEnum.EXCEPTION, result);
    }
}
