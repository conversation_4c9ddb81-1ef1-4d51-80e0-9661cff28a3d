package com.sankuai.scrm.core.service.couponIntegration.utils;

import static org.junit.Assert.*;
import java.util.Date;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CouponIntegrationUtilDateToStringTest {

    private CouponIntegrationUtil couponIntegrationUtil = new CouponIntegrationUtil();

    /**
     * 测试 dateToString 方法，传入的 Date 对象不为 null
     */
    @Test
    public void testDateToStringWhenDateIsNotNull() throws Throwable {
        // arrange
        Date date = new Date();
        // act
        String result = couponIntegrationUtil.dateToString(date);
        // assert
        assertNotNull(result);
        assertTrue(result.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));
    }

    /**
     * 测试 dateToString 方法，传入的 Date 对象为 null
     */
    @Test(expected = NullPointerException.class)
    public void testDateToStringWhenDateIsNull() throws Throwable {
        // arrange
        Date date = null;
        // act
        couponIntegrationUtil.dateToString(date);
        // assert
        // 期望抛出 NullPointerException
    }
}
