package com.sankuai.scrm.core.service.realtime.task.dal.example;

import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ScrmRealtimeSceneUserRecordDOExampleTest {

    private ScrmRealtimeSceneUserRecordDOExample example;

    @Before
    public void setUp() {
        example = new ScrmRealtimeSceneUserRecordDOExample();
    }

    /**
     * 测试 createCriteriaInternal 方法是否能正确创建并返回一个新的 Criteria 对象
     */
    @Test
    public void testCreateCriteriaInternal() throws Throwable {
        // arrange
        ScrmRealtimeSceneUserRecordDOExample example = new ScrmRealtimeSceneUserRecordDOExample();
        // act
        ScrmRealtimeSceneUserRecordDOExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertNotNull(result);
    }

    /**
     * 测试 limit 方法，offset 和 rows 都为正常值
     */
    @Test
    public void testLimitNormalValue() {
        // arrange
        ScrmRealtimeSceneUserRecordDOExample example = new ScrmRealtimeSceneUserRecordDOExample();
        Integer offset = 10;
        Integer rows = 20;
        // act
        ScrmRealtimeSceneUserRecordDOExample result = example.limit(offset, rows);
        // assert
        assertEquals(offset, result.getOffset());
        assertEquals(rows, result.getRows());
    }

    /**
     * 测试 limit 方法，offset 和 rows 都为边界值
     */
    @Test
    public void testLimitBoundaryValue() {
        // arrange
        ScrmRealtimeSceneUserRecordDOExample example = new ScrmRealtimeSceneUserRecordDOExample();
        Integer offset = 0;
        Integer rows = Integer.MAX_VALUE;
        // act
        ScrmRealtimeSceneUserRecordDOExample result = example.limit(offset, rows);
        // assert
        assertEquals(offset, result.getOffset());
        assertEquals(rows, result.getRows());
    }

    /**
     * 测试 limit 方法，offset 和 rows 都为异常值
     */
    @Test
    public void testLimitExceptionValue() {
        // arrange
        ScrmRealtimeSceneUserRecordDOExample example = new ScrmRealtimeSceneUserRecordDOExample();
        Integer offset = null;
        Integer rows = null;
        // act
        ScrmRealtimeSceneUserRecordDOExample result = example.limit(offset, rows);
        // assert
        assertEquals(offset, result.getOffset());
        assertEquals(rows, result.getRows());
    }

    /**
     * 测试 limit 方法，offset 为正常值，rows 为异常值
     */
    @Test
    public void testLimitMixedValue() {
        // arrange
        ScrmRealtimeSceneUserRecordDOExample example = new ScrmRealtimeSceneUserRecordDOExample();
        Integer offset = 10;
        Integer rows = null;
        // act
        ScrmRealtimeSceneUserRecordDOExample result = example.limit(offset, rows);
        // assert
        assertEquals(offset, result.getOffset());
        assertEquals(rows, result.getRows());
    }

    /**
     * 测试limit方法，输入正常的非负整数值
     */
    @Test
    public void testLimitNormal() throws Throwable {
        // arrange
        ScrmRealtimeSceneUserRecordDOExample example = new ScrmRealtimeSceneUserRecordDOExample();
        Integer rows = 10;
        // act
        ScrmRealtimeSceneUserRecordDOExample result = example.limit(rows);
        // assert
        assertEquals(rows, result.getRows());
    }

    /**
     * 测试limit方法，输入null
     */
    @Test
    public void testLimitNull() throws Throwable {
        // arrange
        ScrmRealtimeSceneUserRecordDOExample example = new ScrmRealtimeSceneUserRecordDOExample();
        Integer rows = null;
        // act
        ScrmRealtimeSceneUserRecordDOExample result = example.limit(rows);
        // assert
        assertNull(result.getRows());
    }

    /**
     * 测试 page 方法，正常情况
     */
    @Test
    public void testPageNormal() throws Throwable {
        // arrange
        ScrmRealtimeSceneUserRecordDOExample example = new ScrmRealtimeSceneUserRecordDOExample();
        Integer page = 1;
        Integer pageSize = 10;
        // act
        ScrmRealtimeSceneUserRecordDOExample result = example.page(page, pageSize);
        // assert
        assertEquals((Integer) (page * pageSize), result.getOffset());
        assertEquals(pageSize, result.getRows());
    }

    /**
     * 测试 page 方法，边界情况
     */
    @Test
    public void testPageBoundary() throws Throwable {
        // arrange
        ScrmRealtimeSceneUserRecordDOExample example = new ScrmRealtimeSceneUserRecordDOExample();
        Integer page = 0;
        Integer pageSize = 10;
        // act
        ScrmRealtimeSceneUserRecordDOExample result = example.page(page, pageSize);
        // assert
        assertEquals((Integer) (page * pageSize), result.getOffset());
        assertEquals(pageSize, result.getRows());
    }

    /**
     * 测试 page 方法，异常情况
     */
    @Test(expected = NullPointerException.class)
    public void testPageException() throws Throwable {
        // arrange
        ScrmRealtimeSceneUserRecordDOExample example = new ScrmRealtimeSceneUserRecordDOExample();
        Integer page = null;
        Integer pageSize = 10;
        // act
        example.page(page, pageSize);
    }

    /**
     * 测试 clear 方法
     */
    @Test
    public void testClear() {
        // arrange
        example.setOrderByClause("test");
        example.setDistinct(true);
        example.getOredCriteria().add(new ScrmRealtimeSceneUserRecordDOExample.Criteria());
        example.setOffset(1);
        example.setRows(10);
        // act
        example.clear();
        // assert
        assertTrue(example.getOredCriteria().isEmpty());
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertNull(example.getOffset());
        assertNull(example.getRows());
    }
}
