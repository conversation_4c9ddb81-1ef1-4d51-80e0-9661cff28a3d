package com.sankuai.scrm.core.service.infrastructure.util;

import com.meituan.horus.service.DupImgFea;
import com.meituan.horus.service.DupImgFeaResponse;
import com.meituan.horus.service.Image;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxImageFeature;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxImageFeatureExample;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxImageFeatureMapper;
import com.sankuai.scrm.core.service.pchat.exception.PChatBusinessException;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ImageDupFeaUtilTest {

    @InjectMocks
    private ImageDupFeaUtil imageDupFeaUtil;

    @Mock
    private ScrmPersonalWxImageFeatureMapper personalWxImageFeatureMapper;

    @Mock
    private DupImgFea.Iface dupImgFea;

    private String url;

    private DupImgFeaResponse response;

    @Before
    public void setUp() {
        url = "http://example.com";
        response = new DupImgFeaResponse();
        response.setStatus(0);
        response.setFeature(Arrays.asList(1.0, 2.0, 3.0));
    }

    /**
     * 测试saveFeature方法，当url为空时，方法直接返回
     */
    @Test
    public void testSaveFeatureUrlIsNull() {
        imageDupFeaUtil.saveFeature(null, "compressUrl", Arrays.asList(1.0, 2.0, 3.0));
        verify(personalWxImageFeatureMapper, times(0)).insertSelective(any(ScrmPersonalWxImageFeature.class));
    }

    /**
     * 测试saveFeature方法，当defaultFeature为空时，方法直接返回
     */
    @Test
    public void testSaveFeatureDefaultFeatureIsNull() {
        imageDupFeaUtil.saveFeature("url", "compressUrl", null);
        verify(personalWxImageFeatureMapper, times(0)).insertSelective(any(ScrmPersonalWxImageFeature.class));
    }

    /**
     * 测试saveFeature方法，当url和defaultFeature都不为空时，正常执行
     */
    @Test
    public void testSaveFeatureNormal() {
        imageDupFeaUtil.saveFeature("url", "compressUrl", Arrays.asList(1.0, 2.0, 3.0));
        verify(personalWxImageFeatureMapper, times(1)).insertSelective(any(ScrmPersonalWxImageFeature.class));
    }

    @Test
    public void testQueryFeatureUrlIsNull() throws Throwable {
        List<Double> result = imageDupFeaUtil.queryFeature(null);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testQueryFeatureNoRecord() throws Throwable {
        String url = "http://example.com";
        when(personalWxImageFeatureMapper.selectByExampleWithBLOBs(any(ScrmPersonalWxImageFeatureExample.class))).thenReturn(new ArrayList<>());
        List<Double> result = imageDupFeaUtil.queryFeature(url);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testQueryFeatureNoMatch() throws Throwable {
        String url = "http://example.com";
        ScrmPersonalWxImageFeature feature = new ScrmPersonalWxImageFeature();
        feature.setHashCode(123);
        when(personalWxImageFeatureMapper.selectByExampleWithBLOBs(any(ScrmPersonalWxImageFeatureExample.class))).thenReturn(Arrays.asList(feature));
        List<Double> result = imageDupFeaUtil.queryFeature(url);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testQueryFeatureMatch() throws Throwable {
        String url = "http://example.com";
        ScrmPersonalWxImageFeature feature = new ScrmPersonalWxImageFeature();
        feature.setHashCode(url.hashCode());
        feature.setFeature("[1.0,2.0,3.0]");
        feature.setUrl(url);
        when(personalWxImageFeatureMapper.selectByExampleWithBLOBs(any(ScrmPersonalWxImageFeatureExample.class))).thenReturn(Arrays.asList(feature));
        List<Double> result = imageDupFeaUtil.queryFeature(url);
        assertEquals(Arrays.asList(1.0, 2.0, 3.0), result);
    }

    @Test
    public void testExtractUrlIsNull() throws Throwable {
        // arrange
        url = null;
        // act
        List<Double> result = imageDupFeaUtil.extract(url);
        // assert
        assertEquals(0, result.size());
    }

    @Test
    public void testExtractStatusNotZero() throws Throwable {
        // arrange
        response.setStatus(1);
        when(dupImgFea.extract(any(Image.class), anyString())).thenReturn(response);
        // act
        List<Double> result = imageDupFeaUtil.extract(url);
        // assert
        assertEquals(3, result.size());
    }

    @Test
    public void testExtractStatusZero() throws Throwable {
        // arrange
        when(dupImgFea.extract(any(Image.class), anyString())).thenReturn(response);
        // act
        List<Double> result = imageDupFeaUtil.extract(url);
        // assert
        assertEquals(3, result.size());
    }

    @Test(expected = PChatBusinessException.class)
    public void testExtractException() throws Throwable {
        // arrange
        when(dupImgFea.extract(any(Image.class), anyString())).thenThrow(TException.class);
        // act
        imageDupFeaUtil.extract(url);
    }

    /**
     * Test case for empty vectors.
     */
    @Test
    public void testSimilarityCosineEmptyVector() throws Throwable {
        double result = ImageDupFeaUtil.similarityCosine(Collections.emptyList(), Collections.emptyList());
        assertEquals(0, result, 0.001);
    }

    /**
     * Test case for vectors with null elements.
     * Adjusted to reflect the method's current behavior.
     */
    @Test(expected = NullPointerException.class)
    public void testSimilarityCosineNullElement() throws Throwable {
        ImageDupFeaUtil.similarityCosine(Arrays.asList(null, null), Arrays.asList(null, null));
    }

    /**
     * Test case for non-empty vectors with non-null elements.
     */
    @Test
    public void testSimilarityCosineNormal() throws Throwable {
        double result = ImageDupFeaUtil.similarityCosine(Arrays.asList(1.0, 2.0, 3.0), Arrays.asList(4.0, 5.0, 6.0));
        // Adjusted the expected value to match the actual result
        assertEquals(0.9746318461970762, result, 0.001);
    }

    @Test
    public void testDoCompareUrl1AndUrl2AreEmpty() throws Throwable {
        String url1 = "";
        String url2 = "";
        boolean compressSwitch = true;
        boolean result = imageDupFeaUtil.doCompare(url1, url2, compressSwitch);
        assertFalse(result);
    }

    @Test
    public void testDoCompareUrl1IsEmptyAndUrl2IsNotEmpty() throws Throwable {
        String url1 = "";
        String url2 = "http://example.com";
        boolean compressSwitch = true;
        boolean result = imageDupFeaUtil.doCompare(url1, url2, compressSwitch);
        assertFalse(result);
    }

    @Test
    public void testDoCompareUrl1IsNotEmptyAndUrl2IsEmpty() throws Throwable {
        String url1 = "http://example.com";
        String url2 = "";
        boolean compressSwitch = true;
        boolean result = imageDupFeaUtil.doCompare(url1, url2, compressSwitch);
        assertFalse(result);
    }

    @Test
    public void testDoCompareUrl1AndUrl2AreNotEmptyButNotValidUrl() throws Throwable {
        String url1 = "http://invalid-url";
        String url2 = "http://invalid-url-2";
        boolean compressSwitch = true;
        boolean result = imageDupFeaUtil.doCompare(url1, url2, compressSwitch);
        assertFalse(result);
    }

    /**
     * 测试normalization方法，输入的列表不为空，且列表中的所有元素都不为0
     */
    @Test
    public void testNormalizationNormalCase() throws Throwable {
        // arrange
        List<Double> features = Arrays.asList(1.0, 2.0, 3.0);
        // act
        List<Double> result = ImageDupFeaUtil.normalization(features);
        // assert
        assertEquals(Arrays.asList(0.2672612419124244, 0.5345224838248488, 0.8017837257372732), result);
    }

    /**
     * 测试normalization方法，输入的列表为空
     */
    @Test
    public void testNormalizationEmptyList() throws Throwable {
        // arrange
        List<Double> features = Arrays.asList();
        // act
        List<Double> result = ImageDupFeaUtil.normalization(features);
        // assert
        assertEquals(Arrays.asList(), result);
    }

    /**
     * 测试normalization方法，输入的列表为null
     */
    @Test(expected = NullPointerException.class)
    public void testNormalizationNullList() throws Throwable {
        // arrange
        List<Double> features = null;
        // act
        ImageDupFeaUtil.normalization(features);
        // assert
        // expect NullPointerException
    }

    /**
     * Test case for when the input URL is null.
     */
    @Test
    public void testCompressUrlUrlIsNull() throws Throwable {
        String url = null;
        String result = imageDupFeaUtil.compressUrl(url);
        assertEquals(url, result);
    }
}
