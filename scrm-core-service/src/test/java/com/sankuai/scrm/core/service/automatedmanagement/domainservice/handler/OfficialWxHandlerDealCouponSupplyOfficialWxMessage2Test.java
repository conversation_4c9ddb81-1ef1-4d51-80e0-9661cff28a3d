package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionContentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.infrastructure.acl.UploadWxMediaAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGroupSendMessageAcl;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import com.sankuai.scrm.core.service.infrastructure.vo.WechatMediaResult;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerDealCouponSupplyOfficialWxMessage2Test {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private WeChatTokenAcl weChatTokenAcl;

    @Mock
    private UploadWxMediaAcl uploadWxMediaAcl;

    @Mock
    private WxGroupSendMessageAcl wxGroupSendMessageAcl;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private String executorId;

    private InvokeDetailKeyObject keyObject;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS;

    private StepExecuteResultDTO stepExecuteResultDTO;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = mock(ScrmProcessOrchestrationNodeMediumDTO.class);
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        executorId = "testExecutorId";
        keyObject = new InvokeDetailKeyObject("testKey", (byte) 1, (byte) 1, 1L);
        totalInvokeDetailDOS = new ArrayList<>();
        stepExecuteResultDTO = new StepExecuteResultDTO();
    }

    /**
     * Test case for empty content DTOs
     */
    @Test
    public void testDealCouponSupplyOfficialWxMessage_EmptyContentDTOs() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOs = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = createWxInvokeDetailDO();
        detailDOs.add(detailDO);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        when(processOrchestrationDTO.getNodeMediumDTO().getActionDTO(any())).thenReturn(actionDTO);
        when(processOrchestrationDTO.getNodeMediumDTO().getActionContentDTOList(actionDTO)).thenReturn(new ArrayList<>());
        // act
        officialWxHandler.dealCouponSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, detailDOs, stepExecuteResultDTO);
        // assert
        verify(wxGroupSendMessageAcl, never()).groupSendMessage(any(), anyString(), anyString());
    }

    private ScrmAmProcessOrchestrationWxInvokeDetailDO createWxInvokeDetailDO() {
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setProcessOrchestrationId(processOrchestrationDTO.getId());
        detailDO.setProcessOrchestrationVersion(processOrchestrationDTO.getValidVersion());
        detailDO.setProcessOrchestrationNodeId(1L);
        detailDO.setExecutorId(executorId);
        detailDO.setTargetId("testTargetId");
        detailDO.setTargetIdType((byte) 1);
        detailDO.setStatus((byte) 1);
        return detailDO;
    }
}
