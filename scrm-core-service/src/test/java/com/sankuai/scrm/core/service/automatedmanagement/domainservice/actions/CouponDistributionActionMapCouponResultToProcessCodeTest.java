package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum;
import java.lang.reflect.Method;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CouponDistributionActionMapCouponResultToProcessCodeTest {

    @InjectMocks
    private CouponDistributionAction couponDistributionAction;

    private Method mapCouponResultToProcessCodeMethod;

    @BeforeEach
    void setUp() throws NoSuchMethodException {
        // Get access to the private method using reflection
        mapCouponResultToProcessCodeMethod = CouponDistributionAction.class.getDeclaredMethod("mapCouponResultToProcessCode", String.class);
        mapCouponResultToProcessCodeMethod.setAccessible(true);
    }

    /**
     * Test when couponErrorCode is null should return SEND_COUPON_FAIL
     */
    @Test
    void testMapCouponResultToProcessCode_NullInput() throws Throwable {
        // arrange
        String couponErrorCode = null;
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SEND_COUPON_FAIL, result);
    }

    /**
     * Test when couponErrorCode is "ALREADY_RECEIVED" should return ALREADY_RECEIVED
     */
    @Test
    void testMapCouponResultToProcessCode_AlreadyReceived() throws Throwable {
        // arrange
        String couponErrorCode = "ALREADY_RECEIVED";
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ALREADY_RECEIVED, result);
    }

    /**
     * Test when couponErrorCode is "INVALID_PARAM" should return ILLEGAL_ARGUMENT
     */
    @Test
    void testMapCouponResultToProcessCode_InvalidParam() throws Throwable {
        // arrange
        String couponErrorCode = "INVALID_PARAM";
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT, result);
    }

    /**
     * Test when couponErrorCode is "USER_NOT_FOUND" should return TARGET_USER_NOT_FIND
     */
    @Test
    void testMapCouponResultToProcessCode_UserNotFound() throws Throwable {
        // arrange
        String couponErrorCode = "USER_NOT_FOUND";
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.TARGET_USER_NOT_FIND, result);
    }

    /**
     * Test when couponErrorCode is "MOCK_ISSUE" should return SUCCESS
     */
    @Test
    void testMapCouponResultToProcessCode_MockIssue() throws Throwable {
        // arrange
        String couponErrorCode = "MOCK_ISSUE";
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SUCCESS, result);
    }

    /**
     * Test when couponErrorCode is "ISSUE_FAILED" should return SEND_COUPON_FAIL
     */
    @Test
    void testMapCouponResultToProcessCode_IssueFailed() throws Throwable {
        // arrange
        String couponErrorCode = "ISSUE_FAILED";
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SEND_COUPON_FAIL, result);
    }

    /**
     * Test when couponErrorCode is "SYSTEM_ERROR" should return SEND_COUPON_FAIL
     */
    @Test
    void testMapCouponResultToProcessCode_SystemError() throws Throwable {
        // arrange
        String couponErrorCode = "SYSTEM_ERROR";
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SEND_COUPON_FAIL, result);
    }

    /**
     * Test when couponErrorCode is unknown should return SEND_COUPON_FAIL
     */
    @Test
    void testMapCouponResultToProcessCode_UnknownErrorCode() throws Throwable {
        // arrange
        String couponErrorCode = "UNKNOWN_ERROR";
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SEND_COUPON_FAIL, result);
    }

    /**
     * Test when couponErrorCode is empty string should return SEND_COUPON_FAIL
     */
    @Test
    void testMapCouponResultToProcessCode_EmptyString() throws Throwable {
        // arrange
        String couponErrorCode = "";
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SEND_COUPON_FAIL, result);
    }
}
