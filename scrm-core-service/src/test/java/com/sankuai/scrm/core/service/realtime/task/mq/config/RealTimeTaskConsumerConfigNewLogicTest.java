package com.sankuai.scrm.core.service.realtime.task.mq.config;

import com.meituan.mdp.boot.starter.mdpcache.core.Cache;
import com.sankuai.scrm.core.service.realtime.task.domainservice.ScrmGrowthUserInfoDomainService;
import com.sankuai.scrm.core.service.realtime.task.dto.RealTimeTaskConsumerConfigDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.*;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class RealTimeTaskConsumerConfigNewLogicTest {

    @Mock
    private ScrmGrowthUserInfoDomainService scrmGrowthUserInfoDomainService;

    @Mock
    private Cache localCache;

    @Spy
    @InjectMocks
    private RealTimeTaskConsumerConfig config;

    private RealTimeTaskConsumerConfigDTO configDTO;

    @BeforeEach
    void setUp() {
        configDTO = new RealTimeTaskConsumerConfigDTO();
        configDTO.setTestAppId("testApp");
        Map<String, Long> aiTaskAppId2SwitchPercentNumMap = new HashMap<>();
        aiTaskAppId2SwitchPercentNumMap.put("testApp", 50L);
        configDTO.setAiTaskAppId2SwitchPercentNumMap(aiTaskAppId2SwitchPercentNumMap);
        configDTO.setWhiteListSet(new HashSet<>());
        
        try {
            Field field = RealTimeTaskConsumerConfig.class.getDeclaredField("configDTO");
            field.setAccessible(true);
            field.set(config, configDTO);
            
            Field cacheField = RealTimeTaskConsumerConfig.class.getDeclaredField("localCache");
            cacheField.setAccessible(true);
            cacheField.set(config, localCache);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    // isInWhitelist(Long userId, List<String> appIds) 测试用例

    /**
     * 测试用户在全局白名单的情况
     */
    @Test
    void testIsInWhitelistWithUserInGlobalWhitelist() {
        // arrange
        Long userId = 123L;
        configDTO.getWhiteListSet().add(userId);
        List<String> appIds = Arrays.asList("app1", "app2");

        // act
        boolean result = config.isInWhitelist(userId, appIds);

        // assert
        assertTrue(result);
        // 验证不会调用单个appId的检查方法
        verify(config, never()).isInWhitelist(anyLong(), anyString());
    }

    /**
     * 测试用户不在白名单且appIds列表为空的情况
     */
    @Test
    void testIsInWhitelistWithEmptyAppIds() {
        // arrange
        Long userId = 123L;
        List<String> appIds = Collections.emptyList();

        // act
        boolean result = config.isInWhitelist(userId, appIds);

        // assert
        assertFalse(result);
        verify(config, never()).isInWhitelist(anyLong(), anyString());
    }

    /**
     * 测试匹配第一个appId的情况
     */
    @Test
    void testIsInWhitelistMatchFirstAppId() {
        // arrange
        Long userId = 123L;
        List<String> appIds = Arrays.asList("app1", "app2", "app3");
        
        doReturn(true).when(config).isInWhitelist(userId, "app1");

        // act
        boolean result = config.isInWhitelist(userId, appIds);

        // assert
        assertTrue(result);
        verify(config, times(1)).isInWhitelist(userId, "app1");
        verify(config, never()).isInWhitelist(userId, "app2");
        verify(config, never()).isInWhitelist(userId, "app3");
    }

    /**
     * 测试匹配中间appId的情况
     */
    @Test
    void testIsInWhitelistMatchMiddleAppId() {
        // arrange
        Long userId = 123L;
        List<String> appIds = Arrays.asList("app1", "app2", "app3");
        
        doReturn(false).when(config).isInWhitelist(userId, "app1");
        doReturn(true).when(config).isInWhitelist(userId, "app2");

        // act
        boolean result = config.isInWhitelist(userId, appIds);

        // assert
        assertTrue(result);
        verify(config, times(1)).isInWhitelist(userId, "app1");
        verify(config, times(1)).isInWhitelist(userId, "app2");
        verify(config, never()).isInWhitelist(userId, "app3");
    }

    /**
     * 测试所有appId都不匹配的情况
     */
    @Test
    void testIsInWhitelistNoMatch() {
        // arrange
        Long userId = 123L;
        List<String> appIds = Arrays.asList("app1", "app2");
        
        doReturn(false).when(config).isInWhitelist(userId, "app1");
        doReturn(false).when(config).isInWhitelist(userId, "app2");

        // act
        boolean result = config.isInWhitelist(userId, appIds);

        // assert
        assertFalse(result);
        verify(config, times(1)).isInWhitelist(userId, "app1");
        verify(config, times(1)).isInWhitelist(userId, "app2");
    }

    // percentSwitch(Long userId, Long percentNum) 测试用例

    /**
     * 测试percentNum为null的情况
     */
    @Test
    void testPercentSwitchWithNullPercentNum() {
        // arrange
        Long userId = 123L;
        Long percentNum = null;

        // act
        boolean result = config.percentSwitch(userId, percentNum);

        // assert
        assertFalse(result);
    }

    /**
     * 测试percentNum为0的情况
     */
    @Test
    void testPercentSwitchWithZeroPercentNum() {
        // arrange
        Long userId = 123L;
        Long percentNum = 0L;

        // act
        boolean result = config.percentSwitch(userId, percentNum);

        // assert
        assertFalse(result);
    }

    /**
     * 测试percentNum为负数的情况
     */
    @Test
    void testPercentSwitchWithNegativePercentNum() {
        // arrange
        Long userId = 123L;
        Long percentNum = -10L;

        // act
        boolean result = config.percentSwitch(userId, percentNum);

        // assert
        assertFalse(result);
    }

    /**
     * 测试命中百分比的情况
     */
    @Test
    void testPercentSwitchWithinPercentage() {
        // arrange
        Long userId = 50L; // userId % 100 = 50
        Long percentNum = 60L; // 50 < 60，应该命中

        // act
        boolean result = config.percentSwitch(userId, percentNum);

        // assert
        assertTrue(result);
    }

    /**
     * 测试未命中百分比的情况
     */
    @Test
    void testPercentSwitchOutsidePercentage() {
        // arrange
        Long userId = 60L; // userId % 100 = 60
        Long percentNum = 60L; // 60 >= 60，不应命中

        // act
        boolean result = config.percentSwitch(userId, percentNum);

        // assert
        assertFalse(result);
    }

    /**
     * 测试百分比边界值的情况
     */
    @Test
    void testPercentSwitchBoundaryValue() {
        // arrange
        Long userId = 59L; // userId % 100 = 59
        Long percentNum = 60L; // 59 < 60，应该命中

        // act
        boolean result = config.percentSwitch(userId, percentNum);

        // assert
        assertTrue(result);
    }

    /**
     * 测试percentNum为100的情况（应该对所有userId返回true）
     */
    @Test
    void testPercentSwitchWithFullPercentage() {
        // arrange
        Long percentNum = 100L;
        List<Long> userIds = Arrays.asList(0L, 50L, 99L, 100L, 150L, 199L);

        // act & assert
        for (Long userId : userIds) {
            assertTrue(config.percentSwitch(userId, percentNum),
                    "Should return true for userId " + userId + " when percentNum is 100");
        }
    }

    /**
     * 测试userId为null的情况
     */
    @Test
    void testPercentSwitchWithNullUserId() {
        // arrange
        Long percentNum = 50L;

        // act & assert
        assertThrows(NullPointerException.class, () -> config.percentSwitch(null, percentNum));
    }

    // percentSwitch(Long userId, String appId) 测试用例

    /**
     * 测试appId为空字符串的情况
     */
    @Test
    void testPercentSwitchWithEmptyAppId() {
        // arrange
        Long userId = 123L;

        // act
        boolean result = config.percentSwitch(userId, "");

        // assert
        assertFalse(result);
    }

    /**
     * 测试appId为空白字符串的情况
     */
    @Test
    void testPercentSwitchWithBlankAppId() {
        // arrange
        Long userId = 123L;

        // act
        boolean result = config.percentSwitch(userId, "   ");

        // assert
        assertFalse(result);
    }

    /**
     * 测试非测试appId的情况
     */
    @Test
    void testPercentSwitchWithNonTestAppId() {
        // arrange
        Long userId = 123L;
        String appId = "otherApp";

        // act
        boolean result = config.percentSwitch(userId, appId);

        // assert
        assertFalse(result);
    }

    /**
     * 测试是测试appId但无对应百分比配置的情况
     */
    @Test
    void testPercentSwitchWithTestAppIdNoPercentConfig() {
        // arrange
        Long userId = 123L;
        configDTO.setTestAppId("testApp2");
        configDTO.setAiTaskAppId2SwitchPercentNumMap(new HashMap<>());

        // act
        boolean result = config.percentSwitch(userId, "testApp2");

        // assert
        assertFalse(result);
    }

    /**
     * 测试是测试appId但未命中百分比的情况
     */
    @Test
    void testPercentSwitchWithTestAppIdMissPercent() {
        // arrange
        Long userId = 75L; // userId % 100 = 75
        String appId = "testApp"; // 配置的百分比是50，不应命中

        // act
        boolean result = config.percentSwitch(userId, appId);

        // assert
        assertFalse(result);
    }
} 