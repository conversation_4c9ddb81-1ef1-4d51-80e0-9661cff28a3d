package com.sankuai.scrm.core.service.infrastructure.acl.track.request;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class UserTrackRequestTest {

    /**
     * 测试正常情况下的转换
     */
    @Test
    public void testConvertNormalCase() {
        // arrange
        int platform = 1;
        Long userId = 12345L;
        Long startTimestamp = 1000L;
        Long endTimestamp = 2000L;
        // act
        UserTrackRequest result = UserTrackRequest.convert(platform, userId, startTimestamp, endTimestamp);
        // assert
        assertNotNull(result);
        assertEquals(platform, result.getPlatform());
        assertEquals(userId, result.getUserId());
        assertEquals(startTimestamp, result.getStartTimestamp());
        assertEquals(endTimestamp, result.getEndTimestamp());
        // 虽然不需要mock，但展示Mockito的verify用法
        verifyNoInteractions(mock(UserTrackRequest.class));
    }

    /**
     * 测试部分参数为null的情况
     */
    @Test
    public void testConvertWithNullValues() {
        // arrange
        int platform = 2;
        Long userId = null;
        Long startTimestamp = null;
        Long endTimestamp = 3000L;
        // act
        UserTrackRequest result = UserTrackRequest.convert(platform, userId, startTimestamp, endTimestamp);
        // assert
        assertNotNull(result);
        assertEquals(platform, result.getPlatform());
        assertNull(result.getUserId());
        assertNull(result.getStartTimestamp());
        assertEquals(endTimestamp, result.getEndTimestamp());
    }

    /**
     * 测试所有参数为null的情况（除了platform是基本类型不能为null）
     */
    @Test
    public void testConvertWithAllNullValues() {
        // arrange
        int platform = 3;
        Long userId = null;
        Long startTimestamp = null;
        Long endTimestamp = null;
        // act
        UserTrackRequest result = UserTrackRequest.convert(platform, userId, startTimestamp, endTimestamp);
        // assert
        assertNotNull(result);
        assertEquals(platform, result.getPlatform());
        assertNull(result.getUserId());
        assertNull(result.getStartTimestamp());
        assertNull(result.getEndTimestamp());
    }

    /**
     * 测试边界值情况（最大long值）
     */
    @Test
    public void testConvertWithMaxLongValues() {
        // arrange
        int platform = 4;
        Long userId = Long.MAX_VALUE;
        Long startTimestamp = Long.MAX_VALUE;
        Long endTimestamp = Long.MAX_VALUE;
        // act
        UserTrackRequest result = UserTrackRequest.convert(platform, userId, startTimestamp, endTimestamp);
        // assert
        assertNotNull(result);
        assertEquals(platform, result.getPlatform());
        assertEquals(Long.MAX_VALUE, result.getUserId());
        assertEquals(Long.MAX_VALUE, result.getStartTimestamp());
        assertEquals(Long.MAX_VALUE, result.getEndTimestamp());
    }

    /**
     * 测试边界值情况（最小long值）
     */
    @Test
    public void testConvertWithMinLongValues() {
        // arrange
        int platform = 5;
        Long userId = Long.MIN_VALUE;
        Long startTimestamp = Long.MIN_VALUE;
        Long endTimestamp = Long.MIN_VALUE;
        // act
        UserTrackRequest result = UserTrackRequest.convert(platform, userId, startTimestamp, endTimestamp);
        // assert
        assertNotNull(result);
        assertEquals(platform, result.getPlatform());
        assertEquals(Long.MIN_VALUE, result.getUserId());
        assertEquals(Long.MIN_VALUE, result.getStartTimestamp());
        assertEquals(Long.MIN_VALUE, result.getEndTimestamp());
    }

    /**
     * 测试平台值为0的情况
     */
    @Test
    public void testConvertWithZeroPlatform() {
        // arrange
        int platform = 0;
        Long userId = 123L;
        Long startTimestamp = 100L;
        Long endTimestamp = 200L;
        // act
        UserTrackRequest result = UserTrackRequest.convert(platform, userId, startTimestamp, endTimestamp);
        // assert
        assertNotNull(result);
        assertEquals(platform, result.getPlatform());
        assertEquals(userId, result.getUserId());
        assertEquals(startTimestamp, result.getStartTimestamp());
        assertEquals(endTimestamp, result.getEndTimestamp());
    }
}
