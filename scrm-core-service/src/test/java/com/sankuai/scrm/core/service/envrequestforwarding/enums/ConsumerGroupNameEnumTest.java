package com.sankuai.scrm.core.service.envrequestforwarding.enums;

import com.sankuai.dz.srcm.envrequestforwarding.enums.ConsumerGroupNameEnum;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class ConsumerGroupNameEnumTest {

    /**
     * 测试 getByConsumerGroupName 方法，参数与枚举值完全匹配的情况
     */
    @Test
    public void testGetByConsumerGroupName_Match() {
        // arrange
        String consumerGroupName = "deepsea.msg.result.notice.consumer";
        // act
        ConsumerGroupNameEnum result = ConsumerGroupNameEnum.getByConsumerGroupName(consumerGroupName);
        // assert
        assertEquals(ConsumerGroupNameEnum.Friend_Broadcast_Result_Notifier, result);
    }

    /**
     * 测试 getByConsumerGroupName 方法，参数与所有枚举值都不匹配的情况
     */
    @Test
    public void testGetByConsumerGroupName_NoMatch() {
        // arrange
        String consumerGroupName = "unknown";
        // act
        ConsumerGroupNameEnum result = ConsumerGroupNameEnum.getByConsumerGroupName(consumerGroupName);
        // assert
        assertEquals(ConsumerGroupNameEnum.UNKNOWN, result);
    }

    /**
     * 测试 getByConsumerGroupName 方法，参数为 null 的情况
     */
    @Test
    public void testGetByConsumerGroupName_Null() {
        // arrange
        String consumerGroupName = null;
        // act
        ConsumerGroupNameEnum result = ConsumerGroupNameEnum.getByConsumerGroupName(consumerGroupName);
        // assert
        assertEquals(ConsumerGroupNameEnum.UNKNOWN, result);
    }
}
