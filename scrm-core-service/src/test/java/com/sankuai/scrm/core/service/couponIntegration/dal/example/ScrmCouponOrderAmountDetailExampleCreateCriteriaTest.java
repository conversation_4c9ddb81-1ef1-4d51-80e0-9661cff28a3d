package com.sankuai.scrm.core.service.couponIntegration.dal.example;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmCouponOrderAmountDetailExampleCreateCriteriaTest {

    private ScrmCouponOrderAmountDetailExample scrmCouponOrderAmountDetailExample;

    @Before
    public void setUp() {
        scrmCouponOrderAmountDetailExample = new ScrmCouponOrderAmountDetailExample();
    }

    /**
     * 测试oredCriteria列表为空的情况
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsEmpty() {
        // arrange
        ScrmCouponOrderAmountDetailExample.Criteria criteria = scrmCouponOrderAmountDetailExample.createCriteria();
        // act
        // 无需执行任何操作
        // assert
        assertNotNull(criteria);
        assertEquals(1, scrmCouponOrderAmountDetailExample.getOredCriteria().size());
    }

    /**
     * 测试oredCriteria列表不为空的情况
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsNotEmpty() {
        // arrange
        ScrmCouponOrderAmountDetailExample.Criteria criteria1 = scrmCouponOrderAmountDetailExample.createCriteria();
        ScrmCouponOrderAmountDetailExample.Criteria criteria2 = scrmCouponOrderAmountDetailExample.createCriteria();
        // act
        // 无需执行任何操作
        // assert
        assertNotNull(criteria1);
        assertNotNull(criteria2);
        assertEquals(1, scrmCouponOrderAmountDetailExample.getOredCriteria().size());
    }

    /**
     * 测试or方法，应该返回一个新的Criteria对象，并将其添加到oredCriteria列表中
     */
    @Test
    public void testOr() {
        // arrange
        int originalSize = scrmCouponOrderAmountDetailExample.getOredCriteria().size();
        // act
        ScrmCouponOrderAmountDetailExample.Criteria criteria = scrmCouponOrderAmountDetailExample.or();
        // assert
        assertNotNull(criteria);
        assertEquals(originalSize + 1, scrmCouponOrderAmountDetailExample.getOredCriteria().size());
        assertTrue(scrmCouponOrderAmountDetailExample.getOredCriteria().contains(criteria));
    }
}
