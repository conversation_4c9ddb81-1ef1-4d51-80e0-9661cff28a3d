package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationNodeTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationWxTouchTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxMomentSendRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.SenderListInfoVO;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.VisibleRangeVO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ConcurrentHashMap;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class StaffMomentTouchActionBuildAndStoreInvokeLogListTest {

    @InjectMocks
    private StaffMomentTouchAction staffMomentTouchAction;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ExecuteManagementDTO mediumManagementDTO;

    private ScrmProcessOrchestrationNodeDTO currentProcessingNode;

    private String wxInvokeLogDOMapKey;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        mediumManagementDTO = new ExecuteManagementDTO();
        mediumManagementDTO.setExistedWxInvokeLogDOMap(new ConcurrentHashMap<>());
        currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setNodeId(1L);
        wxInvokeLogDOMapKey = "testKey";
    }

    @Test
    public void testBuildAndStoreInvokeLogList_WhenRequestIsNull() throws Throwable {
        // arrange
        WxMomentSendRequest wxMomentSendRequest = null;
        // act
        staffMomentTouchAction.buildAndStoreInvokeLogList(processOrchestrationDTO, mediumManagementDTO, currentProcessingNode, wxInvokeLogDOMapKey, wxMomentSendRequest);
        // assert
        verify(wxInvokeLogDOMapper, never()).insert(any());
        assertTrue(mediumManagementDTO.getExistedWxInvokeLogDOMap().containsKey(wxInvokeLogDOMapKey));
        assertTrue(mediumManagementDTO.getExistedWxInvokeLogDOMap().get(wxInvokeLogDOMapKey).isEmpty());
    }

    @Test
    public void testBuildAndStoreInvokeLogList_WhenVisibleRangeIsNull() throws Throwable {
        // arrange
        WxMomentSendRequest wxMomentSendRequest = new WxMomentSendRequest();
        // act
        staffMomentTouchAction.buildAndStoreInvokeLogList(processOrchestrationDTO, mediumManagementDTO, currentProcessingNode, wxInvokeLogDOMapKey, wxMomentSendRequest);
        // assert
        verify(wxInvokeLogDOMapper, never()).insert(any());
        assertTrue(mediumManagementDTO.getExistedWxInvokeLogDOMap().containsKey(wxInvokeLogDOMapKey));
        assertTrue(mediumManagementDTO.getExistedWxInvokeLogDOMap().get(wxInvokeLogDOMapKey).isEmpty());
    }

    @Test
    public void testBuildAndStoreInvokeLogList_WhenSenderListIsNull() throws Throwable {
        // arrange
        WxMomentSendRequest wxMomentSendRequest = new WxMomentSendRequest();
        VisibleRangeVO visibleRangeVO = new VisibleRangeVO();
        wxMomentSendRequest.setVisible_range(visibleRangeVO);
        // act
        staffMomentTouchAction.buildAndStoreInvokeLogList(processOrchestrationDTO, mediumManagementDTO, currentProcessingNode, wxInvokeLogDOMapKey, wxMomentSendRequest);
        // assert
        verify(wxInvokeLogDOMapper, never()).insert(any());
        assertTrue(mediumManagementDTO.getExistedWxInvokeLogDOMap().containsKey(wxInvokeLogDOMapKey));
        assertTrue(mediumManagementDTO.getExistedWxInvokeLogDOMap().get(wxInvokeLogDOMapKey).isEmpty());
    }

    @Test
    public void testBuildAndStoreInvokeLogList_WhenUserListIsEmpty() throws Throwable {
        // arrange
        WxMomentSendRequest wxMomentSendRequest = new WxMomentSendRequest();
        VisibleRangeVO visibleRangeVO = new VisibleRangeVO();
        SenderListInfoVO senderListInfoVO = new SenderListInfoVO();
        senderListInfoVO.setUser_list(new ArrayList<>());
        visibleRangeVO.setSender_list(senderListInfoVO);
        wxMomentSendRequest.setVisible_range(visibleRangeVO);
        // act
        staffMomentTouchAction.buildAndStoreInvokeLogList(processOrchestrationDTO, mediumManagementDTO, currentProcessingNode, wxInvokeLogDOMapKey, wxMomentSendRequest);
        // assert
        verify(wxInvokeLogDOMapper, never()).insert(any());
        assertTrue(mediumManagementDTO.getExistedWxInvokeLogDOMap().containsKey(wxInvokeLogDOMapKey));
        assertTrue(mediumManagementDTO.getExistedWxInvokeLogDOMap().get(wxInvokeLogDOMapKey).isEmpty());
    }

    @Test
    public void testBuildAndStoreInvokeLogList_WithMultipleUsers() throws Throwable {
        // arrange
        WxMomentSendRequest wxMomentSendRequest = new WxMomentSendRequest();
        VisibleRangeVO visibleRangeVO = new VisibleRangeVO();
        SenderListInfoVO senderListInfoVO = new SenderListInfoVO();
        senderListInfoVO.setUser_list(Arrays.asList("user1", "user2", "user1"));
        visibleRangeVO.setSender_list(senderListInfoVO);
        wxMomentSendRequest.setVisible_range(visibleRangeVO);
        // act
        staffMomentTouchAction.buildAndStoreInvokeLogList(processOrchestrationDTO, mediumManagementDTO, currentProcessingNode, wxInvokeLogDOMapKey, wxMomentSendRequest);
        // assert
        verify(wxInvokeLogDOMapper, times(2)).insert(any());
        assertTrue(mediumManagementDTO.getExistedWxInvokeLogDOMap().containsKey(wxInvokeLogDOMapKey));
        assertEquals(2, mediumManagementDTO.getExistedWxInvokeLogDOMap().get(wxInvokeLogDOMapKey).size());
    }

    @Test
    public void testBuildAndStoreInvokeLogList_VerifyInvokeLogProperties() throws Throwable {
        // arrange
        WxMomentSendRequest wxMomentSendRequest = new WxMomentSendRequest();
        VisibleRangeVO visibleRangeVO = new VisibleRangeVO();
        SenderListInfoVO senderListInfoVO = new SenderListInfoVO();
        senderListInfoVO.setUser_list(Arrays.asList("testUser"));
        visibleRangeVO.setSender_list(senderListInfoVO);
        wxMomentSendRequest.setVisible_range(visibleRangeVO);
        // act
        staffMomentTouchAction.buildAndStoreInvokeLogList(processOrchestrationDTO, mediumManagementDTO, currentProcessingNode, wxInvokeLogDOMapKey, wxMomentSendRequest);
        // assert
        verify(wxInvokeLogDOMapper).insert(argThat(invokeLog -> {
            return invokeLog.getExecutorId().equals("testUser") && invokeLog.getProcessOrchestrationId().equals(1L) && invokeLog.getProcessOrchestrationVersion().equals("1.0") && invokeLog.getProcessOrchestrationNodeId().equals(1L) && invokeLog.getStatus().equals(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_CREATE.getValue().byteValue()) && invokeLog.getType().equals(ScrmProcessOrchestrationWxTouchTypeEnum.MOMENT.getCode()) && invokeLog.getJobid().equals(StringUtils.EMPTY);
        }));
    }

    /**
     * Test case for getStaffMomentTouchCorpTagList when crowdPackUpdateStrategyInfoDTO is null
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_NullCrowdPackUpdateStrategyInfo() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        processOrchestrationDTO.setCrowdPackType(1);
        // Create a root node
        ScrmProcessOrchestrationNodeDTO rootNode = new ScrmProcessOrchestrationNodeDTO();
        rootNode.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ROOT.getValue());
        nodeMediumDTO.addProcessOrchestrationNodeDTO(rootNode);
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for getStaffMomentTouchCorpTagList when strategyDetailDTOList is empty
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_EmptyStrategyDetailList() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        processOrchestrationDTO.setCrowdPackType(1);
        // Create a root node
        ScrmProcessOrchestrationNodeDTO rootNode = new ScrmProcessOrchestrationNodeDTO();
        rootNode.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ROOT.getValue());
        nodeMediumDTO.addProcessOrchestrationNodeDTO(rootNode);
        // Set empty strategy info
        ScrmCrowdPackUpdateStrategyInfoDTO strategyInfoDTO = new ScrmCrowdPackUpdateStrategyInfoDTO();
        strategyInfoDTO.setCrowdPackUpdateStrategy(new ArrayList<>());
        processOrchestrationDTO.setCrowdPackUpdateStrategyInfoDTO(strategyInfoDTO);
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for getStaffMomentTouchCorpTagList when rootNodeCorpTagSet is empty
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_EmptyRootNodeCorpTagSet() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        processOrchestrationDTO.setCrowdPackType(1);
        // Create a root node
        ScrmProcessOrchestrationNodeDTO rootNode = new ScrmProcessOrchestrationNodeDTO();
        rootNode.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ROOT.getValue());
        nodeMediumDTO.addProcessOrchestrationNodeDTO(rootNode);
        // Set strategy info with empty corp tag set
        ScrmCrowdPackUpdateStrategyInfoDTO strategyInfoDTO = new ScrmCrowdPackUpdateStrategyInfoDTO();
        strategyInfoDTO.setCrowdPackUpdateStrategy(new ArrayList<>());
        processOrchestrationDTO.setCrowdPackUpdateStrategyInfoDTO(strategyInfoDTO);
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
