package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteWriteDomainService;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmRefinementOperationBackEndServiceImplTest {

    @InjectMocks
    private ScrmRefinementOperationBackEndServiceImpl scrmRefinementOperationBackEndService;

    @Mock(lenient = true)
    private ExecuteWriteDomainService executeWriteDomainService;

    /**
     * 测试 autoNeedRetryProcessOrchestration 方法，当 runNeedRetryProcessOrchestrationTask 方法执行成功时
     */
    @Test
    public void testAutoNeedRetryProcessOrchestrationSuccess() throws Throwable {
        // arrange
        doNothing().when(executeWriteDomainService).runNeedRetryProcessOrchestrationTask();
        // act
        scrmRefinementOperationBackEndService.autoNeedRetryProcessOrchestration();
        // assert
        verify(executeWriteDomainService, times(1)).runNeedRetryProcessOrchestrationTask();
    }

    /**
     * 测试 autoNeedRetryProcessOrchestration 方法，当 runNeedRetryProcessOrchestrationTask 方法执行过程中抛出异常时
     */
    @Test
    public void testAutoNeedRetryProcessOrchestrationException() throws Throwable {
        // arrange
        doThrow(new RuntimeException()).when(executeWriteDomainService).runNeedRetryProcessOrchestrationTask();
        // act
        scrmRefinementOperationBackEndService.autoNeedRetryProcessOrchestration();
        // assert
        verify(executeWriteDomainService, times(1)).runNeedRetryProcessOrchestrationTask();
    }
}
