package com.sankuai.scrm.core.service.realtime.task.domainservice;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ExternalContactBaseInfo;
import com.sankuai.scrm.core.service.external.contact.dal.mapper.ExternalContactBaseInfoMapper;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmUserGrowthIDMappingDO;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmUserGrowthIDMappingDOMapper;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineABTestRecordMessageProducer;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineDispatchProducer;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import com.sankuai.scrm.core.service.external.contact.dal.example.ExternalContactBaseInfoExample;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineABTestRecordMessageDTO;
import java.util.Arrays;
import java.util.Date;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.ArgumentCaptor;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
class ScrmGrowthUserInfoDomainServiceTest {

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private ScrmUserGrowthIDMappingDOMapper growthIDMappingDOMapper;

    @Mock
    private ExternalContactBaseInfoMapper externalContactBaseInfoMapper;

    @Mock
    private ScrmUserGrowthIDMappingDOMapper idMappingDOMapper;

    @Mock
    private RedisStoreClient redisClient;

    @Mock
    private GroupRetailAiEngineDispatchProducer groupRetailAiEngineDispatchProducer;

    @Mock
    private GroupRetailAiEngineABTestRecordMessageProducer abTestRecordMessageProducer;

    @InjectMocks
    private ScrmGrowthUserInfoDomainService scrmGrowthUserInfoDomainService;

    @Test
    void updateUserIdDaily_WhenMaxIdIsNull() {
        // Arrange
        when(redisClient.get(any(StoreKey.class))).thenReturn(null);
        ExternalContactBaseInfo contact = new ExternalContactBaseInfo();
        contact.setId(1L);
        contact.setMtUserId(100L);
        contact.setAppId("test-app");
        when(externalContactBaseInfoMapper.selectByExample(any())).thenReturn(
            Collections.singletonList(contact),
            new ArrayList<>()
        );

        // Act
        scrmGrowthUserInfoDomainService.updateUserIdDaily();

        // Assert
        verify(redisClient).get(any(StoreKey.class));
        verify(externalContactBaseInfoMapper, times(1)).selectByExample(any());
        /*verify(groupRetailAiEngineDispatchProducer).sendMessage(any());
        verify(redisClient).set(any(StoreKey.class), eq(1L));*/
    }

    @Test
    void updateUserIdDaily_WhenMaxIdExists() {
        // Arrange
        when(redisClient.get(any(StoreKey.class))).thenReturn(100L);
        ExternalContactBaseInfo contact1 = new ExternalContactBaseInfo();
        contact1.setId(101L);
        contact1.setMtUserId(1001L);
        contact1.setAppId("test-app-1");

        ExternalContactBaseInfo contact2 = new ExternalContactBaseInfo();
        contact2.setId(102L);
        contact2.setMtUserId(0L); // 无效的mtUserId

        List<ExternalContactBaseInfo> contacts = new ArrayList<>();
        contacts.add(contact1);
        contacts.add(contact2);

        when(externalContactBaseInfoMapper.selectByExample(any())).thenReturn(
            contacts,
            new ArrayList<>()
        );

        // Act
        scrmGrowthUserInfoDomainService.updateUserIdDaily();

        // Assert
        verify(redisClient).get(any(StoreKey.class));
        verify(externalContactBaseInfoMapper, times(1)).selectByExample(any());
    }

    @Test
    void updateDBMapInfoByMtUserId_WhenMtUserIdIsInvalid() {
        // Act
        scrmGrowthUserInfoDomainService.updateDBMapInfoByMtUserId(null, "test-app");
        scrmGrowthUserInfoDomainService.updateDBMapInfoByMtUserId(0L, "test-app");
        scrmGrowthUserInfoDomainService.updateDBMapInfoByMtUserId(-1L, "test-app");

        // Assert
        verifyNoInteractions(growthIDMappingDOMapper, mtUserCenterAclService);
    }

    @Test
    void updateDBMapInfoByMtUserId_WhenMappingExistsWithValidDpUserId() {
        // Arrange
        Long mtUserId = 100L;
        String appId = "test-app";
        ScrmUserGrowthIDMappingDO mapping = new ScrmUserGrowthIDMappingDO();
        mapping.setMtUserid(mtUserId);
        mapping.setDpUserid(200L);
        when(growthIDMappingDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(mapping));

        // Act
        scrmGrowthUserInfoDomainService.updateDBMapInfoByMtUserId(mtUserId, appId);

        // Assert
        verify(growthIDMappingDOMapper).selectByExample(any());
        verifyNoMoreInteractions(growthIDMappingDOMapper);
        verifyNoInteractions(mtUserCenterAclService);
    }

    @Test
    void updateDBMapInfoByMtUserId_WhenMappingExistsWithInvalidDpUserId() {
        // Arrange
        Long mtUserId = 100L;
        String appId = "test-app";
        Long newDpUserId = 300L;
        
        ScrmUserGrowthIDMappingDO mapping = new ScrmUserGrowthIDMappingDO();
        mapping.setMtUserid(mtUserId);
        mapping.setDpUserid(0L);
        
        when(growthIDMappingDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(mapping));
        when(mtUserCenterAclService.getDpUserIdByMtUserId(mtUserId)).thenReturn(newDpUserId);

        // Act
        scrmGrowthUserInfoDomainService.updateDBMapInfoByMtUserId(mtUserId, appId);

        // Assert
        verify(growthIDMappingDOMapper).selectByExample(any());
        verify(mtUserCenterAclService).getDpUserIdByMtUserId(mtUserId);
        verify(growthIDMappingDOMapper).updateByPrimaryKeySelective(any());
    }

    @Test
    void updateDBMapInfoByMtUserId_WhenMappingNotExists() {
        // Arrange
        Long mtUserId = 100L;
        String appId = "test-app";
        Long dpUserId = 200L;
        
        when(growthIDMappingDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(mtUserCenterAclService.getDpUserIdByMtUserId(mtUserId)).thenReturn(dpUserId);

        // Act
        scrmGrowthUserInfoDomainService.updateDBMapInfoByMtUserId(mtUserId, appId);

        // Assert
        verify(growthIDMappingDOMapper).selectByExample(any());
        verify(mtUserCenterAclService).getDpUserIdByMtUserId(mtUserId);
        verify(growthIDMappingDOMapper).insertSelective(any());
    }

    @Test
    void existMtUserOfAppId_WhenUserExists() {
        // Arrange
        Long mtUserId = 100L;
        String appId = "test-app";
        when(idMappingDOMapper.countByExample(any())).thenReturn(1L);

        // Act
        boolean result = scrmGrowthUserInfoDomainService.existMtUserOfAppId(mtUserId, appId);

        // Assert
        assertTrue(result);
        verify(idMappingDOMapper).countByExample(any());
    }

    @Test
    void existMtUserOfAppId_WhenUserNotExists() {
        // Arrange
        Long mtUserId = 100L;
        String appId = "test-app";
        when(idMappingDOMapper.countByExample(any())).thenReturn(0L);

        // Act
        boolean result = scrmGrowthUserInfoDomainService.existMtUserOfAppId(mtUserId, appId);

        // Assert
        assertFalse(result);
        verify(idMappingDOMapper).countByExample(any());
    }

    @Test
    void queryMtUserIdByDpUserId_WhenFoundInRedis() {
        // Arrange
        Long dpUserId = 100L;
        Long mtUserId = 200L;
        when(redisClient.get(any(StoreKey.class))).thenReturn(mtUserId);

        // Act
        Long result = scrmGrowthUserInfoDomainService.queryMtUserIdByDpUserId(dpUserId);

        // Assert
        assertEquals(mtUserId, result);
        verify(redisClient).get(any(StoreKey.class));
        verifyNoInteractions(idMappingDOMapper);
    }

    @Test
    void queryMtUserIdByDpUserId_WhenInvalidDpUserId() {
        // Act & Assert
        assertNull(scrmGrowthUserInfoDomainService.queryMtUserIdByDpUserId(null));
        assertNull(scrmGrowthUserInfoDomainService.queryMtUserIdByDpUserId(0L));
        assertNull(scrmGrowthUserInfoDomainService.queryMtUserIdByDpUserId(-1L));
        
        verifyNoInteractions(redisClient, idMappingDOMapper);
    }

    @Test
    void queryMtUserIdByDpUserId_WhenNotFoundInRedisButFoundInDB() {
        // Arrange
        Long dpUserId = 100L;
        Long mtUserId = 200L;
        
        when(redisClient.get(any(StoreKey.class))).thenReturn(null);
        
        ScrmUserGrowthIDMappingDO mapping = new ScrmUserGrowthIDMappingDO();
        mapping.setMtUserid(mtUserId);
        mapping.setDpUserid(dpUserId);
        
        when(idMappingDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(mapping));

        // Act
        Long result = scrmGrowthUserInfoDomainService.queryMtUserIdByDpUserId(dpUserId);

        // Assert
        assertEquals(mtUserId, result);
        verify(redisClient).get(any(StoreKey.class));
        verify(idMappingDOMapper).selectByExample(any());
        verify(redisClient).set(any(StoreKey.class), eq(mtUserId));
    }

    @Test
    void queryMtUserIdByDpUserId_WhenNotFoundInRedisAndDB() {
        // Arrange
        Long dpUserId = 100L;
        when(redisClient.get(any(StoreKey.class))).thenReturn(null);
        when(idMappingDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());

        // Act
        Long result = scrmGrowthUserInfoDomainService.queryMtUserIdByDpUserId(dpUserId);

        // Assert
        assertNull(result);
        verify(redisClient).get(any(StoreKey.class));
        verify(idMappingDOMapper).selectByExample(any());
        verifyNoMoreInteractions(redisClient);
    }

    @Test
    void updateRedisUserIdMap() {
        // Arrange
        Long mtUserId = 100L;
        Long dpUserId = 200L;
        String platform = "dp";

        // Act
        scrmGrowthUserInfoDomainService.updateRedisUserIdMap(mtUserId, dpUserId, platform);

        // Assert
        verify(redisClient).set(any(StoreKey.class), eq(mtUserId));
    }

    @BeforeEach
    void setUp() {
        reset(redisClient, externalContactBaseInfoMapper, abTestRecordMessageProducer);
    }

    @Test
    void testUpdateUserIdDaily_WhenMaxIdIsNull() throws Throwable {
        // Arrange
        when(redisClient.get(any(StoreKey.class))).thenReturn(null);
        ExternalContactBaseInfo contact = new ExternalContactBaseInfo();
        contact.setId(1L);
        contact.setMtUserId(100L);
        contact.setAppId("test-app");
        when(externalContactBaseInfoMapper.selectByExample(any(ExternalContactBaseInfoExample.class))).thenReturn(Collections.singletonList(contact)).thenReturn(new ArrayList<>());
        // Act
        scrmGrowthUserInfoDomainService.updateUserIdDaily();
        // Assert
        verify(redisClient).get(any(StoreKey.class));
        verify(externalContactBaseInfoMapper, atLeastOnce()).selectByExample(any(ExternalContactBaseInfoExample.class));
        verify(abTestRecordMessageProducer).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        ArgumentCaptor<StoreKey> storeKeyCaptor = ArgumentCaptor.forClass(StoreKey.class);
        ArgumentCaptor<Long> maxIdCaptor = ArgumentCaptor.forClass(Long.class);
        verify(redisClient).set(storeKeyCaptor.capture(), maxIdCaptor.capture());
        assertEquals(1L, maxIdCaptor.getValue());
    }

    @Test
    void testUpdateUserIdDaily_WhenMaxIdExists() throws Throwable {
        // Arrange
        when(redisClient.get(any(StoreKey.class))).thenReturn(100L);
        ExternalContactBaseInfo contact = new ExternalContactBaseInfo();
        contact.setId(101L);
        contact.setMtUserId(200L);
        contact.setAppId("test-app");
        when(externalContactBaseInfoMapper.selectByExample(any(ExternalContactBaseInfoExample.class))).thenReturn(Collections.singletonList(contact)).thenReturn(new ArrayList<>());
        // Act
        scrmGrowthUserInfoDomainService.updateUserIdDaily();
        // Assert
        verify(redisClient).get(any(StoreKey.class));
        verify(externalContactBaseInfoMapper).selectByExample(argThat(example -> example.getOredCriteria().get(0).getCriteria().get(0).getValue().equals(100L)));
        verify(abTestRecordMessageProducer).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        ArgumentCaptor<StoreKey> storeKeyCaptor = ArgumentCaptor.forClass(StoreKey.class);
        ArgumentCaptor<Long> maxIdCaptor = ArgumentCaptor.forClass(Long.class);
        verify(redisClient).set(storeKeyCaptor.capture(), maxIdCaptor.capture());
        assertEquals(101L, maxIdCaptor.getValue());
    }

    @Test
    void testUpdateUserIdDaily_WhenNoDataFound() throws Throwable {
        // Arrange
        when(redisClient.get(any(StoreKey.class))).thenReturn(null);
        when(externalContactBaseInfoMapper.selectByExample(any(ExternalContactBaseInfoExample.class))).thenReturn(new ArrayList<>());
        // Act
        scrmGrowthUserInfoDomainService.updateUserIdDaily();
        // Assert
        verify(redisClient).get(any(StoreKey.class));
        verify(externalContactBaseInfoMapper).selectByExample(any(ExternalContactBaseInfoExample.class));
        verify(abTestRecordMessageProducer, never()).sendMessage(any());
        ArgumentCaptor<StoreKey> storeKeyCaptor = ArgumentCaptor.forClass(StoreKey.class);
        ArgumentCaptor<Long> maxIdCaptor = ArgumentCaptor.forClass(Long.class);
        verify(redisClient).set(storeKeyCaptor.capture(), maxIdCaptor.capture());
        assertEquals(0L, maxIdCaptor.getValue());
    }

    @Test
    void testUpdateUserIdDaily_WhenMtUserIdIsNull() throws Throwable {
        // Arrange
        when(redisClient.get(any(StoreKey.class))).thenReturn(null);
        ExternalContactBaseInfo contact = new ExternalContactBaseInfo();
        contact.setId(1L);
        contact.setMtUserId(null);
        contact.setAppId("test-app");
        when(externalContactBaseInfoMapper.selectByExample(any(ExternalContactBaseInfoExample.class))).thenReturn(Collections.singletonList(contact)).thenReturn(new ArrayList<>());
        // Act
        scrmGrowthUserInfoDomainService.updateUserIdDaily();
        // Assert
        verify(abTestRecordMessageProducer, never()).sendMessage(any());
        ArgumentCaptor<StoreKey> storeKeyCaptor = ArgumentCaptor.forClass(StoreKey.class);
        ArgumentCaptor<Long> maxIdCaptor = ArgumentCaptor.forClass(Long.class);
        verify(redisClient).set(storeKeyCaptor.capture(), maxIdCaptor.capture());
        assertEquals(0L, maxIdCaptor.getValue());
    }

    @Test
    void testUpdateUserIdDaily_WhenMtUserIdIsZeroOrNegative() throws Throwable {
        // Arrange
        when(redisClient.get(any(StoreKey.class))).thenReturn(null);
        ExternalContactBaseInfo contact = new ExternalContactBaseInfo();
        contact.setId(1L);
        contact.setMtUserId(0L);
        contact.setAppId("test-app");
        when(externalContactBaseInfoMapper.selectByExample(any(ExternalContactBaseInfoExample.class))).thenReturn(Collections.singletonList(contact)).thenReturn(new ArrayList<>());
        // Act
        scrmGrowthUserInfoDomainService.updateUserIdDaily();
        // Assert
        verify(abTestRecordMessageProducer, never()).sendMessage(any());
        ArgumentCaptor<StoreKey> storeKeyCaptor = ArgumentCaptor.forClass(StoreKey.class);
        ArgumentCaptor<Long> maxIdCaptor = ArgumentCaptor.forClass(Long.class);
        verify(redisClient).set(storeKeyCaptor.capture(), maxIdCaptor.capture());
        assertEquals(0L, maxIdCaptor.getValue());
    }

    @Test
    void testUpdateUserIdDaily_WhenDataSizeEqualsLimit() throws Throwable {
        // Arrange
        when(redisClient.get(any(StoreKey.class))).thenReturn(null);
        List<ExternalContactBaseInfo> firstPage = new ArrayList<>();
        for (long i = 1; i <= 1000; i++) {
            ExternalContactBaseInfo contact = new ExternalContactBaseInfo();
            contact.setId(i);
            contact.setMtUserId(i + 100);
            contact.setAppId("test-app");
            firstPage.add(contact);
        }
        when(externalContactBaseInfoMapper.selectByExample(any(ExternalContactBaseInfoExample.class))).thenReturn(firstPage).thenReturn(new ArrayList<>());
        // Act
        scrmGrowthUserInfoDomainService.updateUserIdDaily();
        // Assert
        verify(externalContactBaseInfoMapper, times(2)).selectByExample(any(ExternalContactBaseInfoExample.class));
        verify(abTestRecordMessageProducer, times(1000)).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        ArgumentCaptor<StoreKey> storeKeyCaptor = ArgumentCaptor.forClass(StoreKey.class);
        ArgumentCaptor<Long> maxIdCaptor = ArgumentCaptor.forClass(Long.class);
        verify(redisClient).set(storeKeyCaptor.capture(), maxIdCaptor.capture());
        assertEquals(1000L, maxIdCaptor.getValue());
    }

    @Test
    void testUpdateUserIdDaily_WhenDataSizeLessThanLimit() throws Throwable {
        // Arrange
        when(redisClient.get(any(StoreKey.class))).thenReturn(null);
        List<ExternalContactBaseInfo> firstPage = new ArrayList<>();
        for (long i = 1; i <= 500; i++) {
            ExternalContactBaseInfo contact = new ExternalContactBaseInfo();
            contact.setId(i);
            contact.setMtUserId(i + 100);
            contact.setAppId("test-app");
            firstPage.add(contact);
        }
        when(externalContactBaseInfoMapper.selectByExample(any(ExternalContactBaseInfoExample.class))).thenReturn(firstPage);
        // Act
        scrmGrowthUserInfoDomainService.updateUserIdDaily();
        // Assert
        verify(externalContactBaseInfoMapper, times(1)).selectByExample(any(ExternalContactBaseInfoExample.class));
        verify(abTestRecordMessageProducer, times(500)).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        ArgumentCaptor<StoreKey> storeKeyCaptor = ArgumentCaptor.forClass(StoreKey.class);
        ArgumentCaptor<Long> maxIdCaptor = ArgumentCaptor.forClass(Long.class);
        verify(redisClient).set(storeKeyCaptor.capture(), maxIdCaptor.capture());
        assertEquals(500L, maxIdCaptor.getValue());
    }

    @Test
    void testUpdateUserIdDaily_WhenRedisSetFails() throws Throwable {
        // Arrange
        when(redisClient.get(any(StoreKey.class))).thenReturn(null);
        ExternalContactBaseInfo contact = new ExternalContactBaseInfo();
        contact.setId(1L);
        contact.setMtUserId(100L);
        contact.setAppId("test-app");
        when(externalContactBaseInfoMapper.selectByExample(any(ExternalContactBaseInfoExample.class))).thenReturn(Collections.singletonList(contact)).thenReturn(new ArrayList<>());
        // Mock Redis set to throw exception
        doThrow(new RuntimeException("Redis error")).when(redisClient).set(any(StoreKey.class), anyLong());
        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            scrmGrowthUserInfoDomainService.updateUserIdDaily();
        });
        // Verify exception message
        assertEquals("Redis error", exception.getMessage());
        // Verify that operations before Redis error were executed
        verify(redisClient).get(any(StoreKey.class));
        verify(externalContactBaseInfoMapper).selectByExample(any(ExternalContactBaseInfoExample.class));
        verify(abTestRecordMessageProducer).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
    }

    @Test
    void testUpdateUserIdDaily_WhenMessageSendFails() throws Throwable {
        // Arrange
        when(redisClient.get(any(StoreKey.class))).thenReturn(null);
        ExternalContactBaseInfo contact = new ExternalContactBaseInfo();
        contact.setId(1L);
        contact.setMtUserId(100L);
        contact.setAppId("test-app");
        when(externalContactBaseInfoMapper.selectByExample(any(ExternalContactBaseInfoExample.class))).thenReturn(Collections.singletonList(contact)).thenReturn(new ArrayList<>());
        // Mock message send to throw exception
        doThrow(new RuntimeException("Message send error")).when(abTestRecordMessageProducer).sendMessage(any(GroupRetailAiEngineABTestRecordMessageDTO.class));
        // Act & Assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            scrmGrowthUserInfoDomainService.updateUserIdDaily();
        });
        // Verify exception message
        assertEquals("Message send error", exception.getMessage());
        // Verify that operations before message send error were executed
        verify(redisClient).get(any(StoreKey.class));
        verify(externalContactBaseInfoMapper).selectByExample(any(ExternalContactBaseInfoExample.class));
        // Verify that Redis set was not called because exception occurred before
        verify(redisClient, never()).set(any(StoreKey.class), anyLong());
    }
}
