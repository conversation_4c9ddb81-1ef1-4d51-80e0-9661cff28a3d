package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.GroupSendStrategy;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.infrastructure.acl.UploadWxMediaAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.AttachmentVO;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.MiniProgramVO;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import com.sankuai.scrm.core.service.infrastructure.vo.WechatMediaResult;
import com.sankuai.scrm.core.service.infrastructure.vo.WxMediaType;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.HashMap;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerDealCouponSupplyOfficialWxGroupMessageV2Test {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private MsgUnifiedPushService msgUnifiedPushService;

    @Mock
    private GroupSendStrategy groupSendStrategy;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private WeChatTokenAcl weChatTokenAcl;

    @Mock
    private UploadWxMediaAcl uploadWxMediaAcl;

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private ShortLinkUtils shortLinkUtils;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO;

    private ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO;

    private ScrmAmProcessOrchestrationProductActivityPageDO pageInfo;

    private Map<String, AttachmentVO> existedAttachmentMap;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        existedAttachmentMap = new HashMap<>();
    }



    /**
     * Test when existedAttachmentMap already contains the attachment
     */
    @Test
    void testGetCouponPageAttachmentVO_ExistingAttachment() throws Throwable {
        // arrange
        actionAttachmentDTO.setProcessOrchestrationNodeId(1L);
        AttachmentVO expectedAttachment = new AttachmentVO();
        MiniProgramVO miniProgramVO = new MiniProgramVO();
        miniProgramVO.setTitle("test");
        expectedAttachment.setMiniprogram(miniProgramVO);
        expectedAttachment.setMsgtype("miniprogram");
        existedAttachmentMap.put("1", expectedAttachment);
        // act
        AttachmentVO result = officialWxHandler.getCouponPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap);
        // assert
        assertEquals(expectedAttachment, result);
        verifyNoInteractions(weChatTokenAcl, uploadWxMediaAcl, informationGatheringService, productManagementService, shortLinkUtils);
    }

    /**
     * Test when token fetch fails
     */
    @Test
    void testGetCouponPageAttachmentVO_TokenFetchFails() throws Throwable {
        // arrange
        processOrchestrationDTO.setAppId("testAppId");
        actionAttachmentDTO.setProcessOrchestrationNodeId(1L);
        when(appConfigRepository.getCorpIdByAppId("testAppId")).thenReturn("testCorpId");
        when(weChatTokenAcl.getTokenByCorpId("testCorpId")).thenReturn(null);
        // act
        AttachmentVO result = officialWxHandler.getCouponPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap);
        // assert
        assertNull(result);
        verify(appConfigRepository).getCorpIdByAppId("testAppId");
        verify(weChatTokenAcl).getTokenByCorpId("testCorpId");
        verifyNoInteractions(uploadWxMediaAcl, informationGatheringService, productManagementService, shortLinkUtils);
    }

    /**
     * Test when media upload fails
     */
    @Test
    void testGetCouponPageAttachmentVO_MediaUploadFails() throws Throwable {
        // arrange
        processOrchestrationDTO.setAppId("testAppId");
        actionAttachmentDTO.setProcessOrchestrationNodeId(1L);
        supplyDetailDTO.setHeadpicUrl("testUrl");
        WeChatTokenResult tokenResult = WeChatTokenResult.builder().access_token("testToken").errcode(0).errmsg("ok").build();
        when(appConfigRepository.getCorpIdByAppId("testAppId")).thenReturn("testCorpId");
        when(weChatTokenAcl.getTokenByCorpId("testCorpId")).thenReturn(tokenResult);
        when(uploadWxMediaAcl.uploadWxTmpMedia(anyString(), any(), anyString(), anyBoolean())).thenReturn(null);
        // act
        AttachmentVO result = officialWxHandler.getCouponPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap);
        // assert
        assertNull(result);
        verify(appConfigRepository).getCorpIdByAppId("testAppId");
        verify(weChatTokenAcl).getTokenByCorpId("testCorpId");
        verify(uploadWxMediaAcl).uploadWxTmpMedia(eq("testUrl"), any(WxMediaType.class), eq("testToken"), eq(true));
        verifyNoInteractions(informationGatheringService, productManagementService, shortLinkUtils);
    }



    /**
     * Test when attachment already exists in existedAttachmentMap
     */
    @Test
    public void testGetActivityPageAttachmentVO_ExistingAttachment() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setProcessOrchestrationNodeId(123L);
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageInfo.setId(456L);
        java.util.Map<String, AttachmentVO> existedAttachmentMap = new java.util.HashMap<>();
        AttachmentVO existingAttachment = new AttachmentVO();
        existedAttachmentMap.put("123-456", existingAttachment);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        // act
        AttachmentVO result = officialWxHandler.getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, stepExecuteResultDTO);
        // assert
        assertSame(existingAttachment, result);
    }

    /**
     * Test when media upload fails
     */
    @Test
    public void testGetActivityPageAttachmentVO_MediaUploadFail() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageInfo.setThumbPicUrl("testUrl");
        java.util.Map<String, AttachmentVO> existedAttachmentMap = new java.util.HashMap<>();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        WeChatTokenResult tokenResult = WeChatTokenResult.builder().access_token("testToken").errcode(0).errmsg("success").build();
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("testCorpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(tokenResult);
        when(uploadWxMediaAcl.uploadWxTmpMedia(anyString(), any(), anyString(), any(Boolean.class))).thenReturn(null);
        // act
        AttachmentVO result = officialWxHandler.getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, stepExecuteResultDTO);
        // assert
        assertNull(result);
        assertFalse(stepExecuteResultDTO.isSuccess());
        assertEquals("供给类官方途径群发消息-构建活动页失败-图片上传失败", stepExecuteResultDTO.getMsg());
    }
}
