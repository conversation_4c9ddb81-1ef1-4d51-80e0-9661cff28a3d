package com.sankuai.scrm.core.service.automatedmanagement.mq.consumer;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.technician.trade.api.order.message.OrderOperateNotify;
import java.lang.reflect.Field;
import java.util.Properties;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class OrderReceiptOperateNotifyConsumer_RecvMessageTest {

    @InjectMocks
    private OrderReceiptOperateNotifyConsumer orderReceiptOperateNotifyConsumer;

    @Mock(lenient = true)
    private InformationGatheringService informationGatheringService;

    @Mock(lenient = true)
    private IConsumerProcessor consumer;

    private OrderReceiptOperateNotifyConsumer consumerUnderTest;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 message body 为空的情况
     */
    @Test
    public void testRecvMessageBodyIsEmpty() {
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "");
        MessagetContext messagetContext = new MessagetContext();
        ConsumeStatus result = orderReceiptOperateNotifyConsumer.recvMessage(message, messagetContext);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试 message body 不为空，但 dealCommunityOrder 返回 null 的情况
     */
    @Test
    public void testRecvMessageBodyIsNotEmptyButDealCommunityOrderReturnsNull() {
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "{}");
        MessagetContext messagetContext = new MessagetContext();
        when(informationGatheringService.dealCommunityOrder(any(OrderOperateNotify.class))).thenReturn(null);
        ConsumeStatus result = orderReceiptOperateNotifyConsumer.recvMessage(message, messagetContext);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试 message body 不为空，dealCommunityOrder 返回非 null 的情况
     */
    @Test
    public void testRecvMessageBodyIsNotEmptyAndDealCommunityOrderReturnsNonNull() {
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "{}");
        MessagetContext messagetContext = new MessagetContext();
        when(informationGatheringService.dealCommunityOrder(any(OrderOperateNotify.class))).thenReturn(ConsumeStatus.CONSUME_SUCCESS);
        ConsumeStatus result = orderReceiptOperateNotifyConsumer.recvMessage(message, messagetContext);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试在处理过程中发生异常的情况
     */
    @Test
    public void testRecvMessageExceptionOccurred() {
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "{}");
        MessagetContext messagetContext = new MessagetContext();
        when(informationGatheringService.dealCommunityOrder(any(OrderOperateNotify.class))).thenThrow(new RuntimeException());
        ConsumeStatus result = orderReceiptOperateNotifyConsumer.recvMessage(message, messagetContext);
        assertEquals(ConsumeStatus.RECONSUME_LATER, result);
    }
    /**
     * Tests the afterPropertiesSet method under exceptional conditions.
     */
    @Test(expected = Exception.class)
    public void testAfterPropertiesSetException() throws Throwable {
        OrderReceiptOperateNotifyConsumer consumerUnderTest = new OrderReceiptOperateNotifyConsumer();
        // Use reflection to set the private static field consumer
        Field consumerField = OrderReceiptOperateNotifyConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(null, consumer);
        // arrange
        doThrow(new Exception()).when(consumer).recvMessageWithParallel(eq(String.class), any());
        // act
        consumerUnderTest.afterPropertiesSet();
        // assert
        verify(consumer, times(1)).recvMessageWithParallel(eq(String.class), any());
    }
}
