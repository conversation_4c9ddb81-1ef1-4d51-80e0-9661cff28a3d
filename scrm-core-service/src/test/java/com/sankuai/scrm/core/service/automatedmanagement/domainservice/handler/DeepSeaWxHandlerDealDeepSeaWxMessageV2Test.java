package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationContentTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import java.util.AbstractMap;
import java.util.Arrays;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationContentSupplyTypeEnum;
import com.sankuai.scrm.core.service.aigc.service.domainservice.ScrmGroupRetailUserCouponRecordDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.BizDistributorServiceKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.group.dynamiccode.constant.GroupDynamicCodeConstants;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class DeepSeaWxHandlerDealDeepSeaWxMessageV2Test {

    @Mock
    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private DeepSeaWxHandler handler;

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmGroupRetailUserCouponRecordDomainService scrmGroupRetailUserCouponRecordDomainService;

    @Spy
    @InjectMocks
    private DeepSeaWxHandler deepSeaWxHandler;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        handler = spy(new DeepSeaWxHandler());
    }

    /**
     * Test case for when content type is NON_SUPPLY.
     * The method should return immediately without any further processing.
     */
    @Test
    public void testDealDeepSeaWxMessageV2_ContentTypeNonSupply() throws Throwable {
        // arrange
        List<String> executorIds = Arrays.asList("executor1", "executor2");
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("type", ScrmProcessOrchestrationContentTypeEnum.NON_SUPPLY.getValueByte(), (byte) 0, 1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOs = Arrays.asList(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(keyObject, detailDOs);
        // act
        handler.dealDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, entry);
        // assert
        // Verify that the method was called once
        verify(handler).dealDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, entry);
        // Verify that no other methods were called on the handler
        verify(handler, never()).dealRealTimeDeepSeaWxMessage(any(), any(), any(), any(), any());
        verify(handler, never()).dealRealTimeDeepSeaWxMessageV2(any(), any(), any(), any(), any());
    }

    /**
     * Test case for when content type is SUPPLY and content sub-type is COUPON_PROMOTION.
     * The method should call dealCouponDeepSeaWxMessage (though it's commented out in the provided code).
     */
    @Test
    public void testDealDeepSeaWxMessageV2_ContentTypeSupplyCouponPromotion() throws Throwable {
        // arrange
        List<String> executorIds = Arrays.asList("executor1", "executor2");
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("type", ScrmProcessOrchestrationContentTypeEnum.SUPPLY.getValueByte(), (byte) 1, 1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOs = Arrays.asList(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(keyObject, detailDOs);
        // act
        handler.dealDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, entry);
        // assert
        verify(handler).dealDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, entry);
        verify(handler, never()).dealRealTimeDeepSeaWxMessage(any(), any(), any(), any(), any());
        verify(handler, never()).dealRealTimeDeepSeaWxMessageV2(any(), any(), any(), any(),any() );
    }

    /**
     * Test case for when content type is SUPPLY but content sub-type is not COUPON_PROMOTION.
     * The method should return immediately without any further processing.
     */
    @Test
    public void testDealDeepSeaWxMessageV2_ContentTypeSupplyNotCouponPromotion() throws Throwable {
        // arrange
        List<String> executorIds = Arrays.asList("executor1", "executor2");
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("type", ScrmProcessOrchestrationContentTypeEnum.SUPPLY.getValueByte(), (byte) 2, 1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOs = Arrays.asList(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(keyObject, detailDOs);
        // act
        handler.dealDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, entry);
        // assert
        verify(handler).dealDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, entry);
        verify(handler, never()).dealRealTimeDeepSeaWxMessage(any(), any(), any(), any(), any());
        verify(handler, never()).dealRealTimeDeepSeaWxMessageV2(any(), any(), any(), any(), any());
    }

    /**
     * Test case for when content type is neither NON_SUPPLY nor SUPPLY.
     * The method should return immediately without any further processing.
     */
    @Test
    public void testDealDeepSeaWxMessageV2_ContentTypeOther() throws Throwable {
        // arrange
        List<String> executorIds = Arrays.asList("executor1", "executor2");
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("type", (byte) 2, (byte) 0, 1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOs = Arrays.asList(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(keyObject, detailDOs);
        // act
        handler.dealDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, entry);
        // assert
        verify(handler).dealDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, entry);
        verify(handler, never()).dealRealTimeDeepSeaWxMessage(any(), any(), any(), any(), any());
        verify(handler, never()).dealRealTimeDeepSeaWxMessageV2(any(), any(), any(), any(), any());
    }

    @Test
    public void testGetAiSceneActivPageMsgPushContentDTOEmptyActionAttachmentList() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        IntelligentFollowResultDTO aiSceneContent = new IntelligentFollowResultDTO();
        aiSceneContent.setShopId(123L);
        processOrchestrationDTO.setAiSceneContent(aiSceneContent);
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        executeManagementDTO.setPoiId("123");
        // Use concrete implementation instead of ArrayList
        List<ScrmProcessOrchestrationActionAttachmentDTO> actionAttachmentDTOList = new java.util.ArrayList<ScrmProcessOrchestrationActionAttachmentDTO>();
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> partWxInvokeDetailDOS = new java.util.ArrayList<ScrmAmProcessOrchestrationWxInvokeDetailDO>();
        // act
        MsgPushContentDTO result = deepSeaWxHandler.getAiSceneActivPageMsgPushContentDTO(processOrchestrationDTO, executeManagementDTO, actionAttachmentDTOList, partWxInvokeDetailDOS, aiSceneContent);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetAiSceneActivPageMsgPushContentDTOSuccessfulPath() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        IntelligentFollowResultDTO aiSceneContent = new IntelligentFollowResultDTO();
        aiSceneContent.setShopId(123L);
        aiSceneContent.setCouponId("testCouponId");
        processOrchestrationDTO.setAiSceneContent(aiSceneContent);
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        executeManagementDTO.setPoiId("123");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setId(1L);
        actionAttachmentDTO.setProcessOrchestrationId(1L);
        actionAttachmentDTO.setProcessOrchestrationVersion("1.0");
        actionAttachmentDTO.setProcessOrchestrationNodeId(1L);
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTitle("testTitle");
        supplyDetailDTO.setHeadpicUrl("testHeadpicUrl");
        supplyDetailDTO.setSupplyType(ScrmProcessOrchestrationContentSupplyTypeEnum.COUPON_PROMOTION.getValue());
        actionAttachmentDTO.setAttachmentSupplyDetailDTO(supplyDetailDTO);
        // Use concrete implementation instead of Arrays.asList
        List<ScrmProcessOrchestrationActionAttachmentDTO> actionAttachmentDTOList = new java.util.ArrayList<ScrmProcessOrchestrationActionAttachmentDTO>();
        actionAttachmentDTOList.add(actionAttachmentDTO);
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO.setExecuteLogId(1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> partWxInvokeDetailDOS = new java.util.ArrayList<ScrmAmProcessOrchestrationWxInvokeDetailDO>();
        partWxInvokeDetailDOS.add(invokeDetailDO);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setTargetMtUserId(456L);
        executeLogDO.setAppId("testAppId");
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneCode("testSceneCode");
        sceneCodeDO.setPoiRestrict(0);
        when(executeLogDOMapper.selectByPrimaryKey(1L)).thenReturn(executeLogDO);
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class))).thenReturn("testDistributorCode");
        when(productManagementService.getActivSceneCodeDO(any(), any(), any(), any(), any())).thenReturn(sceneCodeDO);
        when(scrmGroupRetailUserCouponRecordDomainService.insertRecord(anyLong(), anyString(), anyString(), anyInt(), anyLong(), anyString())).thenReturn(1);
        // Mock the getShorUrl method
        doReturn("http://short.url").when(deepSeaWxHandler).getShorUrl(anyString(), anyBoolean());
        // act
        MsgPushContentDTO result = deepSeaWxHandler.getAiSceneActivPageMsgPushContentDTO(processOrchestrationDTO, executeManagementDTO, actionAttachmentDTOList, partWxInvokeDetailDOS, aiSceneContent);
        // assert
        assertNotNull(result);
        assertNotNull(result.getMiniProgramDTO());
        assertEquals(GroupDynamicCodeConstants.MX_MINIP_APPID, result.getMiniProgramDTO().getAppId());
        assertEquals("testTitle", result.getMiniProgramDTO().getDescription());
    }

    @Test
    public void testGetAiSceneActivPageMsgPushContentDTODefaultHeadpicUrl() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        IntelligentFollowResultDTO aiSceneContent = new IntelligentFollowResultDTO();
        aiSceneContent.setShopId(123L);
        aiSceneContent.setCouponId("testCouponId");
        processOrchestrationDTO.setAiSceneContent(aiSceneContent);
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        executeManagementDTO.setPoiId("123");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setId(1L);
        actionAttachmentDTO.setProcessOrchestrationId(1L);
        actionAttachmentDTO.setProcessOrchestrationVersion("1.0");
        actionAttachmentDTO.setProcessOrchestrationNodeId(1L);
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTitle("testTitle");
        supplyDetailDTO.setSupplyType(ScrmProcessOrchestrationContentSupplyTypeEnum.COUPON_PROMOTION.getValue());
        actionAttachmentDTO.setAttachmentSupplyDetailDTO(supplyDetailDTO);
        // Use concrete implementation instead of Arrays.asList
        List<ScrmProcessOrchestrationActionAttachmentDTO> actionAttachmentDTOList = new java.util.ArrayList<ScrmProcessOrchestrationActionAttachmentDTO>();
        actionAttachmentDTOList.add(actionAttachmentDTO);
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO.setExecuteLogId(1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> partWxInvokeDetailDOS = new java.util.ArrayList<ScrmAmProcessOrchestrationWxInvokeDetailDO>();
        partWxInvokeDetailDOS.add(invokeDetailDO);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setTargetMtUserId(456L);
        executeLogDO.setAppId("testAppId");
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneCode("testSceneCode");
        sceneCodeDO.setPoiRestrict(0);
        when(executeLogDOMapper.selectByPrimaryKey(1L)).thenReturn(executeLogDO);
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class))).thenReturn("testDistributorCode");
        when(productManagementService.getActivSceneCodeDO(any(), any(), any(), any(), any())).thenReturn(sceneCodeDO);
        when(scrmGroupRetailUserCouponRecordDomainService.insertRecord(anyLong(), anyString(), anyString(), anyInt(), anyLong(), anyString())).thenReturn(1);
        // Mock the getShorUrl method
        doReturn("http://short.url").when(deepSeaWxHandler).getShorUrl(anyString(), anyBoolean());
        // act
        MsgPushContentDTO result = deepSeaWxHandler.getAiSceneActivPageMsgPushContentDTO(processOrchestrationDTO, executeManagementDTO, actionAttachmentDTOList, partWxInvokeDetailDOS, aiSceneContent);
        // assert
        assertNotNull(result);
        assertNotNull(result.getMiniProgramDTO());
        assertEquals("https://p0.meituan.net/ingee/f64e62bdc303ae719148960b6a441df3640956.png", result.getMiniProgramDTO().getThumbnail());
    }
}
