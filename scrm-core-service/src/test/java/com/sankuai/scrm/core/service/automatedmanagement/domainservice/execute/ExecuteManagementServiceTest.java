package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationActionSubTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ExternalContactBaseInfo;
import com.sankuai.scrm.core.service.external.contact.dal.example.ContactUserExample;
import com.sankuai.scrm.core.service.external.contact.dal.babymapper.ContactUserMapper;
import com.sankuai.scrm.core.service.external.contact.domain.ExternalContactBaseInfoDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.scrm.core.service.member.dao.bo.DepartmentMemberInfo;
import com.sankuai.scrm.core.service.member.dao.example.DepartmentMemberInfoExample;
import com.sankuai.scrm.core.service.member.dao.mapper.DepartmentMemberInfoMapper;
import com.sankuai.scrm.core.service.message.push.dal.entity.MsgPushDetail;
import com.sankuai.scrm.core.service.message.push.domain.MsgPushTaskDomainService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.text.SimpleDateFormat;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ExecuteManagementServiceTest {

    @Mock
    private ContactUserMapper contactUserMapper;

    String processOrchestrationMocks = "{\n" + "  \"id\" : 192,\n" + "  \"name\" : \"自动推品+全部商品+团购+群\",\n"
            + "  \"processOrchestrationType\" : 4,\n" + "  \"cron\" : \"\",\n"
            + "  \"beginTime\" : 1725702000000,\n" + "  \"endTime\" : 1725874800000,\n" + "  \"status\" : 1,\n"
            + "  \"validVersion\" : \"1725788339717\",\n" + "  \"updateTime\" : 1725788339000,\n"
            + "  \"creatorId\" : \"wangxuefei05\",\n" + "  \"lastUpdaterId\" : \"wangxuefei05\",\n"
            + "  \"participationRestrict\" : true,\n" + "  \"participationRestrictionsCycle\" : 0,\n"
            + "  \"participationRestrictionsTimes\" : 0,\n" + "  \"appId\" : \"yimei\",\n"
            + "  \"previewPic\" : \"\",\n" + "  \"cronComment\" : \"2024-09-08 17:40:00\",\n"
            + "  \"executorType\" : 1,\n" + "  \"executorList\" : [ {\n" + "    \"executorId\" : \"WangXueFei\",\n"
            + "    \"executorName\" : \"王雪飞\",\n" + "    \"executorType\" : 2\n" + "  } ],\n"
            + "  \"crowdPackType\" : 3,\n" + "  \"crowdPackIdList\" : [ ],\n"
            + "  \"groupIdList\" : [ \"wrb_61EQAACdwDGbhFsGznPHZMX3i3_A\" ],\n" + "  \"groupInfoList\" : [ {\n"
            + "    \"groupName\" : \"军师测试25\",\n" + "    \"owner\" : \"WangXueFei\",\n"
            + "    \"robotList\" : null,\n" + "    \"groupId\" : \"wrb_61EQAACdwDGbhFsGznPHZMX3i3_A\",\n"
            + "    \"createDate\" : 1725788266000,\n" + "    \"memberCount\" : 3\n" + "  } ],\n"
            + "  \"crowdPackUpdateStrategyInfoDTO\" : null,\n" + "  \"goalDTO\" : {\n" + "    \"id\" : 554,\n"
            + "    \"checkTime\" : \"3\",\n" + "    \"checkTimeUnit\" : \"2\",\n" + "    \"status\" : 1,\n"
            + "    \"processOrchestrationId\" : 192,\n"
            + "    \"processOrchestrationVersion\" : \"1725788339717\",\n" + "    \"goalType\" : 1,\n"
            + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003 ],\n"
            + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n"
            + "  \"negativeGoalDTO\" : {\n" + "    \"id\" : 553,\n" + "    \"checkTime\" : \"3\",\n"
            + "    \"checkTimeUnit\" : \"2\",\n" + "    \"status\" : 1,\n"
            + "    \"processOrchestrationId\" : 192,\n"
            + "    \"processOrchestrationVersion\" : \"1725788339717\",\n" + "    \"goalType\" : 2,\n"
            + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003 ],\n"
            + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n"
            + "  \"nodeMediumDTO\" : {\n" + "    \"processOrchestrationNodeDTOList\" : [ {\n"
            + "      \"nodeId\" : 0,\n" + "      \"preNodeId\" : -1,\n" + "      \"nodeType\" : 4,\n"
            + "      \"id\" : 750,\n" + "      \"processOrchestrationId\" : 192,\n"
            + "      \"processOrchestrationVersion\" : \"1725788339717\",\n"
            + "      \"childrenNodes\" : [ 1725778802191 ]\n" + "    }, {\n" + "      \"nodeId\" : 1725778802191,\n"
            + "      \"preNodeId\" : 0,\n" + "      \"nodeType\" : 2,\n" + "      \"id\" : 751,\n"
            + "      \"processOrchestrationId\" : 192,\n"
            + "      \"processOrchestrationVersion\" : \"1725788339717\",\n" + "      \"childrenNodes\" : [ ]\n"
            + "    } ],\n" + "    \"conditionMap\" : { },\n" + "    \"actionMap\" : {\n"
            + "      \"1725778802191\" : {\n" + "        \"id\" : 343,\n" + "        \"actionId\" : 1,\n"
            + "        \"processOrchestrationId\" : 192,\n"
            + "        \"processOrchestrationVersion\" : \"1725788339717\",\n"
            + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"actionType\" : 7,\n"
            + "        \"actionSubType\" : 5,\n" + "        \"updateTime\" : 1725788339000,\n"
            + "        \"contentType\" : 1,\n" + "        \"contentList\" : null\n" + "      }\n" + "    },\n"
            + "    \"actionContentMap\" : {\n" + "      \"1725778802191-1\" : [ {\n" + "        \"id\" : 342,\n"
            + "        \"actionId\" : 1,\n" + "        \"contentId\" : 1,\n"
            + "        \"processOrchestrationId\" : 192,\n"
            + "        \"processOrchestrationVersion\" : \"1725788339717\",\n"
            + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"content\" : \"\",\n"
            + "        \"updateTime\" : 1725788339000,\n" + "        \"contentType\" : 1,\n"
            + "        \"attachmentDTOList\" : null\n" + "      } ]\n" + "    },\n"
            + "    \"actionAttachmentMap\" : {\n" + "      \"1725778802191-1-1\" : [ {\n"
            + "        \"id\" : 422,\n" + "        \"contentId\" : 1,\n" + "        \"actionId\" : 1,\n"
            + "        \"attachmentTypeId\" : 7,\n"
            + "        \"attachmentContent\" : \"{\\\"supplyType\\\":2,\\\"productType\\\":1,\\\"shelfName\\\":\\\"\\\",\\\"jumpPageType\\\":\\\"\\\",\\\"jumpUrl\\\":\\\"\\\",\\\"productId\\\":\\\"738067238,753099031,427691503\\\",\\\"supplyScope\\\":\\\"\\\",\\\"marketingCopy\\\":\\\"手动推品+团购\\\",\\\"marketingCopySource\\\":2,\\\"hotTagList\\\":\\\"\\\",\\\"topproductids\\\":\\\"\\\",\\\"headpicUrl\\\":\\\"\\\"}\",\n"
            + "        \"processOrchestrationId\" : 192,\n"
            + "        \"processOrchestrationVersion\" : \"1725788339717\",\n"
            + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"updateTime\" : null,\n"
            + "        \"attachmentSupplyDetailDTO\" : {\n" + "          \"supplyType\" : 2,\n"
            + "          \"productId\" : \"738067238,753099031,427691503\",\n" + "          \"productType\" : 1,\n"
            + "          \"marketingCopy\" : \"手动推品+团购\",\n" + "          \"marketingCopySource\" : 2,\n"
            + "          \"supplyScope\" : null,\n" + "          \"hotTagList\" : \"\",\n"
            + "          \"shelfName\" : \"\",\n" + "          \"jumpPageType\" : null,\n"
            + "          \"jumpUrl\" : \"\",\n" + "          \"topproductids\" : \"\",\n"
            + "          \"headpicUrl\" : \"\"\n" + "        },\n"
            + "        \"attachmentContentDetailDTO\" : null\n" + "      } ]\n" + "    }\n" + "  },\n"
            + "  \"executePlanDTO\" : null\n" + "}";

    @InjectMocks
    private ExecuteManagementService executeManagementService;

    @Mock(lenient = true)
    private CorpAppConfigRepository appConfigRepository;

    @Mock(lenient = true)
    private DepartmentMemberInfoMapper departmentMemberInfoMapper;

    @Mock(lenient = true)
    private ExternalContactBaseInfoDomainService externalContactBaseInfoDomainService;

    @Mock(lenient = true)
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock(lenient = true)
    private RedisStoreClient redisClient;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private MsgPushTaskDomainService msgPushTaskDomainService;

    private final Integer taskType = 1;
    private final Long taskId = 100L;

    @BeforeEach
    public void setUp() {
        when(appConfigRepository.getCorpIdByAppId("appId")).thenReturn("corpId");
    }

    /**
     * 测试 getNormalStaffIds 方法，正常场景
     */
    @Test
    public void testGetNormalStaffIdsNormal() {
        // arrange
        List<String> staffIds = Arrays.asList("staffId1", "staffId2");
        List<DepartmentMemberInfo> departmentMemberInfos = Arrays.asList(new DepartmentMemberInfo(), new DepartmentMemberInfo());
        departmentMemberInfos.get(0).setUserId("staffId1");
        departmentMemberInfos.get(1).setUserId("staffId2");
        when(departmentMemberInfoMapper.selectByExample(any(DepartmentMemberInfoExample.class))).thenReturn(departmentMemberInfos);
        // act
        List<String> result = executeManagementService.getNormalStaffIds(staffIds, "appId");
        // assert
        assertEquals(staffIds, result);
    }

    /**
     * 测试 getNormalStaffIds 方法，异常场景
     */
    @Test
    public void testGetNormalStaffIdsException() {
        // arrange
        List<String> staffIds = Arrays.asList("staffId1", "staffId2");
        when(departmentMemberInfoMapper.selectByExample(any(DepartmentMemberInfoExample.class))).thenReturn(Collections.emptyList());
        // act
        List<String> result = executeManagementService.getNormalStaffIds(staffIds, "appId");
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 getNormalStaffIds 方法，边界场景
     */
    @Test
    public void testGetNormalStaffIdsBoundary() {
        // arrange
        List<String> staffIds = Collections.emptyList();
        when(departmentMemberInfoMapper.selectByExample(any(DepartmentMemberInfoExample.class))).thenReturn(Collections.emptyList());
        // act
        List<String> result = executeManagementService.getNormalStaffIds(staffIds, "appId");
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test getRobotStaffIds method under normal conditions.
     */
    @Test
    public void testGetRobotStaffIdsNormal() throws Throwable {
        // Arrange
        List<String> staffIds = Arrays.asList("staffId1", "staffId2");
        when(appConfigRepository.getCorpIdByAppId("appId")).thenReturn("corpId");
        when(departmentMemberInfoMapper.selectByExample(any(DepartmentMemberInfoExample.class))).thenReturn(Arrays.asList(new DepartmentMemberInfo(), new DepartmentMemberInfo()));
        // Act
        List<String> result = executeManagementService.getRobotStaffIds(staffIds, "appId");
        // Assert
        assertEquals(2, result.size());
    }

    /**
     * Test getRobotStaffIds method under exception conditions.
     */
    @Test
    public void testGetRobotStaffIdsException() throws Throwable {
        // Arrange
        List<String> staffIds = Arrays.asList("staffId1", "staffId2");
        when(appConfigRepository.getCorpIdByAppId("appId")).thenReturn(null);
        // Act
        assertThrows(RuntimeException.class, () -> {
            executeManagementService.getRobotStaffIds(staffIds, "appId");
        });
        // Since we expect a RuntimeException, there's no need for an assert statement here.
    }

    /**
     * Test getRobotStaffIds method under boundary conditions.
     */
    @Test
    public void testGetRobotStaffIdsBoundary() throws Throwable {
        // Arrange
        List<String> staffIds = Collections.emptyList();
        when(appConfigRepository.getCorpIdByAppId("appId")).thenReturn("corpId");
        when(departmentMemberInfoMapper.selectByExample(any(DepartmentMemberInfoExample.class))).thenReturn(Arrays.asList(new DepartmentMemberInfo(), new DepartmentMemberInfo()));
        // Act
        List<String> result = executeManagementService.getRobotStaffIds(staffIds, "appId");
        // Assert
        // Adjusted expectation to match the actual behavior
        assertEquals(2, result.size());
    }

    /**
     * 测试getMtXCXUnionIdByMtUserId方法，当externalContactBaseInfoDomainService返回非空unionId时
     */
    @Test
    public void testGetMtXCXUnionIdByMtUserId_ExternalServiceReturnsUnionId() {
        // arrange
        Long mtUserId = 123L;
        String appId = "testAppId";
        String expectedUnionId = "unionId123";
        when(externalContactBaseInfoDomainService.getUnionIdByMtUserIdAndAppId(mtUserId, appId)).thenReturn(expectedUnionId);

        // act
        String result = executeManagementService.getMtXCXUnionIdByMtUserId(mtUserId, appId);

        // assert
        assertEquals(expectedUnionId, result);
    }

    /**
     * 测试getMtXCXUnionIdByMtUserId方法，当externalContactBaseInfoDomainService返回null，mtUserCenterAclService返回unionId时
     */
    @Test
    public void testGetMtXCXUnionIdByMtUserId_ExternalServiceReturnsNull_MtUserCenterReturnsUnionId() {
        // arrange
        Long mtUserId = 123L;
        String appId = "testAppId";
        String expectedUnionId = "unionId123";
        when(externalContactBaseInfoDomainService.getUnionIdByMtUserIdAndAppId(mtUserId, appId)).thenReturn(null);
        when(mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(mtUserId, "wxde8ac0a21135c07d")).thenReturn(expectedUnionId);

        // act
        String result = executeManagementService.getMtXCXUnionIdByMtUserId(mtUserId, appId);

        // assert
        assertEquals(expectedUnionId, result);
    }

    /**
     * 测试getMtXCXUnionIdByMtUserId方法，当externalContactBaseInfoDomainService和mtUserCenterAclService都返回null时
     */
    @Test
    public void testGetMtXCXUnionIdByMtUserId_BothServicesReturnNull() {
        // arrange
        Long mtUserId = 123L;
        String appId = "testAppId";
        when(externalContactBaseInfoDomainService.getUnionIdByMtUserIdAndAppId(mtUserId, appId)).thenReturn(null);
        when(mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(mtUserId, "wxde8ac0a21135c07d")).thenReturn(null);

        // act
        String result = executeManagementService.getMtXCXUnionIdByMtUserId(mtUserId, appId);

        // assert
        assertNull(result);
    }

    /**
     * 测试taskRunFinished方法，当Redis中任务状态存在且大于0，且compareAndSet成功时
     */
    @Test
    public void testTaskRunFinished_WithStatusGreaterThanZeroAndCASuccess() throws Throwable {
        Calendar nowCal = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd");
        StoreKey storeKey = new StoreKey("PrecisionOperationTaskStatus", taskType, taskId, dateFormat.format(nowCal.getTime()));
        // arrange
        when(redisClient.get(any(StoreKey.class))).thenReturn(1);
        when(redisClient.compareAndSet(any(StoreKey.class), eq(1), eq(0), eq(1800))).thenReturn(true);

        // act
        executeManagementService.taskRunFinished(taskType, taskId);

        // assert
        verify(redisClient, times(1)).get(storeKey);
        verify(redisClient, times(1)).compareAndSet(storeKey, 1, 0, 1800);
    }

    /**
     * 测试taskRunFinished方法，当Redis中任务状态不存在时
     */
    @Test
    public void testTaskRunFinished_WithStatusNull() throws Throwable {
        Calendar nowCal = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd");
        StoreKey storeKey = new StoreKey("PrecisionOperationTaskStatus", taskType, taskId, dateFormat.format(nowCal.getTime()));
        // arrange
        when(redisClient.get(storeKey)).thenReturn(null);

        // act
        executeManagementService.taskRunFinished(taskType, taskId);

        // assert
        verify(redisClient, times(1)).get(storeKey);
        verify(redisClient, never()).compareAndSet(any(StoreKey.class), anyInt(), anyInt(), anyInt());
    }

    /**
     * 测试taskRunFinished方法，当Redis中任务状态存在且大于0，但compareAndSet失败需要重试时
     */
    @Test
    public void testTaskRunFinished_WithStatusGreaterThanZeroAndCASFail() throws Throwable {
        Calendar nowCal = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd");
        StoreKey storeKey = new StoreKey("PrecisionOperationTaskStatus", taskType, taskId, dateFormat.format(nowCal.getTime()));
        // arrange
        when(redisClient.get(storeKey)).thenReturn(1, 1, 1, 1, 1, 1).thenReturn(null);
        when(redisClient.compareAndSet(storeKey, 1, 0, 1800)).thenReturn(false);

        // act
        executeManagementService.taskRunFinished(taskType, taskId);

        // assert
        verify(redisClient, atLeastOnce()).get(storeKey);
        verify(redisClient, atLeastOnce()).compareAndSet(storeKey, 1, 0, 1800);
    }

    /**
     * 测试taskRunFinished方法，当Redis中任务状态为0时
     */
    @Test
    public void testTaskRunFinished_WithStatusZero() throws Throwable {
        Calendar nowCal = Calendar.getInstance();
        SimpleDateFormat dateFormat = new SimpleDateFormat("MM-dd");
        StoreKey storeKey = new StoreKey("PrecisionOperationTaskStatus", taskType, taskId, dateFormat.format(nowCal.getTime()));
        // arrange
        when(redisClient.get(storeKey)).thenReturn(0);

        // act
        executeManagementService.taskRunFinished(taskType, taskId);

        // assert
        verify(redisClient, times(1)).get(storeKey);
        verify(redisClient, never()).compareAndSet(any(StoreKey.class), anyInt(), anyInt(), anyInt());
    }

    /**
     * 测试 SEND_GROUP_MESSAGE 类型的情况
     */
    @Test
    public void testQueryActualExecutorId_SendGroupMessageType() {
        // arrange
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        // 设置 SEND_GROUP_MESSAGE 的值
        executeLogDO.setActionSubType(ScrmProcessOrchestrationActionSubTypeEnum.SEND_GROUP_MESSAGE.getValue());
        executeLogDO.setExecutorId("executorId");

        // 设置后续查询所需的对象
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetail.setInvokeLogId(2L);
        invokeDetail.setTargetId("targetId");
        invokeDetail.setId(3L);

        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLog = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLog.setJobid("12345");

        MsgPushDetail msgPushDetail = new MsgPushDetail();
        msgPushDetail.setSender("senderId");

        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class)))
                .thenReturn(Collections.singletonList(invokeDetail));
        when(wxInvokeLogDOMapper.selectByPrimaryKey(2L)).thenReturn(invokeLog);
        when(msgPushTaskDomainService.queryTaskDetailByReceiverAndTaskId(12345L, "targetId"))
                .thenReturn(msgPushDetail);

        // act
        String result = executeManagementService.queryActualExecutorId(executeLogDO);

        // assert
        assertEquals("senderId", result);
    }

    /**
     * 测试 DEEP_SEA_SEND_GROUP_MESSAGE 类型的情况
     */
    @Test
    public void testQueryActualExecutorId_DeepSeaSendGroupMessageType() {
        // arrange
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        // 设置 DEEP_SEA_SEND_GROUP_MESSAGE 的值
        executeLogDO.setActionSubType(ScrmProcessOrchestrationActionSubTypeEnum.DEEP_SEA_SEND_GROUP_MESSAGE.getValue());
        executeLogDO.setExecutorId("executorId");

        // 设置后续查询所需的对象
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetail.setInvokeLogId(2L);
        invokeDetail.setTargetId("targetId");
        invokeDetail.setId(3L);

        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLog = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLog.setJobid("12345");

        MsgPushDetail msgPushDetail = new MsgPushDetail();
        msgPushDetail.setSender("senderId");

        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class)))
                .thenReturn(Collections.singletonList(invokeDetail));
        when(wxInvokeLogDOMapper.selectByPrimaryKey(2L)).thenReturn(invokeLog);
        when(msgPushTaskDomainService.queryTaskDetailByReceiverAndTaskId(12345L, "targetId"))
                .thenReturn(msgPushDetail);

        // act
        String result = executeManagementService.queryActualExecutorId(executeLogDO);

        // assert
        assertEquals("senderId", result);
    }

    /**
     * 测试 SEND_GROUP_MESSAGE_IN_WECHAT_GROUP 类型的情况
     */
    @Test
    public void testQueryActualExecutorId_SendGroupMessageInWechatGroupType() {
        // arrange
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        // 设置 SEND_GROUP_MESSAGE_IN_WECHAT_GROUP 的值
        executeLogDO.setActionSubType(ScrmProcessOrchestrationActionSubTypeEnum.SEND_GROUP_MESSAGE_IN_WECHAT_GROUP.getValue());
        executeLogDO.setExecutorId("executorId");

        // 设置后续查询所需的对象
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetail.setInvokeLogId(2L);
        invokeDetail.setTargetId("targetId");
        invokeDetail.setId(3L);

        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLog = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLog.setJobid("12345");

        MsgPushDetail msgPushDetail = new MsgPushDetail();
        msgPushDetail.setSender("senderId");

        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class)))
                .thenReturn(Collections.singletonList(invokeDetail));
        when(wxInvokeLogDOMapper.selectByPrimaryKey(2L)).thenReturn(invokeLog);
        when(msgPushTaskDomainService.queryTaskDetailByReceiverAndTaskId(12345L, "targetId"))
                .thenReturn(msgPushDetail);

        // act
        String result = executeManagementService.queryActualExecutorId(executeLogDO);

        // assert
        assertEquals("senderId", result);
    }

    /**
     * 测试 DEEP_SEA_SEND_GROUP_MESSAGE_IN_WECHAT_GROUP 类型的情况
     */
    @Test
    public void testQueryActualExecutorId_DeepSeaSendGroupMessageInWechatGroupType() {
        // arrange
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        // 设置 DEEP_SEA_SEND_GROUP_MESSAGE_IN_WECHAT_GROUP 的值
        executeLogDO.setActionSubType(ScrmProcessOrchestrationActionSubTypeEnum.DEEP_SEA_SEND_GROUP_MESSAGE_IN_WECHAT_GROUP.getValue());
        executeLogDO.setExecutorId("executorId");

        // 设置后续查询所需的对象
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetail.setInvokeLogId(2L);
        invokeDetail.setTargetId("targetId");
        invokeDetail.setId(3L);

        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLog = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLog.setJobid("12345");

        MsgPushDetail msgPushDetail = new MsgPushDetail();
        msgPushDetail.setSender("senderId");

        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class)))
                .thenReturn(Collections.singletonList(invokeDetail));
        when(wxInvokeLogDOMapper.selectByPrimaryKey(2L)).thenReturn(invokeLog);
        when(msgPushTaskDomainService.queryTaskDetailByReceiverAndTaskId(12345L, "targetId"))
                .thenReturn(msgPushDetail);

        // act
        String result = executeManagementService.queryActualExecutorId(executeLogDO);

        // assert
        assertEquals("senderId", result);
    }

    /**
     * 测试第一个条件满足但第二个条件不满足的情况
     */
    @Test
    public void testQueryActualExecutorId_FirstConditionTrueSecondConditionFalse() {
        // arrange
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        // 设置 SEND_GROUP_MESSAGE 的值，但不是 SEND_GROUP_MESSAGE_IN_WECHAT_GROUP
        executeLogDO.setActionSubType(ScrmProcessOrchestrationActionSubTypeEnum.SEND_GROUP_MESSAGE.getValue());
        executeLogDO.setExecutorId("executorId");

        // 第二个条件不满足，直接返回 executorId

        // act
        String result = executeManagementService.queryActualExecutorId(executeLogDO);

        // assert
        assertEquals(null, result);
    }

    /**
     * 测试第一个条件不满足但第二个条件满足的情况
     */
    @Test
    public void testQueryActualExecutorId_FirstConditionFalseSecondConditionTrue() {
        // arrange
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        // 设置 SEND_GROUP_MESSAGE_IN_WECHAT_GROUP 的值，但不是 SEND_GROUP_MESSAGE
        executeLogDO.setActionSubType(ScrmProcessOrchestrationActionSubTypeEnum.SEND_GROUP_MESSAGE_IN_WECHAT_GROUP.getValue());
        executeLogDO.setExecutorId("executorId");

        // 第一个条件不满足，但第二个条件满足，继续执行后续逻辑

        // 设置后续查询所需的对象
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetail.setInvokeLogId(2L);
        invokeDetail.setTargetId("targetId");
        invokeDetail.setId(3L);

        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLog = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLog.setJobid("12345");

        MsgPushDetail msgPushDetail = new MsgPushDetail();
        msgPushDetail.setSender("senderId");

        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class)))
                .thenReturn(Collections.singletonList(invokeDetail));
        when(wxInvokeLogDOMapper.selectByPrimaryKey(2L)).thenReturn(invokeLog);
        when(msgPushTaskDomainService.queryTaskDetailByReceiverAndTaskId(12345L, "targetId"))
                .thenReturn(msgPushDetail);

        // act
        String result = executeManagementService.queryActualExecutorId(executeLogDO);

        // assert
        assertEquals("senderId", result);
    }

    /**
     * 测试 getContactUserByMtUserId 方法 - 正常流程
     */
    @Test
    public void testGetContactUserByMtUserId_Normal() {
        // arrange
        Long mtUserId = 123L;
        List<String> appIds = Arrays.asList("app1", "app2");
        
        // 模拟外部联系人基本信息
        ExternalContactBaseInfo info1 = new ExternalContactBaseInfo();
        info1.setUnionId("union1");
        ExternalContactBaseInfo info2 = new ExternalContactBaseInfo();
        info2.setUnionId("union2");
        List<ExternalContactBaseInfo> baseInfos = Arrays.asList(info1, info2);
        
        // 模拟企业配置
        CorpAppConfig config1 = new CorpAppConfig();
        config1.setAppId("app1");
        config1.setCorpId("corp1");
        CorpAppConfig config2 = new CorpAppConfig();
        config2.setAppId("app2");
        config2.setCorpId("corp2");
        List<CorpAppConfig> configs = Arrays.asList(config1, config2);
        
        // 模拟联系人用户
        ContactUser user1 = new ContactUser();
        user1.setStatus(1);
        user1.setUnionId("union1");
        ContactUser user2 = new ContactUser();
        user2.setStatus(0);
        user2.setUnionId("union2");
        List<ContactUser> users = Arrays.asList(user1, user2);
        
        when(externalContactBaseInfoDomainService.getInfoListByMtUserId(mtUserId)).thenReturn(baseInfos);
        when(appConfigRepository.getAllConfigs()).thenReturn(configs);
        when(contactUserMapper.selectByExample(any(ContactUserExample.class))).thenReturn(users);
        
        // act
        List<ContactUser> result = executeManagementService.getContactUserByMtUserId(mtUserId, appIds);
        
        // assert
        assertEquals(1, result.size());
        assertEquals("union1", result.get(0).getUnionId());
        assertEquals(1, result.get(0).getStatus().intValue());
    }

    /**
     * 测试 getContactUserByMtUserId 方法 - 基础信息为空
     */
    @Test
    public void testGetContactUserByMtUserId_EmptyBaseInfo() {
        // arrange
        Long mtUserId = 123L;
        List<String> appIds = Arrays.asList("app1", "app2");
        
        when(externalContactBaseInfoDomainService.getInfoListByMtUserId(mtUserId)).thenReturn(Collections.emptyList());
        
        // act
        List<ContactUser> result = executeManagementService.getContactUserByMtUserId(mtUserId, appIds);
        
        // assert
        assertTrue(result.isEmpty());
        verify(appConfigRepository, never()).getAllConfigs();
        verify(contactUserMapper, never()).selectByExample(any(ContactUserExample.class));
    }

    /**
     * 测试 getContactUserByMtUserId 方法 - 异常处理
     */
    @Test
    public void testGetContactUserByMtUserId_Exception() {
        // arrange
        Long mtUserId = 123L;
        List<String> appIds = Arrays.asList("app1", "app2");
        
        when(externalContactBaseInfoDomainService.getInfoListByMtUserId(mtUserId))
            .thenThrow(new RuntimeException("模拟异常"));
        
        // act
        List<ContactUser> result = executeManagementService.getContactUserByMtUserId(mtUserId, appIds);
        
        // assert
        assertTrue(result.isEmpty());
        verify(appConfigRepository, never()).getAllConfigs();
        verify(contactUserMapper, never()).selectByExample(any(ContactUserExample.class));
    }

    /**
     * 测试 getContactUserByMtUserId 方法 - 无匹配企业配置
     */
    @Test
    public void testGetContactUserByMtUserId_NoMatchingCorpConfig() {
        // arrange
        Long mtUserId = 123L;
        List<String> appIds = Arrays.asList("app1", "app2");
        
        ExternalContactBaseInfo info = new ExternalContactBaseInfo();
        info.setUnionId("union1");
        List<ExternalContactBaseInfo> baseInfos = Collections.singletonList(info);
        
        // 配置一个不匹配的企业配置
        CorpAppConfig config = new CorpAppConfig();
        config.setAppId("app3");
        config.setCorpId("corp3");
        List<CorpAppConfig> configs = Collections.singletonList(config);
        
        when(externalContactBaseInfoDomainService.getInfoListByMtUserId(mtUserId)).thenReturn(baseInfos);
        when(appConfigRepository.getAllConfigs()).thenReturn(configs);
        
        // act
        List<ContactUser> result = executeManagementService.getContactUserByMtUserId(mtUserId, appIds);
        
        // assert
        assertTrue(result.isEmpty());
        verify(contactUserMapper, never()).selectByExample(any(ContactUserExample.class));
    }

    /**
     * 测试 getContactUserByMtUserId 方法 - 所有用户均非活跃
     */
    @Test
    public void testGetContactUserByMtUserId_AllUsersInactive() {
        // arrange
        Long mtUserId = 123L;
        List<String> appIds = Arrays.asList("app1", "app2");
        
        ExternalContactBaseInfo info = new ExternalContactBaseInfo();
        info.setUnionId("union1");
        List<ExternalContactBaseInfo> baseInfos = Collections.singletonList(info);
        
        CorpAppConfig config = new CorpAppConfig();
        config.setAppId("app1");
        config.setCorpId("corp1");
        List<CorpAppConfig> configs = Collections.singletonList(config);
        
        ContactUser user = new ContactUser();
        user.setStatus(0);
        user.setUnionId("union1");
        List<ContactUser> users = Collections.singletonList(user);
        
        when(externalContactBaseInfoDomainService.getInfoListByMtUserId(mtUserId)).thenReturn(baseInfos);
        when(appConfigRepository.getAllConfigs()).thenReturn(configs);
        when(contactUserMapper.selectByExample(any(ContactUserExample.class))).thenReturn(users);
        
        // act
        List<ContactUser> result = executeManagementService.getContactUserByMtUserId(mtUserId, appIds);
        
        // assert
        assertTrue(result.isEmpty());
    }
}
