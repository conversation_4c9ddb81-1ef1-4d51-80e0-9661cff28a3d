package com.sankuai.scrm.core.service.infrastructure.acl.ds;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.AssistantInfo;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.MobileAddFriendRealTimeEntity;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.service.fe.corp.ds.TResponse.BaseTResponse;
import com.sankuai.service.fe.corp.ds.TResponse.openapi.inefficient.user.MobileAddFriendRealTimeRespDTO;
import com.sankuai.service.fe.corp.ds.tservice.openapi.inefficient.user.AddContactApplicationTService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DsAddFriendAclTest {

    @Mock
    private AddContactApplicationTService addContactApplicationTService;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private DsAssistantAcl dsAssistantAcl;

    @InjectMocks
    private DsAddFriendAcl dsAddFriendAcl;

    /**
     * 测试正常流程 - 成功返回taskId
     */
    @Test
    public void testDsMobileAddFriendRealTime_Success() throws Throwable {
        // arrange
        MobileAddFriendRealTimeEntity entity = new MobileAddFriendRealTimeEntity();
        entity.setAppId("app1");
        entity.setAccountId("account1");
        entity.setAddNumber("***********");
        entity.setNumberType(1);
        entity.setWelcomeContent("welcome");
        CorpAppConfig config = new CorpAppConfig();
        config.setBusinessCode("business1");
        when(appConfigRepository.getConfigByAppId("app1")).thenReturn(config);
        AssistantInfo assistantInfo = new AssistantInfo();
        assistantInfo.setAssistantId(1001L);
        when(dsAssistantAcl.getAssistantByAccount("app1", "account1")).thenReturn(assistantInfo);
        BaseTResponse<MobileAddFriendRealTimeRespDTO> response = new BaseTResponse<>();
        response.setCode(0);
        MobileAddFriendRealTimeRespDTO respDTO = new MobileAddFriendRealTimeRespDTO();
        respDTO.setTaskId("task123");
        response.setData(respDTO);
        when(addContactApplicationTService.mobileAddFriendRealTime(any())).thenReturn(response);
        // act
        String result = dsAddFriendAcl.dsMobileAddFriendRealTime(entity);
        // assert
        assertEquals("task123", result);
        verify(appConfigRepository).getConfigByAppId("app1");
        verify(dsAssistantAcl).getAssistantByAccount("app1", "account1");
        verify(addContactApplicationTService).mobileAddFriendRealTime(any());
    }

    /**
     * 测试配置获取失败 - 抛出异常
     */
    @Test
    public void testDsMobileAddFriendRealTime_ConfigNotFound() throws Throwable {
        // arrange
        MobileAddFriendRealTimeEntity entity = new MobileAddFriendRealTimeEntity();
        entity.setAppId("app1");
        entity.setAccountId("account1");
        when(appConfigRepository.getConfigByAppId("app1")).thenReturn(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> dsAddFriendAcl.dsMobileAddFriendRealTime(entity));
        verify(appConfigRepository).getConfigByAppId("app1");
        verifyNoInteractions(dsAssistantAcl, addContactApplicationTService);
    }

    /**
     * 测试助手信息获取失败 - 返回null
     */
    @Test
    public void testDsMobileAddFriendRealTime_AssistantNotFound() throws Throwable {
        // arrange
        MobileAddFriendRealTimeEntity entity = new MobileAddFriendRealTimeEntity();
        entity.setAppId("app1");
        entity.setAccountId("account1");
        CorpAppConfig config = new CorpAppConfig();
        config.setBusinessCode("business1");
        when(appConfigRepository.getConfigByAppId("app1")).thenReturn(config);
        when(dsAssistantAcl.getAssistantByAccount("app1", "account1")).thenReturn(null);
        // act
        String result = dsAddFriendAcl.dsMobileAddFriendRealTime(entity);
        // assert
        assertNull(result);
        verify(appConfigRepository).getConfigByAppId("app1");
        verify(dsAssistantAcl).getAssistantByAccount("app1", "account1");
        verifyNoInteractions(addContactApplicationTService);
    }

    /**
     * 测试远程服务调用失败 - 返回非0 code
     */
    @Test
    public void testDsMobileAddFriendRealTime_ServiceCallFailed() throws Throwable {
        // arrange
        MobileAddFriendRealTimeEntity entity = new MobileAddFriendRealTimeEntity();
        entity.setAppId("app1");
        entity.setAccountId("account1");
        entity.setAddNumber("***********");
        entity.setNumberType(1);
        entity.setWelcomeContent("welcome");
        CorpAppConfig config = new CorpAppConfig();
        config.setBusinessCode("business1");
        when(appConfigRepository.getConfigByAppId("app1")).thenReturn(config);
        AssistantInfo assistantInfo = new AssistantInfo();
        assistantInfo.setAssistantId(1001L);
        when(dsAssistantAcl.getAssistantByAccount("app1", "account1")).thenReturn(assistantInfo);
        BaseTResponse<MobileAddFriendRealTimeRespDTO> response = new BaseTResponse<>();
        response.setCode(500);
        response.setMsg("Internal Error");
        when(addContactApplicationTService.mobileAddFriendRealTime(any())).thenReturn(response);
        // act
        String result = dsAddFriendAcl.dsMobileAddFriendRealTime(entity);
        // assert
        assertNull(result);
        verify(appConfigRepository).getConfigByAppId("app1");
        verify(dsAssistantAcl).getAssistantByAccount("app1", "account1");
        verify(addContactApplicationTService).mobileAddFriendRealTime(any());
    }

    /**
     * 测试输入参数为null - 抛出异常
     */
    @Test
    public void testDsMobileAddFriendRealTime_NullInput() throws Throwable {
        // act & assert
        assertThrows(NullPointerException.class, () -> dsAddFriendAcl.dsMobileAddFriendRealTime(null));
        verifyNoInteractions(appConfigRepository, dsAssistantAcl, addContactApplicationTService);
    }

    /**
     * 测试配置获取异常 - 抛出异常
     */
    @Test
    public void testDsMobileAddFriendRealTime_ConfigException() throws Throwable {
        // arrange
        MobileAddFriendRealTimeEntity entity = new MobileAddFriendRealTimeEntity();
        entity.setAppId("app1");
        entity.setAccountId("account1");
        when(appConfigRepository.getConfigByAppId("app1")).thenThrow(new RuntimeException("Config error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> dsAddFriendAcl.dsMobileAddFriendRealTime(entity));
        verify(appConfigRepository).getConfigByAppId("app1");
        verifyNoInteractions(dsAssistantAcl, addContactApplicationTService);
    }

    /**
     * 测试助手信息获取异常 - 抛出异常
     */
    @Test
    public void testDsMobileAddFriendRealTime_AssistantException() throws Throwable {
        // arrange
        MobileAddFriendRealTimeEntity entity = new MobileAddFriendRealTimeEntity();
        entity.setAppId("app1");
        entity.setAccountId("account1");
        CorpAppConfig config = new CorpAppConfig();
        config.setBusinessCode("business1");
        when(appConfigRepository.getConfigByAppId("app1")).thenReturn(config);
        when(dsAssistantAcl.getAssistantByAccount("app1", "account1")).thenThrow(new RuntimeException("Assistant error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> dsAddFriendAcl.dsMobileAddFriendRealTime(entity));
        verify(appConfigRepository).getConfigByAppId("app1");
        verify(dsAssistantAcl).getAssistantByAccount("app1", "account1");
        verifyNoInteractions(addContactApplicationTService);
    }

    /**
     * 测试远程服务调用异常 - 抛出异常
     */
    @Test
    public void testDsMobileAddFriendRealTime_ServiceException() throws Throwable {
        // arrange
        MobileAddFriendRealTimeEntity entity = new MobileAddFriendRealTimeEntity();
        entity.setAppId("app1");
        entity.setAccountId("account1");
        entity.setAddNumber("***********");
        entity.setNumberType(1);
        entity.setWelcomeContent("welcome");
        CorpAppConfig config = new CorpAppConfig();
        config.setBusinessCode("business1");
        when(appConfigRepository.getConfigByAppId("app1")).thenReturn(config);
        AssistantInfo assistantInfo = new AssistantInfo();
        assistantInfo.setAssistantId(1001L);
        when(dsAssistantAcl.getAssistantByAccount("app1", "account1")).thenReturn(assistantInfo);
        when(addContactApplicationTService.mobileAddFriendRealTime(any())).thenThrow(new RuntimeException("Service error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> dsAddFriendAcl.dsMobileAddFriendRealTime(entity));
        verify(appConfigRepository).getConfigByAppId("app1");
        verify(dsAssistantAcl).getAssistantByAccount("app1", "account1");
        verify(addContactApplicationTService).mobileAddFriendRealTime(any());
    }
}
