package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowActionTrackDTO;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.scrm.core.service.aigc.service.domainservice.ScrmFridayIntelligentFollowDomainService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;

@ExtendWith(MockitoExtension.class)
public class GroupRetailAiEngineFlowLimitConsumerRecvMessageTest {

    @Mock
    private ScrmFridayIntelligentFollowDomainService fridayIntelligentFollowDomainService;

    @InjectMocks
    private GroupRetailAiEngineFlowLimitConsumer consumer;

    private static final Logger log = LoggerFactory.getLogger(GroupRetailAiEngineFlowLimitConsumerRecvMessageTest.class);

    /**
     * Test when message body is blank
     */
    @Test
    public void testRecvMessage_WhenBodyIsBlank_ThenReturnSuccess() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("");
        MessagetContext context = mock(MessagetContext.class);
        // act
        ConsumeStatus result = consumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        // Only called once for blank check
        verify(message, times(1)).getBody();
    }

    /**
     * Test when JSON parsing fails
     */
    @Test
    public void testRecvMessage_WhenJsonParsingFails_ThenReturnSuccessAndLogError() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("invalid json");
        MessagetContext context = mock(MessagetContext.class);
        // Mock JsonUtils to throw exception
        try (MockedStatic<JsonUtils> mockedJsonUtils = mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.toObjectSafe("invalid json", IntelligentFollowActionTrackDTO.class)).thenThrow(new RuntimeException("JSON parsing error"));
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            // Called twice - once for blank check, once for parsing
            verify(message, times(2)).getBody();
        }
    }

    /**
     * Test when parsed DTO is null
     */
    @Test
    public void testRecvMessage_WhenParsedDtoIsNull_ThenReturnSuccess() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("{}");
        MessagetContext context = mock(MessagetContext.class);
        try (MockedStatic<JsonUtils> mockedJsonUtils = mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.toObjectSafe("{}", IntelligentFollowActionTrackDTO.class)).thenReturn(null);
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            // Called twice - once for blank check, once for parsing
            verify(message, times(2)).getBody();
        }
    }

    /**
     * Test successful service call
     */
    @Test
    public void testRecvMessage_WhenServiceCallSucceeds_ThenReturnSuccess() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("{\"appId\":\"test\",\"userId\":123}");
        MessagetContext context = mock(MessagetContext.class);
        IntelligentFollowActionTrackDTO trackDTO = new IntelligentFollowActionTrackDTO();
        IntelligentFollowResultDTO resultDTO = new IntelligentFollowResultDTO();
        try (MockedStatic<JsonUtils> mockedJsonUtils = mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.toObjectSafe("{\"appId\":\"test\",\"userId\":123}", IntelligentFollowActionTrackDTO.class)).thenReturn(trackDTO);
            mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("result");
            when(fridayIntelligentFollowDomainService.queryIntelligentFollowResult(trackDTO)).thenReturn(resultDTO);
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(fridayIntelligentFollowDomainService).queryIntelligentFollowResult(trackDTO);
            // Called twice - once for blank check, once for parsing
            verify(message, times(2)).getBody();
        }
    }

    /**
     * Test when service throws exception
     */
    @Test
    public void testRecvMessage_WhenServiceThrowsException_ThenReturnSuccessAndLogError() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("{\"appId\":\"test\",\"userId\":123}");
        MessagetContext context = mock(MessagetContext.class);
        IntelligentFollowActionTrackDTO trackDTO = new IntelligentFollowActionTrackDTO();
        try (MockedStatic<JsonUtils> mockedJsonUtils = mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.toObjectSafe("{\"appId\":\"test\",\"userId\":123}", IntelligentFollowActionTrackDTO.class)).thenReturn(trackDTO);
            mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("message content");
            when(fridayIntelligentFollowDomainService.queryIntelligentFollowResult(trackDTO)).thenThrow(new RuntimeException("Service error"));
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(fridayIntelligentFollowDomainService).queryIntelligentFollowResult(trackDTO);
            // Called twice - once for blank check, once for parsing
            verify(message, times(2)).getBody();
        }
    }
}
