package com.sankuai.scrm.core.service.couponIntegration.mq.consumer;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.couponIntegration.domain.CouponConsumeRecordService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class ScrmCouponConsumeRecordConsumerRecvMessageTest {

    @InjectMocks
    private ScrmCouponConsumeRecordConsumer scrmCouponConsumeRecordConsumer;

    @Mock
    private CouponConsumeRecordService couponConsumeRecordService;

    @Mock
    private IConsumerProcessor consumer;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testRecvMessageNormal() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext messagetContext = mock(MessagetContext.class);
        when(message.getBody()).thenReturn("test message");
        // act
        ConsumeStatus result = scrmCouponConsumeRecordConsumer.recvMessage(message, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(couponConsumeRecordService, times(1)).handleCouponConsumeRecordMsg("test message");
    }

    /**
     * 测试消息体为空的情况
     */
    @Test
    public void testRecvMessageEmptyBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext messagetContext = mock(MessagetContext.class);
        when(message.getBody()).thenReturn(null);
        // act
        ConsumeStatus result = scrmCouponConsumeRecordConsumer.recvMessage(message, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(couponConsumeRecordService, times(1)).handleCouponConsumeRecordMsg(null);
    }

    /**
     * 测试处理消息时抛出异常的情况
     */
    @Test
    public void testRecvMessageException() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext messagetContext = mock(MessagetContext.class);
        when(message.getBody()).thenReturn("test message");
        doThrow(new RuntimeException("Test exception")).when(couponConsumeRecordService).handleCouponConsumeRecordMsg("test message");
        // act
        ConsumeStatus result = scrmCouponConsumeRecordConsumer.recvMessage(message, messagetContext);
        // assert
        assertEquals(ConsumeStatus.RECONSUME_LATER, result);
        verify(couponConsumeRecordService, times(1)).handleCouponConsumeRecordMsg("test message");
    }
}
