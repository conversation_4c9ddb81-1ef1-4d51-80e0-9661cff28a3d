package com.sankuai.scrm.core.service.automatedmanagement.domainservice.activitypage;

import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductActivityPageDOMapper;
import com.sankuai.dz.srcm.automatedmanagement.request.activitypage.PagingQueryActivityPagetRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.activitypage.PagingQueryActivityPagetResultVO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ActivityPageManagementService_PagingQueryActivityPageTest {

    @InjectMocks
    private ActivityPageManagementService activityPageManagementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductActivityPageDOMapper activityPageDOMapper;

    private PagingQueryActivityPagetRequest validRequest;

    private ScrmAmProcessOrchestrationProductActivityPageDO validActivityPageDO;

    @Before
    public void setUp() {
        validRequest = new PagingQueryActivityPagetRequest();
        validRequest.setPageNum(1);
        validRequest.setPageSize(10);
        validRequest.setAppId("testAppId");
        validActivityPageDO = new ScrmAmProcessOrchestrationProductActivityPageDO();
        validActivityPageDO.setId(1L);
        validActivityPageDO.setAppId("testAppId");
        validActivityPageDO.setActivityTitle("testActivityTitle");
        validActivityPageDO.setMiniProgramTitle("testMiniProgramTitle");
        validActivityPageDO.setThumbPicUrl("testThumbPicUrl");
        validActivityPageDO.setBackGroundPicUrl("testBackGroundPicUrl");
        validActivityPageDO.setMiniProgramAppId("testMiniProgramAppId");
        validActivityPageDO.setMiniProgramOriginAppId("testMiniProgramOriginAppId");
        validActivityPageDO.setRelatedProductIds("1,2,3");
        validActivityPageDO.setAddTime(new Date());
    }

    @Test
    public void testPagingQueryActivityPageAppIdIsNull() throws Throwable {
        validRequest.setAppId(null);
        List<PagingQueryActivityPagetResultVO> result = activityPageManagementService.pagingQueryActivityPage(validRequest);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testPagingQueryActivityPageActivityPageDescIsNull() throws Throwable {
        validRequest.setActivityPageDesc(null);
        List<ScrmAmProcessOrchestrationProductActivityPageDO> activityPageDOs = new ArrayList<>();
        activityPageDOs.add(validActivityPageDO);
        when(activityPageDOMapper.selectByExample(any())).thenReturn(activityPageDOs);
        List<PagingQueryActivityPagetResultVO> result = activityPageManagementService.pagingQueryActivityPage(validRequest);
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }

    @Test
    public void testPagingQueryActivityPageActivityPageDOsIsEmpty() throws Throwable {
        when(activityPageDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        List<PagingQueryActivityPagetResultVO> result = activityPageManagementService.pagingQueryActivityPage(validRequest);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testPagingQueryActivityPageActivityPageDOsIsNotEmpty() throws Throwable {
        List<ScrmAmProcessOrchestrationProductActivityPageDO> activityPageDOs = new ArrayList<>();
        activityPageDOs.add(validActivityPageDO);
        when(activityPageDOMapper.selectByExample(any())).thenReturn(activityPageDOs);
        List<PagingQueryActivityPagetResultVO> result = activityPageManagementService.pagingQueryActivityPage(validRequest);
        assertNotNull("Result should not be null", result);
        assertFalse("Result should not be empty", result.isEmpty());
        assertEquals("Result should contain one item", 1, result.size());
        PagingQueryActivityPagetResultVO resultItem = result.get(0);
        assertEquals("testThumbPicUrl", resultItem.getProgCover());
        assertEquals("testMiniProgramTitle", resultItem.getProgTitle());
        assertEquals(Arrays.asList(1L, 2L, 3L), resultItem.getProductIds());
        assertEquals("testBackGroundPicUrl", resultItem.getActivityPageImg());
        assertEquals("testActivityTitle", resultItem.getActivityPageTitle());
        assertEquals("testMiniProgramOriginAppId", resultItem.getMiniProgramOriginAppId());
        assertEquals("testMiniProgramAppId", resultItem.getMiniProgramAppId());
        assertEquals(Long.valueOf(1L), resultItem.getActivityPageId());
        assertNotNull(resultItem.getAddTime());
    }

    @Test
    public void testPagingQueryActivityPageWithActivityPageDesc() throws Throwable {
        validRequest.setActivityPageDesc("test");
        List<ScrmAmProcessOrchestrationProductActivityPageDO> activityPageDOs = new ArrayList<>();
        activityPageDOs.add(validActivityPageDO);
        when(activityPageDOMapper.selectByExample(any())).thenReturn(activityPageDOs);
        List<PagingQueryActivityPagetResultVO> result = activityPageManagementService.pagingQueryActivityPage(validRequest);
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
    }

    @Test
    public void testPagingQueryActivityPageWithMultipleResults() throws Throwable {
        List<ScrmAmProcessOrchestrationProductActivityPageDO> activityPageDOs = new ArrayList<>();
        activityPageDOs.add(validActivityPageDO);
        ScrmAmProcessOrchestrationProductActivityPageDO secondActivityPageDO = new ScrmAmProcessOrchestrationProductActivityPageDO();
        secondActivityPageDO.setId(2L);
        secondActivityPageDO.setAppId("testAppId");
        secondActivityPageDO.setActivityTitle("secondActivityTitle");
        secondActivityPageDO.setMiniProgramTitle("secondMiniProgramTitle");
        secondActivityPageDO.setThumbPicUrl("secondThumbPicUrl");
        secondActivityPageDO.setBackGroundPicUrl("secondBackGroundPicUrl");
        secondActivityPageDO.setMiniProgramAppId("secondMiniProgramAppId");
        secondActivityPageDO.setMiniProgramOriginAppId("secondMiniProgramOriginAppId");
        secondActivityPageDO.setRelatedProductIds("4,5,6");
        secondActivityPageDO.setAddTime(new Date());
        activityPageDOs.add(secondActivityPageDO);
        when(activityPageDOMapper.selectByExample(any())).thenReturn(activityPageDOs);
        List<PagingQueryActivityPagetResultVO> result = activityPageManagementService.pagingQueryActivityPage(validRequest);
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals("testActivityTitle", result.get(0).getActivityPageTitle());
        assertEquals("secondActivityTitle", result.get(1).getActivityPageTitle());
    }

    @Test
    public void testPagingQueryActivityPageWithPagination() throws Throwable {
        validRequest.setPageNum(2);
        validRequest.setPageSize(1);
        List<ScrmAmProcessOrchestrationProductActivityPageDO> activityPageDOs = new ArrayList<>();
        activityPageDOs.add(validActivityPageDO);
        when(activityPageDOMapper.selectByExample(any())).thenReturn(activityPageDOs);
        List<PagingQueryActivityPagetResultVO> result = activityPageManagementService.pagingQueryActivityPage(validRequest);
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
    }

    @Test
    public void testPagingQueryActivityPageExampleIsNull() throws Throwable {
        PagingQueryActivityPagetRequest request = new PagingQueryActivityPagetRequest();
        List<ScrmAmProcessOrchestrationProductActivityPageDO> activityPageDOs = new ArrayList<>();
        List<PagingQueryActivityPagetResultVO> result = activityPageManagementService.pagingQueryActivityPage(request);
        assertEquals(null, result);
    }
}
