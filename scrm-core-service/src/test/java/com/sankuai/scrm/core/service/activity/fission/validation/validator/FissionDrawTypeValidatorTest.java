package com.sankuai.scrm.core.service.activity.fission.validation.validator;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.activity.fission.enums.PrizeTypeEnum;
import com.sankuai.dz.srcm.activity.fission.request.GroupFissionActivityRequest;
import com.sankuai.dz.srcm.activity.fission.request.RewardInfoRequest;
import com.sankuai.scrm.core.service.activity.fission.validation.exception.FissionValidatorException;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FissionDrawTypeValidatorTest {

    private final FissionDrawTypeValidator validator = new FissionDrawTypeValidator();

    /**
     * 测试空奖励列表应该抛出异常，因为概率总和不为100%
     */
    @Test
    public void testValidateEmptyRewardListShouldThrowException() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setRewardInfo(Collections.emptyList());
        // act & assert
        FissionValidatorException exception = assertThrows(FissionValidatorException.class, () -> validator.validate(request, "operation"));
        assertEquals("抽奖裂变活动下中奖概率需为1", exception.getMessage());
    }

    /**
     * 测试中奖概率为空应该抛出异常
     */
    @Test
    public void testValidateBlankPrizeProbabilityShouldThrowException() throws Throwable {
        // arrange
        RewardInfoRequest reward = new RewardInfoRequest();
        reward.setPrizeProbability(null);
        reward.setRewardType(PrizeTypeEnum.CASH.getCode());
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setRewardInfo(Collections.singletonList(reward));
        // act & assert
        FissionValidatorException exception = assertThrows(FissionValidatorException.class, () -> validator.validate(request, "operation"));
        assertEquals("抽奖裂变活动下中奖概率不能为空", exception.getMessage());
    }

    /**
     * 测试优惠券类型但优惠券ID为空应该抛出异常
     */
    @Test
    public void testValidateCouponTypeWithBlankCouponIdShouldThrowException() throws Throwable {
        // arrange
        RewardInfoRequest reward = new RewardInfoRequest();
        reward.setPrizeProbability("100%");
        reward.setRewardType(PrizeTypeEnum.COUPON.getCode());
        reward.setCouponId(null);
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setRewardInfo(Collections.singletonList(reward));
        // act & assert
        FissionValidatorException exception = assertThrows(FissionValidatorException.class, () -> validator.validate(request, "operation"));
        assertEquals("抽奖裂变活动中为优惠券奖品code不能为空", exception.getMessage());
    }

    /**
     * 测试实物类型但兑换链接为空应该抛出异常
     */
    @Test
    public void testValidateGoodsTypeWithBlankAddressUrlShouldThrowException() throws Throwable {
        // arrange
        RewardInfoRequest reward = new RewardInfoRequest();
        reward.setPrizeProbability("100%");
        reward.setRewardType(PrizeTypeEnum.GOODS.getCode());
        reward.setAddressUrl(null);
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setRewardInfo(Collections.singletonList(reward));
        // act & assert
        FissionValidatorException exception = assertThrows(FissionValidatorException.class, () -> validator.validate(request, "operation"));
        assertEquals("抽奖裂变活动中为实物奖品兑换链接不能为空", exception.getMessage());
    }

    /**
     * 测试中奖概率总和不为100%应该抛出异常
     */
    @Test
    public void testValidateTotalProbabilityNot100ShouldThrowException() throws Throwable {
        // arrange
        RewardInfoRequest reward1 = new RewardInfoRequest();
        reward1.setPrizeProbability("50%");
        reward1.setRewardType(PrizeTypeEnum.CASH.getCode());
        RewardInfoRequest reward2 = new RewardInfoRequest();
        reward2.setPrizeProbability("40%");
        reward2.setRewardType(PrizeTypeEnum.CASH.getCode());
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setRewardInfo(Arrays.asList(reward1, reward2));
        // act & assert
        FissionValidatorException exception = assertThrows(FissionValidatorException.class, () -> validator.validate(request, "operation"));
        assertEquals("抽奖裂变活动下中奖概率需为1", exception.getMessage());
    }

    /**
     * 测试多种奖励类型混合且概率总和为100%应该通过验证
     */
    @Test
    public void testValidateMixedRewardTypesWithTotal100ShouldPass() throws Throwable {
        // arrange
        RewardInfoRequest couponReward = new RewardInfoRequest();
        couponReward.setPrizeProbability("30%");
        couponReward.setRewardType(PrizeTypeEnum.COUPON.getCode());
        couponReward.setCouponId("coupon123");
        RewardInfoRequest goodsReward = new RewardInfoRequest();
        goodsReward.setPrizeProbability("40%");
        goodsReward.setRewardType(PrizeTypeEnum.GOODS.getCode());
        goodsReward.setAddressUrl("http://example.com");
        RewardInfoRequest cashReward = new RewardInfoRequest();
        cashReward.setPrizeProbability("30%");
        cashReward.setRewardType(PrizeTypeEnum.CASH.getCode());
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setRewardInfo(Arrays.asList(couponReward, goodsReward, cashReward));
        // act & assert
        assertDoesNotThrow(() -> validator.validate(request, "operation"));
    }

    /**
     * 测试概率总和正好100%边界情况应该通过验证
     */
    @Test
    public void testValidateExactly100ProbabilityShouldPass() throws Throwable {
        // arrange
        RewardInfoRequest reward = new RewardInfoRequest();
        reward.setPrizeProbability("100%");
        reward.setRewardType(PrizeTypeEnum.CASH.getCode());
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setRewardInfo(Collections.singletonList(reward));
        // act & assert
        assertDoesNotThrow(() -> validator.validate(request, "operation"));
    }

    /**
     * 测试非现金类型奖励的概率总和为100%应该通过验证
     */
    @Test
    public void testValidateNonCashRewardsWithTotal100ShouldPass() throws Throwable {
        // arrange
        RewardInfoRequest couponReward = new RewardInfoRequest();
        couponReward.setPrizeProbability("60%");
        couponReward.setRewardType(PrizeTypeEnum.COUPON.getCode());
        couponReward.setCouponId("coupon123");
        RewardInfoRequest goodsReward = new RewardInfoRequest();
        goodsReward.setPrizeProbability("40%");
        goodsReward.setRewardType(PrizeTypeEnum.GOODS.getCode());
        goodsReward.setAddressUrl("http://example.com");
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setRewardInfo(Arrays.asList(couponReward, goodsReward));
        // act & assert
        assertDoesNotThrow(() -> validator.validate(request, "operation"));
    }
}
