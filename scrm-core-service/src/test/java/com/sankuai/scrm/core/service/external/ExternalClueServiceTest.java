package com.sankuai.scrm.core.service.external;

import com.google.common.collect.Lists;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.external.contact.dto.ContactUserDTO;
import com.sankuai.dz.srcm.external.contact.request.FriendAdditionRecordRequest;
import com.sankuai.dz.srcm.external.contact.response.FriendAdditionRecordResponse;
import com.sankuai.scrm.core.service.BaseMockTest;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.service.impl.ScrmAutoManagementActivityPageServiceImpl;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ExternalContactBaseInfo;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.external.contact.domain.ExternalContactBaseInfoDomainService;
import com.sankuai.scrm.core.service.external.contact.service.ExternalClueServiceImpl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxContactAcl;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.util.MobileTokenUtil;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.beans.factory.annotation.Autowired;

import java.text.ParseException;
import java.text.SimpleDateFormat;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

/**
 * 功能描述：
 *
 * <AUTHOR>
 * @version 1.0 2024-2024/11/11-22:01
 * @description: TODO
 * @since 1.0
 */
public class ExternalClueServiceTest extends BaseMockTest {

    @Mock
    private ContactUserDomain contactUserDomain;
    @InjectMocks
    private ExternalClueServiceImpl externalClueService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private FriendAdditionRecordRequest createNickNameRequest() {
        FriendAdditionRecordRequest request = new FriendAdditionRecordRequest();
        request.setCorpId("ww07ad5f7a1a72d5be");
        request.setNickName("张三");
        request.setStaffId("13262481095");
        request.setAddTime(1730649600000L);
        return request;
    }

    private List<ContactUser> createContactUser_zyk() {
        ContactUser contactUser = new ContactUser();
        contactUser.setExternalUserId("oNQu9t63bVQ1HnpDM2UGDXGZxKKY");
        contactUser.setUnionId("oNQu9t8CJ9JYNy3CDpTzdio81F2M");
        contactUser.setStaffId("13262481095");
        contactUser.setAddTime(new Date(1730649600000L));
        return Collections.singletonList(contactUser);
    }

    @Mock
    private MobileTokenUtil mobileTokenUtil;

    @Mock
    private ExternalContactBaseInfoDomainService baseInfoDomainService;

    /**
     * 测试通过用户昵称查询，有添加记录且为用户添加咨询师
     */
    @Test
    public void testQueryFriendAdditionRecords_通过用户昵称查询_有添加记录且为用户添加咨询师() throws Throwable {

        List<ExternalContactBaseInfo> mobileInfos = Lists.newArrayList();
        ExternalContactBaseInfo build = ExternalContactBaseInfo.builder().unionId("oNQu9t8CJ9JYNy3CDpTzdio81F2M").name("张三").mobileNo("D9XB4W8OJtK7227").build();
        mobileInfos.add(build);
        // arrange
        when(baseInfoDomainService.queryBaseInfoByName(anyString())).thenReturn(mobileInfos);
        when(contactUserDomain.queryContactUserByUnionIdAndStaffId(anyString(), anyList(), anyString(), any(), any())).
                thenReturn(createContactUser_zyk());
        when(mobileTokenUtil.getMobileByToken(anyString())).thenReturn("17610277227");

        // act
        RemoteResponse<FriendAdditionRecordResponse> result = externalClueService.queryFriendAdditionRecords(createNickNameRequest());

        // assert
        assertEquals("success", result.getMsg());
        assertEquals(1, result.getData().getContactUserResultVOList().size());
        ContactUserDTO actualContactUserDTO = result.getData().getContactUserResultVOList().get(0);
        assertEquals("张三", actualContactUserDTO.getName());
    }
}
