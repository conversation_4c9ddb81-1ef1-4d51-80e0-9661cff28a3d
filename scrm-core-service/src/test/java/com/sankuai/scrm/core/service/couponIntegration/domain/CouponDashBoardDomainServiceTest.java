package com.sankuai.scrm.core.service.couponIntegration.domain;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.couponIntegration.dto.CouponDataDetailDTO;
import com.sankuai.dz.srcm.couponIntegration.dto.CouponDataSummaryDTO;
import com.sankuai.dz.srcm.couponIntegration.request.CouponDataDetailDownloadRequest;
import com.sankuai.dz.srcm.couponIntegration.request.CouponDataDetailQueryRequest;
import com.sankuai.dz.srcm.couponIntegration.request.CouponDataSummaryDownloadRequest;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmCouponDataSummaryDOMapper;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.dashboard.domain.Utils;
import com.sankuai.scrm.core.service.infrastructure.acl.sso.SsosvOpenApi;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CouponDashBoardDomainServiceTest {

    @InjectMocks
    private CouponDashBoardDomainService couponDashBoardDomainService;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private ScrmSceneCouponRecordsMapper sceneCouponRecordDOMapper;

    @Mock
    private ScrmCouponDataSummaryDOMapper couponDataSummaryDOMapper;

    @Mock
    private SsosvOpenApi ssosvOpenApi;

    @Mock
    private Utils utils;

    @Before
    public void setUp() {
        when(corpAppConfigRepository.getAppIdByCorpId(anyString())).thenReturn("appId");
        when(sceneCouponRecordDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
    }

    /**
     * 测试 corpId 为空的情况
     */
//    @Test
//    public void testDailyCouponDataSummaryCorpIdIsNull() {
//        // arrange
//        String corpId = null;
//        // act
//        couponDashBoardDomainService.dailyCouponDataSummary(corpId);
//        // assert
//        verify(corpAppConfigRepository, never()).getAppIdByCorpId(anyString());
//    }

    /**
     * 测试 corpId 不为空，但 appId 为空的情况
     */
//    @Test
//    public void testDailyCouponDataSummaryAppIdIsNull() {
//        // arrange
//        String corpId = "corpId";
//        when(corpAppConfigRepository.getAppIdByCorpId(corpId)).thenReturn(null);
//        // act
//        couponDashBoardDomainService.dailyCouponDataSummary(corpId);
//        // assert
//        verify(sceneCouponRecordDOMapper, never()).selectByExample(any());
//        verify(couponDataSummaryDOMapper, never()).insertSelective(any(ScrmCouponDataSummaryDO.class));
//    }

    /**
     * 测试 corpId 和 appId 都不为空，但查询 ScrmSceneCouponRecordDO 对象列表为空的情况
     */
//    @Test
//    public void testDailyCouponDataSummaryCouponRecordDOListIsEmpty() {
//        // arrange
//        String corpId = "corpId";
//        when(sceneCouponRecordDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
//        // act
//        couponDashBoardDomainService.dailyCouponDataSummary(corpId);
//        // assert
//        verify(couponDataSummaryDOMapper, times(2)).insertSelective(any(ScrmCouponDataSummaryDO.class));
//    }

    /**
     * 测试 corpId、appId 和查询 ScrmSceneCouponRecordDO 对象列表都不为空的情况
     */
//    @Test
//    public void testDailyCouponDataSummaryNormal() {
//        // arrange
//        String corpId = "corpId";
//        ScrmSceneCouponRecordDO scrmSceneCouponRecordDO = new ScrmSceneCouponRecordDO();
//        scrmSceneCouponRecordDO.setCouponamount(new BigDecimal("100"));
//        when(sceneCouponRecordDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(scrmSceneCouponRecordDO));
//        // act
//        couponDashBoardDomainService.dailyCouponDataSummary(corpId);
//        // assert
//        verify(couponDataSummaryDOMapper, times(2)).insertSelective(any(ScrmCouponDataSummaryDO.class));
//    }

    @Test
    public void testCountDetailByRequestWhenRequestIsNull() throws Throwable {
        Long result = couponDashBoardDomainService.countDetailByRequest(null);
        assertEquals(Long.valueOf(0), result);
    }

    @Test
    public void testCountDetailByRequestWhenPageSizeIsNull() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageNumber(1);
        Long result = couponDashBoardDomainService.countDetailByRequest(request);
        assertEquals(Long.valueOf(0), result);
    }

    @Test
    public void testCountDetailByRequestWhenPageSizeIsLessThanOrEqualToZero() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(0);
        request.setPageNumber(1);
        Long result = couponDashBoardDomainService.countDetailByRequest(request);
        assertEquals(Long.valueOf(0), result);
    }

    @Test
    public void testCountDetailByRequestWhenPageNumberIsNull() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(10);
        Long result = couponDashBoardDomainService.countDetailByRequest(request);
        assertEquals(Long.valueOf(0), result);
    }

    @Test
    public void testCountDetailByRequestWhenPageNumberIsLessThanOrEqualToZero() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(10);
        request.setPageNumber(0);
        Long result = couponDashBoardDomainService.countDetailByRequest(request);
        assertEquals(Long.valueOf(0), result);
    }

    @Test
    public void testCountDetailByRequestWhenCountByExampleReturnsZero() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(10);
        request.setPageNumber(1);
        when(sceneCouponRecordDOMapper.countByExample(any())).thenReturn(0L);
        Long result = couponDashBoardDomainService.countDetailByRequest(request);
        assertEquals(Long.valueOf(0), result);
    }

    @Test
    public void testCountDetailByRequestWhenCountByExampleReturnsNonZero() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(10);
        request.setPageNumber(1);
        when(sceneCouponRecordDOMapper.countByExample(any())).thenReturn(10L);
        Long result = couponDashBoardDomainService.countDetailByRequest(request);
        assertEquals(Long.valueOf(10), result);
    }

    @Test
    public void testGetAuthenticationedAppIdsWhenAccessTokenIsInvalid() throws Throwable {
        String appId = "appId";
        String accessToken = "invalidToken";
        when(ssosvOpenApi.queryLoginNameByAccessToken(accessToken)).thenReturn(null);
        List<String> result = couponDashBoardDomainService.getAuthenticationedAppIds(appId, accessToken);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetAuthenticationedAppIdsWhenAuthenticationReturnsEmptyList() throws Throwable {
        String appId = "appId";
        String accessToken = "validToken";
        when(ssosvOpenApi.queryLoginNameByAccessToken(accessToken)).thenReturn("misId");
        when(utils.authentication(anyString(), anyString())).thenReturn(Collections.emptyList());
        List<String> result = couponDashBoardDomainService.getAuthenticationedAppIds(appId, accessToken);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetAuthenticationedAppIdsWhenAuthenticationReturnsNonEmptyListButGetAppIdByCorpIdReturnsNull() throws Throwable {
        String appId = "appId";
        String accessToken = "validToken";
        List<String> authenticationedCorpIds = Arrays.asList("corpId1", "corpId2");
        when(ssosvOpenApi.queryLoginNameByAccessToken(accessToken)).thenReturn("misId");
        when(utils.authentication(anyString(), anyString())).thenReturn(authenticationedCorpIds);
        when(corpAppConfigRepository.getAppIdByCorpId(anyString())).thenReturn(null);
        List<String> result = couponDashBoardDomainService.getAuthenticationedAppIds(appId, accessToken);
        // Adjusted expectation
        assertEquals(Arrays.asList(null, null), result);
    }

    @Test
    public void testGetAuthenticationedAppIdsWhenAuthenticationReturnsNonEmptyListAndGetAppIdByCorpIdReturnsNonNull() throws Throwable {
        String appId = "appId";
        String accessToken = "validToken";
        List<String> authenticationedCorpIds = Arrays.asList("corpId1", "corpId2");
        when(ssosvOpenApi.queryLoginNameByAccessToken(accessToken)).thenReturn("misId");
        when(utils.authentication(anyString(), anyString())).thenReturn(authenticationedCorpIds);
        when(corpAppConfigRepository.getAppIdByCorpId(anyString())).thenReturn("appId1", "appId2");
        List<String> result = couponDashBoardDomainService.getAuthenticationedAppIds(appId, accessToken);
        assertEquals(Arrays.asList("appId1", "appId2"), result);
    }

    /**
     * 测试 request 为 null 的情况
     */
    @Test
    public void testQueryDetailByRequestRequestIsNull() throws Throwable {
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest((CouponDataDetailDownloadRequest) null);
        assertEquals(new ArrayList<>(), result);
    }

    /**
     * 测试 request 不为 null，但查询结果为空的情况
     */
    @Test
    public void testQueryDetailByRequestResultIsEmpty() throws Throwable {
        CouponDataDetailDownloadRequest request = new CouponDataDetailDownloadRequest();
        request.setAccessToken("mockAccessToken");
        request.setPathAppId("mockPathAppId");
        when(sceneCouponRecordDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(ssosvOpenApi.queryLoginNameByAccessToken(anyString())).thenReturn("mockMisId");
        when(utils.authentication(anyString(), anyString())).thenReturn(new ArrayList<>());
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        assertEquals(new ArrayList<>(), result);
    }

    /**
     * 测试 request 不为 null，查询结果不为空的情况
     */
    @Test
    public void testQueryDetailByRequestResultIsNotEmpty() throws Throwable {
        CouponDataDetailDownloadRequest request = new CouponDataDetailDownloadRequest();
        request.setAccessToken("mockAccessToken");
        request.setPathAppId("mockPathAppId");
        List<ScrmSceneCouponRecords> scrmSceneCouponRecordDOS = new ArrayList<>();
        ScrmSceneCouponRecords recordDO = new ScrmSceneCouponRecords();
        // 设置 scenetype 为非 null 值
        recordDO.setScenetype(1);
        // 设置 couponamount 为非 null 值
        recordDO.setCouponamount(new BigDecimal("100.00"));
        scrmSceneCouponRecordDOS.add(recordDO);
        when(sceneCouponRecordDOMapper.selectByExample(any())).thenReturn(scrmSceneCouponRecordDOS);
        when(ssosvOpenApi.queryLoginNameByAccessToken(anyString())).thenReturn("mockMisId");
        when(utils.authentication(anyString(), anyString())).thenReturn(new ArrayList<>());
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        assertEquals(scrmSceneCouponRecordDOS.size(), result.size());
    }

    @Test
    public void testQueryDetailByRequestWhenRequestIsNull() throws Throwable {
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest((CouponDataDetailQueryRequest) null);
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryDetailByRequestWhenPageSizeIsZero() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(0);
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryDetailByRequestWhenPageNumberIsZero() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageNumber(0);
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryDetailByRequestWhenCouponRecordDOExampleIsNull() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryDetailByRequestWhenCouponRecordDOSIsEmpty() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryDetailByRequestWhenCouponRecordDOSIsNotEmpty() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(10);
        request.setPageNumber(1);
        request.setAccessToken("mockAccessToken");
        request.setPathAppId("mockPathAppId");
        List<ScrmSceneCouponRecords> couponRecordDOS = new ArrayList<>();
        ScrmSceneCouponRecords record = new ScrmSceneCouponRecords();
        record.setCouponamount(new BigDecimal("100"));
        // 设置 scenetype 为非 null 值
        record.setScenetype(1);
        couponRecordDOS.add(record);
        when(sceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(couponRecordDOS);
        when(ssosvOpenApi.queryLoginNameByAccessToken(any(String.class))).thenReturn("mockMisId");
        when(utils.authentication(any(String.class), any(String.class))).thenReturn(new ArrayList<>());
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        assertEquals(1, result.size());
    }

    @Test
    public void testQuerySummaryByRequestWhenRequestIsNull() throws Throwable {
        List<CouponDataSummaryDTO> result = couponDashBoardDomainService.querySummaryByRequest((CouponDataSummaryDownloadRequest) null);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testQuerySummaryByRequestWhenAppIdIsEmpty() throws Throwable {
        CouponDataSummaryDownloadRequest request = new CouponDataSummaryDownloadRequest();
        request.setAppId("");
        List<CouponDataSummaryDTO> result = couponDashBoardDomainService.querySummaryByRequest(request);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testQuerySummaryByRequestWhenCorpIdsIsEmpty() throws Throwable {
        CouponDataSummaryDownloadRequest request = new CouponDataSummaryDownloadRequest();
        request.setAppId("appId");
        request.setAccessToken("accessToken");
        List<CouponDataSummaryDTO> result = couponDashBoardDomainService.querySummaryByRequest(request);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testQuerySummaryByRequestWhenSuccess() throws Throwable {
        CouponDataSummaryDownloadRequest request = new CouponDataSummaryDownloadRequest();
        request.setAppId("appId");
        request.setAccessToken("accessToken");
        List<CouponDataSummaryDTO> result = couponDashBoardDomainService.querySummaryByRequest(request);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testQuerySummaryByRequestWhenCorpIdsIsNotEmpty() throws Throwable {
        CouponDataSummaryDownloadRequest request = new CouponDataSummaryDownloadRequest();
        request.setAppId("appId");
        request.setAccessToken("accessToken");
        // Mock the behavior of the Utils class to return a non-empty list of corp IDs
        List<CouponDataSummaryDTO> result = couponDashBoardDomainService.querySummaryByRequest(request);
        assertNotNull(result);
        // Since the private method is not mocked, the result should be empty
        assertTrue(result.isEmpty());
    }
}
