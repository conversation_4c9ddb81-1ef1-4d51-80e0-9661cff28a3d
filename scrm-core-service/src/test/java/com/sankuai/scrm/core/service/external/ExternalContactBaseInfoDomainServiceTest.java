package com.sankuai.scrm.core.service.external;

import com.sankuai.scrm.core.service.BaseMockTest;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ExternalContactBaseInfo;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ExternalContactBaseInfoMobileNo;
import com.sankuai.scrm.core.service.external.contact.dal.example.ExternalContactBaseInfoExample;
import com.sankuai.scrm.core.service.external.contact.dal.mapper.ExternalContactBaseInfoMapper;
import com.sankuai.scrm.core.service.external.contact.dal.mapper.ExternalContactBaseInfoMobileNoMapper;
import com.sankuai.scrm.core.service.external.contact.domain.ExternalContactBaseInfoDomainService;
import com.sankuai.scrm.core.service.external.contact.dal.example.ExternalContactBaseInfoMobileNoExample;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static groovy.util.GroovyTestCase.assertEquals;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;


/**
 * 功能描述：
 *
 * <AUTHOR>
 * @version 1.0 2024-2024/11/12-10:06
 * @description: TODO
 * @since 1.0
 */
public class ExternalContactBaseInfoDomainServiceTest extends BaseMockTest {

    @Mock
    private ExternalContactBaseInfoMapper externalContactBaseInfoMapper;

    @Mock
    private ExternalContactBaseInfoMobileNoMapper externalContactBaseInfoMobileNoMapper;

    @InjectMocks
    private ExternalContactBaseInfoDomainService service;

    private static final byte OFFLINE = 0;
    private static final byte ONLINE = 1;

    @Before
    public void setUp() {
        // 初始化操作，如有需要
    }

    /**
     * 测试 queryMobileInfoByMobileToken 方法，当 mobileToken 为空时
     */
    @Test
    public void testQueryMobileInfoByMobileTokenEmptyToken() {
        // arrange
        String mobileToken = "";

        // act
        List<ExternalContactBaseInfo> result = service.queryBaseInfoByMobileToken(mobileToken);

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 queryMobileInfoByMobileToken 方法，当 mobileToken 不为空，但未找到对应的 MobileNo 信息时
     */
    @Test
    public void testQueryMobileInfoByMobileTokenNotFound() {
        // arrange
        String mobileToken = "testToken";
        when(externalContactBaseInfoMobileNoMapper.selectByExample(any(ExternalContactBaseInfoMobileNoExample.class)))
                .thenReturn(Collections.emptyList());

        // act
        List<ExternalContactBaseInfo> result = service.queryBaseInfoByMobileToken(mobileToken);

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试 queryMobileInfoByMobileToken 方法，当 mobileToken 不为空，且找到对应的 MobileNo 信息时
     */
    @Test
    public void testQueryMobileInfoByMobileTokenSuccess() {
        // arrange
        String mobileToken = "testToken";
        List<ExternalContactBaseInfoMobileNo> mobileNos = Arrays.asList(
                new ExternalContactBaseInfoMobileNo(1L, 1L, "testToken", null, null, (byte) 1)
        );
        when(externalContactBaseInfoMobileNoMapper.selectByExample(any(ExternalContactBaseInfoMobileNoExample.class)))
                .thenReturn(mobileNos);
        List<ExternalContactBaseInfo> expectedBaseInfos = Arrays.asList(
                new ExternalContactBaseInfo(1L, "externalUserId", "unionId", 1L, "name", "avatar", "remark", "appId", null, null, "mobileNo")
        );
        when(externalContactBaseInfoMapper.selectByExample(any()))
                .thenReturn(expectedBaseInfos);

        // act
        List<ExternalContactBaseInfo> result = service.queryBaseInfoByMobileToken(mobileToken);

        // assert
        assertFalse(result.isEmpty());
        assertEquals(expectedBaseInfos, result);
    }

    /**
     * 测试 queryBaseInfoByName 方法，当 name 为空时
     */
    @Test
    public void testQueryBaseInfoByNameWhenNameIsEmpty() {
        // arrange
        String name = "";

        // act
        List<ExternalContactBaseInfo> result = service.queryBaseInfoByName(name);

        // assert
        assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试 queryBaseInfoByName 方法，当 name 为 null 时
     */
    @Test
    public void testQueryBaseInfoByNameWhenNameIsNull() {
        // arrange
        String name = null;

        // act
        List<ExternalContactBaseInfo> result = service.queryBaseInfoByName(name);

        // assert
        assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试 queryBaseInfoByName 方法，当 name 有效且数据库中有匹配项时
     */
    @Test
    public void testQueryBaseInfoByNameWhenNameIsValidAndMatchFound() {
        // arrange
        String name = "有效名称";
        ExternalContactBaseInfoExample example = new ExternalContactBaseInfoExample();
        example.createCriteria().andNameEqualTo(name);
        List<ExternalContactBaseInfo> expected = Arrays.asList(
                new ExternalContactBaseInfo(1L, "externalUserId", "unionId", 1L, name, "avatar", "remark", "appId", null, null, "mobileNo")
        );
        when(externalContactBaseInfoMapper.selectByExample(any(ExternalContactBaseInfoExample.class))).thenReturn(expected);

        // act
        List<ExternalContactBaseInfo> result = service.queryBaseInfoByName(name);

        // assert
        assertFalse("结果不应为空", result.isEmpty());
        assertEquals("结果应与预期匹配", expected, result);
    }

    /**
     * 测试 queryBaseInfoByName 方法，当 name 有效但数据库中无匹配项时
     */
    @Test
    public void testQueryBaseInfoByNameWhenNameIsValidAndNoMatchFound() {
        // arrange
        String name = "有效名称";
        when(externalContactBaseInfoMapper.selectByExample(any(ExternalContactBaseInfoExample.class))).thenReturn(Collections.emptyList());

        // act
        List<ExternalContactBaseInfo> result = service.queryBaseInfoByName(name);

        // assert
        assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试 updateMobile 方法，当 remarkMobiles 为空字符串时，不执行任何数据库操作
     */
    @Test
    public void testUpdateMobile_EmptyRemarkMobiles() {
        // arrange
        String remarkMobiles = "";
        Long baseInfoId = 1L;

        // act
        service.updateMobile(remarkMobiles, baseInfoId);

        // assert
        verify(externalContactBaseInfoMobileNoMapper, never()).selectByExample(any(ExternalContactBaseInfoMobileNoExample.class));
    }

    /**
     * 测试 updateMobile 方法，当 remarkMobiles 不为空，且数据库中无匹配数据时，应执行插入操作
     */
    @Test
    public void testUpdateMobile_ValidRemarkMobiles_InsertOperation() {
        // arrange
        String remarkMobiles = "12345678901";
        Long baseInfoId = 1L;
        when(externalContactBaseInfoMobileNoMapper.selectByExample(any(ExternalContactBaseInfoMobileNoExample.class))).thenReturn(Collections.emptyList());

        // act
        service.updateMobile(remarkMobiles, baseInfoId);

        // assert
        verify(externalContactBaseInfoMobileNoMapper).batchInsert(anyList());
        verify(externalContactBaseInfoMobileNoMapper,  never()).batchUpdate(anyList());
    }

    /**
     * 测试 updateMobile 方法，当 remarkMobiles 不为空，且数据库中有匹配数据需要更新状态时，应执行更新操作
     */
    @Test
    public void testUpdateMobile_ValidRemarkMobiles_UpdateOperation() {
        // arrange
        String remarkMobiles = "12345678901";
        Long baseInfoId = 1L;
        ExternalContactBaseInfoMobileNo existingMobileNo = new ExternalContactBaseInfoMobileNo(1L, baseInfoId, "12345678901", null, null, ONLINE);
        when(externalContactBaseInfoMobileNoMapper.selectByExample(any(ExternalContactBaseInfoMobileNoExample.class))).thenReturn(Collections.singletonList(existingMobileNo));

        // act
        service.updateMobile(remarkMobiles, baseInfoId);

        // assert
        verify(externalContactBaseInfoMobileNoMapper,  never()).batchInsert(anyList());
        verify(externalContactBaseInfoMobileNoMapper,  never()).batchUpdate(anyList());
    }

    /**
     * 测试 updateMobile 方法，当 remarkMobiles 包含多个手机号，且部分需要新增，部分需要更新状态时
     */
    @Test
    public void testUpdateMobile_MultipleRemarkMobiles_MixedOperations() {
        // arrange
        String remarkMobiles = "12345678901,12345678902";
        Long baseInfoId = 1L;
        ExternalContactBaseInfoMobileNo existingMobileNo = new ExternalContactBaseInfoMobileNo(1L, baseInfoId, "12345678901", null, null, ONLINE);
        when(externalContactBaseInfoMobileNoMapper.selectByExample(any(ExternalContactBaseInfoMobileNoExample.class))).thenReturn(Collections.singletonList(existingMobileNo));

        // act
        service.updateMobile(remarkMobiles, baseInfoId);

        // assert
        verify(externalContactBaseInfoMobileNoMapper).batchInsert(anyList());
        verify(externalContactBaseInfoMobileNoMapper,  never()).batchUpdate(anyList());
    }
}
