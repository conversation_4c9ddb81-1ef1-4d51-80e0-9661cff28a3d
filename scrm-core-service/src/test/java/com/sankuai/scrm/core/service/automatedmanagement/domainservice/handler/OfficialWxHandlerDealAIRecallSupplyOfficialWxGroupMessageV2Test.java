package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.GroupSendStrategy;
import com.sankuai.scrm.core.service.message.push.request.MsgPushRequest;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OfficialWxHandlerDealAIRecallSupplyOfficialWxGroupMessageV2Test {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private MsgUnifiedPushService msgUnifiedPushService;

    @Mock
    private GroupSendStrategy groupSendStrategy;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private List<String> executorId;

    private InvokeDetailKeyObject keyObject;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS;

    @Before
    public void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = mock(ScrmProcessOrchestrationNodeMediumDTO.class);
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setAppId("testAppId");
        processOrchestrationDTO.setValidVersion("1.0");
        executorId = Lists.newArrayList("executorId");
        keyObject = new InvokeDetailKeyObject("key", (byte) 1, (byte) 1, 1L);
        totalInvokeDetailDOS = new ArrayList<>();
    }

    /**
     * Test empty content where no content is available to send.
     */
    @Test
    public void testDealAIRecallSupplyOfficialWxGroupMessageV2EmptyContent() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setProcessOrchestrationNodeId(1L);
        detailDO.setGroupId("groupId");
        totalInvokeDetailDOS.add(detailDO);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        when(processOrchestrationDTO.getNodeMediumDTO().getActionDTO(anyLong())).thenReturn(actionDTO);
        // act
        officialWxHandler.dealAIRecallSupplyOfficialWxGroupMessageV2(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        // assert
        verify(msgUnifiedPushService, never()).saveMsgPushTask(any(MsgPushRequest.class));
    }

    /**
     * Test duplicate message where the message has already been sent.
     */
    @Test
    public void testDealAIRecallSupplyOfficialWxGroupMessageV2DuplicateMessage() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setProcessOrchestrationNodeId(1L);
        detailDO.setGroupId("groupId");
        totalInvokeDetailDOS.add(detailDO);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        when(processOrchestrationDTO.getNodeMediumDTO().getActionDTO(anyLong())).thenReturn(actionDTO);
        ScrmAmProcessOrchestrationWxInvokeLogDO logDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        logDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_SUCCESS.getValue().byteValue());
        // act
        officialWxHandler.dealAIRecallSupplyOfficialWxGroupMessageV2(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        // assert
        verify(msgUnifiedPushService, never()).saveMsgPushTask(any(MsgPushRequest.class));
    }
}
