package com.sankuai.scrm.core.service.activity.wxgroup.dal.example;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("ScrmDSPersonalWxGroupMemberMatchRecordDOExample CreateCriteriaInternal Tests")
class ScrmDSPersonalWxGroupMemberMatchRecordDOExampleTest {

    @Spy
    private ScrmDSPersonalWxGroupMemberMatchRecordDOExample example;

    private ScrmDSPersonalWxGroupMemberMatchRecordDOExample scrmDSPersonalWxGroupMemberMatchRecordDOExample;

    @BeforeEach
    public void setUp() {
        scrmDSPersonalWxGroupMemberMatchRecordDOExample = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
    }

    /**
     * Test createCriteria when oredCriteria is empty
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsEmpty() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria mockCriteria = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria();
        doReturn(mockCriteria).when(example).createCriteriaInternal();
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria result = example.createCriteria();
        // assert
        assertNotNull(result);
        assertEquals(1, example.getOredCriteria().size());
        assertSame(mockCriteria, example.getOredCriteria().get(0));
        verify(example, times(1)).createCriteriaInternal();
    }

    /**
     * Test createCriteria when oredCriteria is not empty
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsNotEmpty() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria existingCriteria = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria();
        example.getOredCriteria().add(existingCriteria);
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria newCriteria = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria();
        doReturn(newCriteria).when(example).createCriteriaInternal();
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria result = example.createCriteria();
        // assert
        assertNotNull(result);
        assertEquals(1, example.getOredCriteria().size());
        assertSame(existingCriteria, example.getOredCriteria().get(0));
        assertSame(newCriteria, result);
        verify(example, times(1)).createCriteriaInternal();
    }

    /**
     * Test that multiple calls to createCriteria return different Criteria objects
     */
    @Test
    public void testCreateCriteriaReturnsDifferentObjects() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria criteria1 = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria();
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria criteria2 = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria();
        // First call returns criteria1, second call returns criteria2
        doReturn(criteria1, criteria2).when(example).createCriteriaInternal();
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria result1 = example.createCriteria();
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria result2 = example.createCriteria();
        // assert
        assertNotNull(result1);
        assertNotNull(result2);
        assertNotSame(result1, result2);
        verify(example, times(2)).createCriteriaInternal();
    }

    /**
     * 测试正常场景 - 两个参数都是正整数
     */
    @Test
    public void testLimitWithPositiveIntegers() {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = spy(new ScrmDSPersonalWxGroupMemberMatchRecordDOExample());
        int offset = 10;
        int rows = 20;
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample result = example.limit(offset, rows);
        // assert
        verify(example, times(1)).limit(offset, rows);
        assertEquals(offset, example.getOffset());
        assertEquals(rows, example.getRows());
        assertSame(example, result);
    }

    /**
     * 测试边界场景 - offset为0
     */
    @Test
    public void testLimitWithZeroOffset() {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = spy(new ScrmDSPersonalWxGroupMemberMatchRecordDOExample());
        int offset = 0;
        int rows = 20;
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample result = example.limit(offset, rows);
        // assert
        verify(example, times(1)).limit(offset, rows);
        assertEquals(offset, example.getOffset());
        assertEquals(rows, example.getRows());
        assertSame(example, result);
    }

    /**
     * 测试边界场景 - rows为0
     */
    @Test
    public void testLimitWithZeroRows() {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = spy(new ScrmDSPersonalWxGroupMemberMatchRecordDOExample());
        int offset = 10;
        int rows = 0;
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample result = example.limit(offset, rows);
        // assert
        verify(example, times(1)).limit(offset, rows);
        assertEquals(offset, example.getOffset());
        assertEquals(rows, example.getRows());
        assertSame(example, result);
    }

    /**
     * 测试边界场景 - 两个参数都为0
     */
    @Test
    public void testLimitWithBothZero() {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = spy(new ScrmDSPersonalWxGroupMemberMatchRecordDOExample());
        int offset = 0;
        int rows = 0;
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample result = example.limit(offset, rows);
        // assert
        verify(example, times(1)).limit(offset, rows);
        assertEquals(offset, example.getOffset());
        assertEquals(rows, example.getRows());
        assertSame(example, result);
    }

    /**
     * 测试异常场景 - offset为null
     */
    @Test
    public void testLimitWithNullOffset() {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = spy(new ScrmDSPersonalWxGroupMemberMatchRecordDOExample());
        Integer offset = null;
        int rows = 20;
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample result = example.limit(offset, rows);
        // assert
        verify(example, times(1)).limit(offset, rows);
        assertNull(example.getOffset());
        assertEquals(rows, example.getRows());
        assertSame(example, result);
    }

    /**
     * 测试异常场景 - rows为null
     */
    @Test
    public void testLimitWithNullRows() {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = spy(new ScrmDSPersonalWxGroupMemberMatchRecordDOExample());
        int offset = 10;
        Integer rows = null;
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample result = example.limit(offset, rows);
        // assert
        verify(example, times(1)).limit(offset, rows);
        assertEquals(offset, example.getOffset());
        assertNull(example.getRows());
        assertSame(example, result);
    }

    /**
     * 测试异常场景 - 两个参数都为null
     */
    @Test
    public void testLimitWithBothNull() {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = spy(new ScrmDSPersonalWxGroupMemberMatchRecordDOExample());
        Integer offset = null;
        Integer rows = null;
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample result = example.limit(offset, rows);
        // assert
        verify(example, times(1)).limit(offset, rows);
        assertNull(example.getOffset());
        assertNull(example.getRows());
        assertSame(example, result);
    }

    /**
     * 测试边界场景 - 参数为Integer.MAX_VALUE
     */
    @Test
    public void testLimitWithMaxValues() {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = spy(new ScrmDSPersonalWxGroupMemberMatchRecordDOExample());
        int offset = Integer.MAX_VALUE;
        int rows = Integer.MAX_VALUE;
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample result = example.limit(offset, rows);
        // assert
        verify(example, times(1)).limit(offset, rows);
        assertEquals(offset, example.getOffset());
        assertEquals(rows, example.getRows());
        assertSame(example, result);
    }

    /**
     * Test page method with normal positive values
     */
    @Test
    public void testPageNormalCase() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        Integer page = 2;
        Integer pageSize = 10;
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample result = example.page(page, pageSize);
        // assert
        assertEquals(20, result.getOffset());
        assertEquals(10, result.getRows());
        assertSame(example, result);
    }

    /**
     * Test page method with page 0
     */
    @Test
    public void testPageWithZeroPage() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        Integer page = 0;
        Integer pageSize = 10;
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample result = example.page(page, pageSize);
        // assert
        assertEquals(0, result.getOffset());
        assertEquals(10, result.getRows());
        assertSame(example, result);
    }

    /**
     * Test page method with large values (close to Integer.MAX_VALUE)
     */
    @Test
    public void testPageWithLargeValues() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        Integer page = Integer.MAX_VALUE / 2;
        Integer pageSize = 2;
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample result = example.page(page, pageSize);
        // assert
        assertEquals(Integer.MAX_VALUE - 1, result.getOffset());
        assertEquals(2, result.getRows());
        assertSame(example, result);
    }

    /**
     * Test page method with null page
     */
    @Test
    public void testPageWithNullPage() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        Integer page = null;
        Integer pageSize = 10;
        // act & assert
        assertThrows(NullPointerException.class, () -> example.page(page, pageSize));
    }

    /**
     * Test page method with null pageSize
     */
    @Test
    public void testPageWithNullPageSize() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        Integer page = 1;
        Integer pageSize = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> example.page(page, pageSize));
    }

    /**
     * 测试limit方法，输入正常的非负整数值
     */
    @Test
    public void testLimitNormal() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        Integer rows = 10;
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample result = example.limit(rows);
        // assert
        assertEquals(rows, result.getRows());
    }

    /**
     * 测试limit方法，输入null
     */
    @Test
    public void testLimitNull() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        Integer rows = null;
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample result = example.limit(rows);
        // assert
        assertNull(result.getRows());
    }

    /**
     * 测试or方法，应该返回一个新的Criteria对象，并将其添加到oredCriteria列表中
     */
    @Test
    public void testOr() {
        // arrange
        int originalSize = scrmDSPersonalWxGroupMemberMatchRecordDOExample.getOredCriteria().size();
        // act
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria criteria = scrmDSPersonalWxGroupMemberMatchRecordDOExample.or();
        // assert
        assertNotNull(criteria);
        assertEquals(originalSize + 1, scrmDSPersonalWxGroupMemberMatchRecordDOExample.getOredCriteria().size());
        assertTrue(scrmDSPersonalWxGroupMemberMatchRecordDOExample.getOredCriteria().contains(criteria));
    }

    @Test
    @DisplayName("Should create and return new Criteria object")
    public void testCreateCriteriaInternal_ShouldReturnNewCriteria() throws Throwable {
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria result = example.createCriteriaInternal();
        assertNotNull(result, "Created Criteria should not be null");
        assertTrue(result instanceof ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria, "Result should be instance of Criteria");
    }

    @Test
    @DisplayName("Should return different instances on multiple calls")
    public void testCreateCriteriaInternal_ShouldReturnDifferentInstances() throws Throwable {
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria result1 = example.createCriteriaInternal();
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria result2 = example.createCriteriaInternal();
        assertAll(() -> assertNotNull(result1, "First created Criteria should not be null"), () -> assertNotNull(result2, "Second created Criteria should not be null"), () -> assertNotSame(result1, result2, "Multiple calls should return different Criteria instances"));
    }

    @Test
    @DisplayName("Should create Criteria with empty criteria list")
    public void testCreateCriteriaInternal_ShouldHaveEmptyCriteria() throws Throwable {
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria result = example.createCriteriaInternal();
        assertAll(() -> assertNotNull(result.getCriteria(), "Criteria list should be initialized"), () -> assertTrue(result.getCriteria().isEmpty(), "Initial criteria list should be empty"));
    }

    @Test
    @DisplayName("Should create fully initialized Criteria")
    public void testCreateCriteriaInternal_ShouldBeProperlyInitialized() throws Throwable {
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample.Criteria result = example.createCriteriaInternal();
        assertAll(() -> assertNotNull(result, "Criteria instance should not be null"), () -> assertNotNull(result.getCriteria(), "Criteria list should be initialized"), () -> assertEquals(0, result.getCriteria().size(), "Criteria list should start empty"));
    }

    /**
     * Test the clear() method when the object has non-default values
     */
    @Test
    public void testClearWithNonDefaultValues() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        example.setOrderByClause("id DESC");
        example.setDistinct(true);
        example.or().andIdEqualTo(1L);
        example.setOffset(10);
        example.setRows(20);
        // verify initial state
        assertFalse(example.getOredCriteria().isEmpty());
        assertEquals("id DESC", example.getOrderByClause());
        assertTrue(example.isDistinct());
        assertEquals(Integer.valueOf(10), example.getOffset());
        assertEquals(Integer.valueOf(20), example.getRows());
        // act
        example.clear();
        // assert
        assertTrue(example.getOredCriteria().isEmpty(), "oredCriteria should be empty");
        assertNull(example.getOrderByClause(), "orderByClause should be null");
        assertFalse(example.isDistinct(), "distinct should be false");
        assertNull(example.getRows(), "rows should be null");
        assertNull(example.getOffset(), "offset should be null");
    }

    /**
     * Test the clear() method when the object has default values
     */
    @Test
    public void testClearWithDefaultValues() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        // verify initial state
        assertTrue(example.getOredCriteria().isEmpty());
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertNull(example.getRows());
        assertNull(example.getOffset());
        // act
        example.clear();
        // assert
        assertTrue(example.getOredCriteria().isEmpty(), "oredCriteria should be empty");
        assertNull(example.getOrderByClause(), "orderByClause should be null");
        assertFalse(example.isDistinct(), "distinct should be false");
        assertNull(example.getRows(), "rows should be null");
        assertNull(example.getOffset(), "offset should be null");
    }

    /**
     * Test the clear() method multiple times to verify idempotency
     */
    @Test
    public void testClearMultipleTimes() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberMatchRecordDOExample example = new ScrmDSPersonalWxGroupMemberMatchRecordDOExample();
        example.setOrderByClause("id DESC");
        example.setDistinct(true);
        example.or().andIdEqualTo(1L);
        example.setOffset(10);
        example.setRows(20);
        // verify initial state
        assertFalse(example.getOredCriteria().isEmpty());
        assertEquals("id DESC", example.getOrderByClause());
        assertTrue(example.isDistinct());
        assertEquals(Integer.valueOf(10), example.getOffset());
        assertEquals(Integer.valueOf(20), example.getRows());
        // act
        example.clear();
        // Call clear() again
        example.clear();
        // assert
        assertTrue(example.getOredCriteria().isEmpty(), "oredCriteria should be empty");
        assertNull(example.getOrderByClause(), "orderByClause should be null");
        assertFalse(example.isDistinct(), "distinct should be false");
        assertNull(example.getRows(), "rows should be null");
        assertNull(example.getOffset(), "offset should be null");
    }
}
