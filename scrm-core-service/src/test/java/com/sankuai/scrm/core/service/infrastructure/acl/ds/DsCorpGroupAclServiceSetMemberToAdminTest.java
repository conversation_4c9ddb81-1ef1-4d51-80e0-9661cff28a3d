package com.sankuai.scrm.core.service.infrastructure.acl.ds;

import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsCorpGroupAclService;
import com.sankuai.service.fe.corp.wx.thrift.CorpWxService;
import com.sankuai.service.fe.corp.wx.thrift.SetMemberToAdminRequest;
import com.sankuai.service.fe.corp.wx.thrift.SetMemberToAdminResponse;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DsCorpGroupAclServiceSetMemberToAdminTest {

    @InjectMocks
    private DsCorpGroupAclService dsCorpGroupAclService;

    @Mock
    private CorpWxService.Iface corpWxService;

    private String groupId;

    private String mobile;

    private java.util.List<String> memberUserIdList;

    private java.util.List<String> memberNameList;

    @Before
    public void setUp() {
        groupId = "groupId";
        mobile = "mobile";
        memberUserIdList = Arrays.asList("user1", "user2");
        memberNameList = Arrays.asList("name1", "name2");
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetMemberToAdminGroupIdIsNull() {
        dsCorpGroupAclService.setMemberToAdmin(null, mobile, memberUserIdList, memberNameList);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetMemberToAdminMobileIsNull() {
        dsCorpGroupAclService.setMemberToAdmin(groupId, null, memberUserIdList, memberNameList);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testSetMemberToAdminMemberUserIdListAndMemberNameListAreEmpty() {
        dsCorpGroupAclService.setMemberToAdmin(groupId, mobile, Collections.emptyList(), Collections.emptyList());
    }

    @Test
    public void testSetMemberToAdminSuccess() throws TException {
        SetMemberToAdminResponse response = new SetMemberToAdminResponse();
        when(corpWxService.setMemberToAdmin(any(SetMemberToAdminRequest.class))).thenReturn(response);
        SetMemberToAdminResponse result = dsCorpGroupAclService.setMemberToAdmin(groupId, mobile, memberUserIdList, memberNameList);
        verify(corpWxService, times(1)).setMemberToAdmin(any(SetMemberToAdminRequest.class));
        assertNotNull(result);
    }

    @Test
    public void testSetMemberToAdminThrowsTException() throws TException {
        when(corpWxService.setMemberToAdmin(any(SetMemberToAdminRequest.class))).thenThrow(TException.class);
        SetMemberToAdminResponse result = dsCorpGroupAclService.setMemberToAdmin(groupId, mobile, memberUserIdList, memberNameList);
        verify(corpWxService, times(1)).setMemberToAdmin(any(SetMemberToAdminRequest.class));
        assertNull(result);
    }
}
