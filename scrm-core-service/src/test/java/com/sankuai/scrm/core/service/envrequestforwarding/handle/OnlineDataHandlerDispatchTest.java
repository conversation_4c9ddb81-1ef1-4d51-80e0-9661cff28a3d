package com.sankuai.scrm.core.service.envrequestforwarding.handle;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import com.dianping.lion.Environment;
import com.sankuai.dz.srcm.envrequestforwarding.request.DispatchedOnlineDataRequest;
import com.sankuai.scrm.core.service.envrequestforwarding.config.OfflineDataSyncConfig;
import com.sankuai.scrm.core.service.envrequestforwarding.constant.OfflineDataSyncConstant;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.Spy;
import org.mockito.Mock;
import com.dianping.lion.client.Lion;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class OnlineDataHandlerDispatchTest {

    @Spy
    @InjectMocks
    private OnlineDataHandler onlineDataHandler;

    @Test
    public void testDispatchWhenSwimLanesEmpty() throws Throwable {
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        OfflineDataSyncConfig config = new OfflineDataSyncConfig();
        config.setSwimLanes(Collections.emptyList());
        try (MockedStatic<Lion> lionMock = Mockito.mockStatic(Lion.class);
            MockedStatic<Environment> envMock = Mockito.mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getBean(eq("test-app"), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class))).thenReturn(config);
            onlineDataHandler.dispatch(request);
            verify(onlineDataHandler, never()).handleData(any(), any());
        }
    }

    @Test
    public void testDispatchWhenSwimLanesNull() throws Throwable {
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        OfflineDataSyncConfig config = new OfflineDataSyncConfig();
        config.setSwimLanes(null);
        try (MockedStatic<Lion> lionMock = Mockito.mockStatic(Lion.class);
            MockedStatic<Environment> envMock = Mockito.mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getBean(eq("test-app"), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class))).thenReturn(config);
            onlineDataHandler.dispatch(request);
            verify(onlineDataHandler, never()).handleData(any(), any());
        }
    }

    // The tests for exceptions on getConfig and postAsJson are omitted since we cannot mock the static method call directly.
    @Test
    public void testDispatchWithSingleSwimLane() throws Throwable {
        DispatchedOnlineDataRequest request = DispatchedOnlineDataRequest.builder().corpId("testCorpId").msgContent("testContent").consumerGroupName("testConsumer").producerGroupName("testProducer").build();
        OfflineDataSyncConfig config = new OfflineDataSyncConfig();
        config.setSwimLanes(Collections.singletonList("lane1"));
        try (MockedStatic<Lion> lionMock = Mockito.mockStatic(Lion.class);
            MockedStatic<Environment> envMock = Mockito.mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getBean(eq("test-app"), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class))).thenReturn(config);
            doNothing().when(onlineDataHandler).handleData(any(), any());
            onlineDataHandler.dispatch(request);
            verify(onlineDataHandler, times(1)).handleData(eq(request), eq("lane1"));
        }
    }

    @Test
    public void testDispatchWithMultipleSwimLanes() throws Throwable {
        DispatchedOnlineDataRequest request = DispatchedOnlineDataRequest.builder().corpId("testCorpId").msgContent("testContent").consumerGroupName("testConsumer").producerGroupName("testProducer").build();
        List<String> swimLanes = Arrays.asList("lane1", "lane2", "lane3");
        OfflineDataSyncConfig config = new OfflineDataSyncConfig();
        config.setSwimLanes(swimLanes);
        try (MockedStatic<Lion> lionMock = Mockito.mockStatic(Lion.class);
            MockedStatic<Environment> envMock = Mockito.mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getBean(eq("test-app"), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class))).thenReturn(config);
            doNothing().when(onlineDataHandler).handleData(any(), any());
            onlineDataHandler.dispatch(request);
            for (String lane : swimLanes) {
                verify(onlineDataHandler, times(1)).handleData(eq(request), eq(lane));
            }
            verify(onlineDataHandler, times(swimLanes.size())).handleData(any(), any());
        }
    }

    @Test(expected = Exception.class)
    public void testDispatchWhenGetConfigThrowsException() throws Throwable {
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        try (MockedStatic<Lion> lionMock = Mockito.mockStatic(Lion.class);
            MockedStatic<Environment> envMock = Mockito.mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getBean(eq("test-app"), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class))).thenThrow(new RuntimeException("Config error"));
            onlineDataHandler.dispatch(request);
        }
    }

    @Test(expected = Exception.class)
    public void testDispatchWhenHandleDataThrowsException() throws Throwable {
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        OfflineDataSyncConfig config = new OfflineDataSyncConfig();
        config.setSwimLanes(Collections.singletonList("lane1"));
        try (MockedStatic<Lion> lionMock = Mockito.mockStatic(Lion.class);
            MockedStatic<Environment> envMock = Mockito.mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn("test-app");
            lionMock.when(() -> Lion.getBean(eq("test-app"), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class))).thenReturn(config);
            doThrow(new Exception("Handle data error")).when(onlineDataHandler).handleData(any(), any());
            onlineDataHandler.dispatch(request);
        }
    }
}
