package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.verify;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.DeepSeaWxHandler;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.OfficialWxHandler;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CrowdFriendTouchActionExecuteAfterNodeDealLogicTest {

    @InjectMocks
    private CrowdFriendTouchAction crowdFriendTouchAction;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private WeChatTokenAcl weChatTokenAcl;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private OfficialWxHandler officialWxHandler;

    @Mock
    private DeepSeaWxHandler deepSeaWxHandler;

    /**
     * Test when invoke details is empty
     */
    @Test
    public void testExecuteAfterNodeDealLogicWithEmptyInvokeDetails() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        // Set the id to avoid null
        processOrchestrationDTO.setId(1L);
        List<String> executorIds = Collections.singletonList("executor1");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        Mockito.when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        crowdFriendTouchAction.executeAfterNodeDealLogic(processOrchestrationDTO, executorIds, executeManagementDTO, any());
        // assert
        verify(officialWxHandler, never()).dealOfficialWxMessageV2(any(), any(), any(), any());
        verify(deepSeaWxHandler, never()).dealRealTimeDeepSeaWxMessageV2(any(), any(), any(), any(), any());
    }

    /**
     * Test real-time process orchestration with new logic enabled
     */
    @Test
    public void testExecuteAfterNodeDealLogicForRealTimeProcessWithNewLogic() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        // Set the id to avoid null
        processOrchestrationDTO.setId(1L);
        // REAL_TIME_PROCESS_ORCHESTRATION
        processOrchestrationDTO.setProcessOrchestrationType((byte) 3);
        List<String> executorIds = Collections.singletonList("executor1");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> invokeDetails = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetails.add(detail);
        try (MockedStatic<Lion> lionMock = Mockito.mockStatic(Lion.class);
             MockedStatic<Environment> envMock = Mockito.mockStatic(Environment.class)) {
            lionMock.when(() -> Lion.getBoolean(any(), any(), any())).thenReturn(true);
            envMock.when(Environment::getAppName).thenReturn("test-app");
            Mockito.when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(invokeDetails);
            // act
            crowdFriendTouchAction.executeAfterNodeDealLogic(processOrchestrationDTO, executorIds, executeManagementDTO, any());
            // assert
            verify(deepSeaWxHandler).dealRealTimeDeepSeaWxMessageV2(any(), any(), any(), any(), any());
        }
    }

    /**
     * Test official message with new logic enabled
     */
    @Test
    public void testExecuteAfterNodeDealLogicForOfficialMessageWithNewLogic() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        // Set the id to avoid null
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setProcessOrchestrationType((byte) 1);
        List<String> executorIds = Collections.singletonList("executor1");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> invokeDetails = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setType("message");
        invokeDetails.add(detail);
        try (MockedStatic<Lion> lionMock = Mockito.mockStatic(Lion.class);
             MockedStatic<Environment> envMock = Mockito.mockStatic(Environment.class)) {
            lionMock.when(() -> Lion.getBoolean(any(), any(), any())).thenReturn(true);
            envMock.when(Environment::getAppName).thenReturn("test-app");
            Mockito.when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(invokeDetails);
            // act
            crowdFriendTouchAction.executeAfterNodeDealLogic(processOrchestrationDTO, executorIds, executeManagementDTO, any());
            // assert
            verify(officialWxHandler).dealOfficialWxMessageV2(any(), any(), any(), any());
        }
    }
}
