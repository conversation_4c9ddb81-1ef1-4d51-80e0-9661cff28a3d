package com.sankuai.scrm.core.service.userWechatCoupon.domainService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.flow.enums.PageLocationType;
import com.sankuai.dz.srcm.friend.dynamiccode.dto.AttachmentsDTO;
import com.sankuai.dz.srcm.friend.dynamiccode.dto.FriendWelcomeMessageDTO;
import com.sankuai.dz.srcm.friend.dynamiccode.dto.WelcomeMsgContentDTO;
import com.sankuai.dz.srcm.friend.dynamiccode.enums.FriendWelcomeMsgType;
import com.sankuai.scrm.core.service.flowV2.dal.example.FlowEntryEventLogExample;
import com.sankuai.scrm.core.service.flowV2.dal.mapper.FlowEntryEventLogMapper;
import com.sankuai.scrm.core.service.flowV2.enums.FlowEntryEventType;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxContactUserDetail;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.apache.commons.lang3.ObjectUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GetUserWechantCouponTest {

    @Mock
    private FlowEntryEventLogMapper flowEntryEventLogMapper;

    @InjectMocks
    private GetUserWechantCoupon getUserWechantCoupon;

    private Method method;

    /**
     * 安全调用私有方法，处理各种边界情况
     */
    @SuppressWarnings("unchecked")
    private List<Long> safeInvokeGetCouponIds(FriendWelcomeMessageDTO messageDTO) {
        try {
            // 1. 获取方法引用
            Method method = GetUserWechantCoupon.class.getDeclaredMethod("getCouponIds", FriendWelcomeMessageDTO.class);
            method.setAccessible(true);
            // 2. 处理null messageDTO情况
            if (messageDTO == null) {
                return Collections.emptyList();
            }
            // 3. 确保attachments列表不为null
            if (messageDTO.getAttachments() == null) {
                messageDTO.setAttachments(new ArrayList<>());
            }
            // 4. 调用实际方法
            return (List<Long>) method.invoke(new GetUserWechantCoupon(), messageDTO);
        } catch (Exception e) {
            fail("反射调用方法失败: " + e.getMessage());
            return Collections.emptyList();
        }
    }

    @BeforeEach
    void setUp() throws NoSuchMethodException {
        MockitoAnnotations.openMocks(this);
        method = GetUserWechantCoupon.class.getDeclaredMethod("flowEntryEventLogHasRecord", WxContactUserDetail.class);
        method.setAccessible(true);
    }

    /**
     * Helper method to invoke private flowEntryEventLogHasRecord method using reflection
     */
    private boolean invokeFlowEntryEventLogHasRecord(WxContactUserDetail userDetail) throws Exception {
        try {
            return (boolean) method.invoke(getUserWechantCoupon, userDetail);
        } catch (InvocationTargetException e) {
            throw (Exception) e.getCause();
        }
    }

    /**
     * 测试null messageDTO输入
     * 预期：返回空列表
     */
    @Test
    void testGetCouponIds_NullMessageDTO() throws Throwable {
        // act
        List<Long> result = safeInvokeGetCouponIds(null);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试null attachments列表
     * 预期：返回空列表
     */
    @Test
    void testGetCouponIds_NullAttachments() throws Throwable {
        // arrange
        FriendWelcomeMessageDTO messageDTO = new FriendWelcomeMessageDTO();
        messageDTO.setAttachments(null);
        // act
        List<Long> result = safeInvokeGetCouponIds(messageDTO);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试空attachments列表
     * 预期：返回空列表
     */
    @Test
    void testGetCouponIds_EmptyAttachments() throws Throwable {
        // arrange
        FriendWelcomeMessageDTO messageDTO = new FriendWelcomeMessageDTO();
        messageDTO.setAttachments(Collections.emptyList());
        // act
        List<Long> result = safeInvokeGetCouponIds(messageDTO);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试包含null元素的attachments列表
     * 预期：跳过null元素，返回空列表
     */
    @Test
    void testGetCouponIds_AttachmentsWithNullElement() throws Throwable {
        // arrange
        FriendWelcomeMessageDTO messageDTO = new FriendWelcomeMessageDTO();
        List<AttachmentsDTO> attachments = new ArrayList<>();
        attachments.add(null);
        messageDTO.setAttachments(attachments);
        // act
        List<Long> result = safeInvokeGetCouponIds(messageDTO);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试无效的msgType（空字符串）
     * 预期：跳过无效元素，返回空列表
     */
    @Test
    void testGetCouponIds_EmptyMsgType() throws Throwable {
        // arrange
        FriendWelcomeMessageDTO messageDTO = new FriendWelcomeMessageDTO();
        List<AttachmentsDTO> attachments = new ArrayList<>();
        AttachmentsDTO attachment = new AttachmentsDTO();
        attachment.setMsgType("");
        attachment.setWelcomeMsgContentDTO(new WelcomeMsgContentDTO());
        attachments.add(attachment);
        messageDTO.setAttachments(attachments);
        // act
        List<Long> result = safeInvokeGetCouponIds(messageDTO);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试有效的COUPON类型附件
     * 预期：返回包含正确couponId的列表
     */
    @Test
    void testGetCouponIds_ValidCouponAttachment() throws Throwable {
        // arrange
        FriendWelcomeMessageDTO messageDTO = new FriendWelcomeMessageDTO();
        List<AttachmentsDTO> attachments = new ArrayList<>();
        AttachmentsDTO attachment = new AttachmentsDTO();
        attachment.setMsgType(FriendWelcomeMsgType.COUPON.desc);
        WelcomeMsgContentDTO contentDTO = new WelcomeMsgContentDTO();
        contentDTO.setCouponId(12345L);
        attachment.setWelcomeMsgContentDTO(contentDTO);
        attachments.add(attachment);
        messageDTO.setAttachments(attachments);
        // act
        List<Long> result = safeInvokeGetCouponIds(messageDTO);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(12345L, result.get(0));
    }

    /**
     * 测试混合有效和无效的附件
     * 预期：只返回有效COUPON类型的couponId
     */
    @Test
    void testGetCouponIds_MixedAttachments() throws Throwable {
        // arrange
        FriendWelcomeMessageDTO messageDTO = new FriendWelcomeMessageDTO();
        List<AttachmentsDTO> attachments = new ArrayList<>();
        // 有效优惠券附件
        AttachmentsDTO validAttachment = new AttachmentsDTO();
        validAttachment.setMsgType(FriendWelcomeMsgType.COUPON.desc);
        WelcomeMsgContentDTO validContent = new WelcomeMsgContentDTO();
        validContent.setCouponId(12345L);
        validAttachment.setWelcomeMsgContentDTO(validContent);
        attachments.add(validAttachment);
        // 无效附件（null内容）
        AttachmentsDTO nullContentAttachment = new AttachmentsDTO();
        nullContentAttachment.setMsgType(FriendWelcomeMsgType.COUPON.desc);
        nullContentAttachment.setWelcomeMsgContentDTO(null);
        attachments.add(nullContentAttachment);
        // 非优惠券类型
        AttachmentsDTO nonCouponAttachment = new AttachmentsDTO();
        nonCouponAttachment.setMsgType(FriendWelcomeMsgType.IMAGE.desc);
        WelcomeMsgContentDTO nonCouponContent = new WelcomeMsgContentDTO();
        nonCouponContent.setCouponId(67890L);
        nonCouponAttachment.setWelcomeMsgContentDTO(nonCouponContent);
        attachments.add(nonCouponAttachment);
        messageDTO.setAttachments(attachments);
        // act
        List<Long> result = safeInvokeGetCouponIds(messageDTO);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(12345L, result.get(0));
    }

    /**
     * Test when flow entry event log has record for given user
     */
    @Test
    public void testFlowEntryEventLogHasRecordWhenRecordExists() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("testUnionId");
        when(flowEntryEventLogMapper.countByExample(any(FlowEntryEventLogExample.class))).thenReturn(1L);
        // act
        boolean result = invokeFlowEntryEventLogHasRecord(userDetail);
        // assert
        assertTrue(result);
        verify(flowEntryEventLogMapper).countByExample(any(FlowEntryEventLogExample.class));
    }

    /**
     * Test when flow entry event log has no record for given user
     */
    @Test
    public void testFlowEntryEventLogHasRecordWhenNoRecordExists() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("testUnionId");
        when(flowEntryEventLogMapper.countByExample(any(FlowEntryEventLogExample.class))).thenReturn(0L);
        // act
        boolean result = invokeFlowEntryEventLogHasRecord(userDetail);
        // assert
        assertFalse(result);
        verify(flowEntryEventLogMapper).countByExample(any(FlowEntryEventLogExample.class));
    }

    /**
     * Test when userDetail parameter is null
     */
    @Test
    public void testFlowEntryEventLogHasRecordWhenUserDetailIsNull() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = null;
        // act & assert
        InvocationTargetException exception = assertThrows(InvocationTargetException.class, () -> {
            method.invoke(getUserWechantCoupon, userDetail);
        });
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    /**
     * Test when unionId is null in userDetail
     */
    @Test
    public void testFlowEntryEventLogHasRecordWhenUnionIdIsNull() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId(null);
        // act & assert
        Exception exception = assertThrows(RuntimeException.class, () -> {
            invokeFlowEntryEventLogHasRecord(userDetail);
        });
        assertEquals("Value for unionId cannot be null", exception.getMessage());
    }

    /**
     * Test when mapper throws exception
     */
    @Test
    public void testFlowEntryEventLogHasRecordWhenMapperThrowsException() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("testUnionId");
        when(flowEntryEventLogMapper.countByExample(any(FlowEntryEventLogExample.class))).thenThrow(new RuntimeException("DB error"));
        // act & assert
        Exception exception = assertThrows(RuntimeException.class, () -> {
            invokeFlowEntryEventLogHasRecord(userDetail);
        });
        assertEquals("DB error", exception.getMessage());
    }
}
