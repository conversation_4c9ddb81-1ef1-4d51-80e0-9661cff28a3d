package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.automatedmanagement.request.CreateExcelUploadingCrowdPackRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.CreateExcelUploadingCrowdPackResultVO;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.CrowdPackInnerService;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.exception.ExcelUploadingCrowdPackException;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

/**
 * Test class for ScrmAutoManagementServiceImpl#createExcelUploadingCrowdPack
 */
@RunWith(MockitoJUnitRunner.class)
public class ScrmAutoManagementServiceImplCreateExcelUploadingCrowdPackTest {

    @InjectMocks
    private ScrmAutoManagementServiceImpl scrmAutoManagementService;

    @Mock
    private CrowdPackInnerService crowdPackInnerService;

    private CreateExcelUploadingCrowdPackRequest request;

    private CreateExcelUploadingCrowdPackResultVO resultVO;

    @Before
    public void setUp() {
        request = new CreateExcelUploadingCrowdPackRequest();
        request.setUrl("http://test.com");
        request.setAppId("testApp");
        resultVO = new CreateExcelUploadingCrowdPackResultVO();
        // Note: Assuming resultVO has necessary fields set
    }

    /**
     * Test case for null request
     */
    @Test
    public void testCreateExcelUploadingCrowdPack_NullRequest() throws Throwable {
        // arrange
        request = null;
        // act
        RemoteResponse<CreateExcelUploadingCrowdPackResultVO> response = scrmAutoManagementService.createExcelUploadingCrowdPack(request);
        // assert
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for null URL in request
     */
    @Test
    public void testCreateExcelUploadingCrowdPack_NullUrl() throws Throwable {
        // arrange
        request.setUrl(null);
        // act
        RemoteResponse<CreateExcelUploadingCrowdPackResultVO> response = scrmAutoManagementService.createExcelUploadingCrowdPack(request);
        // assert
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for blank appId in request
     */
    @Test
    public void testCreateExcelUploadingCrowdPack_BlankAppId() throws Throwable {
        // arrange
        request.setAppId("");
        // act
        RemoteResponse<CreateExcelUploadingCrowdPackResultVO> response = scrmAutoManagementService.createExcelUploadingCrowdPack(request);
        // assert
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for successful crowd pack creation
     */
    @Test
    public void testCreateExcelUploadingCrowdPack_Success() throws Throwable {
        // arrange
        when(crowdPackInnerService.createExcelUploadingCrowdPack(any())).thenReturn(resultVO);
        // act
        RemoteResponse<CreateExcelUploadingCrowdPackResultVO> response = scrmAutoManagementService.createExcelUploadingCrowdPack(request);
        // assert
        assertTrue(response.isSuccess());
        assertEquals(resultVO, response.getData());
    }

    /**
     * Test case for ExcelUploadingCrowdPackException
     */
    @Test
    public void testCreateExcelUploadingCrowdPack_ExcelUploadingException() throws Throwable {
        // arrange
        String errorMsg = "Excel uploading failed";
        when(crowdPackInnerService.createExcelUploadingCrowdPack(any())).thenThrow(new ExcelUploadingCrowdPackException(errorMsg));
        // act
        RemoteResponse<CreateExcelUploadingCrowdPackResultVO> response = scrmAutoManagementService.createExcelUploadingCrowdPack(request);
        // assert
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }

    /**
     * Test case for general exception
     */
    @Test
    public void testCreateExcelUploadingCrowdPack_GeneralException() throws Throwable {
        // arrange
        when(crowdPackInnerService.createExcelUploadingCrowdPack(any())).thenThrow(new RuntimeException("Unexpected error"));
        // act
        RemoteResponse<CreateExcelUploadingCrowdPackResultVO> response = scrmAutoManagementService.createExcelUploadingCrowdPack(request);
        // assert
        assertFalse(response.isSuccess());
        assertNull(response.getData());
    }
}
