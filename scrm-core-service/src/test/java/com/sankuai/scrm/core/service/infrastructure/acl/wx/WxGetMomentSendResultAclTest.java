package com.sankuai.scrm.core.service.infrastructure.acl.wx;

import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxGetMomentSendResultRequest;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class WxGetMomentSendResultAclTest {

    @InjectMocks
    private WxGetMomentSendResultAcl wxGetMomentSendResultAcl;

    @Mock(lenient = true)
    private WeChatTokenAcl weChatTokenAcl;

    @Mock(lenient = true)
    private CorpAppConfigRepository appConfigRepository;

    @Test(expected = Exception.class)
    public void testGetMomentSendResultEmptyAppId() throws Throwable {
        WxGetMomentSendResultRequest request = new WxGetMomentSendResultRequest();
        wxGetMomentSendResultAcl.getMomentSendResult(request, "");
    }

    @Test(expected = Exception.class)
    public void testGetMomentSendResultEmptyAccessToken() throws Throwable {
        WxGetMomentSendResultRequest request = new WxGetMomentSendResultRequest();
        String appId = "testAppId";
        String corpId = "testCorpId";
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
        when(weChatTokenAcl.getTokenByCorpId(corpId)).thenReturn(WeChatTokenResult.builder().access_token("").build());
        wxGetMomentSendResultAcl.getMomentSendResult(request, appId);
    }
}
