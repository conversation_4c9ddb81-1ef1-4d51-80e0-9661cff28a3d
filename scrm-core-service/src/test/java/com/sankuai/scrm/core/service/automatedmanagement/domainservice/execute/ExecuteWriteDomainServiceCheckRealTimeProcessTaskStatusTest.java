package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationGoalDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmTimeOperatorParamUnitTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class ExecuteWriteDomainServiceCheckRealTimeProcessTaskStatusTest {

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test case for status VALID and beginTime before calendarStart.
     */
    @Test
    public void testCheckRealTimeProcessTaskStatus_ValidStatusAndBeginTimeBeforeCalendarStart() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.VALID.getValue().byteValue());
        infoDO.setBeginTime(new Date(System.currentTimeMillis() - 100000));
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        infoDO.setEndTime(calendar.getTime());
        when(processOrchestrationReadDomainService.queryGoalFuture(infoDO)).thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));
        // act
        executeWriteDomainService.checkRealTimeProcessTaskStatus(infoDO);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper, times(1)).updateByPrimaryKey(infoDO);
        assertEquals((int) ScrmProcessOrchestrationStatusTypeEnum.RUNNING.getValue().byteValue(), (int) infoDO.getStatus());
    }

    /**
     * Test case for status MESSAGE_SENT and final check time before current time.
     */
    @Test
    public void testCheckRealTimeProcessTaskStatus_MessageSentStatusAndFinalCheckTimeBeforeCurrentTime() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENT.getValue().byteValue());
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -3);
        infoDO.setEndTime(calendar.getTime());
        ScrmProcessOrchestrationGoalDTO goalDTO = new ScrmProcessOrchestrationGoalDTO();
        goalDTO.setCheckTimeUnit("1");
        goalDTO.setCheckTime("1");
        goalDTO.setGoalType((byte) 1);
        goalDTO.setStatus((byte) 1);
        when(processOrchestrationReadDomainService.queryGoalFuture(infoDO)).thenReturn(CompletableFuture.completedFuture(Collections.singletonList(goalDTO)));
        // act
        executeWriteDomainService.checkRealTimeProcessTaskStatus(infoDO);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper, times(1)).updateByPrimaryKey(infoDO);
        assertEquals((int) ScrmProcessOrchestrationStatusTypeEnum.FINISHED.getValue().byteValue(), (int) infoDO.getStatus());
    }

    /**
     * Test case for status RUNNING but no wxInvokeLogs found.
     */
    @Test
    public void testCheckRealTimeProcessTaskStatus_RunningStatusAndNoWxInvokeLogsFound() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.RUNNING.getValue().byteValue());
        infoDO.setId(1L);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        infoDO.setEndTime(calendar.getTime());
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(processOrchestrationReadDomainService.queryGoalFuture(infoDO)).thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));
        // act
        executeWriteDomainService.checkRealTimeProcessTaskStatus(infoDO);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper, never()).updateByPrimaryKey(infoDO);
        assertEquals((int) ScrmProcessOrchestrationStatusTypeEnum.RUNNING.getValue().byteValue(), (int) infoDO.getStatus());
    }

    /**
     * Test case for status MESSAGE_SENDING but endTime minus one day not before current time.
     */
    @Test
    public void testCheckRealTimeProcessTaskStatus_MessageSendingStatusAndEndTimeNotBeforeCurrentTime() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENDING.getValue().byteValue());
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 2);
        infoDO.setEndTime(calendar.getTime());
        when(processOrchestrationReadDomainService.queryGoalFuture(infoDO)).thenReturn(CompletableFuture.completedFuture(Collections.emptyList()));
        // act
        executeWriteDomainService.checkRealTimeProcessTaskStatus(infoDO);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper, never()).updateByPrimaryKey(infoDO);
        assertEquals((int) ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENDING.getValue().byteValue(), (int) infoDO.getStatus());
    }

    /**
     * Test case for status MESSAGE_SENT but final check time not before current time.
     */
    @Test
    public void testCheckRealTimeProcessTaskStatus_MessageSentStatusAndFinalCheckTimeNotBeforeCurrentTime() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENT.getValue().byteValue());
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 2);
        infoDO.setEndTime(calendar.getTime());
        ScrmProcessOrchestrationGoalDTO goalDTO = new ScrmProcessOrchestrationGoalDTO();
        goalDTO.setCheckTimeUnit("1");
        goalDTO.setCheckTime("1");
        goalDTO.setGoalType((byte) 1);
        goalDTO.setStatus((byte) 1);
        when(processOrchestrationReadDomainService.queryGoalFuture(infoDO)).thenReturn(CompletableFuture.completedFuture(Collections.singletonList(goalDTO)));
        // act
        executeWriteDomainService.checkRealTimeProcessTaskStatus(infoDO);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper, never()).updateByPrimaryKey(infoDO);
        assertEquals((int) ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENT.getValue().byteValue(), (int) infoDO.getStatus());
    }
}
