package com.sankuai.scrm.core.service.couponIntegration.domain;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.couponIntegration.dto.CouponDataSummaryDTO;
import com.sankuai.dz.srcm.couponIntegration.request.CouponDataSummaryQueryRequest;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmCouponDataSummaryDOMapper;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.dashboard.domain.Utils;
import com.sankuai.scrm.core.service.infrastructure.acl.sso.SsosvOpenApi;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CouponDashBoardDomainServiceGetTodaySummaryTest {

    @InjectMocks
    private CouponDashBoardDomainService couponDashBoardDomainService;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private ScrmSceneCouponRecordsMapper sceneCouponRecordDOMapper;

    private String corpId;

    private String appId;

    private ScrmSceneCouponRecords scrmSceneCouponRecordDO;

    @Mock
    private SsosvOpenApi ssosvOpenApi;

    @Mock
    private Utils utils;

    @Mock
    private ScrmCouponDataSummaryDOMapper couponDataSummaryDOMapper;

    @Before
    public void setUp() {
        corpId = "corpId";
        appId = "appId";
        scrmSceneCouponRecordDO = new ScrmSceneCouponRecords();
        scrmSceneCouponRecordDO.setCouponamount(new BigDecimal("100"));
        scrmSceneCouponRecordDO.setUsecoupontime(new Date());
    }

    @Test
    public void testGetTodaySummaryCorpIdIsNull() throws Throwable {
        CouponDataSummaryDTO result = couponDashBoardDomainService.getTodaySummary(null);
        assertNull(result);
    }

    @Test
    public void testGetTodaySummaryAppIdIsNull() throws Throwable {
        when(corpAppConfigRepository.getAppIdByCorpId(corpId)).thenReturn(null);
        CouponDataSummaryDTO result = couponDashBoardDomainService.getTodaySummary(corpId);
        assertNull(result);
    }

    @Test
    public void testGetTodaySummaryNoCoupon() throws Throwable {
        when(corpAppConfigRepository.getAppIdByCorpId(corpId)).thenReturn(appId);
        when(sceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(Collections.emptyList());
        CouponDataSummaryDTO result = couponDashBoardDomainService.getTodaySummary(corpId);
        assertNotNull(result);
        assertEquals(BigDecimal.ZERO, result.getCouponSendAmount());
        assertEquals(0L, result.getCouponSendCount().longValue());
        assertEquals(BigDecimal.ZERO, result.getCouponUseAmount());
        assertEquals(0L, result.getCouponUseCount().longValue());
    }

    @Test
    public void testGetTodaySummaryHasCoupon() throws Throwable {
        when(corpAppConfigRepository.getAppIdByCorpId(corpId)).thenReturn(appId);
        when(sceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(Collections.singletonList(scrmSceneCouponRecordDO));
        CouponDataSummaryDTO result = couponDashBoardDomainService.getTodaySummary(corpId);
        assertNotNull(result);
        assertEquals(new BigDecimal("100"), result.getCouponSendAmount());
        assertEquals(1L, result.getCouponSendCount().longValue());
        assertEquals(new BigDecimal("100"), result.getCouponUseAmount());
        assertEquals(1L, result.getCouponUseCount().longValue());
    }

    @Test
    public void testQuerySummaryByRequestWhenRequestIsNull() throws Throwable {
        // Explicitly casting null to CouponDataSummaryQueryRequest to resolve ambiguity
        List<CouponDataSummaryDTO> result = couponDashBoardDomainService.querySummaryByRequest((CouponDataSummaryQueryRequest) null);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testQuerySummaryByRequestWhenAppIdIsEmpty() throws Throwable {
        CouponDataSummaryQueryRequest request = new CouponDataSummaryQueryRequest();
        request.setAppId("");
        List<CouponDataSummaryDTO> result = couponDashBoardDomainService.querySummaryByRequest(request);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testQuerySummaryByRequestWhenCorpIdsIsEmpty() throws Throwable {
        CouponDataSummaryQueryRequest request = new CouponDataSummaryQueryRequest();
        request.setAppId("appId");
        request.setAccessToken("accessToken");
        when(ssosvOpenApi.queryLoginNameByAccessToken(anyString())).thenReturn("misId");
        when(utils.authentication(anyString(), anyString())).thenReturn(new ArrayList<>());
        List<CouponDataSummaryDTO> result = couponDashBoardDomainService.querySummaryByRequest(request);
        assertEquals(new ArrayList<>(), result);
    }

    @Test
    public void testQuerySummaryByRequestWhenTodaySummaryIsNull() throws Throwable {
        CouponDataSummaryQueryRequest request = new CouponDataSummaryQueryRequest();
        request.setAppId("appId");
        request.setAccessToken("accessToken");
        when(ssosvOpenApi.queryLoginNameByAccessToken(anyString())).thenReturn("misId");
        when(utils.authentication(anyString(), anyString())).thenReturn(Arrays.asList("corpId1", "corpId2"));
        when(utils.getCorpName(anyString())).thenReturn("corpName");
        when(couponDataSummaryDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        List<CouponDataSummaryDTO> result = couponDashBoardDomainService.querySummaryByRequest(request);
        assertEquals(2, result.size());
        assertEquals("corpName", result.get(0).getCorpName());
    }

    @Test
    public void testQuerySummaryByRequestWhenTodaySummaryIsNotNull() throws Throwable {
        CouponDataSummaryQueryRequest request = new CouponDataSummaryQueryRequest();
        request.setAppId("appId");
        request.setAccessToken("accessToken");
        when(ssosvOpenApi.queryLoginNameByAccessToken(anyString())).thenReturn("misId");
        when(utils.authentication(anyString(), anyString())).thenReturn(Arrays.asList("corpId1", "corpId2"));
        when(utils.getCorpName(anyString())).thenReturn("corpName");
        when(couponDataSummaryDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        // Mock the dependencies used in getTodaySummary
        when(corpAppConfigRepository.getAppIdByCorpId(anyString())).thenReturn("appId");
        when(sceneCouponRecordDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        List<CouponDataSummaryDTO> result = couponDashBoardDomainService.querySummaryByRequest(request);
        assertEquals(2, result.size());
        assertEquals("corpName", result.get(0).getCorpName());
    }
}
