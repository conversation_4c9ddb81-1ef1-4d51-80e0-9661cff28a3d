package com.sankuai.scrm.core.service.activity.fission.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.activity.fission.dto.activity.GroupFissionActivityDTO;
import com.sankuai.dz.srcm.activity.fission.dto.activity.PersonalGroupInfoDTO;
import com.sankuai.dz.srcm.activity.fission.request.*;
import com.sankuai.dz.srcm.group.dynamiccode.dto.GroupDynamicCodeChannelDTO;
import com.sankuai.dz.srcm.group.dynamiccode.dto.SaveGroupDynamicCodeChannelResult;
import com.sankuai.dz.srcm.group.dynamiccode.enums.CommonOperationResultEnum;
import com.sankuai.dz.srcm.group.dynamiccode.enums.DynamicCodeChannelSceneEnum;
import com.sankuai.scrm.core.service.activity.draw.fission.dal.entity.FissionActivityDrawConfig;
import com.sankuai.scrm.core.service.activity.draw.fission.dal.mapper.FissionActivityDrawConfigMapper;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionActivity;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionPrize;
import com.sankuai.scrm.core.service.activity.fission.dal.example.GroupFissionPrizeExample;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionActivityMapper;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionPrizeMapper;
import com.sankuai.scrm.core.service.activity.fission.domain.GroupFissionActivityDomainService;
import com.sankuai.scrm.core.service.activity.fission.domain.GroupFissionPrizeService;
import com.sankuai.scrm.core.service.activity.fission.enums.ActivityTypeEnum;
import com.sankuai.scrm.core.service.activity.fission.validation.FissionChainContext;
import com.sankuai.scrm.core.service.activity.fission.validation.enums.FissionChainMarkEnum;
import com.sankuai.scrm.core.service.activity.fission.validation.enums.FissionChainOperationEnum;
import com.sankuai.scrm.core.service.activity.fission.validation.exception.FissionValidatorException;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmActivityAndDSPersonalWxGroupRelationMapDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmDSPersonalWxGroupInfoDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.example.ScrmActivityAndDSPersonalWxGroupRelationMapDOExample;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.example.ScrmDSPersonalWxGroupInfoDOExample;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmActivityAndDSPersonalWxGroupRelationMapDOMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmDSPersonalWxGroupInfoDOMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.PersonalWxGroupInfoDomainService;
import com.sankuai.scrm.core.service.group.dynamiccode.domain.GroupDynamicCodeChannelLocalService;
import com.sankuai.scrm.core.service.group.dynamiccode.domain.GroupDynamicCodeLocalService;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.mapper.GroupDynamicCodeChannelConfigMapper;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.mapper.GroupDynamicCodeChannelMapper;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeChannelConfig;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class GroupFissionActivityServiceImplTest {

    @InjectMocks
    private GroupFissionActivityServiceImpl service;

    @Mock(lenient = true)
    private ScrmActivityAndDSPersonalWxGroupRelationMapDOMapper personalWxGroupRelationMapDOMapper;

    @Mock(lenient = true)
    private ScrmDSPersonalWxGroupInfoDOMapper personalWxGroupInfoDOMapper;

    @Mock(lenient = true)
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock(lenient = true)
    private GroupFissionActivityMapper groupFissionActivityMapper;

    @Mock(lenient = true)
    private GroupDynamicCodeChannelConfigMapper codeChannelConfigMapper;

    @Mock(lenient = true)
    private GroupFissionPrizeMapper groupFissionPrizeMapper;
    
    @Mock(lenient = true)
    private GroupDynamicCodeChannelLocalService groupDynamicCodeChannelLocalService;
    
    @Mock(lenient = true)
    private GroupDynamicCodeLocalService groupDynamicCodeLocalService;
    
    @Mock(lenient = true)
    private GroupDynamicCodeChannelMapper groupDynamicCodeChannelMapper;
    
    @Mock(lenient = true)
    private FissionActivityDrawConfigMapper fissionActivityDrawConfigMapper;
    
    @Mock(lenient = true)
    private GroupFissionActivityDomainService groupFissionActivityDomainService;
    
    @Mock(lenient = true)
    private PersonalWxGroupInfoDomainService personalWxGroupInfoDomainService;
    
    @Mock(lenient = true)
    private GroupFissionPrizeService groupFissionPrizeService;
    
    @Mock(lenient = true)
    private FissionChainContext<GroupFissionActivityRequest> fissionChainContext;

    @Captor
    private ArgumentCaptor<GroupFissionActivity> groupFissionActivityCaptor;
    
    @Captor
    private ArgumentCaptor<GroupDynamicCodeChannelDTO> channelDTOCaptor;
    
    @Captor
    private ArgumentCaptor<GroupDynamicCodeChannelConfig> channelConfigCaptor;
    
    @Captor
    private ArgumentCaptor<FissionActivityDrawConfig> drawConfigCaptor;
    
    @Captor
    private ArgumentCaptor<List<RewardInfoRequest>> rewardInfoListCaptor;

    // 添加通用请求对象变量
    private GroupFissionActivityRequest commonRequest;
    private ActivityBaseInfoRequest commonActivityBaseInfo;
    private PosterInfoRequest commonPosterInfo;
    private List<RewardInfoRequest> commonRewardInfoList;
    private CopyDynamicChannelInfo commonCopyChannelInfo;
    private ShareCardInfo commonShareCardInfo;
    private List<PersonalGroupInfoDTO> commonPersonalGroupInfo;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        
        // 初始化通用请求对象
        commonActivityBaseInfo = new ActivityBaseInfoRequest();
        commonActivityBaseInfo.setActivityName("Test Activity");
        commonActivityBaseInfo.setActivityInnerName("Test Inner Name");
        commonActivityBaseInfo.setStartTime(System.currentTimeMillis() + 100000);
        commonActivityBaseInfo.setEndTime(System.currentTimeMillis() + 200000);
        commonActivityBaseInfo.setRule("Test Rule");
        commonActivityBaseInfo.setQuitCheck(false);
        commonActivityBaseInfo.setNewUserCheck(false);
        commonActivityBaseInfo.setRiskCheck(false);
        commonActivityBaseInfo.setShowRankList(true);
        commonActivityBaseInfo.setActivityHeadImg("head.jpg");
        commonActivityBaseInfo.setBackgroundImg("bg.jpg");
        commonActivityBaseInfo.setBackgroundColor("#FFFFFF");
        commonActivityBaseInfo.setOriginalDrawNum(1);
        commonActivityBaseInfo.setIncrDrawNum(1);
        commonActivityBaseInfo.setMaxDrawNum(5);
        commonActivityBaseInfo.setIsRelationUrl(false);

        commonPosterInfo = new PosterInfoRequest();
        commonPosterInfo.setPoster("poster.jpg");
        commonPosterInfo.setShowAvtar(true);
        commonPosterInfo.setShowNickName(true);
        commonPosterInfo.setDynamicCodeIcon("icon.png");

        RewardInfoRequest reward1 = new RewardInfoRequest();
        reward1.setStage(1);
        reward1.setPriceName("Prize 1");
        reward1.setInvitationNum(5);
        reward1.setReceiveType(1);
        reward1.setRewardSize(100);
        reward1.setRewardType(1);
        commonRewardInfoList = new ArrayList<>();
        commonRewardInfoList.add(reward1);

        commonCopyChannelInfo = new CopyDynamicChannelInfo();
        commonCopyChannelInfo.setChannelId(123L);
        commonCopyChannelInfo.setActivityCityList(new ArrayList<>());

        commonShareCardInfo = new ShareCardInfo();
        commonShareCardInfo.setCardImg("card.jpg");
        commonShareCardInfo.setCardTitle("Share Title");
        commonShareCardInfo.setMinipName("Mini Program Name");

        commonPersonalGroupInfo = new ArrayList<>();
        commonPersonalGroupInfo.add(new PersonalGroupInfoDTO("testGropName",301L, "NonWeComGroup1"));

        commonRequest = new GroupFissionActivityRequest();
        commonRequest.setAppId("testAppId");
        commonRequest.setActivityBaseInfo(commonActivityBaseInfo);
        commonRequest.setPosterInfo(commonPosterInfo);
        commonRequest.setRewardInfo(commonRewardInfoList);
        commonRequest.setCopyDynamicChannelInfo(commonCopyChannelInfo);
        commonRequest.setShareCardInfo(commonShareCardInfo);
        commonRequest.setPersonalGroupInfo(commonPersonalGroupInfo);
    }

    /**
     * 测试getPersonalGroupInfo方法，当appId或activityId为空时，应返回null。
     */
    @Test
    public void testGetPersonalGroupInfoWithInvalidParams() throws Throwable {
        // arrange
        String appId = "";
        Long activityId = null;
        // act
        List<PersonalGroupInfoDTO> result = (List<PersonalGroupInfoDTO>) ReflectionTestUtils.invokeMethod(service, "getPersonalGroupInfo", appId, activityId);
        // assert
        assertNull(result);
    }

    /**
     * 测试getPersonalGroupInfo方法，当relationMapDOS为空时，应返回null。
     */
    @Test
    public void testGetPersonalGroupInfoWithEmptyRelationMapDOS() throws Throwable {
        // arrange
        String appId = "testAppId";
        Long activityId = 1L;
        String corpId = "testCorpId";
        when(corpAppConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
        when(personalWxGroupRelationMapDOMapper.selectByExample(any(ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.class))).thenReturn(Collections.emptyList());
        // act
        List<PersonalGroupInfoDTO> result = (List<PersonalGroupInfoDTO>) ReflectionTestUtils.invokeMethod(service, "getPersonalGroupInfo", appId, activityId);
        // assert
        assertNull(result);
    }

    /**
     * 测试getPersonalGroupInfo方法，当groupInfoDOS为空时，应返回null。
     */
    @Test
    public void testGetPersonalGroupInfoWithEmptyGroupInfoDOS() throws Throwable {
        // arrange
        String appId = "testAppId";
        Long activityId = 1L;
        String corpId = "testCorpId";
        List<ScrmActivityAndDSPersonalWxGroupRelationMapDO> relationMapDOS = new ArrayList<>();
        relationMapDOS.add(new ScrmActivityAndDSPersonalWxGroupRelationMapDO());
        when(corpAppConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
        when(personalWxGroupRelationMapDOMapper.selectByExample(any(ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.class))).thenReturn(relationMapDOS);
        when(personalWxGroupInfoDOMapper.selectByExample(any(ScrmDSPersonalWxGroupInfoDOExample.class))).thenReturn(Collections.emptyList());
        // act
        List<PersonalGroupInfoDTO> result = (List<PersonalGroupInfoDTO>) ReflectionTestUtils.invokeMethod(service, "getPersonalGroupInfo", appId, activityId);
        // assert
        assertNull(result);
    }

    /**
     * 测试getPersonalGroupInfo方法，正常情况下应返回非空的PersonalGroupInfoDTO列表。
     */
    @Test
    public void testGetPersonalGroupInfoWithValidParams() throws Throwable {
        // arrange
        String appId = "testAppId";
        Long activityId = 1L;
        String corpId = "testCorpId";
        List<ScrmActivityAndDSPersonalWxGroupRelationMapDO> relationMapDOS = new ArrayList<>();
        relationMapDOS.add(new ScrmActivityAndDSPersonalWxGroupRelationMapDO(1L, activityId, 1L, 1, null, null));
        List<ScrmDSPersonalWxGroupInfoDO> groupInfoDOS = new ArrayList<>();
        ScrmDSPersonalWxGroupInfoDO groupInfoDO = new ScrmDSPersonalWxGroupInfoDO();
        groupInfoDO.setId(1L);
        groupInfoDO.setCorpId(corpId);
        groupInfoDO.setWxGroupId("wxGroupId");
        groupInfoDO.setGroupQrCode("groupQrCode");
        groupInfoDO.setGroupName("groupName");
        groupInfoDO.setGroupNotice("groupNotice");
        groupInfoDO.setOwner("owner");
        groupInfoDO.setPoiName("poiName");
        groupInfoDO.setAdminList("adminList");
        groupInfoDO.setStatus(1);
        groupInfoDO.setDsGroupId(1L);
        groupInfoDOS.add(groupInfoDO);
        when(corpAppConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
        when(personalWxGroupRelationMapDOMapper.selectByExample(any(ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.class))).thenReturn(relationMapDOS);
        when(personalWxGroupInfoDOMapper.selectByExample(any(ScrmDSPersonalWxGroupInfoDOExample.class))).thenReturn(groupInfoDOS);
        // act
        List<PersonalGroupInfoDTO> result = (List<PersonalGroupInfoDTO>) ReflectionTestUtils.invokeMethod(service, "getPersonalGroupInfo", appId, activityId);
        // assert
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertEquals("groupName", result.get(0).getGroupName());
    }

    /**
     * Test queryGroupActivityByActivityId with null activityId
     */
    @Test
    public void testQueryGroupActivityByActivityId_NullActivityId() throws Throwable {
        // arrange
        String appId = "testAppId";
        Long activityId = null;
        // act
        RemoteResponse<GroupFissionActivityDTO> response = service.queryGroupActivityByActivityId(appId, activityId);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("查询裂变活动详情参数错误", response.getMsg());
    }

    /**
     * Test queryGroupActivityByActivityId with null appId
     */
    @Test
    public void testQueryGroupActivityByActivityId_NullAppId() throws Throwable {
        // arrange
        String appId = null;
        Long activityId = 123L;
        // act
        RemoteResponse<GroupFissionActivityDTO> response = service.queryGroupActivityByActivityId(appId, activityId);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("查询裂变活动详情参数错误", response.getMsg());
    }

    /**
     * Test queryGroupActivityByActivityId with non-existent appId
     */
    @Test
    public void testQueryGroupActivityByActivityId_NonExistentAppId() throws Throwable {
        // arrange
        String appId = "nonExistentAppId";
        Long activityId = 123L;
        when(corpAppConfigRepository.getConfigByAppId(appId)).thenReturn(null);
        // act
        RemoteResponse<GroupFissionActivityDTO> response = service.queryGroupActivityByActivityId(appId, activityId);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("查询裂变活动详情参数错误", response.getMsg());
    }

    /**
     * Test queryGroupActivityByActivityId with valid parameters but no activity found
     */
    @Test
    public void testQueryGroupActivityByActivityId_NoActivityFound() throws Throwable {
        // arrange
        String appId = "validAppId";
        Long activityId = 123L;
        when(corpAppConfigRepository.getConfigByAppId(appId)).thenReturn(new CorpAppConfig());
        when(groupFissionActivityMapper.selectByPrimaryKey(activityId)).thenReturn(null);
        // act
        RemoteResponse<GroupFissionActivityDTO> response = service.queryGroupActivityByActivityId(appId, activityId);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals(activityId, response.getData().getActivityId());
    }

    /**
     * Test queryGroupActivityByActivityId with valid parameters and activity found
     */
    @Test
    public void testQueryGroupActivityByActivityId_ActivityFound() throws Throwable {
        // arrange
        String appId = "validAppId";
        Long activityId = 123L;
        Long channelId = 456L;
        GroupFissionActivity activity = new GroupFissionActivity();
        activity.setId(activityId);
        activity.setAppId(appId);
        activity.setChannelId(channelId);
        activity.setStartTime(new Date());
        activity.setEndTime(new Date());
        activity.setActivityName("Test Activity");
        activity.setActivityType((byte) 1);
        GroupDynamicCodeChannelConfig channelConfig = new GroupDynamicCodeChannelConfig();
        channelConfig.setId(1L);
        channelConfig.setChannelId(channelId);
        channelConfig.setPoster("test_poster");
        GroupFissionPrize prize = new GroupFissionPrize();
        prize.setId(1L);
        prize.setActivityId(activityId);
        prize.setPrizeName("Test Prize");
        when(corpAppConfigRepository.getConfigByAppId(appId)).thenReturn(new CorpAppConfig());
        when(groupFissionActivityMapper.selectByPrimaryKey(activityId)).thenReturn(activity);
        when(codeChannelConfigMapper.selectByExample(any())).thenReturn(Arrays.asList(channelConfig));
        when(groupFissionPrizeMapper.selectByExampleWithBLOBs(any())).thenReturn(Arrays.asList(prize));
        // act
        RemoteResponse<GroupFissionActivityDTO> response = service.queryGroupActivityByActivityId(appId, activityId);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertNotNull(response.getData().getActivityBaseInfo());
        assertEquals(activityId, response.getData().getActivityId());
        assertEquals(appId, response.getData().getAppId());
        assertEquals("Test Activity", response.getData().getActivityBaseInfo().getActivityName());
        assertNotNull(response.getData().getRewardInfo());
        assertFalse(response.getData().getRewardInfo().isEmpty());
    }

    /**
     * Test queryGroupActivityByActivityId when exception occurs
     */
    @Test
    public void testQueryGroupActivityByActivityId_Exception() throws Throwable {
        // arrange
        String appId = "validAppId";
        Long activityId = 123L;
        when(corpAppConfigRepository.getConfigByAppId(appId)).thenReturn(new CorpAppConfig());
        when(groupFissionActivityMapper.selectByPrimaryKey(activityId)).thenThrow(new RuntimeException("Database error"));
        // act
        RemoteResponse<GroupFissionActivityDTO> response = service.queryGroupActivityByActivityId(appId, activityId);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("查询裂变活动信息失败", response.getMsg());
    }
    
    // Save Group Fission Activity Tests
    @Nested
    @DisplayName("saveGroupFissionActivity 方法测试")
    public class SaveGroupFissionActivityTest {

        @Test
        @DisplayName("成功保存 - 非企微群裂变 (NON_WECOM_GROUP)")
        public void saveNonWeComGroupFissionActivitySuccess() {
            commonActivityBaseInfo.setActivityType(ActivityTypeEnum.NON_WECOM_GROUP.getCode());
            Long expectedChannelId = 1L;
            Long expectedActivityId = 3L;
            List<Long> expectedGroupIds = Collections.singletonList(301L);

            // Mock validation
            doNothing().when(fissionChainContext).validate(eq(FissionChainMarkEnum.COMMON.getMark()), any(), eq(FissionChainOperationEnum.INSERT.getOperation()));
            doNothing().when(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), any(), eq(FissionChainOperationEnum.INSERT.getOperation()));

            // Mock channel creation (specific scene)
            SaveGroupDynamicCodeChannelResult channelResult = new SaveGroupDynamicCodeChannelResult();
            channelResult.setResultCode(CommonOperationResultEnum.SUCCESS.getCode());
            GroupDynamicCodeChannelDTO channelDTO = new GroupDynamicCodeChannelDTO();
            channelDTO.setId(expectedChannelId);
            channelResult.setGroupDynamicCodeChannelDTO(channelDTO);
            when(groupDynamicCodeChannelLocalService.saveChannel(channelDTOCaptor.capture())).thenReturn(channelResult);

            // Mock poster save
            when(codeChannelConfigMapper.insertSelective(any(GroupDynamicCodeChannelConfig.class))).thenReturn(1);

            // Mock activity save
            when(groupFissionActivityMapper.insertSelective(any(GroupFissionActivity.class))).thenAnswer(invocation -> {
                GroupFissionActivity activity = invocation.getArgument(0);
                activity.setId(expectedActivityId);
                return 1;
            });

            // Mock prize save
            when(groupFissionPrizeService.saveGroupFissionPrize(anyList(), eq(expectedActivityId))).thenReturn(true);

            // Mock group relation save
            when(groupFissionActivityDomainService.insertActivityAndPersonalWxGroupRelation(eq(expectedActivityId), eq(expectedGroupIds), eq(GroupFissionActivityServiceImpl.INITIAL_START_INDEX)))
                    .thenReturn(expectedGroupIds.size());

            // Execute
            RemoteResponse<Boolean> response = service.saveGroupFissionActivity(commonRequest);

            // Assert
            assertNotNull(response);
            assertTrue(response.isSuccess());
            assertTrue(response.getData());

            // Verify interactions
            verify(fissionChainContext).validate(eq(FissionChainMarkEnum.COMMON.getMark()), eq(commonRequest), eq(FissionChainOperationEnum.INSERT.getOperation()));
            verify(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), eq(commonRequest), eq(FissionChainOperationEnum.INSERT.getOperation()));
            verify(groupDynamicCodeChannelLocalService).saveChannel(any(GroupDynamicCodeChannelDTO.class));
            assertEquals(DynamicCodeChannelSceneEnum.NON_WECOM_FISSION_ACTIVITY_CHANNEL.getCode(), channelDTOCaptor.getValue().getScene());
            verify(codeChannelConfigMapper).insertSelective(any(GroupDynamicCodeChannelConfig.class));
            verify(groupFissionActivityMapper).insertSelective(groupFissionActivityCaptor.capture());
            assertEquals(expectedChannelId, groupFissionActivityCaptor.getValue().getChannelId());
            verify(groupFissionPrizeService).saveGroupFissionPrize(eq(commonRewardInfoList), eq(expectedActivityId));
            verify(groupFissionActivityDomainService).insertActivityAndPersonalWxGroupRelation(eq(expectedActivityId), eq(expectedGroupIds), eq(GroupFissionActivityServiceImpl.INITIAL_START_INDEX));
            verify(groupDynamicCodeLocalService, never()).queryAllValidDynamicCodeByChannelIdAndCity(anyLong(), anyList()); // Ensure copy code not called
        }

        @Test
        @DisplayName("保存失败 - 渠道创建失败")
        public void saveFailsWhenChannelCreationFails() {
            commonActivityBaseInfo.setActivityType(ActivityTypeEnum.GROUP.getCode()); // Use GROUP as example

            // Mock validation
            doNothing().when(fissionChainContext).validate(any(), any(), any());

            // Mock channel creation failure
            SaveGroupDynamicCodeChannelResult channelResult = new SaveGroupDynamicCodeChannelResult();
            channelResult.setResultCode(CommonOperationResultEnum.UNKNOWN_PROBLEM.getCode()); // Indicate failure
            channelResult.setMsg("活动名称已存在");
            when(groupDynamicCodeChannelLocalService.saveChannel(any(GroupDynamicCodeChannelDTO.class))).thenReturn(channelResult);

            // Execute
            RemoteResponse<Boolean> response = service.saveGroupFissionActivity(commonRequest);

            // Assert
            assertNotNull(response);
            assertFalse(response.isSuccess());
            assertEquals("活动名称在渠道表中已存在, 请更换活动名称", response.getMsg());

            // Verify other methods not called
            verify(codeChannelConfigMapper, never()).insertSelective(any());
            verify(groupFissionActivityMapper, never()).insertSelective(any());
            verify(groupFissionPrizeService, never()).saveGroupFissionPrize(anyList(), anyLong());
        }

        @Test
        @DisplayName("保存失败 - 活动数据保存失败")
        public void saveFailsWhenActivitySaveFails() {
            commonActivityBaseInfo.setActivityType(ActivityTypeEnum.GROUP.getCode());
            Long expectedChannelId = 1L;

            // Mock validation and channel/poster success
            doNothing().when(fissionChainContext).validate(any(), any(), any());
            SaveGroupDynamicCodeChannelResult channelResult = new SaveGroupDynamicCodeChannelResult();
            channelResult.setResultCode(CommonOperationResultEnum.SUCCESS.getCode());
            GroupDynamicCodeChannelDTO channelDTO = new GroupDynamicCodeChannelDTO();
            channelDTO.setId(expectedChannelId);
            channelResult.setGroupDynamicCodeChannelDTO(channelDTO);
            when(groupDynamicCodeChannelLocalService.saveChannel(any(GroupDynamicCodeChannelDTO.class))).thenReturn(channelResult);
            when(codeChannelConfigMapper.insertSelective(any(GroupDynamicCodeChannelConfig.class))).thenReturn(1);

            // Mock activity save failure
            when(groupFissionActivityMapper.insertSelective(any(GroupFissionActivity.class))).thenReturn(0); // Indicate failure

            // Execute
            RemoteResponse<Boolean> response = service.saveGroupFissionActivity(commonRequest);

            // Assert
            assertNotNull(response);
            assertFalse(response.isSuccess());
            assertEquals("保存群裂变活动数据未成功, 请核对活动基本信息", response.getMsg());

            // Verify prize service not called
            verify(groupFissionPrizeService, never()).saveGroupFissionPrize(anyList(), anyLong());
        }

        @Test
        @DisplayName("保存失败 - 奖品保存失败")
        public void saveFailsWhenPrizeSaveFails() {
            commonActivityBaseInfo.setActivityType(ActivityTypeEnum.GROUP.getCode());
            Long expectedChannelId = 1L;
            Long expectedActivityId = 3L;

            // Mock validation, channel, poster, activity save success
            doNothing().when(fissionChainContext).validate(any(), any(), any());
            SaveGroupDynamicCodeChannelResult channelResult = new SaveGroupDynamicCodeChannelResult();
            channelResult.setResultCode(CommonOperationResultEnum.SUCCESS.getCode());
            GroupDynamicCodeChannelDTO channelDTO = new GroupDynamicCodeChannelDTO();
            channelDTO.setId(expectedChannelId);
            channelResult.setGroupDynamicCodeChannelDTO(channelDTO);
            when(groupDynamicCodeChannelLocalService.saveChannel(any(GroupDynamicCodeChannelDTO.class))).thenReturn(channelResult);
            when(codeChannelConfigMapper.insertSelective(any(GroupDynamicCodeChannelConfig.class))).thenReturn(1);
            when(groupFissionActivityMapper.insertSelective(any(GroupFissionActivity.class))).thenAnswer(invocation -> {
                GroupFissionActivity activity = invocation.getArgument(0);
                activity.setId(expectedActivityId);
                return 1;
            });

            // Mock prize save failure
            when(groupFissionPrizeService.saveGroupFissionPrize(anyList(), eq(expectedActivityId))).thenReturn(false);

            // Execute
            RemoteResponse<Boolean> response = service.saveGroupFissionActivity(commonRequest);

            // Assert
            assertNotNull(response);
            assertFalse(response.isSuccess());
            assertEquals("保存活动奖品未成功, 请核对奖品信息", response.getMsg());
        }

        @Test
        @DisplayName("保存失败 - 非企微群关联失败")
        public void saveFailsWhenNonWeComGroupRelationFails() {
            commonActivityBaseInfo.setActivityType(ActivityTypeEnum.NON_WECOM_GROUP.getCode());
            Long expectedChannelId = 1L;
            Long expectedActivityId = 3L;
            List<Long> expectedGroupIds = Collections.singletonList(301L);

            // Mock previous steps success
            doNothing().when(fissionChainContext).validate(any(), any(), any());
            SaveGroupDynamicCodeChannelResult channelResult = new SaveGroupDynamicCodeChannelResult();
            channelResult.setResultCode(CommonOperationResultEnum.SUCCESS.getCode());
            GroupDynamicCodeChannelDTO channelDTO = new GroupDynamicCodeChannelDTO();
            channelDTO.setId(expectedChannelId);
            channelResult.setGroupDynamicCodeChannelDTO(channelDTO);
            when(groupDynamicCodeChannelLocalService.saveChannel(any(GroupDynamicCodeChannelDTO.class))).thenReturn(channelResult);
            when(codeChannelConfigMapper.insertSelective(any(GroupDynamicCodeChannelConfig.class))).thenReturn(1);
            when(groupFissionActivityMapper.insertSelective(any(GroupFissionActivity.class))).thenAnswer(invocation -> {
                GroupFissionActivity activity = invocation.getArgument(0);
                activity.setId(expectedActivityId);
                return 1;
            });
            when(groupFissionPrizeService.saveGroupFissionPrize(anyList(), eq(expectedActivityId))).thenReturn(true);

            // Mock group relation save failure (return count != expected)
            when(groupFissionActivityDomainService.insertActivityAndPersonalWxGroupRelation(eq(expectedActivityId), eq(expectedGroupIds), eq(GroupFissionActivityServiceImpl.INITIAL_START_INDEX)))
                    .thenReturn(0); // Failed insertion

            // Execute
            RemoteResponse<Boolean> response = service.saveGroupFissionActivity(commonRequest);

            // Assert
            assertNotNull(response);
            assertFalse(response.isSuccess());
            assertEquals("群裂变活动关联群聊失败", response.getMsg());
        }

        @Test
        @DisplayName("保存失败 - 参数校验失败")
        public void saveFailsWhenValidationFails() {
            commonActivityBaseInfo.setActivityType(ActivityTypeEnum.GROUP.getCode());

            // Mock validation failure
            doThrow(new FissionValidatorException("校验失败: 活动名称不能为空"))
                    .when(fissionChainContext).validate(eq(FissionChainMarkEnum.COMMON.getMark()), any(), eq(FissionChainOperationEnum.INSERT.getOperation()));

            // Execute
            RemoteResponse<Boolean> response = service.saveGroupFissionActivity(commonRequest);

            // Assert
            assertNotNull(response);
            assertFalse(response.isSuccess());
            assertEquals("校验失败: 活动名称不能为空", response.getMsg());

            // Verify subsequent methods not called
            verify(groupDynamicCodeChannelLocalService, never()).saveChannel(any());
            verify(groupFissionActivityMapper, never()).insertSelective(any());
        }
    }

    // Update Group Fission Activity Tests (NON_WECOM_GROUP)
    @Nested
    @DisplayName("updateGroupFissionActivity 方法测试 (NON_WECOM_GROUP)")
    public class UpdateNonWeComGroupFissionActivityTest {

        private GroupFissionActivity existingActivityFuture; // Starts in future
        private GroupFissionActivity existingActivityPast;   // Started in past
        private final Long activityId = 100L;
        private final Long channelId = 200L; // For non-private types

        @BeforeEach
        public void updateSetup() {
            // Ensure commonRequest is re-initialized or reset if modified by save tests
            setUp(); // Call the main setUp to reset common objects

            commonRequest.setActivityId(activityId);
            commonRequest.getActivityBaseInfo().setActivityType(ActivityTypeEnum.NON_WECOM_GROUP.getCode());
            // Ensure PosterInfo has an ID for updates
            commonPosterInfo.setId(300L);
            commonRequest.setPosterInfo(commonPosterInfo);

            existingActivityFuture = new GroupFissionActivity();
            existingActivityFuture.setId(activityId);
            existingActivityFuture.setAppId(commonRequest.getAppId());
            existingActivityFuture.setActivityType(ActivityTypeEnum.NON_WECOM_GROUP.getCode());
            existingActivityFuture.setStartTime(new Date(System.currentTimeMillis() + 50000)); // Future
            existingActivityFuture.setEndTime(new Date(System.currentTimeMillis() + 150000));
            existingActivityFuture.setChannelId(channelId); // Needed for poster update logic

            existingActivityPast = new GroupFissionActivity();
            existingActivityPast.setId(activityId);
            existingActivityPast.setAppId(commonRequest.getAppId());
            existingActivityPast.setActivityType(ActivityTypeEnum.NON_WECOM_GROUP.getCode());
            existingActivityPast.setStartTime(new Date(System.currentTimeMillis() - 50000)); // Past
            existingActivityPast.setEndTime(new Date(System.currentTimeMillis() + 150000));
            existingActivityPast.setChannelId(channelId);
        }

        @Test
        @DisplayName("成功更新 (NON_WECOM_GROUP) - 活动未开始")
        public void updateNonWeComGroupSuccessBeforeStart() {
            // Mocks
            when(groupFissionActivityMapper.selectByPrimaryKey(activityId)).thenReturn(existingActivityFuture);
            doNothing().when(fissionChainContext).validate(eq(FissionChainMarkEnum.COMMON.getMark()), any(), eq(FissionChainOperationEnum.UPDATE.getOperation()));
            doNothing().when(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), any(), eq(FissionChainOperationEnum.UPDATE.getOperation()));

            // Mock updateGroupList (delete old, insert new)
            List<Long> newGroupIds = Arrays.asList(301L, 302L); // Example new list
            commonRequest.getPersonalGroupInfo().add(new PersonalGroupInfoDTO("testGroupName",302L, "Group2"));
            doNothing().when(groupFissionActivityDomainService).deleteActivityAndPersonalWxGroupRelation(activityId);
        // Add to request
            // Assume 1 old relation deleted
            when(groupFissionActivityDomainService.insertActivityAndPersonalWxGroupRelation(eq(activityId), eq(newGroupIds), eq(GroupFissionActivityServiceImpl.INITIAL_START_INDEX)))
                    .thenReturn(newGroupIds.size());

            // Mock updatePrizeInfo (delete old, insert new)
            when(groupFissionPrizeMapper.deleteByExample(any(GroupFissionPrizeExample.class))).thenReturn(1); // Assume 1 old prize deleted
            when(groupFissionPrizeService.saveGroupFissionPrize(eq(commonRewardInfoList), eq(activityId))).thenReturn(true);

            // Mock updatePoster (full update)
            when(codeChannelConfigMapper.updateByPrimaryKey(any(GroupDynamicCodeChannelConfig.class))).thenReturn(1);

            // Mock updateFissionActivity (full update)
            when(groupFissionActivityMapper.updateByPrimaryKeySelective(any(GroupFissionActivity.class))).thenReturn(1);

            // Execute
            RemoteResponse<Boolean> response = service.updateGroupFissionActivity(commonRequest);

            // Assert
            assertNotNull(response);
            assertTrue(response.isSuccess());
            assertTrue(response.getData());

            // Verify interactions
            verify(fissionChainContext).validate(eq(FissionChainMarkEnum.COMMON.getMark()), eq(commonRequest), eq(FissionChainOperationEnum.UPDATE.getOperation()));
            verify(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), eq(commonRequest), eq(FissionChainOperationEnum.UPDATE.getOperation()));
            verify(groupFissionActivityMapper).selectByPrimaryKey(activityId);
            verify(groupFissionActivityDomainService).deleteActivityAndPersonalWxGroupRelation(activityId);
            verify(groupFissionActivityDomainService).insertActivityAndPersonalWxGroupRelation(eq(activityId), eq(newGroupIds), eq(GroupFissionActivityServiceImpl.INITIAL_START_INDEX));
            verify(groupFissionPrizeMapper).deleteByExample(any(GroupFissionPrizeExample.class));
            verify(groupFissionPrizeService).saveGroupFissionPrize(eq(commonRewardInfoList), eq(activityId));
            verify(codeChannelConfigMapper).updateByPrimaryKey(channelConfigCaptor.capture()); // Full update
            assertEquals(commonPosterInfo.getId(), channelConfigCaptor.getValue().getId());
            assertEquals(commonPosterInfo.getPoster(), channelConfigCaptor.getValue().getPoster());
            verify(groupFissionActivityMapper).updateByPrimaryKeySelective(groupFissionActivityCaptor.capture()); // Full update
            assertEquals(activityId, groupFissionActivityCaptor.getValue().getId());
            assertEquals(commonActivityBaseInfo.getActivityName(), groupFissionActivityCaptor.getValue().getActivityName()); // Check a few fields
            assertEquals(commonActivityBaseInfo.getRule(), groupFissionActivityCaptor.getValue().getRule());
        }

        @Test
        @DisplayName("成功更新 (NON_WECOM_GROUP) - 活动已开始")
        public void updateNonWeComGroupSuccessAfterStart() {
            List<Long> originalGroupIds = Collections.singletonList(301L);
            List<Long> expectedNewGroupIds = Collections.singletonList(302L);
            commonRequest.getPersonalGroupInfo().add(new PersonalGroupInfoDTO("testGroupName",302L, "Group2")); // Add to request

            // Mocks
            when(groupFissionActivityMapper.selectByPrimaryKey(activityId)).thenReturn(existingActivityPast);
            doNothing().when(fissionChainContext).validate(eq(FissionChainMarkEnum.COMMON.getMark()), any(), eq(FissionChainOperationEnum.UPDATE.getOperation()));
            doNothing().when(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), any(), eq(FissionChainOperationEnum.UPDATE.getOperation()));

            // Mock updateGroupList (only insert new)
            when(personalWxGroupInfoDomainService.queryActivityOrderedGroupIds(activityId)).thenReturn(originalGroupIds);
            when(groupFissionActivityDomainService.insertActivityAndPersonalWxGroupRelation(eq(activityId), eq(expectedNewGroupIds), eq(originalGroupIds.size() + GroupFissionActivityServiceImpl.INITIAL_START_INDEX)))
                    .thenReturn(expectedNewGroupIds.size());

            // Mock updatePoster (partial update)
            when(codeChannelConfigMapper.updateByPrimaryKeySelective(any(GroupDynamicCodeChannelConfig.class))).thenReturn(1);

            // Mock updateFissionActivity (partial update)
            when(groupFissionActivityMapper.updateByPrimaryKeySelective(any(GroupFissionActivity.class))).thenReturn(1);

            // Execute
            RemoteResponse<Boolean> response = service.updateGroupFissionActivity(commonRequest);

            // Assert
            assertNotNull(response);
            assertTrue(response.isSuccess());
            assertTrue(response.getData());

            // Verify interactions
            verify(groupFissionActivityMapper).selectByPrimaryKey(activityId);
            verify(personalWxGroupInfoDomainService).queryActivityOrderedGroupIds(activityId); // Called to find existing
            verify(groupFissionActivityDomainService, never()).deleteActivityAndPersonalWxGroupRelation(anyLong()); // Delete not called
            verify(groupFissionActivityDomainService).insertActivityAndPersonalWxGroupRelation(eq(activityId), eq(expectedNewGroupIds), eq(originalGroupIds.size() + GroupFissionActivityServiceImpl.INITIAL_START_INDEX));
            verify(groupFissionPrizeMapper, never()).deleteByExample(any()); // Prize not updated
            verify(groupFissionPrizeService, never()).saveGroupFissionPrize(any(), anyLong());
            verify(codeChannelConfigMapper, never()).updateByPrimaryKey(any()); // Full poster update not called
            verify(codeChannelConfigMapper).updateByPrimaryKeySelective(channelConfigCaptor.capture()); // Selective update
            assertEquals(commonPosterInfo.getId(), channelConfigCaptor.getValue().getId());
            assertEquals(commonPosterInfo.getPoster(), channelConfigCaptor.getValue().getPoster());
            assertNull(channelConfigCaptor.getValue().getTitle()); // Title should not be updated after start for poster
            verify(groupFissionActivityMapper).updateByPrimaryKeySelective(groupFissionActivityCaptor.capture()); // Selective update
            assertEquals(activityId, groupFissionActivityCaptor.getValue().getId());
            assertEquals(commonActivityBaseInfo.getActivityName(), groupFissionActivityCaptor.getValue().getActivityName()); // Name can be updated
            assertEquals(commonActivityBaseInfo.getRule(), groupFissionActivityCaptor.getValue().getRule()); // Rule can be updated
            assertNull(groupFissionActivityCaptor.getValue().getStartTime()); // Start time cannot be updated after start
            assertNull(groupFissionActivityCaptor.getValue().getQuitCheck()); // Checks cannot be updated
        }

        @Test
        @DisplayName("更新失败 (NON_WECOM_GROUP) - 活动未开始 - 更新群列表失败")
        public void updateNonWeComGroupFailsBeforeStartWhenGroupListUpdateFails() {
            // Mocks for initial steps
            when(groupFissionActivityMapper.selectByPrimaryKey(activityId)).thenReturn(existingActivityFuture);
            doNothing().when(fissionChainContext).validate(any(), any(), any());

            // Mock updateGroupList failure (e.g., insert fails)
            List<Long> newGroupIds = Collections.singletonList(301L);
            doNothing().when(groupFissionActivityDomainService).deleteActivityAndPersonalWxGroupRelation(activityId);
            when(groupFissionActivityDomainService.insertActivityAndPersonalWxGroupRelation(eq(activityId), eq(newGroupIds), eq(GroupFissionActivityServiceImpl.INITIAL_START_INDEX)))
                    .thenReturn(0); // Simulate insert failure

            // Execute
            RemoteResponse<Boolean> response = service.updateGroupFissionActivity(commonRequest);

            // Assert
            assertNotNull(response);
            assertFalse(response.isSuccess());
            assertEquals("更新关联的非企微群列表失败", response.getMsg());

            // Verify subsequent steps not executed
            verify(groupFissionPrizeMapper, never()).deleteByExample(any());
            verify(codeChannelConfigMapper, never()).updateByPrimaryKey(any());
            verify(groupFissionActivityMapper, times(1)).selectByPrimaryKey(any()); // Only select called for activity mapper
            verify(groupFissionActivityMapper, never()).updateByPrimaryKeySelective(any());
        }

        @Test
        @DisplayName("更新失败 (NON_WECOM_GROUP) - 活动未开始 - 更新奖品失败")
        public void updateNonWeComGroupFailsBeforeStartWhenPrizeUpdateFails() {
            // Mocks for initial steps
            when(groupFissionActivityMapper.selectByPrimaryKey(activityId)).thenReturn(existingActivityFuture);
            doNothing().when(fissionChainContext).validate(any(), any(), any());
            doNothing().when(groupFissionActivityDomainService).deleteActivityAndPersonalWxGroupRelation(activityId);
            when(groupFissionActivityDomainService.insertActivityAndPersonalWxGroupRelation(any(), any(), anyInt())).thenReturn(1);

            // Mock updatePrizeInfo failure (e.g., save fails)
            when(groupFissionPrizeMapper.deleteByExample(any(GroupFissionPrizeExample.class))).thenReturn(1);
            when(groupFissionPrizeService.saveGroupFissionPrize(eq(commonRewardInfoList), eq(activityId))).thenReturn(false);

            // Execute
            RemoteResponse<Boolean> response = service.updateGroupFissionActivity(commonRequest);

            // Assert
            assertNotNull(response);
            assertFalse(response.isSuccess());
            // The message might come from groupFissionPrizeService, but the wrapper method has its own message
            assertEquals("活动开始前更新奖品信息表失败", response.getMsg());

            // Verify subsequent steps not executed
            verify(codeChannelConfigMapper, never()).updateByPrimaryKey(any());
            verify(groupFissionActivityMapper, times(1)).selectByPrimaryKey(any());
            verify(groupFissionActivityMapper, never()).updateByPrimaryKeySelective(any());
        }
    }
}