package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.dianping.cat.Cat;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.dashboard.dal.dto.EsFridayIntelligentFollowLog;
import com.sankuai.scrm.core.service.dashboard.domain.DashBoardESWriteDomainService;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmFridayIntelligentFollowLogDO;
import com.sankuai.scrm.core.service.realtime.task.dal.example.ScrmFridayIntelligentFollowLogDOExample;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmFridayIntelligentFollowLogDOMapper;
import java.lang.reflect.Method;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ScrmIntelligentFollowResultDelayConsumerWriteLogTest {

    @Mock
    private DashBoardESWriteDomainService dashBoardESWriteDomainService;

    @Mock
    private ScrmFridayIntelligentFollowLogDOMapper fridayIntelligentFollowLogDOMapper;

    @InjectMocks
    private ScrmIntelligentFollowResultDelayConsumer consumer;

    /**
     * Helper method to invoke the private writeLog method using reflection
     */
    private void invokeWriteLogMethod(IntelligentFollowResultDTO mqMessageDTO, StepExecuteResultDTO stepExecuteResultDTO) throws Throwable {
        Method writeLogMethod = ScrmIntelligentFollowResultDelayConsumer.class.getDeclaredMethod("writeLog", IntelligentFollowResultDTO.class, StepExecuteResultDTO.class);
        writeLogMethod.setAccessible(true);
        writeLogMethod.invoke(consumer, mqMessageDTO, stepExecuteResultDTO);
    }

    /**
     * Test when stepExecuteResultDTO is null, method should return immediately
     */
    @Test
    public void testWriteLogWhenStepResultIsNull() throws Throwable {
        // arrange
        IntelligentFollowResultDTO mqMessageDTO = new IntelligentFollowResultDTO();
        StepExecuteResultDTO stepExecuteResultDTO = null;
        // act
        invokeWriteLogMethod(mqMessageDTO, stepExecuteResultDTO);
        // assert
        verifyNoInteractions(dashBoardESWriteDomainService);
        verifyNoInteractions(fridayIntelligentFollowLogDOMapper);
    }

    /**
     * Test normal case where both ES and DB updates succeed
     */
    @Test
    public void testWriteLogSuccessfullyUpdatesBothESAndDB() throws Throwable {
        // arrange
        IntelligentFollowResultDTO mqMessageDTO = new IntelligentFollowResultDTO();
        mqMessageDTO.setEsId("test-es-id");
        mqMessageDTO.setUserId(123L);
        mqMessageDTO.setAppId("test-app-id");
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        stepExecuteResultDTO.setCode(200);
        stepExecuteResultDTO.setMsg("success");
        stepExecuteResultDTO.setSuccess(true);
        // act
        invokeWriteLogMethod(mqMessageDTO, stepExecuteResultDTO);
        // assert
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(eq("scrm_friday_intelligent_follow_log"), any(EsFridayIntelligentFollowLog.class), eq("test-es-id"));
        verify(fridayIntelligentFollowLogDOMapper).updateByExampleSelective(any(ScrmFridayIntelligentFollowLogDO.class), any(ScrmFridayIntelligentFollowLogDOExample.class));
    }

    /**
     * Test when ES update fails, should catch exception and log error
     * The method should not continue to DB update as the exception is caught at the method level
     */
    @Test
    public void testWriteLogWhenESUpdateFails() throws Throwable {
        // arrange
        IntelligentFollowResultDTO mqMessageDTO = new IntelligentFollowResultDTO();
        mqMessageDTO.setEsId("test-es-id");
        mqMessageDTO.setUserId(123L);
        mqMessageDTO.setAppId("test-app-id");
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        stepExecuteResultDTO.setCode(200);
        stepExecuteResultDTO.setMsg("success");
        stepExecuteResultDTO.setSuccess(true);
        try (MockedStatic<Cat> mockedCat = Mockito.mockStatic(Cat.class)) {
            doThrow(new RuntimeException("ES update failed")).when(dashBoardESWriteDomainService).updateESDocByIdSelective(anyString(), any(), anyString());
            // act
            invokeWriteLogMethod(mqMessageDTO, stepExecuteResultDTO);
            // assert
            verify(dashBoardESWriteDomainService).updateESDocByIdSelective(eq("scrm_friday_intelligent_follow_log"), any(EsFridayIntelligentFollowLog.class), eq("test-es-id"));
            // The method should NOT try to update DB after ES failure
            // because the exception is caught at the method level
            verifyNoInteractions(fridayIntelligentFollowLogDOMapper);
            // Verify Cat.logEvent was called
            mockedCat.verify(() -> Cat.logEvent(eq("ScrmIntelligentFollowResultDelayConsumer.WriteLog.Error"), eq("e")));
        }
    }

    /**
     * Test with null consumer processor - removed as it's not a valid test case
     * The actual implementation doesn't handle null consumer case,
     * so testing it would just be testing Java's NPE behavior
     */
    /**
     * Test when DB update fails, should catch exception and log error
     */
    @Test
    public void testWriteLogWhenDBUpdateFails() throws Throwable {
        // arrange
        IntelligentFollowResultDTO mqMessageDTO = new IntelligentFollowResultDTO();
        mqMessageDTO.setEsId("test-es-id");
        mqMessageDTO.setUserId(123L);
        mqMessageDTO.setAppId("test-app-id");
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        stepExecuteResultDTO.setCode(200);
        stepExecuteResultDTO.setMsg("success");
        stepExecuteResultDTO.setSuccess(true);
        try (MockedStatic<Cat> mockedCat = Mockito.mockStatic(Cat.class)) {
            doThrow(new RuntimeException("DB update failed")).when(fridayIntelligentFollowLogDOMapper).updateByExampleSelective(any(), any());
            // act
            invokeWriteLogMethod(mqMessageDTO, stepExecuteResultDTO);
            // assert
            verify(dashBoardESWriteDomainService).updateESDocByIdSelective(eq("scrm_friday_intelligent_follow_log"), any(EsFridayIntelligentFollowLog.class), eq("test-es-id"));
            verify(fridayIntelligentFollowLogDOMapper).updateByExampleSelective(any(ScrmFridayIntelligentFollowLogDO.class), any(ScrmFridayIntelligentFollowLogDOExample.class));
            // Verify Cat.logEvent was called
            mockedCat.verify(() -> Cat.logEvent(eq("ScrmIntelligentFollowResultDelayConsumer.WriteLog.Error"), eq("e")));
        }
    }

    /**
     * Test when mqMessageDTO is null, should handle gracefully
     */
    @Test
    public void testWriteLogWhenMqMessageDTOIsNull() throws Throwable {
        // arrange
        IntelligentFollowResultDTO mqMessageDTO = null;
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        stepExecuteResultDTO.setCode(200);
        stepExecuteResultDTO.setMsg("success");
        stepExecuteResultDTO.setSuccess(true);
        try (MockedStatic<Cat> mockedCat = Mockito.mockStatic(Cat.class)) {
            // act
            invokeWriteLogMethod(mqMessageDTO, stepExecuteResultDTO);
            // assert
            // Should catch NPE and log error
            mockedCat.verify(() -> Cat.logEvent(eq("ScrmIntelligentFollowResultDelayConsumer.WriteLog.Error"), eq("e")));
            // No interactions with services due to NPE
            verifyNoInteractions(dashBoardESWriteDomainService);
            verifyNoInteractions(fridayIntelligentFollowLogDOMapper);
        }
    }
}
