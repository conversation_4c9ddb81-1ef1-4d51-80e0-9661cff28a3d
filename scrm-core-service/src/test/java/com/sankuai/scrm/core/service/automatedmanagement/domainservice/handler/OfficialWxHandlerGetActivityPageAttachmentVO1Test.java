package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.UploadWxMediaAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.AttachmentVO;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.MiniProgramVO;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import com.sankuai.scrm.core.service.infrastructure.vo.WechatMediaResult;
import java.util.HashMap;
import java.util.Map;
import lombok.Builder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerGetActivityPageAttachmentVO1Test {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private WeChatTokenAcl weChatTokenAcl;

    @Mock
    private UploadWxMediaAcl uploadWxMediaAcl;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO;

    private ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO;

    private ScrmAmProcessOrchestrationProductActivityPageDO pageInfo;

    private Map<String, AttachmentVO> existedAttachmentMap;

    private StepExecuteResultDTO stepExecuteResultDTO;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setProcessOrchestrationNodeId(1L);
        actionAttachmentDTO.setId(1L);
        supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageInfo.setId(1L);
        pageInfo.setActivityTitle("Test Activity");
        pageInfo.setThumbPicUrl("http://test.com/pic.jpg");
        pageInfo.setMiniProgramAppId("wx123456");
        existedAttachmentMap = new HashMap<>();
        stepExecuteResultDTO = new StepExecuteResultDTO();
    }

    /**
     * Test when token retrieval fails
     */
    @Test
    public void testGetActivityPageAttachmentVO_TokenRetrievalFails() throws Throwable {
        // arrange
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("testCorpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(null);
        // act
        AttachmentVO result = officialWxHandler.getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, stepExecuteResultDTO);
        // assert
        assertNull(result);
        assertFalse(stepExecuteResultDTO.isSuccess());
        assertEquals("供给类官方途径群发消息-构建活动页失败-获取token失败", stepExecuteResultDTO.getMsg());
    }

    /**
     * Test when media upload fails
     */
    @Test
    public void testGetActivityPageAttachmentVO_MediaUploadFails() throws Throwable {
        // arrange
        WeChatTokenResult tokenResult = WeChatTokenResult.builder().access_token("test_token").errcode(0).errmsg("ok").build();
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("testCorpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(tokenResult);
        when(uploadWxMediaAcl.uploadWxTmpMedia(anyString(), any(), anyString(), anyBoolean())).thenReturn(null);
        // act
        AttachmentVO result = officialWxHandler.getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, stepExecuteResultDTO);
        // assert
        assertNull(result);
        assertFalse(stepExecuteResultDTO.isSuccess());
        assertEquals("供给类官方途径群发消息-构建活动页失败-图片上传失败", stepExecuteResultDTO.getMsg());
    }

    /**
     * Test when pageInfo has missing required data
     */
    @Test
    public void testGetActivityPageAttachmentVO_InvalidPageInfo() throws Throwable {
        // arrange
        pageInfo.setThumbPicUrl(null);
        WeChatTokenResult tokenResult = WeChatTokenResult.builder().access_token("test_token").errcode(0).errmsg("ok").build();
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("testCorpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(tokenResult);
        // act
        AttachmentVO result = officialWxHandler.getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, stepExecuteResultDTO);
        // assert
        assertNull(result);
        assertFalse(stepExecuteResultDTO.isSuccess());
    }
}
