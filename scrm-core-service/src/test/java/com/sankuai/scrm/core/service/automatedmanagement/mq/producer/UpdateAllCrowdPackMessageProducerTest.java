package com.sankuai.scrm.core.service.automatedmanagement.mq.producer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.dz.srcm.automatedmanagement.dto.mq.RefinementOperationExecuteMessage;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Field;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class UpdateAllCrowdPackMessageProducerTest {

    @InjectMocks
    private UpdateAllCrowdPackMessageProducer producer;

    @Mock
    private IProducerProcessor mockProducer;

    private MockedStatic<JsonUtils> mockedJsonUtils;

    private IProducerProcessor originalProducer;

    @BeforeEach
    public void setUp() throws Exception {
        // Save the original producer
        Field producerField = UpdateAllCrowdPackMessageProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        originalProducer = (IProducerProcessor) producerField.get(null);
        // Set the mock producer
        producerField.set(null, mockProducer);
        // Mock JsonUtils.toStr
        mockedJsonUtils = mockStatic(JsonUtils.class);
    }

    @AfterEach
    public void tearDown() throws Exception {
        // Restore the original producer
        Field producerField = UpdateAllCrowdPackMessageProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, originalProducer);
        // Close the mocked static
        mockedJsonUtils.close();
    }

    /**
     * Test sending a persona crowd pack update message successfully on the first attempt
     */
    @Test
    public void testSendPersonaCrowdPackUpdateTaskExecuteMessage_SuccessFirstAttempt() throws Exception {
        // arrange
        Integer personaId = 123;
        Long packId = 456L;
        String appId = "test-app";
        String crowdPackVersion = "1.0";
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(mockProducer.sendMessage(anyString())).thenReturn(successResult);
        ArgumentCaptor<RefinementOperationExecuteMessage> messageCaptor = ArgumentCaptor.forClass(RefinementOperationExecuteMessage.class);
        mockedJsonUtils.when(() -> JsonUtils.toStr(messageCaptor.capture())).thenReturn("serialized-message");
        // act
        producer.sendPersonaCrowdPackUpdateTaskExecuteMessage(personaId, packId, appId, crowdPackVersion);
        // assert
        verify(mockProducer, times(1)).sendMessage("serialized-message");
        RefinementOperationExecuteMessage capturedMessage = messageCaptor.getValue();
        assertEquals(personaId, capturedMessage.getPersonaId());
        assertEquals(packId, capturedMessage.getCrowdPackId());
        assertEquals(appId, capturedMessage.getAppId());
        assertEquals(crowdPackVersion, capturedMessage.getCrowdPackVersion());
        assertEquals(ScrmProcessOrchestrationTaskTypeEnum.PERSONA_CROWD_PACK_UPDATE.getValue(), capturedMessage.getTaskType());
    }

    /**
     * Test sending a persona crowd pack update message with success on the second attempt
     */
    @Test
    public void testSendPersonaCrowdPackUpdateTaskExecuteMessage_SuccessSecondAttempt() throws Exception {
        // arrange
        Integer personaId = 123;
        Long packId = 456L;
        String appId = "test-app";
        String crowdPackVersion = "1.0";
        ProducerResult failResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(mockProducer.sendMessage(anyString())).thenReturn(failResult).thenReturn(successResult);
        mockedJsonUtils.when(() -> JsonUtils.toStr(any(RefinementOperationExecuteMessage.class))).thenReturn("serialized-message");
        // act
        producer.sendPersonaCrowdPackUpdateTaskExecuteMessage(personaId, packId, appId, crowdPackVersion);
        // assert
        verify(mockProducer, times(2)).sendMessage("serialized-message");
    }

    /**
     * Test sending a persona crowd pack update message with success on the third attempt
     */
    @Test
    public void testSendPersonaCrowdPackUpdateTaskExecuteMessage_SuccessThirdAttempt() throws Exception {
        // arrange
        Integer personaId = 123;
        Long packId = 456L;
        String appId = "test-app";
        String crowdPackVersion = "1.0";
        ProducerResult failResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(mockProducer.sendMessage(anyString())).thenReturn(failResult).thenReturn(failResult).thenReturn(successResult);
        mockedJsonUtils.when(() -> JsonUtils.toStr(any(RefinementOperationExecuteMessage.class))).thenReturn("serialized-message");
        // act
        producer.sendPersonaCrowdPackUpdateTaskExecuteMessage(personaId, packId, appId, crowdPackVersion);
        // assert
        verify(mockProducer, times(3)).sendMessage("serialized-message");
    }

    /**
     * Test sending a persona crowd pack update message with all attempts failing
     */
    @Test
    public void testSendPersonaCrowdPackUpdateTaskExecuteMessage_AllAttemptsFail() throws Exception {
        // arrange
        Integer personaId = 123;
        Long packId = 456L;
        String appId = "test-app";
        String crowdPackVersion = "1.0";
        ProducerResult failResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        when(mockProducer.sendMessage(anyString())).thenReturn(failResult);
        mockedJsonUtils.when(() -> JsonUtils.toStr(any(RefinementOperationExecuteMessage.class))).thenReturn("serialized-message");
        // act
        producer.sendPersonaCrowdPackUpdateTaskExecuteMessage(personaId, packId, appId, crowdPackVersion);
        // assert
        verify(mockProducer, times(3)).sendMessage("serialized-message");
    }

    /**
     * Test sending a persona crowd pack update message with exception on first attempt but success on second
     */
    @Test
    public void testSendPersonaCrowdPackUpdateTaskExecuteMessage_ExceptionThenSuccess() throws Exception {
        // arrange
        Integer personaId = 123;
        Long packId = 456L;
        String appId = "test-app";
        String crowdPackVersion = "1.0";
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(mockProducer.sendMessage(anyString())).thenThrow(new RuntimeException("Send failed")).thenReturn(successResult);
        mockedJsonUtils.when(() -> JsonUtils.toStr(any(RefinementOperationExecuteMessage.class))).thenReturn("serialized-message");
        // act
        producer.sendPersonaCrowdPackUpdateTaskExecuteMessage(personaId, packId, appId, crowdPackVersion);
        // assert
        verify(mockProducer, times(2)).sendMessage("serialized-message");
    }

    /**
     * Test sending a persona crowd pack update message with all attempts throwing exceptions
     */
    @Test
    public void testSendPersonaCrowdPackUpdateTaskExecuteMessage_AllAttemptsException() throws Exception {
        // arrange
        Integer personaId = 123;
        Long packId = 456L;
        String appId = "test-app";
        String crowdPackVersion = "1.0";
        when(mockProducer.sendMessage(anyString())).thenThrow(new RuntimeException("Send failed"));
        mockedJsonUtils.when(() -> JsonUtils.toStr(any(RefinementOperationExecuteMessage.class))).thenReturn("serialized-message");
        // act
        producer.sendPersonaCrowdPackUpdateTaskExecuteMessage(personaId, packId, appId, crowdPackVersion);
        // assert
        verify(mockProducer, times(3)).sendMessage("serialized-message");
    }

    /**
     * Test sending a persona crowd pack update message with null result
     */
    @Test
    public void testSendPersonaCrowdPackUpdateTaskExecuteMessage_NullResult() throws Exception {
        // arrange
        Integer personaId = 123;
        Long packId = 456L;
        String appId = "test-app";
        String crowdPackVersion = "1.0";
        when(mockProducer.sendMessage(anyString())).thenReturn(null);
        mockedJsonUtils.when(() -> JsonUtils.toStr(any(RefinementOperationExecuteMessage.class))).thenReturn("serialized-message");
        // act
        producer.sendPersonaCrowdPackUpdateTaskExecuteMessage(personaId, packId, appId, crowdPackVersion);
        // assert
        verify(mockProducer, times(3)).sendMessage("serialized-message");
    }

    /**
     * Test sending a persona crowd pack update message with null parameters
     */
    @Test
    public void testSendPersonaCrowdPackUpdateTaskExecuteMessage_NullParameters() throws Exception {
        // arrange
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(mockProducer.sendMessage(anyString())).thenReturn(successResult);
        ArgumentCaptor<RefinementOperationExecuteMessage> messageCaptor = ArgumentCaptor.forClass(RefinementOperationExecuteMessage.class);
        mockedJsonUtils.when(() -> JsonUtils.toStr(messageCaptor.capture())).thenReturn("serialized-message");
        // act
        producer.sendPersonaCrowdPackUpdateTaskExecuteMessage(null, null, null, null);
        // assert
        verify(mockProducer, times(1)).sendMessage("serialized-message");
        RefinementOperationExecuteMessage capturedMessage = messageCaptor.getValue();
        assertEquals(null, capturedMessage.getPersonaId());
        assertEquals(null, capturedMessage.getCrowdPackId());
        assertEquals(null, capturedMessage.getAppId());
        assertEquals(null, capturedMessage.getCrowdPackVersion());
        assertEquals(ScrmProcessOrchestrationTaskTypeEnum.PERSONA_CROWD_PACK_UPDATE.getValue(), capturedMessage.getTaskType());
    }
}
