package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.ExcelUploadingPackDataDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAMRealtimeSceneAndProcessMapDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAMRealtimeSceneAndProcessMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.RefinementOperationExecuteMessageProducer;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.group.domain.GroupMemberDomainService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;

@RunWith(MockitoJUnitRunner.class)
public class ExecuteWriteDomainService_ExecuteUnFinishedExecuteLogTest {

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock(lenient = true)
    private RefinementOperationExecuteMessageProducer refinementOperationExecuteMessageProducer;

    private List<ScrmAmProcessOrchestrationExecuteLogDO> executeLogDOS;

    @Mock
    private ScrmAMRealtimeSceneAndProcessMapDOMapper scrmAMRealtimeSceneAndProcessMapDOMapper;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock(lenient = true)
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private ContactUserDomain contactUserDomain;

    @Mock
    private GroupMemberDomainService groupMemberDomainService;

    @Mock
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Before
    public void setUp() {
        executeLogDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDOS.add(executeLogDO);
    }

    @Test
    public void testExecuteUnFinishedExecuteLog_NoData() throws Throwable {
        when(executeLogDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        boolean result = executeWriteDomainService.executeUnFinishedExecuteLog();
        verify(executeLogDOMapper, times(1)).selectByExample(any());
        assertTrue(result);
    }

    @Test
    public void testExecuteUnFinishedExecuteLog_Normal() throws Throwable {
        List<ScrmAmProcessOrchestrationExecuteLogDO> executeLogDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.PAUSED.getValue().byteValue());
        executeLogDO.setFinalCheckTime(new Date());
        executeLogDO.setResumeTime(new Date());
        executeLogDOS.add(executeLogDO);
        when(executeLogDOMapper.selectByExample(any())).thenReturn(executeLogDOS);
        boolean result = executeWriteDomainService.executeUnFinishedExecuteLog();
        verify(executeLogDOMapper, times(1)).selectByExample(any());
        assertTrue(result);
    }

    @Test(expected = Exception.class)
    public void testExecuteUnFinishedExecuteLog_Exception() throws Throwable {
        when(executeLogDOMapper.selectByExample(any())).thenThrow(new RuntimeException());
        executeWriteDomainService.executeUnFinishedExecuteLog();
    }

    /**
     * Test case for blank wxUnionId
     */
    @Test
    public void testExecuteRealTimeTask_WhenWxUnionIdIsBlank() throws Throwable {
        // arrange
        Long sceneId = 1L;
        String wxUnionId = "";
        // act
        StepExecuteResultDTO result = executeWriteDomainService.executeRealTimeTask(sceneId, wxUnionId,"" );
        // assert
        assertFalse(result.isSuccess());
        assertEquals((long) ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.UNION_ID_IS_NULL.getCode(), (long) result.getCode());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.UNION_ID_IS_NULL.getDesc(), result.getMsg());
        // verify no interactions with mocks
        verifyNoInteractions(scrmAMRealtimeSceneAndProcessMapDOMapper, processOrchestrationReadDomainService);
    }

    /**
     * Test case for invalid process orchestration status
     */
    @Test
    public void testExecuteRealTimeTask_WhenProcessOrchestrationStatusInvalid() throws Throwable {
        // arrange
        Long sceneId = 1L;
        String wxUnionId = "test_union_id";
        List<ScrmAMRealtimeSceneAndProcessMapDO> mapDOList = new ArrayList<>();
        ScrmAMRealtimeSceneAndProcessMapDO mapDO = new ScrmAMRealtimeSceneAndProcessMapDO();
        mapDO.setProcessOrchestrationId(1L);
        mapDO.setProcessOrchestrationVersion("1.0");
        mapDOList.add(mapDO);
        ScrmProcessOrchestrationDTO orchestrationDTO = new ScrmProcessOrchestrationDTO();
        // Set status as CLOSED
        orchestrationDTO.setStatus((byte) 0);
        orchestrationDTO.setValidVersion("1.0");
        orchestrationDTO.setId(1L);
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any())).thenReturn(mapDOList);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(eq(1L))).thenReturn(CompletableFuture.completedFuture(orchestrationDTO));
        // act
        StepExecuteResultDTO result = executeWriteDomainService.executeRealTimeTask(sceneId, wxUnionId, "");
        // assert
        assertFalse(result.isSuccess());
        assertEquals((long) ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.NOT_IN_TIME.getCode(), (long) result.getCode());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.NOT_IN_TIME.getDesc(), result.getMsg());
        // verify interactions
        verify(scrmAMRealtimeSceneAndProcessMapDOMapper).selectByExample(any());
        verify(processOrchestrationReadDomainService).queryProcessOrchestrationDetailFuture(eq(1L));
    }

    /**
     * Test case for null process orchestration DTO
     */
    @Test
    public void testExecuteRealTimeTask_WhenProcessOrchestrationDTOIsNull() throws Throwable {
        // arrange
        Long sceneId = 1L;
        String wxUnionId = "test_union_id";
        // First, mock empty list to simulate no matching scene
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        // act
        StepExecuteResultDTO result = executeWriteDomainService.executeRealTimeTask(sceneId, wxUnionId, "");
        // assert
        assertFalse(result.isSuccess());
        assertEquals((long) ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.NOT_IN_TIME.getCode(), (long) result.getCode());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.NOT_IN_TIME.getDesc(), result.getMsg());
        // verify interactions
        verify(scrmAMRealtimeSceneAndProcessMapDOMapper).selectByExample(any());
        verifyNoInteractions(processOrchestrationReadDomainService);
    }

    /**
     * 测试getScrmCrowdPackDetailInfoDTO方法，当查询到用户信息时
     */
    @Test
    public void testGetScrmCrowdPackDetailInfoDTO_WhenUserFound() throws ExecutionException, InterruptedException, TimeoutException {
        String userUnionId = "testUnionId";
        String appId = "testAppId";
        ScrmProcessOrchestrationDTO scrmProcessOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        scrmProcessOrchestrationDTO.setAppId(appId);
        ScrmCrowdPackDetailInfoDTO expectedDTO = new ScrmCrowdPackDetailInfoDTO();
        // doNothing().when(executeWriteDomainService).updateCrowdPackSubTask(any());
        when(crowdPackReadDomainService.queryUserByUnionId(eq(userUnionId), eq(appId))).thenReturn(expectedDTO);

        ScrmCrowdPackDetailInfoDTO result = executeWriteDomainService.getScrmCrowdPackDetailInfoDTO(scrmProcessOrchestrationDTO, userUnionId);

        verify(crowdPackReadDomainService, times(1)).queryUserByUnionId(eq(userUnionId), eq(appId));
        assert result == expectedDTO;
    }

    /**
     * 测试getScrmCrowdPackDetailInfoDTO方法，当未查询到用户信息且更新任务执行成功时
     */
    @Test
    public void testGetScrmCrowdPackDetailInfoDTO_WhenUserNotFoundAndUpdateSuccess() throws Exception {
        String userUnionId = "testUnionId";
        String appId = "testAppId";
        ScrmProcessOrchestrationDTO scrmProcessOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        scrmProcessOrchestrationDTO.setAppId(appId);
        when(crowdPackReadDomainService.queryUserByUnionId(eq(userUnionId), eq(appId))).thenReturn(null).thenReturn(new ScrmCrowdPackDetailInfoDTO());
        // doNothing().when(executeWriteDomainService).updateCrowdPackSubTask(any());

        ScrmCrowdPackDetailInfoDTO result = executeWriteDomainService.getScrmCrowdPackDetailInfoDTO(scrmProcessOrchestrationDTO, userUnionId);

        verify(crowdPackReadDomainService, times(1)).queryUserByUnionId(eq(userUnionId), eq(appId));
        // verify(executeWriteDomainService, times(1)).updateCrowdPackSubTask(Collections.singletonList(userUnionId));
        assert result == null;
    }

    /**
     * 测试getScrmCrowdPackDetailInfoDTO方法，当未查询到用户信息且更新任务执行异常时
     */
    @Test(expected = Exception.class)
    public void testGetScrmCrowdPackDetailInfoDTO_WhenUserNotFoundAndUpdateThrowsException() throws Exception {
        String userUnionId = "testUnionId";
        String appId = "testAppId";
        ScrmProcessOrchestrationDTO scrmProcessOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        scrmProcessOrchestrationDTO.setAppId(appId);
        doNothing().when(executeWriteDomainService).updateCrowdPackSubTask(any());
        when(crowdPackReadDomainService.queryUserByUnionId(eq(userUnionId), eq(appId))).thenReturn(null);
        doThrow(new RuntimeException()).when(executeWriteDomainService).updateCrowdPackSubTask(any());

        executeWriteDomainService.getScrmCrowdPackDetailInfoDTO(scrmProcessOrchestrationDTO, userUnionId);
    }

    /**
     * Test case for empty data list
     */
    @Test
    public void testConcurrentUpdateExcelCrowdPackAsync_WithEmptyList() throws Throwable {
        // arrange
        String appId = "testAppId";
        Long packId = 1L;
        String packVersion = "v1";
        List<ExcelUploadingPackDataDTO> dataList = new ArrayList<>();
        // act
        executeWriteDomainService.concurrentUpdateExcelCrowdPackAsync(appId, dataList, packId, packVersion);
        // assert
        verify(crowdPackWriteDomainService, never()).insertCrowdPackDetailInfo(any(), anyString(), anyString(), anyString(), anyString());
    }
}
