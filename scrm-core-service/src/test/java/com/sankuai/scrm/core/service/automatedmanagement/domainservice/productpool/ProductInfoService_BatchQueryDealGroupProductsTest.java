package com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool;

import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class ProductInfoService_BatchQueryDealGroupProductsTest {

    @InjectMocks
    private ProductInfoService productInfoService;

    @Mock(lenient = true)
    private DealGroupQueryService queryCenterDealGroupQueryService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test case for empty input list
     */
    @Test
    public void testBatchQueryDealGroupProducts_EmptyInput() throws Throwable {
        // arrange
        List<Long> ids = new ArrayList<>();
        // act
        List<DealGroupDTO> result = productInfoService.batchQueryDealGroupProducts(ids);
        // assert
        assertTrue(result.isEmpty());
        verify(queryCenterDealGroupQueryService, never()).queryByDealGroupIds(any());
    }

    /**
     * Test case for input list with size <= BULK_MAX_QUERY_SIZE
     */
    @Test
    public void testBatchQueryDealGroupProducts_SingleBatch() throws Throwable {
        // arrange
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult result = new QueryDealGroupListResult();
        result.setList(Arrays.asList(new DealGroupDTO(), new DealGroupDTO(), new DealGroupDTO()));
        response.setData(result);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        // act
        List<DealGroupDTO> actualResult = productInfoService.batchQueryDealGroupProducts(ids);
        // assert
        assertEquals(3, actualResult.size());
        verify(queryCenterDealGroupQueryService, times(1)).queryByDealGroupIds(any());
    }

    /**
     * Test case for input list with size > BULK_MAX_QUERY_SIZE (multiple batches)
     */
    @Test
    public void testBatchQueryDealGroupProducts_MultipleBatches() throws Throwable {
        // arrange
        List<Long> ids = new ArrayList<>();
        for (long i = 1; i <= 25; i++) {
            ids.add(i);
        }
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult result = new QueryDealGroupListResult();
        result.setList(Arrays.asList(new DealGroupDTO(), new DealGroupDTO()));
        response.setData(result);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        // act
        List<DealGroupDTO> actualResult = productInfoService.batchQueryDealGroupProducts(ids);
        // assert
        assertEquals(4, actualResult.size());
        verify(queryCenterDealGroupQueryService, times(2)).queryByDealGroupIds(any());
    }

    /**
     * Test case for partial success response
     */
    @Test
    public void testBatchQueryDealGroupProducts_PartialSuccess() throws Throwable {
        // arrange
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(100);
        QueryDealGroupListResult result = new QueryDealGroupListResult();
        result.setList(Arrays.asList(new DealGroupDTO(), new DealGroupDTO()));
        response.setData(result);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        // act
        List<DealGroupDTO> actualResult = productInfoService.batchQueryDealGroupProducts(ids);
        // assert
        assertEquals(2, actualResult.size());
        verify(queryCenterDealGroupQueryService, times(1)).queryByDealGroupIds(any());
    }

    /**
     * Test case for error response
     */
    @Test
    public void testBatchQueryDealGroupProducts_ErrorResponse() throws Throwable {
        // arrange
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(500);
        response.setMessage("Internal Server Error");
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        // act
        List<DealGroupDTO> actualResult = productInfoService.batchQueryDealGroupProducts(ids);
        // assert
        assertTrue(actualResult.isEmpty());
        verify(queryCenterDealGroupQueryService, times(1)).queryByDealGroupIds(any());
    }

    /**
     * Test case for exception thrown during service call
     */
    @Test
    public void testBatchQueryDealGroupProducts_ExceptionThrown() throws Throwable {
        // arrange
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenThrow(new RuntimeException("Service unavailable"));
        // act
        List<DealGroupDTO> actualResult = productInfoService.batchQueryDealGroupProducts(ids);
        // assert
        assertTrue(actualResult.isEmpty());
        verify(queryCenterDealGroupQueryService, times(1)).queryByDealGroupIds(any());
    }
}
