package com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentContentDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationAttachmentTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActionAttachmentDO;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmProcessOrchestrationActionAttachmentConverterTest {

    private ScrmProcessOrchestrationActionAttachmentConverter converter = new ScrmProcessOrchestrationActionAttachmentConverter();

    @Test
    public void testConvertToDTOWhenResourceIsNull() throws Throwable {
        ScrmAmProcessOrchestrationActionAttachmentDO resource = null;
        ScrmProcessOrchestrationActionAttachmentDTO result = converter.convertToDTO(resource);
        assertNull(result);
    }

    @Test
    public void testConvertToDTOWhenAttachmentTypeIdIsSupplyAndAttachmentContentIsNotBlank() throws Throwable {
        ScrmAmProcessOrchestrationActionAttachmentDO resource = new ScrmAmProcessOrchestrationActionAttachmentDO();
        resource.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.SUPPLY.getValue());
        // Provide a valid JSON string for deserialization
        resource.setAttachmentContent("{\"supplyType\":1,\"productId\":\"testProduct\",\"productType\":2,\"marketingCopy\":\"Test Marketing Copy\",\"marketingCopySource\":3,\"supplyScope\":4,\"hotTagList\":\"testTag\",\"shelfName\":\"Test Shelf\",\"jumpPageType\":5,\"jumpUrl\":\"http://example.com\",\"topproductids\":\"testIds\",\"headpicUrl\":\"http://example.com/pic\"}");
        ScrmProcessOrchestrationActionAttachmentDTO result = converter.convertToDTO(resource);
        assertNotNull(result.getAttachmentSupplyDetailDTO());
        assertNull(result.getAttachmentContentDetailDTO());
    }

    @Test
    public void testConvertToDTOWhenAttachmentTypeIdIsGreaterThanZeroAndAttachmentContentIsNotBlank() throws Throwable {
        ScrmAmProcessOrchestrationActionAttachmentDO resource = new ScrmAmProcessOrchestrationActionAttachmentDO();
        resource.setAttachmentTypeId(1);
        // Provide a valid JSON string for deserialization
        resource.setAttachmentContent("{\"title\":\"Test Title\",\"contentUrl\":\"http://example.com/content\",\"desc\":\"Test Description\",\"appId\":\"testApp\",\"picUrl\":\"http://example.com/pic\",\"originAppId\":\"testOriginApp\",\"icon\":\"http://example.com/icon\"}");
        ScrmProcessOrchestrationActionAttachmentDTO result = converter.convertToDTO(resource);
        assertNull(result.getAttachmentSupplyDetailDTO());
        assertNotNull(result.getAttachmentContentDetailDTO());
    }

    @Test
    public void testConvertToDTOWhenAttachmentTypeIdIsNotSupplyAndAttachmentContentIsBlank() throws Throwable {
        ScrmAmProcessOrchestrationActionAttachmentDO resource = new ScrmAmProcessOrchestrationActionAttachmentDO();
        resource.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.PICTURE.getValue());
        resource.setAttachmentContent("");
        ScrmProcessOrchestrationActionAttachmentDTO result = converter.convertToDTO(resource);
        assertNull(result.getAttachmentSupplyDetailDTO());
        assertNull(result.getAttachmentContentDetailDTO());
    }

    @Test
    public void testConvertToDTOWhenAttachmentTypeIdIsNotSupplyAndAttachmentContentIsNotBlank() throws Throwable {
        ScrmAmProcessOrchestrationActionAttachmentDO resource = new ScrmAmProcessOrchestrationActionAttachmentDO();
        resource.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.PICTURE.getValue());
        // Provide a valid JSON string for deserialization
        resource.setAttachmentContent("{\"title\":\"Test Title\",\"contentUrl\":\"http://example.com/content\",\"desc\":\"Test Description\",\"appId\":\"testApp\",\"picUrl\":\"http://example.com/pic\",\"originAppId\":\"testOriginApp\",\"icon\":\"http://example.com/icon\"}");
        ScrmProcessOrchestrationActionAttachmentDTO result = converter.convertToDTO(resource);
        assertNull(result.getAttachmentSupplyDetailDTO());
        assertNotNull(result.getAttachmentContentDetailDTO());
    }
}
