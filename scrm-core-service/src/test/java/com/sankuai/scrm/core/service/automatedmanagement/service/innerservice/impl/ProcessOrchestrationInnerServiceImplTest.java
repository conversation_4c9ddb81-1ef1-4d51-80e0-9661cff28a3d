package com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.impl;

import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationGoalDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.request.LoadProcessOrchestrationQRCodeRequest;
import com.sankuai.dz.srcm.automatedmanagement.request.QueryProcessOrchestrationBranchStatisticsRequest;
import com.sankuai.dz.srcm.automatedmanagement.request.UpdateProcessOrchestrationRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.UpdateProcessOrchestrationResultVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmCrowdPackUpdateStrategyInfoVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationGoalVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationInfoVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationNodeVO;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2vo.corwdpack.ScrmCrowdPackUpdateStrategyInfoVOConverter;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2vo.corwdpack.ScrmProcessOrchestrationGoalVOConverter;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2vo.corwdpack.ScrmProcessOrchestrationInfoVOConverter;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2vo.corwdpack.ScrmProcessOrchestrationNodeVOConverter;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.DdlResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.OfficialWxHandler;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationWriteDomainService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProcessOrchestrationInnerServiceImplTest {

    @InjectMocks
    private ProcessOrchestrationInnerServiceImpl processOrchestrationInnerService;

    @Mock(lenient = true)
    private ProcessOrchestrationWriteDomainService processOrchestrationWriteDomainService;

    @Mock(lenient = true)
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationInfoVOConverter processOrchestrationInfoVOConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationGoalVOConverter scrmProcessOrchestrationGoalVOConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationNodeVOConverter scrmProcessOrchestrationNodeVOConverter;

    @Mock(lenient = true)
    private ScrmCrowdPackUpdateStrategyInfoVOConverter scrmCrowdPackUpdateStrategyInfoVOConverter;

    @Mock(lenient = true)
    private OfficialWxHandler officialWxHandler;

    private UpdateProcessOrchestrationRequest request;

    private static final String processOrchestrationQRCodeTestStr = "{ \"id\" : 263, \"name\" : \"企微朋友圈转发测试\", \"processOrchestrationType\" : 4, \"cron\" : \"\", \"beginTime\" : 1730169240000, \"endTime\" : 1730342040000, \"status\" : 1, \"validVersion\" : \"1731484313242\", \"updateTime\" : 1731484313000, \"creatorId\" : \"wangyonghao02\", \"lastUpdaterId\" : \"wangyonghao02\", \"participationRestrict\" : true, \"participationRestrictionsCycle\" : 0, \"participationRestrictionsTimes\" : 0, \"appId\" : \"yimei\", \"previewPic\" : \"\", \"cronComment\" : \"2024-10-30 10:34:00\", \"executorType\" : 1, \"executorList\" : [ { \"executorId\" : \"WangXueFei\", \"executorName\" : \"王雪飞\", \"executorType\" : 2 } ], \"crowdPackType\" : 3, \"crowdPackIdList\" : [ ], \"groupIdList\" : [ \"wrb_61EQAAnSZJ1NP3R3yqsyJtbL_4Kw\", \"wrb_61EQAA-ilg22ELq_-cIv-FURWR6A\" ], \"groupInfoList\" : [ { \"groupName\" : \"群发消息测试1025-2\", \"owner\" : \"WangXueFei\", \"robotList\" : [ \"困猪/:pig\" ], \"groupId\" : \"wrb_61EQAAnSZJ1NP3R3yqsyJtbL_4Kw\", \"createDate\" : 1729837100000, \"memberCount\" : 4 }, { \"groupName\" : \"群发消息测试1025-1\", \"owner\" : \"WangXueFei\", \"robotList\" : [ \"困猪/:pig\" ], \"groupId\" : \"wrb_61EQAA-ilg22ELq_-cIv-FURWR6A\", \"createDate\" : 1729837050000, \"memberCount\" : 4 } ], \"crowdPackUpdateStrategyInfoDTO\" : null, \"goalDTO\" : { \"id\" : 800, \"checkTime\" : \"7\", \"checkTimeUnit\" : \"3\", \"status\" : 1, \"processOrchestrationId\" : 263, \"processOrchestrationVersion\" : \"1731484313242\", \"goalType\" : 1, \"careNegativeResult\" : true, \"positiveResultHighlightList\" : [ 90002, 90003 ], \"negativeResultHighlightList\" : null, \"goalConditionList\" : [ ] }, \"negativeGoalDTO\" : { \"id\" : 799, \"checkTime\" : \"7\", \"checkTimeUnit\" : \"3\", \"status\" : 1, \"processOrchestrationId\" : 263, \"processOrchestrationVersion\" : \"1731484313242\", \"goalType\" : 2, \"careNegativeResult\" : true, \"positiveResultHighlightList\" : [ 90002, 90003 ], \"negativeResultHighlightList\" : null, \"goalConditionList\" : [ ] }, \"nodeMediumDTO\" : { \"processOrchestrationNodeDTOList\" : [ { \"nodeId\" : 0, \"preNodeId\" : -1, \"nodeType\" : 4, \"id\" : 1190, \"processOrchestrationId\" : 263, \"processOrchestrationVersion\" : \"1731484313242\", \"childrenNodes\" : [ 1730255027731 ] }, { \"nodeId\" : 1730255027731, \"preNodeId\" : 0, \"nodeType\" : 2, \"id\" : 1191, \"processOrchestrationId\" : 263, \"processOrchestrationVersion\" : \"1731484313242\", \"childrenNodes\" : [ ] } ], \"conditionMap\" : { }, \"actionMap\" : { \"1730255027731\" : { \"id\" : 510, \"actionId\" : 1, \"processOrchestrationId\" : 263, \"processOrchestrationVersion\" : \"1731484313242\", \"processOrchestrationNodeId\" : 1730255027731, \"actionType\" : 3, \"actionSubType\" : 6, \"updateTime\" : 1731484313000, \"contentType\" : 1, \"contentList\" : null } }, \"actionContentMap\" : { \"1730255027731-1\" : [ { \"id\" : 509, \"actionId\" : 1, \"contentId\" : 1, \"processOrchestrationId\" : 263, \"processOrchestrationVersion\" : \"1731484313242\", \"processOrchestrationNodeId\" : 1730255027731, \"content\" : \"\", \"updateTime\" : 1731484313000, \"contentType\" : 1, \"attachmentDTOList\" : null } ] }, \"actionAttachmentMap\" : { \"1730255027731-1-1\" : [ { \"id\" : 488, \"contentId\" : 1, \"actionId\" : 1, \"attachmentTypeId\" : 7, \"attachmentContent\" : \"{\\\"supplyType\\\":2,\\\"productType\\\":1,\\\"shelfName\\\":\\\"\\\",\\\"jumpPageType\\\":1,\\\"jumpUrl\\\":\\\"\\\",\\\"productId\\\":\\\"757123783\\\",\\\"supplyScope\\\":1,\\\"marketingCopy\\\":\\\"111\\\",\\\"marketingCopySource\\\":2,\\\"hotTagList\\\":\\\"\\\",\\\"topproductids\\\":\\\"\\\",\\\"headpicUrl\\\":\\\"https://msstest.sankuai.com/scrm-s3/poster-17314843069686918099920396694881.png\\\",\\\"posterSetting\\\":{\\\"title\\\":\\\"社群专项特价111\\\",\\\"image\\\":\\\"\\\",\\\"benefitInfo\\\":\\\"\\\"},\\\"qrCodeUrl\\\":\\\"https://img.meituan.net/beautyimg/af9454758f6b7eb5d80486bfa8389e8540032.jpg\\\"}\", \"processOrchestrationId\" : 263, \"processOrchestrationVersion\" : \"1731484313242\", \"processOrchestrationNodeId\" : 1730255027731, \"updateTime\" : null, \"attachmentSupplyDetailDTO\" : { \"supplyType\" : 2, \"productId\" : \"757123783\", \"productType\" : 1, \"marketingCopy\" : \"111\", \"marketingCopySource\" : 2, \"supplyScope\" : 1, \"hotTagList\" : \"\", \"shelfName\" : \"\", \"jumpPageType\" : 1, \"jumpUrl\" : \"\", \"topproductids\" : \"\", \"headpicUrl\" : \"https://msstest.sankuai.com/scrm-s3/poster-17314843069686918099920396694881.png\", \"qrCodeUrl\" : \"https://img.meituan.net/beautyimg/af9454758f6b7eb5d80486bfa8389e8540032.jpg\" }, \"attachmentContentDetailDTO\" : null } ] } }, \"executePlanDTO\" : null }";


    @Before
    public void setUp() {
        request = new UpdateProcessOrchestrationRequest();
        ScrmProcessOrchestrationInfoVO processOrchestrationInfo = new ScrmProcessOrchestrationInfoVO();
        processOrchestrationInfo.setId(1L);
        request.setProcessOrchestrationInfo(processOrchestrationInfo);
        ScrmProcessOrchestrationGoalVO processOrchestrationGoal = new ScrmProcessOrchestrationGoalVO();
        processOrchestrationGoal.setId(1L);
        processOrchestrationGoal.setStatus((byte) 1);
        processOrchestrationGoal.setCheckTime("2023-04-01");
        processOrchestrationGoal.setCheckTimeUnit("DAY");
        processOrchestrationGoal.setCareNegativeResult(false);
        processOrchestrationGoal.setHighLightList(Collections.singletonList(1L));
        processOrchestrationGoal.setNegativeResultHighlightList(Collections.singletonList(2L));
        processOrchestrationGoal.setGoalConditionList(Collections.emptyList());
        processOrchestrationGoal.setNegativeResultConditionList(Collections.emptyList());
        request.getProcessOrchestrationInfo().setStatus(3);
        request.setProcessOrchestrationGoal(processOrchestrationGoal);
        request.setProcessOrchestrationNodeList(Collections.emptyList());
        request.setScrmCrowdPackUpdateStrategyInfoVO(new ScrmCrowdPackUpdateStrategyInfoVO());
    }

    /**
     * Tests the updateProcessOrchestration method under normal conditions.
     */
    @Test
    public void testUpdateProcessOrchestrationNormal() throws Throwable {
        // Arrange
        DdlResultDTO ddlResultDTO = new DdlResultDTO();
        ddlResultDTO.setSuccess(true);
        when(processOrchestrationWriteDomainService.updateProcessOrchestration(any())).thenReturn(ddlResultDTO);
        when(processOrchestrationInfoVOConverter.convertToDTO(any(ScrmProcessOrchestrationInfoVO.class))).thenReturn(new ScrmProcessOrchestrationDTO());
        ScrmProcessOrchestrationGoalDTO goalDTO = new ScrmProcessOrchestrationGoalDTO();
        goalDTO.setGoalType((byte) 1);
        List<ScrmProcessOrchestrationGoalDTO> mockGoalDTOList = Collections.singletonList(goalDTO);
        when(scrmProcessOrchestrationGoalVOConverter.convertToDTO(any(ScrmProcessOrchestrationGoalVO.class))).thenReturn(mockGoalDTOList);
        when(scrmProcessOrchestrationNodeVOConverter.convertToDTO(anyList())).thenReturn(new ScrmProcessOrchestrationNodeMediumDTO());
        when(scrmCrowdPackUpdateStrategyInfoVOConverter.convertToDTO(any(ScrmCrowdPackUpdateStrategyInfoVO.class))).thenReturn(new ScrmCrowdPackUpdateStrategyInfoDTO());
        // Act
        UpdateProcessOrchestrationResultVO result = processOrchestrationInnerService.updateProcessOrchestration(request);
        // Assert
        assertNotNull(result);
        assertTrue(result.getSuccess());
        verify(processOrchestrationWriteDomainService, times(1)).updateProcessOrchestration(any());
        verify(scrmProcessOrchestrationNodeVOConverter, times(1)).convertToDTO(anyList());
        verify(scrmCrowdPackUpdateStrategyInfoVOConverter, times(1)).convertToDTO(any(ScrmCrowdPackUpdateStrategyInfoVO.class));
    }

    /**
     * Tests the updateProcessOrchestration method under exceptional conditions.
     */
    @Test(expected = Exception.class)
    public void testUpdateProcessOrchestrationException() throws Throwable {
        // Arrange
        // Act
        processOrchestrationInnerService.updateProcessOrchestration(request);
        // Assert
        // Expected exception
    }

    /**
     * 测试正常情况下的方法执行
     */
    @Test(expected = NullPointerException.class)
    public void testQueryBranchStatisticsInfoByIdSuccess() throws Throwable {

        ScrmProcessOrchestrationNodeMediumDTO mediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        CompletableFuture<ScrmProcessOrchestrationNodeMediumDTO> future = CompletableFuture.completedFuture(mediumDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationStatisticDetailFuture(anyLong(), any(), any())).thenReturn(future);

        List<ScrmProcessOrchestrationNodeVO> nodeVOList = new ArrayList<>();
        when(scrmProcessOrchestrationNodeVOConverter.convertToDO(any(ScrmProcessOrchestrationNodeMediumDTO.class))).thenReturn(nodeVOList);

        // arrange
        QueryProcessOrchestrationBranchStatisticsRequest request = new QueryProcessOrchestrationBranchStatisticsRequest();

        // act
        List<ScrmProcessOrchestrationNodeVO> result = processOrchestrationInnerService.queryBranchStatisticsInfoById(request);

        // assert
        assertNotNull(result);
    }

    /**
     * 测试方法执行时抛出TimeoutException的情况
     */
    @Test(expected = RuntimeException.class)
    public void testQueryBranchStatisticsInfoByIdTimeoutException() throws Throwable {

        ScrmProcessOrchestrationNodeMediumDTO mediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        CompletableFuture<ScrmProcessOrchestrationNodeMediumDTO> future = CompletableFuture.completedFuture(mediumDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationStatisticDetailFuture(anyLong(), any(), any())).thenReturn(future);

        List<ScrmProcessOrchestrationNodeVO> nodeVOList = new ArrayList<>();
        when(scrmProcessOrchestrationNodeVOConverter.convertToDO(any(ScrmProcessOrchestrationNodeMediumDTO.class))).thenReturn(nodeVOList);

        // arrange
        when(processOrchestrationReadDomainService.queryProcessOrchestrationStatisticDetailFuture(anyLong(), any(), any())).thenThrow(new TimeoutException());

        QueryProcessOrchestrationBranchStatisticsRequest request = new QueryProcessOrchestrationBranchStatisticsRequest();

        // act
        processOrchestrationInnerService.queryBranchStatisticsInfoById(request);

        // assert is handled by the expected exception
    }

    /**
     * 测试方法执行时抛出InterruptedException的情况
     */
    @Test(expected = RuntimeException.class)
    public void testQueryBranchStatisticsInfoByIdInterruptedException() throws Throwable {

        ScrmProcessOrchestrationNodeMediumDTO mediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        CompletableFuture<ScrmProcessOrchestrationNodeMediumDTO> future = CompletableFuture.completedFuture(mediumDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationStatisticDetailFuture(anyLong(), any(), any())).thenReturn(future);

        List<ScrmProcessOrchestrationNodeVO> nodeVOList = new ArrayList<>();
        when(scrmProcessOrchestrationNodeVOConverter.convertToDO(any(ScrmProcessOrchestrationNodeMediumDTO.class))).thenReturn(nodeVOList);

        // arrange
        when(processOrchestrationReadDomainService.queryProcessOrchestrationStatisticDetailFuture(anyLong(), any(), any())).thenThrow(new InterruptedException());

        QueryProcessOrchestrationBranchStatisticsRequest request = new QueryProcessOrchestrationBranchStatisticsRequest();

        // act
        processOrchestrationInnerService.queryBranchStatisticsInfoById(request);

        // assert is handled by the expected exception
    }

    /**
     * 测试方法执行时抛出ExecutionException的情况
     */
    @Test(expected = RuntimeException.class)
    public void testQueryBranchStatisticsInfoByIdExecutionException() throws Throwable {

        ScrmProcessOrchestrationNodeMediumDTO mediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        CompletableFuture<ScrmProcessOrchestrationNodeMediumDTO> future = CompletableFuture.completedFuture(mediumDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationStatisticDetailFuture(anyLong(), any(), any())).thenReturn(future);

        List<ScrmProcessOrchestrationNodeVO> nodeVOList = new ArrayList<>();
        when(scrmProcessOrchestrationNodeVOConverter.convertToDO(any(ScrmProcessOrchestrationNodeMediumDTO.class))).thenReturn(nodeVOList);

        // arrange
        when(processOrchestrationReadDomainService.queryProcessOrchestrationStatisticDetailFuture(anyLong(), any(), any())).thenThrow(new ExecutionException(new Throwable()));

        QueryProcessOrchestrationBranchStatisticsRequest request = new QueryProcessOrchestrationBranchStatisticsRequest();

        // act
        processOrchestrationInnerService.queryBranchStatisticsInfoById(request);

        // assert is handled by the expected exception
    }

    @Test
    public void testLoadProcessOrchestrationQRCode_ProcessOrchestrationNotFound() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationQRCodeTestStr, ScrmProcessOrchestrationDTO.class);

        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(any(Long.class), any(String.class), any())).thenReturn(CompletableFuture.completedFuture(processOrchestrationDTO));

        LoadProcessOrchestrationQRCodeRequest request = new LoadProcessOrchestrationQRCodeRequest();
        request.setAppId("yimei");
        request.setProcessOrchestrationId(263L);
        request.setProcessOrchestrationNodeId(1730255027731L);
        request.setQrCodeWidth(300);
        when(officialWxHandler.buildProductQRCodeUrl(any(), any(), any(), anyInt())).thenReturn("test");
        String result =  processOrchestrationInnerService.loadProcessOrchestrationQRCode(request);
        assertNotNull(result);
    }

    /**
     * 测试流程编排节点未找到的情况
     */
    @Test
    public void testLoadProcessOrchestrationQRCode_ProcessOrchestrationNodeNotFound() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationQRCodeTestStr, ScrmProcessOrchestrationDTO.class);

        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(any(Long.class), any(String.class), any())).thenReturn(CompletableFuture.completedFuture(processOrchestrationDTO));

        LoadProcessOrchestrationQRCodeRequest request = new LoadProcessOrchestrationQRCodeRequest();
        request.setAppId("yimei");
        request.setProcessOrchestrationId(263L);
        request.setProcessOrchestrationNodeId(1730255027731L);
        request.setQrCodeWidth(300);
        when(officialWxHandler.buildProductQRCodeUrl(any(), any(), any(), anyInt())).thenReturn("test");
        String result = processOrchestrationInnerService.loadProcessOrchestrationQRCode(request);

        assertNotNull(result);
    }

    /**
     * 测试流程编排节点不是供给类的情况
     */
    @Test
    public void testLoadProcessOrchestrationQRCode_ProcessOrchestrationNodeNotSupplyType() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationQRCodeTestStr, ScrmProcessOrchestrationDTO.class);

        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(any(Long.class), any(String.class), any())).thenReturn(CompletableFuture.completedFuture(processOrchestrationDTO));


        LoadProcessOrchestrationQRCodeRequest request = new LoadProcessOrchestrationQRCodeRequest();
        request.setAppId("yimei");
        request.setProcessOrchestrationId(263L);
        request.setProcessOrchestrationNodeId(1730255027731L);
        request.setQrCodeWidth(300);
        when(officialWxHandler.buildProductQRCodeUrl(any(), any(), any(), anyInt())).thenReturn("test");
        String result = processOrchestrationInnerService.loadProcessOrchestrationQRCode(request);
        assertNotNull(result);
    }

    /**
     * 测试成功获取产品类型为1的二维码URL
     */
    @Test
    public void testLoadProcessOrchestrationQRCode_SuccessProductType1() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationQRCodeTestStr, ScrmProcessOrchestrationDTO.class);

        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(any(Long.class), any(String.class), any())).thenReturn(CompletableFuture.completedFuture(processOrchestrationDTO));

        LoadProcessOrchestrationQRCodeRequest request = new LoadProcessOrchestrationQRCodeRequest();
        request.setAppId("yimei");
        request.setProcessOrchestrationId(263L);
        request.setProcessOrchestrationNodeId(1730255027731L);
        request.setQrCodeWidth(300);
        when(officialWxHandler.buildProductQRCodeUrl(any(), any(), any(), anyInt())).thenReturn("test");
        String result = processOrchestrationInnerService.loadProcessOrchestrationQRCode(request);

        assertNotNull(result);
    }
}
