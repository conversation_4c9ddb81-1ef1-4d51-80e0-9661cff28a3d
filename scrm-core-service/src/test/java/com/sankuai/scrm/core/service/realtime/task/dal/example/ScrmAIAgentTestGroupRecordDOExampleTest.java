package com.sankuai.scrm.core.service.realtime.task.dal.example;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.realtime.task.dal.example.ScrmAIAgentTestGroupRecordDOExample.Criteria;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ScrmAIAgentTestGroupRecordDOExampleTest {

    /**
     * 测试or()方法基础功能 - 创建并返回新的Criteria对象
     */
    @Test
    void testOr_ShouldCreateAndReturnNewCriteria() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int initialSize = example.getOredCriteria().size();
        // act
        Criteria result = example.or();
        // assert
        assertNotNull(result, "返回的Criteria不应为null");
        assertEquals(initialSize + 1, example.getOredCriteria().size(), "oredCriteria大小应增加1");
        assertSame(result, example.getOredCriteria().get(initialSize), "返回的Criteria应与添加到列表中的对象相同");
    }

    /**
     * 测试多次调用or()方法 - 应持续添加新Criteria到列表中
     */
    @Test
    void testOr_MultipleCalls_ShouldAddMultipleCriteria() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int initialSize = example.getOredCriteria().size();
        // act
        Criteria result1 = example.or();
        Criteria result2 = example.or();
        Criteria result3 = example.or();
        // assert
        assertEquals(initialSize + 3, example.getOredCriteria().size(), "oredCriteria大小应增加3");
        assertSame(result1, example.getOredCriteria().get(initialSize), "第一个Criteria应正确添加");
        assertSame(result2, example.getOredCriteria().get(initialSize + 1), "第二个Criteria应正确添加");
        assertSame(result3, example.getOredCriteria().get(initialSize + 2), "第三个Criteria应正确添加");
        assertNotSame(result1, result2, "不同的Criteria对象应不同");
        assertNotSame(result2, result3, "不同的Criteria对象应不同");
    }

    /**
     * 测试返回的Criteria对象是否可用 - 应能正常调用其方法
     */
    @Test
    void testOr_ReturnedCriteria_ShouldBeFunctional() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act & assert
        assertDoesNotThrow(() -> {
            Criteria criteria = example.or();
            // 验证返回的Criteria是否可用
            criteria.andIdEqualTo(1L);
        }, "返回的Criteria应能正常使用方法");
    }

    /**
     * 测试or()方法与createCriteria()方法的交互
     */
    @Test
    void testOr_WithCreateCriteria_ShouldWorkTogether() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int initialSize = example.getOredCriteria().size();
        // act
        Criteria manualCriteria = example.createCriteria();
        Criteria orCriteria = example.or();
        // assert
        assertEquals(initialSize + 2, example.getOredCriteria().size(), "两种方式创建的Criteria都应添加到列表");
        assertNotSame(manualCriteria, orCriteria, "不同方式创建的Criteria应不同");
    }

    /**
     * 测试or()方法在空列表情况下的行为
     */
    @Test
    void testOr_WithEmptyCriteriaList_ShouldAddFirstCriteria() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        example.clear();
        // act
        Criteria result = example.or();
        // assert
        assertEquals(1, example.getOredCriteria().size(), "应在空列表中添加第一个Criteria");
        assertSame(result, example.getOredCriteria().get(0), "返回的Criteria应与列表中的对象相同");
    }

    @Test
    void testCreateCriteriaInternal_ReturnsNonNullCriteria() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertNotNull(result, "Created Criteria should not be null");
        assertInstanceOf(ScrmAIAgentTestGroupRecordDOExample.Criteria.class, result, "Result should be instance of Criteria");
    }

    @Test
    void testCreateCriteriaInternal_ReturnsNewInstanceEachTime() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria firstInstance = example.createCriteriaInternal();
        ScrmAIAgentTestGroupRecordDOExample.Criteria secondInstance = example.createCriteriaInternal();
        // assert
        assertNotSame(firstInstance, secondInstance, "Each call should return a new instance");
    }

    @Test
    void testCreateCriteriaInternal_ReturnsCriteriaWithEmptyList() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertNotNull(result.getCriteria(), "Criteria list should not be null");
        assertTrue(result.getCriteria().isEmpty(), "Criteria list should be empty initially");
    }

    @Test
    void testCreateCriteriaInternal_AfterOtherOperations() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        example.setDistinct(true);
        example.setOrderByClause("test");
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertNotNull(result, "Should still return valid Criteria after other operations");
    }

    @Test
    void testCreateCriteriaInternal_ReturnsProperType() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = spy(new ScrmAIAgentTestGroupRecordDOExample());
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria result = example.createCriteriaInternal();
        // assert
        verify(example, times(1)).createCriteriaInternal();
        assertNotNull(result, "Result should not be null");
        assertEquals(0, result.getCriteria().size(), "New Criteria should have empty criteria list");
    }

    @Test
    public void testCreateCriteriaWhenOredCriteriaIsEmpty() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria result = example.createCriteria();
        // assert
        assertNotNull(result);
        assertEquals(1, example.getOredCriteria().size());
        assertSame(result, example.getOredCriteria().get(0));
    }

    @Test
    public void testCreateCriteriaWhenOredCriteriaIsNotEmpty() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        ScrmAIAgentTestGroupRecordDOExample.Criteria existingCriteria = new ScrmAIAgentTestGroupRecordDOExample.Criteria();
        example.getOredCriteria().add(existingCriteria);
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria result = example.createCriteria();
        // assert
        assertNotNull(result);
        assertEquals(1, example.getOredCriteria().size());
        assertSame(existingCriteria, example.getOredCriteria().get(0));
        assertNotSame(result, existingCriteria);
    }

    @Test
    public void testCreateCriteriaCallsCreateCriteriaInternal() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = spy(new ScrmAIAgentTestGroupRecordDOExample());
        // act
        example.createCriteria();
        // assert
        verify(example, times(1)).createCriteriaInternal();
    }

    @Test
    public void testCreateCriteriaReturnsCorrectType() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria result = example.createCriteria();
        // assert
        assertTrue(result instanceof ScrmAIAgentTestGroupRecordDOExample.Criteria);
    }

    @Test
    public void testCreateCriteriaMultipleCalls() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria first = example.createCriteria();
        ScrmAIAgentTestGroupRecordDOExample.Criteria second = example.createCriteria();
        // assert
        assertEquals(1, example.getOredCriteria().size());
        assertSame(first, example.getOredCriteria().get(0));
        assertNotSame(first, second);
    }

    @Test
    public void testPageNormalCase() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int page = 2;
        int pageSize = 10;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.page(page, pageSize);
        // assert
        assertEquals(page * pageSize, example.getOffset());
        assertEquals(pageSize, example.getRows());
        assertSame(example, result);
    }

    @Test
    public void testPageFirstPageCase() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int page = 0;
        int pageSize = 10;
        // act
        example.page(page, pageSize);
        // assert
        assertEquals(0, example.getOffset());
        assertEquals(pageSize, example.getRows());
    }

    @Test
    public void testPageLargeNumberCase() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int page = Integer.MAX_VALUE / 100;
        int pageSize = 100;
        // act
        example.page(page, pageSize);
        // assert
        assertEquals((Integer.MAX_VALUE / 100) * 100, example.getOffset());
        assertEquals(pageSize, example.getRows());
    }

    @Test
    public void testPageNullPageCase() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        Integer page = null;
        int pageSize = 10;
        // act & assert
        assertThrows(NullPointerException.class, () -> example.page(page, pageSize));
    }

    @Test
    public void testPageNullPageSizeCase() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int page = 1;
        Integer pageSize = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> example.page(page, pageSize));
    }

    @Test
    public void testPageZeroPageSizeCase() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int page = 1;
        int pageSize = 0;
        // act
        example.page(page, pageSize);
        // assert
        assertEquals(0, example.getOffset());
        assertEquals(0, example.getRows());
    }

    @Test
    public void testPageNegativePageCase() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int page = -1;
        int pageSize = 10;
        // act
        example.page(page, pageSize);
        // assert
        assertEquals(-10, example.getOffset());
        assertEquals(10, example.getRows());
    }

    @Test
    public void testPageMethodChaining() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.page(1, 10).page(2, 20);
        // assert
        assertEquals(40, example.getOffset());
        assertEquals(20, example.getRows());
        assertSame(example, result);
    }

    @Test
    void testLimitWithPositiveOffsetAndRows() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int offset = 10;
        int rows = 20;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(offset, result.getOffset(), "offset应被正确设置为10");
        assertEquals(rows, result.getRows(), "rows应被正确设置为20");
    }

    @Test
    void testLimitWithZeroOffset() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int offset = 0;
        int rows = 20;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(offset, result.getOffset(), "offset为0应被正确设置");
        assertEquals(rows, result.getRows(), "rows应被正确设置为20");
    }

    @Test
    void testLimitWithZeroRows() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int offset = 10;
        int rows = 0;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(offset, result.getOffset(), "offset应被正确设置为10");
        assertEquals(rows, result.getRows(), "rows为0应被正确设置");
    }

    @Test
    void testLimitWithNullOffset() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        Integer offset = null;
        int rows = 20;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertNull(result.getOffset(), "offset为null应被正确设置");
        assertEquals(rows, result.getRows(), "rows应被正确设置为20");
    }

    @Test
    void testLimitWithNullRows() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int offset = 10;
        Integer rows = null;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(offset, result.getOffset(), "offset应被正确设置为10");
        assertNull(result.getRows(), "rows为null应被正确设置");
    }

    @Test
    void testLimitWithNegativeOffset() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int offset = -5;
        int rows = 20;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(offset, result.getOffset(), "负值offset应被正确设置");
        assertEquals(rows, result.getRows(), "rows应被正确设置为20");
    }

    @Test
    void testLimitWithNegativeRows() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int offset = 10;
        int rows = -5;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(offset, result.getOffset(), "offset应被正确设置为10");
        assertEquals(rows, result.getRows(), "负值rows应被正确设置");
    }

    @Test
    void testLimitMethodChaining() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(10, 20).limit(30, 40);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(30, result.getOffset(), "第二次调用的offset应覆盖第一次的值");
        assertEquals(40, result.getRows(), "第二次调用的rows应覆盖第一次的值");
    }

    @Test
    void testLimitWithPositiveNumber() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int testRows = 10;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(testRows);
        // assert
        assertEquals(testRows, result.getRows(), "rows值应被正确设置");
        assertSame(example, result, "应返回当前实例以支持链式调用");
    }

    @Test
    void testLimitWithZero() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(0);
        // assert
        assertEquals(0, result.getRows(), "rows值应被设置为0");
        assertSame(example, result);
    }

    @Test
    void testLimitWithNull() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(null);
        // assert
        assertNull(result.getRows(), "rows值应被设置为null");
        assertSame(example, result);
    }

    @Test
    void testLimitWithMaxValue() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(Integer.MAX_VALUE);
        // assert
        assertEquals(Integer.MAX_VALUE, result.getRows(), "rows值应被设置为Integer.MAX_VALUE");
        assertSame(example, result);
    }

    @Test
    void testLimitWithNegativeNumber() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(-1);
        // assert
        assertEquals(-1, result.getRows(), "rows值应被设置为负数");
        assertSame(example, result);
    }
}
