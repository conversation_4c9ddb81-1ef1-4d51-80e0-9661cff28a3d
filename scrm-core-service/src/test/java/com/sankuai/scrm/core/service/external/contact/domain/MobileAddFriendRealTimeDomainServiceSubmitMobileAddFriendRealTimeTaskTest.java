package com.sankuai.scrm.core.service.external.contact.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.external.contact.enums.MobileAddFriendRealTimeTaskFailTypeEnum;
import com.sankuai.dz.srcm.external.contact.enums.MobileAddFriendRealTimeTaskStatuEnum;
import com.sankuai.inf.kms.pangolin.api.service.IEncryptService;
import com.sankuai.scrm.core.service.external.contact.bo.MobileAddFriendRealTimeEditBO;
import com.sankuai.scrm.core.service.external.contact.config.FriendLionConfig;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ScrmMobileAddFriendTaskDO;
import com.sankuai.scrm.core.service.external.contact.dal.example.ScrmMobileAddFriendTaskDOExample;
import com.sankuai.scrm.core.service.external.contact.dal.mapper.ext.ExtScrmMobileAddFriendTaskDOMapper;
import com.sankuai.scrm.core.service.external.contact.mq.producer.MobileAddFriendRealTimeMsgProducer;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MobileAddFriendRealTimeDomainServiceSubmitMobileAddFriendRealTimeTaskTest {

    @Mock
    private ExtScrmMobileAddFriendTaskDOMapper mobileAddFriendTaskDOMapper;

    @Mock
    private MobileAddFriendRealTimeMsgProducer mobileAddFriendRealTimeMsgProducer;

    @Mock
    private IEncryptService phoneEncryptService;

    @InjectMocks
    private MobileAddFriendRealTimeDomainService mobileAddFriendRealTimeDomainService;

    private static final Long TASK_ID = 123L;

    private static final String ACCOUNT_ID = "test_account";

    private static final int DAILY_LIMIT = 10;

    @BeforeEach
    void setUp() {
        FriendLionConfig.limit = DAILY_LIMIT;
    }

    private ScrmMobileAddFriendTaskDO createTestTask() {
        ScrmMobileAddFriendTaskDO task = new ScrmMobileAddFriendTaskDO();
        task.setId(TASK_ID);
        task.setAccountId(ACCOUNT_ID);
        task.setAddNumber("test_number");
        task.setNumberType(1);
        task.setTaskStatus(MobileAddFriendRealTimeTaskStatuEnum.NOT_STARTED.getCode());
        return task;
    }

    /**
     * Test case when task doesn't exist (query returns empty list)
     */
    @Test
    public void testSubmitMobileAddFriendRealTimeTaskWhenTaskNotExist() throws Throwable {
        // arrange
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(new ArrayList<>());
        // act
        mobileAddFriendRealTimeDomainService.submitMobileAddFriendRealTimeTask(TASK_ID);
        // assert
        verify(mobileAddFriendTaskDOMapper).updateByPrimaryKeySelective(argThat(task -> task.getId().equals(TASK_ID) && task.getTaskStatus().equals(MobileAddFriendRealTimeTaskStatuEnum.FAILED.getCode()) && task.getFailType().equals(MobileAddFriendRealTimeTaskFailTypeEnum.DECRYPT_FAIL.getCode())));
    }

    /**
     * Test case when task exists but daily limit is reached
     */
    @Test
    public void testSubmitMobileAddFriendRealTimeTaskWhenDailyLimitReached() throws Throwable {
        // arrange
        ScrmMobileAddFriendTaskDO task = createTestTask();
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(Arrays.asList(task));
        when(mobileAddFriendTaskDOMapper.countByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn((long) DAILY_LIMIT);
        // act
        mobileAddFriendRealTimeDomainService.submitMobileAddFriendRealTimeTask(TASK_ID);
        // assert
        verify(mobileAddFriendTaskDOMapper).updateByPrimaryKeySelective(argThat(updatedTask -> updatedTask.getId().equals(TASK_ID) && updatedTask.getTaskStatus().equals(MobileAddFriendRealTimeTaskStatuEnum.FAILED.getCode()) && updatedTask.getFailType().equals(MobileAddFriendRealTimeTaskFailTypeEnum.LIMIT.getCode())));
    }

    /**
     * Test case when task exists and should be scheduled for future execution
     */
    @Test
    public void testSubmitMobileAddFriendRealTimeTaskScheduleForFuture() throws Throwable {
        // arrange
        ScrmMobileAddFriendTaskDO task = createTestTask();
        ScrmMobileAddFriendTaskDO previousTask = createTestTask();
        previousTask.setExecuteTime(new Date(System.currentTimeMillis() - 10000));
        // Mock for different queries
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenAnswer(invocation -> {
            ScrmMobileAddFriendTaskDOExample example = invocation.getArgument(0);
            if (example.getOredCriteria().isEmpty()) {
                return new ArrayList<>();
            }
            List<ScrmMobileAddFriendTaskDOExample.Criteria> criteria = example.getOredCriteria();
            if (criteria.get(0).getCriteria().stream().anyMatch(c -> c.getValue() != null && c.getValue().equals(TASK_ID))) {
                return Arrays.asList(task);
            }
            if (criteria.get(0).getCriteria().stream().anyMatch(c -> c.getValue() != null && c.getValue().equals(ACCOUNT_ID))) {
                return Arrays.asList(previousTask);
            }
            return new ArrayList<>();
        });
        when(mobileAddFriendTaskDOMapper.countByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(0L);
        // act
        mobileAddFriendRealTimeDomainService.submitMobileAddFriendRealTimeTask(TASK_ID);
        // assert
        ArgumentCaptor<ScrmMobileAddFriendTaskDO> taskCaptor = ArgumentCaptor.forClass(ScrmMobileAddFriendTaskDO.class);
        verify(mobileAddFriendTaskDOMapper, atLeast(1)).updateByPrimaryKeySelective(taskCaptor.capture());
        List<ScrmMobileAddFriendTaskDO> capturedTasks = taskCaptor.getAllValues();
        ScrmMobileAddFriendTaskDO lastUpdate = capturedTasks.get(capturedTasks.size() - 1);
        assertNotNull(lastUpdate.getExecuteTime());
        assertTrue(lastUpdate.getExecuteTime().after(previousTask.getExecuteTime()));
        verify(mobileAddFriendRealTimeMsgProducer).sendDelayMsg(eq(TASK_ID), anyLong());
    }

    /**
     * Test case when decryption fails during task processing
     */
    @Test
    public void testSubmitMobileAddFriendRealTimeTaskWhenDecryptionFails() throws Throwable {
        // arrange
        ScrmMobileAddFriendTaskDO task = createTestTask();
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(Arrays.asList(task));
        when(phoneEncryptService.decryptUTF8String(anyString())).thenThrow(new GeneralSecurityException("decrypt error"));
        // act
        mobileAddFriendRealTimeDomainService.submitMobileAddFriendRealTimeTask(TASK_ID);
        // assert
        verify(mobileAddFriendTaskDOMapper).updateByPrimaryKeySelective(argThat(updatedTask -> updatedTask.getId().equals(TASK_ID) && updatedTask.getTaskStatus().equals(MobileAddFriendRealTimeTaskStatuEnum.FAILED.getCode()) && updatedTask.getFailType().equals(MobileAddFriendRealTimeTaskFailTypeEnum.DECRYPT_FAIL.getCode())));
    }
}
