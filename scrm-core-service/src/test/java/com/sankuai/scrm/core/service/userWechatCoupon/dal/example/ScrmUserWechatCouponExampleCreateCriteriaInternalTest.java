package com.sankuai.scrm.core.service.userWechatCoupon.dal.example;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmUserWechatCouponExampleCreateCriteriaInternalTest {

    private ScrmUserWechatCouponExample example;

    @Before
    public void setUp() {
        example = new ScrmUserWechatCouponExample();
    }

    /**
     * 测试 createCriteriaInternal 方法是否能正确创建并返回一个新的 Criteria 对象
     */
    @Test
    public void testCreateCriteriaInternal() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        // act
        ScrmUserWechatCouponExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertNotNull(result);
    }

    /**
     * 测试 clear 方法
     */
    @Test
    public void testClear() {
        // arrange
        example.setOrderByClause("test");
        example.setDistinct(true);
        example.setRows(10);
        example.setOffset(10);
        example.createCriteria();
        // act
        example.clear();
        // assert
        assertTrue(example.getOredCriteria().isEmpty());
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertNull(example.getRows());
        assertNull(example.getOffset());
    }

    /**
     * 测试limit方法，输入正常的非负整数值
     */
    @Test
    public void testLimitNormal() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        Integer rows = 5;
        // act
        ScrmUserWechatCouponExample result = example.limit(rows);
        // assert
        assertEquals(rows, result.getRows());
    }

    /**
     * 测试limit方法，输入null
     */
    @Test
    public void testLimitNull() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        Integer rows = null;
        // act
        ScrmUserWechatCouponExample result = example.limit(rows);
        // assert
        assertNull(result.getRows());
    }

    /**
     * 测试 limit 方法，offset 和 rows 都为正常值
     */
    @Test
    public void testLimitWithNormalValue() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        Integer offset = 10;
        Integer rows = 20;
        // act
        ScrmUserWechatCouponExample result = example.limit(offset, rows);
        // assert
        assertSame("方法应返回自身实例以实现链式调用", example, result);
        assertEquals("offset应被正确设置", offset, example.getOffset());
        assertEquals("rows应被正确设置", rows, example.getRows());
    }

    /**
     * 测试 limit 方法，offset 和 rows 都为边界值
     */
    @Test
    public void testLimitWithBoundaryValue() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        Integer offset = 0;
        Integer rows = Integer.MAX_VALUE;
        // act
        ScrmUserWechatCouponExample result = example.limit(offset, rows);
        // assert
        assertSame("方法应返回自身实例以实现链式调用", example, result);
        assertEquals("offset应被正确设置", offset, example.getOffset());
        assertEquals("rows应被正确设置", rows, example.getRows());
    }

    /**
     * 测试 limit 方法，offset 和 rows 都为异常值
     */
    @Test
    public void testLimitWithExceptionValue() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        Integer offset = null;
        Integer rows = null;
        // act
        ScrmUserWechatCouponExample result = example.limit(offset, rows);
        // assert
        assertSame("方法应返回自身实例以实现链式调用", example, result);
        assertNull("offset应被正确设置为null", example.getOffset());
        assertNull("rows应被正确设置为null", example.getRows());
    }

    /**
     * 测试 limit 方法，offset 为正常值，rows 为异常值
     */
    @Test
    public void testLimitWithMixedValue() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        Integer offset = 10;
        Integer rows = null;
        // act
        ScrmUserWechatCouponExample result = example.limit(offset, rows);
        // assert
        assertSame("方法应返回自身实例以实现链式调用", example, result);
        assertEquals("offset应被正确设置", offset, example.getOffset());
        assertNull("rows应被正确设置为null", example.getRows());
    }

    /**
     * Test normal pagination scenario with positive values
     */
    @Test
    public void testPage_WithPositiveValues() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int page = 3;
        int pageSize = 20;
        // act
        ScrmUserWechatCouponExample result = example.page(page, pageSize);
        // assert
        assertEquals(Integer.valueOf(60), result.getOffset());
        assertEquals(Integer.valueOf(20), result.getRows());
        assertSame(example, result);
    }

    /**
     * Test boundary case with minimum valid values
     */
    @Test
    public void testPage_WithMinimumValidValues() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int page = 0;
        int pageSize = 1;
        // act
        ScrmUserWechatCouponExample result = example.page(page, pageSize);
        // assert
        assertEquals(Integer.valueOf(0), result.getOffset());
        assertEquals(Integer.valueOf(1), result.getRows());
        assertSame(example, result);
    }

    /**
     * Test case with negative page number
     */
    @Test
    public void testPage_WithNegativePageNumber() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int page = -2;
        int pageSize = 10;
        // act
        ScrmUserWechatCouponExample result = example.page(page, pageSize);
        // assert
        assertEquals(Integer.valueOf(-20), result.getOffset());
        assertEquals(Integer.valueOf(10), result.getRows());
        assertSame(example, result);
    }

    /**
     * Test case with negative page size
     */
    @Test
    public void testPage_WithNegativePageSize() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int page = 1;
        int pageSize = -5;
        // act
        ScrmUserWechatCouponExample result = example.page(page, pageSize);
        // assert
        assertEquals(Integer.valueOf(-5), result.getOffset());
        assertEquals(Integer.valueOf(-5), result.getRows());
        assertSame(example, result);
    }

    /**
     * Test case with both negative values
     */
    @Test
    public void testPage_WithAllNegativeValues() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int page = -1;
        int pageSize = -10;
        // act
        ScrmUserWechatCouponExample result = example.page(page, pageSize);
        // assert
        assertEquals(Integer.valueOf(10), result.getOffset());
        assertEquals(Integer.valueOf(-10), result.getRows());
        assertSame(example, result);
    }

    /**
     * Test case with zero values
     */
    @Test
    public void testPage_WithZeroValues() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int page = 0;
        int pageSize = 0;
        // act
        ScrmUserWechatCouponExample result = example.page(page, pageSize);
        // assert
        assertEquals(Integer.valueOf(0), result.getOffset());
        assertEquals(Integer.valueOf(0), result.getRows());
        assertSame(example, result);
    }

    /**
     * Test case with large values
     */
    @Test
    public void testPage_WithLargeValues() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int page = Integer.MAX_VALUE / 2;
        int pageSize = 2;
        // act
        ScrmUserWechatCouponExample result = example.page(page, pageSize);
        // assert
        assertEquals(Integer.valueOf(Integer.MAX_VALUE - 1), result.getOffset());
        assertEquals(Integer.valueOf(2), result.getRows());
        assertSame(example, result);
    }

    /**
     * Test case with null values (should throw NullPointerException)
     */
    @Test(expected = NullPointerException.class)
    public void testPage_WithNullValues() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        Integer page = null;
        Integer pageSize = null;
        // act
        example.page(page, pageSize);
        // assert - exception expected
    }

    /**
     * Test case with mocked example object
     */
    @Test
    public void testPage_WithMockedExample() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = mock(ScrmUserWechatCouponExample.class);
        when(example.page(anyInt(), anyInt())).thenCallRealMethod();
        int page = 2;
        int pageSize = 5;
        // act
        ScrmUserWechatCouponExample result = example.page(page, pageSize);
        // assert
        verify(example).page(page, pageSize);
        assertSame(example, result);
    }
}
