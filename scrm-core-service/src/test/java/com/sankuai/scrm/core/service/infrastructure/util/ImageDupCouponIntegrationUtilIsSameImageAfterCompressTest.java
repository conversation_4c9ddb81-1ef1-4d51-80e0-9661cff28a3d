package com.sankuai.scrm.core.service.infrastructure.util;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.scrm.core.service.infrastructure.config.ImgFeatureCompareStrategyConfig;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ImageDupCouponIntegrationUtilIsSameImageAfterCompressTest {

    @InjectMocks
    private ImageDupUtil imageDupUtil;

    @Mock(lenient = true)
    private RedisStoreClient redisClient;

    @Mock(lenient = true)
    private ImageDupFeaUtil imageDupFeaUtil;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试isSameWxImg方法，当输入的URL为空时应返回false
     */
    @Test
    public void testIsSameWxImgWithEmptyUrls() {
        // arrange
        String destImgUrl = "";
        String refImgUrl = "";

        // act
        boolean result = imageDupUtil.isSameWxImg(destImgUrl, refImgUrl);

        // assert
        assertFalse("当输入的URL为空时，应返回false", result);
    }

    /**
     * 测试isSameWxImg方法，当缓存中存在结果时应直接返回缓存结果
     */
    @Test
    public void testIsSameWxImgWithCacheHit() {
        // arrange
        String destImgUrl = "http://example.com/dest.jpg";
        String refImgUrl = "http://example.com/ref.jpg";
        StoreKey storeKey = new StoreKey("pchat_image_compare", destImgUrl.hashCode(), refImgUrl.hashCode());
        when(redisClient.get(storeKey)).thenReturn(true);
        when(imageDupFeaUtil.isEnabled()).thenReturn(false);
        // act
        boolean result = imageDupUtil.isSameWxImg(destImgUrl, refImgUrl);

        // assert
        assertTrue("当缓存中存在结果时，应直接返回缓存结果", result);
    }

    /**
     * 测试isSameWxImg方法，当缓存中不存在结果且特征比对开关打开时，应调用特征比对方法
     */
    @Test
    public void testIsSameWxImgWithFeatureCompareOn() {
        // arrange
        String destImgUrl = "http://example.com/dest.jpg";
        String refImgUrl = "http://example.com/ref.jpg";
        StoreKey storeKey = new StoreKey("pchat_image_compare", destImgUrl.hashCode(), refImgUrl.hashCode());
        when(redisClient.get(storeKey)).thenReturn(null);
        when(imageDupFeaUtil.isSameImage(destImgUrl, refImgUrl, true)).thenReturn(true);
        when(imageDupFeaUtil.isEnabled()).thenReturn(true);
        // act
        boolean result = imageDupUtil.isSameWxImg(destImgUrl, refImgUrl);

        // assert
        assertTrue("当缓存中不存在结果且特征比对开关打开时，应调用特征比对方法", result);
    }

    /**
     * 测试isSameWxImg方法，当发生异常时应返回false
     */
    @Test
    public void testIsSameWxImgWithException() {
        // arrange
        String destImgUrl = "http://example.com/dest.jpg";
        String refImgUrl = "http://example.com/ref.jpg";
        when(redisClient.get(any(StoreKey.class))).thenThrow(new RuntimeException());

        // act
        boolean result = imageDupUtil.isSameWxImg(destImgUrl, refImgUrl);

        // assert
        assertFalse("当发生异常时，应返回false", result);
    }
}