package com.sankuai.scrm.core.service.automatedmanagement.mq.consumer;

import com.meituan.mafka.client.consumer.IConsumerProcessor;
import org.mockito.Mockito;
import java.lang.reflect.Field;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class OrderReceiptOperateNotifyConsumerTest {

    private OrderReceiptOperateNotifyConsumer orderReceiptOperateNotifyConsumer;

    private IConsumerProcessor consumer;

    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        orderReceiptOperateNotifyConsumer = new OrderReceiptOperateNotifyConsumer();
        consumer = Mockito.mock(IConsumerProcessor.class);
        // Use reflection to inject the mock consumer
        Field consumerField = OrderReceiptOperateNotifyConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(orderReceiptOperateNotifyConsumer, consumer);
    }

    /**
     * 测试destroy方法，消费者为空的情况
     */
    @Test
    public void testDestroyConsumerIsNull() throws Throwable {
        // Use reflection to set consumer to null
        Field consumerField = OrderReceiptOperateNotifyConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(orderReceiptOperateNotifyConsumer, null);
        // act
        orderReceiptOperateNotifyConsumer.destroy();
        // assert
        verify(consumer, times(0)).close();
    }

    /**
     * 测试destroy方法，消费者不为空的情况
     */
    @Test
    public void testDestroyConsumerIsNotNull() throws Throwable {
        // act
        orderReceiptOperateNotifyConsumer.destroy();
        // assert
        verify(consumer, times(1)).close();
    }
}
