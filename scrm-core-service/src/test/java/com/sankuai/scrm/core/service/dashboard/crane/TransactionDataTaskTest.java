package com.sankuai.scrm.core.service.dashboard.crane;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.couponIntegration.enums.CreationSceneEnum;
import com.sankuai.dz.srcm.couponIntegration.enums.StatusEnum;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmCouponOrderAmountDetail;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmCouponOrderAmountDetailExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmCouponOrderAmountDetailMapper;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.dashboard.dal.dto.EsNarrowlyTransactionDataSnapshot;
import com.sankuai.scrm.core.service.dashboard.dal.dto.EsTransactionDataSnapshot;
import com.sankuai.scrm.core.service.dashboard.dal.enums.TransactionGtvQueryFieldEnum;
import com.sankuai.scrm.core.service.dashboard.domain.DashBoardESReadDomainService;
import com.sankuai.scrm.core.service.dashboard.domain.DashBoardESWriteDomainService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class TransactionDataTaskTest {

    @InjectMocks
    private TransactionDataTask transactionDataTask;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private DashBoardESReadDomainService dashBoardESReadDomainService;

    @Mock
    private DashBoardESWriteDomainService dashBoardESWriteDomainService;

    @Mock
    private ScrmCouponOrderAmountDetailMapper scrmCouponOrderAmountDetailMapper;

    @Mock
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordsMapper;

    private CorpAppConfig testCorpAppConfig;

    private LocalDate testDate;

    private ScrmSceneCouponRecords testRecord1;

    private ScrmSceneCouponRecords testRecord2;

    private ScrmCouponOrderAmountDetail testDetail1;

    private ScrmCouponOrderAmountDetail testDetail2;

    @Before
    public void setUp() {
        testCorpAppConfig = new CorpAppConfig();
        testCorpAppConfig.setCorpId("testCorpId");
        testCorpAppConfig.setAppId("testAppId");
        testDate = LocalDate.of(2024, 1, 1);
        testRecord1 = new ScrmSceneCouponRecords();
        testRecord1.setUserid(1L);
        testRecord1.setOrderid("order1");
        testRecord1.setCoupongroupid("group1");
        testRecord1.setUnifiedcouponid("coupon1");
        testRecord1.setScenetype(CreationSceneEnum.MANUAL_UPLOAD.getCode());
        testRecord1.setStatisticstatus(StatusEnum.COUNTING.getCode());
        testRecord2 = new ScrmSceneCouponRecords();
        testRecord2.setUserid(2L);
        testRecord2.setOrderid("order2");
        testRecord2.setCoupongroupid("group2");
        testRecord2.setUnifiedcouponid("coupon2");
        testRecord2.setScenetype(CreationSceneEnum.MANUAL_UPLOAD.getCode());
        testRecord2.setStatisticstatus(StatusEnum.COUNTING.getCode());
        testDetail1 = new ScrmCouponOrderAmountDetail();
        testDetail1.setOrderAmount(new BigDecimal("100.00"));
        testDetail1.setActualPayAmount(new BigDecimal("90.00"));
        testDetail2 = new ScrmCouponOrderAmountDetail();
        testDetail2.setOrderAmount(new BigDecimal("200.00"));
        testDetail2.setActualPayAmount(new BigDecimal("180.00"));
    }

    /**
     * Test getDataAndPushToEs normal flow
     */
    @Test
    public void testGetDataAndPushToEs() throws Throwable {
        // arrange
        List<CorpAppConfig> configs = Collections.singletonList(testCorpAppConfig);
        when(corpAppConfigRepository.getAllConfigs()).thenReturn(configs);
        when(dashBoardESReadDomainService.countTotalOrdersWidely(anyString(), any(), any())).thenReturn(10L);
        when(dashBoardESReadDomainService.sumGtvWidely(any(), anyString(), any())).thenReturn(new BigDecimal("1000.00"));
        when(scrmSceneCouponRecordsMapper.selectByExample(any())).thenReturn(Arrays.asList(testRecord1, testRecord2));
        when(scrmCouponOrderAmountDetailMapper.selectByExample(any())).thenReturn(Arrays.asList(testDetail1, testDetail2));
        // act
        transactionDataTask.getDataAndPushToEs();
        // assert
        verify(corpAppConfigRepository).getAllConfigs();
        verify(dashBoardESReadDomainService, atLeastOnce()).countTotalOrdersWidely(anyString(), any(), any());
        verify(dashBoardESReadDomainService, atLeastOnce()).sumGtvWidely(any(), anyString(), any());
        verify(scrmSceneCouponRecordsMapper, atLeastOnce()).selectByExample(any());
        verify(scrmCouponOrderAmountDetailMapper, atLeastOnce()).selectByExample(any());
    }

    /**
     * Test getDataAndPushToEs with no configs
     */
    @Test
    public void testGetDataAndPushToEsNoConfigs() throws Throwable {
        // arrange
        when(corpAppConfigRepository.getAllConfigs()).thenReturn(Collections.emptyList());
        // act
        transactionDataTask.getDataAndPushToEs();
        // assert
        verify(corpAppConfigRepository).getAllConfigs();
        verify(dashBoardESReadDomainService, never()).countTotalOrdersWidely(anyString(), any(), any());
        verify(dashBoardESReadDomainService, never()).sumGtvWidely(any(), anyString(), any());
        verify(scrmSceneCouponRecordsMapper, never()).selectByExample(any());
        verify(scrmCouponOrderAmountDetailMapper, never()).selectByExample(any());
    }

    /**
     * Test getDataAndPushToEs with repository exception
     */
    @Test
    public void testGetDataAndPushToEsException() throws Throwable {
        // arrange
        when(corpAppConfigRepository.getAllConfigs()).thenThrow(new RuntimeException("Test exception"));
        // act
        transactionDataTask.getDataAndPushToEs();
        // assert
        verify(corpAppConfigRepository).getAllConfigs();
        verify(dashBoardESReadDomainService, never()).countTotalOrdersWidely(anyString(), any(), any());
        verify(dashBoardESReadDomainService, never()).sumGtvWidely(any(), anyString(), any());
        verify(scrmSceneCouponRecordsMapper, never()).selectByExample(any());
        verify(scrmCouponOrderAmountDetailMapper, never()).selectByExample(any());
    }
}
