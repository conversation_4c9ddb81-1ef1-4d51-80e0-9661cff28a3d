package com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackAndProcessMapDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackAndProcessMapDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationInfoDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackAndProcessMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.ArgumentMatchers.anyString;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecutePlanPackStatusEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutePlanDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecutePlanDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationExecutePlanDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.DdlResultDTO;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import com.sankuai.scrm.core.service.util.CronUtils;
import java.util.*;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.*;
import org.mockito.*;
import org.mockito.junit.*;
import static org.junit.jupiter.api.Assertions.*;
import java.lang.reflect.Method;
import java.util.Calendar;
import java.util.Date;
import org.mockito.MockedStatic;
import static com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import org.junit.jupiter.api.BeforeEach;

@ExtendWith(MockitoExtension.class)
public class ProcessOrchestrationWriteDomainServiceLogicalDeleteCrowdPackByProcessOrchestrationId1Test {

    @Mock
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock
    private ScrmAmCrowdPackAndProcessMapDOMapper scrmAmCrowdPackAndProcessMapDOMapper;

    @Mock
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @InjectMocks
    private ProcessOrchestrationWriteDomainService processOrchestrationWriteDomainService;

    private final Long validProcessOrchestrationId = 1L;

    private final Long validPackId = 100L;

    private final String validVersion = "v1";

    @Mock
    private ExtScrmAmProcessOrchestrationExecutePlanDOMapper extScrmAmProcessOrchestrationExecutePlanDOMapper;

    private ScrmAmProcessOrchestrationInfoDO processInfo;

    private DdlResultDTO resultDTO;

    private Method checkNextRunTimeMethod;

    /**
     * Test when process orchestration is not found
     */
    @Test
    public void testLogicalDeleteCrowdPackByProcessOrchestrationIdWhenProcessNotFound() throws Throwable {
        // arrange
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class))).thenReturn(Collections.emptyList());
        // act
        processOrchestrationWriteDomainService.logicalDeleteCrowdPackByProcessOrchestrationId(validProcessOrchestrationId);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class));
        verifyNoInteractions(scrmAmCrowdPackAndProcessMapDOMapper);
        verifyNoInteractions(crowdPackWriteDomainService);
    }

    /**
     * Test when crowd pack mapping is not found
     */
    @Test
    public void testLogicalDeleteCrowdPackByProcessOrchestrationIdWhenMappingNotFound() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processInfo = new ScrmAmProcessOrchestrationInfoDO();
        processInfo.setValidVersion(validVersion);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class))).thenReturn(Arrays.asList(processInfo));
        when(scrmAmCrowdPackAndProcessMapDOMapper.selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class))).thenReturn(Collections.emptyList());
        // act
        processOrchestrationWriteDomainService.logicalDeleteCrowdPackByProcessOrchestrationId(validProcessOrchestrationId);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class));
        verify(scrmAmCrowdPackAndProcessMapDOMapper).selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class));
        verifyNoInteractions(crowdPackWriteDomainService);
    }

    /**
     * Test successful logical deletion of crowd pack
     */
    @Test
    public void testLogicalDeleteCrowdPackByProcessOrchestrationIdSuccess() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processInfo = new ScrmAmProcessOrchestrationInfoDO();
        processInfo.setValidVersion(validVersion);
        ScrmAmCrowdPackAndProcessMapDO mapDO = new ScrmAmCrowdPackAndProcessMapDO();
        mapDO.setPackId(validPackId);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class))).thenReturn(Arrays.asList(processInfo));
        when(scrmAmCrowdPackAndProcessMapDOMapper.selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class))).thenReturn(Arrays.asList(mapDO));
        // act
        processOrchestrationWriteDomainService.logicalDeleteCrowdPackByProcessOrchestrationId(validProcessOrchestrationId);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class));
        verify(scrmAmCrowdPackAndProcessMapDOMapper).selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class));
        verify(crowdPackWriteDomainService).logicalDeleteCrowdPack(validPackId);
    }

    /**
     * Test when process orchestration ID is null
     * Since the method doesn't handle null IDs properly, we expect an exception
     */
    @Test
    public void testLogicalDeleteCrowdPackByProcessOrchestrationIdWhenIdIsNull() throws Throwable {
        // We need to handle the null case differently since the method will throw an exception
        // This test verifies the current behavior (exception thrown) rather than ideal behavior
        try {
            // act - this will throw an exception
            processOrchestrationWriteDomainService.logicalDeleteCrowdPackByProcessOrchestrationId(null);
        } catch (RuntimeException e) {
            // expected exception
            assertEquals("Value for id cannot be null", e.getMessage());
        }
        // No need to verify interactions since an exception is thrown early
    }

    /**
     * Test when multiple process orchestrations are found (should use first one)
     */
    @Test
    public void testLogicalDeleteCrowdPackByProcessOrchestrationIdWhenMultipleProcessesFound() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processInfo1 = new ScrmAmProcessOrchestrationInfoDO();
        processInfo1.setValidVersion(validVersion);
        ScrmAmProcessOrchestrationInfoDO processInfo2 = new ScrmAmProcessOrchestrationInfoDO();
        processInfo2.setValidVersion("v2");
        ScrmAmCrowdPackAndProcessMapDO mapDO = new ScrmAmCrowdPackAndProcessMapDO();
        mapDO.setPackId(validPackId);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class))).thenReturn(Arrays.asList(processInfo1, processInfo2));
        when(scrmAmCrowdPackAndProcessMapDOMapper.selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class))).thenReturn(Arrays.asList(mapDO));
        // act
        processOrchestrationWriteDomainService.logicalDeleteCrowdPackByProcessOrchestrationId(validProcessOrchestrationId);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class));
        // Use ArgumentCaptor to capture and verify the example object
        ArgumentCaptor<ScrmAmCrowdPackAndProcessMapDOExample> exampleCaptor = ArgumentCaptor.forClass(ScrmAmCrowdPackAndProcessMapDOExample.class);
        verify(scrmAmCrowdPackAndProcessMapDOMapper).selectByExample(exampleCaptor.capture());
        // Verify the correct version was used in the query
        ScrmAmCrowdPackAndProcessMapDOExample capturedExample = exampleCaptor.getValue();
        List<ScrmAmCrowdPackAndProcessMapDOExample.Criteria> criteria = capturedExample.getOredCriteria();
        assertEquals(1, criteria.size());
        verify(crowdPackWriteDomainService).logicalDeleteCrowdPack(validPackId);
    }

    /**
     * Test when multiple mappings are found (should use first one)
     */
    @Test
    public void testLogicalDeleteCrowdPackByProcessOrchestrationIdWhenMultipleMappingsFound() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processInfo = new ScrmAmProcessOrchestrationInfoDO();
        processInfo.setValidVersion(validVersion);
        ScrmAmCrowdPackAndProcessMapDO mapDO1 = new ScrmAmCrowdPackAndProcessMapDO();
        mapDO1.setPackId(validPackId);
        ScrmAmCrowdPackAndProcessMapDO mapDO2 = new ScrmAmCrowdPackAndProcessMapDO();
        mapDO2.setPackId(validPackId + 1);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class))).thenReturn(Arrays.asList(processInfo));
        when(scrmAmCrowdPackAndProcessMapDOMapper.selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class))).thenReturn(Arrays.asList(mapDO1, mapDO2));
        // act
        processOrchestrationWriteDomainService.logicalDeleteCrowdPackByProcessOrchestrationId(validProcessOrchestrationId);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class));
        verify(scrmAmCrowdPackAndProcessMapDOMapper).selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class));
        // Should only delete the first pack ID
        verify(crowdPackWriteDomainService).logicalDeleteCrowdPack(validPackId);
        verify(crowdPackWriteDomainService, never()).logicalDeleteCrowdPack(validPackId + 1);
    }

    @Test
    public void testCheckNextRunTimeForUpdate_EndTimeBeforeNow_ReturnsResult() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        // 结束时间设置为昨天
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, -2);
        infoDO.setEndTime(cal.getTime());
        DdlResultDTO result = new DdlResultDTO();
        // act
        DdlResultDTO actual = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(infoDO, result);
        // assert
        Assertions.assertSame(result, actual);
    }

    @Test
    public void testCheckNextRunTimeForUpdate_ExecutePlanExists_CronCommentBlank_ReturnsResult() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(2L);
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 2);
        infoDO.setEndTime(cal.getTime());
        // blank
        infoDO.setCronComment("");
        infoDO.setValidVersion("v1");
        // TIMED_PROCESS_ORCHESTRATION
        infoDO.setProcessOrchestrationType((byte) 4);
        DdlResultDTO result = new DdlResultDTO();
        List<ScrmAmProcessOrchestrationExecutePlanDO> planList = new ArrayList<>();
        ScrmAmProcessOrchestrationExecutePlanDO plan = new ScrmAmProcessOrchestrationExecutePlanDO();
        plan.setId(10L);
        planList.add(plan);
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(planList);
        // act
        DdlResultDTO actual = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(infoDO, result);
        // assert
        Assertions.assertSame(result, actual);
    }

    @Test
    public void testCheckNextRunTimeForUpdate_ExecutePlanNotExists_CronCommentBlank_ReturnsResult() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(3L);
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 2);
        infoDO.setEndTime(cal.getTime());
        // blank
        infoDO.setCronComment(null);
        infoDO.setValidVersion("v1");
        // TIMED_PROCESS_ORCHESTRATION
        infoDO.setProcessOrchestrationType((byte) 4);
        DdlResultDTO result = new DdlResultDTO();
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.emptyList());
        // act
        DdlResultDTO actual = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(infoDO, result);
        // assert
        Assertions.assertSame(result, actual);
    }

    @Test
    public void testCheckNextRunTimeForUpdate_ExecutePlanExists_CronCommentNotBlank_NotTimedType() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(4L);
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 2);
        infoDO.setEndTime(cal.getTime());
        infoDO.setCronComment("2024-06-01 12:00:00");
        infoDO.setValidVersion("v2");
        // not TIMED_PROCESS_ORCHESTRATION
        infoDO.setProcessOrchestrationType((byte) 3);
        infoDO.setCron("0 0 12 * * ?");
        List<ScrmAmProcessOrchestrationExecutePlanDO> planList = new ArrayList<>();
        ScrmAmProcessOrchestrationExecutePlanDO plan = new ScrmAmProcessOrchestrationExecutePlanDO();
        plan.setId(11L);
        planList.add(plan);
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(planList);
        // mock CronUtils.getCronNextTime
        // 1小时后
        Date cronNextTime = new Date(System.currentTimeMillis() + 3600_000);
        try (MockedStatic<CronUtils> cronUtilsMockedStatic = Mockito.mockStatic(CronUtils.class)) {
            cronUtilsMockedStatic.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(cronNextTime);
            // act
            DdlResultDTO result = new DdlResultDTO();
            DdlResultDTO actual = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(infoDO, result);
            // assert
            Assertions.assertNotNull(actual);
        }
    }

    @Test
    public void testCheckNextRunTimeForUpdate_ExecutePlanExists_SetTaskStartTime() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(5L);
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 2);
        infoDO.setEndTime(cal.getTime());
        infoDO.setCronComment("2024-06-01 12:00:00");
        infoDO.setValidVersion("v3");
        // not TIMED_PROCESS_ORCHESTRATION
        infoDO.setProcessOrchestrationType((byte) 3);
        infoDO.setCron("0 0 12 * * ?");
        List<ScrmAmProcessOrchestrationExecutePlanDO> planList = new ArrayList<>();
        ScrmAmProcessOrchestrationExecutePlanDO plan = new ScrmAmProcessOrchestrationExecutePlanDO();
        plan.setId(12L);
        planList.add(plan);
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(planList);
        // 1小时后
        Date cronNextTime = new Date(System.currentTimeMillis() + 3600_000);
        try (MockedStatic<CronUtils> cronUtilsMockedStatic = Mockito.mockStatic(CronUtils.class)) {
            cronUtilsMockedStatic.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(cronNextTime);
            // act
            DdlResultDTO result = new DdlResultDTO();
            DdlResultDTO actual = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(infoDO, result);
            // assert
            Assertions.assertNotNull(actual);
        }
    }

    @Test
    public void testCheckNextRunTimeForUpdate_ExecutePlanExists_TaskStartTimeBeforeNow() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(6L);
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 2);
        infoDO.setEndTime(cal.getTime());
        infoDO.setCronComment("2024-06-01 12:00:00");
        infoDO.setValidVersion("v4");
        // not TIMED_PROCESS_ORCHESTRATION
        infoDO.setProcessOrchestrationType((byte) 3);
        infoDO.setCron("0 0 12 * * ?");
        List<ScrmAmProcessOrchestrationExecutePlanDO> planList = new ArrayList<>();
        ScrmAmProcessOrchestrationExecutePlanDO plan = new ScrmAmProcessOrchestrationExecutePlanDO();
        plan.setId(13L);
        planList.add(plan);
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(planList);
        // mock CronUtils.getCronNextTime 返回过去的时间
        // 1小时前
        Date cronNextTime = new Date(System.currentTimeMillis() - 3600_000);
        try (MockedStatic<CronUtils> cronUtilsMockedStatic = Mockito.mockStatic(CronUtils.class)) {
            cronUtilsMockedStatic.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(cronNextTime);
            // act
            DdlResultDTO result = new DdlResultDTO();
            DdlResultDTO actual = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(infoDO, result);
            // assert
            Assertions.assertSame(result, actual);
            verify(extScrmAmProcessOrchestrationExecutePlanDOMapper, atLeastOnce()).deleteByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class));
        }
    }

    @Test
    public void testCheckNextRunTimeForUpdate_ExecutePlanExists_TaskStartTimeAfterTomorrow() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(7L);
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 10);
        infoDO.setEndTime(cal.getTime());
        infoDO.setCronComment("2024-06-01 12:00:00");
        infoDO.setValidVersion("v5");
        // not TIMED_PROCESS_ORCHESTRATION
        infoDO.setProcessOrchestrationType((byte) 3);
        infoDO.setCron("0 0 12 * * ?");
        List<ScrmAmProcessOrchestrationExecutePlanDO> planList = new ArrayList<>();
        ScrmAmProcessOrchestrationExecutePlanDO plan = new ScrmAmProcessOrchestrationExecutePlanDO();
        plan.setId(14L);
        planList.add(plan);
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(planList);
        // mock CronUtils.getCronNextTime 返回明天之后的时间
        Calendar future = Calendar.getInstance();
        future.add(Calendar.DATE, 2);
        Date cronNextTime = future.getTime();
        try (MockedStatic<CronUtils> cronUtilsMockedStatic = Mockito.mockStatic(CronUtils.class)) {
            cronUtilsMockedStatic.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(cronNextTime);
            // act
            DdlResultDTO result = new DdlResultDTO();
            DdlResultDTO actual = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(infoDO, result);
            // assert
            Assertions.assertSame(result, actual);
            verify(extScrmAmProcessOrchestrationExecutePlanDOMapper, atLeastOnce()).deleteByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class));
        }
    }

    @Test
    public void testCheckNextRunTimeForUpdate_ExecutePlanNotExists_SetTaskStartTime() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(8L);
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 2);
        infoDO.setEndTime(cal.getTime());
        infoDO.setCronComment("2024-06-01 12:00:00");
        infoDO.setValidVersion("v6");
        // not TIMED/REALTIME
        infoDO.setProcessOrchestrationType((byte) 1);
        infoDO.setCron("0 0 12 * * ?");
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.emptyList());
        // 1小时后
        Date cronNextTime = new Date(System.currentTimeMillis() + 3600_000);
        try (MockedStatic<CronUtils> cronUtilsMockedStatic = Mockito.mockStatic(CronUtils.class)) {
            cronUtilsMockedStatic.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(cronNextTime);
            // act
            DdlResultDTO result = new DdlResultDTO();
            DdlResultDTO actual = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(infoDO, result);
            // assert
            Assertions.assertNotNull(actual);
        }
    }

    @Test
    public void testCheckNextRunTimeForUpdate_ExecutePlanNotExists_TaskStartTimeAfterTomorrow() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(9L);
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 10);
        infoDO.setEndTime(cal.getTime());
        infoDO.setCronComment("2024-06-01 12:00:00");
        infoDO.setValidVersion("v7");
        // not TIMED/REALTIME
        infoDO.setProcessOrchestrationType((byte) 1);
        infoDO.setCron("0 0 12 * * ?");
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.emptyList());
        Calendar future = Calendar.getInstance();
        future.add(Calendar.DATE, 2);
        Date cronNextTime = future.getTime();
        try (MockedStatic<CronUtils> cronUtilsMockedStatic = Mockito.mockStatic(CronUtils.class)) {
            cronUtilsMockedStatic.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(cronNextTime);
            // act
            DdlResultDTO result = new DdlResultDTO();
            DdlResultDTO actual = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(infoDO, result);
            // assert
            Assertions.assertSame(result, actual);
        }
    }

    @Test
    public void testCheckNextRunTimeForUpdate_ExecutePlanNotExists_WillRunToday() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(10L);
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 2);
        infoDO.setEndTime(cal.getTime());
        infoDO.setCronComment("2024-06-01 12:00:00");
        infoDO.setValidVersion("v8");
        // not TIMED/REALTIME
        infoDO.setProcessOrchestrationType((byte) 1);
        infoDO.setCron("0 0 12 * * ?");
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.emptyList());
        // taskStartTime为今天
        // 10分钟后
        Date cronNextTime = new Date(System.currentTimeMillis() + 1000 * 60 * 10);
        try (MockedStatic<CronUtils> cronUtilsMockedStatic = Mockito.mockStatic(CronUtils.class)) {
            cronUtilsMockedStatic.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(cronNextTime);
            // act
            DdlResultDTO result = new DdlResultDTO();
            DdlResultDTO actual = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(infoDO, result);
            // assert
            Assertions.assertNotNull(actual);
            Assertions.assertTrue(actual.isWillRunToday());
            Assertions.assertEquals(cronNextTime, actual.getNextRunTime());
            verify(extScrmAmProcessOrchestrationExecutePlanDOMapper, atLeastOnce()).insert(any(ScrmAmProcessOrchestrationExecutePlanDO.class));
        }
    }

    @Test
    public void testCheckNextRunTimeForUpdate_CronUtilsThrowsException_ReturnsNull() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(11L);
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 2);
        infoDO.setEndTime(cal.getTime());
        infoDO.setCronComment("2024-06-01 12:00:00");
        infoDO.setValidVersion("v9");
        // not TIMED/REALTIME
        infoDO.setProcessOrchestrationType((byte) 1);
        infoDO.setCron("0 0 12 * * ?");
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.emptyList());
        try (MockedStatic<CronUtils> cronUtilsMockedStatic = Mockito.mockStatic(CronUtils.class)) {
            cronUtilsMockedStatic.when(() -> CronUtils.getCronNextTime(anyString())).thenThrow(new RuntimeException("cron error"));
            // act
            DdlResultDTO result = new DdlResultDTO();
            DdlResultDTO actual = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(infoDO, result);
            // assert
            Assertions.assertNull(actual);
        }
    }

    @Test
    public void testCheckNextRunTimeForUpdate_CalendarInstanceCreated() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(14L);
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 2);
        infoDO.setEndTime(cal.getTime());
        infoDO.setCronComment("2024-06-01 12:00:00");
        infoDO.setValidVersion("v12");
        // not TIMED_PROCESS_ORCHESTRATION
        infoDO.setProcessOrchestrationType((byte) 3);
        infoDO.setCron("0 0 12 * * ?");
        List<ScrmAmProcessOrchestrationExecutePlanDO> planList = new ArrayList<>();
        ScrmAmProcessOrchestrationExecutePlanDO plan = new ScrmAmProcessOrchestrationExecutePlanDO();
        plan.setId(16L);
        planList.add(plan);
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(planList);
        // 1小时后
        Date cronNextTime = new Date(System.currentTimeMillis() + 3600_000);
        try (MockedStatic<CronUtils> cronUtilsMockedStatic = Mockito.mockStatic(CronUtils.class)) {
            cronUtilsMockedStatic.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(cronNextTime);
            // act
            DdlResultDTO result = new DdlResultDTO();
            DdlResultDTO actual = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(infoDO, result);
            // assert
            Assertions.assertNotNull(actual);
        }
    }

    @Test
    public void testCheckNextRunTimeForUpdate_TomorrowCalendarSet() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(15L);
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DATE, 2);
        infoDO.setEndTime(cal.getTime());
        infoDO.setCronComment("2024-06-01 12:00:00");
        infoDO.setValidVersion("v13");
        // not TIMED/REALTIME
        infoDO.setProcessOrchestrationType((byte) 1);
        infoDO.setCron("0 0 12 * * ?");
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.emptyList());
        // taskStartTime为明天
        Calendar tomorrow = Calendar.getInstance();
        tomorrow.add(Calendar.DATE, 1);
        tomorrow.set(Calendar.HOUR_OF_DAY, 1);
        tomorrow.set(Calendar.MINUTE, 0);
        tomorrow.set(Calendar.SECOND, 0);
        tomorrow.set(Calendar.MILLISECOND, 0);
        Date cronNextTime = tomorrow.getTime();
        try (MockedStatic<CronUtils> cronUtilsMockedStatic = Mockito.mockStatic(CronUtils.class)) {
            cronUtilsMockedStatic.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(cronNextTime);
            // act
            DdlResultDTO result = new DdlResultDTO();
            DdlResultDTO actual = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(infoDO, result);
            // assert
            Assertions.assertNotNull(actual);
        }
    }

    private DdlResultDTO invokeCheckNextRunTime(ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO, DdlResultDTO resultDTO, boolean needInsert) throws Exception {
        Method method = ProcessOrchestrationWriteDomainService.class.getDeclaredMethod("checkNextRunTime", ScrmAmProcessOrchestrationInfoDO.class, DdlResultDTO.class, boolean.class);
        method.setAccessible(true);
        return (DdlResultDTO) method.invoke(processOrchestrationWriteDomainService, processOrchestrationInfoDO, resultDTO, needInsert);
    }

    @Test
    public void testCheckNextRunTimeCurrentTimeAfterEndTime() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, -2);
        infoDO.setEndTime(calendar.getTime());
        DdlResultDTO resultDTO = new DdlResultDTO();
        // act
        DdlResultDTO result = invokeCheckNextRunTime(infoDO, resultDTO, false);
        // assert
        assertSame(resultDTO, result);
        assertFalse(result.isWillRunInFuture());
        assertFalse(result.isWillRunToday());
        assertNull(result.getNextRunTime());
    }

    @Test
    public void testCheckNextRunTimeBlankCronComment() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        // tomorrow
        infoDO.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        infoDO.setCronComment("");
        DdlResultDTO resultDTO = new DdlResultDTO();
        // act
        DdlResultDTO result = invokeCheckNextRunTime(infoDO, resultDTO, false);
        // assert
        assertSame(resultDTO, result);
        assertFalse(result.isWillRunInFuture());
        assertFalse(result.isWillRunToday());
        assertNull(result.getNextRunTime());
    }

    @Test
    public void testCheckNextRunTimeTimedProcessValidCronComment() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        // 3 days later
        infoDO.setEndTime(new Date(System.currentTimeMillis() + 86400000 * 3));
        infoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        // Set a future time for cronComment that's today but later
        Calendar futureTime = Calendar.getInstance();
        futureTime.add(Calendar.HOUR, 2);
        infoDO.setCronComment(DateUtil.formatYMdHms(futureTime.getTime()));
        DdlResultDTO resultDTO = new DdlResultDTO();
        // act
        DdlResultDTO result = invokeCheckNextRunTime(infoDO, resultDTO, false);
        // assert
        assertSame(resultDTO, result);
        assertTrue(result.isWillRunInFuture());
        assertNotNull(result.getNextRunTime());
    }

    @Test
    public void testCheckNextRunTimeNonTimedProcessValidCron() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        // 3 days later
        infoDO.setEndTime(new Date(System.currentTimeMillis() + 86400000 * 3));
        infoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue().byteValue());
        // daily at noon
        infoDO.setCron("0 0 12 * * ?");
        infoDO.setCronComment("some comment");
        DdlResultDTO resultDTO = new DdlResultDTO();
        // Create a future time for the cron next time
        Calendar futureTime = Calendar.getInstance();
        futureTime.add(Calendar.HOUR, 2);
        Date cronNextTime = futureTime.getTime();
        // Mock the static CronUtils.getCronNextTime method
        try (MockedStatic<CronUtils> mockedCronUtils = mockStatic(CronUtils.class)) {
            mockedCronUtils.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(cronNextTime);
            // act
            DdlResultDTO result = invokeCheckNextRunTime(infoDO, resultDTO, false);
            // assert
            assertSame(resultDTO, result);
            assertTrue(result.isWillRunInFuture());
            assertEquals(cronNextTime, result.getNextRunTime());
        }
    }

    @Test
    public void testCheckNextRunTimeStartTimeBeforeNow() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        // 3 days later
        infoDO.setEndTime(new Date(System.currentTimeMillis() + 86400000 * 3));
        infoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        // Set a past time for cronComment
        Calendar pastTime = Calendar.getInstance();
        pastTime.add(Calendar.HOUR, -1);
        infoDO.setCronComment(DateUtil.formatYMdHms(pastTime.getTime()));
        DdlResultDTO resultDTO = new DdlResultDTO();
        // act
        DdlResultDTO result = invokeCheckNextRunTime(infoDO, resultDTO, false);
        // assert
        assertSame(resultDTO, result);
        assertFalse(result.isWillRunInFuture());
        assertFalse(result.isWillRunToday());
        assertNull(result.getNextRunTime());
    }

    @Test
    public void testCheckNextRunTimeStartTimeTomorrow() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        // 3 days later
        infoDO.setEndTime(new Date(System.currentTimeMillis() + 86400000 * 3));
        infoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        Calendar tomorrow = Calendar.getInstance();
        tomorrow.add(Calendar.DAY_OF_MONTH, 1);
        tomorrow.set(Calendar.HOUR_OF_DAY, 12);
        tomorrow.set(Calendar.MINUTE, 0);
        tomorrow.set(Calendar.SECOND, 0);
        infoDO.setCronComment(DateUtil.formatYMdHms(tomorrow.getTime()));
        DdlResultDTO resultDTO = new DdlResultDTO();
        // act
        DdlResultDTO result = invokeCheckNextRunTime(infoDO, resultDTO, false);
        // assert
        assertSame(resultDTO, result);
        assertTrue(result.isWillRunInFuture());
        assertNotNull(result.getNextRunTime());
    }

    @Test
    public void testCheckNextRunTimeStartTimeAfterTomorrowBeforeEndTime() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        // 5 days later
        infoDO.setEndTime(new Date(System.currentTimeMillis() + 86400000 * 5));
        infoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        Calendar future = Calendar.getInstance();
        future.add(Calendar.DAY_OF_MONTH, 3);
        future.set(Calendar.HOUR_OF_DAY, 12);
        future.set(Calendar.MINUTE, 0);
        future.set(Calendar.SECOND, 0);
        infoDO.setCronComment(DateUtil.formatYMdHms(future.getTime()));
        DdlResultDTO resultDTO = new DdlResultDTO();
        // act
        DdlResultDTO result = invokeCheckNextRunTime(infoDO, resultDTO, false);
        // assert
        assertSame(resultDTO, result);
        assertTrue(result.isWillRunInFuture());
        assertNotNull(result.getNextRunTime());
    }

    @Test
    public void testCheckNextRunTimeNeedInsertTrue() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        // 3 days later
        infoDO.setEndTime(new Date(System.currentTimeMillis() + 86400000 * 3));
        infoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue().byteValue());
        // daily at noon
        infoDO.setCron("0 0 12 * * ?");
        infoDO.setCronComment("some comment");
        infoDO.setValidVersion("1.0");
        DdlResultDTO resultDTO = new DdlResultDTO();
        // Create a future time that's today but later (to ensure it passes the time checks)
        Calendar laterToday = Calendar.getInstance();
        laterToday.add(Calendar.HOUR, 2);
        Date cronNextTime = laterToday.getTime();
        // Set up the mock for insert
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.insert(any())).thenReturn(1);
        // Mock the static CronUtils.getCronNextTime method
        try (MockedStatic<CronUtils> mockedCronUtils = mockStatic(CronUtils.class)) {
            mockedCronUtils.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(cronNextTime);
            // act
            DdlResultDTO result = invokeCheckNextRunTime(infoDO, resultDTO, true);
            // assert
            assertSame(resultDTO, result);
            assertTrue(result.isWillRunInFuture());
            assertTrue(result.isWillRunToday());
            assertEquals(cronNextTime, result.getNextRunTime());
            // Verify the insert was called with the correct parameters
            ArgumentCaptor<ScrmAmProcessOrchestrationExecutePlanDO> planCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationExecutePlanDO.class);
            verify(extScrmAmProcessOrchestrationExecutePlanDOMapper, times(1)).insert(planCaptor.capture());
            ScrmAmProcessOrchestrationExecutePlanDO capturedPlan = planCaptor.getValue();
            assertEquals(infoDO.getId(), capturedPlan.getProcessOrchestrationId());
            assertEquals(infoDO.getProcessOrchestrationType(), capturedPlan.getProcessOrchestrationType());
            assertEquals(infoDO.getValidVersion(), capturedPlan.getProcessOrchestrationVersion());
            assertEquals(ScrmProcessOrchestrationExecuteStatusTypeEnum.PREPARING.getValue().byteValue(), capturedPlan.getStatus());
            assertEquals(ScrmProcessOrchestrationExecutePlanPackStatusEnum.UPDATE_NOT_EXECUTED.getCode().byteValue(), capturedPlan.getPackStatus());
            assertEquals(cronNextTime, capturedPlan.getTaskStartTime());
        }
    }

    @Test
    public void testCheckNextRunTimeException() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        // 3 days later
        infoDO.setEndTime(new Date(System.currentTimeMillis() + 86400000 * 3));
        infoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue().byteValue());
        // This should cause an exception
        infoDO.setCron("invalid cron");
        infoDO.setCronComment("some comment");
        DdlResultDTO resultDTO = new DdlResultDTO();
        // Mock the static CronUtils.getCronNextTime method to throw an exception
        try (MockedStatic<CronUtils> mockedCronUtils = mockStatic(CronUtils.class)) {
            mockedCronUtils.when(() -> CronUtils.getCronNextTime(anyString())).thenThrow(new RuntimeException("Invalid cron expression"));
            // act
            DdlResultDTO result = invokeCheckNextRunTime(infoDO, resultDTO, false);
            // assert
            // Method returns null on exception
            assertNull(result);
        }
    }

    @Test
    public void testCheckNextRunTimeEndTimeToday() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        // Set end time to today at midnight
        Calendar today = Calendar.getInstance();
        today.set(Calendar.HOUR_OF_DAY, 0);
        today.set(Calendar.MINUTE, 0);
        today.set(Calendar.SECOND, 0);
        today.set(Calendar.MILLISECOND, 0);
        infoDO.setEndTime(today.getTime());
        infoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        // Set a future time for cronComment that's today but later
        Calendar futureTime = Calendar.getInstance();
        futureTime.add(Calendar.HOUR, 2);
        infoDO.setCronComment(DateUtil.formatYMdHms(futureTime.getTime()));
        DdlResultDTO resultDTO = new DdlResultDTO();
        // act
        DdlResultDTO result = invokeCheckNextRunTime(infoDO, resultDTO, false);
        // assert
        assertSame(resultDTO, result);
        // Since end time is today at midnight, and we add 1 day in the method,
        // the task should still be considered valid for today
        assertTrue(result.isWillRunInFuture());
        assertNotNull(result.getNextRunTime());
    }

    @BeforeEach
    void setUp() throws Exception {
        processInfo = new ScrmAmProcessOrchestrationInfoDO();
        processInfo.setId(1L);
        processInfo.setValidVersion("1.0");
        // Set a default process orchestration type to avoid NPE
        processInfo.setProcessOrchestrationType((byte) 1);
        resultDTO = new DdlResultDTO();
        // Get access to the private method using reflection
        checkNextRunTimeMethod = ProcessOrchestrationWriteDomainService.class.getDeclaredMethod("checkNextRunTime", ScrmAmProcessOrchestrationInfoDO.class, DdlResultDTO.class, boolean.class);
        checkNextRunTimeMethod.setAccessible(true);
    }

    @Test
    public void testCheckNextRunTime_AfterEndTime() throws Throwable {
        // arrange
        Calendar cal = Calendar.getInstance();
        // 2 days ago
        cal.add(Calendar.DAY_OF_MONTH, -2);
        processInfo.setEndTime(cal.getTime());
        processInfo.setCronComment("test");
        // act
        DdlResultDTO result = (DdlResultDTO) checkNextRunTimeMethod.invoke(processOrchestrationWriteDomainService, processInfo, resultDTO, true);
        // assert
        assertNotNull(result);
        assertSame(resultDTO, result);
        assertFalse(result.isWillRunInFuture());
        assertFalse(result.isWillRunToday());
        assertNull(result.getNextRunTime());
    }

    @Test
    public void testCheckNextRunTime_BlankCronComment() throws Throwable {
        // arrange
        // tomorrow
        processInfo.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        processInfo.setCronComment("");
        // act
        DdlResultDTO result = (DdlResultDTO) checkNextRunTimeMethod.invoke(processOrchestrationWriteDomainService, processInfo, resultDTO, true);
        // assert
        assertNotNull(result);
        assertSame(resultDTO, result);
        assertFalse(result.isWillRunInFuture());
        assertFalse(result.isWillRunToday());
        assertNull(result.getNextRunTime());
    }

    @Test
    public void testCheckNextRunTime_NonTimedTaskValidCron() throws Throwable {
        // arrange
        // 1 hour later
        Date futureTime = new Date(System.currentTimeMillis() + 3600000);
        // tomorrow
        processInfo.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        processInfo.setCronComment("test");
        // non-timed
        processInfo.setProcessOrchestrationType((byte) 1);
        // daily at noon
        processInfo.setCron("0 0 12 * * ?");
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.insert(any(ScrmAmProcessOrchestrationExecutePlanDO.class))).thenReturn(1);
        try (MockedStatic<CronUtils> mocked = Mockito.mockStatic(CronUtils.class)) {
            mocked.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(futureTime);
            // act
            DdlResultDTO result = (DdlResultDTO) checkNextRunTimeMethod.invoke(processOrchestrationWriteDomainService, processInfo, resultDTO, true);
            // assert
            assertNotNull(result);
            assertSame(resultDTO, result);
            assertTrue(result.isWillRunInFuture());
            assertTrue(result.isWillRunToday());
            assertEquals(futureTime, result.getNextRunTime());
            // Verify that insert was called
            verify(extScrmAmProcessOrchestrationExecutePlanDOMapper).insert(any(ScrmAmProcessOrchestrationExecutePlanDO.class));
        }
    }

    @Test
    public void testCheckNextRunTime_TimedTaskValidTime() throws Throwable {
        // arrange
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.HOUR, 2);
        Date futureTime = cal.getTime();
        String timeStr = String.format("%tF %tT", cal, cal);
        // tomorrow
        processInfo.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        processInfo.setCronComment(timeStr);
        processInfo.setProcessOrchestrationType(TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.insert(any(ScrmAmProcessOrchestrationExecutePlanDO.class))).thenReturn(1);
        try (MockedStatic<DateUtil> mocked = Mockito.mockStatic(DateUtil.class)) {
            // Create a DateTime object for the mock
            cn.hutool.core.date.DateTime dateTime = new cn.hutool.core.date.DateTime(futureTime);
            mocked.when(() -> DateUtil.parseYMdHms(anyString())).thenReturn(dateTime);
            // act
            DdlResultDTO result = (DdlResultDTO) checkNextRunTimeMethod.invoke(processOrchestrationWriteDomainService, processInfo, resultDTO, true);
            // assert
            assertNotNull(result);
            assertSame(resultDTO, result);
            assertTrue(result.isWillRunInFuture());
            assertTrue(result.isWillRunToday());
            assertNotNull(result.getNextRunTime());
            // Verify that insert was called
            verify(extScrmAmProcessOrchestrationExecutePlanDOMapper).insert(any(ScrmAmProcessOrchestrationExecutePlanDO.class));
        }
    }

    @Test
    public void testCheckNextRunTime_StartTimeBeforeNow() throws Throwable {
        // arrange
        // 1 hour ago
        Date pastTime = new Date(System.currentTimeMillis() - 3600000);
        // tomorrow
        processInfo.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        processInfo.setCronComment("test");
        // non-timed
        processInfo.setProcessOrchestrationType((byte) 1);
        // daily at noon
        processInfo.setCron("0 0 12 * * ?");
        try (MockedStatic<CronUtils> mocked = Mockito.mockStatic(CronUtils.class)) {
            mocked.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(pastTime);
            // act
            DdlResultDTO result = (DdlResultDTO) checkNextRunTimeMethod.invoke(processOrchestrationWriteDomainService, processInfo, resultDTO, true);
            // assert
            assertNotNull(result);
            assertSame(resultDTO, result);
            assertFalse(result.isWillRunInFuture());
            assertFalse(result.isWillRunToday());
            assertNull(result.getNextRunTime());
        }
    }

    @Test
    public void testCheckNextRunTime_StartTimeTomorrow() throws Throwable {
        // arrange
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 1);
        // noon tomorrow
        cal.add(Calendar.HOUR_OF_DAY, 12);
        Date tomorrowNoon = cal.getTime();
        // 2 days later
        processInfo.setEndTime(new Date(System.currentTimeMillis() + 172800000));
        processInfo.setCronComment("test");
        // non-timed
        processInfo.setProcessOrchestrationType((byte) 1);
        // noon
        processInfo.setCron("0 0 12 * * ?");
        try (MockedStatic<CronUtils> mocked = Mockito.mockStatic(CronUtils.class)) {
            mocked.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(tomorrowNoon);
            // act
            DdlResultDTO result = (DdlResultDTO) checkNextRunTimeMethod.invoke(processOrchestrationWriteDomainService, processInfo, resultDTO, true);
            // assert
            assertNotNull(result);
            assertSame(resultDTO, result);
            assertTrue(result.isWillRunInFuture());
            assertEquals(tomorrowNoon, result.getNextRunTime());
            assertFalse(result.isWillRunToday());
        }
    }

    @Test
    public void testCheckNextRunTime_CronParseException() throws Throwable {
        // arrange
        // tomorrow
        processInfo.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        processInfo.setCronComment("test");
        // non-timed
        processInfo.setProcessOrchestrationType((byte) 1);
        processInfo.setCron("invalid cron");
        try (MockedStatic<CronUtils> mocked = Mockito.mockStatic(CronUtils.class)) {
            mocked.when(() -> CronUtils.getCronNextTime(anyString())).thenThrow(new RuntimeException("Invalid cron"));
            // act
            DdlResultDTO result = (DdlResultDTO) checkNextRunTimeMethod.invoke(processOrchestrationWriteDomainService, processInfo, resultDTO, true);
            // assert
            assertNull(result);
        }
    }

    @Test
    public void testCheckNextRunTime_NoInsertNeeded() throws Throwable {
        // arrange
        // 1 hour later
        Date futureTime = new Date(System.currentTimeMillis() + 3600000);
        // tomorrow
        processInfo.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        processInfo.setCronComment("test");
        // non-timed
        processInfo.setProcessOrchestrationType((byte) 1);
        // daily at noon
        processInfo.setCron("0 0 12 * * ?");
        try (MockedStatic<CronUtils> mocked = Mockito.mockStatic(CronUtils.class)) {
            mocked.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(futureTime);
            // act
            DdlResultDTO result = (DdlResultDTO) checkNextRunTimeMethod.invoke(processOrchestrationWriteDomainService, processInfo, resultDTO, false);
            // assert
            assertNotNull(result);
            assertSame(resultDTO, result);
            assertTrue(result.isWillRunInFuture());
            assertEquals(futureTime, result.getNextRunTime());
            assertFalse(result.isWillRunToday());
        }
    }

    @Test
    public void testCheckNextRunTime_TimeParseException() throws Throwable {
        // arrange
        // tomorrow
        processInfo.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        processInfo.setCronComment("invalid time format");
        processInfo.setProcessOrchestrationType(TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        try (MockedStatic<DateUtil> mocked = Mockito.mockStatic(DateUtil.class)) {
            mocked.when(() -> DateUtil.parseYMdHms(anyString())).thenThrow(new RuntimeException("Invalid date format"));
            // act
            DdlResultDTO result = (DdlResultDTO) checkNextRunTimeMethod.invoke(processOrchestrationWriteDomainService, processInfo, resultDTO, true);
            // assert
            assertNull(result);
        }
    }

    @Test
    public void testCheckNextRunTime_VerifyExecutePlanCreation() throws Throwable {
        // arrange
        // 1 hour later
        Date futureTime = new Date(System.currentTimeMillis() + 3600000);
        processInfo.setId(123L);
        processInfo.setValidVersion("2.0");
        // some type
        processInfo.setProcessOrchestrationType((byte) 5);
        // tomorrow
        processInfo.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        processInfo.setCronComment("test");
        // daily at noon
        processInfo.setCron("0 0 12 * * ?");
        ArgumentCaptor<ScrmAmProcessOrchestrationExecutePlanDO> planCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationExecutePlanDO.class);
        try (MockedStatic<CronUtils> mocked = Mockito.mockStatic(CronUtils.class)) {
            mocked.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(futureTime);
            when(extScrmAmProcessOrchestrationExecutePlanDOMapper.insert(planCaptor.capture())).thenReturn(1);
            // act
            DdlResultDTO result = (DdlResultDTO) checkNextRunTimeMethod.invoke(processOrchestrationWriteDomainService, processInfo, resultDTO, true);
            // assert
            assertNotNull(result);
            ScrmAmProcessOrchestrationExecutePlanDO capturedPlan = planCaptor.getValue();
            assertEquals(123L, capturedPlan.getProcessOrchestrationId());
            assertEquals("2.0", capturedPlan.getProcessOrchestrationVersion());
            assertEquals((byte) 5, capturedPlan.getProcessOrchestrationType());
            assertEquals(ScrmProcessOrchestrationExecuteStatusTypeEnum.PREPARING.getValue().byteValue(), capturedPlan.getStatus());
            assertEquals(ScrmProcessOrchestrationExecutePlanPackStatusEnum.UPDATE_NOT_EXECUTED.getCode().byteValue(), capturedPlan.getPackStatus());
            assertEquals(futureTime, capturedPlan.getTaskStartTime());
        }
    }
}
