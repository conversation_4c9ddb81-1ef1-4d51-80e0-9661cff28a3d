package com.sankuai.scrm.core.service.couponIntegration.mq.consumer;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import static org.mockito.Mockito.mockStatic;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import java.lang.reflect.Field;
import java.util.Properties;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ScrmCouponConsumeRecordConsumerAfterPropertiesSetTest {

    @Mock
    private MafkaClient mafkaClient;

    @Mock
    private IConsumerProcessor consumer;

    private ScrmCouponConsumeRecordConsumer consumerInstance;

    @Before
    public void setUp() throws Exception {
        consumerInstance = new ScrmCouponConsumeRecordConsumer();
        // Use reflection to set the private static field consumer
        Field consumerField = ScrmCouponConsumeRecordConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(null, consumer);
    }

    @After
    public void tearDown() throws Exception {
        // Reset the consumer field to null to avoid side effects on other tests
        Field consumerField = ScrmCouponConsumeRecordConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(null, null);
    }

    /**
     * Test afterPropertiesSet method under normal conditions.
     */
    @Test
    public void testAfterPropertiesSetNormal() throws Throwable {
        // Mock the static method MafkaClient.buildConsumerFactory
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            // Mock the behavior of buildConsumerFactory to return the mocked consumer
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenReturn(consumer);
            // Act
            consumerInstance.afterPropertiesSet();
            // Assert
            verify(consumer, times(1)).recvMessageWithParallel(any(), any());
        }
    }

    /**
     * Test afterPropertiesSet method under exceptional conditions.
     */
    @Test(expected = Exception.class)
    public void testAfterPropertiesSetException() throws Throwable {
        // Mock the static method MafkaClient.buildConsumerFactory to throw an exception
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            // Mock the behavior of buildConsumerFactory to throw an exception
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenThrow(new Exception("Simulated exception"));
            // Act
            consumerInstance.afterPropertiesSet();
        }
    }
}
