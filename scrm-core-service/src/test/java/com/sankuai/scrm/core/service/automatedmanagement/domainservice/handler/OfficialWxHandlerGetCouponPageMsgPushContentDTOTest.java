package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.BizDistributorServiceKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import java.util.HashMap;
import java.util.Map;
import org.junit.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class OfficialWxHandlerGetCouponPageMsgPushContentDTOTest {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private ShortLinkUtils shortLinkUtils;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test case for getCouponPageMsgPushContentDTO when the attachment already exists in the map.
     */
    @Test
    public void testGetCouponPageMsgPushContentDTO_AttachmentExists() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setProcessOrchestrationNodeId(1L);
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        MsgPushContentDTO existingMsgPushContentDTO = new MsgPushContentDTO();
        existedAttachmentMap.put("1", existingMsgPushContentDTO);
        // act
        MsgPushContentDTO result = officialWxHandler.getCouponPageMsgPushContentDTO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap,null );
        // assert
        assertNotNull(result);
        assertEquals(existingMsgPushContentDTO, result);
        verify(informationGatheringService, never()).queryCommunityDistributor(any());
        verify(productManagementService, never()).getActivSceneCodeDO(any(), any(), any(), any(), any());
    }

    /**
     * Test case for getCouponPageMsgPushContentDTO when the attachment does not exist in the map.
     */
    @Test
    public void testGetCouponPageMsgPushContentDTO_AttachmentDoesNotExist() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setProcessOrchestrationNodeId(1L);
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTitle("Test Title");
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageInfo.setMiniProgramOriginAppId("originAppId");
        pageInfo.setThumbPicUrl("thumbPicUrl");
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        // Mock the sceneCodeDO object
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneCode("testSceneCode");
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class))).thenReturn("distributorCode");
        when(productManagementService.getActivSceneCodeDO(any(), any(), any(), any(), any())).thenReturn(sceneCodeDO);
        // Mock the shortLinkUtils to return a valid URL
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("https://short.link");
        // act
        MsgPushContentDTO result = officialWxHandler.getCouponPageMsgPushContentDTO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap,null );
        // assert
        assertNotNull(result);
        assertEquals(1, existedAttachmentMap.size());
        assertTrue(existedAttachmentMap.containsKey("1"));
        assertEquals(result, existedAttachmentMap.get("1"));
        assertEquals(ContentTypeTEnum.MINI_PROGRAM, result.getContentTypeTEnum());
        assertNotNull(result.getMiniProgramDTO());
        assertEquals("Test Title", result.getMiniProgramDTO().getTitle());
        assertEquals("gh_870576f3c6f9", result.getMiniProgramDTO().getOriginAppId());
        assertNull(result.getMiniProgramDTO().getThumbnail());
    }

    /**
     * Test case for getCouponPageMsgPushContentDTO when the supplyDetailDTO is null.
     */
    @Test(expected = NullPointerException.class)
    public void testGetCouponPageMsgPushContentDTO_NullSupplyDetailDTO() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        // Null supplyDetailDTO
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = null;
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        // act
        officialWxHandler.getCouponPageMsgPushContentDTO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, null);
        // assert
        // Expecting NullPointerException
    }

    /**
     * Test case for getCouponPageMsgPushContentDTO when the pageInfo is null.
     */
    @Test(expected = NullPointerException.class)
    public void testGetCouponPageMsgPushContentDTO_NullPageInfo() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        // Null pageInfo
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = null;
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        // act
        officialWxHandler.getCouponPageMsgPushContentDTO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap,null );
        // assert
        // Expecting NullPointerException
    }

    /**
     * Test case for getCouponPageMsgPushContentDTO when the existedAttachmentMap is null.
     */
    @Test(expected = NullPointerException.class)
    public void testGetCouponPageMsgPushContentDTO_NullExistedAttachmentMap() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        // Null existedAttachmentMap
        Map<String, MsgPushContentDTO> existedAttachmentMap = null;
        // act
        officialWxHandler.getCouponPageMsgPushContentDTO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, null);
        // assert
        // Expecting NullPointerException
    }
}
