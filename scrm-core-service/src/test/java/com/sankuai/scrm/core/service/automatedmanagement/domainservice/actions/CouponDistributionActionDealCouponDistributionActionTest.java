package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.coupon.dto.NextCouponInfoDTO;
import com.sankuai.dz.srcm.realtime.task.dto.SceneSendCouponResponse;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationMockLog;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.realtime.task.domainservice.SceneDomainService;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.AutoActionContentDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.coupon.dto.CouponRequestContext;
import com.sankuai.scrm.core.service.coupon.dto.CouponSceneEnum;

@ExtendWith(MockitoExtension.class)
public class CouponDistributionActionDealCouponDistributionActionTest {

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private SceneDomainService sceneDomainService;

    @Mock
    private ExecuteManagementService executeManagementService;

    @InjectMocks
    private CouponDistributionAction couponDistributionAction;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ExecuteManagementDTO executeManagementDTO;

    private ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO;

    private ScrmProcessOrchestrationNodeDTO currentProcessingNode;

    private ScrmAmProcessOrchestrationExecuteLogDO existedExecuteLogDO;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        processOrchestrationDTO.setAppId("testApp");
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setNodeId(1L);
        scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        scrmCrowdPackDetailInfoDTO.setExternalUserWxUnionId("testUnionId");
        executeManagementDTO = new ExecuteManagementDTO();
    }

    /**
     * Test case for empty content list scenario
     */
    @Test
    public void testDealCouponDistributionAction_EmptyContentList() {
        // arrange
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        processOrchestrationDTO.getNodeMediumDTO().getActionMap().put(currentProcessingNode.getNodeId(), actionDTO);
        // act
        StepExecuteResultDTO result = couponDistributionAction.dealCouponDistributionAction(processOrchestrationDTO, executeManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertFalse(result.isSuccess());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getCode(), result.getCode());
    }

    private void setupValidActionDTO() {
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        List<ScrmProcessOrchestrationActionContentDTO> contentList = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("{\"couponGroupId\":\"testCouponId\"}");
        contentList.add(contentDTO);
        processOrchestrationDTO.getNodeMediumDTO().getActionMap().put(currentProcessingNode.getNodeId(), actionDTO);
        processOrchestrationDTO.getNodeMediumDTO().getActionContentMap().put(currentProcessingNode.getNodeId() + "-" + actionDTO.getActionId(), contentList);
    }

    private CouponRequestContext invokePrivateMethod(ScrmProcessOrchestrationDTO processDTO, ScrmCrowdPackDetailInfoDTO crowdPackDTO, AutoActionContentDetailDTO contentDTO, ScrmAmProcessOrchestrationExecuteLogDO executeLogDO) throws Exception {
        CouponDistributionAction action = new CouponDistributionAction();
        java.lang.reflect.Method method = CouponDistributionAction.class.getDeclaredMethod("buildCouponRequestContext", ScrmProcessOrchestrationDTO.class, ScrmCrowdPackDetailInfoDTO.class, AutoActionContentDetailDTO.class, ScrmAmProcessOrchestrationExecuteLogDO.class);
        method.setAccessible(true);
        return (CouponRequestContext) method.invoke(action, processDTO, crowdPackDTO, contentDTO, executeLogDO);
    }

    @Test
    void testBuildCouponRequestContextNormalCase() throws Throwable {
        // 准备测试数据
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        processDTO.setId(1L);
        processDTO.setAppId("testApp");
        processDTO.setProcessOrchestrationType((byte) 1);
        processDTO.setAiScene(false);
        ScrmCrowdPackDetailInfoDTO crowdPackDTO = new ScrmCrowdPackDetailInfoDTO();
        crowdPackDTO.setExternalUserWxUnionId("testUnionId");
        AutoActionContentDetailDTO contentDTO = new AutoActionContentDetailDTO();
        contentDTO.setCouponGroupId("testCouponGroup");
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setTargetMtUserId(100L);
        executeLogDO.setProcessOrchestrationNodeId(10L);
        executeLogDO.setTaskStartTime(new Date());
        // 执行测试
        CouponRequestContext result = invokePrivateMethod(processDTO, crowdPackDTO, contentDTO, executeLogDO);
        // 验证结果
        assertNotNull(result);
        assertEquals(100L, result.getUserId());
        assertEquals("testUnionId", result.getUnionId());
        assertEquals("testApp", result.getAppId());
        assertEquals("testCouponGroup", result.getCouponGroupId());
        assertEquals(CouponSceneEnum.PROCESS_ORCHESTRATION, result.getScene());
        assertEquals(1L, result.getProcessOrchestrationId());
        assertEquals(10L, result.getProcessOrchestrationNodeId());
        assertEquals((byte) 1, result.getProcessOrchestrationType());
        assertNotNull(result.getTaskStartTime());
        assertFalse(result.isAiScene());
        assertNull(result.getProductId());
        assertNull(result.getShopId());
    }

    @Test
    void testBuildCouponRequestContextAiSceneWithContent() throws Throwable {
        // 准备测试数据
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        processDTO.setId(1L);
        processDTO.setAppId("testApp");
        processDTO.setProcessOrchestrationType((byte) 1);
        processDTO.setAiScene(true);
        IntelligentFollowResultDTO aiContent = new IntelligentFollowResultDTO();
        aiContent.setProductId(200L);
        aiContent.setShopId(300L);
        processDTO.setAiSceneContent(aiContent);
        ScrmCrowdPackDetailInfoDTO crowdPackDTO = new ScrmCrowdPackDetailInfoDTO();
        crowdPackDTO.setExternalUserWxUnionId("testUnionId");
        AutoActionContentDetailDTO contentDTO = new AutoActionContentDetailDTO();
        contentDTO.setCouponGroupId("testCouponGroup");
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setTargetMtUserId(100L);
        executeLogDO.setProcessOrchestrationNodeId(10L);
        executeLogDO.setTaskStartTime(new Date());
        // 执行测试
        CouponRequestContext result = invokePrivateMethod(processDTO, crowdPackDTO, contentDTO, executeLogDO);
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isAiScene());
        assertEquals(200L, result.getProductId());
        assertEquals(300L, result.getShopId());
    }

    @Test
    void testBuildCouponRequestContextAiSceneWithNullContent() throws Throwable {
        // 准备测试数据
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        processDTO.setId(1L);
        processDTO.setAppId("testApp");
        processDTO.setProcessOrchestrationType((byte) 1);
        processDTO.setAiScene(true);
        processDTO.setAiSceneContent(null);
        ScrmCrowdPackDetailInfoDTO crowdPackDTO = new ScrmCrowdPackDetailInfoDTO();
        crowdPackDTO.setExternalUserWxUnionId("testUnionId");
        AutoActionContentDetailDTO contentDTO = new AutoActionContentDetailDTO();
        contentDTO.setCouponGroupId("testCouponGroup");
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setTargetMtUserId(100L);
        executeLogDO.setProcessOrchestrationNodeId(10L);
        executeLogDO.setTaskStartTime(new Date());
        // 执行测试
        CouponRequestContext result = invokePrivateMethod(processDTO, crowdPackDTO, contentDTO, executeLogDO);
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isAiScene());
        assertNull(result.getProductId());
        assertNull(result.getShopId());
    }

    @Test
    void testBuildCouponRequestContextEmptyUnionId() throws Throwable {
        // 准备测试数据
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        processDTO.setId(1L);
        processDTO.setAppId("testApp");
        processDTO.setProcessOrchestrationType((byte) 1);
        processDTO.setAiScene(false);
        ScrmCrowdPackDetailInfoDTO crowdPackDTO = new ScrmCrowdPackDetailInfoDTO();
        crowdPackDTO.setExternalUserWxUnionId("");
        AutoActionContentDetailDTO contentDTO = new AutoActionContentDetailDTO();
        contentDTO.setCouponGroupId("testCouponGroup");
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setTargetMtUserId(100L);
        executeLogDO.setProcessOrchestrationNodeId(10L);
        executeLogDO.setTaskStartTime(new Date());
        // 执行测试
        CouponRequestContext result = invokePrivateMethod(processDTO, crowdPackDTO, contentDTO, executeLogDO);
        // 验证结果
        assertNotNull(result);
        assertEquals("", result.getUnionId());
    }

    @Test
    void testBuildCouponRequestContextNullProcessDTO() throws Throwable {
        // 准备测试数据
        ScrmCrowdPackDetailInfoDTO crowdPackDTO = new ScrmCrowdPackDetailInfoDTO();
        crowdPackDTO.setExternalUserWxUnionId("testUnionId");
        AutoActionContentDetailDTO contentDTO = new AutoActionContentDetailDTO();
        contentDTO.setCouponGroupId("testCouponGroup");
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setTargetMtUserId(100L);
        executeLogDO.setProcessOrchestrationNodeId(10L);
        executeLogDO.setTaskStartTime(new Date());
        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            invokePrivateMethod(null, crowdPackDTO, contentDTO, executeLogDO);
        });
    }

    @Test
    void testBuildCouponRequestContextNullExecuteLog() throws Throwable {
        // 准备测试数据
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        processDTO.setId(1L);
        processDTO.setAppId("testApp");
        processDTO.setProcessOrchestrationType((byte) 1);
        processDTO.setAiScene(false);
        ScrmCrowdPackDetailInfoDTO crowdPackDTO = new ScrmCrowdPackDetailInfoDTO();
        crowdPackDTO.setExternalUserWxUnionId("testUnionId");
        AutoActionContentDetailDTO contentDTO = new AutoActionContentDetailDTO();
        contentDTO.setCouponGroupId("testCouponGroup");
        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            invokePrivateMethod(processDTO, crowdPackDTO, contentDTO, null);
        });
    }
}
