package com.sankuai.scrm.core.service.infrastructure.acl.ds;

import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsCorpGroupAclService;
import com.sankuai.service.fe.corp.wx.thrift.CorpWxService;
import com.sankuai.service.fe.corp.wx.thrift.SetMemberToGroupOwnerResponse;
import org.apache.thrift.TException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DsCorpGroupAclServiceTransferGroupOwner1Test {

    @InjectMocks
    private DsCorpGroupAclService dsCorpGroupAclService;

    @Mock
    private CorpWxService.Iface corpWxService;

    private String mobile;

    private String groupId;

    private String groupMemberName;

    private String groupMemberId;

    @Before
    public void setUp() {
        mobile = "13800138000";
        groupId = "123456";
        groupMemberName = "test";
        groupMemberId = "7890";
    }

    @Test(expected = IllegalArgumentException.class)
    public void testTransferGroupOwnerMobileIsNull() throws Throwable {
        mobile = null;
        dsCorpGroupAclService.transferGroupOwner(mobile, groupId, groupMemberName, groupMemberId);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testTransferGroupOwnerGroupIdIsNull() throws Throwable {
        groupId = null;
        dsCorpGroupAclService.transferGroupOwner(mobile, groupId, groupMemberName, groupMemberId);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testTransferGroupOwnerMemberNameAndMemberIdAreNull() throws Throwable {
        groupMemberName = null;
        groupMemberId = null;
        dsCorpGroupAclService.transferGroupOwner(mobile, groupId, groupMemberName, groupMemberId);
    }

    @Test
    public void testTransferGroupOwnerResponseIsNull() throws Throwable {
        when(corpWxService.setMemberToGroupOwner(any())).thenReturn(null);
        assertFalse(dsCorpGroupAclService.transferGroupOwner(mobile, groupId, groupMemberName, groupMemberId));
    }

    @Test
    public void testTransferGroupOwnerResponseCodeIsNotZero() throws Throwable {
        SetMemberToGroupOwnerResponse response = new SetMemberToGroupOwnerResponse();
        response.setCode(1);
        when(corpWxService.setMemberToGroupOwner(any())).thenReturn(response);
        assertFalse(dsCorpGroupAclService.transferGroupOwner(mobile, groupId, groupMemberName, groupMemberId));
    }

    @Test
    public void testTransferGroupOwnerResponseCodeIsZero() throws Throwable {
        SetMemberToGroupOwnerResponse response = new SetMemberToGroupOwnerResponse();
        response.setCode(0);
        when(corpWxService.setMemberToGroupOwner(any())).thenReturn(response);
        assertTrue(dsCorpGroupAclService.transferGroupOwner(mobile, groupId, groupMemberName, groupMemberId));
    }

    @Test
    public void testTransferGroupOwnerThrowsTException() throws Throwable {
        when(corpWxService.setMemberToGroupOwner(any())).thenThrow(TException.class);
        assertFalse(dsCorpGroupAclService.transferGroupOwner(mobile, groupId, groupMemberName, groupMemberId));
    }
}
