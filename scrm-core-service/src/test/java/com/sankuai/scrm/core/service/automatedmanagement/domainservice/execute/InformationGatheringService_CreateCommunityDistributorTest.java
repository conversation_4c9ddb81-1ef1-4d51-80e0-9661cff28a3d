package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mdp.boot.starter.mdpcache.core.Cache;
import com.sankuai.carnation.distribution.distributor.dto.BizDistributorDTO;
import com.sankuai.carnation.distribution.distributor.service.BizDistributorService;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationDistributorCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationDistributorCodeDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.BizDistributorServiceKeyObject;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.lang.reflect.Field;
import java.util.Collections;
import static org.mockito.ArgumentMatchers.any;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class InformationGatheringService_CreateCommunityDistributorTest {

    @InjectMocks
    private InformationGatheringService informationGatheringService;

    @Mock(lenient = true)
    private BizDistributorService bizDistributorService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationDistributorCodeDOMapper distributorCodeDOMapper;

    @Mock(lenient = true)
    private Cache communityDistributorCache;

    @Before
    public void setUp() throws Exception {
        Field field = InformationGatheringService.class.getDeclaredField("communityDistributorCache");
        field.setAccessible(true);
        field.set(null, communityDistributorCache);
    }

    @After
    public void tearDown() throws Exception {
        Field field = InformationGatheringService.class.getDeclaredField("communityDistributorCache");
        field.setAccessible(true);
        field.set(null, null);
    }

    @Test
    public void testCreateCommunityDistributor_CacheHit() throws Throwable {
        BizDistributorServiceKeyObject keyObject = new BizDistributorServiceKeyObject(1L, "version", 1L, "appId");
        when(communityDistributorCache.get(any(), eq(String.class))).thenReturn("testCode");
        String result = informationGatheringService.createCommunityDistributor(keyObject);
        assertEquals("testCode", result);
        verify(distributorCodeDOMapper, never()).selectByExample(any());
        verify(bizDistributorService, never()).createCommunityDistributor(any());
        verify(bizDistributorService, never()).queryByBizTypeAndId(any(), any(), any(), any());
    }

    @Test
    public void testCreateCommunityDistributor_CacheMissDatabaseMissCreateSuccess() throws Throwable {
        BizDistributorServiceKeyObject keyObject = new BizDistributorServiceKeyObject(1L, "version", 1L, "appId");
        when(communityDistributorCache.get(any(), eq(String.class))).thenReturn(null);
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(bizDistributorService.createCommunityDistributor(any())).thenReturn(RemoteResponse.success(1L));
        BizDistributorDTO bizDistributorDTO = new BizDistributorDTO();
        bizDistributorDTO.setDistributorCode("testCode");
        when(bizDistributorService.queryByBizTypeAndId(any(), any(), any(), any())).thenReturn(RemoteResponse.success(Collections.singletonList(bizDistributorDTO)));
        when(distributorCodeDOMapper.insert(any())).thenReturn(1);
        String result = informationGatheringService.createCommunityDistributor(keyObject);
        assertEquals("testCode", result);
        verify(distributorCodeDOMapper).selectByExample(any());
        verify(bizDistributorService).createCommunityDistributor(any());
        verify(bizDistributorService).queryByBizTypeAndId(any(), any(), any(), any());
        verify(distributorCodeDOMapper).insert(any());
        verify(communityDistributorCache).put(any(), eq("testCode"));
    }

    @Test
    public void testCreateCommunityDistributor_CacheMissDatabaseMissCreateFail() throws Throwable {
        BizDistributorServiceKeyObject keyObject = new BizDistributorServiceKeyObject(1L, "version", 1L, "appId");
        when(communityDistributorCache.get(any(), eq(String.class))).thenReturn(null);
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(bizDistributorService.createCommunityDistributor(any())).thenReturn(RemoteResponse.success(1L));
        when(bizDistributorService.queryByBizTypeAndId(any(), any(), any(), any())).thenReturn(RemoteResponse.fail("Failed to query"));
        String result = informationGatheringService.createCommunityDistributor(keyObject);
        assertEquals(null, result);
        verify(distributorCodeDOMapper).selectByExample(any());
        verify(bizDistributorService).createCommunityDistributor(any());
        verify(bizDistributorService).queryByBizTypeAndId(any(), any(), any(), any());
        verify(distributorCodeDOMapper, never()).insert(any());
        verify(communityDistributorCache, never()).put(any(), any());
    }

    @Test
    public void testCreateCommunityDistributor_DatabaseHit() throws Throwable {
        BizDistributorServiceKeyObject keyObject = new BizDistributorServiceKeyObject(1L, "version", 1L, "appId");
        when(communityDistributorCache.get(any(), eq(String.class))).thenReturn(null);
        ScrmAmProcessOrchestrationDistributorCodeDO distributorCodeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        distributorCodeDO.setDistributorCode("testCode");
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(distributorCodeDO));
        String result = informationGatheringService.createCommunityDistributor(keyObject);
        assertEquals("testCode", result);
        verify(distributorCodeDOMapper).selectByExample(any());
        verify(bizDistributorService, never()).createCommunityDistributor(any());
        verify(bizDistributorService, never()).queryByBizTypeAndId(any(), any(), any(), any());
        verify(communityDistributorCache).put(any(), eq("testCode"));
    }
}
