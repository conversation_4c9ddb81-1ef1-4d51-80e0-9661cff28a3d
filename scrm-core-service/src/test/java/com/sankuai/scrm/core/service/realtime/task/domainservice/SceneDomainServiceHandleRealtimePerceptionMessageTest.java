package com.sankuai.scrm.core.service.realtime.task.domainservice;

import com.sankuai.dz.srcm.realtime.task.dto.RealtimePerceptionMessage;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import java.util.Collections;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmRealtimeSceneDO;
import com.sankuai.scrm.core.service.realtime.task.dal.example.ScrmRealtimeSceneDOExample;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmRealtimeSceneDOMapper;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.automatedmanagement.service.impl.ScrmRefinementOperationBackEndServiceImpl;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ExecuteResultDTO;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.dal.example.ContactUserExample;
import com.sankuai.scrm.core.service.external.contact.dal.babymapper.ContactUserMapper;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmRealtimeSceneUserRecordDOMapper;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SceneDomainServiceHandleRealtimePerceptionMessageTest {

    @InjectMocks
    @Spy
    private SceneDomainService sceneDomainService;

    @Mock
    private ScrmRealtimeSceneDOMapper scrmRealtimeSceneDOMapper;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private ScrmRefinementOperationBackEndServiceImpl scrmRefinementOperationBackEndService;

    @Mock
    private ContactUserMapper contactUserMapper;

    @Mock
    private ScrmRealtimeSceneUserRecordDOMapper scrmRealtimeSceneUserRecordDOMapper;

    private RealtimePerceptionMessage createMessage() {
        RealtimePerceptionMessage message = new RealtimePerceptionMessage();
        message.setSceneId(1);
        message.setUserId(1L);
        return message;
    }

    private ScrmRealtimeSceneDO createSceneDO() {
        ScrmRealtimeSceneDO sceneDO = new ScrmRealtimeSceneDO();
        sceneDO.setSceneid(1);
        sceneDO.setAppid("appId");
        return sceneDO;
    }

    private ContactUser createContactUser() {
        ContactUser contactUser = new ContactUser();
        contactUser.setCorpId("corpId");
        contactUser.setUnionId("unionId");
        return contactUser;
    }

    @Test
    public void testHandleRealtimePerceptionMessageMessageIsNull() throws Throwable {
        sceneDomainService.handleRealtimePerceptionMessage(null);
        verifyNoInteractions(scrmRealtimeSceneDOMapper, corpAppConfigRepository, mtUserCenterAclService, scrmRefinementOperationBackEndService, contactUserMapper);
    }

    @Test
    public void testHandleRealtimePerceptionMessageSceneIdIsNull() throws Throwable {
        RealtimePerceptionMessage message = createMessage();
        message.setSceneId(null);
        sceneDomainService.handleRealtimePerceptionMessage(message);
        verifyNoInteractions(scrmRealtimeSceneDOMapper, corpAppConfigRepository, mtUserCenterAclService, scrmRefinementOperationBackEndService, contactUserMapper);
    }

    @Test
    public void testHandleRealtimePerceptionMessageUserIdIsNull() throws Throwable {
        RealtimePerceptionMessage message = createMessage();
        message.setUserId(null);
        sceneDomainService.handleRealtimePerceptionMessage(message);
        verifyNoInteractions(scrmRealtimeSceneDOMapper, corpAppConfigRepository, mtUserCenterAclService, scrmRefinementOperationBackEndService, contactUserMapper);
    }

    @Test
    public void testHandleRealtimePerceptionMessageSceneNotFound() throws Throwable {
        RealtimePerceptionMessage message = createMessage();
//        when(scrmRealtimeSceneDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        sceneDomainService.handleRealtimePerceptionMessage(message);
//        verify(scrmRealtimeSceneDOMapper).selectByExample(any());
        verifyNoInteractions(corpAppConfigRepository, mtUserCenterAclService, scrmRefinementOperationBackEndService, contactUserMapper);
    }
}
