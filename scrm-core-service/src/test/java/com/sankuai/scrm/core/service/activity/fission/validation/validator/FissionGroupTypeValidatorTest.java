package com.sankuai.scrm.core.service.activity.fission.validation.validator;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.activity.fission.request.GroupFissionActivityRequest;
import com.sankuai.dz.srcm.activity.fission.request.RewardInfoRequest;
import com.sankuai.scrm.core.service.activity.fission.validation.exception.FissionValidatorException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class FissionGroupTypeValidatorTest {

    private final FissionGroupTypeValidator validator = new FissionGroupTypeValidator();

    private String invokePrivateMethod(FissionGroupTypeValidator validator, String methodName, List<RewardInfoRequest> rewardInfoList) throws Exception {
        Method method = FissionGroupTypeValidator.class.getDeclaredMethod(methodName, List.class);
        method.setAccessible(true);
        return (String) method.invoke(validator, rewardInfoList);
    }

    /**
     * 测试正常情况 - rewardInfo列表有效
     */
    @Test
    public void testValidateWithValidRewardInfo() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        List<RewardInfoRequest> rewardInfoList = new ArrayList<>();
        RewardInfoRequest reward1 = new RewardInfoRequest();
        reward1.setStage(1);
        reward1.setInvitationNum(10);
        RewardInfoRequest reward2 = new RewardInfoRequest();
        reward2.setStage(2);
        reward2.setInvitationNum(20);
        rewardInfoList.add(reward1);
        rewardInfoList.add(reward2);
        request.setRewardInfo(rewardInfoList);
        // act & assert
        assertDoesNotThrow(() -> validator.validate(request, "create"));
    }

    /**
     * 测试异常情况 - rewardInfo列表为空
     * 根据实现，空列表不会抛出异常，因为checkRewardListParam方法只检查列表大小>1时的情况
     */
    @Test
    public void testValidateWithEmptyRewardInfo() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setRewardInfo(Collections.emptyList());
        // act & assert
        assertDoesNotThrow(() -> validator.validate(request, "create"));
    }

    /**
     * 测试异常情况 - rewardInfo列表为null
     * 根据实现，会抛出NullPointerException
     */
    @Test
    public void testValidateWithNullRewardInfo() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setRewardInfo(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> validator.validate(request, "create"));
    }

    /**
     * 测试异常情况 - rewardInfo列表包含无效数据
     * 1. 相同stage的奖励
     * 2. 邀请人数设置不正确
     */
    @Test
    public void testValidateWithInvalidRewardInfo() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        List<RewardInfoRequest> rewardInfoList = new ArrayList<>();
        // 创建两个stage相同但邀请人数不同的奖励
        RewardInfoRequest reward1 = new RewardInfoRequest();
        reward1.setStage(1);
        reward1.setInvitationNum(10);
        RewardInfoRequest reward2 = new RewardInfoRequest();
        // 相同stage
        reward2.setStage(1);
        reward2.setInvitationNum(20);
        rewardInfoList.add(reward1);
        rewardInfoList.add(reward2);
        request.setRewardInfo(rewardInfoList);
        // act & assert
        assertThrows(FissionValidatorException.class, () -> validator.validate(request, "create"));
    }

    /**
     * 测试异常情况 - 邀请人数设置不正确
     */
    @Test
    public void testValidateWithInvalidInvitationNum() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        List<RewardInfoRequest> rewardInfoList = new ArrayList<>();
        RewardInfoRequest reward1 = new RewardInfoRequest();
        reward1.setStage(1);
        // 高stage的邀请人数更少
        reward1.setInvitationNum(20);
        RewardInfoRequest reward2 = new RewardInfoRequest();
        reward2.setStage(2);
        reward2.setInvitationNum(10);
        rewardInfoList.add(reward1);
        rewardInfoList.add(reward2);
        request.setRewardInfo(rewardInfoList);
        // act & assert
        assertThrows(FissionValidatorException.class, () -> validator.validate(request, "create"));
    }

    @Test
    void testCheckRewardListParam_WhenEmptyList_ShouldReturnNull() throws Throwable {
        List<RewardInfoRequest> emptyList = Collections.emptyList();
        String result = invokePrivateMethod(validator, "checkRewardListParam", emptyList);
        assertNull(result, "Empty list should return null");
    }

    @Test
    void testCheckRewardListParam_WhenSingleElement_ShouldReturnNull() throws Throwable {
        RewardInfoRequest reward = mock(RewardInfoRequest.class);
        List<RewardInfoRequest> singleElementList = Collections.singletonList(reward);
        String result = invokePrivateMethod(validator, "checkRewardListParam", singleElementList);
        assertNull(result, "Single element list should return null");
    }

    @Test
    void testCheckRewardListParam_WhenDuplicateStages_ShouldReturnErrorMessage() throws Throwable {
        RewardInfoRequest reward1 = mock(RewardInfoRequest.class);
        when(reward1.getStage()).thenReturn(1);
        RewardInfoRequest reward2 = mock(RewardInfoRequest.class);
        when(reward2.getStage()).thenReturn(1);
        List<RewardInfoRequest> rewardInfoList = Arrays.asList(reward1, reward2);
        String result = invokePrivateMethod(validator, "checkRewardListParam", rewardInfoList);
        assertEquals("活动对应奖品阶段设置不正确", result, "Should return error message for duplicate stages");
    }

    @Test
    void testCheckRewardListParam_WhenInvitationNumNotIncreasing_ShouldReturnErrorMessage() throws Throwable {
        RewardInfoRequest reward1 = mock(RewardInfoRequest.class);
        when(reward1.getStage()).thenReturn(1);
        when(reward1.getInvitationNum()).thenReturn(20);
        RewardInfoRequest reward2 = mock(RewardInfoRequest.class);
        when(reward2.getStage()).thenReturn(2);
        when(reward2.getInvitationNum()).thenReturn(15);
        List<RewardInfoRequest> rewardInfoList = Arrays.asList(reward1, reward2);
        String result = invokePrivateMethod(validator, "checkRewardListParam", rewardInfoList);
        assertEquals("活动对应奖品阶段邀请人数设置不正确", result, "Should return error message for non-increasing invitation numbers");
    }

    @Test
    void testCheckRewardListParam_WhenValidList_ShouldReturnNull() throws Throwable {
        RewardInfoRequest reward1 = mock(RewardInfoRequest.class);
        when(reward1.getStage()).thenReturn(1);
        when(reward1.getInvitationNum()).thenReturn(10);
        RewardInfoRequest reward2 = mock(RewardInfoRequest.class);
        when(reward2.getStage()).thenReturn(2);
        when(reward2.getInvitationNum()).thenReturn(20);
        RewardInfoRequest reward3 = mock(RewardInfoRequest.class);
        when(reward3.getStage()).thenReturn(3);
        when(reward3.getInvitationNum()).thenReturn(30);
        List<RewardInfoRequest> rewardInfoList = Arrays.asList(reward1, reward2, reward3);
        String result = invokePrivateMethod(validator, "checkRewardListParam", rewardInfoList);
        assertNull(result, "Valid list should return null");
    }

    @Test
    void testCheckRewardListParam_WhenListContainsNull_ShouldThrowException() throws Throwable {
        RewardInfoRequest reward1 = mock(RewardInfoRequest.class);
        List<RewardInfoRequest> rewardInfoList = Arrays.asList(reward1, null);
        InvocationTargetException exception = assertThrows(InvocationTargetException.class, () -> invokePrivateMethod(validator, "checkRewardListParam", rewardInfoList), "Should throw InvocationTargetException");
        assertTrue(exception.getCause() instanceof NullPointerException, "Cause should be NullPointerException");
    }
}
