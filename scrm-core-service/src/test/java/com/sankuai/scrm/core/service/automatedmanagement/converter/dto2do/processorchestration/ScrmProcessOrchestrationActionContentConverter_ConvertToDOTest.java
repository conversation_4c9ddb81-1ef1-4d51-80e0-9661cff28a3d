package com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionContentDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActionContentDO;
import org.junit.runner.RunWith;
import java.util.Date;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmProcessOrchestrationActionContentConverter_ConvertToDOTest {

    private final ScrmProcessOrchestrationActionContentConverter converter = new ScrmProcessOrchestrationActionContentConverter();

    /**
     * Tests the convertToDO method with null input, expecting a null result.
     */
    @Test
    public void testConvertToDONullInput() throws Throwable {
        // Arrange
        ScrmProcessOrchestrationActionContentDTO resource = null;
        // Act
        ScrmAmProcessOrchestrationActionContentDO result = converter.convertToDO(resource);
        // Assert
        assertNull(result);
    }

    /**
     * Tests the convertToDO method with non-null input, expecting a converted object.
     */
    @Test
    public void testConvertToDONonNullInput() throws Throwable {
        // Arrange
        ScrmProcessOrchestrationActionContentDTO resource = new ScrmProcessOrchestrationActionContentDTO();
        resource.setId(1L);
        resource.setActionId(1);
        resource.setContentId(1);
        resource.setProcessOrchestrationId(1L);
        resource.setProcessOrchestrationVersion("1.0");
        resource.setProcessOrchestrationNodeId(1L);
        resource.setContent("content");
        Date updateTime = new Date();
        resource.setUpdateTime(updateTime);
        resource.setContentType(1);
        // Act
        ScrmAmProcessOrchestrationActionContentDO result = converter.convertToDO(resource);
        // Assert
        assertNotNull(result);
        assertEquals(resource.getId(), result.getId());
        assertEquals(resource.getActionId(), result.getActionId());
        assertEquals(resource.getContentId(), result.getContentId());
        assertEquals(resource.getProcessOrchestrationId(), result.getProcessOrchestrationId());
        assertEquals(resource.getProcessOrchestrationVersion(), result.getProcessOrchestrationVersion());
        assertEquals(resource.getProcessOrchestrationNodeId(), result.getProcessOrchestrationNodeId());
        assertEquals(resource.getContent(), result.getContent());
        assertEquals(updateTime, result.getUpdateTime());
        assertEquals(resource.getContentType().intValue(), (int) result.getContentType());
    }
}
