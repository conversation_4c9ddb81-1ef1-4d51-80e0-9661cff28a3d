package com.sankuai.scrm.core.service.dashboard.crane;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.external.contact.dal.example.ContactUserExample;
import com.sankuai.scrm.core.service.group.dal.example.MemberInfoEntityExample;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Date;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UserDataTaskGetMemberInfoEntityTotalExampleTest {

    private UserDataTask userDataTask = new UserDataTask();

    @Test
    public void testGetMemberInfoEntityTotalExampleNormal() throws Throwable {
        Date start = new Date();
        Date end = new Date();
        String corpId = "testCorpId";
        java.lang.reflect.Method method = UserDataTask.class.getDeclaredMethod("getMemberInfoEntityTotalExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        method.invoke(userDataTask, start, end, corpId);
        assertTrue(true);
    }

    @Test(expected = RuntimeException.class)
    public void testGetMemberInfoEntityTotalExampleStartNull() throws Throwable {
        Date start = null;
        Date end = new Date();
        String corpId = "testCorpId";
        java.lang.reflect.Method method = UserDataTask.class.getDeclaredMethod("getMemberInfoEntityTotalExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        try {
            method.invoke(userDataTask, start, end, corpId);
        } catch (Exception e) {
            if (e.getCause() instanceof RuntimeException) {
                throw (RuntimeException) e.getCause();
            }
            throw e;
        }
    }

    @Test(expected = RuntimeException.class)
    public void testGetMemberInfoEntityTotalExampleEndNull() throws Throwable {
        Date start = new Date();
        Date end = null;
        String corpId = "testCorpId";
        java.lang.reflect.Method method = UserDataTask.class.getDeclaredMethod("getMemberInfoEntityTotalExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        try {
            method.invoke(userDataTask, start, end, corpId);
        } catch (Exception e) {
            if (e.getCause() instanceof RuntimeException) {
                throw (RuntimeException) e.getCause();
            }
            throw e;
        }
    }

    @Test(expected = RuntimeException.class)
    public void testGetMemberInfoEntityTotalExampleCorpIdNull() throws Throwable {
        Date start = new Date();
        Date end = new Date();
        String corpId = null;
        java.lang.reflect.Method method = UserDataTask.class.getDeclaredMethod("getMemberInfoEntityTotalExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        try {
            method.invoke(userDataTask, start, end, corpId);
        } catch (Exception e) {
            if (e.getCause() instanceof RuntimeException) {
                throw (RuntimeException) e.getCause();
            }
            throw e;
        }
    }

    /**
     * Test case for normal scenario of getTotalContactUserExample method
     * Verifies that the method correctly creates a ContactUserExample with the given parameters
     */
    @Test
    public void testGetTotalContactUserExampleNormal() throws Throwable {
        // Given
        Date start = new Date(1000L);
        Date end = new Date(2000L);
        String corpId = "testCorpId";
        // When
        Method method = UserDataTask.class.getDeclaredMethod("getTotalContactUserExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        ContactUserExample result = (ContactUserExample) method.invoke(userDataTask, start, end, corpId);
        // Then
        assertNotNull(result);
        assertNotNull(result.getOredCriteria());
        assertEquals(1, result.getOredCriteria().size());
        assertNotNull(result.getOredCriteria().get(0).getCriteria());
        // Verify the criteria list contains exactly 2 elements
        assertEquals(2, result.getOredCriteria().get(0).getCriteria().size());
        // Verify corpId criterion
        assertEquals(corpId, result.getOredCriteria().get(0).getCriteria().get(0).getValue());
        // Verify the between criterion which contains both start and end dates
        Object betweenCriterion = result.getOredCriteria().get(0).getCriteria().get(1);
        assertNotNull(betweenCriterion);
    }

    /**
     * Test case for minimum valid values scenario of getTotalContactUserExample method
     * Verifies that the method handles minimum valid values correctly
     */
    @Test
    public void testGetTotalContactUserExampleWithNullParams() throws Throwable {
        // Given
        // Epoch time
        Date start = new Date(0L);
        Date end = new Date(1L);
        // Empty string is valid, null is not
        String corpId = "";
        // When
        Method method = UserDataTask.class.getDeclaredMethod("getTotalContactUserExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        ContactUserExample result = (ContactUserExample) method.invoke(userDataTask, start, end, corpId);
        // Then
        assertNotNull(result);
        assertNotNull(result.getOredCriteria());
        assertEquals(1, result.getOredCriteria().size());
        assertNotNull(result.getOredCriteria().get(0).getCriteria());
        assertEquals(2, result.getOredCriteria().get(0).getCriteria().size());
        assertEquals("", result.getOredCriteria().get(0).getCriteria().get(0).getValue());
    }

    /**
     * Test case for boundary values scenario of getTotalContactUserExample method
     * Verifies that the method handles boundary date values correctly
     */
    @Test
    public void testGetTotalContactUserExampleBoundaryValues() throws Throwable {
        // Given
        Date start = new Date(Long.MIN_VALUE);
        Date end = new Date(Long.MAX_VALUE);
        String corpId = "test_corp_id_boundary";
        // When
        Method method = UserDataTask.class.getDeclaredMethod("getTotalContactUserExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        ContactUserExample result = (ContactUserExample) method.invoke(userDataTask, start, end, corpId);
        // Then
        assertNotNull(result);
        assertNotNull(result.getOredCriteria());
        assertEquals(1, result.getOredCriteria().size());
        assertNotNull(result.getOredCriteria().get(0).getCriteria());
        assertEquals(2, result.getOredCriteria().get(0).getCriteria().size());
        assertEquals(corpId, result.getOredCriteria().get(0).getCriteria().get(0).getValue());
    }

    /**
     * Test case for expected exception when corpId is null
     * Verifies that the method throws appropriate exception for null corpId
     */
    @Test(expected = InvocationTargetException.class)
    public void testGetTotalContactUserExampleWithNullCorpId() throws Throwable {
        // Given
        Date start = new Date();
        Date end = new Date();
        String corpId = null;
        // When
        Method method = UserDataTask.class.getDeclaredMethod("getTotalContactUserExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        // Should throw InvocationTargetException
        method.invoke(userDataTask, start, end, corpId);
    }

    /**
     * 测试 getMemberInfoEntityRetentionExample 方法，异常情况，start 为 null
     */
    @Test(expected = RuntimeException.class)
    public void testGetMemberInfoEntityRetentionExampleStartNull() throws Throwable {
        // arrange
        Date start = null;
        Date end = new Date(2000L);
        String corpId = "testCorpId";
        // act
        Method method = UserDataTask.class.getDeclaredMethod("getMemberInfoEntityRetentionExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        try {
            method.invoke(userDataTask, start, end, corpId);
        } catch (Exception e) {
            if (e.getCause() instanceof RuntimeException) {
                throw (RuntimeException) e.getCause();
            }
            throw e;
        }
    }

    /**
     * 测试 getMemberInfoEntityRetentionExample 方法，异常情况，end 为 null
     */
    @Test(expected = RuntimeException.class)
    public void testGetMemberInfoEntityRetentionExampleEndNull() throws Throwable {
        // arrange
        Date start = new Date(1000L);
        Date end = null;
        String corpId = "testCorpId";
        // act
        Method method = UserDataTask.class.getDeclaredMethod("getMemberInfoEntityRetentionExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        try {
            method.invoke(userDataTask, start, end, corpId);
        } catch (Exception e) {
            if (e.getCause() instanceof RuntimeException) {
                throw (RuntimeException) e.getCause();
            }
            throw e;
        }
    }

    /**
     * 测试 getMemberInfoEntityRetentionExample 方法，异常情况，corpId 为 null
     */
    @Test(expected = RuntimeException.class)
    public void testGetMemberInfoEntityRetentionExampleCorpIdNull() throws Throwable {
        // arrange
        Date start = new Date(1000L);
        Date end = new Date(2000L);
        String corpId = null;
        // act
        Method method = UserDataTask.class.getDeclaredMethod("getMemberInfoEntityRetentionExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        try {
            method.invoke(userDataTask, start, end, corpId);
        } catch (Exception e) {
            if (e.getCause() instanceof RuntimeException) {
                throw (RuntimeException) e.getCause();
            }
            throw e;
        }
    }

    @Test
    public void testGetMemberInfoEntityTotalExample_ValidInput() throws Throwable {
        Date start = new Date();
        Date end = new Date();
        String corpId = "validCorpId";
        Method method = UserDataTask.class.getDeclaredMethod("getMemberInfoEntityTotalExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        MemberInfoEntityExample result = (MemberInfoEntityExample) method.invoke(userDataTask, start, end, corpId);
        assertNotNull(result);
    }

    @Test(expected = InvocationTargetException.class)
    public void testGetMemberInfoEntityTotalExample_NullStart() throws Throwable {
        Date start = null;
        Date end = new Date();
        String corpId = "validCorpId";
        Method method = UserDataTask.class.getDeclaredMethod("getMemberInfoEntityTotalExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        method.invoke(userDataTask, start, end, corpId);
    }

    @Test(expected = InvocationTargetException.class)
    public void testGetMemberInfoEntityTotalExample_NullEnd() throws Throwable {
        Date start = new Date();
        Date end = null;
        String corpId = "validCorpId";
        Method method = UserDataTask.class.getDeclaredMethod("getMemberInfoEntityTotalExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        method.invoke(userDataTask, start, end, corpId);
    }

    @Test(expected = InvocationTargetException.class)
    public void testGetMemberInfoEntityTotalExample_NullCorpId() throws Throwable {
        Date start = new Date();
        Date end = new Date();
        String corpId = null;
        Method method = UserDataTask.class.getDeclaredMethod("getMemberInfoEntityTotalExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        method.invoke(userDataTask, start, end, corpId);
    }
}
