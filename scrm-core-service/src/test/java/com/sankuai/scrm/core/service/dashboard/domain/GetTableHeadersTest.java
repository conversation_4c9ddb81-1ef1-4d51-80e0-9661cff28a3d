package com.sankuai.scrm.core.service.dashboard.domain;

import com.sankuai.dz.srcm.dashboard.service.dto.TableHeader;
import com.sankuai.scrm.core.service.dashboard.constants.TableHeaderConstans;
import com.sankuai.scrm.core.service.dashboard.dal.enums.EsDataFields;
import com.sankuai.scrm.core.service.dashboard.dal.enums.Type;
import java.util.List;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

/**
 * Unit tests for GetTableHeaders
 */
@RunWith(MockitoJUnitRunner.class)
public class GetTableHeadersTest {

    // Other test methods remain unchanged
    /**
     * Test getting table headers for null type
     * Verifies that a NullPointerException is thrown for invalid type
     */
    @Test(expected = NullPointerException.class)
    public void testGetTableHeader_WhenTypeNull_ShouldReturnNull() throws Throwable {
        // arrange
        Type type = null;
        // act
        GetTableHeaders.getTableHeader(type);
        // assert is handled by the expected exception
    }

    /**
     * Test getting table headers for TOTAL_USER type
     * Verifies the correct number and order of headers
     */
    @Test
    public void testGetTableHeader_WhenTypeTotalUser_ShouldReturnCorrectHeaders() throws Throwable {
        // arrange
        Type type = Type.TOTAL_USER;
        // act
        List<TableHeader> headers = GetTableHeaders.getTableHeader(type);
        // assert
        Assert.assertEquals(4, headers.size());
        Assert.assertEquals(EsDataFields.CORP_NAME.getField(), headers.get(0).getProp());
        Assert.assertEquals(TableHeaderConstans.CORP_NAME, headers.get(0).getLabel());
        Assert.assertEquals(EsDataFields.CORP_WX_CUSTOMERS_COUNT.getField(), headers.get(1).getProp());
        Assert.assertEquals(TableHeaderConstans.CORP_WX_CUSTOMER_COUNT, headers.get(1).getLabel());
    }

    /**
     * Test getting table headers for ADD_USER type
     * Verifies the correct number and content of headers including increment fields
     */
    @Test
    public void testGetTableHeader_WhenTypeAddUser_ShouldReturnIncrementHeaders() throws Throwable {
        // arrange
        Type type = Type.ADD_USER;
        // act
        List<TableHeader> headers = GetTableHeaders.getTableHeader(type);
        // assert
        Assert.assertEquals(10, headers.size());
        Assert.assertEquals(EsDataFields.CUSTOMER_INCREMENT.getField(), headers.get(1).getProp());
        Assert.assertEquals(TableHeaderConstans.CUSTOMER_INCREMENT, headers.get(1).getLabel());
    }

    /**
     * Test getting table headers for LOST_USER type
     * Verifies the correct number and content of headers including decrement fields
     */
    @Test
    public void testGetTableHeader_WhenTypeLostUser_ShouldReturnDecrementHeaders() throws Throwable {
        // arrange
        Type type = Type.LOST_USER;
        // act
        List<TableHeader> headers = GetTableHeaders.getTableHeader(type);
        // assert
        Assert.assertEquals(10, headers.size());
        Assert.assertEquals(EsDataFields.CUSTOMER_DECREMENT.getField(), headers.get(1).getProp());
        Assert.assertEquals(TableHeaderConstans.CUSTOMER_DECREMENT, headers.get(1).getLabel());
    }

    /**
     * Test getting table headers for NET_ADDED_AND_RETAINED_USERS type
     * Verifies retention rate headers are included
     */
    @Test
    public void testGetTableHeader_WhenTypeNetAddedAndRetained_ShouldReturnRetentionHeaders() throws Throwable {
        // arrange
        Type type = Type.NET_ADDED_AND_RETAINED_USERS;
        // act
        List<TableHeader> headers = GetTableHeaders.getTableHeader(type);
        // assert
        Assert.assertEquals(4, headers.size());
        Assert.assertEquals(EsDataFields.NET_CUSTOMER_INCREMENT.getField(), headers.get(1).getProp());
        Assert.assertEquals(EsDataFields.WEEKLY_USER_RETENTION_RATE.getField(), headers.get(2).getProp());
    }

    /**
     * Test getting table headers for TALKING_CUSTOMER type
     * Verifies sender count headers are included
     */
    @Test
    public void testGetTableHeader_WhenTypeTalkingCustomer_ShouldReturnSenderHeaders() throws Throwable {
        // arrange
        Type type = Type.TALKING_CUSTOMER;
        // act
        List<TableHeader> headers = GetTableHeaders.getTableHeader(type);
        // assert
        Assert.assertEquals(4, headers.size());
        Assert.assertEquals(EsDataFields.IN_GROUP_SENDER_COUNT.getField(), headers.get(1).getProp());
        Assert.assertEquals(EsDataFields.PRIVATE_SENDER_COUNT.getField(), headers.get(2).getProp());
    }

    /**
     * Test getting table headers for ACTIVE_CUSTOMER type
     * Verifies participant count headers are included
     */
    @Test
    public void testGetTableHeader_WhenTypeActiveCustomer_ShouldReturnParticipantHeaders() throws Throwable {
        // arrange
        Type type = Type.ACTIVE_CUSTOMER;
        // act
        List<TableHeader> headers = GetTableHeaders.getTableHeader(type);
        // assert
        Assert.assertEquals(5, headers.size());
        Assert.assertEquals(EsDataFields.FISSION_PARTICIPANT_COUNT.getField(), headers.get(1).getProp());
        Assert.assertEquals(EsDataFields.PARTICIPANT_COUNT.getField(), headers.get(4).getProp());
    }

    /**
     * Test getting table headers for WANT_CUSTOMER type
     * Verifies click count headers are included
     */
    @Test
    public void testGetTableHeader_WhenTypeWantCustomer_ShouldReturnClickHeaders() throws Throwable {
        // arrange
        Type type = Type.WANT_CUSTOMER;
        // act
        List<TableHeader> headers = GetTableHeaders.getTableHeader(type);
        // assert
        Assert.assertEquals(6, headers.size());
        Assert.assertEquals(EsDataFields.CLICK_PRODUCTS_NOTIFICATION_COUNT.getField(), headers.get(1).getProp());
        Assert.assertEquals(EsDataFields.CLICK_CONTENT_COUNT.getField(), headers.get(5).getProp());
    }

    /**
     * Test getting table headers for WIDELY_TRANSACTION_DATA type
     * Verifies transaction headers are included with widely user count
     */
    @Test
    public void testGetTableHeader_WhenTypeWidelyTransaction_ShouldReturnTransactionHeaders() throws Throwable {
        // arrange
        Type type = Type.WIDELY_TRANSACTION_DATA;
        // act
        List<TableHeader> headers = GetTableHeaders.getTableHeader(type);
        // assert
        Assert.assertEquals(7, headers.size());
        Assert.assertEquals(EsDataFields.TRANSACTION_USER_COUNT_WIDELY.getField(), headers.get(1).getProp());
        Assert.assertEquals(EsDataFields.GTV.getField(), headers.get(3).getProp());
    }

    /**
     * Test getting table headers for NARROWLY_TRANSACTION_DATA type
     * Verifies transaction headers are included with narrowly user count
     */
    @Test
    public void testGetTableHeader_WhenTypeNarrowlyTransaction_ShouldReturnTransactionHeaders() throws Throwable {
        // arrange
        Type type = Type.NARROWLY_TRANSACTION_DATA;
        // act
        List<TableHeader> headers = GetTableHeaders.getTableHeader(type);
        // assert
        Assert.assertEquals(7, headers.size());
        Assert.assertEquals(EsDataFields.TRANSACTION_USER_COUNT_NARROWLY.getField(), headers.get(1).getProp());
        Assert.assertEquals(EsDataFields.GTV.getField(), headers.get(3).getProp());
    }
}
