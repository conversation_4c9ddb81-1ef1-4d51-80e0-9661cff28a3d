package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.dz.srcm.aigc.service.enums.IntelligentFollowActionType;
import com.sankuai.scrm.core.service.aigc.service.dal.entity.ScrmGroupRetailUserFootPrintRecordDO;
import com.sankuai.scrm.core.service.aigc.service.dal.mapper.ScrmGroupRetailUserFootPrintRecordDOMapper;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineDispatchProducer;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GroupRetailAiEngineUserFootprintLandConsumerTest {

    private static final String TEST_APP_ID = "testAppId";
    private static final Long TEST_USER_ID = 123L;
    private static final Long TEST_TIMESTAMP = 1622548800000L;
    private static final Long TEST_ITEM_ID = 456L;
    private static final String MT_PLATFORM = "mt";
    private static final String DP_PLATFORM = "dp";

    @InjectMocks
    private GroupRetailAiEngineUserFootprintLandConsumer consumer;

    @Mock(lenient = true)
    private IConsumerProcessor mockConsumer;

    @Mock(lenient = true)
    private RealTimeTaskConsumerConfig consumerConfig;

    @Mock(lenient = true)
    private GroupRetailAiEngineDispatchProducer dispatchProducer;

    @Mock(lenient = true)
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock(lenient = true)
    private ScrmGroupRetailUserFootPrintRecordDOMapper userFootPrintRecordDOMapper;

    @Captor
    private ArgumentCaptor<ScrmGroupRetailUserFootPrintRecordDO> recordCaptor;

    @BeforeEach
    void setUp() {
        when(consumerConfig.getTestAppId()).thenReturn(TEST_APP_ID);
        when(consumerConfig.getDispatchDelayTime()).thenReturn(1000L);
    }

    private void setConsumerField(GroupRetailAiEngineUserFootprintLandConsumer instance, IConsumerProcessor consumer) throws Exception {
        Field field = GroupRetailAiEngineUserFootprintLandConsumer.class.getDeclaredField("consumer");
        field.setAccessible(true);
        field.set(instance, consumer);
    }

    private IConsumerProcessor getConsumerField(GroupRetailAiEngineUserFootprintLandConsumer instance) throws Exception {
        Field field = GroupRetailAiEngineUserFootprintLandConsumer.class.getDeclaredField("consumer");
        field.setAccessible(true);
        return (IConsumerProcessor) field.get(instance);
    }

    /**
     * Test successful initialization of consumer with all required properties
     */
    @Test
    public void testAfterPropertiesSetSuccess() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class,withSettings().lenient())) {
            // Arrange
            IConsumerProcessor mockConsumer = mock(IConsumerProcessor.class, withSettings().lenient());
            mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), anyString())).thenReturn(mockConsumer);
            // Act
            consumer.afterPropertiesSet();
            // Assert
            mockedMafkaClient.verify(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), anyString()));
            verify(mockConsumer).recvMessageWithParallel(eq(String.class), any());
        }
    }

    /**
     * Test exception when building consumer factory fails
     */
    @Test
    public void testAfterPropertiesSetWhenbuildCommonConsumerFactoryFails() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class,withSettings().lenient())) {
            // Arrange
            mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), anyString())).thenThrow(new RuntimeException("Build consumer factory failed"));
            // Act & Assert
            assertThrows(RuntimeException.class, () -> consumer.afterPropertiesSet());
        }
    }

    /**
     * Test exception when registering message handler fails
     */
    @Test
    public void testAfterPropertiesSetWhenRegisterMessageHandlerFails() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class,withSettings().lenient())) {
            // Arrange
            IConsumerProcessor mockConsumer = mock(IConsumerProcessor.class);
            mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), anyString())).thenReturn(mockConsumer);
            lenient().doThrow(new RuntimeException("Register handler failed")).when(mockConsumer).recvMessageWithParallel(any(), any());
            // Act & Assert
            assertThrows(RuntimeException.class, () -> consumer.afterPropertiesSet());
        }
    }

    /**
     * Test that required properties are set correctly
     */
    @Test
    public void testAfterPropertiesSetSetsCorrectProperties() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            // Arrange
            IConsumerProcessor mockConsumer = mock(IConsumerProcessor.class, withSettings().lenient());
            mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), anyString())).thenAnswer(invocation -> {
                Properties props = invocation.getArgument(0);
                assertEquals("daozong", props.getProperty(ConsumerConstants.MafkaBGNamespace));
                assertEquals("com.sankuai.medicalcosmetology.scrm.core", props.getProperty(ConsumerConstants.MafkaClientAppkey));
                assertEquals("scrm.group.retail.ai.engine.user.footprint.land.consumer", props.getProperty(ConsumerConstants.SubscribeGroup));
                return mockConsumer;
            });
            // Act
            consumer.afterPropertiesSet();
            // Assert
            verify(mockConsumer).recvMessageWithParallel(eq(String.class), any());
        }
    }

    /**
     * Test that correct topic name is used when building consumer
     */
    @Test
    public void testAfterPropertiesSetUsesCorrectTopic() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            // Arrange
            IConsumerProcessor mockConsumer = mock(IConsumerProcessor.class,withSettings().lenient());
            mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), anyString())).thenAnswer(invocation -> {
                String topic = invocation.getArgument(1);
                assertEquals("scrm.group.retail.ai.engine.user.footprint.land", topic);
                return mockConsumer;
            });
            // Act
            consumer.afterPropertiesSet();
            // Assert
            verify(mockConsumer).recvMessageWithParallel(eq(String.class), any());
        }
    }

    /**
     * Tests successful destruction when consumer exists
     */
    @Test
    public void testDestroyWhenConsumerExistsSuccessfully() throws Throwable {
        // arrange
        GroupRetailAiEngineUserFootprintLandConsumer consumer = new GroupRetailAiEngineUserFootprintLandConsumer();
        setConsumerField(consumer, mockConsumer);
        // act
        consumer.destroy();
        // assert
        verify(mockConsumer, times(1)).close();
    }

    /**
     * Tests destruction when consumer exists but throws exception on close
     */
    @Test
    public void testDestroyWhenConsumerExistsButThrowsException() throws Throwable {
        // arrange
        GroupRetailAiEngineUserFootprintLandConsumer consumer = new GroupRetailAiEngineUserFootprintLandConsumer();
        setConsumerField(consumer, mockConsumer);
        lenient().doThrow(new RuntimeException("Close failed")).when(mockConsumer).close();
        // act & assert
        Exception exception = assertThrows(RuntimeException.class, () -> {
            consumer.destroy();
        });
        assertEquals("Close failed", exception.getMessage());
        verify(mockConsumer, times(1)).close();
    }

    /**
     * Tests destruction when consumer is null (should do nothing)
     */
    @Test
    public void testDestroyWhenConsumerIsNull() throws Throwable {
        // arrange
        GroupRetailAiEngineUserFootprintLandConsumer consumer = new GroupRetailAiEngineUserFootprintLandConsumer();
        setConsumerField(consumer, null);
        // act
        consumer.destroy();
        // assert
        assertNull(getConsumerField(consumer), "Consumer should remain null after destroy");
        verifyNoInteractions(mockConsumer);
    }

    private ConsumeStatus invokeRecvMessage(MafkaMessage<String> message, MessagetContext context) throws Exception {
        Method method = GroupRetailAiEngineUserFootprintLandConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        method.setAccessible(true);
        return (ConsumeStatus) method.invoke(consumer, message, context);
    }

    @Test
    void testRecvMessage_NullMessageBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class, withSettings().lenient());
        when(message.getBody()).thenReturn(null);
        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class));
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(userFootPrintRecordDOMapper, dispatchProducer);
    }

    @Test
    void testRecvMessage_ViewActionEarlyReturn() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class,withSettings().lenient());
        when(message.getBody()).thenReturn("{\"userTrackDTO\":{\"actionlist\":[\"view\"],\"typelist\":[\"deal\"]," + "\"platformlist\":[\"mt\"],\"userid\":[123],\"timestamplist\":[1000],\"itemidlist\":[456]}}");
        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class,withSettings().lenient()));
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
//        verify(userFootPrintRecordDOMapper).insert(any());
        // verify(dispatchProducer,never()).sendDelayMessage(any(), anyLong());
    }

    @Test
    void testRecvMessage_MtPlatformClickProduct() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("{\"userTrackDTO\":{\"actionlist\":[\"click\"],\"typelist\":[\"deal\"]," + "\"platformlist\":[\"mt\"],\"userid\":[123],\"timestamplist\":[1000],\"itemidlist\":[456]}}");
        when(consumerConfig.getTestAppId()).thenReturn("testApp");
        when(consumerConfig.getDispatchDelayTime()).thenReturn(1000L);
        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class));
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
//        verify(userFootPrintRecordDOMapper).insert(any(ScrmGroupRetailUserFootPrintRecordDO.class));
        // verify(dispatchProducer).sendDelayMessage(any(GroupRetailAiEngineDispatchMessageDTO.class), anyLong());
    }

    @Test
    void testRecvMessage_MtPlatformClickShop() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("{\"userTrackDTO\":{\"actionlist\":[\"click\"],\"typelist\":[\"poi\"]," + "\"platformlist\":[\"mt\"],\"userid\":[123],\"timestamplist\":[1000],\"itemidlist\":[456]}}");
        when(consumerConfig.getTestAppId()).thenReturn("testApp");
        when(consumerConfig.getDispatchDelayTime()).thenReturn(1000L);
        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class));
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
//        verify(userFootPrintRecordDOMapper).insert(any(ScrmGroupRetailUserFootPrintRecordDO.class));
        // verify(dispatchProducer).sendDelayMessage(any(GroupRetailAiEngineDispatchMessageDTO.class), anyLong());
    }

    @Test
    void testRecvMessage_DpPlatformWithUserConversion() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("{\"userTrackDTO\":{\"actionlist\":[\"click\"],\"typelist\":[\"deal\"]," + "\"platformlist\":[\"dp\"],\"userid\":[123],\"timestamplist\":[1000],\"itemidlist\":[456]}}");
        // when(mtUserCenterAclService.getMtUserIdByDpUserId(anyLong())).thenReturn(789L);
        when(consumerConfig.getTestAppId()).thenReturn("testApp");
        when(consumerConfig.getDispatchDelayTime()).thenReturn(1000L);
        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class));
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
//        verify(userFootPrintRecordDOMapper).insert(any(ScrmGroupRetailUserFootPrintRecordDO.class));
        // verify(dispatchProducer).sendDelayMessage(any(GroupRetailAiEngineDispatchMessageDTO.class), anyLong());
    }

    @Test
    void testRecvMessage_AddToCartAction() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("{\"userTrackDTO\":{\"actionlist\":[\"atc\"],\"typelist\":[\"deal\"]," + "\"platformlist\":[\"mt\"],\"userid\":[123],\"timestamplist\":[1000],\"itemidlist\":[456]}}");
        when(consumerConfig.getTestAppId()).thenReturn("testApp");
        when(consumerConfig.getDispatchDelayTime()).thenReturn(1000L);
        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class));
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
//        verify(userFootPrintRecordDOMapper).insert(any(ScrmGroupRetailUserFootPrintRecordDO.class));
        // verify(dispatchProducer).sendDelayMessage(any(GroupRetailAiEngineDispatchMessageDTO.class), anyLong());
    }

    @Test
    void testRecvMessage_OrderAction() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("{\"userTrackDTO\":{\"actionlist\":[\"ord\"],\"typelist\":[\"deal\"]," + "\"platformlist\":[\"mt\"],\"userid\":[123],\"timestamplist\":[1000],\"itemidlist\":[456]}}");
        when(consumerConfig.getTestAppId()).thenReturn("testApp");
        when(consumerConfig.getDispatchDelayTime()).thenReturn(1000L);
        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class));
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
//        verify(userFootPrintRecordDOMapper).insert(any(ScrmGroupRetailUserFootPrintRecordDO.class));
        // verify(dispatchProducer).sendDelayMessage(any(GroupRetailAiEngineDispatchMessageDTO.class), anyLong());
    }

    @Test
    void testRecvMessage_PayAction() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("{\"userTrackDTO\":{\"actionlist\":[\"pay\"],\"typelist\":[\"deal\"]," + "\"platformlist\":[\"mt\"],\"userid\":[123],\"timestamplist\":[1000],\"itemidlist\":[456],\"shopidlist\":[456]}}");
        when(consumerConfig.getTestAppId()).thenReturn("testApp");
        when(consumerConfig.getDispatchDelayTime()).thenReturn(1000L);
        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class));
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
//        verify(userFootPrintRecordDOMapper).insert(any(ScrmGroupRetailUserFootPrintRecordDO.class));
        // verify(dispatchProducer).sendDelayMessage(any(GroupRetailAiEngineDispatchMessageDTO.class), anyLong());
    }

    @Test
    void testRecvMessage_InvalidPlatformValue() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("{\"userTrackDTO\":{\"actionlist\":[\"click\"],\"typelist\":[\"deal\"]," + "\"platformlist\":[\"invalid\"],\"userid\":[123],\"timestamplist\":[1000],\"itemidlist\":[456]}}");
        when(consumerConfig.getTestAppId()).thenReturn("testApp");
        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class));
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
//        verify(userFootPrintRecordDOMapper).insert(any(ScrmGroupRetailUserFootPrintRecordDO.class));
//        verify(dispatchProducer, never()).sendDelayMessage(any(), anyLong());

    }

    @Test
    void testRecvMessage_ExceptionDuringProcessing() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class,withSettings().lenient());
        when(message.getBody()).thenReturn("invalid json");
        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class));
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper, never()).insert(any());
        verify(dispatchProducer, never()).sendDelayMessage(any(), anyLong());
    }

    @Test
    void testRecvMessage_MissingRequiredFields() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class,withSettings().lenient());
        when(message.getBody()).thenReturn("{\"userTrackDTO\":{\"actionlist\":[\"click\"]}}");
        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class));
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper, never()).insert(any());
        verify(dispatchProducer, never()).sendDelayMessage(any(), anyLong());
    }

    @Test
    void testRecvMessage_NullUserIdOrAppId() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class,withSettings().lenient());
        when(message.getBody()).thenReturn("{\"userTrackDTO\":{\"actionlist\":[\"click\"],\"typelist\":[\"deal\"]," + "\"platformlist\":[\"mt\"],\"userid\":[],\"timestamplist\":[1000],\"itemidlist\":[456]}}");
        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class,withSettings().lenient()));
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper, never()).insert(any());
        verify(dispatchProducer, never()).sendDelayMessage(any(), anyLong());
    }

    @Test
    void testRecvMessage_OrderActionSuccessfulInsert() throws Exception {
        // arrange
        String messageBody = String.format(
            "{\"userTrackDTO\":{\"actionlist\":[\"ord\"],\"typelist\":[\"deal\"]," +
            "\"platformlist\":[\"%s\"],\"userid\":[%d],\"timestamplist\":[%d],\"itemidlist\":[%d]}}",
            MT_PLATFORM, TEST_USER_ID, TEST_TIMESTAMP, TEST_ITEM_ID
        );
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(messageBody);
        
        // Mock valid action types to include ORDER
        when(consumerConfig.getValidActionTypes()).thenReturn(Arrays.asList(IntelligentFollowActionType.ORDER.getCode()));
        when(consumerConfig.getValidAppId( any(),eq(null))).thenReturn(TEST_APP_ID);

        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class));

        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        
        // Verify insert was called and capture the argument
        verify(userFootPrintRecordDOMapper).insert(recordCaptor.capture());
        
        // Verify the captured record
        ScrmGroupRetailUserFootPrintRecordDO capturedRecord = recordCaptor.getValue();
        assertEquals(MT_PLATFORM, capturedRecord.getPlatForm());
        assertEquals(TEST_USER_ID, capturedRecord.getMtUserid());
        assertEquals(TEST_TIMESTAMP, capturedRecord.getActionTimestamp());
        assertEquals(IntelligentFollowActionType.ORDER.getCode(), capturedRecord.getActionType());
        assertEquals(String.valueOf(TEST_ITEM_ID), capturedRecord.getActionContent());
        // assertEquals(TEST_APP_ID, capturedRecord.getAppId());

        // Verify dispatch message was sent
        /*verify(dispatchProducer).sendDelayMessage(
            argThat(msg -> msg.getUserId().equals(TEST_USER_ID) && msg.getAppId().equals(TEST_APP_ID)),
            eq(1000L)
        );*/
    }

    @Test
    void    testRecvMessage_PayActionSuccessfulInsert() throws Exception {
        // arrange
        String messageBody = String.format(
            "{\"userTrackDTO\":{\"actionlist\":[\"pay\"],\"typelist\":[\"deal\"]," +
            "\"platformlist\":[\"%s\"],\"userid\":[%d],\"timestamplist\":[%d],\"itemidlist\":[%d],\"shopidlist\":[%d]}}",
            MT_PLATFORM, TEST_USER_ID, TEST_TIMESTAMP, TEST_ITEM_ID,TEST_ITEM_ID
        );
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(messageBody);
        
        // Mock valid action types to include PAY
        when(consumerConfig.getValidActionTypes()).thenReturn(Arrays.asList(IntelligentFollowActionType.PAY.getCode()));
        when(consumerConfig.getValidAppId( any(),eq(null))).thenReturn(TEST_APP_ID);

        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class));

        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        
        // Verify insert was called and capture the argument
        verify(userFootPrintRecordDOMapper).insert(recordCaptor.capture());
        
        // Verify the captured record
        ScrmGroupRetailUserFootPrintRecordDO capturedRecord = recordCaptor.getValue();
        assertEquals(MT_PLATFORM, capturedRecord.getPlatForm());
        assertEquals(TEST_USER_ID, capturedRecord.getMtUserid());
        assertEquals(TEST_TIMESTAMP, capturedRecord.getActionTimestamp());
        assertEquals(IntelligentFollowActionType.PAY.getCode(), capturedRecord.getActionType());
        assertEquals(String.valueOf(TEST_ITEM_ID), capturedRecord.getActionContent());
        // assertEquals(TEST_APP_ID, capturedRecord.getAppId());

        // Verify dispatch message was sent
        /*verify(dispatchProducer).sendDelayMessage(
            argThat(msg -> msg.getUserId().equals(TEST_USER_ID) && msg.getAppId().equals(TEST_APP_ID)),
            eq(1000L)
        );*/
    }

    @Test
    void testRecvMessage_InvalidActionType() throws Exception {
        // arrange
        String messageBody = String.format(
            "{\"userTrackDTO\":{\"actionlist\":[\"ord\"],\"typelist\":[\"deal\"]," +
            "\"platformlist\":[\"%s\"],\"userid\":[%d],\"timestamplist\":[%d],\"itemidlist\":[%d]}}",
            MT_PLATFORM, TEST_USER_ID, TEST_TIMESTAMP, TEST_ITEM_ID
        );
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(messageBody);
        
        // Mock valid action types to NOT include ORDER
        when(consumerConfig.getValidActionTypes()).thenReturn(Collections.emptyList());

        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class));

        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        
        // Verify insert was NOT called
        verify(userFootPrintRecordDOMapper, never()).insert(any());
        
        // Verify dispatch message was still sent (because userId and appId are valid)
        /*verify(dispatchProducer).sendDelayMessage(
            argThat(msg -> msg.getUserId().equals(TEST_USER_ID) && msg.getAppId().equals(TEST_APP_ID)),
            eq(1000L)
        );*/
    }

    @Test
    void testRecvMessage_UndefinedAction() throws Exception {
        // arrange
        String messageBody = String.format(
            "{\"userTrackDTO\":{\"actionlist\":[\"click\"],\"typelist\":[\"deal\"]," +
            "\"platformlist\":[\"%s\"],\"userid\":[%d],\"timestamplist\":[%d],\"itemidlist\":[%d]}}",
            MT_PLATFORM, TEST_USER_ID, TEST_TIMESTAMP, TEST_ITEM_ID
        );
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(messageBody);
        
        // Mock valid action types (not relevant for this test as actionType will be null)
        when(consumerConfig.getValidActionTypes()).thenReturn(Arrays.asList(IntelligentFollowActionType.ORDER.getCode()));

        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class));

        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        
        // Verify insert was NOT called
        verify(userFootPrintRecordDOMapper, never()).insert(any());
        
        // Verify dispatch message was still sent (because userId and appId are valid)
        /*verify(dispatchProducer).sendDelayMessage(
            argThat(msg -> msg.getUserId().equals(TEST_USER_ID) && msg.getAppId().equals(TEST_APP_ID)),
            eq(1000L)
        );*/
    }

    @Test
    void testRecvMessage_DpPlatformOrderAction() throws Exception {
        // arrange
        Long dpUserId = 789L;
        String messageBody = String.format(
            "{\"userTrackDTO\":{\"actionlist\":[\"ord\"],\"typelist\":[\"deal\"]," +
            "\"platformlist\":[\"%s\"],\"userid\":[%d],\"timestamplist\":[%d],\"itemidlist\":[%d]}}",
            DP_PLATFORM, dpUserId, TEST_TIMESTAMP, TEST_ITEM_ID
        );
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(messageBody);
        
        // Mock valid action types to include ORDER
        when(consumerConfig.getValidActionTypes()).thenReturn(Arrays.asList(IntelligentFollowActionType.ORDER.getCode()));
        when(consumerConfig.getValidAppId( any(),eq(null))).thenReturn(TEST_APP_ID);

        // act
        ConsumeStatus result = invokeRecvMessage(message, mock(MessagetContext.class));

        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        
        // Verify insert was called and capture the argument
        verify(userFootPrintRecordDOMapper).insert(recordCaptor.capture());
        
        // Verify the captured record
        ScrmGroupRetailUserFootPrintRecordDO capturedRecord = recordCaptor.getValue();
        assertEquals(DP_PLATFORM, capturedRecord.getPlatForm());
        assertEquals(dpUserId, capturedRecord.getMtUserid()); // 当前逻辑直接使用DP用户ID
        assertEquals(TEST_TIMESTAMP, capturedRecord.getActionTimestamp());
        assertEquals(IntelligentFollowActionType.ORDER.getCode(), capturedRecord.getActionType());
        assertEquals(String.valueOf(TEST_ITEM_ID), capturedRecord.getActionContent());
        /*assertEquals(TEST_APP_ID, capturedRecord.getAppId());

        // Verify dispatch message was sent
        verify(dispatchProducer).sendDelayMessage(
            argThat(msg -> msg.getUserId().equals(dpUserId) && msg.getAppId().equals(TEST_APP_ID)),
            eq(1000L)
        );*/
    }
}
