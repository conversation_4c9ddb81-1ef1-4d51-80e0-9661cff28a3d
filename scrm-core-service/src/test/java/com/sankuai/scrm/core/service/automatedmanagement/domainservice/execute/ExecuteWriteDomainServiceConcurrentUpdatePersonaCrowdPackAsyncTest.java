package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.group.domain.GroupMemberDomainService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.util.IdConvertUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.stubbing.Answer;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import java.util.concurrent.TimeUnit;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

/**
 * Test class for ExecuteWriteDomainService.concurrentUpdatePersonaCrowdPackAsync method
 */
@RunWith(MockitoJUnitRunner.class)
public class ExecuteWriteDomainServiceConcurrentUpdatePersonaCrowdPackAsyncTest {

    @Mock
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock
    private IdConvertUtils idConvertUtils;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private ContactUserDomain contactUserDomain;

    @Mock
    private GroupMemberDomainService groupMemberDomainService;

    private static final String TEST_CORP_ID = "testCorpId";

    private static final String TEST_APP_ID = "testAppId";

    private static final String TEST_UNION_ID = "testUnionId";

    private static final String TEST_EXTERNAL_USER_ID = "testExternalUserId";

    private static final Long TEST_PACK_ID = 123L;

    private static final String TEST_PACK_VERSION = "1.0";

    private static final long ASYNC_WAIT_TIME = 2000L;

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    @Before
    public void setUp() {
        // Setup common test data and mock behaviors
        when(appConfigRepository.getCorpIdByAppId(any())).thenReturn(TEST_CORP_ID);
        ContactUser contactUser = new ContactUser();
        contactUser.setExternalUserId(TEST_EXTERNAL_USER_ID);
        List<ContactUser> contactUsers = Collections.singletonList(contactUser);
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(eq(TEST_CORP_ID), any())).thenReturn(contactUsers);
        MemberInfoEntity memberInfo = new MemberInfoEntity();
        memberInfo.setGroupMemberId(TEST_EXTERNAL_USER_ID);
        List<MemberInfoEntity> memberInfos = Collections.singletonList(memberInfo);
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(eq(TEST_CORP_ID), any())).thenReturn(memberInfos);
        // Default successful insertion
        when(crowdPackWriteDomainService.insertCrowdPackDetailInfo(any(), any(), any(), any(), any())).thenReturn(true);
    }

    /**
     * Test scenario: Valid input with successful execution
     */
    @Test
    public void testConcurrentUpdatePersonaCrowdPackAsync_ValidList() throws Throwable {
        // Arrange
        List<Long> mtUserIdList = Arrays.asList(1L, 2L, 3L);
        when(idConvertUtils.convertMtUserIdToUnionId(any(), eq(TEST_APP_ID))).thenReturn(TEST_UNION_ID);
        // Act
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(TEST_APP_ID, new HashSet<>(mtUserIdList), TEST_PACK_ID, TEST_PACK_VERSION);
        TimeUnit.MILLISECONDS.sleep(ASYNC_WAIT_TIME);
        // Assert
        verify(idConvertUtils, times(mtUserIdList.size())).convertMtUserIdToUnionId(any(), eq(TEST_APP_ID));
        verify(crowdPackWriteDomainService, times(mtUserIdList.size())).insertCrowdPackDetailInfo(eq(TEST_PACK_ID), eq(TEST_UNION_ID), any(), eq(TEST_APP_ID), eq(TEST_PACK_VERSION));
    }

    /**
     * Test scenario: Empty list of user IDs
     */
    @Test
    public void testConcurrentUpdatePersonaCrowdPackAsync_EmptyList() throws Throwable {
        // Act
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(TEST_APP_ID, new HashSet<>(), TEST_PACK_ID, TEST_PACK_VERSION);
        TimeUnit.MILLISECONDS.sleep(ASYNC_WAIT_TIME);
        // Assert
        verify(idConvertUtils, never()).convertMtUserIdToUnionId(any(), any());
        verify(crowdPackWriteDomainService, never()).insertCrowdPackDetailInfo(any(), any(), any(), any(), any());
    }

    /**
     * Test scenario: Missing required parameters
     */
    @Test
    public void testConcurrentUpdatePersonaCrowdPackAsync_MissingParams() throws Throwable {
        // Arrange
        List<Long> validUserList = Arrays.asList(1L, 2L);
        // Act & Assert - Missing appId
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync("", new HashSet<>(validUserList), TEST_PACK_ID, TEST_PACK_VERSION);
        TimeUnit.MILLISECONDS.sleep(ASYNC_WAIT_TIME);
        verify(crowdPackWriteDomainService, never()).insertCrowdPackDetailInfo(any(), any(), any(), any(), any());
        // Act & Assert - Missing packVersion
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(TEST_APP_ID, new HashSet<>(validUserList), TEST_PACK_ID, "");
        TimeUnit.MILLISECONDS.sleep(ASYNC_WAIT_TIME);
        verify(crowdPackWriteDomainService, never()).insertCrowdPackDetailInfo(any(), any(), any(), any(), any());
        // Act & Assert - Missing packId
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(TEST_APP_ID, new HashSet<>(validUserList), null, TEST_PACK_VERSION);
        TimeUnit.MILLISECONDS.sleep(ASYNC_WAIT_TIME);
        verify(crowdPackWriteDomainService, never()).insertCrowdPackDetailInfo(any(), any(), any(), any(), any());
    }

    /**
     * Test scenario: Mixed success and failure in unionId conversion
     */
    @Test
    public void testConcurrentUpdatePersonaCrowdPackAsync_MixedUnionIdConversion() throws Throwable {
        // Arrange
        List<Long> mtUserIdList = Arrays.asList(1L, 2L, 3L);
        when(idConvertUtils.convertMtUserIdToUnionId(any(), eq(TEST_APP_ID))).thenAnswer((Answer<String>) invocation -> {
            Long userId = invocation.getArgument(0);
            if (userId == 2L) {
                return null;
            }
            return "unionId" + userId;
        });
        // Act
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(TEST_APP_ID, new HashSet<>(mtUserIdList), TEST_PACK_ID, TEST_PACK_VERSION);
        TimeUnit.MILLISECONDS.sleep(ASYNC_WAIT_TIME);
        // Assert
        verify(idConvertUtils, times(mtUserIdList.size())).convertMtUserIdToUnionId(any(), eq(TEST_APP_ID));
        verify(crowdPackWriteDomainService, times(2)).insertCrowdPackDetailInfo(eq(TEST_PACK_ID), anyString(), any(), eq(TEST_APP_ID), eq(TEST_PACK_VERSION));
    }

    /**
     * Test scenario: Failed crowd pack insertion
     */
    @Test
    public void testConcurrentUpdatePersonaCrowdPackAsync_FailedInsertion() throws Throwable {
        // Arrange
        List<Long> mtUserIdList = Arrays.asList(1L, 2L, 3L);
        when(idConvertUtils.convertMtUserIdToUnionId(any(), eq(TEST_APP_ID))).thenReturn(TEST_UNION_ID);
        when(crowdPackWriteDomainService.insertCrowdPackDetailInfo(any(), any(), any(), any(), any())).thenReturn(false);
        // Act
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(TEST_APP_ID, new HashSet<>(mtUserIdList), TEST_PACK_ID, TEST_PACK_VERSION);
        TimeUnit.MILLISECONDS.sleep(ASYNC_WAIT_TIME);
        // Assert
        verify(idConvertUtils, times(mtUserIdList.size())).convertMtUserIdToUnionId(any(), eq(TEST_APP_ID));
        verify(crowdPackWriteDomainService, times(mtUserIdList.size())).insertCrowdPackDetailInfo(eq(TEST_PACK_ID), eq(TEST_UNION_ID), any(), eq(TEST_APP_ID), eq(TEST_PACK_VERSION));
    }

    /**
     * Test scenario: Exception handling during execution
     */
    @Test
    public void testConcurrentUpdatePersonaCrowdPackAsync_ExceptionHandling() throws Throwable {
        // Arrange
        List<Long> mtUserIdList = Arrays.asList(1L, 2L, 3L);
        when(idConvertUtils.convertMtUserIdToUnionId(any(), eq(TEST_APP_ID))).thenThrow(new RuntimeException("Test exception"));
        // Act
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(TEST_APP_ID, new HashSet<>(mtUserIdList), TEST_PACK_ID, TEST_PACK_VERSION);
        TimeUnit.MILLISECONDS.sleep(ASYNC_WAIT_TIME);
        // Assert
        verify(idConvertUtils, times(mtUserIdList.size())).convertMtUserIdToUnionId(any(), eq(TEST_APP_ID));
        verify(crowdPackWriteDomainService, never()).insertCrowdPackDetailInfo(any(), any(), any(), any(), any());
    }

    /**
     * Test scenario: Failed unionId conversion
     */
    @Test
    public void testConcurrentUpdatePersonaCrowdPackAsync_FailedUnionIdConversion() throws Throwable {
        // Arrange
        List<Long> mtUserIdList = Arrays.asList(1L, 2L, 3L);
        when(idConvertUtils.convertMtUserIdToUnionId(any(), eq(TEST_APP_ID))).thenReturn(null);
        // Act
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(TEST_APP_ID, new HashSet<>(mtUserIdList), TEST_PACK_ID, TEST_PACK_VERSION);
        TimeUnit.MILLISECONDS.sleep(ASYNC_WAIT_TIME);
        // Assert
        verify(idConvertUtils, times(mtUserIdList.size())).convertMtUserIdToUnionId(any(), eq(TEST_APP_ID));
        verify(crowdPackWriteDomainService, never()).insertCrowdPackDetailInfo(any(), any(), any(), any(), any());
    }

    /**
     * Test scenario: Invalid parameters (empty strings)
     */
    @Test
    public void testConcurrentUpdatePersonaCrowdPackAsync_InvalidParams() throws Throwable {
        // Arrange
        List<Long> mtUserIdList = Arrays.asList(1L, 2L, 3L);
        // Act & Assert - empty appId
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync("", new HashSet<>(mtUserIdList), TEST_PACK_ID, TEST_PACK_VERSION);
        TimeUnit.MILLISECONDS.sleep(ASYNC_WAIT_TIME);
        verify(crowdPackWriteDomainService, never()).insertCrowdPackDetailInfo(any(), any(), any(), any(), any());
        // Act & Assert - empty packVersion
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(TEST_APP_ID, new HashSet<>(mtUserIdList), TEST_PACK_ID, "");
        TimeUnit.MILLISECONDS.sleep(ASYNC_WAIT_TIME);
        verify(crowdPackWriteDomainService, never()).insertCrowdPackDetailInfo(any(), any(), any(), any(), any());
    }
}
