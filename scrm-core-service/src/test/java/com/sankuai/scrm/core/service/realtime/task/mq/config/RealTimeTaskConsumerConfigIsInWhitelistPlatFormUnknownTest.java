package com.sankuai.scrm.core.service.realtime.task.mq.config;

import com.dianping.tgc.process.enums.PlatformEnum;
import com.meituan.mdp.boot.starter.mdpcache.core.Cache;
import com.sankuai.scrm.core.service.realtime.task.domainservice.ScrmGrowthUserInfoDomainService;
import com.sankuai.scrm.core.service.realtime.task.dto.RealTimeTaskConsumerConfigDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class RealTimeTaskConsumerConfigIsInWhitelistPlatFormUnknownTest {

    @Mock
    private ScrmGrowthUserInfoDomainService scrmGrowthUserInfoDomainService;

    @Mock
    private Cache localCache;

    private RealTimeTaskConsumerConfigDTO configDTO;

    @InjectMocks
    private RealTimeTaskConsumerConfig realTimeTaskConsumerConfig;

    @BeforeEach
    void setUp() throws NoSuchFieldException, IllegalAccessException {
        configDTO = new RealTimeTaskConsumerConfigDTO();
        configDTO.setWhiteListSet(new HashSet<>(Arrays.asList(1001L, 1002L)));
        configDTO.setValidAppIds(Arrays.asList("app1", "app2"));
        // Set configDTO field using reflection
        Field field = RealTimeTaskConsumerConfig.class.getDeclaredField("configDTO");
        field.setAccessible(true);
        field.set(realTimeTaskConsumerConfig, configDTO);
        // Set localCache field using reflection
        Field cacheField = RealTimeTaskConsumerConfig.class.getDeclaredField("localCache");
        cacheField.setAccessible(true);
        cacheField.set(realTimeTaskConsumerConfig, localCache);
    }

    /**
     * Test case for empty userIds list
     * Should return false immediately
     */
    @Test
    public void testIsInWhitelistAppIdUnknownEmptyUserIds() throws Throwable {
        // arrange
        List<Long> userIds = Collections.emptyList();
        // act
        boolean result = realTimeTaskConsumerConfig.isInWhitelistAppIdUnknown(userIds, PlatformEnum.MT);
        // assert
        assertFalse(result);
        verifyNoInteractions(scrmGrowthUserInfoDomainService);
        verifyNoInteractions(localCache);
    }

    /**
     * Test case for user in whitelist
     * Should return true without checking cache or service
     */
    @Test
    public void testIsInWhitelistPlatFormUnknownUserInWhitelist() throws Throwable {
        // arrange
        List<Long> userIds = Arrays.asList(1001L, 2002L);
        // act
        boolean result = realTimeTaskConsumerConfig.isInWhitelistAppIdUnknown(userIds, PlatformEnum.MT);
        // assert
        assertTrue(result);
        verifyNoInteractions(scrmGrowthUserInfoDomainService);
        verifyNoInteractions(localCache);
    }

    /**
     * Test case for empty validAppIds
     * Should return false without checking cache or service
     */
    @Test
    public void testIsInWhitelistAppIdUnknownEmptyValidAppIds() throws Throwable {
        // arrange
        List<Long> userIds = Arrays.asList(3003L);
        configDTO.setValidAppIds(Collections.emptyList());
        // act
        boolean result = realTimeTaskConsumerConfig.isInWhitelistAppIdUnknown(userIds, PlatformEnum.MT);
        // assert
        assertFalse(result);
        verifyNoInteractions(scrmGrowthUserInfoDomainService);
        verifyNoInteractions(localCache);
    }

    /**
     * Test case for user found in cache
     * Should return cached value without calling service
     */
    @Test
    public void testIsInWhitelistPlatFormUnknownUserInCache() throws Throwable {
        // arrange
        List<Long> userIds = Arrays.asList(3003L);
        when(localCache.get(3003L, Boolean.class)).thenReturn(true);
        // act
        boolean result = realTimeTaskConsumerConfig.isInWhitelistAppIdUnknown(userIds, PlatformEnum.MT);
        // assert
        assertTrue(result);
        verifyNoInteractions(scrmGrowthUserInfoDomainService);
        verify(localCache).get(3003L, Boolean.class);
    }

    /**
     * Test case for user not in cache needs service call (MT platform)
     * Should call service and cache the result
     */
    @Test
    public void testIsInWhitelistPlatFormUnknownUserNotInCacheMTPlatform() throws Throwable {
        // arrange
        List<Long> userIds = Arrays.asList(3003L);
        when(localCache.get(3003L, Boolean.class)).thenReturn(null);
        when(scrmGrowthUserInfoDomainService.existUserOfAppId(3003L, Arrays.asList("app1", "app2"), PlatformEnum.MT)).thenReturn(true);
        // act
        boolean result = realTimeTaskConsumerConfig.isInWhitelistAppIdUnknown(userIds, PlatformEnum.MT);
        // assert
        assertTrue(result);
        verify(scrmGrowthUserInfoDomainService).existUserOfAppId(3003L, Arrays.asList("app1", "app2"), PlatformEnum.MT);
        verify(localCache).put(3003L, true);
    }

    /**
     * Test case for user not in cache needs service call (DP platform)
     * Should call service and cache the result
     */
    @Test
    public void testIsInWhitelistPlatFormUnknownUserNotInCacheDPPlatform() throws Throwable {
        // arrange
        List<Long> userIds = Arrays.asList(3003L);
        when(localCache.get(3003L, Boolean.class)).thenReturn(null);
        when(scrmGrowthUserInfoDomainService.existUserOfAppId(3003L, Arrays.asList("app1", "app2"), PlatformEnum.DP)).thenReturn(false);
        // act
        boolean result = realTimeTaskConsumerConfig.isInWhitelistAppIdUnknown(userIds, PlatformEnum.DP);
        // assert
        assertFalse(result);
        verify(scrmGrowthUserInfoDomainService).existUserOfAppId(3003L, Arrays.asList("app1", "app2"), PlatformEnum.DP);
        verify(localCache).put(3003L, false);
    }
}
