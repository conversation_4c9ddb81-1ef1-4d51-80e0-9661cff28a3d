package com.sankuai.scrm.core.service.dashboard.crane;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.dashboard.crane.ActiveDataTask;
import com.sankuai.scrm.core.service.dashboard.crane.DataTask;
import com.sankuai.scrm.core.service.dashboard.crane.UserDataTask;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DataTaskTest {

    @InjectMocks
    private DataTask dataTask;

    @Mock
    private UserDataTask userDataTask;

    @Mock
    private ActiveDataTask activeDataTask;

    /**
     * Tests the dataTask method when userDataTask.getDataAndPushToEs() method throws an exception.
     */
    @Test(expected = RuntimeException.class)
    public void testDataTaskUserDataTaskException() throws Throwable {
        // Arrange
        doThrow(new RuntimeException()).when(userDataTask).getDataAndPushToEs();
        // Act
        dataTask.dataTask();
    }

    /**
     * Tests the dataTask method when activeDataTask.getDataAndPushToEs() method throws an exception.
     */
    @Test(expected = RuntimeException.class)
    public void testDataTaskActiveDataTaskException() throws Throwable {
        // Arrange
        doThrow(new RuntimeException()).when(activeDataTask).getDataAndPushToEs();
        // Act
        dataTask.dataTask();
    }

    /**
     * Tests the dataTask method when both userDataTask.getDataAndPushToEs() and activeDataTask.getDataAndPushToEs() methods are expected to execute normally.
     */
    @Test
    public void testDataTaskNormal() throws Throwable {
        // Arrange
        doNothing().when(userDataTask).getDataAndPushToEs();
        doNothing().when(activeDataTask).getDataAndPushToEs();
        // Act
        dataTask.dataTask();
        // Assert
        verify(userDataTask, times(1)).getDataAndPushToEs();
        verify(activeDataTask, times(1)).getDataAndPushToEs();
    }
}
