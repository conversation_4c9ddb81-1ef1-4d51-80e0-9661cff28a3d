package com.sankuai.scrm.core.service.realtime.task.mq.producer;

import com.meituan.mafka.client.producer.AsyncDelayProducerResult;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

import java.lang.reflect.Field;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ScrmIntelligentFollowResultDelayMsgProducerSendAsyncDelayMessageTest {

    @Mock
    private IProducerProcessor<String, String> mockProducer;

    @Mock
    private Logger mockLogger;

    @InjectMocks
    private ScrmIntelligentFollowResultDelayMsgProducer producer;

    @BeforeEach
    void setUp() throws Exception {
        // 使用反射设置私有静态字段
        Field producerField = ScrmIntelligentFollowResultDelayMsgProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, mockProducer);
    }

    /**
     * 测试当传入DTO为null时返回false
     */
    @Test
    void testSendAsyncDelayMessageWhenDtoIsNull() throws Throwable {
        // arrange
        // act
        boolean result = producer.sendAsyncDelayMessage(null, 1000L);
        // assert
        assertFalse(result);
    }

    /**
     * 测试成功发送消息的情况
     */
    @Test
    void testSendAsyncDelayMessageWhenSendSuccess() throws Throwable {
        // arrange
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        AsyncDelayProducerResult mockResult = new AsyncDelayProducerResult(ProducerStatus.SEND_OK);
        when(mockProducer.sendAsyncDelayMessage(anyString(), anyLong(), any())).thenReturn(mockResult);
        // act
        boolean result = producer.sendAsyncDelayMessage(dto, 1000L);
        // assert
        assertTrue(result);
        verify(mockProducer).sendAsyncDelayMessage(anyString(), anyLong(), any());
    }

    /**
     * 测试发送失败的情况
     */
    @Test
    void testSendAsyncDelayMessageWhenSendFailure() throws Throwable {
        // arrange
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        AsyncDelayProducerResult mockResult = new AsyncDelayProducerResult(ProducerStatus.SEND_FAILURE, "error message");
        when(mockProducer.sendAsyncDelayMessage(anyString(), anyLong(), any())).thenReturn(mockResult);
        // act
        boolean result = producer.sendAsyncDelayMessage(dto, 1000L);
        // assert
        // 注意：即使发送失败，方法仍然返回true
        assertTrue(result);
        verify(mockProducer).sendAsyncDelayMessage(anyString(), anyLong(), any());
    }

    /**
     * 测试返回结果为null的情况
     */
    @Test
    void testSendAsyncDelayMessageWhenResultIsNull() throws Throwable {
        // arrange
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        when(mockProducer.sendAsyncDelayMessage(anyString(), anyLong(), any())).thenReturn(null);
        // act
        boolean result = producer.sendAsyncDelayMessage(dto, 1000L);
        // assert
        // 注意：即使结果为null，方法仍然返回true
        assertTrue(result);
        verify(mockProducer).sendAsyncDelayMessage(anyString(), anyLong(), any());
    }

    /**
     * 测试发送过程中抛出异常的情况
     */
    @Test
    void testSendAsyncDelayMessageWhenExceptionThrown() throws Throwable {
        // arrange
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        when(mockProducer.sendAsyncDelayMessage(anyString(), anyLong(), any())).thenThrow(new RuntimeException("test exception"));
        // act
        boolean result = producer.sendAsyncDelayMessage(dto, 1000L);
        // assert
        assertFalse(result);
        verify(mockProducer).sendAsyncDelayMessage(anyString(), anyLong(), any());
    }

    /**
     * 测试发送状态为SEND_UNSUPPORTED的情况
     */
    @Test
    void testSendAsyncDelayMessageWhenSendUnsupported() throws Throwable {
        // arrange
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        AsyncDelayProducerResult mockResult = new AsyncDelayProducerResult(ProducerStatus.SEND_UNSUPPORTED);
        when(mockProducer.sendAsyncDelayMessage(anyString(), anyLong(), any())).thenReturn(mockResult);
        // act
        boolean result = producer.sendAsyncDelayMessage(dto, 1000L);
        // assert
        // 注意：即使状态为不支持，方法仍然返回true
        assertTrue(result);
        verify(mockProducer).sendAsyncDelayMessage(anyString(), anyLong(), any());
    }
}
