package com.sankuai.scrm.core.service.infrastructure.acl.wx;

import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxMomentSendRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxMomentSendResponse;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class WxMomentSendAclTest {

    @InjectMocks
    private WxMomentSendAcl wxMomentSendAcl;

    @Mock(lenient = true)
    private WeChatTokenAcl weChatTokenAcl;

    @Mock(lenient = true)
    private CorpAppConfigRepository appConfigRepository;

    private WxMomentSendRequest request = new WxMomentSendRequest();

    private String appId = "appId";

    private String corpId = "corpId";

    private WeChatTokenResult tokenResultSuccess = WeChatTokenResult.success("access_token");

    private WeChatTokenResult tokenResultFailure = WeChatTokenResult.fail(-1, "Failure");

    @Test(expected = Exception.class)
    public void testSendMomentAppIdIsNull() throws Throwable {
        wxMomentSendAcl.sendMoment(request, null);
    }

    @Test(expected = Exception.class)
    public void testSendMomentCorpIdIsNull() throws Throwable {
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(null);
        wxMomentSendAcl.sendMoment(request, appId);
    }

    @Test(expected = Exception.class)
    public void testSendMomentTokenResultIsNull() throws Throwable {
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
        when(weChatTokenAcl.getTokenByCorpId(corpId)).thenReturn(null);
        wxMomentSendAcl.sendMoment(request, appId);
    }

    @Test(expected = Exception.class)
    public void testSendMomentTokenResultIsNotSuccess() throws Throwable {
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
        when(weChatTokenAcl.getTokenByCorpId(corpId)).thenReturn(tokenResultFailure);
        wxMomentSendAcl.sendMoment(request, appId);
    }

    @Test(expected = Exception.class)
    public void testSendMomentTokenResultAccessTokenIsEmpty() throws Throwable {
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
        when(weChatTokenAcl.getTokenByCorpId(corpId)).thenReturn(WeChatTokenResult.success(""));
        wxMomentSendAcl.sendMoment(request, appId);
    }

    @Test
    public void testSendMomentSuccess() throws Throwable {
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
        when(weChatTokenAcl.getTokenByCorpId(corpId)).thenReturn(tokenResultSuccess);
        WxMomentSendResponse response = wxMomentSendAcl.sendMoment(request, appId);
        assertNotNull(response);
    }
}
