package com.sankuai.scrm.core.service.external.contact.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.sankuai.dz.srcm.friend.dynamiccode.dto.FriendChannelCodeInfo;
import com.sankuai.dz.srcm.friend.dynamiccode.dto.FriendWelcomeMessageDTO;
import com.sankuai.dz.srcm.friend.dynamiccode.dto.TextDTO;
import com.sankuai.scrm.core.service.chat.dal.entity.WelcomeMessage;
import com.sankuai.scrm.core.service.chat.dal.mapper.WelcomeMessageMapper;
import com.sankuai.scrm.core.service.external.contact.bo.ExternalContactAddBO;
import com.sankuai.scrm.core.service.flowV2.domain.FlowEntryConfigDomainService;
import com.sankuai.scrm.core.service.friend.dynamiccode.constant.FriendDynamicCodeConstant;
import com.sankuai.scrm.core.service.friend.dynamiccode.domain.FriendChannelDynamicCodeDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxContactAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxContactUserDetail;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.userWechatCoupon.domainService.GetUserWechantCoupon;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.util.ArrayList;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

class FriendWelcomeMsgDomainServiceTest {

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private CorpWxAcl corpWxAclService;

    @Mock
    private CorpWxContactAcl corpWxContactAcl;

    @Mock
    private FriendChannelDynamicCodeDomainService friendChannelDynamicCodeDomainService;

    @Mock
    private WelcomeMessageMapper welcomeMessageMapper;

    @Mock
    private GetUserWechantCoupon getUserWechantCoupon;

    @Mock
    private FlowEntryConfigDomainService flowEntryConfigDomainService;

    @InjectMocks
    private FriendWelcomeMsgDomainService service;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test when required fields are missing in data
     */
    @Test
    public void testHandleFriendWelcomeMsgMissingRequiredFields() throws Throwable {
        // arrange
        ExternalContactAddBO data = new ExternalContactAddBO();
        // Missing required field
        data.setCorpId(null);
        WxContactUserDetail userDetail = new WxContactUserDetail();
        // act
        service.handleFriendWelcomeMsg(data, userDetail);
        // assert
        verifyNoInteractions(appConfigRepository, corpWxAclService, corpWxContactAcl, friendChannelDynamicCodeDomainService, welcomeMessageMapper, getUserWechantCoupon);
    }

    /**
     * Test when appId cannot be found for corpId
     */
    @Test
    public void testHandleFriendWelcomeMsgInvalidCorpId() throws Throwable {
        // arrange
        ExternalContactAddBO data = new ExternalContactAddBO();
        data.setCorpId("invalidCorp");
        data.setUserId("user1");
        data.setExternalUserId("extUser1");
        data.setWelcomeCode("welcome1");
        data.setState(FriendDynamicCodeConstant.FRIEND_CHANNEL_CODE_STATE_PREFIX + "123_456");
        WxContactUserDetail userDetail = new WxContactUserDetail();
        when(appConfigRepository.getAppIdByCorpId("invalidCorp")).thenReturn(null);
        // act
        service.handleFriendWelcomeMsg(data, userDetail);
        // assert
        verify(appConfigRepository).getAppIdByCorpId("invalidCorp");
        verifyNoMoreInteractions(corpWxAclService, corpWxContactAcl, friendChannelDynamicCodeDomainService, welcomeMessageMapper, getUserWechantCoupon);
    }

    /**
     * Test when token cannot be obtained
     */
    @Test
    public void testHandleFriendWelcomeMsgTokenFailure() throws Throwable {
        // arrange
        ExternalContactAddBO data = new ExternalContactAddBO();
        data.setCorpId("validCorp");
        data.setUserId("user1");
        data.setExternalUserId("extUser1");
        data.setWelcomeCode("welcome1");
        data.setState(FriendDynamicCodeConstant.FRIEND_CHANNEL_CODE_STATE_PREFIX + "123_456");
        WxContactUserDetail userDetail = new WxContactUserDetail();
        when(appConfigRepository.getAppIdByCorpId("validCorp")).thenReturn("app1");
        when(corpWxAclService.getTokenByCorpId("validCorp")).thenReturn("");
        // act
        service.handleFriendWelcomeMsg(data, userDetail);
        // assert
        verify(appConfigRepository).getAppIdByCorpId("validCorp");
        verify(corpWxAclService).getTokenByCorpId("validCorp");
        verifyNoMoreInteractions(corpWxContactAcl, friendChannelDynamicCodeDomainService, welcomeMessageMapper, getUserWechantCoupon);
    }

    /**
     * Test when state format is invalid
     */
    @Test
    public void testHandleFriendWelcomeMsgInvalidStateFormat() throws Throwable {
        // arrange
        ExternalContactAddBO data = new ExternalContactAddBO();
        data.setCorpId("validCorp");
        data.setUserId("user1");
        data.setExternalUserId("extUser1");
        data.setWelcomeCode("welcome1");
        // Invalid format
        data.setState("invalid_state_format");
        WxContactUserDetail userDetail = new WxContactUserDetail();
        when(appConfigRepository.getAppIdByCorpId("validCorp")).thenReturn("app1");
        when(corpWxAclService.getTokenByCorpId("validCorp")).thenReturn("token1");
        // act
        service.handleFriendWelcomeMsg(data, userDetail);
        // assert
        verify(appConfigRepository).getAppIdByCorpId("validCorp");
        verify(corpWxAclService).getTokenByCorpId("validCorp");
        verifyNoMoreInteractions(corpWxContactAcl, friendChannelDynamicCodeDomainService, welcomeMessageMapper, getUserWechantCoupon);
    }

    /**
     * Test when friend channel code info cannot be found
     */
    @Test
    public void testHandleFriendWelcomeMsgFriendChannelCodeInfoNotFound() throws Throwable {
        // arrange
        ExternalContactAddBO data = new ExternalContactAddBO();
        data.setCorpId("validCorp");
        data.setUserId("user1");
        data.setExternalUserId("extUser1");
        data.setWelcomeCode("welcome1");
        data.setState(FriendDynamicCodeConstant.FRIEND_CHANNEL_CODE_STATE_PREFIX + "123_456");
        WxContactUserDetail userDetail = new WxContactUserDetail();
        when(appConfigRepository.getAppIdByCorpId("validCorp")).thenReturn("app1");
        when(corpWxAclService.getTokenByCorpId("validCorp")).thenReturn("token1");
        when(friendChannelDynamicCodeDomainService.queryFriendChannelCodeInfo("app1", 456L)).thenReturn(null);
        // act
        service.handleFriendWelcomeMsg(data, userDetail);
        // assert
        verify(appConfigRepository).getAppIdByCorpId("validCorp");
        verify(corpWxAclService).getTokenByCorpId("validCorp");
        verify(friendChannelDynamicCodeDomainService).queryFriendChannelCodeInfo("app1", 456L);
        verifyNoMoreInteractions(corpWxContactAcl, welcomeMessageMapper, getUserWechantCoupon);
    }

    /**
     * Test when state validation fails
     */
    @Test
    public void testHandleFriendWelcomeMsgStateValidationFails() throws Throwable {
        // arrange
        ExternalContactAddBO data = new ExternalContactAddBO();
        data.setCorpId("validCorp");
        data.setUserId("user1");
        data.setExternalUserId("extUser1");
        data.setWelcomeCode("welcome1");
        data.setState(FriendDynamicCodeConstant.FRIEND_CHANNEL_CODE_STATE_PREFIX + "123_456");
        WxContactUserDetail userDetail = new WxContactUserDetail();
        FriendChannelCodeInfo codeInfo = new FriendChannelCodeInfo();
        // Doesn't match state
        codeInfo.setFriendChannelId(789L);
        when(appConfigRepository.getAppIdByCorpId("validCorp")).thenReturn("app1");
        when(corpWxAclService.getTokenByCorpId("validCorp")).thenReturn("token1");
        when(friendChannelDynamicCodeDomainService.queryFriendChannelCodeInfo("app1", 456L)).thenReturn(codeInfo);
        // act
        service.handleFriendWelcomeMsg(data, userDetail);
        // assert
        verify(appConfigRepository).getAppIdByCorpId("validCorp");
        verify(corpWxAclService).getTokenByCorpId("validCorp");
        verify(friendChannelDynamicCodeDomainService).queryFriendChannelCodeInfo("app1", 456L);
        verifyNoMoreInteractions(corpWxContactAcl, welcomeMessageMapper, getUserWechantCoupon);
    }

    /**
     * Test when no welcome message is set
     */
    @Test
    public void testHandleFriendWelcomeMsgNoWelcomeMessage() throws Throwable {
        // arrange
        ExternalContactAddBO data = new ExternalContactAddBO();
        data.setCorpId("validCorp");
        data.setUserId("user1");
        data.setExternalUserId("extUser1");
        data.setWelcomeCode("welcome1");
        data.setState(FriendDynamicCodeConstant.FRIEND_CHANNEL_CODE_STATE_PREFIX + "123_456");
        WxContactUserDetail userDetail = new WxContactUserDetail();
        FriendChannelCodeInfo codeInfo = new FriendChannelCodeInfo();
        codeInfo.setFriendChannelId(123L);
        codeInfo.setId(456L);
        when(appConfigRepository.getAppIdByCorpId("validCorp")).thenReturn("app1");
        when(corpWxAclService.getTokenByCorpId("validCorp")).thenReturn("token1");
        when(friendChannelDynamicCodeDomainService.queryFriendChannelCodeInfo("app1", 456L)).thenReturn(codeInfo);
        when(flowEntryConfigDomainService.queryConfigByChannelId(123L)).thenReturn(null);
        when(welcomeMessageMapper.selectByPrimaryKey(any())).thenReturn(null);
        // act
        service.handleFriendWelcomeMsg(data, userDetail);
        // assert
        verify(appConfigRepository).getAppIdByCorpId("validCorp");
        verify(corpWxAclService).getTokenByCorpId("validCorp");
        verify(friendChannelDynamicCodeDomainService).queryFriendChannelCodeInfo("app1", 456L);
        verify(flowEntryConfigDomainService).queryConfigByChannelId(123L);
        verifyNoMoreInteractions(corpWxContactAcl, welcomeMessageMapper, getUserWechantCoupon);
    }



    /**
     * Test when welcome message content is empty
     */
    @Test
    public void testHandleFriendWelcomeMsgEmptyWelcomeMessageContent() throws Throwable {
        // arrange
        ExternalContactAddBO data = new ExternalContactAddBO();
        data.setCorpId("validCorp");
        data.setUserId("user1");
        data.setExternalUserId("extUser1");
        data.setWelcomeCode("welcome1");
        data.setState(FriendDynamicCodeConstant.FRIEND_CHANNEL_CODE_STATE_PREFIX + "123_456");
        WxContactUserDetail userDetail = new WxContactUserDetail();
        FriendChannelCodeInfo codeInfo = new FriendChannelCodeInfo();
        codeInfo.setFriendChannelId(123L);
        codeInfo.setId(456L);
        codeInfo.setWelcomeMsgId(789L);
        WelcomeMessage welcomeMessage = new WelcomeMessage();
        // Empty content
        welcomeMessage.setMsgList("");
        when(appConfigRepository.getAppIdByCorpId("validCorp")).thenReturn("app1");
        when(corpWxAclService.getTokenByCorpId("validCorp")).thenReturn("token1");
        when(friendChannelDynamicCodeDomainService.queryFriendChannelCodeInfo("app1", 456L)).thenReturn(codeInfo);
        when(flowEntryConfigDomainService.queryConfigByChannelId(123L)).thenReturn(null);
        when(welcomeMessageMapper.selectByPrimaryKey(789L)).thenReturn(welcomeMessage);
        // act
        service.handleFriendWelcomeMsg(data, userDetail);
        // assert
        verify(appConfigRepository).getAppIdByCorpId("validCorp");
        verify(corpWxAclService).getTokenByCorpId("validCorp");
        verify(friendChannelDynamicCodeDomainService).queryFriendChannelCodeInfo("app1", 456L);
        verify(flowEntryConfigDomainService).queryConfigByChannelId(123L);
        verify(welcomeMessageMapper).selectByPrimaryKey(789L);
        verifyNoInteractions(corpWxContactAcl);
        verifyNoInteractions(getUserWechantCoupon);
    }
}
