package com.sankuai.scrm.core.service.automatedmanagement.domainservice.activitypage;

import com.sankuai.dz.srcm.automatedmanagement.dto.activitypage.ActivityPageCreatePagesDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.activitypage.ActivityPageShelfInfoDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductItemsDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductTagsDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationProductItemsDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationProductTagMapDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductActivityPageDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductItemsDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductTagsDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationProductTagMapDOMapper;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import org.junit.Test;
import org.junit.jupiter.api.BeforeEach;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ActivityPageManagementServiceTest {

    @InjectMocks
    private ActivityPageManagementService activityPageManagementService;

    @Mock
    private ScrmAmProcessOrchestrationProductItemsDOMapper productItemsDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationProductTagsDOMapper productTagsDOMapper;

    @Mock
    private ExtScrmAmProcessOrchestrationProductTagMapDOMapper extScrmAmProcessOrchestrationProductTagMapDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationProductActivityPageDOMapper activityPageDOMapper;



    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试 convertPageDOToResultVO 方法，当输入为 null 时，应返回 null
     */
    @Test
    public void testConvertPageDOToResultVONullInput() {
        // arrange
        ScrmAmProcessOrchestrationProductActivityPageDO activityPageDO = null;
        // act
        ActivityPageShelfInfoDTO result = activityPageManagementService.convertPageDOToResultVO(activityPageDO);
        // assert
        assertNull(result);
    }

    /**
     * 测试 convertPageDOToResultVO 方法，当输入不为 null 时，应返回正确的 ActivityPageShelfInfoDTO 对象
     */
    @Test
    public void testConvertPageDOToResultVONonNullInput() {
        // arrange
        ScrmAmProcessOrchestrationProductActivityPageDO activityPageDO = new ScrmAmProcessOrchestrationProductActivityPageDO();
        activityPageDO.setBackGroundPicUrl("backgroundPicUrl");
        activityPageDO.setThumbPicUrl("thumbPicUrl");
        // act
        ActivityPageShelfInfoDTO result = activityPageManagementService.convertPageDOToResultVO(activityPageDO);
        // assert
        assertNotNull(result);
        assertEquals("backgroundPicUrl", result.getActivityPageImg());
        assertEquals("thumbPicUrl", result.getProgCover());
    }

    @Test
    public void testConvertPageDOToResultVOWithNullInput() {
        // arrange
        ScrmAmProcessOrchestrationProductActivityPageDO activityPageDO = null;
        // act
        ActivityPageShelfInfoDTO result = activityPageManagementService.convertPageDOToResultVO(activityPageDO);
        // assert
        assertNull(result);
    }

    /**
     * 测试创建活动页成功
     */
    @Test(expected = Exception.class)
    public void testCreateActivityPage_Success() throws Exception {
        // arrange
        String appId = "appId";
        ActivityPageCreatePagesDTO createPagesDTO = new ActivityPageCreatePagesDTO();
        createPagesDTO.setActivityPageTitle("有效标题");
        createPagesDTO.setProgTitle("有效小程序标题");
        createPagesDTO.setProgCover("封面URL");
        createPagesDTO.setActivityPageImg("活动页图片URL");
        createPagesDTO.setMiniProgramAppId("小程序AppId");
        createPagesDTO.setMiniProgramOriginAppId("原始小程序AppId");
        createPagesDTO.setProductIds(Arrays.asList(1L, 2L));
        //when(activityPageDOMapper.countByExample(any())).thenReturn(0L);
        //when(productItemsDOMapper.selectByExample(any())).thenReturn(Arrays.asList(new ScrmAmProcessOrchestrationProductItemsDO(), new ScrmAmProcessOrchestrationProductItemsDO()));
       // when(extScrmAmProcessOrchestrationProductTagMapDOMapper.batchInsert(any())).thenReturn(2);
       // when(activityPageDOMapper.insertSelective(any())).thenReturn(1);
        // act
        Long result = activityPageManagementService.createActivityPage(appId, createPagesDTO);
        // assert
        assertNotNull(result);
    }

    /**
     * 测试创建活动页失败，appId为空
     */
    @Test(expected = RuntimeException.class)
    public void testCreateActivityPage_Fail_AppIdIsNull() throws Exception {
        // arrange
        String appId = "";
        ActivityPageCreatePagesDTO createPagesDTO = new ActivityPageCreatePagesDTO();
        // act
        Long result = activityPageManagementService.createActivityPage(appId, createPagesDTO);
        // assert
        // Expected exception
        assertNull(result);
    }

    /**
     * 测试创建活动页失败，活动页标题已存在
     */
    @Test(expected = RuntimeException.class)
    public void testCreateActivityPage_Fail_ActivityPageTitleExists() throws Exception {
        // arrange
        String appId = "appId";
        ActivityPageCreatePagesDTO createPagesDTO = new ActivityPageCreatePagesDTO();
        createPagesDTO.setActivityPageTitle("重复标题");
        when(activityPageDOMapper.countByExample(any())).thenReturn(1L);
        // act
        activityPageManagementService.createActivityPage(appId, createPagesDTO);
        // assert
        // Expected exception
    }

    /**
     * 测试创建活动页失败，插入标签失败
     */
    @Test(expected = RuntimeException.class)
    public void testCreateActivityPage_Fail_InsertTagFail() throws Exception {
        // arrange
        String appId = "appId";
        ActivityPageCreatePagesDTO createPagesDTO = new ActivityPageCreatePagesDTO();
        createPagesDTO.setActivityPageTitle("有效标题");
        when(activityPageDOMapper.countByExample(any())).thenReturn(0L);
        // 模拟插入失败
        when(productTagsDOMapper.insertSelective(any())).thenReturn(0);
        // act
        activityPageManagementService.createActivityPage(appId, createPagesDTO);
        // assert
        // Expected exception
    }

    /**
     * 测试创建活动页失败，插入商品标签关系失败
     */
    @Test(expected = RuntimeException.class)
    public void testCreateActivityPage_Fail_InsertProductTagsRelationsFail() throws Exception {
        // arrange
        String appId = "appId";
        ActivityPageCreatePagesDTO createPagesDTO = new ActivityPageCreatePagesDTO();
        createPagesDTO.setActivityPageTitle("有效标题");
        createPagesDTO.setProductIds(Arrays.asList(1L, 2L));
        when(activityPageDOMapper.countByExample(any())).thenReturn(0L);
        when(productTagsDOMapper.insertSelective(any())).thenReturn(1);
        // 模拟插入失败
        //when(extScrmAmProcessOrchestrationProductTagMapDOMapper.batchInsert(any())).thenReturn(1);
        // act
        activityPageManagementService.createActivityPage(appId, createPagesDTO);
        // assert
        // Expected exception
    }

    /**
     * 测试插入标签成功
     */
    @Test
    public void testInsertTag_Success() {
        // arrange
        String appId = "testAppId";
        String tagName = "testTagName";
        Integer tagType = 1;
        ScrmAmProcessOrchestrationProductTagsDO tagsDO = ScrmAmProcessOrchestrationProductTagsDO.builder().id(1L).appId(appId).tagName(tagName).tagType(tagType).build();
        when(productTagsDOMapper.insertSelective(any(ScrmAmProcessOrchestrationProductTagsDO.class))).thenAnswer(invocation -> {
            ScrmAmProcessOrchestrationProductTagsDO arg = invocation.getArgument(0);
            // 模拟设置ID
            arg.setId(1L);
            return 1;
        });
        // act
        Long result = activityPageManagementService.insertTag(appId, tagName, tagType);
        // assert
        assertNotNull(result);
        assertEquals(Long.valueOf(1L), result);
    }

    /**
     * 测试appId为空抛出异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testInsertTag_AppIdEmpty() {
        // arrange
        String appId = "";
        String tagName = "testTagName";
        Integer tagType = 1;
        // act
        activityPageManagementService.insertTag(appId, tagName, tagType);
        // assert
        // Expected exception
    }

    /**
     * 测试tagName为空抛出异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testInsertTag_TagNameEmpty() {
        // arrange
        String appId = "testAppId";
        String tagName = "";
        Integer tagType = 1;
        // act
        activityPageManagementService.insertTag(appId, tagName, tagType);
        // assert
        // Expected exception
    }

    /**
     * 测试tagType为null抛出异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testInsertTag_TagTypeNull() {
        // arrange
        String appId = "testAppId";
        String tagName = "testTagName";
        Integer tagType = null;
        // act
        activityPageManagementService.insertTag(appId, tagName, tagType);
        // assert
        // Expected exception
    }

    @Test
    public void testInsertPageByDTO_Success() throws Exception {
        // arrange
        String appId = "testAppId";
        Long tagId = 1L;
        ActivityPageCreatePagesDTO dto = new ActivityPageCreatePagesDTO();
        dto.setActivityPageTitle("Test Title");
        dto.setProgTitle("Test Program Title");
        dto.setProgCover("Test Cover URL");
        dto.setActivityPageImg("Test Background URL");
        dto.setMiniProgramAppId("Test MiniProgram AppId");
        dto.setMiniProgramOriginAppId("Test MiniProgram Origin AppId");
        dto.setProductIds(Arrays.asList(1L, 2L, 3L));

        when(activityPageDOMapper.insertSelective(any(ScrmAmProcessOrchestrationProductActivityPageDO.class))).thenReturn(1);

        Method method = ActivityPageManagementService.class.getDeclaredMethod("insertPageByDTO", String.class, ActivityPageCreatePagesDTO.class, Long.class);
        method.setAccessible(true);

        // act
        Long result = (Long) method.invoke(activityPageManagementService, appId, dto, tagId);

        // assert
        assertNull(result);
    }

    /**
     * 测试appId为空时抛出异常
     */
    @Test(expected = Exception.class)
    public void testInsertPageByDTO_AppIdEmpty() throws Exception {
        // arrange
        String appId = "";
        Long tagId = 1L;
        ActivityPageCreatePagesDTO dto = new ActivityPageCreatePagesDTO();

        Method method = ActivityPageManagementService.class.getDeclaredMethod("insertPageByDTO", String.class, ActivityPageCreatePagesDTO.class, Long.class);
        method.setAccessible(true);

        // act
        method.invoke(activityPageManagementService, appId, dto, tagId);

        // assert 通过expected检查异常
    }

    /**
     * 测试activityPageCreatePagesDTO为null时抛出异常
     */
    @Test(expected = Exception.class)
    public void testInsertPageByDTO_DTONull() throws Exception {
        // arrange
        String appId = "testAppId";
        Long tagId = 1L;
        ActivityPageCreatePagesDTO dto = null;

        Method method = ActivityPageManagementService.class.getDeclaredMethod("insertPageByDTO", String.class, ActivityPageCreatePagesDTO.class, Long.class);
        method.setAccessible(true);

        // act
        method.invoke(activityPageManagementService, appId, dto, tagId);

        // assert 通过expected检查异常
    }

    /**
     * 测试tagId为null时抛出异常
     */
    @Test(expected = Exception.class)
    public void testInsertPageByDTO_TagIdNull() throws Exception {
        // arrange
        String appId = "testAppId";
        Long tagId = null;
        ActivityPageCreatePagesDTO dto = new ActivityPageCreatePagesDTO();

        Method method = ActivityPageManagementService.class.getDeclaredMethod("insertPageByDTO", String.class, ActivityPageCreatePagesDTO.class, Long.class);
        method.setAccessible(true);

        // act
        method.invoke(activityPageManagementService, appId, dto, tagId);

        // assert 通过expected检查异常
    }
    @Test
    public void testInsertProductTagsRelationsByIds_Success() throws Exception {
        // arrange
        String appId = "testAppId";
        Long tagId = 1L;
        List<Long> productIds = Arrays.asList(1L, 2L);
        List<ScrmAmProcessOrchestrationProductItemsDO> productItemsDOS = Arrays.asList(
                new ScrmAmProcessOrchestrationProductItemsDO(),
                new ScrmAmProcessOrchestrationProductItemsDO()
        );

        when(productItemsDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductItemsDOExample.class))).thenReturn(productItemsDOS);
        when(extScrmAmProcessOrchestrationProductTagMapDOMapper.batchInsert(any(List.class))).thenReturn(2);

        Method method = ActivityPageManagementService.class.getDeclaredMethod("insertProductTagsRelationsByIds", String.class, Long.class, List.class);
        method.setAccessible(true);

        // act
        boolean result = (boolean) method.invoke(activityPageManagementService, appId, tagId, productIds);

        // assert
        assertTrue(result);
    }

    /**
     * 测试appId为空时返回false
     */
    @Test
    public void testInsertProductTagsRelationsByIds_AppIdEmpty() throws Exception {
        // arrange
        String appId = "";
        Long tagId = 1L;
        List<Long> productIds = Arrays.asList(1L, 2L);

        Method method = ActivityPageManagementService.class.getDeclaredMethod("insertProductTagsRelationsByIds", String.class, Long.class, List.class);
        method.setAccessible(true);

        // act
        boolean result = (boolean) method.invoke(activityPageManagementService, appId, tagId, productIds);

        // assert
        assertFalse(result);
    }

    /**
     * 测试tagId为null时返回false
     */
    @Test
    public void testInsertProductTagsRelationsByIds_TagIdNull() throws Exception {
        // arrange
        String appId = "testAppId";
        Long tagId = null;
        List<Long> productIds = Arrays.asList(1L, 2L);

        Method method = ActivityPageManagementService.class.getDeclaredMethod("insertProductTagsRelationsByIds", String.class, Long.class, List.class);
        method.setAccessible(true);

        // act
        boolean result = (boolean) method.invoke(activityPageManagementService, appId, tagId, productIds);

        // assert
        assertFalse(result);
    }

    /**
     * 测试productIds为空时返回true
     */
    @Test
    public void testInsertProductTagsRelationsByIds_ProductIdsEmpty() throws Exception {
        // arrange
        String appId = "testAppId";
        Long tagId = 1L;
        List<Long> productIds = Arrays.asList();

        Method method = ActivityPageManagementService.class.getDeclaredMethod("insertProductTagsRelationsByIds", String.class, Long.class, List.class);
        method.setAccessible(true);

        // act
        boolean result = (boolean) method.invoke(activityPageManagementService, appId, tagId, productIds);

        // assert
        assertTrue(result);
    }
    @Test
    public void testGetProductIdsByItemIds_Success() throws Exception {
        // arrange
        String appId = "testAppId";
        List<Long> itemIds = Arrays.asList(1L, 2L);
        List<ScrmAmProcessOrchestrationProductItemsDO> productItemsDOS = itemIds.stream().map(itemId -> {
            ScrmAmProcessOrchestrationProductItemsDO productItemsDO = new ScrmAmProcessOrchestrationProductItemsDO();
            productItemsDO.setId(itemId);
            productItemsDO.setProductId(itemId + 10); // 假设productId是itemId+10
            return productItemsDO;
        }).collect(Collectors.toList());

        when(productItemsDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductItemsDOExample.class))).thenReturn(productItemsDOS);

        Method method = ActivityPageManagementService.class.getDeclaredMethod("getProductIdsByItemIds", String.class, List.class);
        method.setAccessible(true);

        // act
        List<Long> result = (List<Long>) method.invoke(activityPageManagementService, appId, itemIds);

        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertTrue(result.containsAll(Arrays.asList(11L, 12L)));
    }

    /**
     * 测试appId为空时返回空列表
     */
    @Test
    public void testGetProductIdsByItemIds_AppIdEmpty() throws Exception {
        // arrange
        String appId = "";
        List<Long> itemIds = Arrays.asList(1L, 2L);

        Method method = ActivityPageManagementService.class.getDeclaredMethod("getProductIdsByItemIds", String.class, List.class);
        method.setAccessible(true);

        // act
        List<Long> result = (List<Long>) method.invoke(activityPageManagementService, appId, itemIds);

        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试itemIds为空时返回空列表
     */
    @Test
    public void testGetProductIdsByItemIds_ItemIdsEmpty() throws Exception {
        // arrange
        String appId = "testAppId";
        List<Long> itemIds = new ArrayList<>();

        Method method = ActivityPageManagementService.class.getDeclaredMethod("getProductIdsByItemIds", String.class, List.class);
        method.setAccessible(true);

        // act
        List<Long> result = (List<Long>) method.invoke(activityPageManagementService, appId, itemIds);

        // assert
        assertTrue(result.isEmpty());
    }
    @Test
    public void testDeleteProductTagsRelationsByTagIds_Success() throws Exception {
        // arrange
        String appId = "testAppId";
        List<Long> tagIds = Arrays.asList(1L, 2L);

        Method method = ActivityPageManagementService.class.getDeclaredMethod("deleteProductTagsRelationsByTagIds", String.class, List.class);
        method.setAccessible(true);

        // act
        method.invoke(activityPageManagementService, appId, tagIds);

        // assert
        verify(extScrmAmProcessOrchestrationProductTagMapDOMapper, times(1)).deleteByExample(any(ScrmAmProcessOrchestrationProductTagMapDOExample.class));
    }

    /**
     * 测试tagIds为空时不执行删除操作
     */
    @Test
    public void testDeleteProductTagsRelationsByTagIds_TagIdsEmpty() throws Exception {
        // arrange
        String appId = "testAppId";
        List<Long> tagIds = Arrays.asList();

        Method method = ActivityPageManagementService.class.getDeclaredMethod("deleteProductTagsRelationsByTagIds", String.class, List.class);
        method.setAccessible(true);

        // act
        method.invoke(activityPageManagementService, appId, tagIds);

        // assert
        verify(extScrmAmProcessOrchestrationProductTagMapDOMapper, never()).deleteByExample(any(ScrmAmProcessOrchestrationProductTagMapDOExample.class));
    }

    /**
     * 测试appId为空时抛出RuntimeException
     */
    @Test(expected = Exception.class)
    public void testDeleteProductTagsRelationsByTagIds_AppIdEmpty() throws Exception {
        // arrange
        String appId = "";
        List<Long> tagIds = Arrays.asList(1L, 2L);

        Method method = ActivityPageManagementService.class.getDeclaredMethod("deleteProductTagsRelationsByTagIds", String.class, List.class);
        method.setAccessible(true);

        // act
        method.invoke(activityPageManagementService, appId, tagIds);

        // assert 通过expected检查异常
    }
}
