package com.sankuai.scrm.core.service.couponIntegration.utils;

import com.sankuai.dz.srcm.couponIntegration.dto.AddCouponResopnse;
import com.sankuai.dz.srcm.couponIntegration.dto.CouponList;
import com.sankuai.dz.srcm.couponIntegration.dto.GetCouponListQuery;
import com.sankuai.dz.srcm.couponIntegration.enums.CreationSceneEnum;
import com.sankuai.dz.srcm.couponIntegration.enums.FunctionModuleEnum;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.StrategistCouponInfo;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.StrategistCouponInfoExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.StrategistCouponInfoMapper;
import com.sankuai.scrm.core.service.couponIntegration.domain.CouponDashBoardDomainService;
import com.sankuai.scrm.core.service.couponIntegration.service.impl.CouponDataServiceImpl;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.text.SimpleDateFormat;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CouponIntegrationUtilTest {

    private static final ZoneId TEST_ZONE_ID = ZoneId.of("Asia/Shanghai");

    @InjectMocks
    private CouponIntegrationUtil couponIntegrationUtil;

    @Mock
    private StrategistCouponInfoMapper strategistCouponInfoMapper;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private CouponDashBoardDomainService couponDashBoardDomainService;

    @Mock
    private CouponDataServiceImpl couponDataService;

    private void invokePrivateMethod(String methodName, Object... args) throws Exception {
        // Assuming the actual method signature matches the parameters passed in the test case
        Method method = CouponIntegrationUtil.class.getDeclaredMethod(methodName, List.class, String.class, CreationSceneEnum.class, FunctionModuleEnum.class, String.class, LocalDateTime.class);
        method.setAccessible(true);
        method.invoke(couponIntegrationUtil, args);
    }

    /**
     * 测试 dateToString 方法，当 date 为 null 时
     */
    @Test(expected = NullPointerException.class)
    public void testDateToStringWhenDateIsNull() throws Throwable {
        // arrange
        Date date = null;
        // act
        couponIntegrationUtil.dateToString(date);
        // assert: expect NullPointerException
    }

    /**
     * 测试 dateToString 方法，当 date 不为 null 时
     */
    @Test
    public void testDateToStringWhenDateIsNotNull() throws Throwable {
        // arrange
        Date date = new Date();
        // act
        String result = couponIntegrationUtil.dateToString(date);
        // assert
        assertNotNull(result);
        assertTrue(result.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));
    }

    /**
     * Tests the localDateTimeToDate method when the input LocalDateTime object is null.
     * Since the method under test does not handle null inputs, this test is expected to throw a NullPointerException.
     */
    @Test(expected = NullPointerException.class)
    public void testLocalDateTimeToDateWhenLocalDateTimeIsNull() throws Throwable {
        // arrange
        LocalDateTime date = null;
        // act
        couponIntegrationUtil.localDateTimeToDate(date);
        // assert is handled by the expected exception
    }

    /**
     * Tests the localDateTimeToDate method when the input LocalDateTime object is not null.
     */
    @Test
    public void testLocalDateTimeToDateWhenLocalDateTimeIsNotNull() throws Throwable {
        // arrange
        LocalDateTime date = LocalDateTime.of(2022, 1, 1, 0, 0);
        // act
        Date result = couponIntegrationUtil.localDateTimeToDate(date);
        // assert
        assertNotNull(result);
        assertEquals(date.atZone(java.time.ZoneId.systemDefault()).toInstant(), result.toInstant());
    }

    @Test
    public void testStringToDateNormalCaseStartDate() throws Throwable {
        // arrange
        String dateString = "2021-12-30";
        boolean isEndDate = false;
        // act
        Date result = CouponIntegrationUtil.stringToDate(dateString, isEndDate);
        // assert
        // Expected: Thu Dec 30 00:00:00 CST 2021
        // Adjusted timestamp for 00:00:00
        assertEquals(new Date(1640793600000L), result);
    }

    @Test(expected = RuntimeException.class)
    public void testStringToDateExceptionCase() throws Throwable {
        // arrange
        String dateString = "invalid-date";
        boolean isEndDate = true;
        // act
        CouponIntegrationUtil.stringToDate(dateString, isEndDate);
    }

    /**
     * Tests the stringToDate method with a correctly formatted date string and isEndDate set to true.
     */
    @Test
    public void testStringToDateEndDateTrue() throws Throwable {
        // arrange
        String dateString = "2021-12-30";
        boolean isEndDate = true;
        // act
        Date result = CouponIntegrationUtil.stringToDate(dateString, isEndDate);
        // assert
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        // Adjust the expected date to the end of the day
        Date expected = sdf.parse("2021-12-30");
        // Adjust the result date to ignore the time part for comparison
        Date resultWithoutTime = new Date(result.getYear(), result.getMonth(), result.getDate());
        assertEquals(expected, resultWithoutTime);
    }

    /**
     * Tests the stringToDate method with a correctly formatted date string and isEndDate set to false.
     */
    @Test
    public void testStringToDateEndDateFalse() throws Throwable {
        // arrange
        String dateString = "2021-12-30";
        boolean isEndDate = false;
        // act
        Date result = CouponIntegrationUtil.stringToDate(dateString, isEndDate);
        // assert
        SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd");
        assertEquals(sdf.parse(dateString), result);
    }

    /**
     * Tests the stringToDate method with an incorrectly formatted date string.
     */
    @Test(expected = RuntimeException.class)
    public void testStringToDateInvalidFormat() throws Throwable {
        // arrange
        String dateString = "invalid-date-format";
        boolean isEndDate = true;
        // act
        CouponIntegrationUtil.stringToDate(dateString, isEndDate);
    }

    /**
     * 测试 transferToCouponList 方法，当输入的 strategistCouponInfos 为空的情况
     */
    @Test
    public void testTransferToCouponListWhenInputIsEmpty() throws Throwable {
        // arrange
        List<StrategistCouponInfo> strategistCouponInfos = Arrays.asList();
        // act
        List<CouponList> result = couponIntegrationUtil.transferToCouponList(strategistCouponInfos);
        // assert
        assertEquals(0, result.size());
    }

    /**
     * 测试 transferToCouponList 方法，当输入的 strategistCouponInfos 不为空，但其中的 StrategistCouponInfo 对象的 creationScene 属性为 MANUAL_UPLOAD 的情况
     */
    @Test
    public void testTransferToCouponListWhenCreationSceneIsManualUpload() throws Throwable {
        // arrange
        StrategistCouponInfo strategistCouponInfo = new StrategistCouponInfo();
        // MANUAL_UPLOAD
        strategistCouponInfo.setCreationScene(0);
        // 设置 sceneDetail
        strategistCouponInfo.setSceneDetail("手动上传");
        strategistCouponInfo.setTaskStartTime(new Date());
        List<StrategistCouponInfo> strategistCouponInfos = Arrays.asList(strategistCouponInfo);
        // act
        List<CouponList> result = couponIntegrationUtil.transferToCouponList(strategistCouponInfos);
        // assert
        assertEquals(1, result.size());
        assertEquals("手动上传", result.get(0).getSceneDetail());
    }

    /**
     * 测试 transferToCouponList 方法，当输入的 strategistCouponInfos 不为空，但其中的 StrategistCouponInfo 对象的 creationScene 属性不为 MANUAL_UPLOAD 的情况
     */
    @Test
    public void testTransferToCouponListWhenCreationSceneIsNotManualUpload() throws Throwable {
        // arrange
        StrategistCouponInfo strategistCouponInfo = new StrategistCouponInfo();
        // REAL_TIME_TASK
        strategistCouponInfo.setCreationScene(1);
        // 设置 sceneDetail
        strategistCouponInfo.setSceneDetail("实时任务");
        strategistCouponInfo.setTaskStartTime(new Date());
        List<StrategistCouponInfo> strategistCouponInfos = Arrays.asList(strategistCouponInfo);
        // act
        List<CouponList> result = couponIntegrationUtil.transferToCouponList(strategistCouponInfos);
        // assert
        assertEquals(1, result.size());
        assertEquals("任务ID:实时任务", result.get(0).getSceneDetail());
    }

    @Test
    public void testPreValidateAndAddCouponList_EmptyCouponGroupIds() throws Throwable {
        // Arrange
        List<String> couponGroupIds = new ArrayList<>();
        String appId = "app1";
        CreationSceneEnum creationSceneEnum = CreationSceneEnum.MANUAL_UPLOAD;
        FunctionModuleEnum functionModuleEnum = FunctionModuleEnum.COUPON_MANAGEMENT;
        String sceneDetail = "testScene";
        LocalDateTime taskStartTime = LocalDateTime.now();
        // Act
        AddCouponResopnse response = couponIntegrationUtil.preValidateAndAddCouponList(couponGroupIds, appId, creationSceneEnum, functionModuleEnum, sceneDetail, taskStartTime);
        // Assert
        assertNotNull(response);
        assertTrue(response.getFailList() == null || response.getFailList().isEmpty());
        assertTrue(response.getRepeatList() == null || response.getRepeatList().isEmpty());
        assertTrue(response.getSuccessList() == null || response.getSuccessList().isEmpty());
    }

    @Test
    public void testPreValidateAndAddCouponList_NullParameters() throws Throwable {
        // Arrange
        List<String> couponGroupIds = null;
        String appId = null;
        CreationSceneEnum creationSceneEnum = null;
        FunctionModuleEnum functionModuleEnum = null;
        String sceneDetail = null;
        LocalDateTime taskStartTime = null;
        // Act
        AddCouponResopnse response = couponIntegrationUtil.preValidateAndAddCouponList(couponGroupIds, appId, creationSceneEnum, functionModuleEnum, sceneDetail, taskStartTime);
        // Assert
        assertNotNull(response);
        assertTrue(response.getFailList() == null || response.getFailList().isEmpty());
        assertTrue(response.getRepeatList() == null || response.getRepeatList().isEmpty());
        assertTrue(response.getSuccessList() == null || response.getSuccessList().isEmpty());
    }

    @Test
    public void testGetStrategistCouponInfoExampleAllFieldsAreNull() throws Throwable {
        GetCouponListQuery query = new GetCouponListQuery();
        StrategistCouponInfoExample result = couponIntegrationUtil.getStrategistCouponInfoExample(query);
        assertNotNull(result);
    }

    @Test
    public void testGetStrategistCouponInfoExampleStartDateAndEndDateAreNotNull() throws Throwable {
        GetCouponListQuery query = new GetCouponListQuery();
        query.setStartDate("2022-01-01");
        query.setEndDate("2022-12-31");
        StrategistCouponInfoExample result = couponIntegrationUtil.getStrategistCouponInfoExample(query);
        assertNotNull(result);
    }

    @Test
    public void testGetStrategistCouponInfoExampleAppIdByCouponIsNotNull() throws Throwable {
        GetCouponListQuery query = new GetCouponListQuery();
        query.setAppIdByCoupon("appIdByCoupon");
        StrategistCouponInfoExample result = couponIntegrationUtil.getStrategistCouponInfoExample(query);
        assertNotNull(result);
    }

    @Test
    public void testGetStrategistCouponInfoExampleAppIdByCouponIsNull() throws Throwable {
        GetCouponListQuery query = new GetCouponListQuery();
        query.setAppId("appId");
        query.setAccessToken("accessToken");
        when(couponDashBoardDomainService.getAuthenticationedAppIds(any(), any())).thenReturn(Arrays.asList("appId1", "appId2"));
        StrategistCouponInfoExample result = couponIntegrationUtil.getStrategistCouponInfoExample(query);
        assertNotNull(result);
    }

    @Test
    public void testGetStrategistCouponInfoExampleCouponGroupIdIsNotNull() throws Throwable {
        GetCouponListQuery query = new GetCouponListQuery();
        query.setCouponGroupId("couponGroupId");
        StrategistCouponInfoExample result = couponIntegrationUtil.getStrategistCouponInfoExample(query);
        assertNotNull(result);
    }

    @Test
    public void testGetStrategistCouponInfoExampleCreationSceneIsNotNull() throws Throwable {
        GetCouponListQuery query = new GetCouponListQuery();
        // Assuming EXISTING_USER is a valid constant for demonstration purposes
        // Adjusted to use a valid enum constant or a mock value if the actual constant is not available
        // Assuming 1 is a valid code for demonstration purposes
        query.setCreationScene(1);
        StrategistCouponInfoExample result = couponIntegrationUtil.getStrategistCouponInfoExample(query);
        assertNotNull(result);
    }

    @Test
    public void testInsertCouponBatchIdsToDB_CouponGroupIdListIsNull() throws Throwable {
        // Assuming the method signature based on the context provided
        List<String> couponGroupIdList = Collections.emptyList();
        String appId = "appId";
        CreationSceneEnum creationSceneEnum = CreationSceneEnum.MANUAL_UPLOAD;
        FunctionModuleEnum functionModuleEnum = FunctionModuleEnum.COUPON_MANAGEMENT;
        String sceneDetail = "sceneDetail";
        LocalDateTime taskStartTime = LocalDateTime.now();
        // Ensure the method is called under conditions that would lead to mapper method invocation
        invokePrivateMethod("insertCouponBatchIdsToDB", couponGroupIdList, appId, creationSceneEnum, functionModuleEnum, sceneDetail, taskStartTime);
        verify(strategistCouponInfoMapper, never()).batchInsert(anyList());
    }
}
