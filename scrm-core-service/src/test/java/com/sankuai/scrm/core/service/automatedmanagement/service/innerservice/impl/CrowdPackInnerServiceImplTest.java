package com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.impl;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.dz.srcm.automatedmanagement.request.CreateCrowdPackRequest;
import com.sankuai.dz.srcm.automatedmanagement.request.CrowdPackUserAddCorpTagRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.CrowdPackUserAddCorpTagResult;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.crowdpack.ScrmCrowdPackBaseInfoConverter;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.crowdpack.ScrmCrowdPackUpdateStrategyConverter;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackUpdateStrategyDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmConfigurationChangeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackUpdateStrategyDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackBaseInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackDetailInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackUserAddCropTagDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.DdlResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.dto.QueryCrowdPackInfoListResultTempDTO;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.impl.CrowdPackInnerServiceImpl;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CrowdPackInnerServiceImplTest {

    @InjectMocks
    private CrowdPackInnerServiceImpl crowdPackInnerService;

    @Mock(lenient = true)
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock(lenient = true)
    private ExtScrmAmCrowdPackBaseInfoDOMapper extScrmAmCrowdPackBaseInfoDOMapper;

    @Mock(lenient = true)
    private ExtScrmAmCrowdPackDetailInfoDOMapper detailInfoDOMapper;

    @Mock(lenient = true)
    private ScrmAmCrowdPackUpdateStrategyDOMapper updateStrategyDOMapper;

    @Mock(lenient = true)
    private ScrmAmConfigurationChangeLogDOMapper configurationChangeLogDOMapper;

    @Mock(lenient = true)
    private ScrmCrowdPackBaseInfoConverter packBaseInfoConverter;

    @Mock(lenient = true)
    private ScrmCrowdPackUpdateStrategyConverter updateStrategyConverter;

    @Mock
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock
    private CrowdPackUserAddCropTagDomainService crowdPackUserAddCorpTagDomainService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test insertCrowPack method when ScrmCrowdPackDTO object is null, should not throw IllegalAccessException
     */
    @Test(expected = Exception.class)
    public void testInsertCrowPackWhenScrmCrowdPackDTOIsNull() throws Throwable {
        CreateCrowdPackRequest request = new CreateCrowdPackRequest();
        String version = "1.0";
        crowdPackInnerService.insertCrowPack(request, null, version);
    }

    /**
     * Test insertCrowPack method when ScrmCrowdPackDTO object is not null and createCrowdPack method returns a DdlResultDTO object with id as null, should return a DdlResultDTO object with id as null
     */
    @Test
    public void testInsertCrowPackWhenDdlResultDTOIdIsNull() throws Throwable {
        CreateCrowdPackRequest request = new CreateCrowdPackRequest();
        ScrmCrowdPackDTO scrmCrowdPackDTO = new ScrmCrowdPackDTO();
        String version = "1.0";
        when(crowdPackWriteDomainService.createCrowdPack(scrmCrowdPackDTO)).thenReturn(new DdlResultDTO(false, "人群包创建失败", null));
        crowdPackInnerService.insertCrowPack(request, scrmCrowdPackDTO, version);
        verify(crowdPackWriteDomainService, times(1)).createCrowdPack(scrmCrowdPackDTO);
    }

    /**
     * Test insertCrowPack method when ScrmCrowdPackDTO object is not null and createCrowdPack method returns a DdlResultDTO object with id not null, should return a DdlResultDTO object with id not null
     */
    @Test
    public void testInsertCrowPackWhenDdlResultDTOIdIsNotNull() throws Throwable {
        CreateCrowdPackRequest request = new CreateCrowdPackRequest();
        ScrmCrowdPackDTO scrmCrowdPackDTO = new ScrmCrowdPackDTO();
        String version = "1.0";
        when(crowdPackWriteDomainService.createCrowdPack(scrmCrowdPackDTO)).thenReturn(new DdlResultDTO(true, "人群包创建成功", 1L));
        crowdPackInnerService.insertCrowPack(request, scrmCrowdPackDTO, version);
        verify(crowdPackWriteDomainService, times(1)).createCrowdPack(scrmCrowdPackDTO);
    }

    /**
     * 测试场景：scrmCrowdPackDTO 为空，返回 result 为 false 的结果
     */
    @Test
    public void testCrowdPackUserBatchAddCorpTag_ScrmCrowdPackDTONull() throws Throwable {
        // arrange
        CrowdPackUserAddCorpTagRequest request = new CrowdPackUserAddCorpTagRequest();
        request.setPackId(1L);
        request.setAppId("appId");
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId")).thenReturn(null);
        // act
        CrowdPackUserAddCorpTagResult result = crowdPackInnerService.crowdPackUserBatchAddCorpTag(request);
        // assert
        assertFalse(result.isResult());
        assertEquals(0, result.getExpectedDuration());
        assertEquals(0, result.getAffectUserNumber());
    }

    /**
     * 测试场景：scrmCrowdPackDTO 不为空，insertPackUserBatchAddCorpTagLogAndSendMsg 返回 false，返回 result 为 false 的结果
     */
    @Test
    public void testCrowdPackUserBatchAddCorpTag_InsertLogAndSendMsgFalse() throws Throwable {
        // arrange
        CrowdPackUserAddCorpTagRequest request = new CrowdPackUserAddCorpTagRequest();
        request.setPackId(1L);
        request.setAppId("appId");
        ScrmCrowdPackDTO scrmCrowdPackDTO = new ScrmCrowdPackDTO();
        scrmCrowdPackDTO.setCrowdCount(100L);
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId")).thenReturn(scrmCrowdPackDTO);
        when(crowdPackUserAddCorpTagDomainService.insertPackUserBatchAddCorpTagLogAndSendMsg(request, scrmCrowdPackDTO)).thenReturn(false);
        // act
        CrowdPackUserAddCorpTagResult result = crowdPackInnerService.crowdPackUserBatchAddCorpTag(request);
        // assert
        assertFalse(result.isResult());
        assertEquals(0, result.getExpectedDuration());
        assertEquals(100, result.getAffectUserNumber());
    }

    /**
     * 测试场景：scrmCrowdPackDTO 不为空，insertPackUserBatchAddCorpTagLogAndSendMsg 返回 true，返回 result 为 true 的结果，并计算 expectedDuration
     */
    @Test
    public void testCrowdPackUserBatchAddCorpTag_InsertLogAndSendMsgTrue() throws Throwable {
        // arrange
        CrowdPackUserAddCorpTagRequest request = new CrowdPackUserAddCorpTagRequest();
        request.setPackId(1L);
        request.setAppId("appId");
        ScrmCrowdPackDTO scrmCrowdPackDTO = new ScrmCrowdPackDTO();
        scrmCrowdPackDTO.setCrowdCount(100L);
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId")).thenReturn(scrmCrowdPackDTO);
        when(crowdPackUserAddCorpTagDomainService.insertPackUserBatchAddCorpTagLogAndSendMsg(request, scrmCrowdPackDTO)).thenReturn(true);
        // act
        CrowdPackUserAddCorpTagResult result = crowdPackInnerService.crowdPackUserBatchAddCorpTag(request);
        // assert
        assertTrue(result.isResult());
        // 100 * 20 / (60 * 1000) = 0
        assertEquals(0, result.getExpectedDuration());
        assertEquals(100, result.getAffectUserNumber());
    }

    /**
     * 测试场景：crowdCount 为 0，计算 expectedDuration 为 0
     */
    @Test
    public void testCrowdPackUserBatchAddCorpTag_CrowdCountZero() throws Throwable {
        // arrange
        CrowdPackUserAddCorpTagRequest request = new CrowdPackUserAddCorpTagRequest();
        request.setPackId(1L);
        request.setAppId("appId");
        ScrmCrowdPackDTO scrmCrowdPackDTO = new ScrmCrowdPackDTO();
        scrmCrowdPackDTO.setCrowdCount(0L);
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId")).thenReturn(scrmCrowdPackDTO);
        when(crowdPackUserAddCorpTagDomainService.insertPackUserBatchAddCorpTagLogAndSendMsg(request, scrmCrowdPackDTO)).thenReturn(true);
        // act
        CrowdPackUserAddCorpTagResult result = crowdPackInnerService.crowdPackUserBatchAddCorpTag(request);
        // assert
        assertTrue(result.isResult());
        assertEquals(0, result.getExpectedDuration());
        assertEquals(0, result.getAffectUserNumber());
    }

    /**
     * 测试场景：crowdCount 为最大值，计算 expectedDuration 为最大值
     */
    @Test
    public void testCrowdPackUserBatchAddCorpTag_CrowdCountMax() throws Throwable {
        // arrange
        CrowdPackUserAddCorpTagRequest request = new CrowdPackUserAddCorpTagRequest();
        request.setPackId(1L);
        request.setAppId("appId");
        ScrmCrowdPackDTO scrmCrowdPackDTO = new ScrmCrowdPackDTO();
        scrmCrowdPackDTO.setCrowdCount(Long.MAX_VALUE);
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId")).thenReturn(scrmCrowdPackDTO);
        when(crowdPackUserAddCorpTagDomainService.insertPackUserBatchAddCorpTagLogAndSendMsg(request, scrmCrowdPackDTO)).thenReturn(true);
        // act
        CrowdPackUserAddCorpTagResult result = crowdPackInnerService.crowdPackUserBatchAddCorpTag(request);
        // assert
        assertTrue(result.isResult());
        assertEquals(Long.MAX_VALUE * 20 / (60 * 1000), result.getExpectedDuration());
        assertEquals(Long.MAX_VALUE, result.getAffectUserNumber());
    }

    /**
     * 测试场景：crowdPackReadDomainService.queryCrowdPackDetailInfo 抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testCrowdPackUserBatchAddCorpTag_QueryCrowdPackDetailInfoException() throws Throwable {
        // arrange
        CrowdPackUserAddCorpTagRequest request = new CrowdPackUserAddCorpTagRequest();
        request.setPackId(1L);
        request.setAppId("appId");
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId")).thenThrow(new RuntimeException("Query failed"));
        // act
        crowdPackInnerService.crowdPackUserBatchAddCorpTag(request);
        // assert
        // 期望抛出异常
    }

    /**
     * 测试场景：crowdPackUserAddCorpTagDomainService.insertPackUserBatchAddCorpTagLogAndSendMsg 抛出异常
     */
    @Test(expected = RuntimeException.class)
    public void testCrowdPackUserBatchAddCorpTag_InsertLogAndSendMsgException() throws Throwable {
        // arrange
        CrowdPackUserAddCorpTagRequest request = new CrowdPackUserAddCorpTagRequest();
        request.setPackId(1L);
        request.setAppId("appId");
        ScrmCrowdPackDTO scrmCrowdPackDTO = new ScrmCrowdPackDTO();
        scrmCrowdPackDTO.setCrowdCount(100L);
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId")).thenReturn(scrmCrowdPackDTO);
        when(crowdPackUserAddCorpTagDomainService.insertPackUserBatchAddCorpTagLogAndSendMsg(request, scrmCrowdPackDTO)).thenThrow(new RuntimeException("Insert failed"));
        // act
        crowdPackInnerService.crowdPackUserBatchAddCorpTag(request);
        // assert
        // 期望抛出异常
    }
}
