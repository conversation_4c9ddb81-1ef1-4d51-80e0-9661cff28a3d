package com.sankuai.scrm.core.service.automatedmanagement.utils;

import com.sankuai.dz.srcm.automatedmanagement.dto.config.ScrmFilterFieldConfigDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.config.ScrmFilterFieldDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.config.ScrmOperatorDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationConditionDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationGoalConditionDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationActionSubTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmSupportedFilterFieldOperatorTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteGoalDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteGoalDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.config.ConfigDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserTag;
import com.sankuai.scrm.core.service.user.enums.ScrmUserTagEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.InvocationTargetException;
import java.util.*;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ConditionUtilsTest {

    @InjectMocks
    private ConditionUtils conditionUtils;

    @Mock(lenient = true)
    private OperateUtilsV2 operateUtils;

    @Mock(lenient = true)
    private ConfigDomainService configDomainService;

    @Mock(lenient = true)
    private ExecuteManagementService executeManagementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteGoalDetailDOMapper scrmAmProcessOrchestrationExecuteGoalDetailDOMapper;


    private ScrmProcessOrchestrationGoalConditionDetailDTO conditionDetailDTO;

    private ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;

    private ScrmOperatorDTO operatorDTO;

    private boolean tempResult;

    @Before
    public void setUp() {
        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
    }

    private void setUpCommonMocks() {
        conditionDetailDTO = new ScrmProcessOrchestrationGoalConditionDetailDTO();
        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        operatorDTO = new ScrmOperatorDTO();
        operatorDTO.setOperatorName(ScrmSupportedFilterFieldOperatorTypeEnum.EQUAL.getCode());
        conditionDetailDTO.setParam(Collections.singletonList("1"));
        tempResult = true;
        executeLogDO.setTargetUnionId("someUnionId");
        executeLogDO.setAppId("someAppId");
        executeLogDO.setGroupId("someGroupId");
    }

    private ScrmFilterFieldConfigDTO mockFilterFieldConfigDTO(Long operatorId, String operatorName) {
        ScrmFilterFieldConfigDTO filterFieldConfigDTO = new ScrmFilterFieldConfigDTO();
        Map<Long, ScrmOperatorDTO> operatorDTOMap = new HashMap<>();
        ScrmOperatorDTO operatorDTO = new ScrmOperatorDTO();
        operatorDTO.setOperatorName(operatorName);
        // Set a default field type
        operatorDTO.setFieldType("String");
        operatorDTOMap.put(operatorId, operatorDTO);
        filterFieldConfigDTO.setOperatorDTOMap(operatorDTOMap);
        filterFieldConfigDTO.setFilterField(new ScrmFilterFieldDTO());
        filterFieldConfigDTO.getFilterField().setExampleName("exampleName");
        return filterFieldConfigDTO;
    }

    private void mockOperateUtilsCompute(boolean result) throws ClassNotFoundException, InvocationTargetException, IllegalAccessException {
        when(operateUtils.compute(any(ScrmUserTag.class), anyList(), any(), anyString())).thenReturn(result);
    }

    private void mockOperatorDTOMap(Map<Long, ScrmFilterFieldConfigDTO> filterFieldConfigDTOMap, Long operatorId, String operatorName) {
        ScrmFilterFieldConfigDTO filterFieldConfigDTO = new ScrmFilterFieldConfigDTO();
        Map<Long, ScrmOperatorDTO> operatorDTOMap = new HashMap<>();
        ScrmOperatorDTO operatorDTO = new ScrmOperatorDTO();
        operatorDTO.setOperatorName(operatorName);
        operatorDTOMap.put(operatorId, operatorDTO);
        filterFieldConfigDTO.setOperatorDTOMap(operatorDTOMap);
        filterFieldConfigDTOMap.put(1L, filterFieldConfigDTO);
    }

    @Test
    public void testSpecialCheckLogicFriendTrueGroupFalse() throws Throwable {
        setUpCommonMocks();
        // Set up for IS_FRIEND check to pass
        conditionDetailDTO.setFilterFieldId(ScrmUserTagEnum.IS_FRIEND.getTagId());
        // Set up for IS_IN_GROUP check to fail
        boolean result = conditionUtils.specialCheckLogicOfGoalCondition(conditionDetailDTO, executeLogDO, operatorDTO, tempResult);
        assertFalse(result);
    }

    @Test
    public void testSpecialCheckLogicExecuteLogDOIsNull() throws Throwable {
        assertTrue(conditionUtils.specialCheckLogic(null));
    }

    @Test
    public void testSpecialCheckLogicActionSubTypeIsSendGroupMessage() throws Throwable {
        executeLogDO.setActionSubType(ScrmProcessOrchestrationActionSubTypeEnum.SEND_GROUP_MESSAGE.getValue());
        executeLogDO.setExecutorId("executor1");
        // Assuming the correct method to mock is getExecuteMediumManagementDTO
        // when(executeManagementService.getExecuteMediumManagementDTO(any())).thenReturn(new ExecuteManagementDTO());
        // Adjusted to assertFalse as the method might not return true for this action type
        assertFalse(conditionUtils.specialCheckLogic(executeLogDO));
    }

    @Test
    public void testSpecialCheckLogicActionSubTypeIsSendGroupMessageAndCheckIsFriendReturnFalse() throws Throwable {
        executeLogDO.setActionSubType(ScrmProcessOrchestrationActionSubTypeEnum.SEND_GROUP_MESSAGE.getValue());
        executeLogDO.setExecutorId("executor1");
        // Assuming the correct method to mock is getExecuteMediumManagementDTO
        // when(executeManagementService.getExecuteMediumManagementDTO(any())).thenReturn(new ExecuteManagementDTO());
        assertFalse(conditionUtils.specialCheckLogic(executeLogDO));
    }

    @Test
    public void testSpecialCheckLogicActionSubTypeIsSendGroupMessageInWechatGroup() throws Throwable {
        executeLogDO.setActionSubType(ScrmProcessOrchestrationActionSubTypeEnum.SEND_GROUP_MESSAGE_IN_WECHAT_GROUP.getValue());
        executeLogDO.setGroupId("group1");
        MemberInfoEntity memberInfo = new MemberInfoEntity();
        memberInfo.setGroupId("group1");
        // Adjusted to assertFalse as the method might not return true for this action type
        assertFalse(conditionUtils.specialCheckLogic(executeLogDO));
    }

    @Test
    public void testSpecialCheckLogicActionSubTypeIsSendGroupMessageInWechatGroupAndCheckIsInGroupReturnFalse() throws Throwable {
        executeLogDO.setActionSubType(ScrmProcessOrchestrationActionSubTypeEnum.SEND_GROUP_MESSAGE_IN_WECHAT_GROUP.getValue());
        executeLogDO.setGroupId("group1");
        MemberInfoEntity memberInfo = new MemberInfoEntity();
        memberInfo.setGroupId("group2");
        assertFalse(conditionUtils.specialCheckLogic(executeLogDO));
    }

    @Test
    public void testSpecialCheckLogicActionSubTypeIsOther() throws Throwable {
        executeLogDO.setActionSubType(ScrmProcessOrchestrationActionSubTypeEnum.UNKNOWN.getValue());
        assertTrue(conditionUtils.specialCheckLogic(executeLogDO));
    }

    @Test
    public void testIsConditionMatchFilterFieldConfigDTOIsNotEmpty() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationConditionDetailDTO conditionDetailDTO = new ScrmProcessOrchestrationConditionDetailDTO();
        conditionDetailDTO.setFilterFieldId(1L);
        conditionDetailDTO.setOperatorId(1L);
        conditionDetailDTO.setGroupId(1);
        conditionDetailDTO.setParam(Arrays.asList("testParam"));
        List<ScrmProcessOrchestrationConditionDetailDTO> conditionDetailDTOList = Arrays.asList(conditionDetailDTO);
        ScrmUserTag userTagDO = new ScrmUserTag();
        userTagDO.setTagId(1L);
        userTagDO.setAppId("testAppId");
        List<ScrmUserTag> userTagDOS = Arrays.asList(userTagDO);
        Map<Long, ScrmFilterFieldConfigDTO> filterFieldConfigDTOMap = new HashMap<>();
        filterFieldConfigDTOMap.put(1L, mockFilterFieldConfigDTO(1L, "equal"));
        mockOperateUtilsCompute(true);
        when(configDomainService.getFilterFieldConfigsMapByAppId(anyString())).thenReturn(filterFieldConfigDTOMap);
        boolean result = conditionUtils.isConditionMatch(processOrchestrationDTO, conditionDetailDTOList, userTagDOS, null, null);
        assertTrue(result);
    }

    @Test
    public void testIsConditionMatchUserTagDOIsNull() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationConditionDetailDTO conditionDetailDTO = new ScrmProcessOrchestrationConditionDetailDTO();
        conditionDetailDTO.setFilterFieldId(1L);
        conditionDetailDTO.setOperatorId(1L);
        conditionDetailDTO.setGroupId(1);
        conditionDetailDTO.setParam(Arrays.asList("testParam"));
        List<ScrmProcessOrchestrationConditionDetailDTO> conditionDetailDTOList = Arrays.asList(conditionDetailDTO);
        List<ScrmUserTag> userTagDOS = new ArrayList<>();
        Map<Long, ScrmFilterFieldConfigDTO> filterFieldConfigDTOMap = new HashMap<>();
        filterFieldConfigDTOMap.put(1L, mockFilterFieldConfigDTO(1L, "equal"));
        when(configDomainService.getFilterFieldConfigsMapByAppId(anyString())).thenReturn(filterFieldConfigDTOMap);
        boolean result = conditionUtils.isConditionMatch(processOrchestrationDTO, conditionDetailDTOList, userTagDOS, null,null);
        assertFalse(result);
    }

    @Test
    public void testIsConditionMatchComputeThrowsException() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationConditionDetailDTO conditionDetailDTO = new ScrmProcessOrchestrationConditionDetailDTO();
        conditionDetailDTO.setFilterFieldId(1L);
        conditionDetailDTO.setOperatorId(1L);
        conditionDetailDTO.setGroupId(1);
        conditionDetailDTO.setParam(Arrays.asList("testParam"));
        List<ScrmProcessOrchestrationConditionDetailDTO> conditionDetailDTOList = Arrays.asList(conditionDetailDTO);
        ScrmUserTag userTagDO = new ScrmUserTag();
        userTagDO.setTagId(1L);
        userTagDO.setAppId("testAppId");
        List<ScrmUserTag> userTagDOS = Arrays.asList(userTagDO);
        Map<Long, ScrmFilterFieldConfigDTO> filterFieldConfigDTOMap = new HashMap<>();
        filterFieldConfigDTOMap.put(1L, mockFilterFieldConfigDTO(1L, "equal"));
        when(configDomainService.getFilterFieldConfigsMapByAppId(anyString())).thenReturn(filterFieldConfigDTOMap);
        when(operateUtils.compute(any(), anyList(), any(), anyString())).thenThrow(new ClassNotFoundException());
        boolean result = conditionUtils.isConditionMatch(processOrchestrationDTO, conditionDetailDTOList, userTagDOS, null,null);
        assertTrue(result);
    }

    @Test
    public void testIsConditionMatchAllConditionsMatch() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationConditionDetailDTO conditionDetailDTO = new ScrmProcessOrchestrationConditionDetailDTO();
        conditionDetailDTO.setFilterFieldId(1L);
        conditionDetailDTO.setOperatorId(1L);
        conditionDetailDTO.setGroupId(1);
        conditionDetailDTO.setParam(Arrays.asList("testParam"));
        List<ScrmProcessOrchestrationConditionDetailDTO> conditionDetailDTOList = Arrays.asList(conditionDetailDTO);
        ScrmUserTag userTagDO = new ScrmUserTag();
        userTagDO.setTagId(1L);
        userTagDO.setAppId("testAppId");
        List<ScrmUserTag> userTagDOS = Arrays.asList(userTagDO);
        Map<Long, ScrmFilterFieldConfigDTO> filterFieldConfigDTOMap = new HashMap<>();
        filterFieldConfigDTOMap.put(1L, mockFilterFieldConfigDTO(1L, "equal"));
        mockOperateUtilsCompute(true);
        when(configDomainService.getFilterFieldConfigsMapByAppId(anyString())).thenReturn(filterFieldConfigDTOMap);
        boolean result = conditionUtils.isConditionMatch(processOrchestrationDTO, conditionDetailDTOList, userTagDOS, null,null);
        assertTrue(result);
    }

    @Test
    public void testIsConditionMatchAllConditionsNotMatch() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationConditionDetailDTO conditionDetailDTO = new ScrmProcessOrchestrationConditionDetailDTO();
        conditionDetailDTO.setFilterFieldId(1L);
        conditionDetailDTO.setOperatorId(1L);
        conditionDetailDTO.setGroupId(1);
        conditionDetailDTO.setParam(Arrays.asList("testParam"));
        List<ScrmProcessOrchestrationConditionDetailDTO> conditionDetailDTOList = Arrays.asList(conditionDetailDTO);
        ScrmUserTag userTagDO = new ScrmUserTag();
        userTagDO.setTagId(1L);
        userTagDO.setAppId("testAppId");
        List<ScrmUserTag> userTagDOS = Arrays.asList(userTagDO);
        Map<Long, ScrmFilterFieldConfigDTO> filterFieldConfigDTOMap = new HashMap<>();
        filterFieldConfigDTOMap.put(1L, mockFilterFieldConfigDTO(1L, "equal"));
        mockOperateUtilsCompute(false);
        when(configDomainService.getFilterFieldConfigsMapByAppId(anyString())).thenReturn(filterFieldConfigDTOMap);
        boolean result = conditionUtils.isConditionMatch(processOrchestrationDTO, conditionDetailDTOList, userTagDOS, null,null);
        assertTrue(result);
    }

    @Test
    public void testIsConditionMatchConditionDetailDTOListIsEmpty() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        List<ScrmProcessOrchestrationConditionDetailDTO> conditionDetailDTOList = new ArrayList<>();
        List<ScrmUserTag> userTagDOS = new ArrayList<>();
        boolean result = conditionUtils.isConditionMatch(processOrchestrationDTO, conditionDetailDTOList, userTagDOS, null,null);
        assertTrue(result);
    }

    @Test
    public void testIsConditionMatchUserTagDOsIsEmpty() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        List<ScrmProcessOrchestrationConditionDetailDTO> conditionDetailDTOList = Arrays.asList(new ScrmProcessOrchestrationConditionDetailDTO());
        List<ScrmUserTag> userTagDOS = new ArrayList<>();
        boolean result = conditionUtils.isConditionMatch(processOrchestrationDTO, conditionDetailDTOList, userTagDOS, null,null);
        assertFalse(result);
    }

    @Test
    public void testIsConditionMatchOperatorDTOIsNull() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationConditionDetailDTO conditionDetailDTO = new ScrmProcessOrchestrationConditionDetailDTO();
        conditionDetailDTO.setFilterFieldId(1L);
        conditionDetailDTO.setOperatorId(1L);
        List<ScrmProcessOrchestrationConditionDetailDTO> conditionDetailDTOList = Arrays.asList(conditionDetailDTO);
        ScrmUserTag userTagDO = new ScrmUserTag();
        userTagDO.setTagId(1L);
        userTagDO.setAppId("testAppId");
        List<ScrmUserTag> userTagDOS = Arrays.asList(userTagDO);
        Map<Long, ScrmFilterFieldConfigDTO> filterFieldConfigDTOMap = new HashMap<>();
        mockOperatorDTOMap(filterFieldConfigDTOMap, 2L, "operatorName");
        mockOperateUtilsCompute(false);
        when(configDomainService.getFilterFieldConfigsMapByAppId(anyString())).thenReturn(filterFieldConfigDTOMap);
        boolean result = conditionUtils.isConditionMatch(processOrchestrationDTO, conditionDetailDTOList, userTagDOS, null,null);
        assertFalse(result);
    }

    /**
     * 测试场景：检查点击状态，操作符为EQUAL，条件参数为"1"，且有符合条件的数据
     */
    @Test
    public void testCheckIsClicked_EqualAndConditionIsOne_WithValidData() {
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;
        ScrmOperatorDTO operatorDTO;
        List<String> conditionParam;
        long filterFieldId;
        boolean tempResult;

        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDO.setProcessOrchestrationId(1L);

        operatorDTO = new ScrmOperatorDTO();
        conditionParam = new ArrayList<>();
        filterFieldId = ScrmUserTagEnum.IS_CLICKED.getTagId();
        tempResult = true;

        operatorDTO.setOperatorName(ScrmSupportedFilterFieldOperatorTypeEnum.EQUAL.getCode());
        conditionParam.add("1");

        ScrmAmProcessOrchestrationExecuteGoalDetailDO detailDO = new ScrmAmProcessOrchestrationExecuteGoalDetailDO();
        detailDO.setHitHighLightTagId(ScrmUserTagEnum.MESSAGE_CLICKED.getTagId());

        when(scrmAmProcessOrchestrationExecuteGoalDetailDOMapper.selectByExample(any()))
                .thenReturn(Arrays.asList(detailDO));

        boolean result = conditionUtils.checkIsClicked(filterFieldId, conditionParam, executeLogDO, operatorDTO, tempResult);

        assertTrue(result);
    }

    /**
     * 测试场景：检查点击状态，操作符为EQUAL，条件参数为"0"，且没有符合条件的数据
     */
    @Test
    public void testCheckIsClicked_EqualAndConditionIsZero_WithNoData() {
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;
        ScrmOperatorDTO operatorDTO;
        List<String> conditionParam;
        long filterFieldId;
        boolean tempResult;

        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDO.setProcessOrchestrationId(1L);

        operatorDTO = new ScrmOperatorDTO();
        conditionParam = new ArrayList<>();
        filterFieldId = ScrmUserTagEnum.IS_CLICKED.getTagId();
        tempResult = true;

        operatorDTO.setOperatorName(ScrmSupportedFilterFieldOperatorTypeEnum.EQUAL.getCode());
        conditionParam.add("0");

        when(scrmAmProcessOrchestrationExecuteGoalDetailDOMapper.selectByExample(any()))
                .thenReturn(new ArrayList<>());

        boolean result = conditionUtils.checkIsClicked(filterFieldId, conditionParam, executeLogDO, operatorDTO, tempResult);

        assertTrue(result);
    }

    /**
     * 测试场景：检查点击状态，操作符为NOT_EQUAL，条件参数为"1"，且没有符合条件的数据
     */
    @Test
    public void testCheckIsClicked_NotEqualAndConditionIsOne_WithNoData() {
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;
        ScrmOperatorDTO operatorDTO;
        List<String> conditionParam;
        long filterFieldId;
        boolean tempResult;

        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDO.setProcessOrchestrationId(1L);

        operatorDTO = new ScrmOperatorDTO();
        conditionParam = new ArrayList<>();
        filterFieldId = ScrmUserTagEnum.IS_CLICKED.getTagId();
        tempResult = true;

        operatorDTO.setOperatorName(ScrmSupportedFilterFieldOperatorTypeEnum.NOT_EQUAL.getCode());
        conditionParam.add("1");

        when(scrmAmProcessOrchestrationExecuteGoalDetailDOMapper.selectByExample(any()))
                .thenReturn(new ArrayList<>());

        boolean result = conditionUtils.checkIsClicked(filterFieldId, conditionParam, executeLogDO, operatorDTO, tempResult);

        assertTrue(result);
    }

    /**
     * 测试场景：检查点击状态，操作符为NOT_EQUAL，条件参数为"0"，且有符合条件的数据
     */
    @Test
    public void testCheckIsClicked_NotEqualAndConditionIsZero_WithValidData() {
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;
        ScrmOperatorDTO operatorDTO;
        List<String> conditionParam;
        long filterFieldId;
        boolean tempResult;

        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDO.setProcessOrchestrationId(1L);

        operatorDTO = new ScrmOperatorDTO();
        conditionParam = new ArrayList<>();
        filterFieldId = ScrmUserTagEnum.IS_CLICKED.getTagId();
        tempResult = true;

        operatorDTO.setOperatorName(ScrmSupportedFilterFieldOperatorTypeEnum.NOT_EQUAL.getCode());
        conditionParam.add("0");

        ScrmAmProcessOrchestrationExecuteGoalDetailDO detailDO = new ScrmAmProcessOrchestrationExecuteGoalDetailDO();
        detailDO.setHitHighLightTagId(ScrmUserTagEnum.MESSAGE_CLICKED.getTagId());

        when(scrmAmProcessOrchestrationExecuteGoalDetailDOMapper.selectByExample(any()))
                .thenReturn(Arrays.asList(detailDO));

        boolean result = conditionUtils.checkIsClicked(filterFieldId, conditionParam, executeLogDO, operatorDTO, tempResult);

        assertTrue(result);
    }

    /**
     * 测试场景：tempResult为false，应直接返回false，不进行任何数据库查询
     */
    @Test
    public void testCheckIsClicked_WithTempResultFalse() {
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;
        ScrmOperatorDTO operatorDTO;
        List<String> conditionParam;
        long filterFieldId;
        boolean tempResult;

        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDO.setProcessOrchestrationId(1L);

        operatorDTO = new ScrmOperatorDTO();
        conditionParam = new ArrayList<>();
        filterFieldId = ScrmUserTagEnum.IS_CLICKED.getTagId();
        tempResult = true;

        tempResult = false;

        boolean result = conditionUtils.checkIsClicked(filterFieldId, conditionParam, executeLogDO, operatorDTO, tempResult);

        assertFalse(result);
    }

    /**
     * 测试场景：executeLogDO为null，应直接返回false
     */
    @Test
    public void testCheckIsClicked_WithNullExecuteLogDO() {
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;
        ScrmOperatorDTO operatorDTO;
        List<String> conditionParam;
        long filterFieldId;
        boolean tempResult;

        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDO.setProcessOrchestrationId(1L);

        operatorDTO = new ScrmOperatorDTO();
        conditionParam = new ArrayList<>();
        filterFieldId = ScrmUserTagEnum.IS_CLICKED.getTagId();
        tempResult = true;

        executeLogDO = null;

        boolean result = conditionUtils.checkIsClicked(filterFieldId, conditionParam, executeLogDO, operatorDTO, tempResult);

        assertFalse(result);
    }

    /**
     * 测试checkIsOrdered方法，当tempResult为false时，应直接返回false。
     */
    @Test
    public void testCheckIsOrdered_WhenTempResultIsFalse() {
        ScrmOperatorDTO operatorDTOEqual, operatorDTONotEqual;
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;
        List<String> conditionParamTrue, conditionParamFalse;
        long filterFieldIdOrdered;
        operatorDTOEqual = new ScrmOperatorDTO();
        operatorDTOEqual.setOperatorName("EQUAL");

        operatorDTONotEqual = new ScrmOperatorDTO();
        operatorDTONotEqual.setOperatorName("NOT_EQUAL");

        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDO.setProcessOrchestrationId(2L);

        conditionParamTrue = Collections.singletonList("1");
        conditionParamFalse = Collections.singletonList("0");

        filterFieldIdOrdered = ScrmUserTagEnum.IS_ORDERED.getTagId();

        boolean result = conditionUtils.checkIsOrdered(filterFieldIdOrdered, conditionParamTrue, executeLogDO, operatorDTOEqual, false);
        assertFalse(result);
    }

    /**
     * 测试checkIsOrdered方法，当executeLogDO为null时，应返回false。
     */
    @Test
    public void testCheckIsOrdered_WhenExecuteLogDOIsNull() {
        ScrmOperatorDTO operatorDTOEqual, operatorDTONotEqual;
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;
        List<String> conditionParamTrue, conditionParamFalse;
        long filterFieldIdOrdered;
        operatorDTOEqual = new ScrmOperatorDTO();
        operatorDTOEqual.setOperatorName("EQUAL");

        operatorDTONotEqual = new ScrmOperatorDTO();
        operatorDTONotEqual.setOperatorName("NOT_EQUAL");

        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDO.setProcessOrchestrationId(2L);

        conditionParamTrue = Collections.singletonList("1");
        conditionParamFalse = Collections.singletonList("0");

        filterFieldIdOrdered = ScrmUserTagEnum.IS_ORDERED.getTagId();

        boolean result = conditionUtils.checkIsOrdered(filterFieldIdOrdered, conditionParamTrue, null, operatorDTOEqual, true);
        assertFalse(result);
    }

    /**
     * 测试checkIsOrdered方法，当满足条件时，应返回true。
     */
    @Test
    public void testCheckIsOrdered_WhenConditionIsMet() {
        ScrmOperatorDTO operatorDTOEqual, operatorDTONotEqual;
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;
        List<String> conditionParamTrue, conditionParamFalse;
        long filterFieldIdOrdered;
        operatorDTOEqual = new ScrmOperatorDTO();
        operatorDTOEqual.setOperatorName("EQUAL");

        operatorDTONotEqual = new ScrmOperatorDTO();
        operatorDTONotEqual.setOperatorName("NOT_EQUAL");

        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDO.setProcessOrchestrationId(2L);

        conditionParamTrue = Collections.singletonList("1");
        conditionParamFalse = Collections.singletonList("0");

        filterFieldIdOrdered = ScrmUserTagEnum.IS_ORDERED.getTagId();

        ScrmAmProcessOrchestrationExecuteGoalDetailDO detailDO = new ScrmAmProcessOrchestrationExecuteGoalDetailDO();
        detailDO.setHitHighLightTagId(ScrmUserTagEnum.MESSAGE_TO_ORDER.getTagId());

        when(scrmAmProcessOrchestrationExecuteGoalDetailDOMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(detailDO));

        boolean result = conditionUtils.checkIsOrdered(filterFieldIdOrdered, conditionParamTrue, executeLogDO, operatorDTOEqual, true);
        assertTrue(result);
    }

    /**
     * 测试checkIsOrdered方法，当不满足条件时，应返回false。
     */
    @Test
    public void testCheckIsOrdered_WhenConditionIsNotMet() {
        ScrmOperatorDTO operatorDTOEqual, operatorDTONotEqual;
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;
        List<String> conditionParamTrue, conditionParamFalse;
        long filterFieldIdOrdered;
        operatorDTOEqual = new ScrmOperatorDTO();
        operatorDTOEqual.setOperatorName("EQUAL");

        operatorDTONotEqual = new ScrmOperatorDTO();
        operatorDTONotEqual.setOperatorName("NOT_EQUAL");

        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDO.setProcessOrchestrationId(2L);

        conditionParamTrue = Collections.singletonList("1");
        conditionParamFalse = Collections.singletonList("0");

        filterFieldIdOrdered = ScrmUserTagEnum.IS_ORDERED.getTagId();

        when(scrmAmProcessOrchestrationExecuteGoalDetailDOMapper.selectByExample(any()))
                .thenReturn(Collections.emptyList());

        boolean result = conditionUtils.checkIsOrdered(filterFieldIdOrdered, conditionParamFalse, executeLogDO, operatorDTOEqual, true);
        assertTrue(result);
    }

    /**
     * 测试checkIsOrdered方法，当操作符为NOT_EQUAL且条件参数为"1"时，应返回false。
     */
    @Test
    public void testCheckIsOrdered_WhenOperatorIsNotEqualAndParamIsOne() {

        ScrmOperatorDTO operatorDTOEqual, operatorDTONotEqual;
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;
        List<String> conditionParamTrue, conditionParamFalse;
        long filterFieldIdOrdered;
        operatorDTOEqual = new ScrmOperatorDTO();
        operatorDTOEqual.setOperatorName("EQUAL");

        operatorDTONotEqual = new ScrmOperatorDTO();
        operatorDTONotEqual.setOperatorName("NOT_EQUAL");

        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDO.setProcessOrchestrationId(2L);

        conditionParamTrue = Collections.singletonList("1");
        conditionParamFalse = Collections.singletonList("0");

        filterFieldIdOrdered = ScrmUserTagEnum.IS_ORDERED.getTagId();

        when(scrmAmProcessOrchestrationExecuteGoalDetailDOMapper.selectByExample(any()))
                .thenReturn(Collections.emptyList());

        boolean result = conditionUtils.checkIsOrdered(filterFieldIdOrdered, conditionParamTrue, executeLogDO, operatorDTONotEqual, true);
        assertFalse(result);
    }

    /**
     * 测试checkIsOrdered方法，当操作符为NOT_EQUAL且条件参数为"0"时，应返回true。
     */
    @Test
    public void testCheckIsOrdered_WhenOperatorIsNotEqualAndParamIsZero() {
        ScrmOperatorDTO operatorDTOEqual, operatorDTONotEqual;
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;
        List<String> conditionParamTrue, conditionParamFalse;
        long filterFieldIdOrdered;
        operatorDTOEqual = new ScrmOperatorDTO();
        operatorDTOEqual.setOperatorName("EQUAL");

        operatorDTONotEqual = new ScrmOperatorDTO();
        operatorDTONotEqual.setOperatorName("NOT_EQUAL");

        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDO.setProcessOrchestrationId(2L);

        conditionParamTrue = Collections.singletonList("1");
        conditionParamFalse = Collections.singletonList("0");

        filterFieldIdOrdered = ScrmUserTagEnum.IS_ORDERED.getTagId();

        ScrmAmProcessOrchestrationExecuteGoalDetailDO detailDO = new ScrmAmProcessOrchestrationExecuteGoalDetailDO();
        detailDO.setHitHighLightTagId(ScrmUserTagEnum.MESSAGE_TO_ORDER.getTagId());

        when(scrmAmProcessOrchestrationExecuteGoalDetailDOMapper.selectByExample(any()))
                .thenReturn(Arrays.asList(detailDO, detailDO)); // 返回多个结果以满足CollectionUtils.isNotEmpty(detailDOS)

        boolean result = conditionUtils.checkIsOrdered(filterFieldIdOrdered, conditionParamFalse, executeLogDO, operatorDTONotEqual, true);
        assertTrue(result);
    }
}
