package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.message.push.dto.MsgTaskDetailResultDTO;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushDetailStatus;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * GroupSendStrategy 的测试类
 */
class GroupSendStrategyProcessSuccess1Test {

    @InjectMocks
    private GroupSendStrategy groupSendStrategy;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS;

    private ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO;

    private List<MsgTaskDetailResultDTO> taskDetailResultDTOS;

    private String msgId;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // 初始化 processOrchestrationDTO
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        // 初始化 wxInvokeDetailDOS
        wxInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setId(1L);
        detailDO.setExecuteLogId(1L);
        detailDO.setProcessOrchestrationId(1L);
        detailDO.setProcessOrchestrationVersion("1.0");
        detailDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_CREATE.getValue().byteValue());
        wxInvokeDetailDOS.add(detailDO);
        // 初始化 wxInvokeLogDO
        wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        taskDetailResultDTOS = new ArrayList<>();
        msgId = "testMsgId";
    }

    /**
     * 测试 processSuccess 方法，当所有参数正常且没有失败任务时
     */
    @Test
    void testProcessSuccessWithNoFailedTaskDetailResult() throws Throwable {
        // arrange
        MsgTaskDetailResultDTO successTask = new MsgTaskDetailResultDTO();
        successTask.setStatus(MsgPushDetailStatus.SUCCESS.getCode());
        successTask.setReceiverId("successReceiverId");
        taskDetailResultDTOS.add(successTask);
        // act
        groupSendStrategy.processSuccess(processOrchestrationDTO, wxInvokeDetailDOS, wxInvokeLogDO, taskDetailResultDTOS, msgId);
        // assert
        ArgumentCaptor<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationWxInvokeDetailDO.class);
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(detailCaptor.capture(), any());
        assertEquals(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue(), detailCaptor.getValue().getStatus());
        assertEquals(wxInvokeLogDO.getId(), detailCaptor.getValue().getInvokeLogId());
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * 测试 processSuccess 方法，当有失败任务时
     */
    @Test
    void testProcessSuccessWithFailedTaskDetailResult() throws Throwable {
        // arrange
        MsgTaskDetailResultDTO failedTask = new MsgTaskDetailResultDTO();
        failedTask.setStatus(MsgPushDetailStatus.FAILED.getCode());
        failedTask.setReceiverId("failedReceiverId");
        taskDetailResultDTOS.add(failedTask);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> failedDetails = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO failedDetail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        failedDetail.setExecuteLogId(1L);
        failedDetails.add(failedDetail);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(failedDetails);
        // act
        groupSendStrategy.processSuccess(processOrchestrationDTO, wxInvokeDetailDOS, wxInvokeLogDO, taskDetailResultDTOS, msgId);
        // assert
        ArgumentCaptor<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationWxInvokeDetailDO.class);
        verify(wxInvokeDetailDOMapper, times(2)).updateByExampleSelective(detailCaptor.capture(), any());
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> capturedDetails = detailCaptor.getAllValues();
        assertEquals(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_FAILED_INVALID_OR_CANT_DELIVER.getValue().byteValue(), capturedDetails.get(0).getStatus());
        assertEquals(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue(), capturedDetails.get(1).getStatus());
        ArgumentCaptor<ScrmAmProcessOrchestrationExecuteLogDO> logCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationExecuteLogDO.class);
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(logCaptor.capture(), any());
        assertEquals(ScrmProcessOrchestrationExecuteStatusTypeEnum.FAILED_CALL_EXTERNAL_SERVICE.getValue().byteValue(), logCaptor.getValue().getStatus());
    }

    /**
     * 测试 processSuccess 方法，当 taskDetailResultDTOS 为空时
     */
    @Test
    void testProcessSuccessWithEmptyTaskDetailResult() throws Throwable {
        // arrange
        taskDetailResultDTOS = new ArrayList<>();
        // act
        groupSendStrategy.processSuccess(processOrchestrationDTO, wxInvokeDetailDOS, wxInvokeLogDO, taskDetailResultDTOS, msgId);
        // assert
        ArgumentCaptor<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationWxInvokeDetailDO.class);
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(detailCaptor.capture(), any());
        assertEquals(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue(), detailCaptor.getValue().getStatus());
        assertEquals(wxInvokeLogDO.getId(), detailCaptor.getValue().getInvokeLogId());
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * 测试 processSuccess 方法，当 msgId 为 null 时
     */
    @Test
    void testProcessSuccessWithNullMsgId() throws Throwable {
        // arrange
        msgId = null;
        // act
        groupSendStrategy.processSuccess(processOrchestrationDTO, wxInvokeDetailDOS, wxInvokeLogDO, taskDetailResultDTOS, msgId);
        // assert
        ArgumentCaptor<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationWxInvokeDetailDO.class);
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(detailCaptor.capture(), any());
        assertEquals(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue(), detailCaptor.getValue().getStatus());
        assertEquals(wxInvokeLogDO.getId(), detailCaptor.getValue().getInvokeLogId());
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }
}
