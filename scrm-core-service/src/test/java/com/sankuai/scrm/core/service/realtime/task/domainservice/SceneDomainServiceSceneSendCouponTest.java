package com.sankuai.scrm.core.service.realtime.task.domainservice;

import com.dianping.unified.coupon.issue.api.dto.UnifiedCouponIssueDetail;
import com.dianping.unified.coupon.issue.api.dto.UnifiedCouponIssueResult;
import com.dianping.unified.coupon.issue.api.response.UnifiedCouponIssueResponse;
import com.sankuai.dz.srcm.couponIntegration.enums.FunctionModuleEnum;
import com.sankuai.dz.srcm.realtime.task.dto.SceneSendCouponResponse;
import com.sankuai.scrm.core.service.activity.miniprogram.domain.CouponDomainService;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.couponIntegration.domain.CouponDashBoardDomainService;
import com.sankuai.scrm.core.service.couponIntegration.utils.CouponIntegrationUtil;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class SceneDomainServiceSceneSendCouponTest {

    @InjectMocks
    private SceneDomainService sceneDomainService;

    @Mock
    private CouponIntegrationUtil util;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private CouponDomainService couponDomainService;

    @Mock
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordDOMapper;

    @Mock
    private CouponDashBoardDomainService couponDashBoardDomainService;



    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test successful coupon sending scenario
     */
    @Test
    public void testSceneSendCoupon_Success() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        String couponGroupId = "testCouponGroupId";
        String appId = "testAppId";
        String distributorCode = "testDistributorCode";
        String sceneDetail = "testSceneDetail";
        Integer sceneType = 1;
        FunctionModuleEnum functionModule = FunctionModuleEnum.AUTOMATED_MARKETING;
        Date taskStartTime = new Date();
        long userId = 123L;
        when(mtUserCenterAclService.getUserIdByUnionIdFromMtUserCenter(unionId)).thenReturn(userId);
        UnifiedCouponIssueResponse mockResponse = new UnifiedCouponIssueResponse();
        mockResponse.setSuccess(true);
        UnifiedCouponIssueDetail detail = new UnifiedCouponIssueDetail();
        detail.setUnifiedCouponId("testUnifiedCouponId1");
        detail.setDiscountAmount(BigDecimal.TEN);
        detail.setPriceLimit(BigDecimal.valueOf(100));
        detail.setCouponGroupName("Test Coupon");
        detail.setBeginTime(new Date());
        detail.setEndTime(new Date());
        detail.setRedirectLink("http://test.com");
        List<UnifiedCouponIssueDetail> resultList = new ArrayList<>();
        resultList.add(detail);
        UnifiedCouponIssueResult result = new UnifiedCouponIssueResult(resultList, 0, 1);
        mockResponse.setResult(result);

        when(couponDomainService.sendMtCouponAndResponse(anyLong(), anyString(), anyString(),eq(null))).thenReturn(mockResponse);
        when(couponDashBoardDomainService.getStatisticStatus(anyString(), anyString(), anyString(), anyInt())).thenReturn(1);
        // act
        SceneSendCouponResponse response = sceneDomainService.sceneSendCoupon(unionId, couponGroupId, appId, distributorCode, sceneDetail, sceneType, functionModule, taskStartTime);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getSuccessList());
        assertEquals(1, response.getSuccessList().size());
        verify(scrmSceneCouponRecordDOMapper).batchInsert(anyList());
    }

    /**
     * Test scenario with invalid input parameters
     */
    @Test
    public void testSceneSendCoupon_InvalidInputParameters() throws Throwable {
        // arrange
        String unionId = "";
        String couponGroupId = "";
        String appId = "";
        String distributorCode = "";
        String sceneDetail = "testSceneDetail";
        Integer sceneType = 1;
        FunctionModuleEnum functionModule = FunctionModuleEnum.AUTOMATED_MARKETING;
        Date taskStartTime = new Date();
        // act
        SceneSendCouponResponse response = sceneDomainService.sceneSendCoupon(unionId, couponGroupId, appId, distributorCode, sceneDetail, sceneType, functionModule, taskStartTime);
        // assert
        assertNull(response);
        verify(mtUserCenterAclService, never()).getUserIdByUnionIdFromMtUserCenter(anyString());
        verify(couponDomainService, never()).sendMtCouponAndResponse(anyLong(), anyString(), anyString(),anyInt());
    }

    /**
     * Test exception handling scenario
     */
    @Test
    public void testSceneSendCoupon_ExceptionHandling() throws Throwable {
        // arrange
        String unionId = "testUnionId";
        String couponGroupId = "testCouponGroupId";
        String appId = "testAppId";
        String distributorCode = "testDistributorCode";
        String sceneDetail = "testSceneDetail";
        Integer sceneType = 1;
        FunctionModuleEnum functionModule = FunctionModuleEnum.AUTOMATED_MARKETING;
        Date taskStartTime = new Date();
        when(mtUserCenterAclService.getUserIdByUnionIdFromMtUserCenter(unionId)).thenThrow(new RuntimeException("Test exception"));
        // act
        SceneSendCouponResponse response = sceneDomainService.sceneSendCoupon(unionId, couponGroupId, appId, distributorCode, sceneDetail, sceneType, functionModule, taskStartTime);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        verify(couponDomainService, never()).sendMtCouponAndResponse(anyLong(), anyString(), anyString(),anyInt());
    }

}
