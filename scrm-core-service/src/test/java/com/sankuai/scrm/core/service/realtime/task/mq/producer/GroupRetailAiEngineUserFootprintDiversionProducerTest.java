package com.sankuai.scrm.core.service.realtime.task.mq.producer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import java.lang.reflect.Field;
import java.util.Properties;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GroupRetailAiEngineUserFootprintDiversionProducerTest {

    private GroupRetailAiEngineUserFootprintDiversionProducer producer;

    private MockedStatic<MafkaClient> mockedMafkaClient;

    @BeforeEach
    void setUp() throws Exception {
        producer = new GroupRetailAiEngineUserFootprintDiversionProducer();
        // Reset static field before each test using reflection
        setPrivateStaticField("producer", null);
    }

    @AfterEach
    void tearDown() {
        if (mockedMafkaClient != null) {
            mockedMafkaClient.close();
        }
    }

    /**
     * Helper method to set private static field value using reflection
     */
    private void setPrivateStaticField(String fieldName, Object value) throws Exception {
        Field field = GroupRetailAiEngineUserFootprintDiversionProducer.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(null, value);
    }

    /**
     * Helper method to get private static field value using reflection
     */
    private Object getPrivateStaticField(String fieldName) throws Exception {
        Field field = GroupRetailAiEngineUserFootprintDiversionProducer.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        return field.get(null);
    }

    /**
     * 测试正常初始化场景
     * 验证属性正确设置且生产者成功创建
     */
    @Test
    void testAfterPropertiesSetNormalCase() throws Throwable {
        // arrange
        IProducerProcessor mockProducer = mock(IProducerProcessor.class);
        mockedMafkaClient = mockStatic(MafkaClient.class);
        mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenReturn(mockProducer);
        // act
        producer.afterPropertiesSet();
        // assert
        mockedMafkaClient.verify(() -> MafkaClient.buildProduceFactory(any(Properties.class), eq("scrm.group.retail.ai.engine.user.footprint.diversion")));
        assertNotNull(getPrivateStaticField("producer"));
        assertEquals(mockProducer, getPrivateStaticField("producer"));
    }

    /**
     * 测试MafkaClient构建生产者抛出异常的场景
     * 验证异常被正确抛出且静态producer字段未被设置
     */
    @Test
    void testAfterPropertiesSetWhenMafkaClientThrowsException() throws Throwable {
        // arrange
        mockedMafkaClient = mockStatic(MafkaClient.class);
        mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenThrow(new RuntimeException("Mafka client error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> producer.afterPropertiesSet());
        assertNull(getPrivateStaticField("producer"));
    }

    /**
     * 测试重复初始化场景
     * 验证会重复创建生产者实例（根据实际实现行为调整）
     */
    @Test
    void testAfterPropertiesSetWhenCalledMultipleTimes() throws Throwable {
        // arrange
        IProducerProcessor mockProducer = mock(IProducerProcessor.class);
        mockedMafkaClient = mockStatic(MafkaClient.class);
        mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenReturn(mockProducer);
        // act - first call
        producer.afterPropertiesSet();
        // second call
        producer.afterPropertiesSet();
        // assert - verify buildProduceFactory called twice (as per actual implementation)
        mockedMafkaClient.verify(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString()), times(2));
    }

    /**
     * 测试属性设置是否正确
     * 验证properties中设置了正确的BG namespace和appkey
     */
    @Test
    void testAfterPropertiesSetPropertySettings() throws Throwable {
        // arrange
        IProducerProcessor mockProducer = mock(IProducerProcessor.class);
        mockedMafkaClient = mockStatic(MafkaClient.class);
        mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenAnswer(invocation -> {
            Properties props = invocation.getArgument(0);
            assertEquals("daozong", props.getProperty(ConsumerConstants.MafkaBGNamespace));
            assertEquals("com.sankuai.medicalcosmetology.scrm.core", props.getProperty(ConsumerConstants.MafkaClientAppkey));
            return mockProducer;
        });
        // act
        producer.afterPropertiesSet();
        // assert - verification is done in the thenAnswer block
    }
}
