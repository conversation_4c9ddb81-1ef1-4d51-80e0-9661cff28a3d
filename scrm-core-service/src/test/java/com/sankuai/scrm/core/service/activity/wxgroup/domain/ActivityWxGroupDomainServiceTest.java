package com.sankuai.scrm.core.service.activity.wxgroup.domain;

import com.google.common.collect.Lists;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmActivityAndDSPersonalWxGroupRelationMapDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.example.ScrmActivityAndDSPersonalWxGroupRelationMapDOExample;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmActivityAndDSPersonalWxGroupRelationMapDOMapper;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.never;

@ExtendWith(MockitoExtension.class)
public class ActivityWxGroupDomainServiceTest {

    @InjectMocks
    private ActivityWxGroupDomainService activityWxGroupDomainService;

    @Mock
    private ScrmActivityAndDSPersonalWxGroupRelationMapDOMapper relationMapper;

    @Captor
    private ArgumentCaptor<ScrmActivityAndDSPersonalWxGroupRelationMapDOExample> exampleCaptor;

    @Nested
    @DisplayName("queryDsGroupIdListByActivityId 方法测试")
    class QueryDsGroupIdListByActivityIdTest {

        @Test
        @DisplayName("当 activityId 为 null 时，应返回空列表")
        void shouldReturnEmptyListWhenActivityIdIsNull() {
            List<Long> result = activityWxGroupDomainService.queryDsGroupIdListByActivityId(null);
            assertTrue(result.isEmpty(), "当 activityId 为 null 时，结果列表应为空");
            verify(relationMapper, never()).selectByExample(any());
        }

        @Test
        @DisplayName("当找到关联关系时，应返回对应的 dsGroupId 列表")
        void shouldReturnDsGroupIdListWhenRelationsFound() {
            Long activityId = 1L;
            Long dsGroupId1 = 101L;
            Long dsGroupId2 = 102L;

            ScrmActivityAndDSPersonalWxGroupRelationMapDO relation1 = new ScrmActivityAndDSPersonalWxGroupRelationMapDO();
            relation1.setActivityId(activityId);
            relation1.setDsGroupId(dsGroupId1);
            relation1.setGroupIndex(1); // 模拟排序依据

            ScrmActivityAndDSPersonalWxGroupRelationMapDO relation2 = new ScrmActivityAndDSPersonalWxGroupRelationMapDO();
            relation2.setActivityId(activityId);
            relation2.setDsGroupId(dsGroupId2);
            relation2.setGroupIndex(2); // 模拟排序依据

            List<ScrmActivityAndDSPersonalWxGroupRelationMapDO> mockRelations = Lists.newArrayList(relation1, relation2);

            when(relationMapper.selectByExample(any(ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.class))).thenReturn(mockRelations);

            List<Long> result = activityWxGroupDomainService.queryDsGroupIdListByActivityId(activityId);

            assertNotNull(result, "结果列表不应为 null");
            assertEquals(2, result.size(), "结果列表应包含两个元素");
            assertEquals(dsGroupId1, result.get(0), "第一个元素应为 dsGroupId1");
            assertEquals(dsGroupId2, result.get(1), "第二个元素应为 dsGroupId2");

            verify(relationMapper).selectByExample(exampleCaptor.capture());
            ScrmActivityAndDSPersonalWxGroupRelationMapDOExample capturedExample = exampleCaptor.getValue();
            assertEquals(activityId, capturedExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
            assertEquals("group_index asc", capturedExample.getOrderByClause());
        }

        @Test
        @DisplayName("当找不到关联关系时，应返回空列表")
        void shouldReturnEmptyListWhenNoRelationsFound() {
            Long activityId = 2L;
            when(relationMapper.selectByExample(any(ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.class))).thenReturn(Collections.emptyList());

            List<Long> result = activityWxGroupDomainService.queryDsGroupIdListByActivityId(activityId);

            assertNotNull(result, "结果列表不应为 null");
            assertTrue(result.isEmpty(), "当找不到关联关系时，结果列表应为空");

            verify(relationMapper).selectByExample(exampleCaptor.capture());
             ScrmActivityAndDSPersonalWxGroupRelationMapDOExample capturedExample = exampleCaptor.getValue();
            assertEquals(activityId, capturedExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
            assertEquals("group_index asc", capturedExample.getOrderByClause());
        }

        @Test
        @DisplayName("当关联关系中的 dsGroupId 为 null 时，应过滤掉这些 null 值")
        void shouldFilterOutNullDsGroupIds() {
            Long activityId = 3L;
            Long dsGroupId1 = 103L;

            ScrmActivityAndDSPersonalWxGroupRelationMapDO relation1 = new ScrmActivityAndDSPersonalWxGroupRelationMapDO();
            relation1.setActivityId(activityId);
            relation1.setDsGroupId(dsGroupId1);
            relation1.setGroupIndex(1);

            ScrmActivityAndDSPersonalWxGroupRelationMapDO relationNull = new ScrmActivityAndDSPersonalWxGroupRelationMapDO();
            relationNull.setActivityId(activityId);
            relationNull.setDsGroupId(null); // dsGroupId 为 null
            relationNull.setGroupIndex(2);

            List<ScrmActivityAndDSPersonalWxGroupRelationMapDO> mockRelations = Lists.newArrayList(relation1, relationNull);

            when(relationMapper.selectByExample(any(ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.class))).thenReturn(mockRelations);

            List<Long> result = activityWxGroupDomainService.queryDsGroupIdListByActivityId(activityId);

            assertNotNull(result, "结果列表不应为 null");
            assertEquals(1, result.size(), "结果列表应只包含一个非 null 的元素");
            assertEquals(dsGroupId1, result.get(0), "结果列表应包含 dsGroupId1");
        }
    }

    @Nested
    @DisplayName("queryActivityIdByWxGroupId 方法测试")
    class QueryActivityIdByWxGroupIdTest {

        @Test
        @DisplayName("当 dsGroupId 为 null 时，应返回 null")
        void shouldReturnNullWhenDsGroupIdIsNull() {
            Long result = activityWxGroupDomainService.queryActivityIdByWxGroupId(null);
            assertNull(result, "当 dsGroupId 为 null 时，结果应为 null");
             verify(relationMapper, never()).selectByExample(any());
        }

        @Test
        @DisplayName("当找到关联关系时，应返回第一个关联的 activityId")
        void shouldReturnActivityIdWhenRelationFound() {
            Long dsGroupId = 201L;
            Long expectedActivityId = 11L;

            ScrmActivityAndDSPersonalWxGroupRelationMapDO relation1 = new ScrmActivityAndDSPersonalWxGroupRelationMapDO();
            relation1.setDsGroupId(dsGroupId);
            relation1.setActivityId(expectedActivityId);

            // 即使有多个，也只取第一个
            ScrmActivityAndDSPersonalWxGroupRelationMapDO relation2 = new ScrmActivityAndDSPersonalWxGroupRelationMapDO();
             relation2.setDsGroupId(dsGroupId);
            relation2.setActivityId(12L);


            List<ScrmActivityAndDSPersonalWxGroupRelationMapDO> mockRelations = Lists.newArrayList(relation1, relation2);

            when(relationMapper.selectByExample(any(ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.class))).thenReturn(mockRelations);

            Long result = activityWxGroupDomainService.queryActivityIdByWxGroupId(dsGroupId);

            assertNotNull(result, "结果不应为 null");
            assertEquals(expectedActivityId, result, "返回的 activityId 应为第一个关联关系的值");

            verify(relationMapper).selectByExample(exampleCaptor.capture());
            ScrmActivityAndDSPersonalWxGroupRelationMapDOExample capturedExample = exampleCaptor.getValue();
            assertEquals(dsGroupId, capturedExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
        }

        @Test
        @DisplayName("当找不到关联关系时，应返回 null")
        void shouldReturnNullWhenNoRelationFound() {
            Long dsGroupId = 202L;
            when(relationMapper.selectByExample(any(ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.class))).thenReturn(Collections.emptyList());

            Long result = activityWxGroupDomainService.queryActivityIdByWxGroupId(dsGroupId);

            assertNull(result, "当找不到关联关系时，结果应为 null");

             verify(relationMapper).selectByExample(exampleCaptor.capture());
            ScrmActivityAndDSPersonalWxGroupRelationMapDOExample capturedExample = exampleCaptor.getValue();
            assertEquals(dsGroupId, capturedExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
        }
    }
} 