package com.sankuai.scrm.core.service.activity.wxgroup.dal.example;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.util.ArrayList;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.jupiter.params.ParameterizedTest;
import org.junit.jupiter.params.provider.CsvSource;
import org.junit.jupiter.params.provider.ValueSource;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("ScrmDSPersonalWxGroupMemberInfoDOExample Limit Method Tests")
public class ScrmDSPersonalWxGroupMemberInfoDOExampleTest {

    private ScrmDSPersonalWxGroupMemberInfoDOExample example;

    /**
     * 测试异常场景 - 负数页码
     */
    @ParameterizedTest
    @ValueSource(ints = { -1, -5, -10 })
    void testPageWithNegativePageNumber(int negativePage) {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        int pageSize = 10;
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.page(negativePage, pageSize);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(negativePage * pageSize, result.getOffset(), "负数页码应产生负偏移量");
        assertEquals(10, result.getRows(), "行数设置不正确");
    }

    /**
     * 测试异常场景 - 负数页大小
     */
    @ParameterizedTest
    @ValueSource(ints = { -1, -5, -10 })
    void testPageWithNegativePageSize(int negativePageSize) {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        int page = 2;
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.page(page, negativePageSize);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(page * negativePageSize, result.getOffset(), "负数页大小应产生负偏移量");
        assertEquals(negativePageSize, result.getRows(), "行数应为负数");
    }

    /**
     * 参数化测试 - 多种分页组合
     */
    @ParameterizedTest
    @CsvSource({ "1, 10, 10, 10", "2, 20, 40, 20", "0, 5, 0, 5", "3, 0, 0, 0", "-1, 10, -10, 10", "2, -5, -10, -5" })
    void testPageWithVariousParameters(int page, int pageSize, int expectedOffset, int expectedRows) {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.page(page, pageSize);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(expectedOffset, result.getOffset(), "偏移量计算不正确");
        assertEquals(expectedRows, result.getRows(), "行数设置不正确");
    }

    @BeforeEach
    void setUp() {
        example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
    }

    /**
     * 测试 createCriteriaInternal 方法是否能正确创建并返回一个新的 Criteria 对象
     */
    @Test
    public void testCreateCriteriaInternal() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertNotNull(result);
    }

    /**
     * 测试正常分页场景 - 正数页码和页大小
     */
    @Test
    void testPageWithPositivePageAndSize() {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        int page = 2;
        int pageSize = 10;
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.page(page, pageSize);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(20, result.getOffset(), "偏移量计算不正确");
        assertEquals(10, result.getRows(), "行数设置不正确");
    }

    /**
     * 测试边界场景 - 第0页
     */
    @Test
    void testPageWithZeroPageNumber() {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        int page = 0;
        int pageSize = 10;
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.page(page, pageSize);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(0, result.getOffset(), "第0页偏移量应为0");
        assertEquals(10, result.getRows(), "行数设置不正确");
    }

    /**
     * 测试边界场景 - 页大小为0
     */
    @Test
    void testPageWithZeroPageSize() {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        int page = 2;
        int pageSize = 0;
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.page(page, pageSize);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(0, result.getOffset(), "页大小为0时偏移量应为0");
        assertEquals(0, result.getRows(), "行数应为0");
    }

    /**
     * 测试链式调用
     */
    @Test
    void testPageMethodChaining() {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.page(1, 10).page(2, 20);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(40, result.getOffset(), "第二次调用偏移量计算不正确");
        assertEquals(20, result.getRows(), "第二次调用行数设置不正确");
    }

    /**
     * Test limit method with positive integer value
     * Expected: Should set rows value and return instance for chaining
     */
    @Test
    @DisplayName("Should set positive rows value and return instance")
    public void testLimit_WithPositiveValue() {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        Integer rows = 10;
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.limit(rows);
        // assert
        assertAll(() -> assertEquals(rows, result.getRows(), "Rows value should be set to input value"), () -> assertSame(example, result, "Should return same instance for method chaining"));
    }

    /**
     * Test limit method with zero value
     * Expected: Should accept zero and return instance for chaining
     */
    @Test
    @DisplayName("Should accept zero rows value")
    public void testLimit_WithZero() {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        Integer rows = 0;
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.limit(rows);
        // assert
        assertAll(() -> assertEquals(rows, result.getRows(), "Rows value should be set to zero"), () -> assertSame(example, result, "Should return same instance for method chaining"));
    }

    /**
     * Test limit method with negative value
     * Expected: Should accept negative value and return instance for chaining
     */
    @Test
    @DisplayName("Should accept negative rows value")
    public void testLimit_WithNegativeValue() {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        Integer rows = -5;
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.limit(rows);
        // assert
        assertAll(() -> assertEquals(rows, result.getRows(), "Rows value should be set to negative value"), () -> assertSame(example, result, "Should return same instance for method chaining"));
    }

    /**
     * Test limit method with null value
     * Expected: Should accept null and return instance for chaining
     */
    @Test
    @DisplayName("Should accept null rows value")
    public void testLimit_WithNull() {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.limit(null);
        // assert
        assertAll(() -> assertNull(result.getRows(), "Rows value should be null"), () -> assertSame(example, result, "Should return same instance for method chaining"));
    }

    /**
     * Test limit method with multiple calls
     * Expected: Should update rows value and maintain chaining
     */
    @Test
    @DisplayName("Should update rows value on multiple calls")
    public void testLimit_MultipleCalls() {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        Integer initialRows = 10;
        Integer newRows = 20;
        // act
        example.limit(initialRows);
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.limit(newRows);
        // assert
        assertAll(() -> assertEquals(newRows, result.getRows(), "Rows value should be updated to new value"), () -> assertSame(example, result, "Should return same instance for method chaining"));
    }

    /**
     * Test limit method preserves other example properties
     * Expected: Should not affect other properties
     */
    @Test
    @DisplayName("Should preserve other properties when setting rows")
    public void testLimit_PreservesOtherProperties() {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        String orderBy = "id DESC";
        example.setOrderByClause(orderBy);
        Integer rows = 10;
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.limit(rows);
        // assert
        assertAll(() -> assertEquals(rows, result.getRows(), "Rows value should be set"), () -> assertEquals(orderBy, result.getOrderByClause(), "OrderByClause should be preserved"), () -> assertSame(example, result, "Should return same instance for method chaining"));
    }

    /**
     * Test limit method with valid positive integers
     * Expected: Should set offset and rows values and return the same instance
     */
    @Test
    @DisplayName("Should set valid positive integers for offset and rows")
    public void testLimit_WithValidPositiveIntegers() {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        Integer offset = 10;
        Integer rows = 20;
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.limit(offset, rows);
        // assert
        assertAll(() -> assertEquals(offset, result.getOffset(), "Offset should match the input value"), () -> assertEquals(rows, result.getRows(), "Rows should match the input value"), () -> assertSame(example, result, "Should return the same instance"));
    }

    /**
     * Test limit method with zero values
     * Expected: Should accept zero values and return the same instance
     */
    @Test
    @DisplayName("Should accept zero values for offset and rows")
    public void testLimit_WithZeroValues() {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        Integer offset = 0;
        Integer rows = 0;
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.limit(offset, rows);
        // assert
        assertAll(() -> assertEquals(offset, result.getOffset(), "Offset should be zero"), () -> assertEquals(rows, result.getRows(), "Rows should be zero"), () -> assertSame(example, result, "Should return the same instance"));
    }

    /**
     * Test limit method with null values
     * Expected: Should accept null values and return the same instance
     */
    @Test
    @DisplayName("Should accept null values for offset and rows")
    public void testLimit_WithNullValues() {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.limit(null, null);
        // assert
        assertAll(() -> assertNull(result.getOffset(), "Offset should be null"), () -> assertNull(result.getRows(), "Rows should be null"), () -> assertSame(example, result, "Should return the same instance"));
    }

    /**
     * Test limit method with negative values
     * Expected: Should accept negative values and return the same instance
     */
    @Test
    @DisplayName("Should accept negative values for offset and rows")
    public void testLimit_WithNegativeValues() {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        Integer offset = -10;
        Integer rows = -20;
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.limit(offset, rows);
        // assert
        assertAll(() -> assertEquals(offset, result.getOffset(), "Offset should match the negative input value"), () -> assertEquals(rows, result.getRows(), "Rows should match the negative input value"), () -> assertSame(example, result, "Should return the same instance"));
    }

    /**
     * Test limit method chaining functionality
     * Expected: Should update values when called multiple times
     */
    @Test
    @DisplayName("Should support method chaining and maintain latest values")
    public void testLimit_MethodChaining() {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        Integer finalOffset = 30;
        Integer finalRows = 40;
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.limit(10, 20).limit(finalOffset, finalRows);
        // assert
        assertAll(() -> assertEquals(finalOffset, result.getOffset(), "Offset should have the last set value"), () -> assertEquals(finalRows, result.getRows(), "Rows should have the last set value"), () -> assertSame(example, result, "Should return the same instance"));
    }

    /**
     * Test limit method with mixed null values
     * Expected: Should accept mixed null and non-null values
     */
    @Test
    @DisplayName("Should accept mixed null and non-null values")
    public void testLimit_WithMixedNullValues() {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        Integer offset = 10;
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample result = example.limit(offset, null);
        // assert
        assertAll(() -> assertEquals(offset, result.getOffset(), "Offset should match the input value"), () -> assertNull(result.getRows(), "Rows should be null"), () -> assertSame(example, result, "Should return the same instance"));
    }

    /**
     * Tests the scenario when oredCriteria list is empty.
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsEmpty() throws Throwable {
        // Arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        // Act
        ScrmDSPersonalWxGroupMemberInfoDOExample.Criteria criteria = example.createCriteria();
        // Assert
        assertNotNull(criteria);
        assertEquals(1, example.getOredCriteria().size());
    }

    /**
     * Tests the scenario when oredCriteria list is not empty.
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsNotEmpty() throws Throwable {
        // Arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        ScrmDSPersonalWxGroupMemberInfoDOExample.Criteria criteria1 = example.createCriteria();
        // Act
        ScrmDSPersonalWxGroupMemberInfoDOExample.Criteria criteria2 = example.createCriteria();
        // Assert
        assertNotNull(criteria2);
        assertEquals(1, example.getOredCriteria().size());
    }

    /**
     * Tests the or method.
     */
    @Test
    public void testOr() throws Throwable {
        // Arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        int originalSize = example.getOredCriteria().size();
        // Act
        ScrmDSPersonalWxGroupMemberInfoDOExample.Criteria criteria = example.or();
        // Assert
        assertNotNull(criteria);
        assertEquals(originalSize + 1, example.getOredCriteria().size());
        assertTrue(example.getOredCriteria().contains(criteria));
    }

    /**
     * Test clearing an empty ScrmDSPersonalWxGroupMemberInfoDOExample
     */
    @Test
    public void testClearEmptyExample() throws Throwable {
        // arrange - example is already empty after setup
        // act
        example.clear();
        // assert
        assertTrue(example.getOredCriteria().isEmpty());
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertNull(example.getRows());
        assertNull(example.getOffset());
    }

    /**
     * Test clearing a populated ScrmDSPersonalWxGroupMemberInfoDOExample
     */
    @Test
    public void testClearPopulatedExample() throws Throwable {
        // arrange
        example.setOrderByClause("id DESC");
        example.setDistinct(true);
        example.setRows(10);
        example.setOffset(5);
        // This adds a criteria
        example.or().andIdEqualTo(1L);
        // Verify initial state is populated
        assertFalse(example.getOredCriteria().isEmpty());
        assertEquals("id DESC", example.getOrderByClause());
        assertTrue(example.isDistinct());
        assertEquals(10, example.getRows());
        assertEquals(5, example.getOffset());
        // act
        example.clear();
        // assert
        assertTrue(example.getOredCriteria().isEmpty());
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertNull(example.getRows());
        assertNull(example.getOffset());
    }
}
