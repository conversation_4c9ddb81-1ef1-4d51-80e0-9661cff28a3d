package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.PrivateSendStrategy;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerDealAIRecallSupplyOfficialWxMessageV2Test {

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private MsgUnifiedPushService msgUnifiedPushService;

    @Mock
    private PrivateSendStrategy privateSendStrategy;

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private List<String> executorIds;

    private InvokeDetailKeyObject keyObject;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS;

    @BeforeEach
    public void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        executorIds = Lists.newArrayList("executorId", "executorId2");
        keyObject = new InvokeDetailKeyObject("key", (byte) 1, (byte) 1, 1L);
        totalInvokeDetailDOS = new ArrayList<>();
    }

    /**
     * Test case for empty totalInvokeDetailDOS.
     */
    @Test
    public void testDealAIRecallSupplyOfficialWxMessageV2EmptyInvokeDetailList() throws Throwable {
        // arrange
        totalInvokeDetailDOS.clear();
        // act
        officialWxHandler.dealAIRecallSupplyOfficialWxMessageV2(processOrchestrationDTO, executorIds, keyObject, totalInvokeDetailDOS, null);
        // assert
        verify(executeManagementService, never()).subTaskRunBegin(anyInt(), anyLong());
    }

}
