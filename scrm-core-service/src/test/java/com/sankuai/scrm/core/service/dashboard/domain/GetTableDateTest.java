package com.sankuai.scrm.core.service.dashboard.domain;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.dashboard.service.dto.UserDateRequest;
import com.sankuai.scrm.core.service.dashboard.dal.dto.EsNarrowlyTransactionDataSnapshot;
import com.sankuai.scrm.core.service.dashboard.dal.dto.EsTransactionDataSnapshot;
import com.sankuai.scrm.core.service.dashboard.dal.dto.TransactionDataNarrowly;
import com.sankuai.scrm.core.service.dashboard.dal.dto.TransactionDataWidely;
import com.sankuai.scrm.core.service.dashboard.domain.Utils;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class GetTableDateTest {

    @InjectMocks
    private GetTableDate getTableDate;

    @Mock
    private Utils utils;

    @Mock
    private DashBoardESReadDomainService dashBoardESReadDomainService;

    /**
     * Test case for populateNarrowlyTransactionData method
     * Scenario: When input list is empty
     */
    @Test
    public void testPopulateNarrowlyTransactionData_EmptyList() throws Throwable {
        // Arrange
        List<EsNarrowlyTransactionDataSnapshot> esTransactionDataSnapshots = Arrays.asList();
        UserDateRequest userDateRequest = new UserDateRequest();
        // Act
        List<TransactionDataNarrowly> result = getTableDate.populateNarrowlyTransactionData(esTransactionDataSnapshots, userDateRequest);
        // Assert
        assertEquals(0, result.size());
    }

    /**
     * Test case for populateNarrowlyTransactionData method
     * Scenario: When input list is not empty
     */
    @Test
    public void testPopulateNarrowlyTransactionData_NotEmptyList() throws Throwable {
        // Arrange
        EsNarrowlyTransactionDataSnapshot snapshot = new EsNarrowlyTransactionDataSnapshot();
        snapshot.setCorpId("corp1");
        snapshot.setNarrowUserCount(10L);
        snapshot.setTransactionOrderCount(20L);
        snapshot.setGtv("100");
        snapshot.setActualGtv("50");
        snapshot.setWriteOffGtv("200");
        snapshot.setActualCheckGtv("100");
        List<EsNarrowlyTransactionDataSnapshot> esTransactionDataSnapshots = Arrays.asList(snapshot);
        UserDateRequest userDateRequest = new UserDateRequest();
        // Mocking the utils.getCorpName method to return "corp1" for the given corpId
        when(utils.getCorpName("corp1")).thenReturn("corp1");
        // Act
        List<TransactionDataNarrowly> result = getTableDate.populateNarrowlyTransactionData(esTransactionDataSnapshots, userDateRequest);
        // Assert
        assertEquals(1, result.size());
        assertEquals("corp1", result.get(0).getCorpName());
        assertEquals(10L, result.get(0).getTransactionUserCountNarrowly().longValue());
        assertEquals(20L, result.get(0).getTransactionOrderCount().longValue());
        assertEquals(new java.math.BigDecimal("100"), result.get(0).getGtv());
        assertEquals(new java.math.BigDecimal("50"), result.get(0).getActualGtv());
        assertEquals(new java.math.BigDecimal("200"), result.get(0).getWriteOffGtv());
        assertEquals(new java.math.BigDecimal("100"), result.get(0).getActualCheckGtv());
    }

    /**
     * Test normal case with valid data
     */
    @Test
    public void testPopulateWidelyTransactionData_NormalCase() {
        // arrange
        UserDateRequest request = new UserDateRequest();
        request.setStartDate("2023-01-01");
        request.setEndDate("2023-01-31");
        EsTransactionDataSnapshot snapshot = EsTransactionDataSnapshot.builder().corpId("corp1").transactionOrderCount(100L).gtv("1000.00").actualGtv("900.00").writeOffGtv("800.00").actualCheckGtv("700.00").build();
        when(utils.getCorpName("corp1")).thenReturn("Corporation 1");
        when(dashBoardESReadDomainService.countTransactionFieldData(any(), eq("corp1"), any(), any())).thenReturn(50L);
        // act
        List<TransactionDataWidely> result = getTableDate.populateWidelyTransactionData(Collections.singletonList(snapshot), request);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        TransactionDataWidely data = result.get(0);
        assertEquals("Corporation 1", data.getCorpName());
        assertEquals(Long.valueOf(50L), data.getTransactionUserCountWidely());
        assertEquals(Long.valueOf(100L), data.getTransactionOrderCount());
        assertEquals(new BigDecimal("1000.00"), data.getGtv());
        assertEquals(new BigDecimal("900.00"), data.getActualGtv());
        assertEquals(new BigDecimal("800.00"), data.getWriteOffGtv());
        assertEquals(new BigDecimal("700.00"), data.getActualCheckGtv());
    }

    /**
     * Test with empty input list
     */
    @Test
    public void testPopulateWidelyTransactionData_EmptyList() {
        // arrange
        UserDateRequest request = new UserDateRequest();
        request.setStartDate("2023-01-01");
        request.setEndDate("2023-01-31");
        // act
        List<TransactionDataWidely> result = getTableDate.populateWidelyTransactionData(new ArrayList<>(), request);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test with multiple corporations
     */
    @Test
    public void testPopulateWidelyTransactionData_MultipleCorporations() {
        // arrange
        UserDateRequest request = new UserDateRequest();
        request.setStartDate("2023-01-01");
        request.setEndDate("2023-01-31");
        EsTransactionDataSnapshot snapshot1 = EsTransactionDataSnapshot.builder().corpId("corp1").transactionOrderCount(100L).gtv("1000.00").actualGtv("900.00").writeOffGtv("800.00").actualCheckGtv("700.00").build();
        EsTransactionDataSnapshot snapshot2 = EsTransactionDataSnapshot.builder().corpId("corp2").transactionOrderCount(200L).gtv("2000.00").actualGtv("1900.00").writeOffGtv("1800.00").actualCheckGtv("1700.00").build();
        when(utils.getCorpName("corp1")).thenReturn("Corporation 1");
        when(utils.getCorpName("corp2")).thenReturn("Corporation 2");
        when(dashBoardESReadDomainService.countTransactionFieldData(any(), eq("corp1"), any(), any())).thenReturn(50L);
        when(dashBoardESReadDomainService.countTransactionFieldData(any(), eq("corp2"), any(), any())).thenReturn(100L);
        // act
        List<TransactionDataWidely> result = getTableDate.populateWidelyTransactionData(Arrays.asList(snapshot1, snapshot2), request);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        TransactionDataWidely data1 = result.get(0);
        assertEquals("Corporation 1", data1.getCorpName());
        assertEquals(Long.valueOf(50L), data1.getTransactionUserCountWidely());
        assertEquals(Long.valueOf(100L), data1.getTransactionOrderCount());
        TransactionDataWidely data2 = result.get(1);
        assertEquals("Corporation 2", data2.getCorpName());
        assertEquals(Long.valueOf(100L), data2.getTransactionUserCountWidely());
        assertEquals(Long.valueOf(200L), data2.getTransactionOrderCount());
    }

    /**
     * Test with null GTVs in snapshot
     */
    @Test
    public void testPopulateWidelyTransactionData_NullGTVs() {
        // arrange
        UserDateRequest request = new UserDateRequest();
        request.setStartDate("2023-01-01");
        request.setEndDate("2023-01-31");
        EsTransactionDataSnapshot snapshot = EsTransactionDataSnapshot.builder().corpId("corp1").transactionOrderCount(100L).gtv(null).actualGtv(null).writeOffGtv(null).actualCheckGtv(null).build();
        when(utils.getCorpName("corp1")).thenReturn("Corporation 1");
        when(dashBoardESReadDomainService.countTransactionFieldData(any(), eq("corp1"), any(), any())).thenReturn(50L);
        // act
        List<TransactionDataWidely> result = getTableDate.populateWidelyTransactionData(Collections.singletonList(snapshot), request);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        TransactionDataWidely data = result.get(0);
        assertEquals(BigDecimal.ZERO, data.getGtv());
        assertEquals(BigDecimal.ZERO, data.getActualGtv());
        assertEquals(BigDecimal.ZERO, data.getWriteOffGtv());
        assertEquals(BigDecimal.ZERO, data.getActualCheckGtv());
    }

    /**
     * Test with invalid date format in request
     */
    @Test(expected = Exception.class)
    public void testPopulateWidelyTransactionData_InvalidDateFormat() {
        // arrange
        UserDateRequest request = new UserDateRequest();
        request.setStartDate("invalid-date");
        request.setEndDate("2023-01-31");
        // act
        getTableDate.populateWidelyTransactionData(new ArrayList<>(), request);
    }
}
