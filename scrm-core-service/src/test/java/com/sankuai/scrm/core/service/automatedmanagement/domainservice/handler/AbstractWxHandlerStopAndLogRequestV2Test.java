package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.assertFalse;
import static org.junit.jupiter.api.Assertions.assertTrue;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.MockedStatic;
import org.mockito.Mockito;

public class AbstractWxHandlerStopAndLogRequestV2Test {

    private AbstractWxHandler handler;

    private MockedStatic<Environment> mockedEnvironment;

    private MockedStatic<Lion> mockedLion;

    @BeforeEach
    public void setUp() {
        handler = new AbstractWxHandler() {

            @Override
            protected void logRequest(String request, String appId) {
                // Mock implementation for abstract method
            }

            @Override
            protected String getShorUrl(String coreUrl, boolean forceShortUrl) {
                // Mock implementation for abstract method
                return null;
            }
        };
        mockedEnvironment = Mockito.mockStatic(Environment.class);
        mockedLion = Mockito.mockStatic(Lion.class);
    }

    @AfterEach
    public void tearDown() {
        mockedEnvironment.close();
        mockedLion.close();
    }

    /**
     * Test stopAndLogRequestV2 when the environment is production.
     */
    @Test
    public void testStopAndLogRequestV2_EnvironmentIsProduction() {
        mockedEnvironment.when(Environment::isProductEnv).thenReturn(true);
        boolean result = handler.stopAndLogRequestV2("request", Collections.singletonList("executorId"), "appId");
        assertFalse(result, "Should return false when the environment is production");
    }

    /**
     * Test stopAndLogRequestV2 when the environment is not production and the executor is not in the whitelist.
     */
    @Test
    public void testStopAndLogRequestV2_ExecutorNotInWhitelist() {
        mockedEnvironment.when(Environment::isProductEnv).thenReturn(false);
        mockedLion.when(() -> Lion.getString(Environment.getAppName(), "automatedManagement.call.wx.service.mock.switch", "WangXueFei")).thenReturn("WangXueFei");
        List<String> executorIds = Arrays.asList("executorId");
        boolean result = handler.stopAndLogRequestV2("request", executorIds, "appId");
        assertTrue(result, "Should return true when the executor is not in the whitelist and environment is not production");
    }

    /**
     * Test stopAndLogRequestV2 when the environment is not production and the executor is in the whitelist.
     */
    @Test
    public void testStopAndLogRequestV2_ExecutorInWhitelist() {
        mockedEnvironment.when(Environment::isProductEnv).thenReturn(false);
        mockedLion.when(() -> Lion.getString(Environment.getAppName(), "automatedManagement.call.wx.service.mock.switch", "WangXueFei")).thenReturn("WangXueFei,executorId");
        List<String> executorIds = Arrays.asList("executorId");
        boolean result = handler.stopAndLogRequestV2("request", executorIds, "appId");
        assertFalse(result, "Should return false when the executor is in the whitelist and environment is not production");
    }
}
