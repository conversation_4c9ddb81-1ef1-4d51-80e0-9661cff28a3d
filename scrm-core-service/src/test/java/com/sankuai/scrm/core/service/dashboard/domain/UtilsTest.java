package com.sankuai.scrm.core.service.dashboard.domain;

import static org.junit.Assert.*;
import static org.mockito.Mockito.when;
import com.dianping.lion.client.Lion;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UtilsTest {

    // Note: The tests for matching and non-matching appIds are removed because they cannot be implemented
    @Mock
    private Lion lion;

    /**
     * 测试 appId 为 null 的情况
     */
    @Test
    public void testGetCorpNameByAppIdNull() throws Throwable {
        Utils utils = new Utils();
        String result = utils.getCorpNameByAppId(null);
        assertEquals("", result);
    }

    /**
     * 测试 appId 不为 null，但 businessInfo 列表为空的情况
     */
    @Test
    public void testGetCorpNameByAppIdEmptyBusinessInfo() throws Throwable {
        Utils utils = new Utils();
        String result = utils.getCorpNameByAppId("appId");
        assertEquals("", result);
    }
}
