package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionContentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxMomentSendRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.ExternalContactListInfoVO;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.VisibleRangeVO;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class StaffMomentTouchActionGetWxMomentSendRequestWithTagListTest {

    @InjectMocks
    @Spy
    private StaffMomentTouchAction staffMomentTouchAction;

    @Mock
    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;

    @Mock
    private ScrmProcessOrchestrationActionDTO actionDTO;

    @Mock
    private ExecuteManagementDTO executeManagementDTO;

    private StepExecuteResultDTO result;

    @BeforeEach
    void setUp() {
        result = new StepExecuteResultDTO();
    }

    /**
     * Test normal case with existing visible_range and external_contact_list
     */
    @Test
    public void testGetWxMomentSendRequestWithTagList_NormalCase() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        List<String> tagList = Arrays.asList("tag1", "tag2");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(null, 1, "appId", Arrays.asList("executor1"), 2);
        StepExecuteResultDTO result = new StepExecuteResultDTO();
        WxMomentSendRequest mockRequest = new WxMomentSendRequest();
        VisibleRangeVO visibleRange = new VisibleRangeVO();
        ExternalContactListInfoVO externalContactList = new ExternalContactListInfoVO();
        visibleRange.setExternal_contact_list(externalContactList);
        mockRequest.setVisible_range(visibleRange);
        doReturn(mockRequest).when(staffMomentTouchAction).getWxMomentSendRequest(any(), any(), any(), any(), any(), any());
        // act
        WxMomentSendRequest response = staffMomentTouchAction.getWxMomentSendRequestWithTagList(processOrchestrationDTO, executeLogDO, contentDTOS, actionDTO, tagList, executeManagementDTO, result);
        // assert
        assertNotNull(response);
        assertNotNull(response.getVisible_range());
        assertNotNull(response.getVisible_range().getExternal_contact_list());
        assertEquals(tagList, response.getVisible_range().getExternal_contact_list().getTag_list());
    }

    /**
     * Test case where request has visible_range but no external_contact_list
     */
    @Test
    public void testGetWxMomentSendRequestWithTagList_NoExternalContactList() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        List<String> tagList = Arrays.asList("tag1", "tag2");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(null, 1, "appId", Arrays.asList("executor1"), 2);
        StepExecuteResultDTO result = new StepExecuteResultDTO();
        WxMomentSendRequest mockRequest = new WxMomentSendRequest();
        VisibleRangeVO visibleRange = new VisibleRangeVO();
        mockRequest.setVisible_range(visibleRange);
        doReturn(mockRequest).when(staffMomentTouchAction).getWxMomentSendRequest(any(), any(), any(), any(), any(), any());
        // act
        WxMomentSendRequest response = staffMomentTouchAction.getWxMomentSendRequestWithTagList(processOrchestrationDTO, executeLogDO, contentDTOS, actionDTO, tagList, executeManagementDTO, result);
        // assert
        assertNotNull(response);
        assertNotNull(response.getVisible_range());
        assertNotNull(response.getVisible_range().getExternal_contact_list());
        assertEquals(tagList, response.getVisible_range().getExternal_contact_list().getTag_list());
    }

    /**
     * Test case where request is null
     */
    @Test
    public void testGetWxMomentSendRequestWithTagList_NullRequest() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        List<String> tagList = Arrays.asList("tag1", "tag2");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(null, 1, "appId", Arrays.asList("executor1"), 2);
        StepExecuteResultDTO result = new StepExecuteResultDTO();
        doReturn(null).when(staffMomentTouchAction).getWxMomentSendRequest(any(), any(), any(), any(), any(), any());
        // act
        WxMomentSendRequest response = staffMomentTouchAction.getWxMomentSendRequestWithTagList(processOrchestrationDTO, executeLogDO, contentDTOS, actionDTO, tagList, executeManagementDTO, result);
        // assert
        assertNull(response);
    }

    /**
     * Test case where request has no visible_range
     */
    @Test
    public void testGetWxMomentSendRequestWithTagList_NoVisibleRange() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        List<String> tagList = Arrays.asList("tag1", "tag2");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(null, 1, "appId", Arrays.asList("executor1"), 2);
        StepExecuteResultDTO result = new StepExecuteResultDTO();
        WxMomentSendRequest mockRequest = new WxMomentSendRequest();
        doReturn(mockRequest).when(staffMomentTouchAction).getWxMomentSendRequest(any(), any(), any(), any(), any(), any());
        // act
        WxMomentSendRequest response = staffMomentTouchAction.getWxMomentSendRequestWithTagList(processOrchestrationDTO, executeLogDO, contentDTOS, actionDTO, tagList, executeManagementDTO, result);
        // assert
        assertNotNull(response);
        assertNull(response.getVisible_range());
    }

    /**
     * Test case with empty tag list
     */
    @Test
    public void testGetWxMomentSendRequestWithTagList_EmptyTagList() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        List<String> tagList = new ArrayList<>();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(null, 1, "appId", Arrays.asList("executor1"), 2);
        StepExecuteResultDTO result = new StepExecuteResultDTO();
        WxMomentSendRequest mockRequest = new WxMomentSendRequest();
        VisibleRangeVO visibleRange = new VisibleRangeVO();
        ExternalContactListInfoVO externalContactList = new ExternalContactListInfoVO();
        visibleRange.setExternal_contact_list(externalContactList);
        mockRequest.setVisible_range(visibleRange);
        doReturn(mockRequest).when(staffMomentTouchAction).getWxMomentSendRequest(any(), any(), any(), any(), any(), any());
        // act
        WxMomentSendRequest response = staffMomentTouchAction.getWxMomentSendRequestWithTagList(processOrchestrationDTO, executeLogDO, contentDTOS, actionDTO, tagList, executeManagementDTO, result);
        // assert
        assertNotNull(response);
        assertNotNull(response.getVisible_range());
        assertNotNull(response.getVisible_range().getExternal_contact_list());
        assertTrue(response.getVisible_range().getExternal_contact_list().getTag_list().isEmpty());
    }

    /**
     * Test case with null tag list
     */
    @Test
    public void testGetWxMomentSendRequestWithTagList_NullTagList() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        List<String> tagList = null;
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(null, 1, "appId", Arrays.asList("executor1"), 2);
        StepExecuteResultDTO result = new StepExecuteResultDTO();
        WxMomentSendRequest mockRequest = new WxMomentSendRequest();
        VisibleRangeVO visibleRange = new VisibleRangeVO();
        ExternalContactListInfoVO externalContactList = new ExternalContactListInfoVO();
        visibleRange.setExternal_contact_list(externalContactList);
        mockRequest.setVisible_range(visibleRange);
        doReturn(mockRequest).when(staffMomentTouchAction).getWxMomentSendRequest(any(), any(), any(), any(), any(), any());
        // act
        WxMomentSendRequest response = staffMomentTouchAction.getWxMomentSendRequestWithTagList(processOrchestrationDTO, executeLogDO, contentDTOS, actionDTO, tagList, executeManagementDTO, result);
        // assert
        assertNotNull(response);
        assertNotNull(response.getVisible_range());
        assertNotNull(response.getVisible_range().getExternal_contact_list());
        assertNull(response.getVisible_range().getExternal_contact_list().getTag_list());
    }

    /**
     * Test supply content type scenario
     */
    @Test
    public void testGetWxMomentSendRequest_SupplyContentType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        // SUPPLY type
        actionDTO.setContentType(1);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTOS.add(contentDTO);
        WxMomentSendRequest expectedRequest = new WxMomentSendRequest();
        doReturn(expectedRequest).when(staffMomentTouchAction).getSupplyWxMomentSendRequest(any(), any(), any(), any(), any());
        // act
        WxMomentSendRequest actualRequest = staffMomentTouchAction.getWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, actionDTO, executeManagementDTO, result);
        // assert
        assertNotNull(actualRequest);
        assertEquals(expectedRequest, actualRequest);
        verify(staffMomentTouchAction).getSupplyWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
    }

    /**
     * Test normal content type scenario
     */
    @Test
    public void testGetWxMomentSendRequest_NormalContentType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        // Normal type
        actionDTO.setContentType(0);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTOS.add(contentDTO);
        WxMomentSendRequest expectedRequest = new WxMomentSendRequest();
        doReturn(expectedRequest).when(staffMomentTouchAction).getNormalWxMomentSendRequest(any(), any(), any(), any(), any());
        // act
        WxMomentSendRequest actualRequest = staffMomentTouchAction.getWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, actionDTO, executeManagementDTO, result);
        // assert
        assertNotNull(actualRequest);
        assertEquals(expectedRequest, actualRequest);
        verify(staffMomentTouchAction).getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
    }

    /**
     * Test empty content DTOs scenario
     */
    @Test
    public void testGetWxMomentSendRequest_EmptyContentDTOs() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setContentType(0);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        // Mock the behavior to handle empty content
        doReturn(null).when(staffMomentTouchAction).getNormalWxMomentSendRequest(any(), any(), eq(contentDTOS), any(), any());
        // act
        WxMomentSendRequest actualRequest = staffMomentTouchAction.getWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, actionDTO, executeManagementDTO, result);
        // assert
        assertNull(actualRequest);
        verify(staffMomentTouchAction).getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
    }

    /**
     * Test null content DTOs scenario
     */
    @Test
    public void testGetWxMomentSendRequest_NullContentDTOs() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setContentType(0);
        // Mock the behavior to handle null content
        doReturn(null).when(staffMomentTouchAction).getNormalWxMomentSendRequest(any(), any(), eq(null), any(), any());
        // act
        WxMomentSendRequest actualRequest = staffMomentTouchAction.getWxMomentSendRequest(processOrchestrationDTO, executeLogDO, null, actionDTO, executeManagementDTO, result);
        // assert
        assertNull(actualRequest);
        verify(staffMomentTouchAction).getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, null, executeManagementDTO, result);
    }

    /**
     * Test null action DTO scenario
     */
    @Test
    public void testGetWxMomentSendRequest_NullActionDTO() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTOS.add(contentDTO);
        // act & assert
        assertThrows(NullPointerException.class, () -> staffMomentTouchAction.getWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, null, executeManagementDTO, result));
    }

    /**
     * Test null result scenario
     */
    @Test
    public void testGetWxMomentSendRequest_NullResult() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setContentType(0);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTOS.add(contentDTO);
        // Mock the behavior to throw NPE when result is null
        doThrow(new NullPointerException("result cannot be null")).when(staffMomentTouchAction).getNormalWxMomentSendRequest(any(), any(), any(), any(), eq(null));
        // act & assert
        assertThrows(NullPointerException.class, () -> staffMomentTouchAction.getWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, actionDTO, executeManagementDTO, null));
    }
}
