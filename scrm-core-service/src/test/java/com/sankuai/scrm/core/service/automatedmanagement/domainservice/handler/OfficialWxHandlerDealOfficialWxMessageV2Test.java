package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.GroupSendStrategy;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.PrivateSendStrategy;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerDealOfficialWxMessageV2Test {

    @Spy
    private OfficialWxHandler officialWxHandler;

    @Mock
    private GroupSendStrategy groupSendStrategy;

    @Mock
    private PrivateSendStrategy privateSendStrategy;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private List<String> executorIds;

    private InvokeDetailKeyObject keyObject;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOList;

    String processOrchestrationMocks = "{\n" + "  \"id\" : 192,\n" + "  \"name\" : \"自动推品+全部商品+团购+群\",\n"
            + "  \"processOrchestrationType\" : 4,\n" + "  \"cron\" : \"\",\n"
            + "  \"beginTime\" : 1725702000000,\n" + "  \"endTime\" : 1725874800000,\n" + "  \"status\" : 1,\n"
            + "  \"validVersion\" : \"1725788339717\",\n" + "  \"updateTime\" : 1725788339000,\n"
            + "  \"creatorId\" : \"wangxuefei05\",\n" + "  \"lastUpdaterId\" : \"wangxuefei05\",\n"
            + "  \"participationRestrict\" : true,\n" + "  \"participationRestrictionsCycle\" : 0,\n"
            + "  \"participationRestrictionsTimes\" : 0,\n" + "  \"appId\" : \"yimei\",\n"
            + "  \"previewPic\" : \"\",\n" + "  \"cronComment\" : \"2024-09-08 17:40:00\",\n"
            + "  \"executorType\" : 1,\n" + "  \"executorList\" : [ {\n" + "    \"executorId\" : \"test\",\n"
            + "    \"executorName\" : \"王雪飞\",\n" + "    \"executorType\" : 2\n" + "  } ],\n"
            + "  \"crowdPackType\" : 3,\n" + "  \"crowdPackIdList\" : [ ],\n"
            + "  \"groupIdList\" : [ \"wrb_61EQAACdwDGbhFsGznPHZMX3i3_A\" ],\n" + "  \"groupInfoList\" : [ {\n"
            + "    \"groupName\" : \"军师测试25\",\n" + "    \"owner\" : \"WangXueFei\",\n"
            + "    \"robotList\" : null,\n" + "    \"groupId\" : \"wrb_61EQAACdwDGbhFsGznPHZMX3i3_A\",\n"
            + "    \"createDate\" : 1725788266000,\n" + "    \"memberCount\" : 3\n" + "  } ],\n"
            + "  \"crowdPackUpdateStrategyInfoDTO\" : null,\n" + "  \"goalDTO\" : {\n" + "    \"id\" : 554,\n"
            + "    \"checkTime\" : \"3\",\n" + "    \"checkTimeUnit\" : \"2\",\n" + "    \"status\" : 1,\n"
            + "    \"processOrchestrationId\" : 192,\n"
            + "    \"processOrchestrationVersion\" : \"1725788339717\",\n" + "    \"goalType\" : 1,\n"
            + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003 ],\n"
            + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n"
            + "  \"negativeGoalDTO\" : {\n" + "    \"id\" : 553,\n" + "    \"checkTime\" : \"3\",\n"
            + "    \"checkTimeUnit\" : \"2\",\n" + "    \"status\" : 1,\n"
            + "    \"processOrchestrationId\" : 192,\n"
            + "    \"processOrchestrationVersion\" : \"1725788339717\",\n" + "    \"goalType\" : 2,\n"
            + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003 ],\n"
            + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n"
            + "  \"nodeMediumDTO\" : {\n" + "    \"processOrchestrationNodeDTOList\" : [ {\n"
            + "      \"nodeId\" : 0,\n" + "      \"preNodeId\" : -1,\n" + "      \"nodeType\" : 4,\n"
            + "      \"id\" : 750,\n" + "      \"processOrchestrationId\" : 192,\n"
            + "      \"processOrchestrationVersion\" : \"1725788339717\",\n"
            + "      \"childrenNodes\" : [ 1725778802191 ]\n" + "    }, {\n" + "      \"nodeId\" : 1725778802191,\n"
            + "      \"preNodeId\" : 0,\n" + "      \"nodeType\" : 2,\n" + "      \"id\" : 751,\n"
            + "      \"processOrchestrationId\" : 192,\n"
            + "      \"processOrchestrationVersion\" : \"1725788339717\",\n" + "      \"childrenNodes\" : [ ]\n"
            + "    } ],\n" + "    \"conditionMap\" : { },\n" + "    \"actionMap\" : {\n"
            + "      \"1725778802191\" : {\n" + "        \"id\" : 343,\n" + "        \"actionId\" : 1,\n"
            + "        \"processOrchestrationId\" : 192,\n"
            + "        \"processOrchestrationVersion\" : \"1725788339717\",\n"
            + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"actionType\" : 7,\n"
            + "        \"actionSubType\" : 5,\n" + "        \"updateTime\" : 1725788339000,\n"
            + "        \"contentType\" : 1,\n" + "        \"contentList\" : null\n" + "      }\n" + "    },\n"
            + "    \"actionContentMap\" : {\n" + "      \"1725778802191-1\" : [ {\n" + "        \"id\" : 342,\n"
            + "        \"actionId\" : 1,\n" + "        \"contentId\" : 1,\n"
            + "        \"processOrchestrationId\" : 192,\n"
            + "        \"processOrchestrationVersion\" : \"1725788339717\",\n"
            + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"content\" : \"\",\n"
            + "        \"updateTime\" : 1725788339000,\n" + "        \"contentType\" : 1,\n"
            + "        \"attachmentDTOList\" : null\n" + "      } ]\n" + "    },\n"
            + "    \"actionAttachmentMap\" : {\n" + "      \"1725778802191-1-1\" : [ {\n"
            + "        \"id\" : 422,\n" + "        \"contentId\" : 1,\n" + "        \"actionId\" : 1,\n"
            + "        \"attachmentTypeId\" : 7,\n"
            + "        \"attachmentContent\" : \"{\\\"supplyType\\\":2,\\\"productType\\\":1,\\\"shelfName\\\":\\\"\\\",\\\"jumpPageType\\\":\\\"\\\",\\\"jumpUrl\\\":\\\"\\\",\\\"productId\\\":\\\"738067238,753099031,427691503\\\",\\\"supplyScope\\\":\\\"\\\",\\\"marketingCopy\\\":\\\"手动推品+团购\\\",\\\"marketingCopySource\\\":2,\\\"hotTagList\\\":\\\"\\\",\\\"topproductids\\\":\\\"\\\",\\\"headpicUrl\\\":\\\"\\\"}\",\n"
            + "        \"processOrchestrationId\" : 192,\n"
            + "        \"processOrchestrationVersion\" : \"1725788339717\",\n"
            + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"updateTime\" : null,\n"
            + "        \"attachmentSupplyDetailDTO\" : {\n" + "          \"supplyType\" : 2,\n"
            + "          \"productId\" : \"738067238,753099031,427691503\",\n" + "          \"productType\" : 1,\n"
            + "          \"marketingCopy\" : \"手动推品+团购\",\n" + "          \"marketingCopySource\" : 2,\n"
            + "          \"supplyScope\" : null,\n" + "          \"hotTagList\" : \"\",\n"
            + "          \"shelfName\" : \"\",\n" + "          \"jumpPageType\" : null,\n"
            + "          \"jumpUrl\" : \"\",\n" + "          \"topproductids\" : \"\",\n"
            + "          \"headpicUrl\" : \"\"\n" + "        },\n"
            + "        \"attachmentContentDetailDTO\" : null\n" + "      } ]\n" + "    }\n" + "  },\n"
            + "  \"executePlanDTO\" : null\n" + "}";

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = JsonUtils.toObject(processOrchestrationMocks, ScrmProcessOrchestrationDTO.class);
        executorIds = new ArrayList<>();
        executorIds.add("executor1");
        detailDOList = new ArrayList<>();
        keyObject = new InvokeDetailKeyObject("type", (byte) 0, (byte) 0, 1L);
    }

    /**
     * Test handling non-supply content type message
     */
    @Test
    public void testDealOfficialWxMessageV2_NonSupplyContent() throws Throwable {
        // arrange
        // NON_SUPPLY
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setTargetId("123");
        detailDO.setId(1L);
        detailDO.setProcessOrchestrationId(1L);
        detailDO.setProcessOrchestrationNodeId(1L);
        detailDOS.add(detailDO);

        keyObject.setContentType((byte) 0);
        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry =
                new AbstractMap.SimpleEntry<>(keyObject, detailDOS);

        // act
        officialWxHandler.dealOfficialWxMessageV2(processOrchestrationDTO, executorIds, entry, null);
        // assert
        verify(officialWxHandler, times(1))
                .dealNormalOfficialWxMessageV2(any(), any(), any(), any());
    }

    /**
     * Test handling automatic product promotion supply content
     */
    @Test
    public void testDealOfficialWxMessageV2_AutomaticProductPromotion() throws Throwable {
        // arrange
        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(keyObject, detailDOList);

        // SUPPLY
        keyObject.setContentType((byte) 1);
        // AUTOMATIC_PRODUCT_PROMOTION
        keyObject.setContentSubType((byte) 1);

        // act
        officialWxHandler.dealOfficialWxMessageV2(processOrchestrationDTO, executorIds, entry, null);
        // assert
        verify(officialWxHandler, times(1))
                .dealAIRecallSupplyOfficialWxMessageV2(any(), any(), any(), any(), any());
    }

    /**
     * Test handling manual product promotion supply content
     */
    @Test
    public void testDealOfficialWxMessageV2_ManualProductPromotion() throws Throwable {
        // arrange

        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setTargetId("123");
        detailDO.setId(1L);
        detailDO.setProcessOrchestrationId(1L);
        detailDO.setProcessOrchestrationNodeId(1L);
        detailDOS.add(detailDO);

        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(keyObject, detailDOS);
        // SUPPLY
        keyObject.setContentType((byte) 1);
        // MANUAL_PRODUCT_PROMOTION
        keyObject.setContentSubType((byte) 2);

        // act
        officialWxHandler.dealOfficialWxMessageV2(processOrchestrationDTO, executorIds, entry,null );
        // assert
        verify(officialWxHandler, times(1))
                .dealChosenSupplyOfficialWxMessageV2(any(), any(), any(), any(), any());
    }

    /**
     * Test handling customized product promotion supply content
     */
    @Test
    public void testDealOfficialWxMessageV2_CustomizedProductPromotion() throws Throwable {
        // arrange

        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setTargetId("123");
        detailDO.setId(1L);
        detailDO.setProcessOrchestrationId(1L);
        detailDO.setProcessOrchestrationNodeId(1L);
        detailDOS.add(detailDO);

        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(keyObject, detailDOS);

        // SUPPLY
        keyObject.setContentType((byte) 1);
        // CUSTOMIZED_PRODUCT_PROMOTION
        keyObject.setContentSubType((byte) 3);

        // act
        officialWxHandler.dealOfficialWxMessageV2(processOrchestrationDTO, executorIds, entry,null );
        // assert
        verify(officialWxHandler, times(1))
                .dealChosenSupplyOfficialWxMessageV2(any(), any(), any(), any(), any());
    }

    /**
     * Test handling coupon promotion supply content
     */
    @Test
    public void testDealOfficialWxMessageV2_CouponPromotion() throws Throwable {
        // arrange

        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setTargetId("123");
        detailDO.setId(1L);
        detailDO.setProcessOrchestrationId(1L);
        detailDO.setProcessOrchestrationNodeId(1L);
        detailDOS.add(detailDO);

        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(keyObject, detailDOS);
        // SUPPLY
        keyObject.setContentType((byte) 1);
        // COUPON_PROMOTION
        keyObject.setContentSubType((byte) 4);

        // act
        officialWxHandler.dealOfficialWxMessageV2(processOrchestrationDTO, executorIds, entry,null );
        // assert
        verify(officialWxHandler, times(1))
                .dealCouponSupplyOfficialWxMessageV2(any(), any(), any(), any(), any());
    }

    /**
     * Test handling null entry parameter
     */
    @Test
    public void testDealOfficialWxMessageV2_NullEntry() throws Throwable {
        // arrange & act & assert
        assertThrows(NullPointerException.class, () -> {
            officialWxHandler.dealOfficialWxMessageV2(processOrchestrationDTO, executorIds, null, null);
        });
        // verify no methods were called
        verify(officialWxHandler, never()).dealNormalOfficialWxMessageV2(any(), anyList(), any(), anyList());
        verify(officialWxHandler, never()).dealAIRecallSupplyOfficialWxMessageV2(any(), anyList(), any(), anyList(),any() );
        verify(officialWxHandler, never()).dealChosenSupplyOfficialWxMessageV2(any(), anyList(), any(), anyList(), any());
        verify(officialWxHandler, never()).dealCouponSupplyOfficialWxMessageV2(any(), anyList(), any(), anyList(), any());
    }

    /**
     * Test handling invalid content type
     */
    @Test
    public void testDealOfficialWxMessageV2_InvalidContentType() throws Throwable {
        // arrange
        // Invalid content type
        keyObject.setContentType((byte) 99);
        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(keyObject, detailDOList);
        // act
        officialWxHandler.dealOfficialWxMessageV2(processOrchestrationDTO, executorIds, entry,null );
        // assert
        verify(officialWxHandler, never()).dealNormalOfficialWxMessageV2(any(), anyList(), any(), anyList());
        verify(officialWxHandler, never()).dealAIRecallSupplyOfficialWxMessageV2(any(), anyList(), any(), anyList(), any());
        verify(officialWxHandler, never()).dealChosenSupplyOfficialWxMessageV2(any(), anyList(), any(), anyList(), any());
        verify(officialWxHandler, never()).dealCouponSupplyOfficialWxMessageV2(any(), anyList(), any(), anyList(), any());
    }
}
