package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionContentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.UploadWxAttachmentAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxMomentSendRequest;
import com.sankuai.scrm.core.service.infrastructure.vo.WechatMediaResult;
import com.sankuai.scrm.core.service.infrastructure.vo.WxMediaType;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class StaffMomentTouchActionGetSupplyWxMomentSendRequestTest {

    @InjectMocks
    private StaffMomentTouchAction staffMomentTouchAction;

    @Mock
    private UploadWxAttachmentAcl uploadWxAttachmentAcl;

    @Mock
    private ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;

    private ExecuteManagementDTO executeManagementDTO;

    private StepExecuteResultDTO result;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setAppId("testAppId");
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeManagementDTO = new ExecuteManagementDTO();
        executeManagementDTO.setStaffLimitSet(new HashSet<>(Arrays.asList("staff1", "staff2")));
        result = new StepExecuteResultDTO();
        result.setSuccess(true);
    }

    private List<ScrmProcessOrchestrationActionContentDTO> createContentDTOsWithValidAttachment() {
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setHeadpicUrl("http://test.com/image.jpg");
        supplyDetailDTO.setMarketingCopy("testCopy");
        attachmentDTO.setAttachmentSupplyDetailDTO(supplyDetailDTO);
        contentDTO.setAttachmentDTOList(Arrays.asList(attachmentDTO));
        return Arrays.asList(contentDTO);
    }

    private List<ScrmProcessOrchestrationActionContentDTO> createContentDTOsWithEmptyAttachment() {
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setAttachmentDTOList(new ArrayList<>());
        return Arrays.asList(contentDTO);
    }

    private List<ScrmProcessOrchestrationActionContentDTO> createContentDTOsWithNullSupplyDetail() {
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        contentDTO.setAttachmentDTOList(Arrays.asList(attachmentDTO));
        return Arrays.asList(contentDTO);
    }

    private List<ScrmProcessOrchestrationActionContentDTO> createContentDTOsWithEmptyHeadpicUrl() {
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setHeadpicUrl("");
        attachmentDTO.setAttachmentSupplyDetailDTO(supplyDetailDTO);
        contentDTO.setAttachmentDTOList(Arrays.asList(attachmentDTO));
        return Arrays.asList(contentDTO);
    }

    private List<ScrmProcessOrchestrationActionContentDTO> createContentDTOsWithValidMarketingCopy() {
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setHeadpicUrl("http://test.com/image.jpg");
        supplyDetailDTO.setMarketingCopy("testMarketingCopy");
        attachmentDTO.setAttachmentSupplyDetailDTO(supplyDetailDTO);
        contentDTO.setAttachmentDTOList(Arrays.asList(attachmentDTO));
        return Arrays.asList(contentDTO);
    }

    private List<ScrmProcessOrchestrationActionContentDTO> createContentDTOsWithMultipleAttachments() {
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        List<ScrmProcessOrchestrationActionAttachmentDTO> attachments = new ArrayList<>();
        ScrmProcessOrchestrationActionAttachmentDTO attachment1 = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetail1 = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetail1.setHeadpicUrl("http://test.com/image1.jpg");
        attachment1.setAttachmentSupplyDetailDTO(supplyDetail1);
        ScrmProcessOrchestrationActionAttachmentDTO attachment2 = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetail2 = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetail2.setHeadpicUrl("http://test.com/image2.jpg");
        attachment2.setAttachmentSupplyDetailDTO(supplyDetail2);
        attachments.add(attachment1);
        attachments.add(attachment2);
        contentDTO.setAttachmentDTOList(attachments);
        return Arrays.asList(contentDTO);
    }

    @Test
    public void testGetSupplyWxMomentSendRequest_SuccessWithValidAttachment() throws Throwable {
        // Arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = createContentDTOsWithValidAttachment();
        WechatMediaResult mediaResult = new WechatMediaResult();
        mediaResult.setMedia_id("testMediaId");
        mediaResult.setErrcode(0);
        doReturn(contentDTOS.get(0).getAttachmentDTOList()).when(nodeMediumDTO).getActionAttachmentDTOList(any());
        doReturn(mediaResult).when(uploadWxAttachmentAcl).uploadWxAttachment(anyString(), any(), anyString(), anyBoolean());
        // Act
        WxMomentSendRequest request = staffMomentTouchAction.getSupplyWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
        // Assert
        assertNotNull(request);
        assertNotNull(request.getAttachments());
        assertEquals("testMediaId", request.getAttachments().get(0).getImage().getMedia_id());
        assertTrue(result.isSuccess());
        verify(uploadWxAttachmentAcl).uploadWxAttachment(anyString(), any(), anyString(), anyBoolean());
        verify(nodeMediumDTO).getActionAttachmentDTOList(any());
    }

    @Test
    public void testGetSupplyWxMomentSendRequest_FailedMediaUpload() throws Throwable {
        // Arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = createContentDTOsWithValidAttachment();
        WechatMediaResult mediaResult = new WechatMediaResult();
        mediaResult.setErrcode(1);
        mediaResult.setErrmsg("Upload failed");
        doReturn(contentDTOS.get(0).getAttachmentDTOList()).when(nodeMediumDTO).getActionAttachmentDTOList(any());
        doReturn(mediaResult).when(uploadWxAttachmentAcl).uploadWxAttachment(anyString(), any(), anyString(), anyBoolean());
        // Act
        WxMomentSendRequest request = staffMomentTouchAction.getSupplyWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
        // Assert
        assertNull(request);
        assertFalse(result.isSuccess());
        assertTrue(result.isExistedFailedAttachmentUpload());
        verify(uploadWxAttachmentAcl).uploadWxAttachment(anyString(), any(), anyString(), anyBoolean());
    }

    @Test
    public void testGetSupplyWxMomentSendRequest_EmptyAttachmentList() throws Throwable {
        // Arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = createContentDTOsWithEmptyAttachment();
        doReturn(new ArrayList<>()).when(nodeMediumDTO).getActionAttachmentDTOList(any());
        // Act
        WxMomentSendRequest request = staffMomentTouchAction.getSupplyWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
        // Assert
        assertNull(request);
        verify(nodeMediumDTO).getActionAttachmentDTOList(any());
        verify(uploadWxAttachmentAcl, never()).uploadWxAttachment(anyString(), any(), anyString(), anyBoolean());
    }

    @Test
    public void testGetSupplyWxMomentSendRequest_NullSupplyDetail() throws Throwable {
        // Arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = createContentDTOsWithNullSupplyDetail();
        doReturn(contentDTOS.get(0).getAttachmentDTOList()).when(nodeMediumDTO).getActionAttachmentDTOList(any());
        // Act
        WxMomentSendRequest request = staffMomentTouchAction.getSupplyWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
        // Assert
        assertNull(request);
        verify(nodeMediumDTO).getActionAttachmentDTOList(any());
        verify(uploadWxAttachmentAcl, never()).uploadWxAttachment(anyString(), any(), anyString(), anyBoolean());
    }

    @Test
    public void testGetSupplyWxMomentSendRequest_EmptyHeadpicUrl() throws Throwable {
        // Arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = createContentDTOsWithEmptyHeadpicUrl();
        doReturn(contentDTOS.get(0).getAttachmentDTOList()).when(nodeMediumDTO).getActionAttachmentDTOList(any());
        // Act
        WxMomentSendRequest request = staffMomentTouchAction.getSupplyWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
        // Assert
        assertNull(request);
        verify(nodeMediumDTO).getActionAttachmentDTOList(any());
        verify(uploadWxAttachmentAcl, never()).uploadWxAttachment(anyString(), any(), anyString(), anyBoolean());
    }

    @Test
    public void testGetSupplyWxMomentSendRequest_ValidMarketingCopy() throws Throwable {
        // Arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = createContentDTOsWithValidMarketingCopy();
        WechatMediaResult mediaResult = new WechatMediaResult();
        mediaResult.setMedia_id("testMediaId");
        mediaResult.setErrcode(0);
        doReturn(contentDTOS.get(0).getAttachmentDTOList()).when(nodeMediumDTO).getActionAttachmentDTOList(any());
        doReturn(mediaResult).when(uploadWxAttachmentAcl).uploadWxAttachment(anyString(), any(), anyString(), anyBoolean());
        // Act
        WxMomentSendRequest request = staffMomentTouchAction.getSupplyWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
        // Assert
        assertNotNull(request);
        assertEquals("testMarketingCopy", request.getText().getContent());
        verify(uploadWxAttachmentAcl).uploadWxAttachment(anyString(), any(), anyString(), anyBoolean());
    }

    @Test
    public void testGetSupplyWxMomentSendRequest_MultipleAttachments() throws Throwable {
        // Arrange
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = createContentDTOsWithMultipleAttachments();
        WechatMediaResult mediaResult = new WechatMediaResult();
        mediaResult.setMedia_id("testMediaId");
        mediaResult.setErrcode(0);
        doReturn(contentDTOS.get(0).getAttachmentDTOList()).when(nodeMediumDTO).getActionAttachmentDTOList(any());
        doReturn(mediaResult).when(uploadWxAttachmentAcl).uploadWxAttachment(anyString(), any(), anyString(), anyBoolean());
        // Act
        WxMomentSendRequest request = staffMomentTouchAction.getSupplyWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
        // Assert
        assertNotNull(request);
        assertEquals(2, request.getAttachments().size());
        verify(uploadWxAttachmentAcl, times(2)).uploadWxAttachment(anyString(), any(), anyString(), anyBoolean());
    }
}
