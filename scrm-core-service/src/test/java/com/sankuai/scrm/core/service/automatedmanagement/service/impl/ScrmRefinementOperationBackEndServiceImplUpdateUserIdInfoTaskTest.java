package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.scrm.core.service.realtime.task.domainservice.ScrmGrowthUserInfoDomainService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ScrmRefinementOperationBackEndServiceImplUpdateUserIdInfoTaskTest {

    @Mock
    private ScrmGrowthUserInfoDomainService scrmGrowthUserInfoDomainService;

    @Mock
    private Transaction transaction;

    private MockedStatic<Cat> mockedCat;

    @InjectMocks
    private ScrmRefinementOperationBackEndServiceImpl scrmRefinementOperationBackEndService;

    @Before
    public void setUp() {
        // 模拟 Cat.newTransaction 方法
        mockedCat = mockStatic(Cat.class);
        mockedCat.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
    }

    @After
    public void tearDown() {
        // 清理静态模拟
        if (mockedCat != null) {
            mockedCat.close();
        }
    }

    /**
     * 测试 updateUserIdInfoTask 方法成功执行的场景
     */
    @Test
    public void testUpdateUserIdInfoTask_Success() {
        // Arrange
        // 当 scrmGrowthUserInfoDomainService.updateUserIdDaily() 被调用时，不执行任何操作
        doNothing().when(scrmGrowthUserInfoDomainService).updateUserIdDaily();
        // 当 transaction.setStatus(Transaction.SUCCESS) 被调用时，不执行任何操作
        doNothing().when(transaction).setStatus(Transaction.SUCCESS);
        // 当 transaction.complete() 被调用时，不执行任何操作
        doNothing().when(transaction).complete();

        // Act
        scrmRefinementOperationBackEndService.updateUserIdInfoTask();

        // Assert
        // 验证 Cat.newTransaction 被以正确的参数调用了一次
        mockedCat.verify(() -> Cat.newTransaction("ScrmRefinementOperationBackEndService", "updateUserIdInfoTask"), times(1));
        // 验证 transaction.setStatus(Transaction.SUCCESS) 被调用了一次
        verify(transaction, times(1)).setStatus(Transaction.SUCCESS);
        // 验证 scrmGrowthUserInfoDomainService.updateUserIdDaily() 被调用了一次
        verify(scrmGrowthUserInfoDomainService, times(1)).updateUserIdDaily();
        // 验证 transaction.complete() 被调用了一次
        verify(transaction, times(1)).complete();
    }

    /**
     * 测试 updateUserIdInfoTask 方法在 scrmGrowthUserInfoDomainService.updateUserIdDaily() 抛出异常时的场景
     * 注意：根据原始代码实现，即使发生异常，transaction 状态仍然保持为 SUCCESS
     */
    @Test
    public void testUpdateUserIdInfoTask_Exception() {
        // Arrange
        RuntimeException testException = new RuntimeException("Test Exception for updateUserIdDaily");
        // 当 scrmGrowthUserInfoDomainService.updateUserIdDaily() 被调用时，抛出 testException
        doThrow(testException).when(scrmGrowthUserInfoDomainService).updateUserIdDaily();
        // 当 transaction.setStatus(Transaction.SUCCESS) 被调用时，不执行任何操作
        doNothing().when(transaction).setStatus(Transaction.SUCCESS);
        // 当 transaction.complete() 被调用时，不执行任何操作
        doNothing().when(transaction).complete();

        // Act
        scrmRefinementOperationBackEndService.updateUserIdInfoTask();

        // Assert
        // 验证 Cat.newTransaction 被以正确的参数调用了一次
        mockedCat.verify(() -> Cat.newTransaction("ScrmRefinementOperationBackEndService", "updateUserIdInfoTask"), times(1));
        // 验证 transaction.setStatus(Transaction.SUCCESS) 被调用了一次 (根据现有代码行为，异常时不会更改状态)
        verify(transaction, times(1)).setStatus(Transaction.SUCCESS);
        // 验证 scrmGrowthUserInfoDomainService.updateUserIdDaily() 被调用了一次
        verify(scrmGrowthUserInfoDomainService, times(1)).updateUserIdDaily();
        // 验证 transaction.complete() 被调用了一次 (在 finally 块中)
        verify(transaction, times(1)).complete();
    }

    /**
     * 测试当 transaction.complete() 抛出异常时的场景
     * 注意：由于原始实现没有处理 complete() 方法的异常，我们预期异常会向上传播
     */
    @Test(expected = RuntimeException.class)
    public void testUpdateUserIdInfoTask_WhenTransactionCompleteThrowsException() {
        // Arrange
        doNothing().when(scrmGrowthUserInfoDomainService).updateUserIdDaily();
        doNothing().when(transaction).setStatus(Transaction.SUCCESS);
        doThrow(new RuntimeException("Transaction complete failed")).when(transaction).complete();

        // Act - 应该抛出异常
        scrmRefinementOperationBackEndService.updateUserIdInfoTask();

        // Assert - 不应该执行到这里
        verify(scrmGrowthUserInfoDomainService, times(1)).updateUserIdDaily();
        verify(transaction, times(1)).setStatus(Transaction.SUCCESS);
        verify(transaction, times(1)).complete();
    }

    /**
     * 测试当 Cat.newTransaction 返回 null 时的场景
     * 注意：由于原始实现假设 transaction 不为 null，我们预期会抛出 NPE
     */
    @Test(expected = NullPointerException.class)
    public void testUpdateUserIdInfoTask_WhenTransactionIsNull() {
        // Arrange
        mockedCat.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(null);

        // Act - 应该抛出 NPE
        scrmRefinementOperationBackEndService.updateUserIdInfoTask();

        // Assert - 不应该执行到这里
        verify(scrmGrowthUserInfoDomainService, never()).updateUserIdDaily();
    }
} 