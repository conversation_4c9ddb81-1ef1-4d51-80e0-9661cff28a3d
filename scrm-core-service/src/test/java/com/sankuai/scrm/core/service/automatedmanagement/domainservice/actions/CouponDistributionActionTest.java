package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum;
import com.sankuai.dz.srcm.coupon.dto.NextCouponInfoDTO;
import com.sankuai.dz.srcm.realtime.task.dto.SceneSendCouponResponse;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationMockLogMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.realtime.task.domainservice.SceneDomainService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/11/26
 */
@RunWith(MockitoJUnitRunner.class)
public class CouponDistributionActionTest {
    String processOrchestrationMocks = "{\n" + "  \"id\" : 192,\n" + "  \"name\" : \"自动推品+全部商品+团购+群\",\n"
            + "  \"processOrchestrationType\" : 4,\n" + "  \"cron\" : \"\",\n"
            + "  \"beginTime\" : 1725702000000,\n" + "  \"endTime\" : 1725874800000,\n" + "  \"status\" : 1,\n"
            + "  \"validVersion\" : \"1725788339717\",\n" + "  \"updateTime\" : 1725788339000,\n"
            + "  \"creatorId\" : \"wangxuefei05\",\n" + "  \"lastUpdaterId\" : \"wangxuefei05\",\n"
            + "  \"participationRestrict\" : true,\n" + "  \"participationRestrictionsCycle\" : 0,\n"
            + "  \"participationRestrictionsTimes\" : 0,\n" + "  \"appId\" : \"yimei\",\n"
            + "  \"previewPic\" : \"\",\n" + "  \"cronComment\" : \"2024-09-08 17:40:00\",\n"
            + "  \"executorType\" : 1,\n" + "  \"executorList\" : [ {\n" + "    \"executorId\" : \"test\",\n"
            + "    \"executorName\" : \"王雪飞\",\n" + "    \"executorType\" : 2\n" + "  } ],\n"
            + "  \"crowdPackType\" : 3,\n" + "  \"crowdPackIdList\" : [ ],\n"
            + "  \"groupIdList\" : [ \"wrb_61EQAACdwDGbhFsGznPHZMX3i3_A\" ],\n" + "  \"groupInfoList\" : [ {\n"
            + "    \"groupName\" : \"军师测试25\",\n" + "    \"owner\" : \"WangXueFei\",\n"
            + "    \"robotList\" : null,\n" + "    \"groupId\" : \"wrb_61EQAACdwDGbhFsGznPHZMX3i3_A\",\n"
            + "    \"createDate\" : 1725788266000,\n" + "    \"memberCount\" : 3\n" + "  } ],\n"
            + "  \"crowdPackUpdateStrategyInfoDTO\" : null,\n" + "  \"goalDTO\" : {\n" + "    \"id\" : 554,\n"
            + "    \"checkTime\" : \"3\",\n" + "    \"checkTimeUnit\" : \"2\",\n" + "    \"status\" : 1,\n"
            + "    \"processOrchestrationId\" : 192,\n"
            + "    \"processOrchestrationVersion\" : \"1725788339717\",\n" + "    \"goalType\" : 1,\n"
            + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003 ],\n"
            + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n"
            + "  \"negativeGoalDTO\" : {\n" + "    \"id\" : 553,\n" + "    \"checkTime\" : \"3\",\n"
            + "    \"checkTimeUnit\" : \"2\",\n" + "    \"status\" : 1,\n"
            + "    \"processOrchestrationId\" : 192,\n"
            + "    \"processOrchestrationVersion\" : \"1725788339717\",\n" + "    \"goalType\" : 2,\n"
            + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003 ],\n"
            + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n"
            + "  \"nodeMediumDTO\" : {\n" + "    \"processOrchestrationNodeDTOList\" : [ {\n"
            + "      \"nodeId\" : 0,\n" + "      \"preNodeId\" : -1,\n" + "      \"nodeType\" : 4,\n"
            + "      \"id\" : 750,\n" + "      \"processOrchestrationId\" : 192,\n"
            + "      \"processOrchestrationVersion\" : \"1725788339717\",\n"
            + "      \"childrenNodes\" : [ 1725778802191 ]\n" + "    }, {\n" + "      \"nodeId\" : 1725778802191,\n"
            + "      \"preNodeId\" : 0,\n" + "      \"nodeType\" : 2,\n" + "      \"id\" : 751,\n"
            + "      \"processOrchestrationId\" : 192,\n"
            + "      \"processOrchestrationVersion\" : \"1725788339717\",\n" + "      \"childrenNodes\" : [ ]\n"
            + "    } ],\n" + "    \"conditionMap\" : { },\n" + "    \"actionMap\" : {\n"
            + "      \"1725778802191\" : {\n" + "        \"id\" : 343,\n" + "        \"actionId\" : 1,\n"
            + "        \"processOrchestrationId\" : 192,\n"
            + "        \"processOrchestrationVersion\" : \"1725788339717\",\n"
            + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"actionType\" : 7,\n"
            + "        \"actionSubType\" : 5,\n" + "        \"updateTime\" : 1725788339000,\n"
            + "        \"contentType\" : 1,\n" + "        \"contentList\" : null\n" + "      }\n" + "    },\n"
            + "    \"actionContentMap\" : {\n" + "      \"1725778802191-1\" : [ {\n" + "        \"id\" : 342,\n"
            + "        \"actionId\" : 1,\n" + "        \"contentId\" : 1,\n"
            + "        \"processOrchestrationId\" : 192,\n"
            + "        \"processOrchestrationVersion\" : \"1725788339717\",\n"
            + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"content\" : \"\",\n"
            + "        \"updateTime\" : 1725788339000,\n" + "        \"contentType\" : 1,\n"
            + "        \"attachmentDTOList\" : null\n" + "      } ]\n" + "    },\n"
            + "    \"actionAttachmentMap\" : {\n" + "      \"1725778802191-1-1\" : [ {\n"
            + "        \"id\" : 422,\n" + "        \"contentId\" : 1,\n" + "        \"actionId\" : 1,\n"
            + "        \"attachmentTypeId\" : 7,\n"
            + "        \"attachmentContent\" : \"{\\\"supplyType\\\":2,\\\"productType\\\":1,\\\"shelfName\\\":\\\"\\\",\\\"jumpPageType\\\":\\\"\\\",\\\"jumpUrl\\\":\\\"\\\",\\\"productId\\\":\\\"738067238,753099031,427691503\\\",\\\"supplyScope\\\":\\\"\\\",\\\"marketingCopy\\\":\\\"手动推品+团购\\\",\\\"marketingCopySource\\\":2,\\\"hotTagList\\\":\\\"\\\",\\\"topproductids\\\":\\\"\\\",\\\"headpicUrl\\\":\\\"\\\"}\",\n"
            + "        \"processOrchestrationId\" : 192,\n"
            + "        \"processOrchestrationVersion\" : \"1725788339717\",\n"
            + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"updateTime\" : null,\n"
            + "        \"attachmentSupplyDetailDTO\" : {\n" + "          \"supplyType\" : 2,\n"
            + "          \"productId\" : \"738067238,753099031,427691503\",\n" + "          \"productType\" : 1,\n"
            + "          \"marketingCopy\" : \"手动推品+团购\",\n" + "          \"marketingCopySource\" : 2,\n"
            + "          \"supplyScope\" : null,\n" + "          \"hotTagList\" : \"\",\n"
            + "          \"shelfName\" : \"\",\n" + "          \"jumpPageType\" : null,\n"
            + "          \"jumpUrl\" : \"\",\n" + "          \"topproductids\" : \"\",\n"
            + "          \"headpicUrl\" : \"\"\n" + "        },\n"
            + "        \"attachmentContentDetailDTO\" : null\n" + "      } ]\n" + "    }\n" + "  },\n"
            + "  \"executePlanDTO\" : null\n" + "}";

    @InjectMocks
    private CouponDistributionAction couponDistributionAction;

    @Mock(lenient = true)
    private InformationGatheringService informationGatheringService;

    @Mock(lenient = true)
    private SceneDomainService sceneDomainService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock(lenient = true)
    private ExecuteManagementService managementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationMockLogMapper mockLogMapper;

    @Mock(lenient = true)
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordDOMapper;

    private List<ContactUser> originContactUsers;
    private ScrmProcessOrchestrationDTO processOrchestrationDTO;
    private ExecuteManagementDTO executeManagementDTO;
    private ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO;
    private ScrmProcessOrchestrationNodeDTO currentProcessingNode;
    private ScrmAmProcessOrchestrationExecuteLogDO existedExecuteLogDO;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
        originContactUsers = new ArrayList<>();
        ContactUser contactUser = new ContactUser();
        contactUser.setStaffId("test");
        originContactUsers.add(contactUser);
        
        // 初始化测试数据
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        processOrchestrationDTO.setAppId("test_app_id");
        processOrchestrationDTO.setProcessOrchestrationType((byte)3);
        
        // 初始化 NodeMediumDTO
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        Map<Long, ScrmProcessOrchestrationActionDTO> actionMap = new HashMap<>();
        nodeMediumDTO.setActionMap(actionMap);
        
        // 初始化 actionContentMap
        Map<String, List<ScrmProcessOrchestrationActionContentDTO>> actionContentMap = new HashMap<>();
        nodeMediumDTO.setActionContentMap(actionContentMap);
        
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        
        executeManagementDTO = new ExecuteManagementDTO(processOrchestrationDTO, 1, "test_app_id", new ArrayList<>(), 2);
        
        scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        scrmCrowdPackDetailInfoDTO.setExternalUserWxUnionId("test_union_id");
        
        currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setNodeId(1L);
        
        existedExecuteLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        existedExecuteLogDO.setId(1L);
        
        // Mock common dependencies
        when(managementService.getMtUserIdByUnionId(anyString())).thenReturn(123L);
        when(executeLogDOMapper.insertSelective(any())).thenReturn(1);
        when(executeLogDOMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        when(mockLogMapper.insert(any())).thenReturn(1);
        when(scrmSceneCouponRecordDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
    }

    @Test
    public void testDealCouponDistributionAction_ExecutorListSizeEqualsExistedWxInvokeLogDOMapSize() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationMocks,
                                                                                 ScrmProcessOrchestrationDTO.class);
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(processOrchestrationDTO, 1, processOrchestrationDTO.getAppId(), processOrchestrationDTO.getExecutorList().stream().map(o->o.getExecutorId()).collect(Collectors.toList()), 2);
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = processOrchestrationDTO.getNodeMediumDTO().getProcessOrchestrationNodeDTOList().get(0);
        ScrmAmProcessOrchestrationExecuteLogDO existedExecuteLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        existedExecuteLogDO.setId(1L);
        existedExecuteLogDO.setProcessOrchestrationId(processOrchestrationDTO.getId());
        existedExecuteLogDO.setProcessOrchestrationNodeId(currentProcessingNode.getNodeId());
        existedExecuteLogDO.setProcessOrchestrationVersion(processOrchestrationDTO.getValidVersion());
        existedExecuteLogDO.setGroupMemberId("1");

        when(managementService.getContactUserByExternalUnionId(any(), any())).thenReturn(originContactUsers);
        // act
        StepExecuteResultDTO result = couponDistributionAction.dealCouponDistributionAction(processOrchestrationDTO,
                                                                                            executeManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);

        // assert
        assertFalse(result.isSuccess());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getCode(),
                     result.getCode().intValue());
    }

    @Test
    public void testDealCouponDistributionAction_ContentListIsEmpty() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationMocks,
                                                                                 ScrmProcessOrchestrationDTO.class);
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(processOrchestrationDTO, 1, processOrchestrationDTO.getAppId(), processOrchestrationDTO.getExecutorList().stream().map(o->o.getExecutorId()).collect(Collectors.toList()), 2);
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = processOrchestrationDTO.getNodeMediumDTO().getProcessOrchestrationNodeDTOList().get(0);
        ScrmAmProcessOrchestrationExecuteLogDO existedExecuteLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        existedExecuteLogDO.setId(1L);
        existedExecuteLogDO.setProcessOrchestrationId(processOrchestrationDTO.getId());
        existedExecuteLogDO.setProcessOrchestrationNodeId(currentProcessingNode.getNodeId());
        existedExecuteLogDO.setProcessOrchestrationVersion(processOrchestrationDTO.getValidVersion());
        existedExecuteLogDO.setGroupMemberId("1");

        when(managementService.getContactUserByExternalUnionId(any(), any())).thenReturn(originContactUsers);
        // act
        StepExecuteResultDTO result = couponDistributionAction.dealCouponDistributionAction(processOrchestrationDTO,
                executeManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);

        // assert
        assertFalse(result.isSuccess());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getCode(),
                result.getCode().intValue());
    }

    @Test
    public void testDealCouponDistributionAction_WithNullContentList() {
        // 准备测试数据
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setProcessOrchestrationNodeId(currentProcessingNode.getNodeId());
        actionDTO.setActionId(1);
        processOrchestrationDTO.getNodeMediumDTO().getActionMap().put(currentProcessingNode.getNodeId(), actionDTO);
        
        // 设置 actionContentMap 返回 null
        String actionContentKey = String.format("%d-%d", actionDTO.getProcessOrchestrationNodeId(), actionDTO.getActionId());
        processOrchestrationDTO.getNodeMediumDTO().getActionContentMap().put(actionContentKey, null);

        // 执行测试
        StepExecuteResultDTO result = couponDistributionAction.dealCouponDistributionAction(
            processOrchestrationDTO, executeManagementDTO, scrmCrowdPackDetailInfoDTO, 
            currentProcessingNode, existedExecuteLogDO);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getCode(), 
            result.getCode().intValue());
    }

    @Test
    public void testDealCouponDistributionAction_WithEmptyContentList() {
        // 准备测试数据
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setProcessOrchestrationNodeId(currentProcessingNode.getNodeId());
        actionDTO.setActionId(1);
        processOrchestrationDTO.getNodeMediumDTO().getActionMap().put(currentProcessingNode.getNodeId(), actionDTO);
        
        // 设置 actionContentMap 返回空列表
        String actionContentKey = String.format("%d-%d", actionDTO.getProcessOrchestrationNodeId(), actionDTO.getActionId());
        processOrchestrationDTO.getNodeMediumDTO().getActionContentMap().put(actionContentKey, new ArrayList<>());

        // 执行测试
        StepExecuteResultDTO result = couponDistributionAction.dealCouponDistributionAction(
            processOrchestrationDTO, executeManagementDTO, scrmCrowdPackDetailInfoDTO, 
            currentProcessingNode, existedExecuteLogDO);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getCode(), 
            result.getCode().intValue());
    }

    @Test
    public void testDealCouponDistributionAction_SendCouponSuccess() {
        // 准备测试数据
        processOrchestrationDTO.setProcessOrchestrationType((byte)3);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setProcessOrchestrationNodeId(currentProcessingNode.getNodeId());
        actionDTO.setActionId(1);
        processOrchestrationDTO.getNodeMediumDTO().getActionMap().put(currentProcessingNode.getNodeId(), actionDTO);
        
        // 设置 actionContentMap 返回有效内容
        List<ScrmProcessOrchestrationActionContentDTO> contentList = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("{\"couponGroupId\":\"test_coupon_group\"}");
        contentList.add(contentDTO);
        
        String actionContentKey = String.format("%d-%d", actionDTO.getProcessOrchestrationNodeId(), actionDTO.getActionId());
        processOrchestrationDTO.getNodeMediumDTO().getActionContentMap().put(actionContentKey, contentList);
        
        // Mock sceneSendCoupon 返回成功
        SceneSendCouponResponse sendResult = new SceneSendCouponResponse();
        sendResult.setSuccess(true);
        List<NextCouponInfoDTO> successList = new ArrayList<>();
        successList.add(new NextCouponInfoDTO());
        sendResult.setSuccessList(successList);
        
        when(sceneDomainService.sceneSendCoupon(any(), any(), any(), any(), any(), any(), any(), any()))
            .thenReturn(sendResult);

        // 执行测试
        StepExecuteResultDTO result = couponDistributionAction.dealCouponDistributionAction(
            processOrchestrationDTO, executeManagementDTO, scrmCrowdPackDetailInfoDTO, 
            currentProcessingNode, existedExecuteLogDO);

        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SUCCESS.getCode(), 
            result.getCode().intValue());
        verify(sceneDomainService).sceneSendCoupon(any(), any(), any(), any(), any(), any(), any(), any());
        verify(informationGatheringService).logUserStatusChangeDetail(anyLong(), anyString(), anyLong(), 
            anyLong(), anyByte(), anyInt(), anyLong(), anyString());
    }

    @Test
    public void testDealCouponDistributionAction_SendCouponFail() {
        // 准备测试数据
        processOrchestrationDTO.setProcessOrchestrationType((byte)3);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setProcessOrchestrationNodeId(currentProcessingNode.getNodeId());
        actionDTO.setActionId(1);
        processOrchestrationDTO.getNodeMediumDTO().getActionMap().put(currentProcessingNode.getNodeId(), actionDTO);
        
        // 设置 actionContentMap 返回有效内容
        List<ScrmProcessOrchestrationActionContentDTO> contentList = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("{\"couponGroupId\":\"test_coupon_group\"}");
        contentList.add(contentDTO);
        
        String actionContentKey = String.format("%d-%d", actionDTO.getProcessOrchestrationNodeId(), actionDTO.getActionId());
        processOrchestrationDTO.getNodeMediumDTO().getActionContentMap().put(actionContentKey, contentList);
        
        // Mock sceneSendCoupon 返回失败
        SceneSendCouponResponse sendResult = new SceneSendCouponResponse();
        sendResult.setSuccess(false);
        
        when(sceneDomainService.sceneSendCoupon(any(), any(), any(), any(), any(), any(), any(), any()))
            .thenReturn(sendResult);

        // 执行测试
        StepExecuteResultDTO result = couponDistributionAction.dealCouponDistributionAction(
            processOrchestrationDTO, executeManagementDTO, scrmCrowdPackDetailInfoDTO, 
            currentProcessingNode, existedExecuteLogDO);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SEND_COUPON_FAIL.getCode(),
            result.getCode().intValue());
        verify(sceneDomainService).sceneSendCoupon(any(), any(), any(), any(), any(), any(), any(), any());
    }

    @Test
    public void testDealCouponDistributionAction_WithExistingExecuteLog() {
        // 准备测试数据
        processOrchestrationDTO.setProcessOrchestrationType((byte)3);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setProcessOrchestrationNodeId(currentProcessingNode.getNodeId());
        actionDTO.setActionId(1);
        processOrchestrationDTO.getNodeMediumDTO().getActionMap().put(currentProcessingNode.getNodeId(), actionDTO);
        
        // 设置 actionContentMap 返回有效内容
        List<ScrmProcessOrchestrationActionContentDTO> contentList = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("{\"couponGroupId\":\"test_coupon_group\"}");
        contentList.add(contentDTO);
        
        String actionContentKey = String.format("%d-%d", actionDTO.getProcessOrchestrationNodeId(), actionDTO.getActionId());
        processOrchestrationDTO.getNodeMediumDTO().getActionContentMap().put(actionContentKey, contentList);
        
        // Mock sceneSendCoupon 返回成功
        SceneSendCouponResponse sendResult = new SceneSendCouponResponse();
        sendResult.setSuccess(true);
        List<NextCouponInfoDTO> successList = new ArrayList<>();
        successList.add(new NextCouponInfoDTO());
        sendResult.setSuccessList(successList);
        
        when(sceneDomainService.sceneSendCoupon(any(), any(), any(), any(), any(), any(), any(), any()))
            .thenReturn(sendResult);

        // 执行测试
        StepExecuteResultDTO result = couponDistributionAction.dealCouponDistributionAction(
            processOrchestrationDTO, executeManagementDTO, scrmCrowdPackDetailInfoDTO, 
            currentProcessingNode, existedExecuteLogDO);

        // 验证结果
        assertTrue(result.isSuccess());
        verify(executeLogDOMapper, times(2)).updateByPrimaryKeySelective(any());
        verify(sceneDomainService).sceneSendCoupon(any(), any(), any(), any(), any(), any(), any(), any());
        verify(informationGatheringService).logUserStatusChangeDetail(anyLong(), anyString(), anyLong(), 
            anyLong(), anyByte(), anyInt(), anyLong(), anyString());
    }

    @Test
    public void testDealCouponDistributionAction_AiSceneWithNullCouponId() {
        // 准备测试数据
        processOrchestrationDTO.setProcessOrchestrationType((byte)3);
        processOrchestrationDTO.setAiScene(true);
        IntelligentFollowResultDTO aiSceneContent = new IntelligentFollowResultDTO();
        aiSceneContent.setCouponId(null);
        processOrchestrationDTO.setAiSceneContent(aiSceneContent);

        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setProcessOrchestrationNodeId(currentProcessingNode.getNodeId());
        actionDTO.setActionId(1);
        processOrchestrationDTO.getNodeMediumDTO().getActionMap().put(currentProcessingNode.getNodeId(), actionDTO);
        
        // 设置 actionContentMap 返回有效内容，确保 contentDetailDTO 非空
        List<ScrmProcessOrchestrationActionContentDTO> contentList = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("{\"couponGroupId\":\"test_coupon_group\"}");
        contentList.add(contentDTO);
        
        String actionContentKey = String.format("%d-%d", actionDTO.getProcessOrchestrationNodeId(), actionDTO.getActionId());
        processOrchestrationDTO.getNodeMediumDTO().getActionContentMap().put(actionContentKey, contentList);

        // 执行测试
        StepExecuteResultDTO result = couponDistributionAction.dealCouponDistributionAction(
            processOrchestrationDTO, executeManagementDTO, scrmCrowdPackDetailInfoDTO, 
            currentProcessingNode, existedExecuteLogDO);

        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SUCCESS.getCode(), 
            result.getCode().intValue());
    }

    @Test
    public void testDealCouponDistributionAction_AiScene_EmptyCouponId_ReturnsSuccess() {
        // 准备测试数据
        processOrchestrationDTO.setProcessOrchestrationType((byte)3);
        processOrchestrationDTO.setAiScene(true);
        IntelligentFollowResultDTO aiSceneContent = new IntelligentFollowResultDTO();
        aiSceneContent.setCouponId("");
        processOrchestrationDTO.setAiSceneContent(aiSceneContent);

        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setProcessOrchestrationNodeId(currentProcessingNode.getNodeId());
        actionDTO.setActionId(1);
        processOrchestrationDTO.getNodeMediumDTO().getActionMap().put(currentProcessingNode.getNodeId(), actionDTO);

        // 设置 actionContentMap 返回有效内容，确保 contentDetailDTO 非空
        List<ScrmProcessOrchestrationActionContentDTO> contentList = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("{\"couponGroupId\":\"test_coupon_group\"}");
        contentList.add(contentDTO);

        String actionContentKey = String.format("%d-%d", actionDTO.getProcessOrchestrationNodeId(), actionDTO.getActionId());
        processOrchestrationDTO.getNodeMediumDTO().getActionContentMap().put(actionContentKey, contentList);

        // 执行测试
        StepExecuteResultDTO result = couponDistributionAction.dealCouponDistributionAction(
                processOrchestrationDTO, executeManagementDTO, scrmCrowdPackDetailInfoDTO,
                currentProcessingNode, existedExecuteLogDO);

        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SUCCESS.getCode(),
                result.getCode().intValue());
    }

    @Test
    public void testDealCouponDistributionAction_AiScene_WhitespaceCouponId_ReturnsSuccess() {
        // 准备测试数据
        processOrchestrationDTO.setProcessOrchestrationType((byte)3);
        processOrchestrationDTO.setAiScene(true);
        IntelligentFollowResultDTO aiSceneContent = new IntelligentFollowResultDTO();
        aiSceneContent.setCouponId("   ");
        processOrchestrationDTO.setAiSceneContent(aiSceneContent);

        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setProcessOrchestrationNodeId(currentProcessingNode.getNodeId());
        actionDTO.setActionId(1);
        processOrchestrationDTO.getNodeMediumDTO().getActionMap().put(currentProcessingNode.getNodeId(), actionDTO);

        // 设置 actionContentMap 返回有效内容，确保 contentDetailDTO 非空
        List<ScrmProcessOrchestrationActionContentDTO> contentList = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("{\"couponGroupId\":\"test_coupon_group\"}");
        contentList.add(contentDTO);

        String actionContentKey = String.format("%d-%d", actionDTO.getProcessOrchestrationNodeId(), actionDTO.getActionId());
        processOrchestrationDTO.getNodeMediumDTO().getActionContentMap().put(actionContentKey, contentList);

        // 执行测试
        StepExecuteResultDTO result = couponDistributionAction.dealCouponDistributionAction(
                processOrchestrationDTO, executeManagementDTO, scrmCrowdPackDetailInfoDTO,
                currentProcessingNode, existedExecuteLogDO);

        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SUCCESS.getCode(),
                result.getCode().intValue());
    }

    @Test
    public void testDealCouponDistributionAction_AiSceneWithNotNullShopId() {
        // 准备测试数据
        processOrchestrationDTO.setProcessOrchestrationType((byte)3);
        processOrchestrationDTO.setAiScene(true);
        IntelligentFollowResultDTO aiSceneContent = new IntelligentFollowResultDTO();
        aiSceneContent.setCouponId("test_coupon_id");
        aiSceneContent.setShopId(123L);
        processOrchestrationDTO.setAiSceneContent(aiSceneContent);

        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setProcessOrchestrationNodeId(currentProcessingNode.getNodeId());
        actionDTO.setActionId(1);
        processOrchestrationDTO.getNodeMediumDTO().getActionMap().put(currentProcessingNode.getNodeId(), actionDTO);
        
        // 设置 actionContentMap 返回有效内容
        List<ScrmProcessOrchestrationActionContentDTO> contentList = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("{\"couponGroupId\":\"test_coupon_group\"}");
        contentList.add(contentDTO);
        
        String actionContentKey = String.format("%d-%d", actionDTO.getProcessOrchestrationNodeId(), actionDTO.getActionId());
        processOrchestrationDTO.getNodeMediumDTO().getActionContentMap().put(actionContentKey, contentList);

        // 执行测试
        StepExecuteResultDTO result = couponDistributionAction.dealCouponDistributionAction(
            processOrchestrationDTO, executeManagementDTO, scrmCrowdPackDetailInfoDTO, 
            currentProcessingNode, existedExecuteLogDO);

        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.AI_SHOP_COUPON.getCode(), 
            result.getCode().intValue());
    }

    /*@Test
    public void testDealCouponDistributionAction_AiSceneWithNullProductId() {
        // 准备测试数据
        processOrchestrationDTO.setProcessOrchestrationType((byte)3);
        processOrchestrationDTO.setAiScene(true);
        IntelligentFollowResultDTO aiSceneContent = new IntelligentFollowResultDTO();
        aiSceneContent.setCouponId("test_coupon_id");
        aiSceneContent.setShopId(null);
        aiSceneContent.setProductId(null);
        processOrchestrationDTO.setAiSceneContent(aiSceneContent);

        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setProcessOrchestrationNodeId(currentProcessingNode.getNodeId());
        actionDTO.setActionId(1);
        processOrchestrationDTO.getNodeMediumDTO().getActionMap().put(currentProcessingNode.getNodeId(), actionDTO);
        
        // 设置 actionContentMap 返回有效内容
        List<ScrmProcessOrchestrationActionContentDTO> contentList = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("{\"couponGroupId\":\"test_coupon_group\"}");
        contentList.add(contentDTO);
        
        String actionContentKey = String.format("%d-%d", actionDTO.getProcessOrchestrationNodeId(), actionDTO.getActionId());
        processOrchestrationDTO.getNodeMediumDTO().getActionContentMap().put(actionContentKey, contentList);

        // 执行测试
        StepExecuteResultDTO result = couponDistributionAction.dealCouponDistributionAction(
            processOrchestrationDTO, executeManagementDTO, scrmCrowdPackDetailInfoDTO, 
            currentProcessingNode, existedExecuteLogDO);

        // 验证结果
        assertFalse(result.isSuccess());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getCode(), 
            result.getCode().intValue());
    }*/

    @Test
    public void testDealCouponDistributionAction_AiSceneWithValidParams() {
        // 准备测试数据
        processOrchestrationDTO.setProcessOrchestrationType((byte)3);
        processOrchestrationDTO.setAiScene(true);
        IntelligentFollowResultDTO aiSceneContent = new IntelligentFollowResultDTO();
        aiSceneContent.setCouponId("ai_coupon_id");
        aiSceneContent.setShopId(null);
        aiSceneContent.setProductId(456L);
        processOrchestrationDTO.setAiSceneContent(aiSceneContent);

        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setProcessOrchestrationNodeId(currentProcessingNode.getNodeId());
        actionDTO.setActionId(1);
        processOrchestrationDTO.getNodeMediumDTO().getActionMap().put(currentProcessingNode.getNodeId(), actionDTO);
        
        // 设置 actionContentMap 返回有效内容
        List<ScrmProcessOrchestrationActionContentDTO> contentList = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("{\"couponGroupId\":\"test_coupon_group\"}");
        contentList.add(contentDTO);
        
        String actionContentKey = String.format("%d-%d", actionDTO.getProcessOrchestrationNodeId(), actionDTO.getActionId());
        processOrchestrationDTO.getNodeMediumDTO().getActionContentMap().put(actionContentKey, contentList);
        
        // Mock sceneSendCoupon 返回成功
        SceneSendCouponResponse sendResult = new SceneSendCouponResponse();
        sendResult.setSuccess(true);
        List<NextCouponInfoDTO> successList = new ArrayList<>();
        successList.add(new NextCouponInfoDTO());
        sendResult.setSuccessList(successList);
        
        when(sceneDomainService.sceneSendCoupon(any(), any(), any(), any(), any(), any(), any(), any()))
            .thenReturn(sendResult);

        // 执行测试
        StepExecuteResultDTO result = couponDistributionAction.dealCouponDistributionAction(
            processOrchestrationDTO, executeManagementDTO, scrmCrowdPackDetailInfoDTO, 
            currentProcessingNode, existedExecuteLogDO);

        // 验证结果
        assertTrue(result.isSuccess());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SUCCESS.getCode(), 
            result.getCode().intValue());
        verify(sceneDomainService).sceneSendCoupon(any(), eq("ai_coupon_id"), any(), any(), any(), any(), any(), any());
        verify(informationGatheringService).logUserStatusChangeDetail(anyLong(), anyString(), anyLong(), 
            anyLong(), anyByte(), anyInt(), anyLong(), anyString());
    }
}
