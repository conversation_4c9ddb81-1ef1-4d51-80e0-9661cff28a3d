package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGroupSendMessageAcl;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class OfficialWxHandlerBuildActivityPageAttachmentsV2Test {

    @Spy
    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private WxGroupSendMessageAcl wxGroupSendMessageAcl;

    @Mock
    private WeChatTokenAcl weChatTokenAcl;

    @Mock
    private ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @BeforeEach
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test case for empty pageDOS list.
     */
    @Test
    public void testBuildActivityPageAttachmentsV2EmptyPageDOS() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        List<ScrmAmProcessOrchestrationProductActivityPageDO> pageDOS = new ArrayList<>();
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        boolean isChosenSupply = false;
        // act
        List<MsgPushContentDTO> result = officialWxHandler.buildActivityPageAttachmentsV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageDOS, existedAttachmentMap, isChosenSupply,null );
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for isChosenSupply true and relatedProductIds blank.
     */
    @Test
    public void testBuildActivityPageAttachmentsV2ChosenSupplyBlankRelatedProductIds() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        List<ScrmAmProcessOrchestrationProductActivityPageDO> pageDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationProductActivityPageDO pageDO = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageDO.setRelatedProductIds("");
        pageDOS.add(pageDO);
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        boolean isChosenSupply = true;
        // act
        List<MsgPushContentDTO> result = officialWxHandler.buildActivityPageAttachmentsV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageDOS, existedAttachmentMap, isChosenSupply, null);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case: Empty entry value
     * Expected: Method should continue without processing
     */
    @Test
    public void testDealNormalOfficialWxGroupMessage_EmptyEntryValue() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        processOrchestrationDTO.setAppId("app1");
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        String executorId = "executor1";
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("type1", (byte) 1, (byte) 1, 1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        // act
        officialWxHandler.dealNormalOfficialWxGroupMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, stepExecuteResultDTO);
        // assert
        verify(wxInvokeLogDOMapper, never()).insert(any());
    }

    /**
     * Test case: Empty contentDTOS
     * Expected: Method should continue without processing
     */
    @Test
    public void testDealNormalOfficialWxGroupMessage_EmptyContentDTOS() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        processOrchestrationDTO.setAppId("app1");
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        String executorId = "executor1";
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("type1", (byte) 1, (byte) 1, 1L);
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setProcessOrchestrationNodeId(1L);
        detailDO.setGroupId("group1");
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = Collections.singletonList(detailDO);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        // Mock token result
        WeChatTokenResult tokenResult = WeChatTokenResult.builder().access_token("token123").errcode(0).errmsg("success").build();
        when(weChatTokenAcl.getTokenByCorpId(any())).thenReturn(tokenResult);
        // Mock empty content DTOs
        when(nodeMediumDTO.getActionDTO(eq(1L))).thenReturn(new ScrmProcessOrchestrationActionDTO());
        when(nodeMediumDTO.getActionContentDTOList(any())).thenReturn(Collections.emptyList());
        // Mock checkAlreadySentGroupMessage
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        officialWxHandler.dealNormalOfficialWxGroupMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, stepExecuteResultDTO);
        // assert
        verify(wxInvokeLogDOMapper, never()).insert(any());
    }
}
