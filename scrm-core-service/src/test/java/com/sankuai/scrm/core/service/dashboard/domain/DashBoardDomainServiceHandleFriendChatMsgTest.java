package com.sankuai.scrm.core.service.dashboard.domain;

import com.sankuai.scrm.core.service.activity.fission.domain.FissionInvitationDomainService;
import com.sankuai.scrm.core.service.dashboard.dal.dto.ActiveDataDashBoardUserLogDoc;
import com.sankuai.scrm.core.service.dashboard.dal.dto.UserDataDashboardUserLogDoc;
import com.sankuai.scrm.core.service.external.contact.dal.babymapper.ContactUserMapper;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.flow.dal.mapper.ScrmFlowMaterialRelationLogMapper;
import com.sankuai.scrm.core.service.group.domain.GroupMemberDomainService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DashBoardDomainServiceHandleFriendChatMsgTest {

    @InjectMocks
    private DashBoardDomainService dashBoardDomainService;

    @Mock
    private DashBoardESWriteDomainService dashBoardESWriteDomainService;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    private String validMsg;

    private String invalidMsg;

    private String groupMessageMsg;

    private String noFriendMsg;

    private String noUnionIdMsg;

    private String noMessageMsg;

    private String noCorpIdMsg;

    private String validCorpIdMsg;

    private String validCorpIdWithConfigMsg;

    @Mock
    private GroupMemberDomainService groupMemberDomainService;

    @Mock
    private ContactUserDomain contactUserDomain;

    @Mock
    private ContactUserMapper contactUserMapper;

    @Mock
    private FissionInvitationDomainService fissionInvitationDomainService;

    @Mock
    private ScrmFlowMaterialRelationLogMapper scrmFlowMaterialRelationLogMapper;

    @Before
    public void setUp() {
        validMsg = "{\"senderType\":2,\"friend\":{\"unionId\":\"unionId1\",\"wwCorpId\":\"corpId1\"},\"message\":{\"contentType\":0,\"content\":\"content1\"},\"sendTimeMillis\":1622505600000}";
        // Valid JSON but missing required fields
        invalidMsg = "{\"senderType\":2}";
        groupMessageMsg = "{\"senderType\":2,\"friend\":{\"unionId\":\"unionId1\",\"wwCorpId\":\"corpId1\"},\"message\":{\"contentType\":1,\"content\":\"GROUP_MESSAGE_CONSTANT\"},\"sendTimeMillis\":1622505600000}";
        noFriendMsg = "{\"senderType\":2,\"friend\":null,\"message\":{\"contentType\":0,\"content\":\"content1\"},\"sendTimeMillis\":1622505600000}";
        noUnionIdMsg = "{\"senderType\":2,\"friend\":{\"unionId\":null,\"wwCorpId\":\"corpId1\"},\"message\":{\"contentType\":0,\"content\":\"content1\"},\"sendTimeMillis\":1622505600000}";
        noMessageMsg = "{\"senderType\":2,\"friend\":{\"unionId\":\"unionId1\",\"wwCorpId\":\"corpId1\"},\"message\":null,\"sendTimeMillis\":1622505600000}";
        noCorpIdMsg = "{\"senderType\":2,\"businessCode\":\"testCode\",\"friend\":{\"unionId\":\"unionId1\",\"wwCorpId\":null},\"message\":{\"contentType\":0,\"content\":\"content1\"},\"sendTimeMillis\":1622505600000}";
        validCorpIdMsg = "{\"senderType\":2,\"friend\":{\"unionId\":\"unionId1\",\"wwCorpId\":\"corpId1\"},\"message\":{\"contentType\":0,\"content\":\"content1\"},\"sendTimeMillis\":1622505600000}";
        validCorpIdWithConfigMsg = "{\"senderType\":2,\"businessCode\":\"testCode\",\"friend\":{\"unionId\":\"unionId1\",\"wwCorpId\":null},\"message\":{\"contentType\":0,\"content\":\"content1\"},\"sendTimeMillis\":1622505600000}";
    }

    private ContactUser createContactUser(String corpId, String unionId, String state, String staffId) {
        ContactUser contactUser = new ContactUser();
        contactUser.setCorpId(corpId);
        contactUser.setUnionId(unionId);
        contactUser.setState(state);
        contactUser.setStaffId(staffId);
        contactUser.setStatus(1);
        contactUser.setUpdateTime(new Date());
        return contactUser;
    }

    /**
     * 测试反序列化失败的情况
     */
    @Test
    public void testHandleFriendChatMsgInvalidJson() throws Throwable {
        // arrange - use a valid JSON but with missing required fields
        String msg = "{\"senderType\":null}";
        // act
        dashBoardDomainService.handleFriendChatMsg(msg);
        // assert
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(anyString(), any(ActiveDataDashBoardUserLogDoc.class), anyString());
    }

    /**
     * 测试 senderType 不是 2 的情况
     */
    @Test
    public void testHandleFriendChatMsgSenderTypeNot2() throws Throwable {
        // arrange
        String msg = "{\"senderType\":1,\"friend\":{\"unionId\":\"unionId1\",\"wwCorpId\":\"corpId1\"},\"message\":{\"contentType\":0,\"content\":\"content1\"},\"sendTimeMillis\":1622505600000}";
        // act
        dashBoardDomainService.handleFriendChatMsg(msg);
        // assert
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(anyString(), any(ActiveDataDashBoardUserLogDoc.class), anyString());
    }

    /**
     * 测试 friend 对象为空的情况
     */
    @Test
    public void testHandleFriendChatMsgNoFriend() throws Throwable {
        // act
        dashBoardDomainService.handleFriendChatMsg(noFriendMsg);
        // assert
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(anyString(), any(ActiveDataDashBoardUserLogDoc.class), anyString());
    }

    /**
     * 测试 unionId 为空的情况
     */
    @Test
    public void testHandleFriendChatMsgNoUnionId() throws Throwable {
        // act
        dashBoardDomainService.handleFriendChatMsg(noUnionIdMsg);
        // assert
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(anyString(), any(ActiveDataDashBoardUserLogDoc.class), anyString());
    }

    /**
     * 测试 message 对象为空的情况
     */
    @Test
    public void testHandleFriendChatMsgNoMessage() throws Throwable {
        // act
        dashBoardDomainService.handleFriendChatMsg(noMessageMsg);
        // assert
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(anyString(), any(ActiveDataDashBoardUserLogDoc.class), anyString());
    }

    /**
     * 测试 corpId 为空且 corpAppConfigRepository.getConfigByBusinessCode 返回 null 的情况
     */
    @Test
    public void testHandleFriendChatMsgNoCorpIdAndNoConfig() throws Throwable {
        // arrange
        when(corpAppConfigRepository.getConfigByBusinessCode(anyString())).thenReturn(null);
        // act
        dashBoardDomainService.handleFriendChatMsg(noCorpIdMsg);
        // assert
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(anyString(), any(ActiveDataDashBoardUserLogDoc.class), anyString());
    }

    /**
     * 测试 corpId 为空但 corpAppConfigRepository.getConfigByBusinessCode 返回非空 CorpAppConfig 的情况
     */
    @Test
    public void testHandleFriendChatMsgNoCorpIdButWithConfig() throws Throwable {
        // arrange
        CorpAppConfig config = new CorpAppConfig();
        config.setCorpId("corpId1");
        when(corpAppConfigRepository.getConfigByBusinessCode(eq("testCode"))).thenReturn(config);
        // act
        dashBoardDomainService.handleFriendChatMsg(validCorpIdWithConfigMsg);
        // assert
        verify(dashBoardESWriteDomainService, times(1)).updateESDocByIdSelective(anyString(), any(ActiveDataDashBoardUserLogDoc.class), anyString());
    }

    /**
     * 测试 corpId 不为空的情况
     */
    @Test
    public void testHandleFriendChatMsgValidCorpId() throws Throwable {
        // act
        dashBoardDomainService.handleFriendChatMsg(validCorpIdMsg);
        // assert
        verify(dashBoardESWriteDomainService, times(1)).updateESDocByIdSelective(anyString(), any(ActiveDataDashBoardUserLogDoc.class), anyString());
    }

    @Test
    public void testHandleDashBoardUserLogWhenDelContactUser_UnionIdIsNull() throws Throwable {
        String corpId = "corpId";
        String externalUserId = "externalUserId";
        when(contactUserDomain.getUnionIdByExternalUserId(corpId, externalUserId)).thenReturn(null);
        dashBoardDomainService.handleDashBoardUserLogWhenDelContactUser(corpId, externalUserId);
        verify(contactUserDomain, times(1)).getUnionIdByExternalUserId(corpId, externalUserId);
        verify(groupMemberDomainService, never()).getMemberInfoEntitiesByCorpIdAndUnionId(anyString(), anyString());
        verify(contactUserDomain, never()).getContactUsersByCorpIdAndUnionIdAndExternalUserIdFromMaster(anyString(), anyString(), anyString());
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(anyString(), any(UserDataDashboardUserLogDoc.class), anyString());
    }

}
