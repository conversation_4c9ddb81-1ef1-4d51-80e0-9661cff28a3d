package com.sankuai.scrm.core.service.activity.miniprogram.service;

import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.activity.miniprogram.request.ActivityPageRequest;
import com.sankuai.dz.srcm.activity.miniprogram.vo.ItemCardVO;
import com.sankuai.dztheme.generalproduct.GeneralProductService;
import com.sankuai.dztheme.shop.service.DzThemeShopService;
import com.sankuai.dztheme.shop.vo.v1.ShopThemeResponse;
import com.sankuai.scrm.core.service.BaseMockTest;
import com.sankuai.scrm.core.service.activity.miniprogram.dal.entity.MiniProgramActivityItem;
import com.sankuai.scrm.core.service.activity.miniprogram.dal.entity.MiniProgramActivityProduct;
import com.sankuai.scrm.core.service.activity.miniprogram.domain.ActivityDomainService;
import com.sankuai.scrm.core.service.activity.miniprogram.domain.CouponDomainService;
import com.sankuai.scrm.core.service.activity.miniprogram.domain.ItemDomainService;
import com.sankuai.scrm.core.service.activity.miniprogram.domain.ProductDomainService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.concurrent.Future;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

/**
 * @Description
 * <AUTHOR>
 * @Create On 2024/12/4 14:20
 * @Version v1.0.0
 */
public class ActivityCustomerServiceImplGetItemCardVOListTest extends BaseMockTest {
    @InjectMocks
    private ActivityCustomerServiceImpl activityCustomerService;

    @Mock
    private ItemDomainService itemDomainService;

    @Mock
    private ProductDomainService productDomainService;

    @Mock
    private ActivityDomainService activityDomainService;
    @Mock
    private DzThemeShopService dzThemeShopService;
    @Mock
    private GeneralProductService generalProductService;
    @Mock
    private CouponDomainService couponDomainService;



    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试请求参数为null时的场景
     */
    @Test
    public void testGetItemCardVOListRequestIsNull() {
        // arrange
        ActivityPageRequest request = null;

        // act
        List<ItemCardVO> result = activityCustomerService.getItemCardVOList(request);

        // assert
        assertTrue("结果应为空列表", result.isEmpty());
    }

    /**
     * 测试活动项为空时的场景
     */
    @Test
    public void testGetItemCardVOListActivityItemsAreEmpty() {
        // arrange
        ActivityPageRequest request = mock(ActivityPageRequest.class);
        when(request.getActivityId()).thenReturn(1L);
        when(itemDomainService.queryItemList(anyLong())).thenReturn(Lists.newArrayList());

        // act
        List<ItemCardVO> result = activityCustomerService.getItemCardVOList(request);

        // assert
        assertTrue("结果应为空列表", result.isEmpty());
        verify(itemDomainService, times(1)).queryItemList(anyLong());
    }


    @Test
    public void testGetItemCardVOListWithValidRequest() throws Exception {
        // arrange
        ActivityPageRequest request = mock(ActivityPageRequest.class);
        when(request.getActivityId()).thenReturn(1L);
        when(request.getCityId()).thenReturn(10);
        when(request.getMtUserId()).thenReturn(123L);

        MiniProgramActivityItem item = new MiniProgramActivityItem();
        item.setId(1L);
        item.setDisplayOrder(1);
        List<MiniProgramActivityItem> itemList = Lists.newArrayList(item);
        when(itemDomainService.queryItemList(anyLong())).thenReturn(itemList);

        MiniProgramActivityProduct miniProgramActivityProduct = new MiniProgramActivityProduct();
        miniProgramActivityProduct.setProductId(1L);
        miniProgramActivityProduct.setShopId(1L);
        miniProgramActivityProduct.setMtShopId(1L);
        miniProgramActivityProduct.setCouponId("1");
        miniProgramActivityProduct.setItemId(1L);
        miniProgramActivityProduct.setDisplayOrder(1);
        miniProgramActivityProduct.setSkuId(1L);
        List<MiniProgramActivityProduct> products = Lists.newArrayList();
        products.add(miniProgramActivityProduct);
        when(productDomainService.queryProductList(anyList(), anyInt())).thenReturn(products);

        List<Future<ShopThemeResponse>> shopInfoFutureList = Lists.newArrayList();

        ShopThemeResponse shopThemeResponse = new ShopThemeResponse();
        when(dzThemeShopService.queryShopTheme(any())).thenReturn(shopThemeResponse);
        try (MockedStatic<FutureFactory> futureFactoryMockedStatic = mockStatic(FutureFactory.class)) {
            futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(any())).thenReturn(null);

            when(generalProductService.query(any())).thenReturn(null);
            when(couponDomainService.getDrawCouponInfoFuture(anyLong(),anyList())).thenReturn(null);
            // act
            List<ItemCardVO> result = activityCustomerService.getItemCardVOList(request);

            // assert
            assertNotNull("结果不应为空", result);
            assertFalse("结果不应为空列表", result.isEmpty());
            verify(itemDomainService, times(1)).queryItemList(anyLong());
            verify(productDomainService, times(1)).queryProductList(anyList(), anyInt());

        }
    }
}
