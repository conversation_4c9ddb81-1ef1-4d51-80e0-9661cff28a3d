package com.sankuai.scrm.core.service.envrequestforwarding.handle;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.meituan.dorado.rpc.meta.RpcInvocation;
import com.sankuai.scrm.core.service.envrequestforwarding.config.OfflineDataSyncConfig;
import com.sankuai.scrm.core.service.envrequestforwarding.constant.OfflineDataSyncConstant;
import java.util.Arrays;
import java.util.Collections;
import org.junit.runner.RunWith;
import org.mockito.*;
import org.slf4j.Logger;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class OfflineRequestHandlerCheckCorpIdTest {

    @InjectMocks
    private OfflineRequestHandler offlineRequestHandler;

    @Mock
    private Logger log;

    private static final String TEST_APP_NAME = "testApp";

    private static final String TEST_CORP_ID = "testCorpId";

    private static final String TEST_INTERFACE_NAME = "com.sankuai.scrm.core.service.envrequestforwarding.handle.OfflineRequestHandlerCheckCorpIdTest$TestInterface";

    // Helper interface for testing
    private interface TestInterface {

        String getName();
    }

    @Test
    public void testCheckCorpId_WhenInvocationIsNull() throws Throwable {
        String corpId = TEST_CORP_ID;
        boolean result = offlineRequestHandler.checkCorpId(corpId, null);
        assertFalse("Should return false when invocation is null", result);
    }

    @Test
    public void testCheckCorpId_WhenConfigIsNull() throws Throwable {
        String corpId = TEST_CORP_ID;
        RpcInvocation invocation = mock(RpcInvocation.class);
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn(TEST_APP_NAME);
            lionMock.when(() -> Lion.getBean(eq(TEST_APP_NAME), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class))).thenReturn(null);
            boolean result = offlineRequestHandler.checkCorpId(corpId, invocation);
            assertFalse("Should return false when config is null", result);
            envMock.verify(Environment::getAppName);
            lionMock.verify(() -> Lion.getBean(eq(TEST_APP_NAME), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class)));
        }
    }

    @Test
    public void testCheckCorpId_WhenCorpIdNotInConfig() throws Throwable {
        String corpId = "nonExistentCorpId";
        RpcInvocation invocation = mock(RpcInvocation.class);
        OfflineDataSyncConfig config = new OfflineDataSyncConfig();
        config.setCorpIds(Arrays.asList("corpId1", "corpId2"));
        config.setInterfaceNames(Collections.emptyList());
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn(TEST_APP_NAME);
            lionMock.when(() -> Lion.getBean(eq(TEST_APP_NAME), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class))).thenReturn(config);
            boolean result = offlineRequestHandler.checkCorpId(corpId, invocation);
            assertFalse("Should return false when corpId is not in config", result);
            envMock.verify(Environment::getAppName);
            lionMock.verify(() -> Lion.getBean(eq(TEST_APP_NAME), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class)));
        }
    }

    @Test
    public void testCheckCorpId_WhenCorpIdInConfig() throws Throwable {
        String corpId = TEST_CORP_ID;
        RpcInvocation invocation = mock(RpcInvocation.class);
        OfflineDataSyncConfig config = new OfflineDataSyncConfig();
        config.setCorpIds(Arrays.asList(TEST_CORP_ID, "corpId2"));
        config.setInterfaceNames(Collections.emptyList());
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn(TEST_APP_NAME);
            lionMock.when(() -> Lion.getBean(eq(TEST_APP_NAME), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class))).thenReturn(config);
            boolean result = offlineRequestHandler.checkCorpId(corpId, invocation);
            assertTrue("Should return true when corpId is in config", result);
            envMock.verify(Environment::getAppName);
            lionMock.verify(() -> Lion.getBean(eq(TEST_APP_NAME), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class)));
        }
    }

    @Test
    public void testCheckCorpId_WhenInterfaceNameInConfig() throws Throwable {
        String corpId = "anyCorpId";
        RpcInvocation invocation = mock(RpcInvocation.class);
        OfflineDataSyncConfig config = new OfflineDataSyncConfig();
        config.setCorpIds(Collections.emptyList());
        config.setInterfaceNames(Arrays.asList(TEST_INTERFACE_NAME));
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class);
            MockedStatic<Environment> envMock = mockStatic(Environment.class)) {
            envMock.when(Environment::getAppName).thenReturn(TEST_APP_NAME);
            lionMock.when(() -> Lion.getBean(eq(TEST_APP_NAME), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class))).thenReturn(config);
            when(invocation.getServiceInterface()).thenReturn((Class) TestInterface.class);
            boolean result = offlineRequestHandler.checkCorpId(corpId, invocation);
            assertTrue("Should return true when interface name is in config", result);
            verify(invocation, times(1)).getServiceInterface();
            envMock.verify(Environment::getAppName);
            lionMock.verify(() -> Lion.getBean(eq(TEST_APP_NAME), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class)));
        }
    }
}
