package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductPoolDeleteProductsDTO;
import com.sankuai.dz.srcm.automatedmanagement.request.productpool.BatchDeleteProductRequest;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class ScrmAutoManagementProductPoolServiceImpl_BatchDeleteProductsTest {

    @InjectMocks
    private ScrmAutoManagementProductPoolServiceImpl scrmAutoManagementProductPoolService;

    @Mock(lenient = true)
    private ProductManagementService productManagementService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testBatchDeleteProductsNormal() throws Throwable {
        // arrange
        BatchDeleteProductRequest request = new BatchDeleteProductRequest();
        request.setAppId("appId");
        request.setProducts(Arrays.asList(new ProductPoolDeleteProductsDTO()));
        doNothing().when(productManagementService).batchDeleteProducts(anyString(), anyList());
        // act
        RemoteResponse response = scrmAutoManagementProductPoolService.batchDeleteProducts(request);
        // assert
        assertTrue(response.isSuccess());
    }

    /**
     * 测试 appId 为空的情况
     */
    @Test
    public void testBatchDeleteProductsAppIdEmpty() throws Throwable {
        // arrange
        BatchDeleteProductRequest request = new BatchDeleteProductRequest();
        request.setAppId("");
        request.setProducts(Arrays.asList(new ProductPoolDeleteProductsDTO()));
        // act
        RemoteResponse response = scrmAutoManagementProductPoolService.batchDeleteProducts(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("请求参数错误", response.getMsg());
    }

    /**
     * 测试 products 列表为空的情况
     */
    @Test
    public void testBatchDeleteProductsProductsEmpty() throws Throwable {
        // arrange
        BatchDeleteProductRequest request = new BatchDeleteProductRequest();
        request.setAppId("appId");
        request.setProducts(null);
        // act
        RemoteResponse response = scrmAutoManagementProductPoolService.batchDeleteProducts(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("请求参数错误", response.getMsg());
    }

    /**
     * 测试删除商品过程中发生异常的情况
     */
    @Test
    public void testBatchDeleteProductsException() throws Throwable {
        // arrange
        BatchDeleteProductRequest request = new BatchDeleteProductRequest();
        request.setAppId("appId");
        request.setProducts(Arrays.asList(new ProductPoolDeleteProductsDTO()));
        doThrow(new RuntimeException()).when(productManagementService).batchDeleteProducts(anyString(), anyList());
        // act
        RemoteResponse response = scrmAutoManagementProductPoolService.batchDeleteProducts(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("删除商品失败", response.getMsg());
    }
}
