package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.dianping.pay.unifiedorder.onlinequery.model.UnifiedOrderDiscountDTO;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.*;
import com.sankuai.scrm.core.service.aigc.service.SupplyMarketingTextService;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.*;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.dashboard.domain.DashBoardESWriteDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.order.UnifiedOrderAclService;
import com.sankuai.scrm.core.service.util.ThreadPoolUtils;
import com.sankuai.technician.trade.api.order.enums.OrderDistributionTypeEnum;
import com.sankuai.technician.trade.api.order.message.OrderOperateNotify;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

/**
 * Test class for InformationGatheringService
 */
class InformationGatheringServiceDealCommunityOrder1Test {

    @Mock
    private ScrmAmProcessOrchestrationDistributorCodeDOMapper distributorCodeDOMapper;

    @Mock
    private UnifiedOrderAclService unifiedOrderAclService;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper invokeDetailDOMapper;

    @Mock
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationRelatedOrderInfoDOMapper relatedOrderInfoDOMapper;

    @Mock
    private DashBoardESWriteDomainService dashBoardESWriteDomainService;

    @Mock
    private SupplyMarketingTextService supplyMarketingTextService;

    @Mock
    private ScrmAmProcessOrchestrationExecuteGoalDetailDOMapper executeGoalDetailDOMapper;

    @InjectMocks
    private InformationGatheringService informationGatheringService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // Basic mock setup
        when(executeGoalDetailDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(executeGoalDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(executeGoalDetailDOMapper.insert(any())).thenReturn(1);
        when(executeGoalDetailDOMapper.batchInsert(any())).thenReturn(1);
        when(relatedOrderInfoDOMapper.insert(any())).thenReturn(1);
        doNothing().when(supplyMarketingTextService).batchRecycleUserTag(any(), any());
        doNothing().when(supplyMarketingTextService).recycleUserTag(anyLong(), any());
        // Mock ES service to avoid actual ES operations
        doNothing().when(dashBoardESWriteDomainService).updateESDocByIdSelective(anyString(), any(), anyString());
    }

    @Test
    public void testDealCommunityOrderWeakAttributionPath() throws Throwable {
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        operateNotify.setOperateType(1);
        operateNotify.setOrderId("order123");
        Map<String, String> extInfo = new HashMap<>();
        extInfo.put("distributorId", "123");
        operateNotify.setExtInfo(extInfo);
        ScrmAmProcessOrchestrationDistributorCodeDO codeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        codeDO.setProcessOrchestrationId(1L);
        codeDO.setProcessOrchestrationVersion("v1");
        codeDO.setProcessOrchestrationNodeId(1L);
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Arrays.asList(codeDO));
        UnifiedOrderWithId order = new UnifiedOrderWithId();
        order.setMtUserId(100L);
        order.setUnifiedOrderId("UO123");
        when(unifiedOrderAclService.getOrderInfo(any())).thenReturn(order);
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(any())).thenReturn(CompletableFuture.completedFuture(processOrchestrationDTO));
        when(executeLogDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(relatedOrderInfoDOMapper).insert(any());
    }

    @Test
    public void testDealCommunityOrderStrongAttributionWithCouponOrder() throws Throwable {
        // Prepare test data
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        operateNotify.setOperateType(1);
        operateNotify.setOrderId("order123");
        Map<String, String> extInfo = new HashMap<>();
        extInfo.put("distributorId", "123");
        operateNotify.setExtInfo(extInfo);
        // Setup distributor code
        ScrmAmProcessOrchestrationDistributorCodeDO codeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        codeDO.setProcessOrchestrationId(1L);
        codeDO.setProcessOrchestrationVersion("v1");
        codeDO.setProcessOrchestrationNodeId(1L);
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Arrays.asList(codeDO));
        // Setup order
        UnifiedOrderWithId order = new UnifiedOrderWithId();
        order.setMtUserId(100L);
        order.setUnifiedOrderId("UO123");
        UnifiedOrderDiscountDTO discount = new UnifiedOrderDiscountDTO();
        discount.setDiscountType(2);
        discount.setDiscountId("coupon123");
        order.setDiscounts(Arrays.asList(discount));
        when(unifiedOrderAclService.getOrderInfo(any())).thenReturn(order);
        // Setup process orchestration
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ACTION.getValue());
        nodeDTO.setNodeId(1L);
        nodeDTO.setProcessOrchestrationId(1L);
        nodeDTO.setProcessOrchestrationVersion("v1");
        processOrchestrationDTO.setNodeMediumDTO(new ScrmProcessOrchestrationNodeMediumDTO());
        processOrchestrationDTO.getNodeMediumDTO().addProcessOrchestrationNodeDTO(nodeDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(any())).thenReturn(CompletableFuture.completedFuture(processOrchestrationDTO));
        // Setup execute log
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDO.setTargetUnionId("union123");
        executeLogDO.setProcessOrchestrationId(1L);
        executeLogDO.setProcessOrchestrationVersion("v1");
        executeLogDO.setProcessOrchestrationNodeId(1L);
        when(executeLogDOMapper.selectByExample(any())).thenReturn(Arrays.asList(executeLogDO));
        // Setup invoke detail
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO.setContentSubType(ScrmProcessOrchestrationContentSupplyTypeEnum.COUPON_PROMOTION.getValue().byteValue());
        invokeDetailDO.setProcessOrchestrationId(1L);
        invokeDetailDO.setProcessOrchestrationVersion("v1");
        invokeDetailDO.setProcessOrchestrationNodeId(1L);
        invokeDetailDO.setExecuteLogId(1L);
        when(invokeDetailDOMapper.selectByExample(any())).thenReturn(Arrays.asList(invokeDetailDO));
        // Setup coupon record
        ScrmSceneCouponRecords couponRecord = new ScrmSceneCouponRecords();
        couponRecord.setUnifiedcouponid("coupon123");
        couponRecord.setCoupongroupid("group123");
        when(scrmSceneCouponRecordDOMapper.selectByExample(any())).thenReturn(Arrays.asList(couponRecord));
        // Execute test
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        // Verify results
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        // Verify goal detail creation
        ArgumentCaptor<List<ScrmAmProcessOrchestrationExecuteGoalDetailDO>> goalDetailCaptor = ArgumentCaptor.forClass((Class) List.class);
        verify(executeGoalDetailDOMapper).batchInsert(goalDetailCaptor.capture());
        List<ScrmAmProcessOrchestrationExecuteGoalDetailDO> capturedGoalDetails = goalDetailCaptor.getValue();
        assertNotNull(capturedGoalDetails);
        assertFalse(capturedGoalDetails.isEmpty());
        ScrmAmProcessOrchestrationExecuteGoalDetailDO goalDetail = capturedGoalDetails.get(0);
        assertEquals(1L, goalDetail.getProcessOrchestrationId());
        assertEquals("v1", goalDetail.getProcessOrchestrationVersion());
        assertEquals(1L, goalDetail.getProcessOrchestrationNodeId());
        assertEquals(1L, goalDetail.getExecuteLogId());
        verify(supplyMarketingTextService).batchRecycleUserTag(any(), any());
        verify(relatedOrderInfoDOMapper).insert(any());
    }

    @Test
    public void testDealCommunityOrderStrongAttributionWithRegularOrder() throws Throwable {
        // Prepare test data
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        operateNotify.setOperateType(1);
        operateNotify.setOrderId("order123");
        Map<String, String> extInfo = new HashMap<>();
        extInfo.put("distributorId", "123");
        operateNotify.setExtInfo(extInfo);
        // Setup distributor code
        ScrmAmProcessOrchestrationDistributorCodeDO codeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        codeDO.setProcessOrchestrationId(1L);
        codeDO.setProcessOrchestrationVersion("v1");
        codeDO.setProcessOrchestrationNodeId(1L);
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Arrays.asList(codeDO));
        // Setup order
        UnifiedOrderWithId order = new UnifiedOrderWithId();
        order.setMtUserId(100L);
        order.setUnifiedOrderId("UO123");
        when(unifiedOrderAclService.getOrderInfo(any())).thenReturn(order);
        // Setup process orchestration
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ACTION.getValue());
        nodeDTO.setNodeId(1L);
        nodeDTO.setProcessOrchestrationId(1L);
        nodeDTO.setProcessOrchestrationVersion("v1");
        processOrchestrationDTO.setNodeMediumDTO(new ScrmProcessOrchestrationNodeMediumDTO());
        processOrchestrationDTO.getNodeMediumDTO().addProcessOrchestrationNodeDTO(nodeDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(any())).thenReturn(CompletableFuture.completedFuture(processOrchestrationDTO));
        // Setup execute log
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDO.setTargetUnionId("union123");
        executeLogDO.setProcessOrchestrationId(1L);
        executeLogDO.setProcessOrchestrationVersion("v1");
        executeLogDO.setProcessOrchestrationNodeId(1L);
        when(executeLogDOMapper.selectByExample(any())).thenReturn(Arrays.asList(executeLogDO));
        // Setup invoke detail
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        // Not coupon type
        invokeDetailDO.setContentSubType((byte) 1);
        invokeDetailDO.setProcessOrchestrationId(1L);
        invokeDetailDO.setProcessOrchestrationVersion("v1");
        invokeDetailDO.setProcessOrchestrationNodeId(1L);
        invokeDetailDO.setExecuteLogId(1L);
        when(invokeDetailDOMapper.selectByExample(any())).thenReturn(Arrays.asList(invokeDetailDO));
        // Execute test
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        // Verify results
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        // Verify goal detail creation
        ArgumentCaptor<List<ScrmAmProcessOrchestrationExecuteGoalDetailDO>> goalDetailCaptor = ArgumentCaptor.forClass((Class) List.class);
        verify(executeGoalDetailDOMapper).batchInsert(goalDetailCaptor.capture());
        List<ScrmAmProcessOrchestrationExecuteGoalDetailDO> capturedGoalDetails = goalDetailCaptor.getValue();
        assertNotNull(capturedGoalDetails);
        assertFalse(capturedGoalDetails.isEmpty());
        ScrmAmProcessOrchestrationExecuteGoalDetailDO goalDetail = capturedGoalDetails.get(0);
        assertEquals(1L, goalDetail.getProcessOrchestrationId());
        assertEquals("v1", goalDetail.getProcessOrchestrationVersion());
        assertEquals(1L, goalDetail.getProcessOrchestrationNodeId());
        assertEquals(1L, goalDetail.getExecuteLogId());
        verify(supplyMarketingTextService).batchRecycleUserTag(any(), any());
        verify(relatedOrderInfoDOMapper).insert(any());
    }

    @Test
    public void testDealCommunityOrderWhenProcessOrchestrationDetailQueryFails() throws Throwable {
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        operateNotify.setOperateType(1);
        operateNotify.setOrderId("order123");
        Map<String, String> extInfo = new HashMap<>();
        extInfo.put("distributorId", "123");
        operateNotify.setExtInfo(extInfo);
        ScrmAmProcessOrchestrationDistributorCodeDO codeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        codeDO.setProcessOrchestrationId(1L);
        codeDO.setProcessOrchestrationVersion("v1");
        codeDO.setProcessOrchestrationNodeId(1L);
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Arrays.asList(codeDO));
        UnifiedOrderWithId order = new UnifiedOrderWithId();
        order.setMtUserId(100L);
        order.setUnifiedOrderId("UO123");
        when(unifiedOrderAclService.getOrderInfo(any())).thenReturn(order);
        CompletableFuture<ScrmProcessOrchestrationDTO> failedFuture = new CompletableFuture<>();
        failedFuture.completeExceptionally(new RuntimeException("DB error"));
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(any())).thenReturn(failedFuture);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        when(executeLogDOMapper.selectByExample(any())).thenReturn(Arrays.asList(executeLogDO));
        assertThrows(java.util.concurrent.CompletionException.class, () -> {
            informationGatheringService.dealCommunityOrder(operateNotify);
        });
    }

    @Test
    public void testDealCommunityOrderWhenOrderInfoIsNull() throws Throwable {
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        operateNotify.setOperateType(1);
        operateNotify.setOrderId("order123");
        Map<String, String> extInfo = new HashMap<>();
        extInfo.put("distributorId", "123");
        operateNotify.setExtInfo(extInfo);
        ScrmAmProcessOrchestrationDistributorCodeDO codeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        codeDO.setProcessOrchestrationId(1L);
        codeDO.setProcessOrchestrationVersion("v1");
        codeDO.setProcessOrchestrationNodeId(1L);
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Arrays.asList(codeDO));
        when(unifiedOrderAclService.getOrderInfo(any())).thenReturn(null);
        assertThrows(NullPointerException.class, () -> {
            informationGatheringService.dealCommunityOrder(operateNotify);
        });
    }

    @Test
    public void testDealCommunityOrderWhenNoInvokeDetailsFound() throws Throwable {
        // Prepare test data
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        operateNotify.setOperateType(1);
        operateNotify.setOrderId("order123");
        Map<String, String> extInfo = new HashMap<>();
        extInfo.put("distributorId", "123");
        operateNotify.setExtInfo(extInfo);
        // Setup distributor code
        ScrmAmProcessOrchestrationDistributorCodeDO codeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        codeDO.setProcessOrchestrationId(1L);
        codeDO.setProcessOrchestrationVersion("v1");
        codeDO.setProcessOrchestrationNodeId(1L);
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Arrays.asList(codeDO));
        // Setup order
        UnifiedOrderWithId order = new UnifiedOrderWithId();
        order.setMtUserId(100L);
        order.setUnifiedOrderId("UO123");
        when(unifiedOrderAclService.getOrderInfo(any())).thenReturn(order);
        // Setup process orchestration
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ACTION.getValue());
        nodeDTO.setNodeId(1L);
        nodeDTO.setProcessOrchestrationId(1L);
        nodeDTO.setProcessOrchestrationVersion("v1");
        processOrchestrationDTO.setNodeMediumDTO(new ScrmProcessOrchestrationNodeMediumDTO());
        processOrchestrationDTO.getNodeMediumDTO().addProcessOrchestrationNodeDTO(nodeDTO);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setActionType(ScrmProcessOrchestrationActionTypeEnum.COUPON_DISTRIBUTION.getValue().byteValue());
        actionDTO.setProcessOrchestrationNodeId(1L);
        processOrchestrationDTO.getNodeMediumDTO().addActionDTO(1L, actionDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(any())).thenReturn(CompletableFuture.completedFuture(processOrchestrationDTO));
        // Setup execute log
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDO.setTargetUnionId("union123");
        executeLogDO.setProcessOrchestrationId(1L);
        executeLogDO.setProcessOrchestrationVersion("v1");
        executeLogDO.setProcessOrchestrationNodeId(1L);
        when(executeLogDOMapper.selectByExample(any())).thenReturn(Arrays.asList(executeLogDO));
        // Setup invoke detail - return empty list to simulate no invoke details found
        when(invokeDetailDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // Execute test
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        // Verify results
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        // Verify logUserStatusChangeDetail was called with correct parameters
        verify(executeGoalDetailDOMapper).insert(argThat(goalDetail -> {
            ScrmAmProcessOrchestrationExecuteGoalDetailDO detail = (ScrmAmProcessOrchestrationExecuteGoalDetailDO) goalDetail;
            return detail.getProcessOrchestrationId().equals(1L) && detail.getProcessOrchestrationVersion().equals("v1") && detail.getProcessOrchestrationNodeId().equals(1L) && detail.getExecuteLogId().equals(1L) && detail.getGoalType() == ProcessOrchestrationInformationGatheringEnum.COUPON_USED.getGoalType();
        }));
        verify(supplyMarketingTextService).recycleUserTag(anyLong(), any());
        verify(relatedOrderInfoDOMapper).insert(any());
    }
}
