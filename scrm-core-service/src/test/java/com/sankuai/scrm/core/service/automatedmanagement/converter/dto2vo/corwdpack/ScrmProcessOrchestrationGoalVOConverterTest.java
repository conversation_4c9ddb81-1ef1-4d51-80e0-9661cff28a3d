package com.sankuai.scrm.core.service.automatedmanagement.converter.dto2vo.corwdpack;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationGoalDTO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationGoalConditionDetailVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationGoalVO;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2vo.corwdpack.ScrmProcessOrchestrationGoalConditionDetailVOConverter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmProcessOrchestrationGoalVOConverterTest {

    @InjectMocks
    private ScrmProcessOrchestrationGoalVOConverter converter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationGoalConditionDetailVOConverter scrmProcessOrchestrationGoalConditionDetailVOConverter;

    @Test
    public void testConvertToDTO_NullResource() {
        ScrmProcessOrchestrationGoalVO resource = null;
        List<ScrmProcessOrchestrationGoalDTO> result = converter.convertToDTO(resource);
        assertNull(result);
    }

    @Test
    public void testConvertToDTO_EmptyLists() {
        ScrmProcessOrchestrationGoalVO resource = new ScrmProcessOrchestrationGoalVO();
        List<ScrmProcessOrchestrationGoalDTO> result = converter.convertToDTO(resource);
        assertEquals(2, result.size());
        verify(scrmProcessOrchestrationGoalConditionDetailVOConverter, never()).convertToDTOsSafety(anyList());
    }

    @Test
    public void testConvertToDTO_NonEmptyGoalConditionList() {
        ScrmProcessOrchestrationGoalVO resource = new ScrmProcessOrchestrationGoalVO();
        resource.setGoalConditionList(Collections.singletonList(new ScrmProcessOrchestrationGoalConditionDetailVO()));
        List<ScrmProcessOrchestrationGoalDTO> result = converter.convertToDTO(resource);
        assertEquals(2, result.size());
        verify(scrmProcessOrchestrationGoalConditionDetailVOConverter, times(1)).convertToDTOsSafety(anyList());
    }

    @Test
    public void testConvertToDTO_NonEmptyNegativeResultConditionList() {
        ScrmProcessOrchestrationGoalVO resource = new ScrmProcessOrchestrationGoalVO();
        resource.setNegativeResultConditionList(Collections.singletonList(new ScrmProcessOrchestrationGoalConditionDetailVO()));
        List<ScrmProcessOrchestrationGoalDTO> result = converter.convertToDTO(resource);
        assertEquals(2, result.size());
        verify(scrmProcessOrchestrationGoalConditionDetailVOConverter, times(1)).convertToDTOsSafety(anyList());
    }

    @Test
    public void testConvertToDTO_NonEmptyLists() {
        ScrmProcessOrchestrationGoalVO resource = new ScrmProcessOrchestrationGoalVO();
        resource.setGoalConditionList(Collections.singletonList(new ScrmProcessOrchestrationGoalConditionDetailVO()));
        resource.setNegativeResultConditionList(Collections.singletonList(new ScrmProcessOrchestrationGoalConditionDetailVO()));
        List<ScrmProcessOrchestrationGoalDTO> result = converter.convertToDTO(resource);
        assertEquals(2, result.size());
        verify(scrmProcessOrchestrationGoalConditionDetailVOConverter, times(2)).convertToDTOsSafety(anyList());
    }

    @Test
    public void testConvertToDOWithNullResource() throws Throwable {
        ScrmProcessOrchestrationGoalVO result = converter.convertToDO(null);
        assertNull(result);
    }

    @Test
    public void testConvertToDOWithEmptyResource() throws Throwable {
        List<ScrmProcessOrchestrationGoalDTO> resource = new ArrayList<>();
        ScrmProcessOrchestrationGoalVO result = converter.convertToDO(resource);
        assertNull(result);
    }

    @Test
    public void testConvertToDOWithGoalType1And2() throws Throwable {
        List<ScrmProcessOrchestrationGoalDTO> resource = new ArrayList<>();
        ScrmProcessOrchestrationGoalDTO goalDTO1 = new ScrmProcessOrchestrationGoalDTO();
        goalDTO1.setGoalType((byte) 1);
        goalDTO1.setCareNegativeResult(true);
        goalDTO1.setNegativeResultHighlightList(new ArrayList<>());
        goalDTO1.setPositiveResultHighlightList(new ArrayList<>());
        resource.add(goalDTO1);
        ScrmProcessOrchestrationGoalDTO goalDTO2 = new ScrmProcessOrchestrationGoalDTO();
        goalDTO2.setGoalType((byte) 2);
        goalDTO2.setCareNegativeResult(false);
        goalDTO2.setNegativeResultHighlightList(new ArrayList<>());
        goalDTO2.setPositiveResultHighlightList(new ArrayList<>());
        resource.add(goalDTO2);
        ScrmProcessOrchestrationGoalVO result = converter.convertToDO(resource);
        assertNotNull(result);
        assertNotNull(result.getGoalConditionList());
        assertNotNull(result.getNegativeResultHighlightList());
        assertNotNull(result.getHighLightList());
    }

    @Test
    public void testConvertToDOWithNoGoalType2() throws Throwable {
        List<ScrmProcessOrchestrationGoalDTO> resource = new ArrayList<>();
        ScrmProcessOrchestrationGoalDTO goalDTO = new ScrmProcessOrchestrationGoalDTO();
        goalDTO.setGoalType((byte) 1);
        goalDTO.setCareNegativeResult(true);
        goalDTO.setNegativeResultHighlightList(new ArrayList<>());
        goalDTO.setPositiveResultHighlightList(new ArrayList<>());
        resource.add(goalDTO);
        ScrmProcessOrchestrationGoalVO result = converter.convertToDO(resource);
        assertNotNull(result);
        assertNotNull(result.getGoalConditionList());
        assertNotNull(result.getNegativeResultHighlightList());
        assertNotNull(result.getHighLightList());
    }
}
