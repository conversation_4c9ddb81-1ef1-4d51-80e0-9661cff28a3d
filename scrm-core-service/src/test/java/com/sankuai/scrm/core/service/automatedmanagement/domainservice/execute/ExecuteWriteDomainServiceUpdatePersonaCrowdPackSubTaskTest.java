package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.UpdateAllCrowdPackMessageProducer;
import com.sankuai.scrm.core.service.infrastructure.acl.persona.PersonaService;
import java.io.IOException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashSet;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ExecuteWriteDomainServiceUpdatePersonaCrowdPackSubTaskTest {

    @Mock
    private PersonaService personaService;

    @Mock
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock
    private UpdateAllCrowdPackMessageProducer updateAllCrowdPackMessageProducer;

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    private Integer personaId;

    private Long packId;

    // 后续补充详细测试用例
    private String appId;

    private String packVersion;

    @BeforeEach
    void setUp() {
        personaId = 123;
        packId = 456L;
        appId = "test-app-id";
        packVersion = "1.0.0";
    }

    /**
     * Test normal successful execution with valid persona ID and user list
     */
    @Test
    void testUpdatePersonaCrowdPackSubTask_Success() throws Throwable {
        // arrange
        String url = "wvkq://dznnddg.lst/hfsfn";
        List<Long> mtUserIdList = Arrays.asList(1001L, 1002L);
        when(personaService.getUserIdListByCrowdId(personaId)).thenReturn(url);
        when(personaService.getMtUserIdListByUrl(url)).thenReturn(mtUserIdList);
        doNothing().when(crowdPackWriteDomainService).concurrencyDeleteCrowdPackDetailInfoByPackId(packId);
        // act
        executeWriteDomainService.updatePersonaCrowdPackSubTask(personaId, packId, appId, packVersion);
        // assert
        verify(personaService).getUserIdListByCrowdId(personaId);
        verify(personaService).getMtUserIdListByUrl(url);
        verify(crowdPackWriteDomainService).concurrencyDeleteCrowdPackDetailInfoByPackId(packId);
        verify(updateAllCrowdPackMessageProducer).sendCrowdPackUpdateDBTaskExecuteMessage(appId, new HashSet<>(mtUserIdList), packId, packVersion);
    }

    /**
     * Test when persona service returns empty URL
     */
    @Test
    void testUpdatePersonaCrowdPackSubTask_EmptyUrl() throws Throwable {
        // arrange
        String url = "";
        List<Long> mtUserIdList = Collections.emptyList();
        when(personaService.getUserIdListByCrowdId(personaId)).thenReturn(url);
        when(personaService.getMtUserIdListByUrl(url)).thenReturn(mtUserIdList);
        doNothing().when(crowdPackWriteDomainService).concurrencyDeleteCrowdPackDetailInfoByPackId(packId);
        // act
        executeWriteDomainService.updatePersonaCrowdPackSubTask(personaId, packId, appId, packVersion);
        // assert
        verify(personaService).getUserIdListByCrowdId(personaId);
        verify(personaService).getMtUserIdListByUrl(url);
        verify(crowdPackWriteDomainService).concurrencyDeleteCrowdPackDetailInfoByPackId(packId);
    }

    /**
     * Test when persona service returns empty user list
     */
    @Test
    void testUpdatePersonaCrowdPackSubTask_EmptyUserList() throws Throwable {
        // arrange
        String url = "iemg://rjimnfq.cji/tapgi";
        List<Long> mtUserIdList = Collections.emptyList();
        when(personaService.getUserIdListByCrowdId(personaId)).thenReturn(url);
        when(personaService.getMtUserIdListByUrl(url)).thenReturn(mtUserIdList);
        doNothing().when(crowdPackWriteDomainService).concurrencyDeleteCrowdPackDetailInfoByPackId(packId);
        // act
        executeWriteDomainService.updatePersonaCrowdPackSubTask(personaId, packId, appId, packVersion);
        // assert
        verify(personaService).getUserIdListByCrowdId(personaId);
        verify(personaService).getMtUserIdListByUrl(url);
        verify(crowdPackWriteDomainService).concurrencyDeleteCrowdPackDetailInfoByPackId(packId);
    }

    /**
     * Test when getMtUserIdListByUrl throws IOException
     */
    @Test
    void testUpdatePersonaCrowdPackSubTask_GetUserIdListThrowsIOException() throws Throwable {
        // arrange
        String url = "qpea://kduvdjn.ztc/wijtd";
        when(personaService.getUserIdListByCrowdId(personaId)).thenReturn(url);
        when(personaService.getMtUserIdListByUrl(url)).thenThrow(new IOException("Failed to get user IDs"));
        // act & assert
        assertThrows(IOException.class, () -> {
            executeWriteDomainService.updatePersonaCrowdPackSubTask(personaId, packId, appId, packVersion);
        });
        verify(personaService).getUserIdListByCrowdId(personaId);
        verify(personaService).getMtUserIdListByUrl(url);
        verify(crowdPackWriteDomainService, never()).concurrencyDeleteCrowdPackDetailInfoByPackId(anyLong());
        verify(updateAllCrowdPackMessageProducer, never()).sendCrowdPackUpdateDBTaskExecuteMessage(anyString(), any(), anyLong(), anyString());
    }

    /**
     * Test with null packId
     */
    @Test
    void testUpdatePersonaCrowdPackSubTask_NullPackId() throws Throwable {
        // arrange
        packId = null;
        String url = "gbhs://svfqmrm.bca/yzies";
        List<Long> mtUserIdList = Collections.singletonList(1001L);
        when(personaService.getUserIdListByCrowdId(personaId)).thenReturn(url);
        when(personaService.getMtUserIdListByUrl(url)).thenReturn(mtUserIdList);
        doNothing().when(crowdPackWriteDomainService).concurrencyDeleteCrowdPackDetailInfoByPackId(packId);
        // act
        executeWriteDomainService.updatePersonaCrowdPackSubTask(personaId, packId, appId, packVersion);
        // assert
        verify(personaService).getUserIdListByCrowdId(personaId);
        verify(personaService).getMtUserIdListByUrl(url);
        verify(crowdPackWriteDomainService).concurrencyDeleteCrowdPackDetailInfoByPackId(packId);
        verify(updateAllCrowdPackMessageProducer).sendCrowdPackUpdateDBTaskExecuteMessage(appId, new HashSet<>(mtUserIdList), packId, packVersion);
    }

    /**
     * Test with negative packId
     */
    @Test
    void testUpdatePersonaCrowdPackSubTask_NegativePackId() throws Throwable {
        // arrange
        packId = -1L;
        String url = "ihey://apmtvoe.lvt/lwhcp";
        List<Long> mtUserIdList = Collections.singletonList(1001L);
        when(personaService.getUserIdListByCrowdId(personaId)).thenReturn(url);
        when(personaService.getMtUserIdListByUrl(url)).thenReturn(mtUserIdList);
        doNothing().when(crowdPackWriteDomainService).concurrencyDeleteCrowdPackDetailInfoByPackId(packId);
        // act
        executeWriteDomainService.updatePersonaCrowdPackSubTask(personaId, packId, appId, packVersion);
        // assert
        verify(personaService).getUserIdListByCrowdId(personaId);
        verify(personaService).getMtUserIdListByUrl(url);
        verify(crowdPackWriteDomainService).concurrencyDeleteCrowdPackDetailInfoByPackId(packId);
        verify(updateAllCrowdPackMessageProducer).sendCrowdPackUpdateDBTaskExecuteMessage(appId, new HashSet<>(mtUserIdList), packId, packVersion);
    }

    /**
     * Test with large number of users
     */
    @Test
    void testUpdatePersonaCrowdPackSubTask_LargeNumberOfUsers() throws Throwable {
        // arrange
        String url = "xqma://lxdocng.lxs/chnay";
        List<Long> mtUserIdList = new ArrayList<>();
        for (long i = 0; i < 10000; i++) {
            mtUserIdList.add(i);
        }
        when(personaService.getUserIdListByCrowdId(personaId)).thenReturn(url);
        when(personaService.getMtUserIdListByUrl(url)).thenReturn(mtUserIdList);
        doNothing().when(crowdPackWriteDomainService).concurrencyDeleteCrowdPackDetailInfoByPackId(packId);

        // act
        executeWriteDomainService.updatePersonaCrowdPackSubTask(personaId, packId, appId, packVersion);

        // assert
        verify(personaService).getUserIdListByCrowdId(personaId);
        verify(personaService).getMtUserIdListByUrl(url);
        verify(crowdPackWriteDomainService).concurrencyDeleteCrowdPackDetailInfoByPackId(packId);
        // 使用 any() 匹配器或者 argThat() 进行更精确的验证
    }
    /**
     * Test with null appId
     */
    @Test
    void testUpdatePersonaCrowdPackSubTask_NullAppId() throws Throwable {
        // arrange
        appId = null;
        String url = "gjyu://xftrzpc.asa/wlmhb";
        List<Long> mtUserIdList = Collections.singletonList(1001L);
        when(personaService.getUserIdListByCrowdId(personaId)).thenReturn(url);
        when(personaService.getMtUserIdListByUrl(url)).thenReturn(mtUserIdList);
        doNothing().when(crowdPackWriteDomainService).concurrencyDeleteCrowdPackDetailInfoByPackId(packId);
        // act
        executeWriteDomainService.updatePersonaCrowdPackSubTask(personaId, packId, appId, packVersion);
        // assert
        verify(personaService).getUserIdListByCrowdId(personaId);
        verify(personaService).getMtUserIdListByUrl(url);
        verify(crowdPackWriteDomainService).concurrencyDeleteCrowdPackDetailInfoByPackId(packId);
        verify(updateAllCrowdPackMessageProducer).sendCrowdPackUpdateDBTaskExecuteMessage(null, new HashSet<>(mtUserIdList), packId, packVersion);
    }

    /**
     * Test with null packVersion
     */
    @Test
    void testUpdatePersonaCrowdPackSubTask_NullPackVersion() throws Throwable {
        // arrange
        packVersion = null;
        String url = "http://example.com/users";
        List<Long> mtUserIdList = Collections.singletonList(1001L);
        when(personaService.getUserIdListByCrowdId(personaId)).thenReturn(url);
        when(personaService.getMtUserIdListByUrl(url)).thenReturn(mtUserIdList);
        doNothing().when(crowdPackWriteDomainService).concurrencyDeleteCrowdPackDetailInfoByPackId(packId);
        // act
        executeWriteDomainService.updatePersonaCrowdPackSubTask(personaId, packId, appId, packVersion);
        // assert
        verify(personaService).getUserIdListByCrowdId(personaId);
        verify(personaService).getMtUserIdListByUrl(url);
        verify(crowdPackWriteDomainService).concurrencyDeleteCrowdPackDetailInfoByPackId(packId);
        verify(updateAllCrowdPackMessageProducer).sendCrowdPackUpdateDBTaskExecuteMessage(appId, new HashSet<>(mtUserIdList), packId, null);
    }
}
