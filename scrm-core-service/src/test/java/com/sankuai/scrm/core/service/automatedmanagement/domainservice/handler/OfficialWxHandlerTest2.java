package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerTest2 {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    /**
     * 测试空的invokeDetailDOS和null的taskId
     */
    @Test
    public void testUpdateLogStatus_EmptyInvokeDetailsAndNullTaskId() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> invokeDetailDOS = Collections.emptyList();
        Long taskId = null;
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "1.0";
        Long processOrchestrationNodeId = 1L;

        // act
        Method updateLogStatus = officialWxHandler.getClass().getDeclaredMethod("updateLogStatus", List.class, Long.class, Long.class, String.class, Long.class);
        updateLogStatus.setAccessible(true);
        updateLogStatus.invoke(officialWxHandler, invokeDetailDOS, taskId, processOrchestrationId, processOrchestrationVersion, processOrchestrationNodeId);

        // assert
        verify(wxInvokeLogDOMapper, never()).selectByExample(any());
    }

    /**
     * 测试未找到对应的WxInvokeLogDO
     */
    @Test
    public void testUpdateLogStatus_NoWxInvokeLogFound() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> invokeDetailDOS = Arrays.asList(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        Long taskId = 1L;
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "1.0";
        Long processOrchestrationNodeId = 1L;
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());

        // act
        Method updateLogStatus = officialWxHandler.getClass().getDeclaredMethod("updateLogStatus", List.class, Long.class, Long.class, String.class, Long.class);
        updateLogStatus.setAccessible(true);
        updateLogStatus.invoke(officialWxHandler, invokeDetailDOS, taskId, processOrchestrationId, processOrchestrationVersion, processOrchestrationNodeId);

        // assert
        verify(wxInvokeDetailDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * 测试正常更新日志状态
     */
    @Test
    public void testUpdateLogStatus_NormalUpdate() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setId(1L);
        detailDO.setExecuteLogId(1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> invokeDetailDOS = Lists.newArrayList(detailDO);
        Long taskId = 1L;
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "1.0";
        Long processOrchestrationNodeId = 1L;
        ScrmAmProcessOrchestrationWxInvokeLogDO logDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        logDO.setId(1L);
        logDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.CREATED_FAILED.getValue().byteValue());
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(logDO));

        // act
        Method updateLogStatus = officialWxHandler.getClass().getDeclaredMethod("updateLogStatus", List.class, Long.class, Long.class, String.class, Long.class);
        updateLogStatus.setAccessible(true);
        updateLogStatus.invoke(officialWxHandler, invokeDetailDOS, taskId, processOrchestrationId, processOrchestrationVersion, processOrchestrationNodeId);

        // assert
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(), any());
    }


}
