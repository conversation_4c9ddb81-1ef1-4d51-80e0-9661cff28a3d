package com.sankuai.scrm.core.service.infrastructure.acl.poi;

import com.sankuai.sinai.data.api.dto.DpPoiBackCategoryDTO;
import com.sankuai.sinai.data.api.dto.DpPoiDTO;
import com.sankuai.sinai.data.api.dto.DpPoiRequest;
import com.sankuai.sinai.data.api.service.DpPoiService;
import com.google.common.collect.Lists;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;

import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class PoiCategoryAclServiceTest {

    @InjectMocks
    private PoiCategoryAclService poiCategoryAclService;

    @Mock
    private DpPoiService dpPoiService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试dpSopId为0时，应返回null
     */
    @Test
    public void testGetBackMainCategoryPath_WhenDpSopIdIsZero() throws Exception {
        // arrange
        long dpSopId = 0;

        // act
        DpPoiBackCategoryDTO result = poiCategoryAclService.getBackMainCategoryPath(dpSopId);

        // assert
        assertNull(result);
    }

    /**
     * 测试dpPoiService返回空列表时，应返回null
     */
    @Test
    public void testGetBackMainCategoryPath_WhenDpPoiServiceReturnsEmptyList() throws Exception {
        // arrange
        long dpSopId = 1;
        when(dpPoiService.findShopsByShopIds(any(DpPoiRequest.class))).thenReturn(Collections.emptyList());

        // act
        DpPoiBackCategoryDTO result = poiCategoryAclService.getBackMainCategoryPath(dpSopId);

        // assert
        assertNull(result);
    }

    /**
     * 测试dpPoiService返回的列表中第一个元素为null时，应返回null
     */
    @Test
    public void testGetBackMainCategoryPath_WhenFirstElementInListIsNull() throws Exception {
        // arrange
        long dpSopId = 1;
        when(dpPoiService.findShopsByShopIds(any(DpPoiRequest.class))).thenReturn(Collections.singletonList(null));

        // act
        DpPoiBackCategoryDTO result = poiCategoryAclService.getBackMainCategoryPath(dpSopId);

        // assert
        assertNull(result);
    }

    /**
     * 测试dpPoiDTO的backMainCategoryPath为空时，应返回null
     */
    @Test
    public void testGetBackMainCategoryPath_WhenBackMainCategoryPathIsEmpty() throws Exception {
        // arrange
        long dpSopId = 1;
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        dpPoiDTO.setBackMainCategoryPath(Collections.emptyList());
        when(dpPoiService.findShopsByShopIds(any(DpPoiRequest.class))).thenReturn(Collections.singletonList(dpPoiDTO));

        // act
        DpPoiBackCategoryDTO result = poiCategoryAclService.getBackMainCategoryPath(dpSopId);

        // assert
        assertNull(result);
    }

    /**
     * 测试正常情况下，能够返回正确的DpPoiBackCategoryDTO
     */
    @Test
    public void testGetBackMainCategoryPath_WhenNormal() throws Exception {
        // arrange
        long dpSopId = 1;
        DpPoiDTO dpPoiDTO = new DpPoiDTO();
        DpPoiBackCategoryDTO expectedCategory = new DpPoiBackCategoryDTO();
        expectedCategory.setCategoryLevel(2);
        dpPoiDTO.setBackMainCategoryPath(Collections.singletonList(expectedCategory));
        when(dpPoiService.findShopsByShopIds(any(DpPoiRequest.class))).thenReturn(Collections.singletonList(dpPoiDTO));

        // act
        DpPoiBackCategoryDTO result = poiCategoryAclService.getBackMainCategoryPath(dpSopId);

        // assert
        assertNotNull(result);
        assertEquals(expectedCategory, result);
    }

    /**
     * 测试当方法抛出异常时，应返回null
     */
    @Test
    public void testGetBackMainCategoryPath_WhenExceptionOccurs() throws Exception {
        // arrange
        long dpSopId = 1;
        when(dpPoiService.findShopsByShopIds(any(DpPoiRequest.class))).thenThrow(new RuntimeException());

        // act
        DpPoiBackCategoryDTO result = poiCategoryAclService.getBackMainCategoryPath(dpSopId);

        // assert
        assertNull(result);
    }
}
