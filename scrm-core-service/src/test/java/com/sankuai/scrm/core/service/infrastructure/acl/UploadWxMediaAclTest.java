package com.sankuai.scrm.core.service.infrastructure.acl;

import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import java.lang.reflect.Field;
import java.util.Set;
import static org.junit.Assert.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UploadWxMediaAclTest {

    private static Set<String> originalBucketSet;

    @SuppressWarnings("unchecked")
    private static Set<String> getStaticFieldValue(String fieldName) throws Exception {
        Field field = UploadWxMediaAcl.class.getDeclaredField(fieldName);
        field.setAccessible(true);
        return (Set<String>) field.get(null);
    }

    /**
     * 测试init方法，期望BUCKETSET包含yzs, wed, baby, home
     */
    @Test
    public void testInit() throws Throwable {
        // arrange
        UploadWxMediaAcl uploadWxMediaAcl = new UploadWxMediaAcl();
        // act
        uploadWxMediaAcl.init();
        // assert
        Set<String> bucketSet = getStaticFieldValue("BUCKETSET");
        assertTrue(bucketSet.contains("yzs"));
        assertTrue(bucketSet.contains("wed"));
        assertTrue(bucketSet.contains("baby"));
        assertTrue(bucketSet.contains("home"));
    }
}
