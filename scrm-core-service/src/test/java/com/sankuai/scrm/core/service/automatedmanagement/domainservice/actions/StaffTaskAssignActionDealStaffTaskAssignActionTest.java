package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationActionExecutionPathEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationActionSubTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.ExecuteMediumDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ConcurrentSkipListSet;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class StaffTaskAssignActionDealStaffTaskAssignActionTest {

    @InjectMocks
    private StaffTaskAssignAction staffTaskAssignAction;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ExecuteManagementDTO mediumManagementDTO;

    private ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO;

    private ScrmProcessOrchestrationNodeDTO currentProcessingNode;

    private ScrmAmProcessOrchestrationExecuteLogDO existedExecuteLogDO;

    public StaffTaskAssignActionDealStaffTaskAssignActionTest() {
        MockitoAnnotations.initMocks(this);
        // Initialize common test data
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        processOrchestrationDTO.setAppId("testApp");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(1L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);
        // Initialize mediumManagementDTO with required fields
        mediumManagementDTO = new ExecuteManagementDTO();
        mediumManagementDTO.setStaffLimitSet(new HashSet<>(Arrays.asList("staff1")));
        // Initialize alternateExecuteLogIdSet
        mediumManagementDTO.setAlternateExecuteLogIdSet(new ConcurrentSkipListSet<>());
        // Initialize staffTaskCounts
        mediumManagementDTO.setStaffTaskCounts(new ConcurrentHashMap<>());
        // Initialize nodeExecuteLogDTOMap
        mediumManagementDTO.setNodeExecuteLogDTOMap(new ConcurrentHashMap<>());
        scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        scrmCrowdPackDetailInfoDTO.setExternalUserId(1L);
        scrmCrowdPackDetailInfoDTO.setExternalUserWxUnionId("unionId1");
        currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setNodeId(1L);
    }

    @Test
    public void testDealStaffTaskAssignAction_ExistedExecuteLogDONotNull() throws Throwable {
        existedExecuteLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        List<MemberInfoEntity> memberInfoList = new ArrayList<>();
        MemberInfoEntity memberInfo = new MemberInfoEntity();
        memberInfo.setGroupId("group1");
        memberInfoList.add(memberInfo);
        when(executeManagementService.getUsersGroupList(any(), any())).thenReturn(memberInfoList);
        when(executeManagementService.getStaffListByGroupId(any())).thenReturn(memberInfoList);
        StepExecuteResultDTO result = staffTaskAssignAction.dealStaffTaskAssignAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        assertNotNull(result);
        verify(executeLogDOMapper, never()).selectByExample(any());
    }

    @Test
    public void testDealStaffTaskAssignAction_NoHistoricalLogs_ParticipationRestricted() throws Throwable {
        processOrchestrationDTO.setParticipationRestrict(true);
        when(executeLogDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        List<MemberInfoEntity> memberInfoList = new ArrayList<>();
        MemberInfoEntity memberInfo = new MemberInfoEntity();
        memberInfo.setGroupId("group1");
        memberInfoList.add(memberInfo);
        when(executeManagementService.getUsersGroupList(any(), any())).thenReturn(memberInfoList);
        when(executeManagementService.getStaffListByGroupId(any())).thenReturn(memberInfoList);
        StepExecuteResultDTO result = staffTaskAssignAction.dealStaffTaskAssignAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        assertNotNull(result);
        verify(executeLogDOMapper).selectByExample(any());
    }

    @Test
    public void testDealStaffTaskAssignAction_HistoricalLogsExist_ParticipationRestricted() throws Throwable {
        processOrchestrationDTO.setParticipationRestrict(true);
        List<ScrmAmProcessOrchestrationExecuteLogDO> historicalLogs = new ArrayList<>();
        ScrmAmProcessOrchestrationExecuteLogDO log = new ScrmAmProcessOrchestrationExecuteLogDO();
        // Ensure the log has an ID
        log.setId(1L);
        historicalLogs.add(log);
        when(executeLogDOMapper.selectByExample(any())).thenReturn(historicalLogs);
        StepExecuteResultDTO result = staffTaskAssignAction.dealStaffTaskAssignAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        assertNotNull(result);
        assertFalse(result.isSuccess());
    }

    @Test
    public void testDealStaffTaskAssignAction_NoUserGroups() throws Throwable {
        when(executeManagementService.getUsersGroupList(any(), any())).thenReturn(new ArrayList<>());
        StepExecuteResultDTO result = staffTaskAssignAction.dealStaffTaskAssignAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        assertNotNull(result);
        assertFalse(result.isSuccess());
    }

    @Test
    public void testDealStaffTaskAssignAction_NoStaffInGroup() throws Throwable {
        List<MemberInfoEntity> memberInfoList = new ArrayList<>();
        MemberInfoEntity memberInfo = new MemberInfoEntity();
        memberInfo.setGroupId("group1");
        memberInfoList.add(memberInfo);
        when(executeManagementService.getUsersGroupList(any(), any())).thenReturn(memberInfoList);
        when(executeManagementService.getStaffListByGroupId(any())).thenReturn(new ArrayList<>());
        StepExecuteResultDTO result = staffTaskAssignAction.dealStaffTaskAssignAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        assertNotNull(result);
        assertFalse(result.isSuccess());
    }

    @Test
    public void testDealStaffTaskAssignAction_ParticipationRestrictionWithCycle() throws Throwable {
        processOrchestrationDTO.setParticipationRestrict(false);
        processOrchestrationDTO.setParticipationRestrictionsCycle((byte) 2);
        processOrchestrationDTO.setParticipationRestrictionsTimes((byte) 3);
        List<ScrmAmProcessOrchestrationExecuteLogDO> historicalLogs = new ArrayList<>();
        ScrmAmProcessOrchestrationExecuteLogDO log = new ScrmAmProcessOrchestrationExecuteLogDO();
        // Ensure the log has an ID
        log.setId(1L);
        historicalLogs.add(log);
        when(executeLogDOMapper.selectByExample(any())).thenReturn(historicalLogs);
        StepExecuteResultDTO result = staffTaskAssignAction.dealStaffTaskAssignAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        assertNotNull(result);
        verify(executeLogDOMapper).selectByExample(any());
    }

    /**
     * Test case: Normal successful execution path
     */
    @Test
    public void testDealStaffTaskAssignActionSub_Success() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        processOrchestrationDTO.setAppId("testApp");
        ScrmProcessOrchestrationNodeDTO currentNode = new ScrmProcessOrchestrationNodeDTO();
        currentNode.setNodeId(100L);
        ExecuteManagementDTO mediumManagementDTO = mock(ExecuteManagementDTO.class);
        Set<String> staffLimitSet = new HashSet<>();
        staffLimitSet.add("staff1");
        when(mediumManagementDTO.getStaffLimitSet()).thenReturn(staffLimitSet);
        when(mediumManagementDTO.tryAddExecute(any(ExecuteMediumDTO.class))).thenReturn("staff1");
        List<MemberInfoEntity> memberList = new ArrayList<>();
        MemberInfoEntity memberInfo = new MemberInfoEntity();
        memberInfo.setGroupId("group1");
        memberInfo.setGroupMemberId("member1");
        memberInfo.setMemberName("Test Member");
        memberInfo.setAvatar("avatar.jpg");
        memberList.add(memberInfo);
        List<MemberInfoEntity> staffList = new ArrayList<>();
        MemberInfoEntity staffInfo = new MemberInfoEntity();
        staffInfo.setGroupMemberId("staff1");
        staffInfo.setGroupId("group1");
        staffList.add(staffInfo);
        when(executeManagementService.getUsersGroupList(eq("unionId"), eq("testApp"))).thenReturn(memberList);
        when(executeManagementService.getStaffListByGroupId(anyList())).thenReturn(staffList);
        when(executeManagementService.getMtUserIdByUnionId(anyString())).thenReturn(123L);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setActionSubType(ScrmProcessOrchestrationActionSubTypeEnum.ADD_FRIEND.getValue());
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        nodeMediumDTO.addActionDTO(currentNode.getNodeId(), actionDTO);
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(1L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);
        // act
        StepExecuteResultDTO result = staffTaskAssignAction.dealStaffTaskAssignActionSub(processOrchestrationDTO, 1L, "unionId", currentNode, mediumManagementDTO, null);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertEquals(processOrchestrationDTO.getId(), result.getProcessOrchestrationId());
        assertEquals(currentNode.getNodeId(), result.getProcessOrchestrationNodeId());
    }

    /**
     * Test case: Failure when no user groups found
     */
    @Test
    public void testDealStaffTaskAssignActionSub_NoUserGroups() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testApp");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(1L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);
        when(executeManagementService.getUsersGroupList(eq("unionId"), eq("testApp"))).thenReturn(Collections.emptyList());
        // act
        StepExecuteResultDTO result = staffTaskAssignAction.dealStaffTaskAssignActionSub(processOrchestrationDTO, 1L, "unionId", new ScrmProcessOrchestrationNodeDTO(), new ExecuteManagementDTO(), null);
        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
    }

    /**
     * Test case: Failure when no valid staff found
     */
    @Test
    public void testDealStaffTaskAssignActionSub_NoValidStaff() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testApp");
        List<MemberInfoEntity> memberList = new ArrayList<>();
        MemberInfoEntity memberInfo = new MemberInfoEntity();
        memberInfo.setGroupId("group1");
        memberList.add(memberInfo);
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(1L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);
        when(executeManagementService.getUsersGroupList(eq("unionId"), eq("testApp"))).thenReturn(memberList);
        when(executeManagementService.getStaffListByGroupId(anyList())).thenReturn(Collections.emptyList());
        // act
        StepExecuteResultDTO result = staffTaskAssignAction.dealStaffTaskAssignActionSub(processOrchestrationDTO, 1L, "unionId", new ScrmProcessOrchestrationNodeDTO(), new ExecuteManagementDTO(), null);
        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
    }

    /**
     * Test case: Test with existing execute log
     */
    @Test
    public void testDealStaffTaskAssignActionSub_WithExistingLog() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationExecuteLogDO existingLog = new ScrmAmProcessOrchestrationExecuteLogDO();
        existingLog.setId(1L);
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(1L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        processOrchestrationDTO.setAppId("testApp");
        ExecuteManagementDTO mediumManagementDTO = mock(ExecuteManagementDTO.class);
        Set<String> staffLimitSet = new HashSet<>();
        staffLimitSet.add("staff1");
        when(mediumManagementDTO.getStaffLimitSet()).thenReturn(staffLimitSet);
        when(mediumManagementDTO.tryAddExecute(any(ExecuteMediumDTO.class))).thenReturn("staff1");
        List<MemberInfoEntity> memberList = new ArrayList<>();
        MemberInfoEntity memberInfo = new MemberInfoEntity();
        memberInfo.setGroupId("group1");
        memberInfo.setGroupMemberId("member1");
        memberInfo.setMemberName("Test Member");
        memberInfo.setAvatar("avatar.jpg");
        memberList.add(memberInfo);
        List<MemberInfoEntity> staffList = new ArrayList<>();
        MemberInfoEntity staffInfo = new MemberInfoEntity();
        staffInfo.setGroupMemberId("staff1");
        staffInfo.setGroupId("group1");
        staffList.add(staffInfo);
        when(executeManagementService.getUsersGroupList(eq("unionId"), eq("testApp"))).thenReturn(memberList);
        when(executeManagementService.getStaffListByGroupId(anyList())).thenReturn(staffList);
        when(executeManagementService.getMtUserIdByUnionId(anyString())).thenReturn(123L);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setActionSubType(ScrmProcessOrchestrationActionSubTypeEnum.ADD_FRIEND.getValue());
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        // Match the node ID
        nodeMediumDTO.addActionDTO(100L, actionDTO);
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        ScrmProcessOrchestrationNodeDTO currentNode = new ScrmProcessOrchestrationNodeDTO();
        // Ensure this matches the actionDTO node ID
        currentNode.setNodeId(100L);
        // act
        StepExecuteResultDTO result = staffTaskAssignAction.dealStaffTaskAssignActionSub(processOrchestrationDTO, 1L, "unionId", currentNode, mediumManagementDTO, existingLog);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(executeLogDOMapper).updateByPrimaryKey(any(ScrmAmProcessOrchestrationExecuteLogDO.class));
    }
}
