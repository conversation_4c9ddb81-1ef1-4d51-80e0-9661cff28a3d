package com.sankuai.scrm.core.service.automatedmanagement.mq.consumer;

import static org.mockito.Mockito.*;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackUserAddCropTagDomainService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class ScrmPackUserBatchAddCorpTagConsumerRecvMessageTest {

    @InjectMocks
    private ScrmPackUserBatchAddCorpTagConsumer scrmPackUserBatchAddCorpTagConsumer;

    @Mock
    private CrowdPackUserAddCropTagDomainService crowdPackUserAddCropTagDomainService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试正常场景
     */
    @Test
    public void testRecvMessageNormal() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext messagetContext = mock(MessagetContext.class);
        when(message.getBody()).thenReturn("test message");
        // act
        ConsumeStatus result = scrmPackUserBatchAddCorpTagConsumer.recvMessage(message, messagetContext);
        // assert
        verify(crowdPackUserAddCropTagDomainService, times(1)).handleCrowdPackUserBatchAddCorpTagMessage(anyString());
        assert (result == ConsumeStatus.CONSUME_SUCCESS);
    }

    /**
     * 测试异常场景
     */
    @Test
    public void testRecvMessageException() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext messagetContext = mock(MessagetContext.class);
        when(message.getBody()).thenReturn("test message");
        doThrow(new RuntimeException()).when(crowdPackUserAddCropTagDomainService).handleCrowdPackUserBatchAddCorpTagMessage(anyString());
        // act
        ConsumeStatus result = scrmPackUserBatchAddCorpTagConsumer.recvMessage(message, messagetContext);
        // assert
        verify(crowdPackUserAddCropTagDomainService, times(1)).handleCrowdPackUserBatchAddCorpTagMessage(anyString());
        assert (result == ConsumeStatus.CONSUME_SUCCESS);
    }
}
