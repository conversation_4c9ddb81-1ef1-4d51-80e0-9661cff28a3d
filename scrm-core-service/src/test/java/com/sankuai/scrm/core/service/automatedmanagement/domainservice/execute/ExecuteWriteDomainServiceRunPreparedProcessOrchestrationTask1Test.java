package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecutePlanPackStatusEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration.ScrmProcessOrchestrationExecutePlanConverter;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackAndProcessMapDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackBaseInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutePlanDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackAndProcessMapDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecutePlanDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackAndProcessMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackBaseInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationExecutePlanDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackUpdateLockService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.RefinementOperationExecuteMessageProducer;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ExecuteWriteDomainServiceRunPreparedProcessOrchestrationTask1Test {

    // 后续补充详细测试用例
    @Mock
    private ExtScrmAmProcessOrchestrationExecutePlanDOMapper executePlanDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock
    private ScrmAmCrowdPackAndProcessMapDOMapper crowdPackAndProcessMapDOMapper;

    @Mock
    private ScrmAmCrowdPackBaseInfoDOMapper scrmAmCrowdPackBaseInfoDOMapper;

    @Mock
    private ScrmProcessOrchestrationExecutePlanConverter scrmProcessOrchestrationExecutePlanConverter;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock
    private CrowdPackUpdateLockService crowdPackUpdateLockService;

    @Mock
    private RefinementOperationExecuteMessageProducer refinementOperationExecuteMessageProducer;

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    private ScrmAmProcessOrchestrationExecutePlanDO planDO;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ScrmAmProcessOrchestrationInfoDO infoDO;

    @BeforeEach
    void setUp() {
        planDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        planDO.setId(1L);
        planDO.setProcessOrchestrationId(1L);
        planDO.setProcessOrchestrationVersion("1.0");
        planDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_TIMED_TASK.getValue().byteValue());
        planDO.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.PREPARING.getValue().byteValue());
        planDO.setPackStatus((byte) 0);
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setValidVersion("1.0");
        infoDO.setAppId("test-app-id");
    }

    /**
     * Tests when no prepared tasks are available
     */
    @Test
    public void testRunPreparedProcessOrchestrationTaskNoTasksAvailable() throws Throwable {
        // arrange
        when(executePlanDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        executeWriteDomainService.runPreparedProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, atLeastOnce()).selectByExample(any());
        verify(processOrchestrationReadDomainService, never()).queryProcessOrchestrationDetailFuture(anyLong());
    }

    /**
     * Tests successful execution of a prepared task
     */
    @Test
    public void testRunPreparedProcessOrchestrationTaskSuccess() throws Throwable {
        // arrange
        // Mock for updateCrowdPackExecuteTwoHoursLater
        when(executePlanDOMapper.selectByExample(any())).thenReturn(Collections.emptyList()).thenReturn(Collections.singletonList(planDO));
        CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(processOrchestrationDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(anyLong())).thenReturn(future);
        when(executePlanDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // Create a partial mock to avoid calling the actual runProcessOrchestrationTask
        ExecuteWriteDomainService partialMock = spy(executeWriteDomainService);
        doNothing().when(partialMock).runProcessOrchestrationTask(any());
        // act
        partialMock.runPreparedProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, atLeastOnce()).selectByExample(any());
        verify(processOrchestrationReadDomainService, atLeastOnce()).queryProcessOrchestrationDetailFuture(anyLong());
        verify(executePlanDOMapper).updateByExampleSelective(any(), any());
        verify(partialMock).runProcessOrchestrationTask(any());
    }

    /**
     * Tests when crowd pack update is needed
     */
    @Test
    public void testRunPreparedProcessOrchestrationTaskWithCrowdPackUpdate() throws Throwable {
        // arrange
        // Setup for updateCrowdPackExecuteTwoHoursLater
        Calendar twoHoursLater = Calendar.getInstance();
        twoHoursLater.add(Calendar.HOUR_OF_DAY, 2);
        ScrmAmProcessOrchestrationExecutePlanDO updatePlanDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        updatePlanDO.setId(2L);
        updatePlanDO.setProcessOrchestrationId(2L);
        updatePlanDO.setProcessOrchestrationVersion("1.0");
        updatePlanDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_TIMED_TASK.getValue().byteValue());
        updatePlanDO.setPackStatus(ScrmProcessOrchestrationExecutePlanPackStatusEnum.UPDATE_NOT_EXECUTED.getCode().byteValue());
        updatePlanDO.setTaskStartTime(new Date());
        // First call for updateCrowdPackExecuteTwoHoursLater, second for getPreparedExecuteScrmProcessOrchestrationDTO
        when(executePlanDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(updatePlanDO)).thenReturn(Collections.singletonList(planDO));
        // Setup for updateCrowdPackExecuteTwoHoursLater
        lenient().when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(anyLong())).thenReturn(infoDO);
        ScrmAmCrowdPackAndProcessMapDO mapDO = new ScrmAmCrowdPackAndProcessMapDO();
        mapDO.setPackId(10L);
        lenient().when(crowdPackAndProcessMapDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(mapDO));
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        // TEMP_PACK
        baseInfoDO.setType((byte) 1);
        lenient().when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(anyLong())).thenReturn(baseInfoDO);
        lenient().when(crowdPackUpdateLockService.getProducerValue(anyLong())).thenReturn(null);
        lenient().when(crowdPackUpdateLockService.tryProducerLock(anyLong(), anyInt())).thenReturn(true);
        lenient().doNothing().when(refinementOperationExecuteMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(any(), any(), any());
        CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(processOrchestrationDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(anyLong())).thenReturn(future);
        when(executePlanDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // Create a partial mock to avoid calling the actual runProcessOrchestrationTask
        ExecuteWriteDomainService partialMock = spy(executeWriteDomainService);
        doNothing().when(partialMock).runProcessOrchestrationTask(any());
        // act
        partialMock.runPreparedProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, atLeastOnce()).selectByExample(any());
        verify(processOrchestrationReadDomainService, atLeastOnce()).queryProcessOrchestrationDetailFuture(anyLong());
        verify(executePlanDOMapper).updateByExampleSelective(any(), any());
        verify(partialMock).runProcessOrchestrationTask(any());
    }
}
