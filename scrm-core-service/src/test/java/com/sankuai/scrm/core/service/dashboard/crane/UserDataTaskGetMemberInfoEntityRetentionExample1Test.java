package com.sankuai.scrm.core.service.dashboard.crane;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.external.contact.dal.example.ContactUserExample;
import com.sankuai.scrm.core.service.group.dal.example.MemberInfoEntityExample;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Date;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UserDataTaskGetMemberInfoEntityRetentionExample1Test {

    private UserDataTask userDataTask = new UserDataTask();

    private MemberInfoEntityExample invokePrivateMethod(String methodName, Date start, Date end, String corpId) throws Exception {
        Method method = UserDataTask.class.getDeclaredMethod(methodName, Date.class, Date.class, String.class);
        method.setAccessible(true);
        return (MemberInfoEntityExample) method.invoke(userDataTask, start, end, corpId);
    }

    private MemberInfoEntityExample invokePrivateMethod(Date start, Date end, String corpId) throws Exception {
        java.lang.reflect.Method method = UserDataTask.class.getDeclaredMethod("getMemberInfoEntityTotalExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        return (MemberInfoEntityExample) method.invoke(userDataTask, start, end, corpId);
    }

    @Test
    public void testGetMemberInfoEntityRetentionExampleNormal() throws Throwable {
        Date start = new Date(1000L);
        Date end = new Date(2000L);
        String corpId = "testCorpId";
        MemberInfoEntityExample result = invokePrivateMethod("getMemberInfoEntityRetentionExample", start, end, corpId);
        assertNotNull(result);
    }

    @Test
    public void testGetMemberInfoEntityRetentionExampleStartNull() throws Throwable {
        Date start = null;
        Date end = new Date(2000L);
        String corpId = "testCorpId";
        try {
            invokePrivateMethod("getMemberInfoEntityRetentionExample", start, end, corpId);
            fail("Expected RuntimeException to be thrown");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof RuntimeException);
        }
    }

    @Test
    public void testGetMemberInfoEntityRetentionExampleEndNull() throws Throwable {
        Date start = new Date(1000L);
        Date end = null;
        String corpId = "testCorpId";
        try {
            invokePrivateMethod("getMemberInfoEntityRetentionExample", start, end, corpId);
            fail("Expected RuntimeException to be thrown");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof RuntimeException);
        }
    }

    @Test
    public void testGetMemberInfoEntityRetentionExampleCorpIdNull() throws Throwable {
        Date start = new Date(1000L);
        Date end = new Date(2000L);
        String corpId = null;
        try {
            invokePrivateMethod("getMemberInfoEntityRetentionExample", start, end, corpId);
            fail("Expected RuntimeException to be thrown");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof RuntimeException);
        }
    }

    @Test
    public void testGetMemberInfoEntityRetentionExampleCorpIdEmpty() throws Throwable {
        Date start = new Date(1000L);
        Date end = new Date(2000L);
        String corpId = "";
        MemberInfoEntityExample result = invokePrivateMethod("getMemberInfoEntityRetentionExample", start, end, corpId);
        assertNotNull(result);
    }

    /**
     * Test getMemberInfoEntityTotalExample method with valid start, end, and corpId.
     */
    @Test
    public void testGetMemberInfoEntityTotalExampleNormal() throws Throwable {
        // Arrange
        Date start = new Date();
        Date end = new Date();
        String corpId = "testCorpId";
        // Act
        MemberInfoEntityExample result = invokePrivateMethod(start, end, corpId);
        // Assert
        assertNotNull(result);
    }

    /**
     * Test getMemberInfoEntityTotalExample method with null corpId.
     */
    @Test
    public void testGetMemberInfoEntityTotalExampleCorpIdNull() throws Throwable {
        // Arrange
        Date start = new Date();
        Date end = new Date();
        String corpId = null;
        // Act & Assert
        try {
            invokePrivateMethod(start, end, corpId);
            fail("Expected RuntimeException was not thrown");
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof RuntimeException);
            assertEquals("Value for corpId cannot be null", e.getCause().getMessage());
        }
    }

    /**
     * 测试 getRetentionContactUserExample 方法，传入有效的 start、end 和 corpId
     */
    @Test
    public void testGetRetentionContactUserExample_ValidInput() throws Throwable {
        // arrange
        Date start = new Date(1000L);
        Date end = new Date(2000L);
        String corpId = "testCorpId";
        // act
        Method method = UserDataTask.class.getDeclaredMethod("getRetentionContactUserExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        ContactUserExample result = (ContactUserExample) method.invoke(userDataTask, start, end, corpId);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getOredCriteria().get(0).getCriteria().get(0).getValue());
        assertEquals(corpId, result.getOredCriteria().get(0).getCriteria().get(1).getValue());
        assertTrue(result.getOredCriteria().get(0).getCriteria().get(2).getValue() instanceof Date);
        assertTrue(((Date) result.getOredCriteria().get(0).getCriteria().get(2).getValue()).getTime() >= start.getTime());
        assertTrue(((Date) result.getOredCriteria().get(0).getCriteria().get(2).getValue()).getTime() <= end.getTime());
    }

    /**
     * 测试 getRetentionContactUserExample 方法，传入的 start、end 或 corpId 为 null
     */
    @Test(expected = RuntimeException.class)
    public void testGetRetentionContactUserExample_NullInput() throws Throwable {
        // arrange
        Date start = null;
        Date end = null;
        String corpId = null;
        // act
        Method method = UserDataTask.class.getDeclaredMethod("getRetentionContactUserExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        try {
            method.invoke(userDataTask, start, end, corpId);
        } catch (Exception e) {
            if (e instanceof RuntimeException) {
                throw (RuntimeException) e;
            }
            throw new RuntimeException("Value for corpId cannot be null");
        }
    }
}
