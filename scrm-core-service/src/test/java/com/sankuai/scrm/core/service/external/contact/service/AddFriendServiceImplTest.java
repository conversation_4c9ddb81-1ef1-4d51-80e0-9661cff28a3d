package com.sankuai.scrm.core.service.external.contact.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.ResponseEnum;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.external.contact.dto.ScrmMobileAddFriendTaskDTO;
import com.sankuai.dz.srcm.external.contact.enums.MobileAddFriendRealTimeTaskStatuEnum;
import com.sankuai.dz.srcm.external.contact.request.QueryPageMobileAddFriendRealTimeRequest;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ScrmMobileAddFriendTaskDO;
import com.sankuai.scrm.core.service.external.contact.domain.MobileAddFriendRealTimeDomainService;
import com.sankuai.service.fe.corp.ds.enums.addfriend.call.CallNumberTypeEnum;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import java.util.stream.Collectors;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AddFriendServiceImplTest {

    @Mock
    private MobileAddFriendRealTimeDomainService mobileAddFriendRealTimeDomainService;

    @InjectMocks
    private AddFriendServiceImpl addFriendServiceImpl;

    /**
     * Test when request is null should return parameter error
     */
    @Test
    public void testQueryPageMobileAddFriendRealTimeNullRequest() throws Throwable {
        // act
        PageRemoteResponse<ScrmMobileAddFriendTaskDTO> response = addFriendServiceImpl.queryPageMobileAddFriendRealTime(null);
        // assert
        assertEquals(ResponseEnum.FAILURE.code, response.getCode());
        assertEquals("参数错误", response.getMsg());
    }

    /**
     * Test when appId is null should return parameter error
     */
    @Test
    public void testQueryPageMobileAddFriendRealTimeNullAppId() throws Throwable {
        // arrange
        QueryPageMobileAddFriendRealTimeRequest request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        // act
        PageRemoteResponse<ScrmMobileAddFriendTaskDTO> response = addFriendServiceImpl.queryPageMobileAddFriendRealTime(request);
        // assert
        assertEquals(ResponseEnum.FAILURE.code, response.getCode());
        assertEquals("参数错误", response.getMsg());
    }

    /**
     * Test when pageSize exceeds 100 should return parameter error
     */
    @Test
    public void testQueryPageMobileAddFriendRealTimePageSizeExceedLimit() throws Throwable {
        // arrange
        QueryPageMobileAddFriendRealTimeRequest request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setAppId("testApp");
        request.setPageNum(1);
        request.setPageSize(101);
        // act
        PageRemoteResponse<ScrmMobileAddFriendTaskDTO> response = addFriendServiceImpl.queryPageMobileAddFriendRealTime(request);
        // assert
        assertEquals(ResponseEnum.FAILURE.code, response.getCode());
        assertEquals("单次查询数量超过100条", response.getMsg());
    }

    /**
     * Test when no records found should return empty list
     */
    @Test
    public void testQueryPageMobileAddFriendRealTimeNoRecords() throws Throwable {
        // arrange
        QueryPageMobileAddFriendRealTimeRequest request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setAppId("testApp");
        request.setPageNum(1);
        request.setPageSize(10);
        when(mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTask(any())).thenReturn(0L);
        // act
        PageRemoteResponse<ScrmMobileAddFriendTaskDTO> response = addFriendServiceImpl.queryPageMobileAddFriendRealTime(request);
        // assert
        assertEquals(ResponseEnum.SUCCESS.code, response.getCode());
        assertTrue(response.getData().isEmpty());
        assertEquals(0, response.getTotalHit());
        assertTrue(response.isEnd());
    }

    /**
     * Test successful query with single record
     */
    @Test
    public void testQueryPageMobileAddFriendRealTimeSingleRecord() throws Throwable {
        // arrange
        QueryPageMobileAddFriendRealTimeRequest request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setAppId("testApp");
        request.setPageNum(1);
        request.setPageSize(10);
        ScrmMobileAddFriendTaskDO taskDO = new ScrmMobileAddFriendTaskDO();
        taskDO.setId(1L);
        taskDO.setAppId("testApp");
        taskDO.setAccountId("testAccount");
        taskDO.setAddNumber("*********");
        taskDO.setNumberType(CallNumberTypeEnum.PHONE_NUMBER.getCode());
        taskDO.setTaskStatus(MobileAddFriendRealTimeTaskStatuEnum.COMPLETED.getCode());
        taskDO.setCreateTime(new Date());
        taskDO.setUpdateTime(new Date());
        when(mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTask(any())).thenReturn(1L);
        when(mobileAddFriendRealTimeDomainService.queryPageMobileAddFriendRealTimeTask(any())).thenReturn(Collections.singletonList(taskDO));
        // act
        PageRemoteResponse<ScrmMobileAddFriendTaskDTO> response = addFriendServiceImpl.queryPageMobileAddFriendRealTime(request);
        // assert
        assertEquals(ResponseEnum.SUCCESS.code, response.getCode());
        assertEquals(1, response.getData().size());
        assertEquals(1, response.getTotalHit());
        assertTrue(response.isEnd());
        ScrmMobileAddFriendTaskDTO dto = response.getData().get(0);
        assertEquals(1L, dto.getId());
        assertEquals("testApp", dto.getAppId());
        assertEquals("testAccount", dto.getAccountId());
        assertEquals(CallNumberTypeEnum.PHONE_NUMBER.getDesc(), dto.getNumberTypeMSg());
        assertEquals(MobileAddFriendRealTimeTaskStatuEnum.COMPLETED.getDesc(), dto.getTaskMsg());
    }

    /**
     * Test successful query with multiple records and not end page
     */
    @Test
    public void testQueryPageMobileAddFriendRealTimeMultipleRecordsNotEnd() throws Throwable {
        // arrange
        QueryPageMobileAddFriendRealTimeRequest request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setAppId("testApp");
        request.setPageNum(1);
        request.setPageSize(2);
        ScrmMobileAddFriendTaskDO taskDO1 = new ScrmMobileAddFriendTaskDO();
        taskDO1.setId(1L);
        taskDO1.setAppId("testApp");
        taskDO1.setNumberType(CallNumberTypeEnum.PHONE_NUMBER.getCode());
        taskDO1.setTaskStatus(MobileAddFriendRealTimeTaskStatuEnum.COMPLETED.getCode());
        taskDO1.setCreateTime(new Date());
        taskDO1.setUpdateTime(new Date());
        ScrmMobileAddFriendTaskDO taskDO2 = new ScrmMobileAddFriendTaskDO();
        taskDO2.setId(2L);
        taskDO2.setAppId("testApp");
        taskDO2.setNumberType(CallNumberTypeEnum.MT_USERID.getCode());
        taskDO2.setTaskStatus(MobileAddFriendRealTimeTaskStatuEnum.IN_PROGRESS.getCode());
        taskDO2.setCreateTime(new Date());
        taskDO2.setUpdateTime(new Date());
        when(mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTask(any())).thenReturn(3L);
        when(mobileAddFriendRealTimeDomainService.queryPageMobileAddFriendRealTimeTask(any())).thenReturn(Arrays.asList(taskDO1, taskDO2));
        // act
        PageRemoteResponse<ScrmMobileAddFriendTaskDTO> response = addFriendServiceImpl.queryPageMobileAddFriendRealTime(request);
        // assert
        assertEquals(ResponseEnum.SUCCESS.code, response.getCode());
        assertEquals(2, response.getData().size());
        assertEquals(3, response.getTotalHit());
        assertFalse(response.isEnd());
    }

    /**
     * Test when exception occurs should return system error
     */
    @Test
    public void testQueryPageMobileAddFriendRealTimeExceptionOccurs() throws Throwable {
        // arrange
        QueryPageMobileAddFriendRealTimeRequest request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setAppId("testApp");
        request.setPageNum(1);
        request.setPageSize(10);
        when(mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTask(any())).thenThrow(new RuntimeException("Test exception"));
        // act
        PageRemoteResponse<ScrmMobileAddFriendTaskDTO> response = addFriendServiceImpl.queryPageMobileAddFriendRealTime(request);
        // assert
        assertEquals(ResponseEnum.FAILURE.code, response.getCode());
        assertEquals("系统异常", response.getMsg());
    }

    /**
     * Test with minimum page size (1)
     */
    @Test
    public void testQueryPageMobileAddFriendRealTimeMinimumPageSize() throws Throwable {
        // arrange
        QueryPageMobileAddFriendRealTimeRequest request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setAppId("testApp");
        request.setPageNum(1);
        request.setPageSize(1);
        ScrmMobileAddFriendTaskDO taskDO = new ScrmMobileAddFriendTaskDO();
        taskDO.setId(1L);
        taskDO.setAppId("testApp");
        taskDO.setNumberType(CallNumberTypeEnum.PHONE_NUMBER.getCode());
        taskDO.setTaskStatus(MobileAddFriendRealTimeTaskStatuEnum.COMPLETED.getCode());
        taskDO.setCreateTime(new Date());
        taskDO.setUpdateTime(new Date());
        when(mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTask(any())).thenReturn(1L);
        when(mobileAddFriendRealTimeDomainService.queryPageMobileAddFriendRealTimeTask(any())).thenReturn(Collections.singletonList(taskDO));
        // act
        PageRemoteResponse<ScrmMobileAddFriendTaskDTO> response = addFriendServiceImpl.queryPageMobileAddFriendRealTime(request);
        // assert
        assertEquals(ResponseEnum.SUCCESS.code, response.getCode());
        assertEquals(1, response.getData().size());
        assertEquals(1, response.getTotalHit());
        assertTrue(response.isEnd());
    }

    /**
     * Test with maximum page size (100)
     */
    @Test
    public void testQueryPageMobileAddFriendRealTimeMaximumPageSize() throws Throwable {
        // arrange
        QueryPageMobileAddFriendRealTimeRequest request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setAppId("testApp");
        request.setPageNum(1);
        request.setPageSize(100);
        // Create 100 mock records
        List<ScrmMobileAddFriendTaskDO> mockRecords = Collections.nCopies(100, new ScrmMobileAddFriendTaskDO());
        mockRecords = mockRecords.stream().map(record -> {
            ScrmMobileAddFriendTaskDO taskDO = new ScrmMobileAddFriendTaskDO();
            taskDO.setId(1L);
            taskDO.setAppId("testApp");
            taskDO.setNumberType(CallNumberTypeEnum.PHONE_NUMBER.getCode());
            taskDO.setTaskStatus(MobileAddFriendRealTimeTaskStatuEnum.COMPLETED.getCode());
            taskDO.setCreateTime(new Date());
            taskDO.setUpdateTime(new Date());
            return taskDO;
        }).collect(Collectors.toList());
        when(mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTask(any())).thenReturn(100L);
        when(mobileAddFriendRealTimeDomainService.queryPageMobileAddFriendRealTimeTask(any())).thenReturn(mockRecords);
        // act
        PageRemoteResponse<ScrmMobileAddFriendTaskDTO> response = addFriendServiceImpl.queryPageMobileAddFriendRealTime(request);
        // assert
        assertEquals(ResponseEnum.SUCCESS.code, response.getCode());
        assertEquals(100, response.getData().size());
        assertEquals(100, response.getTotalHit());
        assertTrue(response.isEnd());
    }
}
