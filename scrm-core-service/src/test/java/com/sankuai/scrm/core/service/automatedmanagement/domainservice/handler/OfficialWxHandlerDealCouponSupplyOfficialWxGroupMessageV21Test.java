package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.*;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.GroupSendStrategy;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verifyNoInteractions;

@ExtendWith(MockitoExtension.class)
class OfficialWxHandlerDealCouponSupplyOfficialWxGroupMessageV21Test {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private MsgUnifiedPushService msgUnifiedPushService;

    @Mock
    private GroupSendStrategy groupSendStrategy;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    private ScrmProcessOrchestrationDTO processDTO;

    private InvokeDetailKeyObject keyObject;

    @BeforeEach
    void setUp() {
        processDTO = new ScrmProcessOrchestrationDTO();
        processDTO.setId(1L);
        processDTO.setAppId("testApp");
        processDTO.setValidVersion("1.0");
        keyObject = new InvokeDetailKeyObject("key", (byte) 1, (byte) 1, 1L);
    }

    /**
     * Test case for empty invoke details list
     */
    @Test
    void testDealCouponSupplyOfficialWxGroupMessageV2EmptyDetails() throws Throwable {
        // arrange
        List<String> executorIds = new ArrayList<>();
        executorIds.add("exec1");
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> details = Collections.emptyList();
        // act
        officialWxHandler.dealCouponSupplyOfficialWxGroupMessageV2(processDTO, executorIds, keyObject, details,null );
        // assert
        verifyNoInteractions(wxInvokeLogDOMapper);
        verifyNoInteractions(msgUnifiedPushService);
    }
}
