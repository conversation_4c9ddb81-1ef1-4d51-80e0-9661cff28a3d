package com.sankuai.scrm.core.service.external.contact.service;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.external.contact.request.GenerateQrCodeUrlRequest;
import com.sankuai.dz.srcm.external.contact.response.GenerateQrCodeUrlResponse;
import com.sankuai.scrm.core.service.friend.cluecode.bo.ClueLinkBO;
import com.sankuai.scrm.core.service.friend.cluecode.domain.ClueCodeDomainService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ExternalClueServiceImplTest {

    @InjectMocks
    private ExternalClueServiceImpl externalClueService;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private ClueCodeDomainService clueCodeDomainService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test the normal scenario where all inputs are valid and the method returns a success response.
     */
    @Test
    public void testGenerateQrCodeUrl2NormalScenario() throws Throwable {
        // arrange
        GenerateQrCodeUrlRequest request = new GenerateQrCodeUrlRequest();
        request.setCorpId("validCorpId");
        request.setClueId(123L);
        request.setUserId("validUserId");
        request.setTagId("validTagId");
        when(appConfigRepository.getAppIdByCorpId("validCorpId")).thenReturn("validAppId");
        ClueLinkBO clueLinkBO = new ClueLinkBO();
        clueLinkBO.setSmsLink("smsLink");
        clueLinkBO.setH5Link("h5Link");
        when(clueCodeDomainService.queryCodeSmsLink("validAppId", 123L, "validUserId", "validTagId")).thenReturn(clueLinkBO);
        // act
        RemoteResponse<GenerateQrCodeUrlResponse> response = externalClueService.generateQrCodeUrl2(request);
        // assert
        assertTrue(response.isSuccess());
        assertEquals("smsLink", response.getData().getSmsLink());
        assertEquals("h5Link", response.getData().getH5Link());
    }

    /**
     * Test the scenario where corpId is empty.
     */
    @Test
    public void testGenerateQrCodeUrl2CorpIdEmpty() throws Throwable {
        // arrange
        GenerateQrCodeUrlRequest request = new GenerateQrCodeUrlRequest();
        request.setCorpId("");
        request.setClueId(123L);
        request.setUserId("validUserId");
        request.setTagId("validTagId");
        // act
        RemoteResponse<GenerateQrCodeUrlResponse> response = externalClueService.generateQrCodeUrl2(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("CorpId为空", response.getMsg());
    }

    /**
     * Test the scenario where clueId is null.
     */
    @Test
    public void testGenerateQrCodeUrl2ClueIdNull() throws Throwable {
        // arrange
        GenerateQrCodeUrlRequest request = new GenerateQrCodeUrlRequest();
        request.setCorpId("validCorpId");
        request.setClueId(null);
        request.setUserId("validUserId");
        request.setTagId("validTagId");
        // act
        RemoteResponse<GenerateQrCodeUrlResponse> response = externalClueService.generateQrCodeUrl2(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("线索Id为空", response.getMsg());
    }

    /**
     * Test the scenario where userId is empty.
     */
    @Test
    public void testGenerateQrCodeUrl2UserIdEmpty() throws Throwable {
        // arrange
        GenerateQrCodeUrlRequest request = new GenerateQrCodeUrlRequest();
        request.setCorpId("validCorpId");
        request.setClueId(123L);
        request.setUserId("");
        request.setTagId("validTagId");
        // act
        RemoteResponse<GenerateQrCodeUrlResponse> response = externalClueService.generateQrCodeUrl2(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("UserId为空", response.getMsg());
    }

    /**
     * Test the scenario where tagId is empty.
     */
    @Test
    public void testGenerateQrCodeUrl2TagIdEmpty() throws Throwable {
        // arrange
        GenerateQrCodeUrlRequest request = new GenerateQrCodeUrlRequest();
        request.setCorpId("validCorpId");
        request.setClueId(123L);
        request.setUserId("validUserId");
        request.setTagId("");
        // act
        RemoteResponse<GenerateQrCodeUrlResponse> response = externalClueService.generateQrCodeUrl2(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("tagId为空", response.getMsg());
    }

    /**
     * Test the scenario where appId is empty (business line not found).
     */
    @Test
    public void testGenerateQrCodeUrl2AppIdEmpty() throws Throwable {
        // arrange
        GenerateQrCodeUrlRequest request = new GenerateQrCodeUrlRequest();
        request.setCorpId("validCorpId");
        request.setClueId(123L);
        request.setUserId("validUserId");
        request.setTagId("validTagId");
        when(appConfigRepository.getAppIdByCorpId("validCorpId")).thenReturn("");
        // act
        RemoteResponse<GenerateQrCodeUrlResponse> response = externalClueService.generateQrCodeUrl2(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("业务线不存在", response.getMsg());
    }

    /**
     * Test the scenario where ClueLinkBO is null (link generation failed).
     */
    @Test
    public void testGenerateQrCodeUrl2ClueLinkBONull() throws Throwable {
        // arrange
        GenerateQrCodeUrlRequest request = new GenerateQrCodeUrlRequest();
        request.setCorpId("validCorpId");
        request.setClueId(123L);
        request.setUserId("validUserId");
        request.setTagId("validTagId");
        when(appConfigRepository.getAppIdByCorpId("validCorpId")).thenReturn("validAppId");
        when(clueCodeDomainService.queryCodeSmsLink("validAppId", 123L, "validUserId", "validTagId")).thenReturn(null);
        // act
        RemoteResponse<GenerateQrCodeUrlResponse> response = externalClueService.generateQrCodeUrl2(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("生成链接失败", response.getMsg());
    }
}
