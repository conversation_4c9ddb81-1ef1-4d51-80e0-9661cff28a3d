package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertNotNull;
import static org.mockito.Mockito.CALLS_REAL_METHODS;
import static org.mockito.Mockito.mock;

@RunWith(MockitoJUnitRunner.class)
public class AbstractActionTest {

    @InjectMocks
    private AbstractAction abstractAction = mock(AbstractAction.class, CALLS_REAL_METHODS);

    @Mock(lenient = true)
    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    @Mock(lenient = true)
    private ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationNodeDTO currentProcessingNode;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDO existedExecuteLogDO;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOExample executeLogDOExample;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_ParticipationRestrictTrue() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTOTemp = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTOTemp.setParticipationRestrict(true);
        processOrchestrationDTOTemp.setParticipationRestrictionsCycle((byte) 3);
        processOrchestrationDTOTemp.setParticipationRestrictionsTimes((byte) 2);
        processOrchestrationDTOTemp.setId(1L);
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTOTemp = new ScrmCrowdPackDetailInfoDTO();
        scrmCrowdPackDetailInfoDTOTemp.setExternalUserWxUnionId("unionId");
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTOTemp, currentProcessingNode, null);
        assertNotNull(result);
    }

    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_ParticipationRestrictFalse() throws Throwable {
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        assertNotNull(result);
    }

    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_ExistedExecuteLogDONotNull() throws Throwable {
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        assertNotNull(result);
    }
}
