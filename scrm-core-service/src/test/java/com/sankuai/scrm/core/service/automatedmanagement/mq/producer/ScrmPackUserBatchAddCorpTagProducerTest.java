package com.sankuai.scrm.core.service.automatedmanagement.mq.producer;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.producer.AsyncDelayProducerResult;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.scrm.core.service.automatedmanagement.mq.entity.ScrmPackUserBatchAddCorpTagMessage;
import java.lang.reflect.Field;
import java.util.Properties;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmPackUserBatchAddCorpTagProducerTest {

    private ScrmPackUserBatchAddCorpTagProducer producer;

    private IProducerProcessor mockProducer;

    @Mock
    private IProducerProcessor producerProcessor;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        producer = new ScrmPackUserBatchAddCorpTagProducer();
        mockProducer = mock(IProducerProcessor.class);
        Field producerField = ScrmPackUserBatchAddCorpTagProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, mockProducer);
    }

    @After
    public void tearDown() throws Exception {
        Field producerField = ScrmPackUserBatchAddCorpTagProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, null);
    }

    @Test
    public void testSendAsyncDelayMessageWithNullMessage() throws Throwable {
        ScrmPackUserBatchAddCorpTagMessage message = null;
        long delayTime = 1000;
        boolean result = producer.sendAsyncDelayMessage(message, delayTime);
        assertFalse(result);
    }

    @Test
    public void testSendAsyncDelayMessageWithDelayTimeLessThan5000() throws Throwable {
        ScrmPackUserBatchAddCorpTagMessage message = new ScrmPackUserBatchAddCorpTagMessage();
        long delayTime = 1000;
        AsyncDelayProducerResult mockResult = new AsyncDelayProducerResult(ProducerStatus.SEND_OK);
        when(mockProducer.sendAsyncDelayMessage(any(), anyLong(), any())).thenReturn(mockResult);
        boolean result = producer.sendAsyncDelayMessage(message, delayTime);
        assertTrue(result);
    }

    @Test
    public void testSendAsyncDelayMessageWithSuccess() throws Throwable {
        ScrmPackUserBatchAddCorpTagMessage message = new ScrmPackUserBatchAddCorpTagMessage();
        long delayTime = 5000;
        AsyncDelayProducerResult mockResult = new AsyncDelayProducerResult(ProducerStatus.SEND_OK);
        when(mockProducer.sendAsyncDelayMessage(any(), anyLong(), any())).thenReturn(mockResult);
        boolean result = producer.sendAsyncDelayMessage(message, delayTime);
        assertTrue(result);
    }

    @Test
    public void testSendAsyncDelayMessageWithFailure() throws Throwable {
        ScrmPackUserBatchAddCorpTagMessage message = new ScrmPackUserBatchAddCorpTagMessage();
        long delayTime = 5000;
        AsyncDelayProducerResult mockResult = new AsyncDelayProducerResult(ProducerStatus.SEND_FAILURE);
        when(mockProducer.sendAsyncDelayMessage(any(), anyLong(), any())).thenReturn(mockResult);
        boolean result = producer.sendAsyncDelayMessage(message, delayTime);
        assertFalse(result);
    }

    /**
     * Tests afterPropertiesSet method under normal conditions.
     */
    @Test
    public void testAfterPropertiesSetNormal() throws Throwable {
        try (MockedStatic<MafkaClient> mockedStatic = mockStatic(MafkaClient.class)) {
            // Arrange
            ScrmPackUserBatchAddCorpTagProducer producer = new ScrmPackUserBatchAddCorpTagProducer();
            mockedStatic.when(() -> MafkaClient.buildDelayProduceFactory(any(Properties.class), anyString())).thenReturn(producerProcessor);
            // Act
            producer.afterPropertiesSet();
            // Assert
            mockedStatic.verify(() -> MafkaClient.buildDelayProduceFactory(any(Properties.class), anyString()), times(1));
        }
    }

    /**
     * Tests afterPropertiesSet method under exceptional conditions.
     */
    @Test(expected = Exception.class)
    public void testAfterPropertiesSetException() throws Throwable {
        try (MockedStatic<MafkaClient> mockedStatic = mockStatic(MafkaClient.class)) {
            // Arrange
            ScrmPackUserBatchAddCorpTagProducer producer = new ScrmPackUserBatchAddCorpTagProducer();
            mockedStatic.when(() -> MafkaClient.buildDelayProduceFactory(any(Properties.class), anyString())).thenThrow(new Exception());
            // Act
            producer.afterPropertiesSet();
            // Assert
            mockedStatic.verify(() -> MafkaClient.buildDelayProduceFactory(any(Properties.class), anyString()), times(1));
        }
    }
}
