package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmAIAgentTestGroupRecordDOMapper;
import com.sankuai.scrm.core.service.realtime.task.domainservice.ScrmGrowthUserInfoDomainService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;

import static org.junit.jupiter.api.Assertions.assertSame;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import java.util.Properties;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
public class GroupRetailAiEngineABTestRecordMessageConsumerDestroy1Test {

    @Mock
    private ScrmGrowthUserInfoDomainService scrmGrowthUserInfoDomainService;

    @Mock
    private ScrmAIAgentTestGroupRecordDOMapper aiAgentTestGroupRecordDOMapper;

    @Mock
    private RedisStoreClient redisClient;

    @Mock
    private IConsumerProcessor consumer;

    @InjectMocks
    private GroupRetailAiEngineABTestRecordMessageConsumer groupRetailAiEngineABTestRecordMessageConsumer;

    private void setConsumerField(IConsumerProcessor consumer) throws Exception {
        Field consumerField = GroupRetailAiEngineABTestRecordMessageConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(groupRetailAiEngineABTestRecordMessageConsumer, consumer);
    }

    /**
     * 测试当consumer不为null时，成功关闭consumer
     */
    @Test
    public void testDestroy_WhenConsumerNotNull_ShouldCloseConsumer() throws Throwable {
        // arrange
        setConsumerField(consumer);
        // act
        groupRetailAiEngineABTestRecordMessageConsumer.destroy();
        // assert
        verify(consumer, times(1)).close();
    }

    /**
     * 测试当consumer为null时，不执行任何操作
     */
    @Test
    public void testDestroy_WhenConsumerIsNull_ShouldDoNothing() throws Throwable {
        // arrange
        setConsumerField(null);
        // act
        groupRetailAiEngineABTestRecordMessageConsumer.destroy();
        // assert
        verifyNoInteractions(consumer);
    }

    /**
     * 测试当consumer关闭抛出异常时，方法应该抛出异常
     */
    @Test
    public void testDestroy_WhenConsumerCloseThrowsException_ShouldPropagateException() throws Throwable {
        // arrange
        setConsumerField(consumer);
        RuntimeException expectedException = new RuntimeException("Close failed");
        doThrow(expectedException).when(consumer).close();
        // act
        RuntimeException actualException = assertThrows(RuntimeException.class, () -> groupRetailAiEngineABTestRecordMessageConsumer.destroy());
        // assert
        verify(consumer, times(1)).close();
        assertSame(expectedException, actualException);
    }

    @Test
    public void testAfterPropertiesSetSuccess() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockConsumer = mock(IConsumerProcessor.class);
            mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), anyString())).thenReturn(mockConsumer);
            GroupRetailAiEngineABTestRecordMessageConsumer consumer = new GroupRetailAiEngineABTestRecordMessageConsumer();
            // act
            consumer.afterPropertiesSet();
            // assert
            mockedMafkaClient.verify(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), eq("scrm.group.retail.ai.engine.test.record.message")));
            verify(mockConsumer).recvMessageWithParallel(eq(String.class), any());
        }
    }

    @Test
    public void testAfterPropertiesSetPropertiesConfiguration() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockConsumer = mock(IConsumerProcessor.class);
            mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), anyString())).thenAnswer(invocation -> {
                Properties props = invocation.getArgument(0);
                assertEquals("daozong", props.getProperty(ConsumerConstants.MafkaBGNamespace));
                assertEquals("com.sankuai.medicalcosmetology.scrm.core", props.getProperty(ConsumerConstants.MafkaClientAppkey));
                assertEquals("scrm.group.retail.ai.engine.test.record.message.consumer", props.getProperty(ConsumerConstants.SubscribeGroup));
                return mockConsumer;
            });
            GroupRetailAiEngineABTestRecordMessageConsumer consumer = new GroupRetailAiEngineABTestRecordMessageConsumer();
            // act
            consumer.afterPropertiesSet();
            // assert - verification happens in the thenAnswer block
        }
    }
}
