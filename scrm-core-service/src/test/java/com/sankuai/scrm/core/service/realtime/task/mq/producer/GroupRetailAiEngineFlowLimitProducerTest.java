package com.sankuai.scrm.core.service.realtime.task.mq.producer;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowActionTrackDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Field;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class GroupRetailAiEngineFlowLimitProducerTest {

    @InjectMocks
    private GroupRetailAiEngineFlowLimitProducer producer;

    private IProducerProcessor mockProducer;

    private MockedStatic<JsonUtils> mockedJsonUtils;

    @BeforeEach
    public void setUp() throws Exception {
        // 创建mock对象
        mockProducer = mock(IProducerProcessor.class);
        mockedJsonUtils = Mockito.mockStatic(JsonUtils.class);
        // 使用反射设置私有静态字段
        Field producerField = GroupRetailAiEngineFlowLimitProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, mockProducer);
    }

    @AfterEach
    public void tearDown() {
        mockedJsonUtils.close();
    }

    /**
     * 测试输入参数为null时方法直接返回
     */
    @Test
    public void testSendMessageWithNullInput() throws Throwable {
        // arrange - no setup needed for null input
        // act
        producer.sendMessage(null);
        // assert
        verifyNoInteractions(mockProducer);
        mockedJsonUtils.verifyNoInteractions();
    }

    /**
     * 测试正常发送成功的情况
     */
    @Test
    public void testSendMessageSuccess() throws Throwable {
        // arrange
        IntelligentFollowActionTrackDTO dto = new IntelligentFollowActionTrackDTO();
        String jsonStr = "test_json";
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        mockedJsonUtils.when(() -> JsonUtils.toStr(dto)).thenReturn(jsonStr);
        when(mockProducer.sendMessage(jsonStr)).thenReturn(successResult);
        // act
        producer.sendMessage(dto);
        // assert
        mockedJsonUtils.verify(() -> JsonUtils.toStr(dto));
        verify(mockProducer).sendMessage(jsonStr);
    }

    /**
     * 测试发送失败的情况
     */
    @Test
    public void testSendMessageFailure() throws Throwable {
        // arrange
        IntelligentFollowActionTrackDTO dto = new IntelligentFollowActionTrackDTO();
        String jsonStr = "test_json";
        ProducerResult failureResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        mockedJsonUtils.when(() -> JsonUtils.toStr(dto)).thenReturn(jsonStr);
        when(mockProducer.sendMessage(jsonStr)).thenReturn(failureResult);
        // act
        producer.sendMessage(dto);
        // assert
        mockedJsonUtils.verify(() -> JsonUtils.toStr(dto));
        verify(mockProducer).sendMessage(jsonStr);
    }

    /**
     * 测试发送过程中抛出异常的情况
     */
    @Test
    public void testSendMessageWithException() throws Throwable {
        // arrange
        IntelligentFollowActionTrackDTO dto = new IntelligentFollowActionTrackDTO();
        String jsonStr = "test_json";
        Exception expectedException = new RuntimeException("test exception");
        mockedJsonUtils.when(() -> JsonUtils.toStr(dto)).thenReturn(jsonStr);
        when(mockProducer.sendMessage(jsonStr)).thenThrow(expectedException);
        // act
        producer.sendMessage(dto);
        // assert
        mockedJsonUtils.verify(() -> JsonUtils.toStr(dto));
        verify(mockProducer).sendMessage(jsonStr);
    }

    /**
     * 测试序列化异常的情况
     */
    @Test
    public void testSendMessageWithSerializationException() throws Throwable {
        // arrange
        IntelligentFollowActionTrackDTO dto = new IntelligentFollowActionTrackDTO();
        RuntimeException expectedException = new RuntimeException("serialization failed");
        mockedJsonUtils.when(() -> JsonUtils.toStr(dto)).thenThrow(expectedException);
        // act
        producer.sendMessage(dto);
        // assert
        mockedJsonUtils.verify(() -> JsonUtils.toStr(dto));
        verifyNoInteractions(mockProducer);
    }
}
