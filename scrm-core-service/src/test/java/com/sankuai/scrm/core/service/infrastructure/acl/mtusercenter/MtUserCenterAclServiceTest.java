package com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter;

import com.sankuai.wpt.user.thirdinfo.thrift.thirdinfo.WeixinInfoResp;
import com.sankuai.wpt.user.thirdinfo.thrift.thirdinfo.ThirdInfoService;
import org.apache.thrift.TException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.*;
import com.sankuai.wpt.user.thirdinfo.thrift.thirdinfo.WeixinInfo;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class MtUserCenterAclServiceTest {

    @InjectMocks
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private ThirdInfoService.Iface rpcUserThirdInfoService;

    private String unionId;

    private String weixinAppid;

    private WeixinInfoResp weixinInfoResp;

    @Before
    public void setUp() {
        unionId = "testUnionId";
        weixinAppid = "testWeixinAppid";
        weixinInfoResp = new WeixinInfoResp();
    }

    @Test
    public void testGetWeixinInfoUnionIdOrWeixinAppidIsNull() throws Throwable {
        unionId = null;
        WeixinInfo result = mtUserCenterAclService.getWeixinInfo(unionId, weixinAppid);
        assertNull(result);
        unionId = "testUnionId";
        weixinAppid = null;
        result = mtUserCenterAclService.getWeixinInfo(unionId, weixinAppid);
        assertNull(result);
    }

    @Test
    public void testGetWeixinInfoWeixinInfoRespIsNull() throws Throwable {
        when(rpcUserThirdInfoService.getWeixinInfoByUnionId(unionId, weixinAppid, true, true)).thenReturn(null);
        WeixinInfo result = mtUserCenterAclService.getWeixinInfo(unionId, weixinAppid);
        assertNull(result);
    }

    @Test
    public void testGetWeixinInfoIsSuccessIsFalse() throws Throwable {
        weixinInfoResp.setSuccess(false);
        when(rpcUserThirdInfoService.getWeixinInfoByUnionId(unionId, weixinAppid, true, true)).thenReturn(weixinInfoResp);
        WeixinInfo result = mtUserCenterAclService.getWeixinInfo(unionId, weixinAppid);
        assertNull(result);
    }

    @Test
    public void testGetWeixinInfoThrowsTException() throws Throwable {
        when(rpcUserThirdInfoService.getWeixinInfoByUnionId(unionId, weixinAppid, true, true)).thenThrow(TException.class);
        WeixinInfo result = mtUserCenterAclService.getWeixinInfo(unionId, weixinAppid);
        assertNull(result);
    }

    @Test
    public void testGetWeixinInfoIsSuccessIsTrue() throws Throwable {
        weixinInfoResp.setSuccess(true);
        WeixinInfo weixinInfo = new WeixinInfo();
        weixinInfoResp.setWeixinInfo(weixinInfo);
        when(rpcUserThirdInfoService.getWeixinInfoByUnionId(unionId, weixinAppid, true, true)).thenReturn(weixinInfoResp);
        WeixinInfo result = mtUserCenterAclService.getWeixinInfo(unionId, weixinAppid);
        assertEquals(weixinInfo, result);
    }
}
