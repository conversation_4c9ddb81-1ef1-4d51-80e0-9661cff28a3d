package com.sankuai.scrm.core.service.infrastructure.acl.ds;

import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.AssistantInfo;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.enums.AssistantPcLoginStatusEnum;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.enums.CorpAssistantStatus;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.enums.DeviceType;
import com.sankuai.service.fe.corp.wx.thrift.AssisantInfoModel;
import com.sankuai.service.fe.corp.wx.thrift.CorpWxService;
import com.sankuai.service.fe.corp.wx.thrift.GetCorpAssistantRequest;
import com.sankuai.service.fe.corp.wx.thrift.GetCorpAssistantResponse;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class DsAssistantAclTest {

    @Mock(lenient = true)
    private CorpWxService.Iface corpWxService;

    @InjectMocks
    private DsAssistantAcl dsAssistantAcl;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test case for invalid input parameters
     */
    @Test
    public void testGetAssistantListInvalidInput() throws Throwable {
        // arrange
        String corpId = "";
        long orgId = -1;
        // act
        List<AssistantInfo> result = dsAssistantAcl.getAssistantList(corpId, orgId);
        // assert
        assertNull(result);
        verify(corpWxService, never()).getCorpAssistant(any());
    }

    /**
     * Test case for null response from corpWxService
     */
    @Test
    public void testGetAssistantListNullResponse() throws Throwable {
        // arrange
        String corpId = "corp123";
        long orgId = 1L;
        when(corpWxService.getCorpAssistant(any())).thenReturn(null);
        // act
        List<AssistantInfo> result = dsAssistantAcl.getAssistantList(corpId, orgId);
        // assert
        assertNull(result);
        verify(corpWxService, times(1)).getCorpAssistant(any());
    }

    /**
     * Test case for empty assistant list in response
     */
    @Test
    public void testGetAssistantListEmptyResponse() throws Throwable {
        // arrange
        String corpId = "corp123";
        long orgId = 1L;
        GetCorpAssistantResponse response = new GetCorpAssistantResponse();
        response.setAssistants(new ArrayList<>());
        when(corpWxService.getCorpAssistant(any())).thenReturn(response);
        // act
        List<AssistantInfo> result = dsAssistantAcl.getAssistantList(corpId, orgId);
        // assert
        assertNull(result);
        verify(corpWxService, times(1)).getCorpAssistant(any());
    }

    /**
     * Test case for response with some invalid assistants
     */
    @Test
    public void testGetAssistantListWithInvalidAssistants() throws Throwable {
        // arrange
        String corpId = "corp123";
        long orgId = 1L;
        GetCorpAssistantResponse response = new GetCorpAssistantResponse();
        List<AssisantInfoModel> assistants = new ArrayList<>();
        // Valid assistant
        AssisantInfoModel validAssistant = new AssisantInfoModel();
        validAssistant.setDeviceType(DeviceType.CLOUD_PHONE.getCode());
        validAssistant.setStatus(CorpAssistantStatus.NORMAL.getCode());
        validAssistant.setUserid("user1");
        assistants.add(validAssistant);
        // Invalid assistant (wrong status)
        AssisantInfoModel invalidAssistant = new AssisantInfoModel();
        invalidAssistant.setDeviceType(DeviceType.CLOUD_PHONE.getCode());
        invalidAssistant.setStatus(CorpAssistantStatus.ABNORMAL.getCode());
        invalidAssistant.setUserid("user2");
        assistants.add(invalidAssistant);
        response.setAssistants(assistants);
        when(corpWxService.getCorpAssistant(any())).thenReturn(response);
        // act
        List<AssistantInfo> result = dsAssistantAcl.getAssistantList(corpId, orgId);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("user1", result.get(0).getAccountId());
        verify(corpWxService, times(1)).getCorpAssistant(any());
    }

    /**
     * Test case for response with all valid assistants
     */
    @Test
    public void testGetAssistantListAllValidAssistants() throws Throwable {
        // arrange
        String corpId = "corp123";
        long orgId = 1L;
        GetCorpAssistantResponse response = new GetCorpAssistantResponse();
        List<AssisantInfoModel> assistants = new ArrayList<>();
        for (int i = 0; i < 3; i++) {
            AssisantInfoModel assistant = new AssisantInfoModel();
            assistant.setDeviceType(DeviceType.CLOUD_PHONE.getCode());
            assistant.setStatus(CorpAssistantStatus.NORMAL.getCode());
            assistant.setUserid("user" + i);
            assistants.add(assistant);
        }
        response.setAssistants(assistants);
        when(corpWxService.getCorpAssistant(any())).thenReturn(response);
        // act
        List<AssistantInfo> result = dsAssistantAcl.getAssistantList(corpId, orgId);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        for (int i = 0; i < 3; i++) {
            assertEquals("user" + i, result.get(i).getAccountId());
        }
        verify(corpWxService, times(1)).getCorpAssistant(any());
    }

    /**
     * Test case for pagination scenario
     */
    @Test
    public void testGetAssistantListPagination() throws Throwable {
        // arrange
        String corpId = "corp123";
        long orgId = 1L;
        GetCorpAssistantResponse response1 = new GetCorpAssistantResponse();
        GetCorpAssistantResponse response2 = new GetCorpAssistantResponse();
        List<AssisantInfoModel> assistants1 = new ArrayList<>();
        List<AssisantInfoModel> assistants2 = new ArrayList<>();
        for (int i = 0; i < 20; i++) {
            AssisantInfoModel assistant = new AssisantInfoModel();
            assistant.setDeviceType(DeviceType.CLOUD_PHONE.getCode());
            assistant.setStatus(CorpAssistantStatus.NORMAL.getCode());
            assistant.setUserid("user" + i);
            assistants1.add(assistant);
        }
        for (int i = 20; i < 25; i++) {
            AssisantInfoModel assistant = new AssisantInfoModel();
            assistant.setDeviceType(DeviceType.CLOUD_PHONE.getCode());
            assistant.setStatus(CorpAssistantStatus.NORMAL.getCode());
            assistant.setUserid("user" + i);
            assistants2.add(assistant);
        }
        response1.setAssistants(assistants1);
        response1.setTotal(25);
        response2.setAssistants(assistants2);
        response2.setTotal(25);
        when(corpWxService.getCorpAssistant(any())).thenReturn(response1).thenReturn(response2);
        // act
        List<AssistantInfo> result = dsAssistantAcl.getAssistantList(corpId, orgId);
        // assert
        assertNotNull(result);
        assertEquals(25, result.size());
        for (int i = 0; i < 25; i++) {
            assertEquals("user" + i, result.get(i).getAccountId());
        }
        verify(corpWxService, times(2)).getCorpAssistant(any());
    }

    /**
     * Test case for exception scenario
     */
    @Test
    public void testGetAssistantListException() throws Throwable {
        // arrange
        String corpId = "corp123";
        long orgId = 1L;
        when(corpWxService.getCorpAssistant(any())).thenThrow(new TException("Test exception"));
        // act
        List<AssistantInfo> result = dsAssistantAcl.getAssistantList(corpId, orgId);
        // assert
        assertNull(result);
        verify(corpWxService, times(1)).getCorpAssistant(any());
    }
}
