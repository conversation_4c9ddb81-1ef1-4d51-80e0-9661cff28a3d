package com.sankuai.scrm.core.service.activity.fission.validation.validator;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.activity.fission.request.GroupFissionActivityRequest;
import com.sankuai.dz.srcm.activity.fission.request.RewardInfoRequest;
import com.sankuai.scrm.core.service.activity.fission.validation.exception.FissionValidatorException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import org.junit.*;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class FissionGroupTypeValidatorCommonCheckTest {

    private final FissionGroupTypeValidator validator = new FissionGroupTypeValidator();

    private void invokeCommonCheck(GroupFissionActivityRequest request) throws Exception {
        Method method = FissionGroupTypeValidator.class.getDeclaredMethod("commonCheck", GroupFissionActivityRequest.class);
        method.setAccessible(true);
        method.invoke(validator, request);
    }

    /**
     * Test case when rewardInfo list is empty
     */
    @Test
    public void testCommonCheck_RewardInfoListEmpty() throws Throwable {
        GroupFissionActivityRequest request = mock(GroupFissionActivityRequest.class);
        when(request.getRewardInfo()).thenReturn(Collections.emptyList());
        org.junit.jupiter.api.Assertions.assertDoesNotThrow(() -> invokeCommonCheck(request));
        verify(request, times(1)).getRewardInfo();
    }

    /**
     * Test case when rewardInfo list has valid stages
     */
    @Test
    public void testCommonCheck_ValidStages() throws Throwable {
        GroupFissionActivityRequest request = mock(GroupFissionActivityRequest.class);
        RewardInfoRequest reward1 = new RewardInfoRequest();
        reward1.setStage(1);
        reward1.setInvitationNum(5);
        RewardInfoRequest reward2 = new RewardInfoRequest();
        reward2.setStage(2);
        reward2.setInvitationNum(10);
        when(request.getRewardInfo()).thenReturn(Arrays.asList(reward1, reward2));
        org.junit.jupiter.api.Assertions.assertDoesNotThrow(() -> invokeCommonCheck(request));
        verify(request, times(1)).getRewardInfo();
    }

    /**
     * Test case when rewardInfo list has duplicate stages
     */
    @Test
    public void testCommonCheck_DuplicateStages() throws Throwable {
        GroupFissionActivityRequest request = mock(GroupFissionActivityRequest.class);
        RewardInfoRequest reward1 = new RewardInfoRequest();
        reward1.setStage(1);
        reward1.setInvitationNum(5);
        RewardInfoRequest reward2 = new RewardInfoRequest();
        // Duplicate stage
        reward2.setStage(1);
        reward2.setInvitationNum(10);
        when(request.getRewardInfo()).thenReturn(Arrays.asList(reward1, reward2));
        Exception exception = org.junit.jupiter.api.Assertions.assertThrows(Exception.class, () -> invokeCommonCheck(request));
        org.junit.jupiter.api.Assertions.assertTrue(exception.getCause() instanceof FissionValidatorException);
        org.junit.jupiter.api.Assertions.assertEquals("活动对应奖品阶段设置不正确", exception.getCause().getMessage());
    }

    /**
     * Test case when rewardInfo list has invalid invitation numbers
     */
    @Test
    public void testCommonCheck_InvalidInvitationNumbers() throws Throwable {
        GroupFissionActivityRequest request = mock(GroupFissionActivityRequest.class);
        RewardInfoRequest reward1 = new RewardInfoRequest();
        reward1.setStage(1);
        reward1.setInvitationNum(10);
        RewardInfoRequest reward2 = new RewardInfoRequest();
        reward2.setStage(2);
        // Lower than previous stage
        reward2.setInvitationNum(5);
        when(request.getRewardInfo()).thenReturn(Arrays.asList(reward1, reward2));
        Exception exception = org.junit.jupiter.api.Assertions.assertThrows(Exception.class, () -> invokeCommonCheck(request));
        org.junit.jupiter.api.Assertions.assertTrue(exception.getCause() instanceof FissionValidatorException);
        org.junit.jupiter.api.Assertions.assertEquals("活动对应奖品阶段邀请人数设置不正确", exception.getCause().getMessage());
    }
}
