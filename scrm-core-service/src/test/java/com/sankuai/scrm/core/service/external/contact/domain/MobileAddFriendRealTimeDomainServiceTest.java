package com.sankuai.scrm.core.service.external.contact.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ScrmMobileAddFriendTaskDO;
import com.sankuai.scrm.core.service.external.contact.dal.example.ScrmMobileAddFriendTaskDOExample;
import com.sankuai.scrm.core.service.external.contact.dal.mapper.ext.ExtScrmMobileAddFriendTaskDOMapper;
import java.util.ArrayList;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.assertEquals;
import com.sankuai.dz.srcm.external.contact.request.QueryPageMobileAddFriendRealTimeRequest;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;

@ExtendWith(MockitoExtension.class)
class MobileAddFriendRealTimeDomainServiceTest {

    @Mock
    private ExtScrmMobileAddFriendTaskDOMapper mobileAddFriendTaskDOMapper;

    @InjectMocks
    private MobileAddFriendRealTimeDomainService mobileAddFriendRealTimeDomainService;

    /**
     * 测试存在符合条件的任务时返回true
     */
    @Test
    public void testCheckMobileAddFriendRealTimeTaskExist_WhenTaskExists_ReturnTrue() throws Throwable {
        // arrange
        String appId = "testAppId";
        String accountId = "testAccountId";
        String addNumber = "***********";
        Integer numberType = 1;
        List<ScrmMobileAddFriendTaskDO> taskList = new ArrayList<>();
        taskList.add(new ScrmMobileAddFriendTaskDO());
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(taskList);
        // act
        Boolean result = mobileAddFriendRealTimeDomainService.checkMobileAddFriendRealTimeTaskExist(appId, accountId, addNumber, numberType);
        // assert
        assertTrue(result);
    }

    /**
     * 测试不存在符合条件的任务时返回false
     */
    @Test
    public void testCheckMobileAddFriendRealTimeTaskExist_WhenTaskNotExists_ReturnFalse() throws Throwable {
        // arrange
        String appId = "testAppId";
        String accountId = "testAccountId";
        String addNumber = "***********";
        Integer numberType = 1;
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(new ArrayList<>());
        // act
        Boolean result = mobileAddFriendRealTimeDomainService.checkMobileAddFriendRealTimeTaskExist(appId, accountId, addNumber, numberType);
        // assert
        assertFalse(result);
    }

    /**
     * 测试查询结果为null时返回false
     */
    @Test
    public void testCheckMobileAddFriendRealTimeTaskExist_WhenQueryResultIsNull_ReturnFalse() throws Throwable {
        // arrange
        String appId = "testAppId";
        String accountId = "testAccountId";
        String addNumber = "***********";
        Integer numberType = 1;
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(null);
        // act
        Boolean result = mobileAddFriendRealTimeDomainService.checkMobileAddFriendRealTimeTaskExist(appId, accountId, addNumber, numberType);
        // assert
        assertFalse(result);
    }

    /**
     * 测试addNumber为空时正常处理
     */
    @Test
    public void testCheckMobileAddFriendRealTimeTaskExist_WhenAddNumberIsEmpty_ReturnFalse() throws Throwable {
        // arrange
        String appId = "testAppId";
        String accountId = "testAccountId";
        String addNumber = "";
        Integer numberType = 1;
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(new ArrayList<>());
        // act
        Boolean result = mobileAddFriendRealTimeDomainService.checkMobileAddFriendRealTimeTaskExist(appId, accountId, addNumber, numberType);
        // assert
        assertFalse(result);
    }

    /**
     * 测试参数为null时抛出异常
     */
    @Test
    public void testCheckMobileAddFriendRealTimeTaskExist_WhenAllParamsAreEmpty_ReturnFalse() throws Throwable {
        // arrange
        String appId = null;
        String accountId = null;
        String addNumber = null;
        Integer numberType = null;
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            mobileAddFriendRealTimeDomainService.checkMobileAddFriendRealTimeTaskExist(appId, accountId, addNumber, numberType);
        });
    }

    @Test
    public void testCountMobileAddFriendRealTimeTaskWithOnlyAppId() throws Throwable {
        // arrange
        QueryPageMobileAddFriendRealTimeRequest request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setAppId("testApp");
        when(mobileAddFriendTaskDOMapper.countByExample(any())).thenReturn(5L);
        // act
        Long count = mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTask(request);
        // assert
        assertEquals(5L, count);
    }

    @Test
    public void testCountMobileAddFriendRealTimeTaskWithAccountId() throws Throwable {
        // arrange
        QueryPageMobileAddFriendRealTimeRequest request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setAppId("testApp");
        request.setAccountId("testAccount");
        when(mobileAddFriendTaskDOMapper.countByExample(any())).thenReturn(3L);
        // act
        Long count = mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTask(request);
        // assert
        assertEquals(3L, count);
    }

    @Test
    public void testCountMobileAddFriendRealTimeTaskWithAddNumber() throws Throwable {
        // arrange
        QueryPageMobileAddFriendRealTimeRequest request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setAppId("testApp");
        request.setAddNumber("***********");
        when(mobileAddFriendTaskDOMapper.countByExample(any())).thenReturn(2L);
        // act
        Long count = mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTask(request);
        // assert
        assertEquals(2L, count);
    }

    @Test
    public void testCountMobileAddFriendRealTimeTaskWithNumberType() throws Throwable {
        // arrange
        QueryPageMobileAddFriendRealTimeRequest request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setAppId("testApp");
        request.setNumberType(1);
        when(mobileAddFriendTaskDOMapper.countByExample(any())).thenReturn(4L);
        // act
        Long count = mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTask(request);
        // assert
        assertEquals(4L, count);
    }

    @Test
    public void testCountMobileAddFriendRealTimeTaskWithAllParams() throws Throwable {
        // arrange
        QueryPageMobileAddFriendRealTimeRequest request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setAppId("testApp");
        request.setAccountId("testAccount");
        request.setAddNumber("***********");
        request.setNumberType(1);
        when(mobileAddFriendTaskDOMapper.countByExample(any())).thenReturn(1L);
        // act
        Long count = mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTask(request);
        // assert
        assertEquals(1L, count);
    }

    @Test
    public void testCountMobileAddFriendRealTimeTaskWithEmptyAccountId() throws Throwable {
        // arrange
        QueryPageMobileAddFriendRealTimeRequest request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setAppId("testApp");
        request.setAccountId("");
        when(mobileAddFriendTaskDOMapper.countByExample(any())).thenReturn(5L);
        // act
        Long count = mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTask(request);
        // assert
        assertEquals(5L, count);
    }

    @Test
    public void testCountMobileAddFriendRealTimeTaskWithEmptyAddNumber() throws Throwable {
        // arrange
        QueryPageMobileAddFriendRealTimeRequest request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setAppId("testApp");
        request.setAddNumber("");
        when(mobileAddFriendTaskDOMapper.countByExample(any())).thenReturn(5L);
        // act
        Long count = mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTask(request);
        // assert
        assertEquals(5L, count);
    }

    @Test
    public void testCountMobileAddFriendRealTimeTaskWithNullNumberType() throws Throwable {
        // arrange
        QueryPageMobileAddFriendRealTimeRequest request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setAppId("testApp");
        request.setNumberType(null);
        when(mobileAddFriendTaskDOMapper.countByExample(any())).thenReturn(5L);
        // act
        Long count = mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTask(request);
        // assert
        assertEquals(5L, count);
    }
}
