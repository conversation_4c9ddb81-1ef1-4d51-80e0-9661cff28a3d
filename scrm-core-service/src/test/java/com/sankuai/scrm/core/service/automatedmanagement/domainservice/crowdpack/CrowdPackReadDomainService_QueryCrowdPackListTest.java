package com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack;

import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackBaseInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ext.ScrmAmCrowdPackDetailCountDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackBaseInfoDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackBaseInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackDetailInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.crowdpack.ScrmCrowdPackBaseInfoConverter;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CrowdPackReadDomainService_QueryCrowdPackListTest {

    @InjectMocks
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock(lenient = true)
    private ExtScrmAmCrowdPackBaseInfoDOMapper extScrmAmCrowdPackBaseInfoDOMapper;

    @Mock(lenient = true)
    private ExtScrmAmCrowdPackDetailInfoDOMapper extScrmAmCrowdPackDetailInfoDOMapper;

    @Mock(lenient = true)
    private ScrmCrowdPackBaseInfoConverter packBaseInfoConverter;

    private ScrmCrowdPackDTO dto;

    @Before
    public void setUp() {
        dto = new ScrmCrowdPackDTO();
        dto.setAppId("testAppId");
        dto.setId(1L);
    }

    @Test
    public void testQueryCrowdPackListNormal() throws Throwable {
        List<ScrmAmCrowdPackBaseInfoDO> baseInfoDOList = Arrays.asList(new ScrmAmCrowdPackBaseInfoDO(), new ScrmAmCrowdPackBaseInfoDO());
        List<ScrmAmCrowdPackDetailCountDO> detailCountDOList = Arrays.asList(new ScrmAmCrowdPackDetailCountDO(), new ScrmAmCrowdPackDetailCountDO());
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByExampleOrderByUpdateTimeDesc(any(ScrmAmCrowdPackBaseInfoDOExample.class))).thenReturn(baseInfoDOList);
        when(extScrmAmCrowdPackDetailInfoDOMapper.batchCountByExample(any())).thenReturn(detailCountDOList);
        ScrmCrowdPackDTO dto1 = new ScrmCrowdPackDTO();
        dto1.setId(1L);
        ScrmCrowdPackDTO dto2 = new ScrmCrowdPackDTO();
        dto2.setId(2L);
        when(packBaseInfoConverter.convertToDTOsSafety(any())).thenReturn(Arrays.asList(dto1, dto2));
        List<ScrmCrowdPackDTO> result = crowdPackReadDomainService.queryCrowdPackList(dto, 1, 10);
        assertEquals(2, result.size());
        verify(extScrmAmCrowdPackBaseInfoDOMapper, times(1)).selectByExampleOrderByUpdateTimeDesc(any(ScrmAmCrowdPackBaseInfoDOExample.class));
        verify(extScrmAmCrowdPackDetailInfoDOMapper, times(1)).batchCountByExample(any());
    }

    @Test
    public void testQueryCrowdPackListBoundary() throws Throwable {
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByExampleOrderByUpdateTimeDesc(any(ScrmAmCrowdPackBaseInfoDOExample.class))).thenReturn(Collections.emptyList());
        when(packBaseInfoConverter.convertToDTOsSafety(any())).thenReturn(Collections.emptyList());
        when(extScrmAmCrowdPackDetailInfoDOMapper.batchCountByExample(any())).thenReturn(Collections.emptyList());
        List<ScrmCrowdPackDTO> result = crowdPackReadDomainService.queryCrowdPackList(dto, 1, 10);
        assertEquals(0, result.size());
        verify(extScrmAmCrowdPackBaseInfoDOMapper, times(1)).selectByExampleOrderByUpdateTimeDesc(any(ScrmAmCrowdPackBaseInfoDOExample.class));
        verify(extScrmAmCrowdPackDetailInfoDOMapper, times(1)).batchCountByExample(any());
    }

    @Test(expected = Exception.class)
    public void testQueryCrowdPackListException() throws Throwable {
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByExampleOrderByUpdateTimeDesc(any(ScrmAmCrowdPackBaseInfoDOExample.class))).thenThrow(new RuntimeException());
        crowdPackReadDomainService.queryCrowdPackList(dto, 1, 10);
    }
}
