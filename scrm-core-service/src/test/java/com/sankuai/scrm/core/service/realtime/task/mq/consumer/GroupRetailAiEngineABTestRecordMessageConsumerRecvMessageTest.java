package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmAIAgentTestGroupRecordDO;
import com.sankuai.scrm.core.service.realtime.task.dal.example.ScrmAIAgentTestGroupRecordDOExample;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmAIAgentTestGroupRecordDOMapper;
import com.sankuai.scrm.core.service.realtime.task.domainservice.ScrmGrowthUserInfoDomainService;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineABTestRecordMessageDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;

@ExtendWith(MockitoExtension.class)
class GroupRetailAiEngineABTestRecordMessageConsumerRecvMessageTest {

    @Mock
    private ScrmGrowthUserInfoDomainService scrmGrowthUserInfoDomainService;

    @Mock
    private ScrmAIAgentTestGroupRecordDOMapper aiAgentTestGroupRecordDOMapper;

    @Mock
    private RedisStoreClient redisClient;

    @InjectMocks
    private GroupRetailAiEngineABTestRecordMessageConsumer consumer;

    private static class TestMafkaMessage extends MafkaMessage<String> {

        private final RuntimeException exceptionToThrow;

        public TestMafkaMessage(RuntimeException exceptionToThrow) {
            super("topic", 0, 0L, "key", "test");
            this.exceptionToThrow = exceptionToThrow;
        }

        @Override
        public String getBody() {
            throw exceptionToThrow;
        }

        @Override
        public String toString() {
            return "TestMafkaMessage";
        }
    }

    private ConsumeStatus invokeRecvMessage(MafkaMessage<String> message, MessagetContext context) throws Exception {
        Method method = GroupRetailAiEngineABTestRecordMessageConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        method.setAccessible(true);
        return (ConsumeStatus) method.invoke(consumer, message, context);
    }

    /**
     * Test empty message body scenario
     */
    @Test
    public void testRecvMessageEmptyBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(null);
        MessagetContext context = mock(MessagetContext.class);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test actionType=1 scenario
     */
    @Test
    public void testRecvMessageActionType1() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);
        GroupRetailAiEngineABTestRecordMessageDTO dto = new GroupRetailAiEngineABTestRecordMessageDTO();
        dto.setActionType(1);
        dto.setMtUserId(123L);
        dto.setAppId("testApp");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(scrmGrowthUserInfoDomainService).updateDBMapInfoByMtUserId(123L, "testApp");
    }

    /**
     * Test existing record update scenario
     */
    @Test
    public void testRecvMessageExistingRecordUpdate() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);
        GroupRetailAiEngineABTestRecordMessageDTO dto = new GroupRetailAiEngineABTestRecordMessageDTO();
        dto.setActionType(2);
        dto.setMtUserId(123L);
        dto.setTestVersion("v1");
        dto.setStatus(1);
        dto.setOriginValue(100);
        dto.setTestValue(200);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        List<ScrmAIAgentTestGroupRecordDO> records = new ArrayList<>();
        records.add(new ScrmAIAgentTestGroupRecordDO());
        when(aiAgentTestGroupRecordDOMapper.selectByExample(any(ScrmAIAgentTestGroupRecordDOExample.class))).thenReturn(records);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(aiAgentTestGroupRecordDOMapper).updateByExampleSelective(any(), any());
    }

    /**
     * Test redis setnx failure scenario
     */
    @Test
    public void testRecvMessageRedisSetnxFailure() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);
        GroupRetailAiEngineABTestRecordMessageDTO dto = new GroupRetailAiEngineABTestRecordMessageDTO();
        dto.setActionType(2);
        dto.setMtUserId(123L);
        dto.setTestVersion("v1");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(aiAgentTestGroupRecordDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(redisClient.setnx(any(StoreKey.class), eq(true), eq(60))).thenReturn(false);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(redisClient).setnx(any(StoreKey.class), eq(true), eq(60));
    }

    /**
     * Test new record insertion scenario
     */
    @Test
    public void testRecvMessageNewRecordInsertion() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);
        GroupRetailAiEngineABTestRecordMessageDTO dto = new GroupRetailAiEngineABTestRecordMessageDTO();
        dto.setActionType(2);
        dto.setMtUserId(123L);
        dto.setTestVersion("v1");
        dto.setStatus(1);
        dto.setOriginValue(100);
        dto.setTestValue(200);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(aiAgentTestGroupRecordDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(redisClient.setnx(any(StoreKey.class), eq(true), eq(60))).thenReturn(true);
        when(aiAgentTestGroupRecordDOMapper.insertSelective(any())).thenReturn(1);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(aiAgentTestGroupRecordDOMapper).insertSelective(any());
    }

    /**
     * Test record insertion failure scenario
     */
    @Test
    public void testRecvMessageInsertionFailure() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);
        GroupRetailAiEngineABTestRecordMessageDTO dto = new GroupRetailAiEngineABTestRecordMessageDTO();
        dto.setActionType(2);
        dto.setMtUserId(123L);
        dto.setTestVersion("v1");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(aiAgentTestGroupRecordDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(redisClient.setnx(any(StoreKey.class), eq(true), eq(60))).thenReturn(true);
        when(aiAgentTestGroupRecordDOMapper.insertSelective(any())).thenReturn(0);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(aiAgentTestGroupRecordDOMapper).insertSelective(any());
    }

    /**
     * Test JSON parsing failure scenario
     */
    @Test
    public void testRecvMessageJsonParsingFailure() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        MessagetContext context = mock(MessagetContext.class);
        when(message.getBody()).thenReturn("invalid json");
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }
}
