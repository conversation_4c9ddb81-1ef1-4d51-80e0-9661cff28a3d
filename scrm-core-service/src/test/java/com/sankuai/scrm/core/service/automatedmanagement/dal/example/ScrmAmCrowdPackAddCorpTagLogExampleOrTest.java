package com.sankuai.scrm.core.service.automatedmanagement.dal.example;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ScrmAmCrowdPackAddCorpTagLogExampleOrTest {

    private ScrmAmCrowdPackAddCorpTagLogExample scrmAmCrowdPackAddCorpTagLogExample;

    @Before
    public void setUp() {
        scrmAmCrowdPackAddCorpTagLogExample = new ScrmAmCrowdPackAddCorpTagLogExample();
    }

    /**
     * 测试or方法，应该返回一个新的Criteria对象，并将其添加到oredCriteria列表中
     */
    @Test
    public void testOr() {
        // arrange
        int originalSize = scrmAmCrowdPackAddCorpTagLogExample.getOredCriteria().size();
        // act
        ScrmAmCrowdPackAddCorpTagLogExample.Criteria criteria = scrmAmCrowdPackAddCorpTagLogExample.or();
        // assert
        assertNotNull(criteria);
        assertEquals(originalSize + 1, scrmAmCrowdPackAddCorpTagLogExample.getOredCriteria().size());
        assertTrue(scrmAmCrowdPackAddCorpTagLogExample.getOredCriteria().contains(criteria));
    }

    /**
     * 测试oredCriteria列表为空的情况
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsEmpty() {
        // arrange
        ScrmAmCrowdPackAddCorpTagLogExample.Criteria criteria = scrmAmCrowdPackAddCorpTagLogExample.createCriteria();
        // act
        // 无需执行任何操作
        // assert
        assertNotNull(criteria);
        assertEquals(1, scrmAmCrowdPackAddCorpTagLogExample.getOredCriteria().size());
    }

    /**
     * 测试oredCriteria列表不为空的情况
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsNotEmpty() {
        // arrange
        ScrmAmCrowdPackAddCorpTagLogExample.Criteria criteria1 = scrmAmCrowdPackAddCorpTagLogExample.createCriteria();
        ScrmAmCrowdPackAddCorpTagLogExample.Criteria criteria2 = scrmAmCrowdPackAddCorpTagLogExample.createCriteria();
        // act
        // 无需执行任何操作
        // assert
        assertNotNull(criteria1);
        assertNotNull(criteria2);
        assertEquals(1, scrmAmCrowdPackAddCorpTagLogExample.getOredCriteria().size());
    }
}
