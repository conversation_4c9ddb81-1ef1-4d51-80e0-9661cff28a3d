package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteWriteDomainService;
import com.sankuai.scrm.core.service.message.push.dto.MsgTaskDetailResultDTO;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushDetailStatus;
import com.sankuai.scrm.core.service.message.push.request.MsgPushRequest;
import com.sankuai.scrm.core.service.message.push.response.MsgPushResponse;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class PrivateSendStrategyTest {

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @InjectMocks
    private PrivateSendStrategy privateSendStrategy;

    @Mock
    private ExecuteWriteDomainService executeWriteDomainService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试场景：当任务详情结果和wxInvokeDetailDOS都不为空，且任务详情结果中包含有效的sender时，应正确调用updateExecuteLogSenderByIds方法
     */
    @Test
    public void testUpdateExecuteLogSenderWithValidData() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        // arrange
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setGroupId("receiverId");
        detailDO.setExecuteLogId(1L);
        wxInvokeDetailDOS.add(detailDO);
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = new ArrayList<>();
        MsgTaskDetailResultDTO taskDetailResultDTO = new MsgTaskDetailResultDTO();
        taskDetailResultDTO.setSender("sender");
        taskDetailResultDTO.setReceiverId("receiverId");
        taskDetailResultDTO.setStatus(MsgPushDetailStatus.SUCCESS.getCode());
        taskDetailResultDTOS.add(taskDetailResultDTO);
        // act
        Method method = privateSendStrategy.getClass().getDeclaredMethod("updateExecuteLogSender", List.class, List.class);
        method.setAccessible(true);
        method.invoke(privateSendStrategy, wxInvokeDetailDOS, taskDetailResultDTOS);
        // assert
        verify(executeWriteDomainService, times(1)).updateExecuteLogSenderByIds(anyString(), anyList());
    }

    /**
     * 测试场景：处理成功，无失败的消息推送任务
     */
    @Test
    public void testProcessSuccessWithNoFailedMsgTasks() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS = new ArrayList<>();
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = new ArrayList<>();
        String msgId = "msgId";
        // act
        privateSendStrategy.processSuccess(processOrchestrationDTO, wxInvokeDetailDOS, wxInvokeLogDO, taskDetailResultDTOS, msgId);
        // assert
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(any(), any());
    }

    /**
     * 测试场景：处理成功，存在失败的消息推送任务
     */
    @Test
    public void testProcessSuccessWithFailedMsgTasks() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS = new ArrayList<>();
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = Arrays.asList(new MsgTaskDetailResultDTO() {

            {
                setStatus(MsgPushDetailStatus.FAILED.getCode());
                setReceiverId("receiverId1");
            }
        });
        String msgId = "msgId";
        // act
        privateSendStrategy.processSuccess(processOrchestrationDTO, wxInvokeDetailDOS, wxInvokeLogDO, taskDetailResultDTOS, msgId);
        // assert
        verify(wxInvokeLogDOMapper, times(0)).insert(any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
    }

    /**
     * Test when JsonUtils.toStr throws exception - should still proceed with status update
     */
    @Test
    public void testProcessFailure_JsonUtilsException() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setExecuteLogId(1L);
        totalInvokeDetailDOS.add(detail);
        MsgPushRequest request = new MsgPushRequest();
        MsgPushResponse<String> response = MsgPushResponse.success("test");
        try (MockedStatic<JsonUtils> mockedJsonUtils = mockStatic(JsonUtils.class)) {
            // Return empty string for first call to avoid NPE in log message
            mockedJsonUtils.when(() -> JsonUtils.toStr(eq(request))).thenReturn("");
            mockedJsonUtils.when(() -> JsonUtils.toStr(eq(response))).thenReturn("");
            // act
            privateSendStrategy.processFailure(totalInvokeDetailDOS, request, response);
            // verify service was called despite JsonUtils issues
            verify(executeWriteDomainService).updateExecuteLogStatusByIds(any(), eq(Arrays.asList(1L)));
            // verify JsonUtils was called for both request and response
            mockedJsonUtils.verify(() -> JsonUtils.toStr(eq(request)));
            mockedJsonUtils.verify(() -> JsonUtils.toStr(eq(response)));
        }
    }
}
