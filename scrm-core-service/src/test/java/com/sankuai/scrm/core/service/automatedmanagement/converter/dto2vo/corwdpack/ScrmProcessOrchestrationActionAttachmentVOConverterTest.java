package com.sankuai.scrm.core.service.automatedmanagement.converter.dto2vo.corwdpack;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationAttachmentTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationActionAttachmentVO;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentContentDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmProcessOrchestrationActionAttachmentVOConverterTest {

    private ScrmProcessOrchestrationActionAttachmentVOConverter converter = new ScrmProcessOrchestrationActionAttachmentVOConverter();

    @Test
    public void testConvertToDTOWhenVOIsNull() throws Throwable {
        ScrmProcessOrchestrationActionAttachmentVO vo = null;
        ScrmProcessOrchestrationActionAttachmentDTO result = converter.convertToDTO(vo);
        assertNull(result);
    }

    @Test
    public void testConvertToDTOWhenAttachmentTypeIdIsNull() throws Throwable {
        ScrmProcessOrchestrationActionAttachmentVO vo = new ScrmProcessOrchestrationActionAttachmentVO();
        vo.setAttachmentTypeId(null);
        vo.setAttachmentContent("{}");
        // We expect a NullPointerException to be thrown
        try {
            ScrmProcessOrchestrationActionAttachmentDTO result = converter.convertToDTO(vo);
            fail("Expected NullPointerException was not thrown");
        } catch (NullPointerException e) {
            // This is the expected behavior given the current implementation
            assertTrue(true);
        }
    }

    @Test
    public void testConvertToDTOWhenAttachmentContentIsNull() throws Throwable {
        ScrmProcessOrchestrationActionAttachmentVO vo = new ScrmProcessOrchestrationActionAttachmentVO();
        vo.setAttachmentContent(null);
        vo.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.SUPPLY.getValue());
        ScrmProcessOrchestrationActionAttachmentDTO result = converter.convertToDTO(vo);
        assertNotNull(result);
        assertNull(result.getAttachmentSupplyDetailDTO());
        assertNull(result.getAttachmentContentDetailDTO());
    }

    @Test
    public void testConvertToDTOWhenAttachmentTypeIdNotEqualSUPPLY() throws Throwable {
        ScrmProcessOrchestrationActionAttachmentVO vo = new ScrmProcessOrchestrationActionAttachmentVO();
        vo.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.PICTURE.getValue());
        vo.setAttachmentContent("{\"title\":\"test\"}");
        ScrmProcessOrchestrationActionAttachmentDTO result = converter.convertToDTO(vo);
        assertNotNull(result);
        assertNull(result.getAttachmentSupplyDetailDTO());
        assertNotNull(result.getAttachmentContentDetailDTO());
        assertEquals("test", result.getAttachmentContentDetailDTO().getTitle());
    }

    @Test
    public void testConvertToDTOWhenAttachmentTypeIdEqualSUPPLY() throws Throwable {
        ScrmProcessOrchestrationActionAttachmentVO vo = new ScrmProcessOrchestrationActionAttachmentVO();
        vo.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.SUPPLY.getValue());
        vo.setAttachmentContent("{\"supplyType\":1}");
        ScrmProcessOrchestrationActionAttachmentDTO result = converter.convertToDTO(vo);
        assertNotNull(result);
        assertNotNull(result.getAttachmentSupplyDetailDTO());
        assertNull(result.getAttachmentContentDetailDTO());
        assertEquals(Integer.valueOf(1), result.getAttachmentSupplyDetailDTO().getSupplyType());
    }
}
