package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationWxTouchTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.GroupSendStrategy;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.PrivateSendStrategy;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.group.dynamiccode.constant.GroupDynamicCodeConstants;
import com.sankuai.scrm.core.service.infrastructure.acl.UploadWxMediaAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGetGroupMsgSendResultAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGroupSendMessageAcl;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import com.sankuai.scrm.core.service.message.push.dto.MsgTaskDetailResultDTO;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import org.apache.commons.lang.StringUtils;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;


@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerDealNotFriendResultV2Test {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    @Mock
    private ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO;

    @Mock
    private ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO;

    @Mock
    private ScrmAmProcessOrchestrationProductActivityPageDO pageInfo;

    @Mock
    private Map<String, MsgPushContentDTO> existedAttachmentMap;

    @Mock
    private ShortLinkUtils shortLinkUtils;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private MsgUnifiedPushService msgUnifiedPushService;

    @Mock
    private GroupSendStrategy groupSendStrategy;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private WxGroupSendMessageAcl wxGroupSendMessageAcl;

    @Mock
    private WxGetGroupMsgSendResultAcl wxGetGroupmsgSendResultAcl;

    @Mock
    private UploadWxMediaAcl uploadWxMediaAcl;

    @Mock
    private WeChatTokenAcl weChatTokenAcl;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private PrivateSendStrategy privateSendStrategy;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private ScrmProcessOrchestrationDTO createBasicDTO() {
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setId(1L);
        dto.setValidVersion("v1");
        dto.setAppId("app1");
        // Set up NodeMediumDTO with mock
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = mock(ScrmProcessOrchestrationNodeMediumDTO.class);
        // Set up ActionDTO
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setId(1L);
        // Set up ActionContentDTO
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("test content");
        contentDTO.setId(1L);
        // Mock the behavior of NodeMediumDTO methods
        when(nodeMediumDTO.getActionDTO(anyLong())).thenReturn(actionDTO);
        when(nodeMediumDTO.getActionContentDTOList(any(ScrmProcessOrchestrationActionDTO.class))).thenReturn(Collections.singletonList(contentDTO));
        dto.setNodeMediumDTO(nodeMediumDTO);
        return dto;
    }

    private ScrmAmProcessOrchestrationWxInvokeDetailDO createInvokeDetail(Long processOrchestrationNodeId) {
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setProcessOrchestrationNodeId(processOrchestrationNodeId);
        detail.setGroupId("group1");
        detail.setExecuteLogId(1L);
        return detail;
    }

    private ScrmProcessOrchestrationDTO setupProcessOrchestrationDTO(Long nodeId) {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = mock(ScrmProcessOrchestrationNodeMediumDTO.class);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setId(1L);
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("test content");
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOs = Collections.singletonList(contentDTO);
        when(processOrchestrationDTO.getNodeMediumDTO()).thenReturn(nodeMediumDTO);
        when(nodeMediumDTO.getActionDTO(nodeId)).thenReturn(actionDTO);
        when(nodeMediumDTO.getActionContentDTOList(actionDTO)).thenReturn(contentDTOs);
        when(nodeMediumDTO.getActionAttachmentDTOList(any())).thenReturn(Collections.emptyList());
        return processOrchestrationDTO;
    }

    /**
     * Test case for empty taskDetailResultDTOS list.
     * The method should return immediately without performing any updates.
     */
    @Test
    public void testDealNotFriendResultV2EmptyTaskDetailResultDTOS() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = Collections.emptyList();
        // act
        officialWxHandler.dealNotFriendResultV2(processOrchestrationId, processOrchestrationVersion, invokeLogDO, taskDetailResultDTOS);
        // assert
        verify(wxInvokeDetailDOMapper, never()).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(wxInvokeDetailDOMapper, never()).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class));
    }





    /**
     * Test case for no failed invoke detail DOs found.
     * The method should return without performing any updates.
     */
    @Test
    public void testDealNotFriendResultV2NoFailedInvokeDetailDOs() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setType("groupMessage");
        MsgTaskDetailResultDTO taskDetailResultDTO = new MsgTaskDetailResultDTO();
        taskDetailResultDTO.setReceiverId("receiver1");
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = Arrays.asList(taskDetailResultDTO);
        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(Collections.emptyList());
        // act
        officialWxHandler.dealNotFriendResultV2(processOrchestrationId, processOrchestrationVersion, invokeLogDO, taskDetailResultDTOS);
        // assert
        verify(wxInvokeDetailDOMapper).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(wxInvokeDetailDOMapper, never()).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class));
    }



    /**
     * Test case for when pageInfo is null.
     */
    @Test
    public void testGetActivityPageAttachmentVOV2_NullPageInfo() throws Throwable {
        // arrange
        // act
        MsgPushContentDTO result = officialWxHandler.getActivityPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, null, existedAttachmentMap, null);
        // assert
        assertNull(result);
    }

    /**
     * Test case for when the result is already cached.
     */
    @Test
    public void testGetActivityPageAttachmentVOV2_CachedResult() throws Throwable {
        // arrange
        long nodeId = 1L;
        long pageId = 2L;
        String cacheKey = nodeId + "-" + pageId;
        MsgPushContentDTO cachedDTO = new MsgPushContentDTO();
        when(actionAttachmentDTO.getProcessOrchestrationNodeId()).thenReturn(nodeId);
        when(pageInfo.getId()).thenReturn(pageId);
        when(existedAttachmentMap.containsKey(cacheKey)).thenReturn(true);
        when(existedAttachmentMap.get(cacheKey)).thenReturn(cachedDTO);
        // Mock the buildPageUrl method to avoid NPE
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        // act
        MsgPushContentDTO result = officialWxHandler.getActivityPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, null);
        // assert
        assertNotNull(result);
        // Verify we got the exact same cached instance
        assertSame(cachedDTO, result);
    }


    /**
     * Test empty taskDetailResultDTOS scenario
     */
    @Test
    public void testDealSuccessResultV2EmptyTaskDetailResult() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = Collections.emptyList();
        // act
        officialWxHandler.dealSuccessResultV2(processOrchestrationDTO, processOrchestrationId, processOrchestrationVersion, invokeLogDO, taskDetailResultDTOS);
        // assert
        verify(wxInvokeDetailDOMapper, never()).selectByExample(any());
        verify(informationGatheringService, never()).logUserStatusChangeDetail(any(), any());
    }

    /**
     * Test group message success scenario
     */
    @Test
    public void testDealSuccessResultV2GroupMessageSuccess() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        ScrmProcessOrchestrationNodeDTO node = new ScrmProcessOrchestrationNodeDTO();
        node.setNodeId(1L);
        node.setChildrenNodes(Collections.emptyList());
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(Collections.singletonList(node));
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setType(ScrmProcessOrchestrationWxTouchTypeEnum.GROUP_MESSAGE.getCode());
        // Ensure this is set
        invokeLogDO.setProcessOrchestrationNodeId(1L);
        MsgTaskDetailResultDTO taskResult = new MsgTaskDetailResultDTO();
        taskResult.setReceiverId("group1");
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = Collections.singletonList(taskResult);
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(invokeDetail));
        // act
        officialWxHandler.dealSuccessResultV2(processOrchestrationDTO, processOrchestrationId, processOrchestrationVersion, invokeLogDO, taskDetailResultDTOS);
        // assert
        verify(wxInvokeDetailDOMapper).selectByExample(any());
        verify(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
        verify(informationGatheringService).logUserStatusChangeDetail(any(), any());
    }

    /**
     * Test private message success scenario
     */
    @Test
    public void testDealSuccessResultV2PrivateMessageSuccess() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        ScrmProcessOrchestrationNodeDTO node = new ScrmProcessOrchestrationNodeDTO();
        node.setNodeId(1L);
        node.setChildrenNodes(Collections.emptyList());
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(Collections.singletonList(node));
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setType(ScrmProcessOrchestrationWxTouchTypeEnum.MESSAGE.getCode());
        // Ensure this is set
        invokeLogDO.setProcessOrchestrationNodeId(1L);
        MsgTaskDetailResultDTO taskResult = new MsgTaskDetailResultDTO();
        taskResult.setReceiverId("user1");
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = Collections.singletonList(taskResult);
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(invokeDetail));
        // act
        officialWxHandler.dealSuccessResultV2(processOrchestrationDTO, processOrchestrationId, processOrchestrationVersion, invokeLogDO, taskDetailResultDTOS);
        // assert
        verify(wxInvokeDetailDOMapper).selectByExample(any());
        verify(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
        verify(informationGatheringService).logUserStatusChangeDetail(any(), any());
    }

    /**
     * Test no matching invoke details found scenario
     */
    @Test
    public void testDealSuccessResultV2NoMatchingInvokeDetails() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        ScrmProcessOrchestrationNodeDTO node = new ScrmProcessOrchestrationNodeDTO();
        node.setNodeId(1L);
        node.setChildrenNodes(Collections.emptyList());
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(Collections.singletonList(node));
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setType(ScrmProcessOrchestrationWxTouchTypeEnum.MESSAGE.getCode());
        // Ensure this is set
        invokeLogDO.setProcessOrchestrationNodeId(1L);
        MsgTaskDetailResultDTO taskResult = new MsgTaskDetailResultDTO();
        taskResult.setReceiverId("user1");
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = Collections.singletonList(taskResult);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        officialWxHandler.dealSuccessResultV2(processOrchestrationDTO, processOrchestrationId, processOrchestrationVersion, invokeLogDO, taskDetailResultDTOS);
        // assert
        verify(wxInvokeDetailDOMapper).selectByExample(any());
        verify(wxInvokeDetailDOMapper, never()).updateByExampleSelective(any(), any());
        verify(informationGatheringService, never()).logUserStatusChangeDetail(any(), any());
    }



    /**
     * Test buildWxInvokeLogDO with all valid parameters
     */
    @Test
    public void testBuildWxInvokeLogDO_WithValidParameters() throws Throwable {
        // arrange
        String executorId = "executor123";
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setId(1L);
        dto.setValidVersion("v1.0");
        Long nodeId = 100L;
        // Using GROUP_MESSAGE as an example
        ScrmProcessOrchestrationWxTouchTypeEnum message = ScrmProcessOrchestrationWxTouchTypeEnum.GROUP_MESSAGE;
        // act
        Method method = OfficialWxHandler.class.getDeclaredMethod("buildWxInvokeLogDO", String.class, ScrmProcessOrchestrationDTO.class, Long.class, ScrmProcessOrchestrationWxTouchTypeEnum.class);
        method.setAccessible(true);
        ScrmAmProcessOrchestrationWxInvokeLogDO result = (ScrmAmProcessOrchestrationWxInvokeLogDO) method.invoke(null, executorId, dto, nodeId, message);
        // assert
        assertNotNull(result);
        assertEquals(executorId, result.getExecutorId());
        assertEquals(dto.getId(), result.getProcessOrchestrationId());
        assertEquals(dto.getValidVersion(), result.getProcessOrchestrationVersion());
        assertEquals(nodeId, result.getProcessOrchestrationNodeId());
        assertEquals(StringUtils.EMPTY, result.getJobid());
        assertEquals(message.getCode(), result.getType());
        // Using direct byte value 1 for WAIT_FOR_CREATE
        assertEquals(Byte.valueOf((byte) 1), result.getStatus());
    }

    /**
     * Test buildWxInvokeLogDO with empty executorId
     */
    @Test
    public void testBuildWxInvokeLogDO_WithEmptyExecutorId() throws Throwable {
        // arrange
        String executorId = "";
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setId(1L);
        dto.setValidVersion("v1.0");
        Long nodeId = 100L;
        ScrmProcessOrchestrationWxTouchTypeEnum message = ScrmProcessOrchestrationWxTouchTypeEnum.GROUP_MESSAGE;
        // act
        Method method = OfficialWxHandler.class.getDeclaredMethod("buildWxInvokeLogDO", String.class, ScrmProcessOrchestrationDTO.class, Long.class, ScrmProcessOrchestrationWxTouchTypeEnum.class);
        method.setAccessible(true);
        ScrmAmProcessOrchestrationWxInvokeLogDO result = (ScrmAmProcessOrchestrationWxInvokeLogDO) method.invoke(null, executorId, dto, nodeId, message);
        // assert
        assertNotNull(result);
        assertEquals("", result.getExecutorId());
    }

    /**
     * Test buildWxInvokeLogDO with null executorId
     */
    @Test
    public void testBuildWxInvokeLogDO_WithNullExecutorId() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setId(1L);
        dto.setValidVersion("v1.0");
        Long nodeId = 100L;
        ScrmProcessOrchestrationWxTouchTypeEnum message = ScrmProcessOrchestrationWxTouchTypeEnum.GROUP_MESSAGE;
        // act
        Method method = OfficialWxHandler.class.getDeclaredMethod("buildWxInvokeLogDO", String.class, ScrmProcessOrchestrationDTO.class, Long.class, ScrmProcessOrchestrationWxTouchTypeEnum.class);
        method.setAccessible(true);
        ScrmAmProcessOrchestrationWxInvokeLogDO result = (ScrmAmProcessOrchestrationWxInvokeLogDO) method.invoke(null, null, dto, nodeId, message);
        // assert
        assertNotNull(result);
        assertNull(result.getExecutorId());
    }



    /**
     * Test buildWxInvokeLogDO with null nodeId
     */
    @Test
    public void testBuildWxInvokeLogDO_WithNullNodeId() throws Throwable {
        // arrange
        String executorId = "executor123";
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setId(1L);
        dto.setValidVersion("v1.0");
        ScrmProcessOrchestrationWxTouchTypeEnum message = ScrmProcessOrchestrationWxTouchTypeEnum.GROUP_MESSAGE;
        // act
        Method method = OfficialWxHandler.class.getDeclaredMethod("buildWxInvokeLogDO", String.class, ScrmProcessOrchestrationDTO.class, Long.class, ScrmProcessOrchestrationWxTouchTypeEnum.class);
        method.setAccessible(true);
        ScrmAmProcessOrchestrationWxInvokeLogDO result = (ScrmAmProcessOrchestrationWxInvokeLogDO) method.invoke(null, executorId, dto, null, message);
        // assert
        assertNotNull(result);
        assertNull(result.getProcessOrchestrationNodeId());
    }



    /**
     * Test buildWxInvokeLogDO with null version in DTO
     */
    @Test
    public void testBuildWxInvokeLogDO_WithNullVersion() throws Throwable {
        // arrange
        String executorId = "executor123";
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setId(1L);
        dto.setValidVersion(null);
        Long nodeId = 100L;
        ScrmProcessOrchestrationWxTouchTypeEnum message = ScrmProcessOrchestrationWxTouchTypeEnum.GROUP_MESSAGE;
        // act
        Method method = OfficialWxHandler.class.getDeclaredMethod("buildWxInvokeLogDO", String.class, ScrmProcessOrchestrationDTO.class, Long.class, ScrmProcessOrchestrationWxTouchTypeEnum.class);
        method.setAccessible(true);
        ScrmAmProcessOrchestrationWxInvokeLogDO result = (ScrmAmProcessOrchestrationWxInvokeLogDO) method.invoke(null, executorId, dto, nodeId, message);
        // assert
        assertNotNull(result);
        assertNull(result.getProcessOrchestrationVersion());
    }

    /**
     * Test empty input list
     */
    @Test
    public void testDealCouponSupplyOfficialWxGroupMessageV2EmptyInput() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        // Initialize the ID
        processOrchestrationDTO.setId(1L);
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("key", (byte) 1, (byte) 1, 1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        // act
        officialWxHandler.dealCouponSupplyOfficialWxGroupMessageV2(processOrchestrationDTO, Collections.singletonList("executor1"), keyObject, totalInvokeDetailDOS,null );
        // assert
        verify(wxInvokeLogDOMapper, never()).selectByExample(any());
    }



    /**
     * Test case for when the key exists in the existedAttachmentMap.
     */
    @Test
    public void testGetCustomizedProductPromotionMsgPushContentDTO_KeyExists() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setProductType(1);
        supplyDetailDTO.setJumpUrl("jumpUrl");
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        MsgPushContentDTO existingMsgPushContentDTO = new MsgPushContentDTO();
        existedAttachmentMap.put("1-jumpUrl", existingMsgPushContentDTO);
        // act
        MsgPushContentDTO result = officialWxHandler.getCustomizedProductPromotionMsgPushContentDTO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, existedAttachmentMap);
        // assert
        assertNotNull(result);
        assertEquals(existingMsgPushContentDTO, result);
    }

    /**
     * Test case for when the key does not exist in the existedAttachmentMap.
     */
    @Test
    public void testGetCustomizedProductPromotionMsgPushContentDTO_KeyDoesNotExist() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setProductType(1);
        // 确保URL包含参数
        supplyDetailDTO.setJumpUrl("https://example.com?param=1");
        // 设置jumpPageType
        supplyDetailDTO.setJumpPageType(1);
        supplyDetailDTO.setShelfName("shelfName");
        supplyDetailDTO.setHeadpicUrl("headpicUrl");
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        // Mock informationGatheringService
        when(informationGatheringService.queryCommunityDistributor(any())).thenReturn("testDistributorCode");
        // Mock shortLinkUtils
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("https://short.url");
        // act
        MsgPushContentDTO result = officialWxHandler.getCustomizedProductPromotionMsgPushContentDTO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, existedAttachmentMap);
        // assert
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.MINI_PROGRAM, result.getContentTypeTEnum());
        assertNotNull(result.getMiniProgramDTO());
        assertEquals("shelfName", result.getMiniProgramDTO().getTitle());
        assertEquals(GroupDynamicCodeConstants.ORIGIN_MX_MINIP_APPID, result.getMiniProgramDTO().getOriginAppId());
        assertEquals(GroupDynamicCodeConstants.MX_MINIP_APPID, result.getMiniProgramDTO().getAppId());
        assertEquals("headpicUrl", result.getMiniProgramDTO().getThumbnail());
        assertNotNull(result.getMiniProgramDTO().getPagePath());
        assertTrue(existedAttachmentMap.containsKey("1-https://example.com?param=1"));
    }




    /**
     * Test empty taskDetailResultDTOS - should return immediately
     */
    @Test
    public void testDealPreventDisturbanceResultV2EmptyTaskDetails() throws Throwable {
        // arrange
        List<MsgTaskDetailResultDTO> taskDetails = Collections.emptyList();
        // act
        officialWxHandler.dealPreventDisturbanceResultV2(1L, "v1", new ScrmAmProcessOrchestrationWxInvokeLogDO(), taskDetails);
        // assert
        verifyNoInteractions(wxInvokeDetailDOMapper);
        verifyNoInteractions(executeLogDOMapper);
    }



    /**
     * Test non-group message scenario with no failed details
     */
    @Test
    public void testDealPreventDisturbanceResultV2NonGroupMessageNoFailures() throws Throwable {
        // arrange
        Long processId = 1L;
        String version = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLog = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLog.setType(ScrmProcessOrchestrationWxTouchTypeEnum.MESSAGE.getCode());
        MsgTaskDetailResultDTO taskDetail = new MsgTaskDetailResultDTO();
        taskDetail.setReceiverId("user1");
        List<MsgTaskDetailResultDTO> taskDetails = Collections.singletonList(taskDetail);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        officialWxHandler.dealPreventDisturbanceResultV2(processId, version, invokeLog, taskDetails);
        // assert
        verify(wxInvokeDetailDOMapper).selectByExample(any());
        verifyNoMoreInteractions(wxInvokeDetailDOMapper);
        verifyNoInteractions(executeLogDOMapper);
    }



    /**
     * Test database update failure scenario
     */
    @Test
    public void testDealPreventDisturbanceResultV2DatabaseUpdateFailure() throws Throwable {
        // arrange
        Long processId = 1L;
        String version = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLog = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLog.setType(ScrmProcessOrchestrationWxTouchTypeEnum.GROUP_MESSAGE.getCode());
        MsgTaskDetailResultDTO taskDetail = new MsgTaskDetailResultDTO();
        taskDetail.setReceiverId("group1");
        List<MsgTaskDetailResultDTO> taskDetails = Collections.singletonList(taskDetail);
        ScrmAmProcessOrchestrationWxInvokeDetailDO failedDetail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        failedDetail.setExecuteLogId(100L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> failedDetails = Collections.singletonList(failedDetail);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(failedDetails);
        doThrow(new RuntimeException("DB Error")).when(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
        // act & assert
        try {
            officialWxHandler.dealPreventDisturbanceResultV2(processId, version, invokeLog, taskDetails);
            fail("Expected exception not thrown");
        } catch (RuntimeException e) {
            assertEquals("DB Error", e.getMessage());
        }
    }


    /**
     * Test token retrieval failure scenario
     */
    @Test
    public void testDealNormalOfficialWxGroupMessageTokenFailure() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = setupProcessOrchestrationDTO(1L);
        when(processOrchestrationDTO.getId()).thenReturn(1L);
        when(processOrchestrationDTO.getValidVersion()).thenReturn("1.0");
        when(processOrchestrationDTO.getAppId()).thenReturn("app1");
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> details = new ArrayList<>();
        details.add(createInvokeDetail(1L));
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(appConfigRepository.getCorpIdByAppId(any())).thenReturn("corp1");
        WeChatTokenResult tokenResult = WeChatTokenResult.builder().errcode(40001).build();
        when(weChatTokenAcl.getTokenByCorpId(any())).thenReturn(tokenResult);
        StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("type", (byte) 1, (byte) 1, 1L);
        // act
        officialWxHandler.dealNormalOfficialWxGroupMessage(processOrchestrationDTO, "executor", keyObject, details, stepResult);
        // assert
        assertFalse(stepResult.isSuccess());
        // ATTACHMENT_ABNORMALITY code
        assertEquals(Integer.valueOf(11), stepResult.getCode());
    }
}