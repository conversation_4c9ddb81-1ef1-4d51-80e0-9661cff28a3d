package com.sankuai.scrm.core.service.couponIntegration.domain;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.couponIntegration.enums.CreationSceneEnum;
import com.sankuai.dz.srcm.couponIntegration.enums.StatusEnum;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmCouponConsumeRecordMessage;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.StrategistCouponInfo;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.StrategistCouponInfoExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.StrategistCouponInfoMapper;
import java.math.BigDecimal;
import java.text.ParseException;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class CouponConsumeRecordServiceTest {

    @InjectMocks
    private CouponConsumeRecordService couponConsumeRecordService;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private StrategistCouponInfoMapper strategistCouponInfoMapper;

    @Mock
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordsMapper;

    private CouponConsumeRecordService service = new CouponConsumeRecordService();

    private ScrmCouponConsumeRecordMessage createTestRecordMessage() {
        ScrmCouponConsumeRecordMessage message = new ScrmCouponConsumeRecordMessage();
        message.setCouponGroupId("testGroup");
        message.setUnifiedCouponId("testCouponId");
        message.setUserId(123L);
        message.setClientType((byte) 1);
        message.setCouponParValue(new BigDecimal("100"));
        message.setCouponPriceLimit(new BigDecimal("50"));
        message.setOrderId("testOrder");
        message.setBeginTime("2023-01-01 00:00:00");
        message.setEndTime("2023-12-31 23:59:59");
        message.setUseCouponTime("2023-06-01 12:00:00");
        return message;
    }

    private List<StrategistCouponInfo> createTestStrategistCouponInfoList() {
        List<StrategistCouponInfo> list = new ArrayList<>();
        StrategistCouponInfo info = new StrategistCouponInfo();
        info.setAppId("testAppId");
        info.setCouponGroupId("testGroup");
        info.setCreationScene(CreationSceneEnum.MANUAL_UPLOAD.getCode());
        info.setStatus(StatusEnum.COUNTING.getCode());
        list.add(info);
        return list;
    }

    /**
     * Test case: When recordMessage is null
     * Expected: Method should return without any processing
     */
    @Test
    public void testRecordMessageToScrmSceneCouponRecord_NullInput() {
        // arrange
        ScrmCouponConsumeRecordMessage recordMessage = null;
        // act
        couponConsumeRecordService.recordMessageToScrmSceneCouponRecord(recordMessage);
        // assert
        verify(strategistCouponInfoMapper, never()).selectByExample(any());
    }

    /**
     * Test case: When no strategist coupon info found
     * Expected: Method should return without further processing
     */
    @Test
    public void testRecordMessageToScrmSceneCouponRecord_NoStrategistCouponInfo() {
        // arrange
        ScrmCouponConsumeRecordMessage recordMessage = new ScrmCouponConsumeRecordMessage();
        recordMessage.setCouponGroupId("testGroup");
        when(strategistCouponInfoMapper.selectByExample(any(StrategistCouponInfoExample.class))).thenReturn(Collections.emptyList());
        // act
        couponConsumeRecordService.recordMessageToScrmSceneCouponRecord(recordMessage);
        // assert
        verify(scrmSceneCouponRecordsMapper, never()).insert(any());
        verify(scrmSceneCouponRecordsMapper, never()).updateByExample(any(), any());
    }

    /**
     * Test case: When record doesn't exist (count=0)
     * Expected: Should insert new record
     */
    @Test
    public void testRecordMessageToScrmSceneCouponRecord_InsertNewRecord() {
        // arrange
        ScrmCouponConsumeRecordMessage recordMessage = createTestRecordMessage();
        List<StrategistCouponInfo> strategistCouponInfos = createTestStrategistCouponInfoList();
        when(strategistCouponInfoMapper.selectByExample(any(StrategistCouponInfoExample.class))).thenReturn(strategistCouponInfos);
        when(scrmSceneCouponRecordsMapper.countByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(0L);
        when(executeManagementService.getMtXCXUnionIdByMtUserId(anyLong(), anyString())).thenReturn("testUnionId");
        // act
        couponConsumeRecordService.recordMessageToScrmSceneCouponRecord(recordMessage);
        // assert
        verify(scrmSceneCouponRecordsMapper).insert(any(ScrmSceneCouponRecords.class));
        verify(scrmSceneCouponRecordsMapper, never()).updateByExample(any(), any());
    }

    /**
     * Test case: When record exists (count>0)
     * Expected: Should update existing record
     */
    @Test
    public void testRecordMessageToScrmSceneCouponRecord_UpdateExistingRecord() {
        // arrange
        ScrmCouponConsumeRecordMessage recordMessage = createTestRecordMessage();
        List<StrategistCouponInfo> strategistCouponInfos = createTestStrategistCouponInfoList();
        when(strategistCouponInfoMapper.selectByExample(any(StrategistCouponInfoExample.class))).thenReturn(strategistCouponInfos);
        when(scrmSceneCouponRecordsMapper.countByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(1L);
        when(executeManagementService.getMtXCXUnionIdByMtUserId(anyLong(), anyString())).thenReturn("testUnionId");
        // act
        couponConsumeRecordService.recordMessageToScrmSceneCouponRecord(recordMessage);
        // assert
        verify(scrmSceneCouponRecordsMapper, never()).insert(any());
        verify(scrmSceneCouponRecordsMapper).updateByExample(any(ScrmSceneCouponRecords.class), any(ScrmSceneCouponRecordsExample.class));
    }

    /**
     * 测试正常情况：dateStr 是一个有效的日期字符串
     */
    @Test
    public void testGetDateFromStrValidDate() throws Throwable {
        // arrange
        String dateStr = "2023-10-01 12:34:56";
        LocalDateTime expectedDateTime = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Date expectedDate = Date.from(expectedDateTime.atZone(ZoneId.systemDefault()).toInstant());
        // act
        Date result = service.getDateFromStr(dateStr);
        // assert
        assertNotNull(result);
        assertEquals(expectedDate, result);
    }

    /**
     * 测试边界情况：dateStr 为空字符串
     */
    @Test
    public void testGetDateFromStrEmptyString() throws Throwable {
        // arrange
        String dateStr = "";
        // act
        Date result = service.getDateFromStr(dateStr);
        // assert
        assertNull(result);
    }

    /**
     * 测试异常情况：dateStr 为 null
     */
    @Test
    public void testGetDateFromStrNullString() throws Throwable {
        // arrange
        String dateStr = null;
        // act
        // assert
        // 期望抛出 NullPointerException
        assertNull(service.getDateFromStr(dateStr));
    }

    /**
     * 测试异常情况：dateStr 格式不正确
     */
    @Test(expected = java.time.format.DateTimeParseException.class)
    public void testGetDateFromStrInvalidFormat() throws Throwable {
        // arrange
        String dateStr = "2023/10/01 12:34:56";
        // act
        service.getDateFromStr(dateStr);
        // assert
        // 期望抛出 DateTimeParseException
    }
}
