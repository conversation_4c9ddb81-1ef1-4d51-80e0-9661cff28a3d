package com.sankuai.scrm.core.service.couponIntegration.mq.consumer;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import java.lang.reflect.Field;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

public class ScrmCouponConsumeRecordConsumerTest {

    private ScrmCouponConsumeRecordConsumer scrmCouponConsumeRecordConsumer;

    private IConsumerProcessor consumer;

    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        scrmCouponConsumeRecordConsumer = new ScrmCouponConsumeRecordConsumer();
        consumer = Mockito.mock(IConsumerProcessor.class);
        // Use reflection to set the consumer field
        Field consumerField = ScrmCouponConsumeRecordConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(scrmCouponConsumeRecordConsumer, consumer);
    }

    /**
     * 测试destroy方法，消费者为空的情况
     */
    @Test
    public void testDestroyConsumerIsNull() throws Throwable {
        // Use reflection to set the consumer field to null
        Field consumerField = ScrmCouponConsumeRecordConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(scrmCouponConsumeRecordConsumer, null);
        // act
        scrmCouponConsumeRecordConsumer.destroy();
        // assert
        verify(consumer, times(0)).close();
    }

    /**
     * 测试destroy方法，消费者不为空的情况
     */
    @Test
    public void testDestroyConsumerIsNotNull() throws Throwable {
        // act
        scrmCouponConsumeRecordConsumer.destroy();
        // assert
        verify(consumer, times(1)).close();
    }
}
