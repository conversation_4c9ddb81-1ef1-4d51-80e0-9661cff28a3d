package com.sankuai.scrm.core.service.external.contact.service;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.squirrel.client.StoreKey;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.external.contact.request.MobileAddFriendRealTimeRequest;
import com.sankuai.inf.kms.pangolin.api.service.IEncryptService;
import com.sankuai.scrm.core.service.external.contact.config.FriendLionConfig;
import com.sankuai.scrm.core.service.external.contact.domain.MobileAddFriendRealTimeDomainService;
import com.sankuai.scrm.core.service.external.contact.mq.producer.MobileAddFriendRealTimeTaskMsgProducer;
import com.sankuai.service.fe.corp.ds.enums.addfriend.call.CallNumberTypeEnum;
import java.security.GeneralSecurityException;
import java.util.Date;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class AddFriendServiceImplCreateMobileAddFriendRealTimeTest {

    @Mock
    private MobileAddFriendRealTimeDomainService mobileAddFriendRealTimeDomainService;

    @Mock
    private MobileAddFriendRealTimeTaskMsgProducer mobileAddFriendRealTimeTaskMsgProducer;

    @Mock
    private com.dianping.squirrel.client.impl.redis.RedisStoreClient redisClient;

    @Mock
    private IEncryptService phoneEncryptService;

    @InjectMocks
    private AddFriendServiceImpl addFriendServiceImpl;

    private MobileAddFriendRealTimeRequest validRequest;

    private static final int TEST_DAILY_LIMIT = 100;

    private static Integer originalLimit;

    @BeforeEach
    void setUp() {
        validRequest = new MobileAddFriendRealTimeRequest();
        validRequest.setAppId("app123");
        validRequest.setAccountId("account123");
        validRequest.setAddNumber("***********");
        validRequest.setNumberType(CallNumberTypeEnum.PHONE_NUMBER.getCode());
        validRequest.setWelcomeContent("Welcome message");
        // Store original limit value and set test value
        try {
            java.lang.reflect.Field limitField = FriendLionConfig.class.getDeclaredField("limit");
            limitField.setAccessible(true);
            originalLimit = (Integer) limitField.get(null);
            limitField.set(null, TEST_DAILY_LIMIT);
        } catch (Exception e) {
            // Log error but continue with test
            System.err.println("Failed to set FriendLionConfig.limit: " + e.getMessage());
        }
    }

    @AfterEach
    void tearDown() {
        // Restore original limit value
        if (originalLimit != null) {
            try {
                java.lang.reflect.Field limitField = FriendLionConfig.class.getDeclaredField("limit");
                limitField.setAccessible(true);
                limitField.set(null, originalLimit);
            } catch (Exception e) {
                System.err.println("Failed to restore FriendLionConfig.limit: " + e.getMessage());
            }
        }
    }

    /**
     * Test case for null request parameter
     */
    @Test
    public void testCreateMobileAddFriendRealTimeNullRequest() throws Throwable {
        // act
        RemoteResponse<Long> response = addFriendServiceImpl.createMobileAddFriendRealTime(null);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("参数不能为空", response.getMsg());
    }

    /**
     * Test case for empty appId
     */
    @Test
    public void testCreateMobileAddFriendRealTimeEmptyAppId() throws Throwable {
        // arrange
        validRequest.setAppId("");
        // act
        RemoteResponse<Long> response = addFriendServiceImpl.createMobileAddFriendRealTime(validRequest);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("addId不能为空", response.getMsg());
    }

    /**
     * Test case for empty accountId
     */
    @Test
    public void testCreateMobileAddFriendRealTimeEmptyAccountId() throws Throwable {
        // arrange
        validRequest.setAccountId("");
        // act
        RemoteResponse<Long> response = addFriendServiceImpl.createMobileAddFriendRealTime(validRequest);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("企微个人id不能为空", response.getMsg());
    }

    /**
     * Test case for empty addNumber
     */
    @Test
    public void testCreateMobileAddFriendRealTimeEmptyAddNumber() throws Throwable {
        // arrange
        validRequest.setAddNumber("");
        // act
        RemoteResponse<Long> response = addFriendServiceImpl.createMobileAddFriendRealTime(validRequest);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("待添加手机号 | 美团userId不能为空", response.getMsg());
    }

    /**
     * Test case for null numberType
     */
    @Test
    public void testCreateMobileAddFriendRealTimeNullNumberType() throws Throwable {
        // arrange
        validRequest.setNumberType(null);
        // act
        RemoteResponse<Long> response = addFriendServiceImpl.createMobileAddFriendRealTime(validRequest);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("号码类型不能为空", response.getMsg());
    }

    /**
     * Test case for invalid numberType
     */
    @Test
    public void testCreateMobileAddFriendRealTimeInvalidNumberType() throws Throwable {
        // arrange
        validRequest.setNumberType(999);
        // act
        RemoteResponse<Long> response = addFriendServiceImpl.createMobileAddFriendRealTime(validRequest);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("号码类型错误", response.getMsg());
    }

    /**
     * Test case for null welcomeContent
     */
    @Test
    public void testCreateMobileAddFriendRealTimeNullWelcomeContent() throws Throwable {
        // arrange
        validRequest.setWelcomeContent(null);
        // act
        RemoteResponse<Long> response = addFriendServiceImpl.createMobileAddFriendRealTime(validRequest);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("好友验证信息不能为空，且最长限制128", response.getMsg());
    }

    /**
     * Test case for empty welcomeContent
     */
    @Test
    public void testCreateMobileAddFriendRealTimeEmptyWelcomeContent() throws Throwable {
        // arrange
        validRequest.setWelcomeContent("");
        // act
        RemoteResponse<Long> response = addFriendServiceImpl.createMobileAddFriendRealTime(validRequest);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("好友验证信息不能为空，且最长限制128", response.getMsg());
    }

    /**
     * Test case for too long welcomeContent
     */
    @Test
    public void testCreateMobileAddFriendRealTimeTooLongWelcomeContent() throws Throwable {
        // arrange
        StringBuilder longContent = new StringBuilder();
        for (int i = 0; i < 129; i++) {
            longContent.append("a");
        }
        validRequest.setWelcomeContent(longContent.toString());
        // act
        RemoteResponse<Long> response = addFriendServiceImpl.createMobileAddFriendRealTime(validRequest);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("好友验证信息不能为空，且最长限制128", response.getMsg());
    }

    /**
     * Test case for lock acquisition failure
     */
    @Test
    public void testCreateMobileAddFriendRealTimeLockAcquisitionFailure() throws Throwable {
        // arrange
        when(redisClient.setnx(any(StoreKey.class), anyString())).thenReturn(false);
        // act
        RemoteResponse<Long> response = addFriendServiceImpl.createMobileAddFriendRealTime(validRequest);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("该好友已添加或任务已创建", response.getMsg());
        verify(redisClient, never()).delete(any(StoreKey.class));
    }

    /**
     * Test case for existing task check
     */
    @Test
    public void testCreateMobileAddFriendRealTimeExistingTask() throws Throwable {
        // arrange
        when(redisClient.setnx(any(StoreKey.class), anyString())).thenReturn(true);
        when(mobileAddFriendRealTimeDomainService.checkMobileAddFriendRealTimeTaskExist(anyString(), anyString(), anyString(), anyInt())).thenReturn(true);
        // act
        RemoteResponse<Long> response = addFriendServiceImpl.createMobileAddFriendRealTime(validRequest);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("该好友已添加或任务已创建", response.getMsg());
        verify(redisClient).delete(any(StoreKey.class));
    }

    /**
     * Test case for daily limit exceeded
     */
    @Test
    public void testCreateMobileAddFriendRealTimeDailyLimitExceeded() throws Throwable {
        // arrange
        when(redisClient.setnx(any(StoreKey.class), anyString())).thenReturn(true);
        when(mobileAddFriendRealTimeDomainService.checkMobileAddFriendRealTimeTaskExist(anyString(), anyString(), anyString(), anyInt())).thenReturn(false);
        when(mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTaskToday(anyString())).thenReturn((long) TEST_DAILY_LIMIT + 1);
        // act
        RemoteResponse<Long> response = addFriendServiceImpl.createMobileAddFriendRealTime(validRequest);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("该企微个人账号今日添加好友已达上限", response.getMsg());
        verify(redisClient).delete(any(StoreKey.class));
    }

    /**
     * Test case for successful task creation
     */
    @Test
    public void testCreateMobileAddFriendRealTimeSuccess() throws Throwable {
        // arrange
        Long expectedId = 12345L;
        when(redisClient.setnx(any(StoreKey.class), anyString())).thenReturn(true);
        when(mobileAddFriendRealTimeDomainService.checkMobileAddFriendRealTimeTaskExist(anyString(), anyString(), anyString(), anyInt())).thenReturn(false);
        when(mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTaskToday(anyString())).thenReturn((long) TEST_DAILY_LIMIT - 1);
        when(mobileAddFriendRealTimeDomainService.createMobileAddFriendRealTimeTask(any(), any())).thenReturn(expectedId);
        // act
        RemoteResponse<Long> response = addFriendServiceImpl.createMobileAddFriendRealTime(validRequest);
        // assert
        assertTrue(response.isSuccess());
        assertEquals(expectedId, response.getData());
        verify(mobileAddFriendRealTimeTaskMsgProducer).sengMsg(expectedId);
        verify(redisClient).delete(any(StoreKey.class));
    }

    /**
     * Test case for task creation failure
     */
    @Test
    public void testCreateMobileAddFriendRealTimeTaskCreationFailure() throws Throwable {
        // arrange
        when(redisClient.setnx(any(StoreKey.class), anyString())).thenReturn(true);
        when(mobileAddFriendRealTimeDomainService.checkMobileAddFriendRealTimeTaskExist(anyString(), anyString(), anyString(), anyInt())).thenReturn(false);
        when(mobileAddFriendRealTimeDomainService.countMobileAddFriendRealTimeTaskToday(anyString())).thenReturn((long) TEST_DAILY_LIMIT - 1);
        when(mobileAddFriendRealTimeDomainService.createMobileAddFriendRealTimeTask(any(), any())).thenReturn(null);
        // act
        RemoteResponse<Long> response = addFriendServiceImpl.createMobileAddFriendRealTime(validRequest);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("创建任务失败，系统异常", response.getMsg());
        verify(redisClient).delete(any(StoreKey.class));
    }
}
