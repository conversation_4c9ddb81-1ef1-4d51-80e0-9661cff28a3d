package com.sankuai.scrm.core.service.infrastructure.replay.crane;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxExternalTagAcl;
import com.sankuai.scrm.core.service.infrastructure.replay.dal.entity.MarkWxTagLog;
import com.sankuai.scrm.core.service.infrastructure.replay.domain.MarkCorpWxTagDomainService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;

import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class MarkCorpWxTagReplayServiceTest {

    @InjectMocks
    private MarkCorpWxTagReplayService markCorpWxTagReplayService;

    @Mock
    private MarkCorpWxTagDomainService markCorpWxTagDomainService;

    @Mock
    private CorpWxExternalTagAcl corpWxExternalTagAcl;

    private MockedStatic<Lion> lionMockedStatic;

    private MockedStatic<Environment> environmentMockedStatic;

    @AfterEach
    public void tearDown() {
        if (lionMockedStatic != null) {
            lionMockedStatic.close();
        }
        if (environmentMockedStatic != null) {
            environmentMockedStatic.close();
        }
    }

    /**
     * Test case: Empty result list scenario
     * Expected: Method should return early when no records found
     */
    @Test
    public void testMarkWxTagReplay_EmptyResultList() {
        // arrange
        lionMockedStatic = mockStatic(Lion.class);
        environmentMockedStatic = mockStatic(Environment.class);
        when(Environment.getAppName()).thenReturn("test-app");
        when(Lion.getInt(eq("test-app"), eq("mark.wx.tag.replay.max.retry.count"), eq(3))).thenReturn(3);
        when(Lion.getInt(eq("test-app"), eq("mark.wx.tag.replay.max.retry.offset.day"), eq(7))).thenReturn(7);
        when(markCorpWxTagDomainService.queryList(anyInt(), any(), anyString(), anyInt(), anyLong())).thenReturn(new ArrayList<>());
        // act
        markCorpWxTagReplayService.markWxTagReplay();
        // assert
        verify(markCorpWxTagDomainService, times(1)).queryList(anyInt(), any(), anyString(), anyInt(), anyLong());
        verify(corpWxExternalTagAcl, never()).markCordWxTag(any(), any());
    }

    /**
     * Test case: Single page with unique records
     * Expected: Should process all records in single page
     */
    @Test
    public void testMarkWxTagReplay_SinglePageWithUniqueRecords() {
        // arrange
        lionMockedStatic = mockStatic(Lion.class);
        environmentMockedStatic = mockStatic(Environment.class);
        when(Environment.getAppName()).thenReturn("test-app");
        when(Lion.getInt(eq("test-app"), eq("mark.wx.tag.replay.max.retry.count"), eq(3))).thenReturn(3);
        when(Lion.getInt(eq("test-app"), eq("mark.wx.tag.replay.max.retry.offset.day"), eq(7))).thenReturn(7);
        List<MarkWxTagLog> logs = Arrays.asList(createMarkWxTagLog(1L, "key1"), createMarkWxTagLog(2L, "key2"));
        when(markCorpWxTagDomainService.queryList(anyInt(), any(), anyString(), anyInt(), anyLong())).thenReturn(logs).thenReturn(new ArrayList<>());
        // act
        markCorpWxTagReplayService.markWxTagReplay();
        // assert
        verify(markCorpWxTagDomainService, times(1)).queryList(anyInt(), any(), anyString(), anyInt(), anyLong());
        verify(corpWxExternalTagAcl, times(2)).markCordWxTag(any(), any());
    }

    /**
     * Test case: Multiple pages with duplicate records
     * Expected: Should skip duplicate records and process unique ones
     */
    @Test
    public void testMarkWxTagReplay_MultiplePageWithDuplicates() {
        // arrange
        lionMockedStatic = mockStatic(Lion.class);
        environmentMockedStatic = mockStatic(Environment.class);
        when(Environment.getAppName()).thenReturn("test-app");
        when(Lion.getInt(eq("test-app"), eq("mark.wx.tag.replay.max.retry.count"), eq(3))).thenReturn(3);
        when(Lion.getInt(eq("test-app"), eq("mark.wx.tag.replay.max.retry.offset.day"), eq(7))).thenReturn(7);
        List<MarkWxTagLog> firstPage = Arrays.asList(createMarkWxTagLog(1L, "key1"), createMarkWxTagLog(2L, "key2"));
        // duplicate
        List<MarkWxTagLog> // duplicate
        // duplicate
        // new
        secondPage = Arrays.asList(createMarkWxTagLog(3L, "key2"), createMarkWxTagLog(4L, "key3"));
        when(markCorpWxTagDomainService.queryList(anyInt(), any(), anyString(), anyInt(), anyLong())).thenReturn(firstPage).thenReturn(secondPage).thenReturn(new ArrayList<>());
        // act
        markCorpWxTagReplayService.markWxTagReplay();
        // assert
        verify(markCorpWxTagDomainService, times(1)).queryList(anyInt(), any(), anyString(), anyInt(), anyLong());
        verify(corpWxExternalTagAcl, times(2)).markCordWxTag(any(), any());
    }

    /**
     * Test case: Null content in mark log
     * Expected: Should skip records with null content
     */
    @Test
    public void testMarkWxTagReplay_NullContent() {
        // arrange
        lionMockedStatic = mockStatic(Lion.class);
        environmentMockedStatic = mockStatic(Environment.class);
        when(Environment.getAppName()).thenReturn("test-app");
        when(Lion.getInt(eq("test-app"), eq("mark.wx.tag.replay.max.retry.count"), eq(3))).thenReturn(3);
        when(Lion.getInt(eq("test-app"), eq("mark.wx.tag.replay.max.retry.offset.day"), eq(7))).thenReturn(7);
        MarkWxTagLog logWithNullContent = new MarkWxTagLog();
        logWithNullContent.setId(1L);
        logWithNullContent.setUniqueKey("key1");
        logWithNullContent.setContent(null);
        List<MarkWxTagLog> logs = Arrays.asList(logWithNullContent);
        when(markCorpWxTagDomainService.queryList(anyInt(), any(), anyString(), anyInt(), anyLong())).thenReturn(logs).thenReturn(new ArrayList<>());
        // act
        markCorpWxTagReplayService.markWxTagReplay();
        // assert
        verify(markCorpWxTagDomainService, times(1)).queryList(anyInt(), any(), anyString(), anyInt(), anyLong());
        verify(corpWxExternalTagAcl, never()).markCordWxTag(any(), any());
    }

    private MarkWxTagLog createMarkWxTagLog(Long id, String uniqueKey) {
        MarkWxTagLog log = new MarkWxTagLog();
        log.setId(id);
        log.setUniqueKey(uniqueKey);
        log.setContent("{\"data\":{},\"corpId\":\"test\"}");
        log.setAddTime(new Date());
        return log;
    }
}
