package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.infrastructure.acl.UploadWxMediaAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.AttachmentVO;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import com.sankuai.scrm.core.service.infrastructure.vo.WechatMediaResult;
import com.sankuai.scrm.core.service.infrastructure.vo.WxMediaType;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import java.util.HashMap;
import java.util.Map;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackDetailInfoDOMapper;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerGetActivityPageAttachmentVOTest {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private WeChatTokenAcl weChatTokenAcl;

    @Mock
    private UploadWxMediaAcl uploadWxMediaAcl;

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private ShortLinkUtils shortLinkUtils;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO;

    private ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO;

    private ScrmAmProcessOrchestrationProductActivityPageDO pageInfo;

    private Map<String, AttachmentVO> existedAttachmentMap;

    private StepExecuteResultDTO stepExecuteResultDTO;

    @Before
    public void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setProcessOrchestrationNodeId(1L);
        actionAttachmentDTO.setId(1L);
        actionAttachmentDTO.setProcessOrchestrationId(1L);
        actionAttachmentDTO.setProcessOrchestrationVersion("1.0");
        supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageInfo.setId(1L);
        pageInfo.setActivityTitle("Test Activity");
        pageInfo.setThumbPicUrl("http://test.com/pic.jpg");
        pageInfo.setMiniProgramAppId("miniAppId");
        existedAttachmentMap = new HashMap<>();
        stepExecuteResultDTO = new StepExecuteResultDTO();
        stepExecuteResultDTO.setSuccess(true);
        // Setup default mock responses
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneCode("testScene");
        when(productManagementService.getActivSceneCodeDO(any(), any(), any(), any(), any())).thenReturn(sceneCodeDO);
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("http://short.url");
        when(informationGatheringService.queryCommunityDistributor(any())).thenReturn("testDistributor");
    }

    /**
     * Test case for successful attachment retrieval from cache
     */
    @Test
    public void testGetActivityPageAttachmentVO_FromCache() throws Throwable {
        // arrange
        String cacheKey = actionAttachmentDTO.getProcessOrchestrationNodeId() + "-" + pageInfo.getId();
        AttachmentVO cachedAttachment = new AttachmentVO();
        existedAttachmentMap.put(cacheKey, cachedAttachment);
        // act
        AttachmentVO result = officialWxHandler.getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, stepExecuteResultDTO);
        // assert
        assertSame(cachedAttachment, result);
        assertTrue(stepExecuteResultDTO.isSuccess());
    }

    /**
     * Test case for successful new attachment creation
     */
    @Test
    public void testGetActivityPageAttachmentVO_SuccessfulCreation() throws Throwable {
        // arrange
        WeChatTokenResult tokenResult = mock(WeChatTokenResult.class);
        when(tokenResult.isSuccess()).thenReturn(true);
        when(tokenResult.getAccess_token()).thenReturn("test_token");
        WechatMediaResult mediaResult = new WechatMediaResult();
        mediaResult.setMedia_id("test_media_id");
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("testCorpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(tokenResult);
        when(uploadWxMediaAcl.uploadWxTmpMedia(anyString(), any(WxMediaType.class), anyString(), anyBoolean())).thenReturn(mediaResult);
        stepExecuteResultDTO.setSuccess(true);
        // act
        AttachmentVO result = officialWxHandler.getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, stepExecuteResultDTO);
        // assert
        assertNotNull(result);
        assertEquals("miniprogram", result.getMsgtype());
        assertTrue(stepExecuteResultDTO.isSuccess());
    }

    /**
     * Test case for failed token retrieval
     */
    @Test
    public void testGetActivityPageAttachmentVO_TokenRetrievalFailed() throws Throwable {
        // arrange
        WeChatTokenResult tokenResult = mock(WeChatTokenResult.class);
        when(tokenResult.isSuccess()).thenReturn(false);
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("testCorpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(tokenResult);
        // act
        AttachmentVO result = officialWxHandler.getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, stepExecuteResultDTO);
        // assert
        assertNull(result);
        assertFalse(stepExecuteResultDTO.isSuccess());
        assertTrue(stepExecuteResultDTO.isExistedFailedAttachmentUpload());
    }

    /**
     * Test case for failed media upload
     */
    @Test
    public void testGetActivityPageAttachmentVO_MediaUploadFailed() throws Throwable {
        // arrange
        WeChatTokenResult tokenResult = mock(WeChatTokenResult.class);
        when(tokenResult.isSuccess()).thenReturn(true);
        when(tokenResult.getAccess_token()).thenReturn("test_token");
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("testCorpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(tokenResult);
        when(uploadWxMediaAcl.uploadWxTmpMedia(anyString(), any(WxMediaType.class), anyString(), anyBoolean())).thenReturn(null);
        // act
        AttachmentVO result = officialWxHandler.getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, stepExecuteResultDTO);
        // assert
        assertNull(result);
        assertFalse(stepExecuteResultDTO.isSuccess());
        assertTrue(stepExecuteResultDTO.isExistedFailedAttachmentUpload());
    }

    /**
     * Test case for empty thumb pic URL
     */
    @Test
    public void testGetActivityPageAttachmentVO_EmptyThumbPicUrl() throws Throwable {
        // arrange
        pageInfo.setThumbPicUrl("");
        // act
        AttachmentVO result = officialWxHandler.getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, stepExecuteResultDTO);
        // assert
        assertNull(result);
        assertFalse(stepExecuteResultDTO.isSuccess());
    }
}