package com.sankuai.scrm.core.service.envrequestforwarding.filter;

import com.meituan.dorado.rpc.handler.filter.FilterHandler;
import com.meituan.dorado.rpc.meta.RpcInvocation;
import com.meituan.dorado.rpc.meta.RpcResult;
import com.sankuai.dz.srcm.envrequestforwarding.dto.OfflineRequestHandlerResponseDTO;
import com.sankuai.dz.srcm.envrequestforwarding.enums.ResultTypeEnum;
import com.sankuai.scrm.core.service.envrequestforwarding.handle.OfflineRequestHandler;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

public class OfflineThriftFilterTest {

    @InjectMocks
    private OfflineThriftFilter offlineThriftFilter;

    @Mock
    private OfflineRequestHandler offlineRequestHandler;

    @Mock
    private FilterHandler nextHandler;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试场景：OfflineRequestHandler处理成功，返回成功结果
     */
    @Test
    public void testFilterHandlerSuccess() throws Throwable {
        // arrange
        RpcInvocation invocation = mock(RpcInvocation.class);
        OfflineRequestHandlerResponseDTO responseDTO = new OfflineRequestHandlerResponseDTO(ResultTypeEnum.SUCCESS.getCode(), "{\"@class\":\"com.sankuai.service.fe.corp.wx.thrift.GetTokenByCorpIdRequest\",\"corpId\":\"ww6155e0a24f071a89\",\"setCorpId\":true}");
        when(offlineRequestHandler.dispatch(invocation)).thenReturn(responseDTO);

        // act
        RpcResult result = offlineThriftFilter.filter(invocation, nextHandler);

        // assert
        assertNotNull(result);
        assertEquals("GetTokenByCorpIdRequest(corpId:ww6155e0a24f071a89)", result.getReturnVal().toString());
    }

    /**
     * 测试场景：OfflineRequestHandler处理失败，调用nextHandler处理
     */
    @Test
    public void testFilterHandlerFail() throws Throwable {
        // arrange
        RpcInvocation invocation = mock(RpcInvocation.class);
        OfflineRequestHandlerResponseDTO responseDTO = new OfflineRequestHandlerResponseDTO(ResultTypeEnum.FAIL.getCode(), null);
        when(offlineRequestHandler.dispatch(invocation)).thenReturn(responseDTO);
        RpcResult expectedRpcResult = new RpcResult();
        when(nextHandler.handle(invocation)).thenReturn(expectedRpcResult);

        // act
        RpcResult result = offlineThriftFilter.filter(invocation, nextHandler);

        // assert
        assertNotNull(result);
        assertSame(expectedRpcResult, result);
    }

    /**
     * 测试场景：OfflineRequestHandler抛出异常，调用nextHandler处理
     */
    @Test
    public void testFilterHandlerException() throws Throwable {
        // arrange
        RpcInvocation invocation = mock(RpcInvocation.class);
        when(offlineRequestHandler.dispatch(invocation)).thenThrow(new RuntimeException("Dispatch error"));
        RpcResult expectedRpcResult = new RpcResult();
        when(nextHandler.handle(invocation)).thenReturn(expectedRpcResult);

        // act
        RpcResult result = offlineThriftFilter.filter(invocation, nextHandler);

        // assert
        assertNotNull(result);
        assertSame(expectedRpcResult, result);
    }

    /**
     * 测试场景：OfflineRequestHandler处理成功，但返回结果无法反序列化，调用nextHandler处理
     */
    @Test
    public void testFilterHandlerDeserializeFail() throws Throwable {
        // arrange
        RpcInvocation invocation = mock(RpcInvocation.class);
        OfflineRequestHandlerResponseDTO responseDTO = new OfflineRequestHandlerResponseDTO(ResultTypeEnum.SUCCESS.getCode(), "not a json string");
        when(offlineRequestHandler.dispatch(invocation)).thenReturn(responseDTO);
        RpcResult expectedRpcResult = new RpcResult();
        when(nextHandler.handle(invocation)).thenReturn(expectedRpcResult);

        // act
        RpcResult result = offlineThriftFilter.filter(invocation, nextHandler);

        // assert
        assertNotNull(result);
        assertSame(expectedRpcResult, result);
    }
}
