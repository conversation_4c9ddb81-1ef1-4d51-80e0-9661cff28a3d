package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DzBizDataUserTrackConsumerTest {

    @Mock
    private IConsumerProcessor mockConsumer;

    @InjectMocks
    private DzBizDataUserTrackConsumer dzBizDataUserTrackConsumer;

    private void setPrivateConsumerField(DzBizDataUserTrackConsumer instance, IConsumerProcessor consumer) throws NoSuchFieldException, IllegalAccessException {
        Field field = DzBizDataUserTrackConsumer.class.getDeclaredField("consumer");
        field.setAccessible(true);
        field.set(instance, consumer);
    }

    /**
     * Test case when consumer is null - should do nothing
     */
    @Test
    public void testDestroyWhenConsumerIsNull() throws Throwable {
        // arrange
        DzBizDataUserTrackConsumer consumer = new DzBizDataUserTrackConsumer();
        setPrivateConsumerField(consumer, null);
        // act & assert
        assertDoesNotThrow(() -> consumer.destroy());
    }

    /**
     * Test case when consumer is not null - should call close()
     */
    @Test
    public void testDestroyWhenConsumerExists() throws Throwable {
        // arrange
        DzBizDataUserTrackConsumer consumer = new DzBizDataUserTrackConsumer();
        setPrivateConsumerField(consumer, mockConsumer);
        // act
        consumer.destroy();
        // assert
        verify(mockConsumer).close();
        verifyNoMoreInteractions(mockConsumer);
    }

    /**
     * Test case when consumer exists but close() throws exception
     */
    @Test
    public void testDestroyWhenConsumerCloseThrowsException() throws Throwable {
        // arrange
        DzBizDataUserTrackConsumer consumer = new DzBizDataUserTrackConsumer();
        setPrivateConsumerField(consumer, mockConsumer);
        Exception expectedException = new Exception("Close failed");
        doThrow(expectedException).when(mockConsumer).close();
        // act & assert
        Exception actualException = assertThrows(Exception.class, () -> consumer.destroy());
        verify(mockConsumer).close();
        verifyNoMoreInteractions(mockConsumer);
    }

    @Test
    public void testAfterPropertiesSetSuccess() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockProcessor = mock(IConsumerProcessor.class);
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenReturn(mockProcessor);
            dzBizDataUserTrackConsumer.afterPropertiesSet();
            mockedMafkaClient.verify(() -> MafkaClient.buildConsumerFactory(any(Properties.class), eq("dz_biz_data_user_track_custom")));
            verify(mockProcessor).recvMessageWithParallel(eq(String.class), any());
        }
    }

    @Test
    public void testAfterPropertiesSetWhenBuildConsumerFactoryThrowsException() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenThrow(new RuntimeException("Build failed"));
            assertThrows(RuntimeException.class, () -> dzBizDataUserTrackConsumer.afterPropertiesSet());
            mockedMafkaClient.verify(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString()));
        }
    }

    @Test
    public void testAfterPropertiesSetWhenRecvMessageThrowsException() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockProcessor = mock(IConsumerProcessor.class);
            doThrow(new RuntimeException("Recv failed")).when(mockProcessor).recvMessageWithParallel(any(), any());
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenReturn(mockProcessor);
            assertThrows(RuntimeException.class, () -> dzBizDataUserTrackConsumer.afterPropertiesSet());
            verify(mockProcessor).recvMessageWithParallel(any(), any());
        }
    }

    @Test
    public void testAfterPropertiesSetWhenCalledMultipleTimes() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockProcessor1 = mock(IConsumerProcessor.class);
            IConsumerProcessor mockProcessor2 = mock(IConsumerProcessor.class);
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenReturn(mockProcessor1).thenReturn(mockProcessor2);
            dzBizDataUserTrackConsumer.afterPropertiesSet();
            dzBizDataUserTrackConsumer.afterPropertiesSet();
            mockedMafkaClient.verify(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString()), times(2));
            verify(mockProcessor1).recvMessageWithParallel(any(), any());
            verify(mockProcessor2).recvMessageWithParallel(any(), any());
        }
    }

    @Test
    public void testAfterPropertiesSetWhenRequiredPropertiesMissing() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenThrow(new IllegalArgumentException("Missing required properties"));
            assertThrows(IllegalArgumentException.class, () -> dzBizDataUserTrackConsumer.afterPropertiesSet());
            mockedMafkaClient.verify(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString()));
        }
    }

    @Test
    public void testRecvMessageWithBlackListedPageId() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, null, "This message contains page ID 42291648");
        MessagetContext messagetContext = new MessagetContext();

        try (MockedStatic<Lion> lionMockedStatic = mockStatic(Lion.class)) {
            lionMockedStatic.when(() -> Lion.getList(any(), any(), any(), any()))
                    .thenReturn(Arrays.asList("42291648", "43048887", "40459248"));
            lionMockedStatic.when(() -> Lion.getBoolean(any(), any(), any()))
                    .thenReturn(true);
            // act
            ConsumeStatus result = (ConsumeStatus) ReflectionTestUtils.invokeMethod(dzBizDataUserTrackConsumer, "recvMessage", message, messagetContext);

            // assert
            org.junit.jupiter.api.Assertions.assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        }
    }
}
