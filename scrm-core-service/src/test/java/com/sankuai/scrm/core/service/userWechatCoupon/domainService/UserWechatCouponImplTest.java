package com.sankuai.scrm.core.service.userWechatCoupon.domainService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.unified.coupon.manage.api.UnifiedCouponInfoService;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.request.BatchLoadCouponRequest;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import com.sankuai.dz.srcm.userWechatCoupon.dto.UserWechatCouponRequest;
import com.sankuai.scrm.core.service.userWechatCoupon.dal.entity.ScrmUserWechatCoupon;
import com.sankuai.scrm.core.service.userWechatCoupon.dal.example.ScrmUserWechatCouponExample;
import com.sankuai.scrm.core.service.userWechatCoupon.dal.mapper.ScrmUserWechatCouponMapper;
import java.lang.reflect.Constructor;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class UserWechatCouponImplTest {

    @Mock
    private ScrmUserWechatCouponMapper scrmUserWechatCouponMapper;

    @InjectMocks
    private UserWechatCouponImpl userWechatCouponImpl;

    @Mock
    private UnifiedCouponInfoService unifiedCouponInfoService;

    private void invokeUpdateUsed(ScrmUserWechatCoupon coupon) throws Exception {
        Method method = UserWechatCouponImpl.class.getDeclaredMethod("updateUsed", ScrmUserWechatCoupon.class);
        method.setAccessible(true);
        method.invoke(userWechatCouponImpl, coupon);
    }

    private boolean invokeCheckParam(UserWechatCouponRequest request) throws Exception {
        Method method = UserWechatCouponImpl.class.getDeclaredMethod("checkParam", UserWechatCouponRequest.class);
        method.setAccessible(true);
        return (boolean) method.invoke(userWechatCouponImpl, request);
    }

    private List<ScrmUserWechatCoupon> invokePrivateMethod(UserWechatCouponRequest request) throws Exception {
        Method method = UserWechatCouponImpl.class.getDeclaredMethod("getScrmUserWechantCoupons", UserWechatCouponRequest.class);
        method.setAccessible(true);
        return (List<ScrmUserWechatCoupon>) method.invoke(userWechatCouponImpl, request);
    }

    private List<String> invokePrivateMethod(UserWechatCouponRequest request, List<ScrmUserWechatCoupon> coupons) throws Exception {
        Method method = UserWechatCouponImpl.class.getDeclaredMethod("getUserWechatCouponResponses", UserWechatCouponRequest.class, List.class);
        method.setAccessible(true);
        return (List<String>) method.invoke(userWechatCouponImpl, request, coupons);
    }

    private UnifiedCouponDTO createUnifiedCouponDTO(String id, boolean used, boolean available) throws Exception {
        Constructor<UnifiedCouponDTO> constructor = UnifiedCouponDTO.class.getDeclaredConstructor();
        constructor.setAccessible(true);
        UnifiedCouponDTO dto = constructor.newInstance();
        Method setId = UnifiedCouponDTO.class.getMethod("setUnifiedCouponId", String.class);
        Method setUsed = UnifiedCouponDTO.class.getMethod("setUsed", boolean.class);
        Method setAvailable = UnifiedCouponDTO.class.getMethod("setAvailable", boolean.class);
        setId.invoke(dto, id);
        setUsed.invoke(dto, used);
        setAvailable.invoke(dto, available);
        return dto;
    }

    /**
     * Test updating coupon with all required fields and no optional fields
     */
    @Test
    public void testUpdateUsedWithRequiredFieldsOnly() throws Throwable {
        // arrange
        ScrmUserWechatCoupon coupon = new ScrmUserWechatCoupon();
        coupon.setCouponid("coupon123");
        coupon.setCouponcode("code123");
        coupon.setCategoryids("cat1,cat2");
        coupon.setPoiid(1001L);
        coupon.setMtuserid(2001L);
        coupon.setMtcityid(null);
        coupon.setDpcityid(null);
        when(scrmUserWechatCouponMapper.updateByExample(eq(coupon), any(ScrmUserWechatCouponExample.class))).thenReturn(1);
        // act
        invokeUpdateUsed(coupon);
        // assert
        verify(scrmUserWechatCouponMapper, times(1)).updateByExample(eq(coupon), any(ScrmUserWechatCouponExample.class));
    }

    /**
     * Test updating coupon with mtcityid not null
     */
    @Test
    public void testUpdateUsedWithMtCityId() throws Throwable {
        // arrange
        ScrmUserWechatCoupon coupon = new ScrmUserWechatCoupon();
        coupon.setCouponid("coupon123");
        coupon.setCouponcode("code123");
        coupon.setCategoryids("cat1,cat2");
        coupon.setPoiid(1001L);
        coupon.setMtuserid(2001L);
        coupon.setMtcityid(10);
        coupon.setDpcityid(null);
        when(scrmUserWechatCouponMapper.updateByExample(eq(coupon), any(ScrmUserWechatCouponExample.class))).thenReturn(1);
        // act
        invokeUpdateUsed(coupon);
        // assert
        verify(scrmUserWechatCouponMapper, times(1)).updateByExample(eq(coupon), any(ScrmUserWechatCouponExample.class));
    }

    /**
     * Test updating coupon with dpcityid not null
     */
    @Test
    public void testUpdateUsedWithDpCityId() throws Throwable {
        // arrange
        ScrmUserWechatCoupon coupon = new ScrmUserWechatCoupon();
        coupon.setCouponid("coupon123");
        coupon.setCouponcode("code123");
        coupon.setCategoryids("cat1,cat2");
        coupon.setPoiid(1001L);
        coupon.setMtuserid(2001L);
        coupon.setMtcityid(null);
        coupon.setDpcityid(20);
        when(scrmUserWechatCouponMapper.updateByExample(eq(coupon), any(ScrmUserWechatCouponExample.class))).thenReturn(1);
        // act
        invokeUpdateUsed(coupon);
        // assert
        verify(scrmUserWechatCouponMapper, times(1)).updateByExample(eq(coupon), any(ScrmUserWechatCouponExample.class));
    }

    /**
     * Test updating coupon with both mtcityid and dpcityid not null
     */
    @Test
    public void testUpdateUsedWithBothCityIds() throws Throwable {
        // arrange
        ScrmUserWechatCoupon coupon = new ScrmUserWechatCoupon();
        coupon.setCouponid("coupon123");
        coupon.setCouponcode("code123");
        coupon.setCategoryids("cat1,cat2");
        coupon.setPoiid(1001L);
        coupon.setMtuserid(2001L);
        coupon.setMtcityid(10);
        coupon.setDpcityid(20);
        when(scrmUserWechatCouponMapper.updateByExample(eq(coupon), any(ScrmUserWechatCouponExample.class))).thenReturn(1);
        // act
        invokeUpdateUsed(coupon);
        // assert
        verify(scrmUserWechatCouponMapper, times(1)).updateByExample(eq(coupon), any(ScrmUserWechatCouponExample.class));
    }

    /**
     * Test database update failure scenario
     */
    @Test
    public void testUpdateUsedWithDatabaseFailure() throws Throwable {
        // arrange
        ScrmUserWechatCoupon coupon = new ScrmUserWechatCoupon();
        coupon.setCouponid("coupon123");
        coupon.setCouponcode("code123");
        coupon.setCategoryids("cat1,cat2");
        coupon.setPoiid(1001L);
        coupon.setMtuserid(2001L);
        coupon.setMtcityid(10);
        coupon.setDpcityid(20);
        when(scrmUserWechatCouponMapper.updateByExample(eq(coupon), any(ScrmUserWechatCouponExample.class))).thenReturn(0);
        // act
        invokeUpdateUsed(coupon);
        // assert
        verify(scrmUserWechatCouponMapper, times(1)).updateByExample(eq(coupon), any(ScrmUserWechatCouponExample.class));
    }

    /**
     * Test case when UserWechatCouponRequest is null
     * Expected: Should return true as it's invalid
     */
    @Test
    void testCheckParamWhenRequestIsNull() throws Throwable {
        // act
        boolean result = invokeCheckParam(null);
        // assert
        assertTrue(result);
    }

    /**
     * Test case when categoryIds is null
     * Expected: Should return true as it's invalid
     */
    @Test
    void testCheckParamWhenCategoryIdsIsNull() throws Throwable {
        // arrange
        UserWechatCouponRequest request = mock(UserWechatCouponRequest.class);
        when(request.getCategoryIds()).thenReturn(null);
        // act
        boolean result = invokeCheckParam(request);
        // assert
        assertTrue(result);
        // Only called once since method returns early
        verify(request).getCategoryIds();
    }

    /**
     * Test case when categoryIds is empty
     * Expected: Should return true as it's invalid
     */
    @Test
    void testCheckParamWhenCategoryIdsIsEmpty() throws Throwable {
        // arrange
        UserWechatCouponRequest request = mock(UserWechatCouponRequest.class);
        when(request.getCategoryIds()).thenReturn("");
        // act
        boolean result = invokeCheckParam(request);
        // assert
        assertTrue(result);
        // Checks both null and empty conditions
        verify(request, times(2)).getCategoryIds();
    }

    /**
     * Test case when poiId is null
     * Expected: Should return true as it's invalid
     */
    @Test
    void testCheckParamWhenPoiIdIsNull() throws Throwable {
        // arrange
        UserWechatCouponRequest request = mock(UserWechatCouponRequest.class);
        when(request.getCategoryIds()).thenReturn("1,2");
        when(request.getPoiId()).thenReturn(null);
        // act
        boolean result = invokeCheckParam(request);
        // assert
        assertTrue(result);
        // Checks both null and empty conditions
        verify(request, times(2)).getCategoryIds();
        verify(request).getPoiId();
    }

    /**
     * Test case when both city IDs are null
     * Expected: Should return true as it's invalid
     */
    @Test
    void testCheckParamWhenBothCityIdsAreNull() throws Throwable {
        // arrange
        UserWechatCouponRequest request = mock(UserWechatCouponRequest.class);
        when(request.getCategoryIds()).thenReturn("1,2");
        when(request.getPoiId()).thenReturn(1L);
        when(request.getMtCityId()).thenReturn(null);
        when(request.getDpCityId()).thenReturn(null);
        // act
        boolean result = invokeCheckParam(request);
        // assert
        assertTrue(result);
        // Checks both null and empty conditions
        verify(request, times(2)).getCategoryIds();
        verify(request).getPoiId();
        verify(request).getMtCityId();
        verify(request).getDpCityId();
    }

    /**
     * Test case when mtUserId is null
     * Expected: Should return true as it's invalid
     */
    @Test
    void testCheckParamWhenMtUserIdIsNull() throws Throwable {
        // arrange
        UserWechatCouponRequest request = mock(UserWechatCouponRequest.class);
        when(request.getCategoryIds()).thenReturn("1,2");
        when(request.getPoiId()).thenReturn(1L);
        when(request.getMtCityId()).thenReturn(1);
        when(request.getMtUserId()).thenReturn(null);
        // act
        boolean result = invokeCheckParam(request);
        // assert
        assertTrue(result);
        // Checks both null and empty conditions
        verify(request, times(2)).getCategoryIds();
        verify(request).getPoiId();
        verify(request).getMtCityId();
        verify(request).getMtUserId();
    }

    /**
     * Test case when all required parameters are valid
     * Expected: Should return false as it's valid
     */
    @Test
    void testCheckParamWhenAllParametersAreValid() throws Throwable {
        // arrange
        UserWechatCouponRequest request = mock(UserWechatCouponRequest.class);
        when(request.getCategoryIds()).thenReturn("1,2");
        when(request.getPoiId()).thenReturn(1L);
        when(request.getMtCityId()).thenReturn(1);
        when(request.getMtUserId()).thenReturn(1L);
        // act
        boolean result = invokeCheckParam(request);
        // assert
        assertFalse(result);
        // Checks both null and empty conditions
        verify(request, times(2)).getCategoryIds();
        verify(request).getPoiId();
        verify(request).getMtCityId();
        verify(request).getMtUserId();
    }

    /**
     * Test case when some optional parameters are missing but required ones present
     * Expected: Should return false as it's still valid
     */
    @Test
    void testCheckParamWhenOptionalParametersMissing() throws Throwable {
        // arrange
        UserWechatCouponRequest request = mock(UserWechatCouponRequest.class);
        when(request.getCategoryIds()).thenReturn("1,2");
        when(request.getPoiId()).thenReturn(1L);
        when(request.getMtUserId()).thenReturn(1L);
        // optional field
        // act
        boolean result = invokeCheckParam(request);
        // assert
        assertFalse(result);
        // Checks both null and empty conditions
        verify(request, times(2)).getCategoryIds();
        verify(request).getPoiId();
        verify(request).getMtUserId();
        // shouldn't check optional field
        verify(request, never()).getDpCityId();
    }

    /**
     * Test case when all optional fields are null
     */
    @Test
    public void testGetScrmUserWechantCouponsAllOptionalFieldsNull() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setCategoryIds("1,2,3");
        request.setPoiId(100L);
        request.setMtUserId(200L);
        ScrmUserWechatCoupon expectedCoupon = new ScrmUserWechatCoupon();
        expectedCoupon.setId(1L);
        when(scrmUserWechatCouponMapper.selectByExample(any(ScrmUserWechatCouponExample.class))).thenReturn(Collections.singletonList(expectedCoupon));
        // act
        List<ScrmUserWechatCoupon> result = invokePrivateMethod(request);
        // assert
        assertEquals(1, result.size());
        assertEquals(expectedCoupon, result.get(0));
    }

    /**
     * Test case when all optional fields have values
     */
    @Test
    public void testGetScrmUserWechantCouponsAllOptionalFieldsPresent() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setCategoryIds("1,2,3");
        request.setPoiId(100L);
        request.setMtUserId(200L);
        request.setUnionId("union123");
        request.setDpCityId(10);
        request.setMtCityId(20);
        ScrmUserWechatCoupon expectedCoupon = new ScrmUserWechatCoupon();
        expectedCoupon.setId(1L);
        when(scrmUserWechatCouponMapper.selectByExample(any(ScrmUserWechatCouponExample.class))).thenReturn(Collections.singletonList(expectedCoupon));
        // act
        List<ScrmUserWechatCoupon> result = invokePrivateMethod(request);
        // assert
        assertEquals(1, result.size());
        assertEquals(expectedCoupon, result.get(0));
    }

    /**
     * Test case when only unionId is present
     */
    @Test
    public void testGetScrmUserWechantCouponsOnlyUnionIdPresent() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setCategoryIds("1,2,3");
        request.setPoiId(100L);
        request.setMtUserId(200L);
        request.setUnionId("union123");
        ScrmUserWechatCoupon expectedCoupon = new ScrmUserWechatCoupon();
        expectedCoupon.setId(1L);
        when(scrmUserWechatCouponMapper.selectByExample(any(ScrmUserWechatCouponExample.class))).thenReturn(Collections.singletonList(expectedCoupon));
        // act
        List<ScrmUserWechatCoupon> result = invokePrivateMethod(request);
        // assert
        assertEquals(1, result.size());
        assertEquals(expectedCoupon, result.get(0));
    }

    /**
     * Test case when only dpCityId is present
     */
    @Test
    public void testGetScrmUserWechantCouponsOnlyDpCityIdPresent() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setCategoryIds("1,2,3");
        request.setPoiId(100L);
        request.setMtUserId(200L);
        request.setDpCityId(10);
        ScrmUserWechatCoupon expectedCoupon = new ScrmUserWechatCoupon();
        expectedCoupon.setId(1L);
        when(scrmUserWechatCouponMapper.selectByExample(any(ScrmUserWechatCouponExample.class))).thenReturn(Collections.singletonList(expectedCoupon));
        // act
        List<ScrmUserWechatCoupon> result = invokePrivateMethod(request);
        // assert
        assertEquals(1, result.size());
        assertEquals(expectedCoupon, result.get(0));
    }

    /**
     * Test case when only mtCityId is present
     */
    @Test
    public void testGetScrmUserWechantCouponsOnlyMtCityIdPresent() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setCategoryIds("1,2,3");
        request.setPoiId(100L);
        request.setMtUserId(200L);
        request.setMtCityId(20);
        ScrmUserWechatCoupon expectedCoupon = new ScrmUserWechatCoupon();
        expectedCoupon.setId(1L);
        when(scrmUserWechatCouponMapper.selectByExample(any(ScrmUserWechatCouponExample.class))).thenReturn(Collections.singletonList(expectedCoupon));
        // act
        List<ScrmUserWechatCoupon> result = invokePrivateMethod(request);
        // assert
        assertEquals(1, result.size());
        assertEquals(expectedCoupon, result.get(0));
    }

    /**
     * Test case when no records found in database
     */
    @Test
    public void testGetScrmUserWechantCouponsNoRecordsFound() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setCategoryIds("1,2,3");
        request.setPoiId(100L);
        request.setMtUserId(200L);
        when(scrmUserWechatCouponMapper.selectByExample(any(ScrmUserWechatCouponExample.class))).thenReturn(Collections.emptyList());
        // act
        List<ScrmUserWechatCoupon> result = invokePrivateMethod(request);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case when multiple records found in database
     */
    @Test
    public void testGetScrmUserWechantCouponsMultipleRecordsFound() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setCategoryIds("1,2,3");
        request.setPoiId(100L);
        request.setMtUserId(200L);
        ScrmUserWechatCoupon coupon1 = new ScrmUserWechatCoupon();
        coupon1.setId(1L);
        ScrmUserWechatCoupon coupon2 = new ScrmUserWechatCoupon();
        coupon2.setId(2L);
        when(scrmUserWechatCouponMapper.selectByExample(any(ScrmUserWechatCouponExample.class))).thenReturn(Arrays.asList(coupon1, coupon2));
        // act
        List<ScrmUserWechatCoupon> result = invokePrivateMethod(request);
        // assert
        assertEquals(2, result.size());
        assertTrue(result.contains(coupon1));
        assertTrue(result.contains(coupon2));
    }

    /**
     * Test case when input request is null
     */
    @Test
    public void testGetScrmUserWechantCouponsNullRequest() throws Throwable {
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> {
            invokePrivateMethod(null);
        });
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    /**
     * Test case when mandatory field categoryIds is missing
     */
    @Test
    public void testGetScrmUserWechantCouponsMissingCategoryIds() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setPoiId(100L);
        request.setMtUserId(200L);
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> {
            invokePrivateMethod(request);
        });
        assertEquals("Value for categoryids cannot be null", exception.getCause().getMessage());
    }

    /**
     * Test case when mandatory field poiId is missing
     */
    @Test
    public void testGetScrmUserWechantCouponsMissingPoiId() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setCategoryIds("1,2,3");
        request.setMtUserId(200L);
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> {
            invokePrivateMethod(request);
        });
        assertEquals("Value for poiid cannot be null", exception.getCause().getMessage());
    }

    /**
     * Test case when mandatory field mtUserId is missing
     */
    @Test
    public void testGetScrmUserWechantCouponsMissingMtUserId() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setCategoryIds("1,2,3");
        request.setPoiId(100L);
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> {
            invokePrivateMethod(request);
        });
        assertEquals("Value for mtuserid cannot be null", exception.getCause().getMessage());
    }

    /**
     * Test successful case with available coupons
     */
    @Test
    public void testGetUserWechatCouponResponsesSuccessWithAvailableCoupons() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setMtUserId(123L);
        ScrmUserWechatCoupon coupon1 = new ScrmUserWechatCoupon();
        coupon1.setCouponid("1001");
        ScrmUserWechatCoupon coupon2 = new ScrmUserWechatCoupon();
        coupon2.setCouponid("1002");
        List<ScrmUserWechatCoupon> coupons = Arrays.asList(coupon1, coupon2);
        UnifiedCouponDTO dto1 = createUnifiedCouponDTO("1001", false, true);
        UnifiedCouponDTO dto2 = createUnifiedCouponDTO("1002", false, true);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> response = new UnifiedCouponManageResponse<>();
        response.setSuccess(true);
        response.setResult(Arrays.asList(dto1, dto2));
        when(unifiedCouponInfoService.batchLoadCoupon(any(BatchLoadCouponRequest.class))).thenReturn(response);
        // act
        List<String> result = invokePrivateMethod(request, coupons);
        // assert
        assertEquals(2, result.size());
        assertTrue(result.contains("1001"));
        assertTrue(result.contains("1002"));
    }

    /**
     * Test case where some coupons are used/unavailable
     */
    @Test
    public void testGetUserWechatCouponResponsesWithMixedAvailability() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setMtUserId(123L);
        ScrmUserWechatCoupon coupon1 = new ScrmUserWechatCoupon();
        coupon1.setCouponid("1001");
        ScrmUserWechatCoupon coupon2 = new ScrmUserWechatCoupon();
        coupon2.setCouponid("1002");
        List<ScrmUserWechatCoupon> coupons = Arrays.asList(coupon1, coupon2);
        UnifiedCouponDTO dto1 = createUnifiedCouponDTO("1001", false, true);
        UnifiedCouponDTO dto2 = createUnifiedCouponDTO("1002", true, false);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> response = new UnifiedCouponManageResponse<>();
        response.setSuccess(true);
        response.setResult(Arrays.asList(dto1, dto2));
        when(unifiedCouponInfoService.batchLoadCoupon(any(BatchLoadCouponRequest.class))).thenReturn(response);
        // act
        List<String> result = invokePrivateMethod(request, coupons);
        // assert
        assertEquals(1, result.size());
        assertTrue(result.contains("1001"));
        assertFalse(result.contains("1002"));
    }

    /**
     * Test case where service call fails
     */
    @Test
    public void testGetUserWechatCouponResponsesWhenServiceFails() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setMtUserId(123L);
        ScrmUserWechatCoupon coupon1 = new ScrmUserWechatCoupon();
        coupon1.setCouponid("1001");
        List<ScrmUserWechatCoupon> coupons = Collections.singletonList(coupon1);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> response = new UnifiedCouponManageResponse<>();
        response.setSuccess(false);
        response.setResultMsg("Service error");
        when(unifiedCouponInfoService.batchLoadCoupon(any(BatchLoadCouponRequest.class))).thenReturn(response);
        // act
        List<String> result = invokePrivateMethod(request, coupons);
        // assert
        assertNull(result);
    }

    /**
     * Test case with empty coupon list input
     */
    @Test
    public void testGetUserWechatCouponResponsesWithEmptyCouponList() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setMtUserId(123L);
        List<ScrmUserWechatCoupon> coupons = Collections.emptyList();
        // Mock the service response for empty list case
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> response = new UnifiedCouponManageResponse<>();
        response.setSuccess(true);
        response.setResult(Collections.emptyList());
        when(unifiedCouponInfoService.batchLoadCoupon(any(BatchLoadCouponRequest.class))).thenReturn(response);
        // act
        List<String> result = invokePrivateMethod(request, coupons);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case with null coupon list input
     */
    @Test
    public void testGetUserWechatCouponResponsesWithNullCouponList() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setMtUserId(123L);
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> {
            invokePrivateMethod(request, null);
        });
        // Verify the root cause is NPE
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    /**
     * Test case where service returns null result
     */
    @Test
    public void testGetUserWechatCouponResponsesWhenServiceReturnsNullResult() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setMtUserId(123L);
        ScrmUserWechatCoupon coupon1 = new ScrmUserWechatCoupon();
        coupon1.setCouponid("1001");
        List<ScrmUserWechatCoupon> coupons = Collections.singletonList(coupon1);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> response = new UnifiedCouponManageResponse<>();
        response.setSuccess(true);
        response.setResult(null);
        when(unifiedCouponInfoService.batchLoadCoupon(any(BatchLoadCouponRequest.class))).thenReturn(response);
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> {
            invokePrivateMethod(request, coupons);
        });
        // Verify the root cause is NPE
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    /**
     * Test case where all coupons are used/unavailable
     */
    @Test
    public void testGetUserWechatCouponResponsesWhenAllCouponsUnavailable() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        request.setMtUserId(123L);
        ScrmUserWechatCoupon coupon1 = new ScrmUserWechatCoupon();
        coupon1.setCouponid("1001");
        ScrmUserWechatCoupon coupon2 = new ScrmUserWechatCoupon();
        coupon2.setCouponid("1002");
        List<ScrmUserWechatCoupon> coupons = Arrays.asList(coupon1, coupon2);
        UnifiedCouponDTO dto1 = createUnifiedCouponDTO("1001", true, false);
        UnifiedCouponDTO dto2 = createUnifiedCouponDTO("1002", true, false);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> response = new UnifiedCouponManageResponse<>();
        response.setSuccess(true);
        response.setResult(Arrays.asList(dto1, dto2));
        when(unifiedCouponInfoService.batchLoadCoupon(any(BatchLoadCouponRequest.class))).thenReturn(response);
        // act
        List<String> result = invokePrivateMethod(request, coupons);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
