package com.sankuai.scrm.core.service.privatelive.community.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.privatelive.community.dto.CommunityLiveUserIntentionProductDTO;
import com.sankuai.dz.srcm.privatelive.community.dto.CommunityUserIntentionProductRequest;
import com.sankuai.scrm.core.service.privatelive.community.domain.PrivateLiveCommunityDomainService;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmLiveUserProductActionStat;
import com.sankuai.scrm.core.service.user.domain.PrivateUserIntentionProductDomainService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.HashMap;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveCommunityServiceImplTest {

    @Mock
    private PrivateLiveCommunityDomainService privateLiveCommunityDomainService;

    @Mock
    private PrivateUserIntentionProductDomainService privateUserIntentionProductDomainService;

    @InjectMocks
    private PrivateLiveCommunityServiceImpl privateLiveCommunityService;

    @Before
    public void setUp() throws Exception {
        // 初始化Mock对象
    }

    /**
     * 测试 liveId 为空的情况
     */
    @Test
    public void testQueryIntentionProductByUnionIdLiveIdEmpty() {
        // arrange
        CommunityUserIntentionProductRequest request = new CommunityUserIntentionProductRequest();
        request.setLiveId("");
        request.setConsultantTaskId(1L);
        request.setUnionId(Arrays.asList("unionId1", "unionId2"));

        // act
        RemoteResponse<CommunityLiveUserIntentionProductDTO> response = privateLiveCommunityService.queryIntentionProductByUnionId(request);

        // assert
        assertFalse("应当返回失败", response.isSuccess());
        assertEquals("liveId不能为空", response.getMsg());
    }

    /**
     * 测试 consultantTaskId 为空的情况
     */
    @Test
    public void testQueryIntentionProductByUnionIdConsultantTaskIdNull() {
        // arrange
        CommunityUserIntentionProductRequest request = new CommunityUserIntentionProductRequest();
        request.setLiveId("liveId");
        request.setConsultantTaskId(null);
        request.setUnionId(Arrays.asList("unionId1", "unionId2"));

        // act
        RemoteResponse<CommunityLiveUserIntentionProductDTO> response = privateLiveCommunityService.queryIntentionProductByUnionId(request);

        // assert
        assertFalse("应当返回失败", response.isSuccess());
        assertEquals("咨询师Id不能为空", response.getMsg());
    }

    /**
     * 测试 unionId 为空的情况
     */
    @Test
    public void testQueryIntentionProductByUnionIdUnionIdEmpty() {
        // arrange
        CommunityUserIntentionProductRequest request = new CommunityUserIntentionProductRequest();
        request.setLiveId("liveId");
        request.setConsultantTaskId(1L);
        request.setUnionId(Arrays.asList("", ""));

        // act
        RemoteResponse<CommunityLiveUserIntentionProductDTO> response = privateLiveCommunityService.queryIntentionProductByUnionId(request);

        // assert
        assertFalse("应当返回失败", response.isSuccess());
        assertEquals("unionId不能为空", response.getMsg());
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testQueryIntentionProductByUnionIdSuccess() throws Exception {
        // arrange
        CommunityUserIntentionProductRequest request = new CommunityUserIntentionProductRequest();
        request.setLiveId("liveId");
        request.setConsultantTaskId(1L);
        request.setUnionId(Arrays.asList("unionId1", "unionId2"));

        HashMap<String, ScrmLiveUserProductActionStat> mockResult = new HashMap<>();
        mockResult.put("unionId1", new ScrmLiveUserProductActionStat(1L, "unionId1", "projectId", 1, 1L, "wxId", 1, null, null, 1L, 1L, 1));
        when(privateUserIntentionProductDomainService.queryLastProductActionStatByUnionIds(anyList(), any(Long.class), any(String.class))).thenReturn(mockResult);

        // act
        RemoteResponse<CommunityLiveUserIntentionProductDTO> response = privateLiveCommunityService.queryIntentionProductByUnionId(request);

        // assert
        assertTrue("应当返回成功", response.isSuccess());
        assertNotNull("返回的DTO不应为空", response.getData());
        assertEquals("liveId应与请求中的liveId相同", request.getLiveId(), response.getData().getLiveId());
        assertEquals("consultantTaskId应与请求中的consultantTaskId相同", request.getConsultantTaskId(), response.getData().getConsultantTaskId());
        assertNotNull("意向商品详情列表不应为空", response.getData().getIntentionProductDetailDTOList());
        assertFalse("意向商品详情列表不应为空", response.getData().getIntentionProductDetailDTOList().isEmpty());
    }

    /**
     * 测试异常情况
     */
    @Test
    public void testQueryIntentionProductByUnionIdException() {
        // arrange
        CommunityUserIntentionProductRequest request = new CommunityUserIntentionProductRequest();
        request.setLiveId("liveId");
        request.setConsultantTaskId(1L);
        request.setUnionId(Arrays.asList("unionId1", "unionId2"));

        when(privateUserIntentionProductDomainService.queryLastProductActionStatByUnionIds(anyList(), any(Long.class), any(String.class))).thenThrow(new RuntimeException("数据库异常"));

        // act
        RemoteResponse<CommunityLiveUserIntentionProductDTO> response = privateLiveCommunityService.queryIntentionProductByUnionId(request);

        // assert
        assertFalse("应当返回失败", response.isSuccess());
        assertEquals("查询最新意向商品失败", response.getMsg());
    }
}
