package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationContentTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.List;
import java.util.Map;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

class DeepSeaWxHandlerGetShorUrl1Test {

    @InjectMocks
    private DeepSeaWxHandler deepSeaWxHandler;

    @Mock
    private ShortLinkUtils shortLinkUtils;

    @Mock
    private Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> typeExecuteLogDOMapEntry;

    @Mock
    private InvokeDetailKeyObject invokeDetailKeyObject;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Scenario 1: coreUrl is blank.
     */
    @Test
    public void testGetShorUrl_BlankCoreUrl() throws Throwable {
        // arrange
        String coreUrl = "";
        boolean forceShortUrl = false;
        // act
        String result = deepSeaWxHandler.getShorUrl(coreUrl, forceShortUrl);
        // assert
        assertEquals(coreUrl, result);
    }

    /**
     * Scenario 2: coreUrl does not start with "http".
     */
    @Test
    public void testGetShorUrl_NonHttpCoreUrl() throws Throwable {
        // arrange
        String coreUrl = "ftp://example.com";
        boolean forceShortUrl = false;
        // act
        String result = deepSeaWxHandler.getShorUrl(coreUrl, forceShortUrl);
        // assert
        assertEquals(coreUrl, result);
    }

    /**
     * Scenario 3: coreUrl length is less than 400 and forceShortUrl is false.
     */
    @Test
    public void testGetShorUrl_ShortCoreUrlForceFalse() throws Throwable {
        // arrange
        String coreUrl = "http://example.com";
        boolean forceShortUrl = false;
        // act
        String result = deepSeaWxHandler.getShorUrl(coreUrl, forceShortUrl);
        // assert
        assertEquals(coreUrl, result);
    }

    /**
     * Scenario 4: coreUrl length is less than 400 and forceShortUrl is true.
     */
    @Test
    public void testGetShorUrl_ShortCoreUrlForceTrue() throws Throwable {
        // arrange
        String coreUrl = "http://example.com";
        boolean forceShortUrl = true;
        String expectedShortUrl = "http://short.url";
        when(shortLinkUtils.getShortLink(coreUrl, 90)).thenReturn(expectedShortUrl);
        // act
        String result = deepSeaWxHandler.getShorUrl(coreUrl, forceShortUrl);
        // assert
        assertEquals(expectedShortUrl, result);
        verify(shortLinkUtils, times(1)).getShortLink(coreUrl, 90);
    }

    /**
     * Scenario 5: coreUrl length is greater than or equal to 400 and forceShortUrl is false.
     */
    @Test
    public void testGetShorUrl_LongCoreUrlForceFalse() throws Throwable {
        // arrange
        StringBuilder longUrlBuilder = new StringBuilder("http://example.com/");
        for (int i = 0; i < 400; i++) {
            longUrlBuilder.append("a");
        }
        String coreUrl = longUrlBuilder.toString();
        boolean forceShortUrl = false;
        String expectedShortUrl = "http://short.url";
        when(shortLinkUtils.getShortLink(coreUrl, 90)).thenReturn(expectedShortUrl);
        // act
        String result = deepSeaWxHandler.getShorUrl(coreUrl, forceShortUrl);
        // assert
        assertEquals(expectedShortUrl, result);
        verify(shortLinkUtils, times(1)).getShortLink(coreUrl, 90);
    }

    /**
     * Scenario 6: coreUrl length is greater than or equal to 400 and forceShortUrl is true.
     */
    @Test
    public void testGetShorUrl_LongCoreUrlForceTrue() throws Throwable {
        // arrange
        StringBuilder longUrlBuilder = new StringBuilder("http://example.com/");
        for (int i = 0; i < 400; i++) {
            longUrlBuilder.append("a");
        }
        String coreUrl = longUrlBuilder.toString();
        boolean forceShortUrl = true;
        String expectedShortUrl = "http://short.url";
        when(shortLinkUtils.getShortLink(coreUrl, 90)).thenReturn(expectedShortUrl);
        // act
        String result = deepSeaWxHandler.getShorUrl(coreUrl, forceShortUrl);
        // assert
        assertEquals(expectedShortUrl, result);
        verify(shortLinkUtils, times(1)).getShortLink(coreUrl, 90);
    }

    /**
     * Scenario 7: shortLinkUtils.getShortLink throws an exception.
     */
    @Test
    public void testGetShorUrl_ExceptionThrown() throws Throwable {
        // arrange
        StringBuilder longUrlBuilder = new StringBuilder("http://example.com/");
        for (int i = 0; i < 400; i++) {
            longUrlBuilder.append("a");
        }
        String coreUrl = longUrlBuilder.toString();
        boolean forceShortUrl = true;
        when(shortLinkUtils.getShortLink(coreUrl, 90)).thenThrow(new RuntimeException("Failed to generate short URL"));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            deepSeaWxHandler.getShorUrl(coreUrl, forceShortUrl);
        });
        verify(shortLinkUtils, times(1)).getShortLink(coreUrl, 90);
    }

    /**
     * Test case for NON_SUPPLY content type
     */
    @Test
    public void testDealDeepSeaWxMessageV2WithNonSupplyContentType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        List<String> executorIds = Lists.newArrayList("executor123");
        when(typeExecuteLogDOMapEntry.getKey()).thenReturn(invokeDetailKeyObject);
        when(invokeDetailKeyObject.getContentType()).thenReturn(ScrmProcessOrchestrationContentTypeEnum.NON_SUPPLY.getValue().byteValue());
        // act
        deepSeaWxHandler.dealDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, typeExecuteLogDOMapEntry);
        // assert
        verify(typeExecuteLogDOMapEntry, times(1)).getKey();
        verify(invokeDetailKeyObject, times(1)).getContentType();
    }

    /**
     * Test case for SUPPLY content type
     */
    @Test
    public void testDealDeepSeaWxMessageV2WithSupplyContentType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        List<String> executorIds = Lists.newArrayList("executor123");
        when(typeExecuteLogDOMapEntry.getKey()).thenReturn(invokeDetailKeyObject);
        when(invokeDetailKeyObject.getContentType()).thenReturn(ScrmProcessOrchestrationContentTypeEnum.SUPPLY.getValue().byteValue());
        // act
        deepSeaWxHandler.dealDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, typeExecuteLogDOMapEntry);
        // assert
        verify(typeExecuteLogDOMapEntry, times(2)).getKey();
        verify(invokeDetailKeyObject, times(2)).getContentType();
    }

    /**
     * Test case for unknown content type
     */
    @Test
    public void testDealDeepSeaWxMessageV2WithUnknownContentType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        List<String> executorIds = Lists.newArrayList("executor123");
        when(typeExecuteLogDOMapEntry.getKey()).thenReturn(invokeDetailKeyObject);
        when(invokeDetailKeyObject.getContentType()).thenReturn((byte) 99);
        // act
        deepSeaWxHandler.dealDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, typeExecuteLogDOMapEntry);
        // assert
        verify(typeExecuteLogDOMapEntry, times(2)).getKey();
        verify(invokeDetailKeyObject, times(2)).getContentType();
    }

    /**
     * Test case for null typeExecuteLogDOMapEntry
     * Now expects NullPointerException to be thrown
     */
    @Test
    public void testDealDeepSeaWxMessageV2WithNullTypeExecuteLogDOMapEntry() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        List<String> executorIds = Lists.newArrayList("executor123");
        // act & assert
        NullPointerException exception = assertThrows(NullPointerException.class, () -> {
            deepSeaWxHandler.dealDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, null);
        });
        assertNotNull(exception, "NullPointerException should be thrown");
    }

    /**
     * Test case for null InvokeDetailKeyObject
     * Now expects NullPointerException to be thrown
     */
    @Test
    public void testDealDeepSeaWxMessageV2WithNullInvokeDetailKeyObject() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        List<String> executorIds = Lists.newArrayList("executor123");
        when(typeExecuteLogDOMapEntry.getKey()).thenReturn(null);
        // act & assert
        NullPointerException exception = assertThrows(NullPointerException.class, () -> {
            deepSeaWxHandler.dealDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, typeExecuteLogDOMapEntry);
        });
        assertNotNull(exception, "NullPointerException should be thrown");
    }
}
