package com.sankuai.scrm.core.service.realtime.task.dal.example;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.util.ArrayList;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

public class ScrmAIAgentTestGroupRecordDOExampleClear1Test {

    private ScrmAIAgentTestGroupRecordDOExample example;

    @BeforeEach
    public void setUp() {
        example = new ScrmAIAgentTestGroupRecordDOExample();
    }

    /**
     * 测试clear方法在初始状态下的行为
     */
    @Test
    public void testClearWhenInitialState() throws Throwable {
        // arrange - 初始状态已由setUp方法创建
        // act
        example.clear();
        // assert
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertTrue(example.getOredCriteria().isEmpty());
        assertNull(example.getOffset());
        assertNull(example.getRows());
    }

    /**
     * 测试clear方法在所有字段都有值的情况下的行为
     */
    @Test
    public void testClearWhenAllFieldsSet() throws Throwable {
        // arrange
        example.setOrderByClause("test_order");
        example.setDistinct(true);
        // 添加一个Criteria
        example.or();
        example.setOffset(10);
        example.setRows(20);
        // act
        example.clear();
        // assert
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertTrue(example.getOredCriteria().isEmpty());
        assertNull(example.getOffset());
        assertNull(example.getRows());
    }

    /**
     * 测试clear方法在部分字段有值的情况下的行为
     */
    @Test
    public void testClearWhenPartialFieldsSet() throws Throwable {
        // arrange
        example.setOrderByClause("test_order");
        example.setDistinct(true);
        // 不设置offset和rows
        // act
        example.clear();
        // assert
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertTrue(example.getOredCriteria().isEmpty());
        assertNull(example.getOffset());
        assertNull(example.getRows());
    }

    /**
     * 测试clear方法在多次调用后的行为
     */
    @Test
    public void testClearWhenCalledMultipleTimes() throws Throwable {
        // arrange
        example.setOrderByClause("test_order");
        example.setDistinct(true);
        example.or();
        // act
        example.clear();
        // 第二次调用
        example.clear();
        // assert
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertTrue(example.getOredCriteria().isEmpty());
        assertNull(example.getOffset());
        assertNull(example.getRows());
    }

    /**
     * 测试clear方法后可以重新设置值
     */
    @Test
    public void testClearThenResetValues() throws Throwable {
        // arrange
        example.setOrderByClause("test_order");
        example.setDistinct(true);
        example.or();
        // act
        example.clear();
        // 重新设置值
        example.setOrderByClause("new_order");
        example.setDistinct(false);
        example.or();
        example.setOffset(5);
        example.setRows(10);
        // assert
        assertEquals("new_order", example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertFalse(example.getOredCriteria().isEmpty());
        assertEquals(5, example.getOffset());
        assertEquals(10, example.getRows());
    }

    /**
     * 演示如何使用Mockito框架（虽然这个简单类不需要Mock）
     */
    @Test
    public void testClearWithMockitoDemonstration() throws Throwable {
        // arrange - 创建spy对象而不是mock对象
        ScrmAIAgentTestGroupRecordDOExample spyExample = Mockito.spy(new ScrmAIAgentTestGroupRecordDOExample());
        // 设置一些初始状态
        spyExample.setOrderByClause("test_order");
        spyExample.setDistinct(true);
        spyExample.or();
        // act
        spyExample.clear();
        // assert - 验证方法被调用且状态被重置
        verify(spyExample).clear();
        assertNull(spyExample.getOrderByClause());
        assertFalse(spyExample.isDistinct());
        assertTrue(spyExample.getOredCriteria().isEmpty());
    }
}
