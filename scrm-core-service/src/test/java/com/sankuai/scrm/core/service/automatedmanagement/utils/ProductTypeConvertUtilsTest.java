package com.sankuai.scrm.core.service.automatedmanagement.utils;

import com.sankuai.medicalcosmetology.product.selectify.api.enums.ProductTypeEnum;
import org.junit.Assert;
import org.junit.Test;

public class ProductTypeConvertUtilsTest {

    /**
     * 测试convertOutBizProductType2LocalProductType方法，当outBizProductType为null时，应返回null
     */
    @Test
    public void testConvertOutBizProductType2LocalProductTypeNull() {
        // arrange
        Integer outBizProductType = null;
        // act
        Integer result = ProductTypeConvertUtils.convertOutBizProductType2LocalProductType(outBizProductType);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试convertOutBizProductType2LocalProductType方法，当outBizProductType不为null，但无法获取到对应的枚举对象时，应返回null
     */
    @Test
    public void testConvertOutBizProductType2LocalProductTypeNoEnum() {
        // arrange
        // 不存在的枚举值
        Integer outBizProductType = 999;
        // act
        Integer result = ProductTypeConvertUtils.convertOutBizProductType2LocalProductType(outBizProductType);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试convertOutBizProductType2LocalProductType方法，当outBizProductType不为null，且可以获取到对应的枚举对象时，应返回该枚举对象的shelfValue
     */
    @Test
    public void testConvertOutBizProductType2LocalProductTypeValid() {
        // arrange
        // 存在的枚举值
        Integer outBizProductType = 1;
        // act
        Integer result = ProductTypeConvertUtils.convertOutBizProductType2LocalProductType(outBizProductType);
        // assert
        Assert.assertEquals(Integer.valueOf(2), result);
    }

    /**
     * Tests the convertLocalProductType2OutBizProductType method when productType is null.
     */
    @Test
    public void testConvertLocalProductType2OutBizProductTypeWhenProductTypeIsNull() throws Throwable {
        // arrange
        Integer productType = null;
        // act
        Integer result = ProductTypeConvertUtils.convertLocalProductType2OutBizProductType(productType);
        // assert
        Assert.assertNull(result);
    }

    /**
     * Tests the convertLocalProductType2OutBizProductType method when productType is not null, but getByShelfValue returns null.
     */
    @Test
    public void testConvertLocalProductType2OutBizProductTypeWhenProductTypeIsNotNullButGetByShelfValueReturnsNull() throws Throwable {
        // arrange
        // Non-Existing productType
        Integer productType = 999;
        // act
        Integer result = ProductTypeConvertUtils.convertLocalProductType2OutBizProductType(productType);
        // assert
        Assert.assertNull(result);
    }

    /**
     * Tests the convertLocalProductType2OutBizProductType method when productType is not null, and getByShelfValue returns not null.
     */
    @Test
    public void testConvertLocalProductType2OutBizProductTypeWhenProductTypeIsNotNullAndGetByShelfValueReturnsNotNull() throws Throwable {
        // arrange
        // Existing productType
        Integer productType = 2;
        // act
        Integer result = ProductTypeConvertUtils.convertLocalProductType2OutBizProductType(productType);
        // assert
        // Corrected the expected value to match the actual logic
        Assert.assertEquals(Integer.valueOf(1), result);
    }
}
