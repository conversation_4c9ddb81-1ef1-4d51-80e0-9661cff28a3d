package com.sankuai.scrm.core.service.infrastructure.acl.wx;

import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxGroupSendMessageRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGroupSendMessageResponse;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.dianping.baby.http.HttpClientUtil;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.slf4j.Logger;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.*;
import org.junit.*;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.*;

@RunWith(MockitoJUnitRunner.class)
public class WxGroupSendMessageAclTest {

    @InjectMocks
    private WxGroupSendMessageAcl wxGroupSendMessageAcl;

    @Mock(lenient = true)
    private HttpClientUtil httpClientUtil;

    @Mock(lenient = true)
    private Logger log;

    private WxGroupSendMessageRequest request;

    private String appId;

    private String token;

    private String url;

    @Before
    public void setup() {
        request = new WxGroupSendMessageRequest();
        appId = "testAppId";
        token = "testToken";
        url = "https://qyapi.weixin.qq.com/cgi-bin/externalcontact/add_msg_template?access_token=" + token;
    }

    @Test(expected = Exception.class)
    public void testGroupSendMessageAppIdOrTokenIsNull() throws Throwable {
        wxGroupSendMessageAcl.groupSendMessage(request, null, token);
    }

    @Test(expected = Exception.class)
    public void testGroupSendMessageTokenIsNull() throws Throwable {
        wxGroupSendMessageAcl.groupSendMessage(request, appId, null);
    }
}
