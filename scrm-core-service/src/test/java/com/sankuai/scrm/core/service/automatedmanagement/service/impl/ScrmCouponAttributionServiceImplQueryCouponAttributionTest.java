package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.automatedmanagement.request.coupon.attribution.ScrmCouponAttributionRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.coupon.attribution.ScrmCouponAttributionResponse;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

class ScrmCouponAttributionServiceImplQueryCouponAttributionTest {

    @Mock
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordDOMapper;

    @InjectMocks
    private ScrmCouponAttributionServiceImpl scrmCouponAttributionService;

    private ScrmCouponAttributionRequest request;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        request = new ScrmCouponAttributionRequest();
    }

    /**
     * 测试参数错误场景：request为null
     */
    @Test
    void testQueryCouponAttributionRequestNull() throws Throwable {
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttribution(null);
        assertFalse(response.isSuccess());
        assertEquals("参数错误", response.getMsg());
    }

    /**
     * 测试参数错误场景：couponId为空
     */
    @Test
    void testQueryCouponAttributionCouponIdBlank() throws Throwable {
        request.setMtUserID(123L);
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttribution(request);
        assertFalse(response.isSuccess());
        assertEquals("参数错误", response.getMsg());
    }

    /**
     * 测试参数错误场景：mtUserID为null
     */
    @Test
    void testQueryCouponAttributionMtUserIdNull() throws Throwable {
        request.setCouponId("123");
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttribution(request);
        assertFalse(response.isSuccess());
        assertEquals("参数错误", response.getMsg());
    }

    /**
     * 测试记录不存在场景
     */
    @Test
    void testQueryCouponAttributionRecordNotFound() throws Throwable {
        request.setCouponId("123");
        request.setMtUserID(456L);
        when(scrmSceneCouponRecordDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttribution(request);
        assertTrue(response.isSuccess());
        assertFalse(response.getData().isCommunityChannel());
    }

    /**
     * 测试用户ID不匹配场景
     */
    @Test
    void testQueryCouponAttributionUserIdNotMatch() throws Throwable {
        request.setCouponId("123");
        request.setMtUserID(456L);
        ScrmSceneCouponRecords record = ScrmSceneCouponRecords.builder().userid(789L).build();
        when(scrmSceneCouponRecordDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(record));
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttribution(request);
        assertFalse(response.isSuccess());
        assertEquals("用户id不匹配", response.getMsg());
    }

    /**
     * 测试用户ID匹配场景
     */
    @Test
    void testQueryCouponAttributionUserIdMatch() throws Throwable {
        request.setCouponId("123");
        request.setMtUserID(456L);
        ScrmSceneCouponRecords record = ScrmSceneCouponRecords.builder().userid(456L).distributorcode("ABC").build();
        when(scrmSceneCouponRecordDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(record));
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttribution(request);
        assertTrue(response.isSuccess());
        assertTrue(response.getData().isCommunityChannel());
        assertEquals("ABC", response.getData().getDistributorCode());
        verify(scrmSceneCouponRecordDOMapper, times(1)).updateByExampleSelective(any(), any());
    }

    /**
     * 测试系统异常场景
     */
    @Test
    void testQueryCouponAttributionSystemException() throws Throwable {
        request.setCouponId("123");
        request.setMtUserID(456L);
        when(scrmSceneCouponRecordDOMapper.selectByExample(any())).thenThrow(new RuntimeException("系统异常"));
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttribution(request);
        assertFalse(response.isSuccess());
        assertEquals("系统异常", response.getMsg());
    }
}
