package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxMomentSendAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxMomentSendRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxMomentSendResponse;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
public class StaffMomentTouchActionDealStaffMomentActionTest {

    private TestStaffMomentTouchAction staffMomentTouchAction;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private WxMomentSendAcl wxMomentSendAcl;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ExecuteManagementDTO mediumManagementDTO;

    private ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO;

    private ScrmProcessOrchestrationNodeDTO currentProcessingNode;

    private ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;

    class TestStaffMomentTouchAction extends StaffMomentTouchAction {

        private ScrmAmProcessOrchestrationExecuteLogDO returnExecuteLogDO;

        public TestStaffMomentTouchAction(ExecuteManagementService executeManagementService, ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper, ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper, WxMomentSendAcl wxMomentSendAcl) {
            ReflectionTestUtils.setField(this, "executeManagementService", executeManagementService);
            ReflectionTestUtils.setField(this, "executeLogDOMapper", executeLogDOMapper);
            ReflectionTestUtils.setField(this, "wxInvokeLogDOMapper", wxInvokeLogDOMapper);
            ReflectionTestUtils.setField(this, "wxMomentSendAcl", wxMomentSendAcl);
        }

        public void setReturnExecuteLogDO(ScrmAmProcessOrchestrationExecuteLogDO executeLogDO) {
            this.returnExecuteLogDO = executeLogDO;
        }

        @Override
        protected ScrmAmProcessOrchestrationExecuteLogDO getScrmAmProcessOrchestrationExecuteLogDO(ScrmProcessOrchestrationDTO processOrchestrationDTO, ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO, ScrmProcessOrchestrationNodeDTO currentProcessingNode, ScrmAmProcessOrchestrationExecuteLogDO existedExecuteLogDO) {
            return returnExecuteLogDO;
        }

        @Override
        protected List<ScrmAmProcessOrchestrationExecuteLogDO> getExecuteLogByExample(ScrmAmProcessOrchestrationExecuteLogDOExample executeLogDOExample) {
            return new ArrayList<>();
        }
    }

    @BeforeEach
    void setUp() {
        staffMomentTouchAction = new TestStaffMomentTouchAction(executeManagementService, executeLogDOMapper, wxInvokeLogDOMapper, wxMomentSendAcl);
        // Setup processOrchestrationDTO
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        processOrchestrationDTO.setAppId("testApp");
        processOrchestrationDTO.setDemoScene(false);
        processOrchestrationDTO.setDemoCorpTagList(new ArrayList<>());
        processOrchestrationDTO.setParticipationRestrict(true);
        processOrchestrationDTO.setParticipationRestrictionsTimes((byte) 1);
        // Setup NodeMediumDTO
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        // Setup action
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setActionId(1);
        actionDTO.setProcessOrchestrationNodeId(1L);
        // Setup action content
        List<ScrmProcessOrchestrationActionContentDTO> contentList = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("Test Content");
        contentList.add(contentDTO);
        actionDTO.setContentList(contentList);
        nodeMediumDTO.addActionDTO(1L, actionDTO);
        // Setup mediumManagementDTO
        mediumManagementDTO = new ExecuteManagementDTO();
        mediumManagementDTO.setStaffLimitSet(new HashSet<>(Arrays.asList("staff1")));
        mediumManagementDTO.setExistedWxInvokeLogDOMap(new HashMap<>());
        // Setup other DTOs
        scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        scrmCrowdPackDetailInfoDTO.setExternalUserWxUnionId("testUnionId");
        currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setNodeId(1L);
        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
    }

    /**
     * Test case: Empty staff moment touch corp tag list
     */
    @Test
    public void testDealStaffMomentAction_EmptyCorpTagList() throws Throwable {
        // arrange
        // Non-zero to avoid ALL_USER_TAG
        processOrchestrationDTO.setCrowdPackType(1);
        // act
        StepExecuteResultDTO result = staffMomentTouchAction.dealStaffMomentAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, executeLogDO);
        // assert
        assertFalse(result.isSuccess());
    }

    /**
     * Test case: Execute log DO is null
     */
    @Test
    public void testDealStaffMomentAction_ExecuteLogDOIsNull() throws Throwable {
        // arrange
        // Will return ALL_USER_TAG
        processOrchestrationDTO.setCrowdPackType(0);
        staffMomentTouchAction.setReturnExecuteLogDO(null);
        // act
        StepExecuteResultDTO result = staffMomentTouchAction.dealStaffMomentAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertFalse(result.isSuccess());
    }

    /**
     * Test case: Empty contact users list
     */
    @Test
    public void testDealStaffMomentAction_EmptyContactUsers() throws Throwable {
        // arrange
        // Will return ALL_USER_TAG
        processOrchestrationDTO.setCrowdPackType(0);
        staffMomentTouchAction.setReturnExecuteLogDO(executeLogDO);
        // act
        StepExecuteResultDTO result = staffMomentTouchAction.dealStaffMomentAction(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertFalse(result.isSuccess());
    }
}
