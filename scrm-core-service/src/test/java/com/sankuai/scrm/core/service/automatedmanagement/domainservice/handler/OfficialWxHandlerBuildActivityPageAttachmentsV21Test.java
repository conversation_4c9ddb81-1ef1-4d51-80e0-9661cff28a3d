package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerBuildActivityPageAttachmentsV21Test {

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private ShortLinkUtils shortLinkUtils;

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    private ScrmProcessOrchestrationDTO processDTO;

    private ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO;

    private ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO;

    private ExecuteManagementDTO executeManagementDTO;

    @BeforeEach
    void setUp() {
        processDTO = new ScrmProcessOrchestrationDTO();
        processDTO.setAppId("testApp");
        attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachmentDTO.setProcessOrchestrationNodeId(1L);
        supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        executeManagementDTO = new ExecuteManagementDTO();
        executeManagementDTO.setPoiId("123");
    }

    /**
     * Test empty page list input returns empty attachment list
     */
    @Test
    public void testBuildActivityPageAttachmentsV2EmptyPageList() throws Throwable {
        // arrange
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        // act
        List<MsgPushContentDTO> result = officialWxHandler.buildActivityPageAttachmentsV2(processDTO, attachmentDTO, supplyDetailDTO, Collections.emptyList(), existedAttachmentMap, false, executeManagementDTO);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test page already in cache returns cached attachment
     */
    @Test
    public void testBuildActivityPageAttachmentsV2PageInCache() throws Throwable {
        // arrange
        Long nodeId = 1L;
        long pageId = 123L;
        String cacheKey = nodeId + "-" + pageId;
        attachmentDTO.setProcessOrchestrationNodeId(nodeId);
        ScrmAmProcessOrchestrationProductActivityPageDO pageDO = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageDO.setId(pageId);
        MsgPushContentDTO cachedDTO = new MsgPushContentDTO();
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        existedAttachmentMap.put(cacheKey, cachedDTO);
        // act
        List<MsgPushContentDTO> result = officialWxHandler.buildActivityPageAttachmentsV2(processDTO, attachmentDTO, supplyDetailDTO, Collections.singletonList(pageDO), existedAttachmentMap, false, executeManagementDTO);
        // assert
        assertEquals(1, result.size());
        assertSame(cachedDTO, result.get(0));
    }

    /**
     * Test chosen supply skips page without related products
     */
    @Test
    public void testBuildActivityPageAttachmentsV2ChosenSupplyNoRelatedProducts() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationProductActivityPageDO pageDO = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageDO.setId(123L);
        // no related products
        pageDO.setRelatedProductIds(null);
        attachmentDTO.setProcessOrchestrationNodeId(1L);
        // act
        // isChosenSupply
        List<MsgPushContentDTO> // isChosenSupply
        // isChosenSupply
        // isChosenSupply
        // isChosenSupply
        // isChosenSupply
        // isChosenSupply
        // isChosenSupply
        result = officialWxHandler.buildActivityPageAttachmentsV2(processDTO, attachmentDTO, supplyDetailDTO, Collections.singletonList(pageDO), new HashMap<>(), true, executeManagementDTO);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test new page not in cache creates new attachment
     */
    @Test
    public void testBuildActivityPageAttachmentsV2NewPageNotInCache() throws Throwable {
        // arrange
        String appId = "testApp";
        Long nodeId = 1L;
        long pageId = 123L;
        String distributorCode = "dist123";
        String pageUrl = "https://test.page.url";
        processDTO.setAppId(appId);
        attachmentDTO.setProcessOrchestrationNodeId(nodeId);
        ScrmAmProcessOrchestrationProductActivityPageDO pageDO = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageDO.setId(pageId);
        pageDO.setActivityTitle("Test Activity");
        pageDO.setMiniProgramOriginAppId("originApp");
        pageDO.setMiniProgramAppId("mpApp");
        pageDO.setThumbPicUrl("thumb.jpg");
        pageDO.setRelatedProductIds("1,2,3");
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneCode("testScene");
        sceneCodeDO.setPoiRestrict(0);
        when(informationGatheringService.queryCommunityDistributor(any())).thenReturn(distributorCode);
        when(productManagementService.getActivSceneCodeDO(any(), any(), any(), any(), any())).thenReturn(sceneCodeDO);
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn(pageUrl);
        // act
        List<MsgPushContentDTO> result = officialWxHandler.buildActivityPageAttachmentsV2(processDTO, attachmentDTO, supplyDetailDTO, Collections.singletonList(pageDO), new HashMap<>(), true, executeManagementDTO);
        // assert
        assertEquals(1, result.size());
        MsgPushContentDTO contentDTO = result.get(0);
        assertNotNull(contentDTO.getMiniProgramDTO());
        assertEquals("Test Activity", contentDTO.getMiniProgramDTO().getTitle());
        assertEquals("originApp", contentDTO.getMiniProgramDTO().getOriginAppId());
        assertEquals("mpApp", contentDTO.getMiniProgramDTO().getAppId());
        assertEquals("thumb.jpg", contentDTO.getMiniProgramDTO().getThumbnail());
        verify(productManagementService).getActivSceneCodeDO(eq(attachmentDTO), eq(supplyDetailDTO), eq(pageDO), eq(distributorCode), eq(appId));
    }
}
