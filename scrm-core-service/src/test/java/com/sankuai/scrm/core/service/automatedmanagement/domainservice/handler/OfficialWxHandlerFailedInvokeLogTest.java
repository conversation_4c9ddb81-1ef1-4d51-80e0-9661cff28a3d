package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.GroupSendStrategy;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.PrivateSendStrategy;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.infrastructure.acl.UploadWxMediaAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGetGroupMsgSendResultAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGroupSendMessageAcl;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import java.util.Date;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerFailedInvokeLogTest {

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    private ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private WxGroupSendMessageAcl wxGroupSendMessageAcl;

    @Mock
    private WxGetGroupMsgSendResultAcl wxGetGroupmsgSendResultAcl;

    @Mock
    private UploadWxMediaAcl uploadWxMediaAcl;

    @Mock
    private WeChatTokenAcl weChatTokenAcl;

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private ShortLinkUtils shortLinkUtils;

    @Mock
    private MsgUnifiedPushService msgUnifiedPushService;

    @Mock
    private GroupSendStrategy groupSendStrategy;

    @Mock
    private PrivateSendStrategy privateSendStrategy;

    @BeforeEach
    void setUp() {
        invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setId(1L);
    }

    /**
     * Test successful update of both invoke log and execute log
     */
    @Test
    public void testFailedInvokeLogSuccess() throws Throwable {
        // arrange
        when(wxInvokeLogDOMapper.updateByPrimaryKey(any())).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // act
        officialWxHandler.failedInvokeLog(invokeLogDO);
        // assert
        ArgumentCaptor<ScrmAmProcessOrchestrationWxInvokeLogDO> invokeLogCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationWxInvokeLogDO.class);
        verify(wxInvokeLogDOMapper).updateByPrimaryKey(invokeLogCaptor.capture());
        assertEquals(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_FAILED_INVALID_OR_CANT_DELIVER.getValue().byteValue(), invokeLogCaptor.getValue().getStatus());
        assertNotNull(invokeLogCaptor.getValue().getUpdateTime());
        ArgumentCaptor<ScrmAmProcessOrchestrationExecuteLogDO> executeLogCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationExecuteLogDO.class);
        ArgumentCaptor<ScrmAmProcessOrchestrationExecuteLogDOExample> exampleCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationExecuteLogDOExample.class);
        verify(executeLogDOMapper).updateByExampleSelective(executeLogCaptor.capture(), exampleCaptor.capture());
        assertEquals(ScrmProcessOrchestrationExecuteStatusTypeEnum.FAILED_CALL_EXTERNAL_SERVICE.getValue().byteValue(), executeLogCaptor.getValue().getStatus());
        assertNotNull(executeLogCaptor.getValue().getUpdateTime());
    }

    /**
     * Test when wxInvokeLogDOMapper update fails
     */
    @Test
    public void testFailedInvokeLogWhenInvokeLogUpdateFails() throws Throwable {
        // arrange
        when(wxInvokeLogDOMapper.updateByPrimaryKey(any())).thenReturn(0);
        // act
        officialWxHandler.failedInvokeLog(invokeLogDO);
        // assert
        verify(wxInvokeLogDOMapper).updateByPrimaryKey(any());
        // The execute log update should still be attempted even if invoke log update fails
        verify(executeLogDOMapper).updateByExampleSelective(any(), any());
    }

    /**
     * Test when executeLogDOMapper update fails
     */
    @Test
    public void testFailedInvokeLogWhenExecuteLogUpdateFails() throws Throwable {
        // arrange
        when(wxInvokeLogDOMapper.updateByPrimaryKey(any())).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(0);
        // act
        officialWxHandler.failedInvokeLog(invokeLogDO);
        // assert
        verify(wxInvokeLogDOMapper).updateByPrimaryKey(any());
        verify(executeLogDOMapper).updateByExampleSelective(any(), any());
    }

    /**
     * Test with null input parameter
     */
    @Test
    public void testFailedInvokeLogWithNullInput() throws Throwable {
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            officialWxHandler.failedInvokeLog(null);
        });
        verify(wxInvokeLogDOMapper, never()).updateByPrimaryKey(any());
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * Test when database throws exception
     */
    @Test
    public void testFailedInvokeLogWhenDatabaseThrowsException() throws Throwable {
        // arrange
        when(wxInvokeLogDOMapper.updateByPrimaryKey(any())).thenThrow(new RuntimeException("Database error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            officialWxHandler.failedInvokeLog(invokeLogDO);
        });
        verify(wxInvokeLogDOMapper).updateByPrimaryKey(any());
        // The execute log update should not be attempted if invoke log update throws exception
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * Test getMiniProgramTitle when input string length * 3 >= 64
     * Should return fixed string "【回馈特惠】社群专享特价爆品"
     */
    @Test
    public void testGetMiniProgramTitle_WhenLengthGreaterThanOrEqual64() {
        // arrange
        // length = 25 * 3 = 75 > 64
        String input = "这是一个长度超过22个字符的测试字符串用来测试长度大于64的情况";
        // act
        String result = officialWxHandler.getMiniProgramTitle(input);
        // assert
        assertEquals("【回馈特惠】社群专享特价爆品", result);
    }

    /**
     * Test getMiniProgramTitle when input string length * 3 < 64
     * Should return original string
     */
    @Test
    public void testGetMiniProgramTitle_WhenLengthLessThan64() {
        // arrange
        // length = 4 * 3 = 12 < 64
        String input = "测试标题";
        // act
        String result = officialWxHandler.getMiniProgramTitle(input);
        // assert
        assertEquals(input, result);
    }

    /**
     * Test getMiniProgramTitle when input string is empty
     * Should return empty string
     */
    @Test
    public void testGetMiniProgramTitle_WhenEmptyString() {
        // arrange
        String input = "";
        // act
        String result = officialWxHandler.getMiniProgramTitle(input);
        // assert
        assertEquals("", result);
    }

    /**
     * Test getMiniProgramTitle when input string is null
     * Should return null
     */
    @Test
    public void testGetMiniProgramTitle_WhenNull() {
        // arrange
        String input = null;
        // act
        String result = officialWxHandler.getMiniProgramTitle(input);
        // assert
        assertNull(result);
    }

    /**
     * Test getMiniProgramTitle when input string length * 3 exactly equals 64
     * Should return fixed string "【回馈特惠】社群专享特价爆品"
     */
    @Test
    public void testGetMiniProgramTitle_WhenLengthExactly64() {
        // arrange
        // length = 21.33... * 3 ≈ 64
        String input = "这是一个长度刚好等于21个字符的测试用例字符";
        // act
        String result = officialWxHandler.getMiniProgramTitle(input);
        // assert
        assertEquals("【回馈特惠】社群专享特价爆品", result);
    }
}
