package com.sankuai.scrm.core.service.activity.wxgroup.dal.example;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("ScrmActivityAndDSPersonalWxGroupRelationMapDOExample Limit Method Tests")
public class ScrmActivityAndDSPersonalWxGroupRelationMapDOExampleCreateCriteriaInternalTest {

    /**
     * 测试 createCriteriaInternal 方法是否能正确创建并返回一个新的 Criteria 对象
     */
    @Test
    public void testCreateCriteriaInternal() throws Throwable {
        // arrange
        ScrmDSPersonalWxGroupMemberInfoDOExample example = new ScrmDSPersonalWxGroupMemberInfoDOExample();
        // act
        ScrmDSPersonalWxGroupMemberInfoDOExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertNotNull(result);
    }

    /**
     * Test limit method with positive integer value
     * Expected: Should set rows value and return instance
     */
    @Test
    @DisplayName("Test limit with positive value")
    public void testLimitWithPositiveValue() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        Integer rows = 10;
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample result = example.limit(rows);
        // assert
        assertAll(() -> assertEquals(rows, example.getRows(), "Rows value should be set to input value"), () -> assertSame(example, result, "Method should return the same instance"));
    }

    /**
     * Test limit method with zero value
     * Expected: Should accept zero and return instance
     */
    @Test
    @DisplayName("Test limit with zero")
    public void testLimitWithZero() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        Integer rows = 0;
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample result = example.limit(rows);
        // assert
        assertAll(() -> assertEquals(rows, example.getRows(), "Rows value should be set to zero"), () -> assertSame(example, result, "Method should return the same instance"));
    }

    /**
     * Test limit method with null value
     * Expected: Should accept null and return instance
     */
    @Test
    @DisplayName("Test limit with null")
    public void testLimitWithNull() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample result = example.limit(null);
        // assert
        assertAll(() -> assertNull(example.getRows(), "Rows value should be set to null"), () -> assertSame(example, result, "Method should return the same instance"));
    }

    /**
     * Test limit method chaining capability
     * Expected: Should update rows value and maintain chain ability
     */
    @Test
    @DisplayName("Test limit method chaining")
    public void testLimitMethodChaining() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        Integer firstRows = 10;
        Integer secondRows = 20;
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample result = example.limit(firstRows).limit(secondRows);
        // assert
        assertAll(() -> assertEquals(secondRows, example.getRows(), "Rows value should be updated to the last set value"), () -> assertSame(example, result, "Method should return the same instance"));
    }

    /**
     * Test limit method with maximum integer value
     * Expected: Should accept maximum value and return instance
     */
    @Test
    @DisplayName("Test limit with maximum integer value")
    public void testLimitWithMaxValue() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        Integer rows = Integer.MAX_VALUE;
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample result = example.limit(rows);
        // assert
        assertAll(() -> assertEquals(rows, example.getRows(), "Rows value should be set to maximum integer value"), () -> assertSame(example, result, "Method should return the same instance"));
    }
}
