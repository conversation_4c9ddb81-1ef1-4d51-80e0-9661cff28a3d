package com.sankuai.scrm.core.service.userWechatCoupon.domainService;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.when;
import com.alibaba.fastjson.JSON;
import com.sankuai.dz.srcm.friend.dynamiccode.dto.AttachmentsDTO;
import com.sankuai.dz.srcm.friend.dynamiccode.dto.FriendWelcomeMessageDTO;
import com.sankuai.dz.srcm.friend.dynamiccode.dto.WelcomeMsgContentDTO;
import com.sankuai.scrm.core.service.chat.dal.entity.WelcomeMessage;
import com.sankuai.scrm.core.service.coupon.dal.entity.CouponInfo;
import com.sankuai.scrm.core.service.flow.dal.entity.ScrmFlowMaterialRelationLog;
import com.sankuai.scrm.core.service.flow.dal.mapper.ScrmFlowMaterialRelationLogMapper;
import com.sankuai.scrm.core.service.flow.handler.GroundPromotionLandingPageFlowEntryHandler;
import com.sankuai.scrm.core.service.flowV2.dal.mapper.FlowEntryEventLogMapper;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxContactUserDetail;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.userWechatCoupon.dal.entity.ScrmUserWechatCoupon;
import com.sankuai.scrm.core.service.userWechatCoupon.dal.mapper.ScrmUserWechatCouponMapper;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class GetUserWechantCouponGetcoupon1Test {

    @InjectMocks
    private GetUserWechantCoupon getUserWechantCoupon;

    @Mock
    private ScrmUserWechatCouponMapper scrmUserWechatCouponMapper;

    @Mock
    private FlowEntryEventLogMapper flowEntryEventLogMapper;

    @Mock
    private ScrmFlowMaterialRelationLogMapper scrmFlowMaterialRelationLogMapper;

    @Mock
    private GroundPromotionLandingPageFlowEntryHandler groundPromotionLandingPageFlowEntryHandler;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    private WxContactUserDetail createValidUserDetail() {
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("test-union-id");
        return userDetail;
    }

    /**
     * Test case for null couponId
     */
    @Test
    public void testGetcouponWithNullCouponId() throws Throwable {
        // arrange
        WelcomeMessage welcomeMessage = new WelcomeMessage();
        FriendWelcomeMessageDTO messageDTO = new FriendWelcomeMessageDTO();
        AttachmentsDTO attachmentsDTO = new AttachmentsDTO();
        attachmentsDTO.setMsgType("coupon");
        WelcomeMsgContentDTO contentDTO = new WelcomeMsgContentDTO();
        contentDTO.setCouponId(null);
        attachmentsDTO.setWelcomeMsgContentDTO(contentDTO);
        messageDTO.setAttachments(Collections.singletonList(attachmentsDTO));
        welcomeMessage.setMsgList(JSON.toJSONString(messageDTO));
        WxContactUserDetail userDetail = createValidUserDetail();
        when(flowEntryEventLogMapper.countByExample(any())).thenReturn(1L);
        when(mtUserCenterAclService.getUserIdByUnionIdFromMtUserCenter(any())).thenReturn(1L);
        List<ScrmFlowMaterialRelationLog> logs = new ArrayList<>();
        ScrmFlowMaterialRelationLog log = new ScrmFlowMaterialRelationLog();
        log.setCategoryId(1L);
        log.setPoiId(1L);
        log.setMtCityId(1);
        log.setDpCityId(1);
        logs.add(log);
        when(scrmFlowMaterialRelationLogMapper.selectByExample(any())).thenReturn(logs);
        // act
        boolean result = getUserWechantCoupon.getcoupon(welcomeMessage, userDetail);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for existing coupon
     */
    @Test
    public void testGetcouponWithExistingCoupon() throws Throwable {
        // arrange
        WelcomeMessage welcomeMessage = new WelcomeMessage();
        FriendWelcomeMessageDTO messageDTO = new FriendWelcomeMessageDTO();
        AttachmentsDTO attachmentsDTO = new AttachmentsDTO();
        attachmentsDTO.setMsgType("coupon");
        WelcomeMsgContentDTO contentDTO = new WelcomeMsgContentDTO();
        contentDTO.setCouponId(1L);
        attachmentsDTO.setWelcomeMsgContentDTO(contentDTO);
        messageDTO.setAttachments(Collections.singletonList(attachmentsDTO));
        welcomeMessage.setMsgList(JSON.toJSONString(messageDTO));
        WxContactUserDetail userDetail = createValidUserDetail();
        when(flowEntryEventLogMapper.countByExample(any())).thenReturn(1L);
        when(mtUserCenterAclService.getUserIdByUnionIdFromMtUserCenter(any())).thenReturn(1L);
        List<ScrmFlowMaterialRelationLog> logs = new ArrayList<>();
        ScrmFlowMaterialRelationLog log = new ScrmFlowMaterialRelationLog();
        log.setCategoryId(1L);
        log.setPoiId(1L);
        log.setMtCityId(1);
        log.setDpCityId(1);
        logs.add(log);
        when(scrmFlowMaterialRelationLogMapper.selectByExample(any())).thenReturn(logs);
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setCouponCode("test-code");
        when(groundPromotionLandingPageFlowEntryHandler.queryCouponInfo(anyLong())).thenReturn(couponInfo);
        when(scrmUserWechatCouponMapper.countByExample(any())).thenReturn(1L);
        // act
        boolean result = getUserWechantCoupon.getcoupon(welcomeMessage, userDetail);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for successful coupon insertion
     */
    @Test
    public void testGetcouponWithSuccessfulInsertion() throws Throwable {
        // arrange
        WelcomeMessage welcomeMessage = new WelcomeMessage();
        FriendWelcomeMessageDTO messageDTO = new FriendWelcomeMessageDTO();
        AttachmentsDTO attachmentsDTO = new AttachmentsDTO();
        attachmentsDTO.setMsgType("coupon");
        WelcomeMsgContentDTO contentDTO = new WelcomeMsgContentDTO();
        contentDTO.setCouponId(1L);
        attachmentsDTO.setWelcomeMsgContentDTO(contentDTO);
        messageDTO.setAttachments(Collections.singletonList(attachmentsDTO));
        welcomeMessage.setMsgList(JSON.toJSONString(messageDTO));
        WxContactUserDetail userDetail = createValidUserDetail();
        when(flowEntryEventLogMapper.countByExample(any())).thenReturn(1L);
        when(mtUserCenterAclService.getUserIdByUnionIdFromMtUserCenter(any())).thenReturn(1L);
        List<ScrmFlowMaterialRelationLog> logs = new ArrayList<>();
        ScrmFlowMaterialRelationLog log = new ScrmFlowMaterialRelationLog();
        log.setCategoryId(1L);
        log.setPoiId(1L);
        log.setMtCityId(1);
        log.setDpCityId(1);
        logs.add(log);
        when(scrmFlowMaterialRelationLogMapper.selectByExample(any())).thenReturn(logs);
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setCouponCode("test-code");
        when(groundPromotionLandingPageFlowEntryHandler.queryCouponInfo(anyLong())).thenReturn(couponInfo);
        when(scrmUserWechatCouponMapper.countByExample(any())).thenReturn(0L);
        when(scrmUserWechatCouponMapper.insert(any())).thenReturn(1);
        // act
        boolean result = getUserWechantCoupon.getcoupon(welcomeMessage, userDetail);
        // assert
        assertTrue(result);
    }
}
