package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.dz.srcm.aigc.service.enums.IntelligentFollowActionType;
import com.sankuai.scrm.core.service.aigc.service.dal.entity.ScrmIntelligentFollowTaskLogDO;
import com.sankuai.scrm.core.service.aigc.service.dal.example.ScrmIntelligentFollowTaskLogDOExample;
import com.sankuai.scrm.core.service.aigc.service.dal.mapper.ScrmIntelligentFollowTaskLogDOMapper;
import com.sankuai.scrm.core.service.aigc.service.domainservice.ScrmFridayIntelligentFollowDomainService;
import com.sankuai.scrm.core.service.chat.domain.PrivateChatDomainService;
import com.sankuai.scrm.core.service.dashboard.domain.DashBoardESWriteDomainService;
import com.sankuai.scrm.core.service.data.statistics.dal.babymapper.ContactUserLogDOMapper;
import com.sankuai.scrm.core.service.data.statistics.dal.entity.ContactUserLogDO;
import com.sankuai.scrm.core.service.group.dal.entity.OperatorHelperPrivateChat;
import com.sankuai.scrm.core.service.group.dynamiccode.constant.GroupDynamicCodeConstants;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.acl.track.UserTrackAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.track.request.UserTrackRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.track.result.UserTrackResult;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmFridayIntelligentFollowLogDO;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmRealtimeSceneUserRecordDO;
import com.sankuai.scrm.core.service.realtime.task.dal.example.ScrmRealtimeSceneUserRecordDOExample;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmFridayIntelligentFollowLogDOMapper;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmRealtimeSceneUserRecordDOMapper;
import com.sankuai.scrm.core.service.realtime.task.domainservice.ScrmGrowthUserInfoDomainService;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineDispatchMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Method;
import java.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

class GroupRetailAiEngineDispatchConsumerRecvMessage3Test {

    @Mock
    private RealTimeTaskConsumerConfig consumerConfig;

    @Mock
    private ScrmFridayIntelligentFollowDomainService fridayIntelligentFollowDomainService;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private PrivateChatDomainService privateChatDomainService;

    @Mock
    private PoiRelationService poiRelationService;

    @Mock
    private ScrmGrowthUserInfoDomainService scrmGrowthUserInfoDomainService;

    @Mock
    private ScrmIntelligentFollowTaskLogDOMapper intelligentFollowTaskLogDOMapper;

    @Mock
    private ScrmRealtimeSceneUserRecordDOMapper realtimeSceneUserRecordDOMapper;

    @Mock
    private ContactUserLogDOMapper contactUserLogDOMapper;

    @Mock
    private ScrmFridayIntelligentFollowLogDOMapper fridayIntelligentFollowLogDOMapper;

    @Mock
    private UserTrackAcl userTrackAcl;

    @Mock
    private RedisStoreClient redisClient;

    @Mock
    private DashBoardESWriteDomainService dashBoardESWriteDomainService;

    @InjectMocks
    private GroupRetailAiEngineDispatchConsumer consumer;

    @Mock
    private MafkaMessage<String> message;

    private MessagetContext context;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        context = new MessagetContext();
        // Mock Cat to avoid static method calls
        mockStatic(Cat.class);
    }

    private ConsumeStatus invokeRecvMessage(MafkaMessage<String> message, MessagetContext context) throws Exception {
        Method method = GroupRetailAiEngineDispatchConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        method.setAccessible(true);
        return (ConsumeStatus) method.invoke(consumer, message, context);
    }

    /**
     * Helper method to mock static Cat methods
     */
    private static void mockStatic(Class<?> clazz) {
        // This is a placeholder for PowerMock or Mockito static mocking
        // In a real implementation, you would use PowerMock or Mockito 3.4+ to mock static methods
    }

    /**
     * Test empty message body returns CONSUME_SUCCESS
     */
    @Test
    void testRecvMessageEmptyBody() throws Throwable {
        // arrange
        when(message.getBody()).thenReturn(null);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test invalid JSON message returns CONSUME_SUCCESS
     */
    @Test
    void testRecvMessageInvalidJson() throws Throwable {
        // arrange
        when(message.getBody()).thenReturn("invalid json");
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test actionType=1 case updates DB map info
     */
    @Test
    void testRecvMessageActionType1() throws Throwable {
        // arrange
        GroupRetailAiEngineDispatchMessageDTO dto = new GroupRetailAiEngineDispatchMessageDTO();
        dto.setActionType(1);
        dto.setUserId(123L);
        dto.setAppId("testApp");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(scrmGrowthUserInfoDomainService).updateDBMapInfoByMtUserId(123L, "testApp");
    }

    /**
     * Test not in effective time returns CONSUME_SUCCESS
     */
    @Test
    void testRecvMessageNotInEffectiveTime() throws Throwable {
        // arrange
        GroupRetailAiEngineDispatchMessageDTO dto = new GroupRetailAiEngineDispatchMessageDTO();
        dto.setUserId(123L);
        dto.setAppId("testApp");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(consumerConfig.checkInEffectiveTime()).thenReturn(false);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(consumerConfig).checkInEffectiveTime();
    }

    /**
     * Test not in whitelist returns CONSUME_SUCCESS
     */
    @Test
    void testRecvMessageNotInWhitelist() throws Throwable {
        // arrange
        GroupRetailAiEngineDispatchMessageDTO dto = new GroupRetailAiEngineDispatchMessageDTO();
        dto.setUserId(123L);
        dto.setAppId("testApp");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(123L, "testApp")).thenReturn(false);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(consumerConfig).isInWhitelist(123L, "testApp");
    }

    /**
     * Test existing task log with whitelist and expired update time deletes old log
     */
    @Test
    void testRecvMessageExistingTaskLogWithWhitelist() throws Throwable {
        // arrange
        GroupRetailAiEngineDispatchMessageDTO dto = new GroupRetailAiEngineDispatchMessageDTO();
        dto.setUserId(123L);
        dto.setAppId("testApp");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        Calendar logExpireCalendar = Calendar.getInstance();
        logExpireCalendar.add(Calendar.MINUTE, -11);
        ScrmIntelligentFollowTaskLogDO existingLog = new ScrmIntelligentFollowTaskLogDO();
        existingLog.setId(1L);
        existingLog.setUpdateTime(logExpireCalendar.getTime());
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(123L, "testApp")).thenReturn(true);
        when(consumerConfig.isInWhitelistPure(123L)).thenReturn(true);
        when(consumerConfig.getValidAppIds()).thenReturn(Arrays.asList("testApp"));
        when(intelligentFollowTaskLogDOMapper.selectByExample(any(ScrmIntelligentFollowTaskLogDOExample.class))).thenReturn(Arrays.asList(existingLog));
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(intelligentFollowTaskLogDOMapper).deleteByPrimaryKey(1L);
    }

    /**
     * Test existing task log with non-expired update time returns CONSUME_SUCCESS
     */
    @Test
    void testRecvMessageExistingTaskLogNonExpired() throws Throwable {
        // arrange
        GroupRetailAiEngineDispatchMessageDTO dto = new GroupRetailAiEngineDispatchMessageDTO();
        dto.setUserId(123L);
        dto.setAppId("testApp");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        ScrmIntelligentFollowTaskLogDO existingLog = new ScrmIntelligentFollowTaskLogDO();
        existingLog.setId(1L);
        // Current time, not expired
        existingLog.setUpdateTime(new Date());
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(123L, "testApp")).thenReturn(true);
        when(consumerConfig.getValidAppIds()).thenReturn(Arrays.asList("testApp"));
        when(intelligentFollowTaskLogDOMapper.selectByExample(any(ScrmIntelligentFollowTaskLogDOExample.class))).thenReturn(Arrays.asList(existingLog));
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(intelligentFollowTaskLogDOMapper, never()).deleteByPrimaryKey(anyLong());
    }

    /**
     * Test Redis lock failure returns CONSUME_SUCCESS
     */
    @Test
    void testRecvMessageRedisLockFailure() throws Throwable {
        // arrange
        GroupRetailAiEngineDispatchMessageDTO dto = new GroupRetailAiEngineDispatchMessageDTO();
        dto.setUserId(123L);
        dto.setAppId("testApp");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(123L, "testApp")).thenReturn(true);
        when(consumerConfig.getValidAppIds()).thenReturn(Arrays.asList("testApp"));
        when(intelligentFollowTaskLogDOMapper.selectByExample(any(ScrmIntelligentFollowTaskLogDOExample.class))).thenReturn(Collections.emptyList());
        when(redisClient.setnx(any(StoreKey.class), eq(true), eq(60))).thenReturn(false);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(redisClient).setnx(any(StoreKey.class), eq(true), eq(60));
    }

    /**
     * Test task log insert failure returns CONSUME_SUCCESS
     */
    @Test
    void testRecvMessageTaskLogInsertFailure() throws Throwable {
        // arrange
        GroupRetailAiEngineDispatchMessageDTO dto = new GroupRetailAiEngineDispatchMessageDTO();
        dto.setUserId(123L);
        dto.setAppId("testApp");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(123L, "testApp")).thenReturn(true);
        when(consumerConfig.getValidAppIds()).thenReturn(Arrays.asList("testApp"));
        when(intelligentFollowTaskLogDOMapper.selectByExample(any(ScrmIntelligentFollowTaskLogDOExample.class))).thenReturn(Collections.emptyList());
        when(redisClient.setnx(any(StoreKey.class), eq(true), eq(60))).thenReturn(true);
        when(intelligentFollowTaskLogDOMapper.insertSelective(any(ScrmIntelligentFollowTaskLogDO.class))).thenReturn(0);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(intelligentFollowTaskLogDOMapper).insertSelective(any(ScrmIntelligentFollowTaskLogDO.class));
    }

    /**
     * Test exception case returns CONSUME_SUCCESS
     */
    @Test
    void testRecvMessageExceptionCase() throws Throwable {
        // arrange
        GroupRetailAiEngineDispatchMessageDTO dto = new GroupRetailAiEngineDispatchMessageDTO();
        dto.setUserId(123L);
        dto.setAppId("testApp");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(123L, "testApp")).thenReturn(true);
        when(consumerConfig.getValidAppIds()).thenReturn(Arrays.asList("testApp"));
        when(intelligentFollowTaskLogDOMapper.selectByExample(any(ScrmIntelligentFollowTaskLogDOExample.class))).thenThrow(new RuntimeException("Test exception"));
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }
}
