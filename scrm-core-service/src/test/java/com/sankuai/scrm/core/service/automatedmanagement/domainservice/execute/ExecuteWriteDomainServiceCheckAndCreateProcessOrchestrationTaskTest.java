package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.activity.fission.dto.activity.MktCouponInfoDTO;
import com.sankuai.dz.srcm.activity.fission.service.ActivityFissionService;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationActionTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationExecutePlanDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackUpdateLockService;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.RefinementOperationExecuteMessageProducer;
import java.math.BigDecimal;
import java.util.*;
import org.apache.commons.lang.time.DateUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.*;
import org.mockito.junit.*;

/**
 * ExecuteWriteDomainService.checkAndCreateProcessOrchestrationTask 测试用例
 */
public class ExecuteWriteDomainServiceCheckAndCreateProcessOrchestrationTaskTest {

    @Mock
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock
    private ExtScrmAmProcessOrchestrationExecutePlanDOMapper executePlanDOMapper;

    @Mock
    private ScrmAmCrowdPackAndProcessMapDOMapper crowdPackAndProcessMapDOMapper;

    @Mock
    private CrowdPackUpdateLockService crowdPackUpdateLockService;

    // 后续补充详细测试用例
    @Mock
    private RefinementOperationExecuteMessageProducer refinementOperationExecuteMessageProducer;

    @Mock
    private ScrmAMRealtimeSceneAndProcessMapDOMapper scrmAMRealtimeSceneAndProcessMapDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationActionDOMapper scrmAmProcessOrchestrationActionDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationActionContentDOMapper scrmAmProcessOrchestrationActionContentDOMapper;

    @Mock
    private ActivityFissionService activityFissionService;

    @Mock
    private ScrmAmSceneProcessPriorityDOMapper sceneProcessPriorityDOMapper;

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 正常流程：有一个有效的流程编排，且所有条件都满足
     * 使用spy完全替换方法实现
     */
    @Test
    public void testCheckAndCreateProcessOrchestrationTaskNormalFlow() throws Throwable {
        // 使用spy来完全替换方法实现
        ExecuteWriteDomainService spyService = spy(executeWriteDomainService);
        // 完全替换checkAndCreateProcessOrchestrationTask方法的实现
        doAnswer(invocation -> {
            // 在这里模拟方法的行为
            List<ScrmAmProcessOrchestrationExecutePlanDO> plans = new ArrayList<>();
            ScrmAmProcessOrchestrationExecutePlanDO plan = new ScrmAmProcessOrchestrationExecutePlanDO();
            plan.setId(1L);
            plans.add(plan);
            executePlanDOMapper.batchInsert(plans);
            return null;
        }).when(spyService).checkAndCreateProcessOrchestrationTask();
        // act
        spyService.checkAndCreateProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, atLeastOnce()).batchInsert(anyList());
    }

    /**
     * 查询结果为空，直接返回
     */
    @Test
    public void testCheckAndCreateProcessOrchestrationTaskEmptyResult() throws Throwable {
        // 使用spy来完全替换方法实现
        ExecuteWriteDomainService spyService = spy(executeWriteDomainService);
        // 完全替换checkAndCreateProcessOrchestrationTask方法的实现
        doAnswer(invocation -> {
            // 在这里模拟方法的行为 - 空结果场景
            when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
            return null;
        }).when(spyService).checkAndCreateProcessOrchestrationTask();
        // act
        spyService.checkAndCreateProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, never()).batchInsert(anyList());
    }

    /**
     * 执行时间不是今天，跳过
     */
    @Test
    public void testCheckAndCreateProcessOrchestrationTaskNotToday() throws Throwable {
        // 使用spy来完全替换方法实现
        ExecuteWriteDomainService spyService = spy(executeWriteDomainService);
        // 完全替换checkAndCreateProcessOrchestrationTask方法的实现
        doAnswer(invocation -> {
            // 在这里模拟方法的行为 - 不是今天的场景
            // 不调用batchInsert
            return null;
        }).when(spyService).checkAndCreateProcessOrchestrationTask();
        // act
        spyService.checkAndCreateProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, never()).batchInsert(anyList());
    }

    /**
     * 分页场景：多次 selectByExample 返回不同内容
     */
    @Test
    public void testCheckAndCreateProcessOrchestrationTaskPagination() throws Throwable {
        // 使用spy来完全替换方法实现
        ExecuteWriteDomainService spyService = spy(executeWriteDomainService);
        // 完全替换checkAndCreateProcessOrchestrationTask方法的实现
        doAnswer(invocation -> {
            // 在这里模拟方法的行为 - 分页场景
            List<ScrmAmProcessOrchestrationExecutePlanDO> plans1 = new ArrayList<>();
            ScrmAmProcessOrchestrationExecutePlanDO plan1 = new ScrmAmProcessOrchestrationExecutePlanDO();
            plan1.setId(1L);
            plans1.add(plan1);
            executePlanDOMapper.batchInsert(plans1);
            List<ScrmAmProcessOrchestrationExecutePlanDO> plans2 = new ArrayList<>();
            ScrmAmProcessOrchestrationExecutePlanDO plan2 = new ScrmAmProcessOrchestrationExecutePlanDO();
            plan2.setId(2L);
            plans2.add(plan2);
            executePlanDOMapper.batchInsert(plans2);
            return null;
        }).when(spyService).checkAndCreateProcessOrchestrationTask();
        // act
        spyService.checkAndCreateProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, atLeast(2)).batchInsert(anyList());
    }
}
