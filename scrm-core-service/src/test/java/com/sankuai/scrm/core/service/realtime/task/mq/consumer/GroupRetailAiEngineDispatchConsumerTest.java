package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.google.common.collect.Lists;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowActionTrackDTO;
import com.sankuai.dz.srcm.friend.dynamiccode.enums.ContactUserActionType;
import com.sankuai.scrm.core.service.chat.domain.PrivateChatDomainService;
import com.sankuai.scrm.core.service.data.statistics.dal.babymapper.ContactUserLogDOMapper;
import com.sankuai.scrm.core.service.data.statistics.dal.entity.ContactUserLogDO;
import com.sankuai.scrm.core.service.group.dal.entity.OperatorHelperPrivateChat;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmRealtimeSceneUserRecordDO;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmRealtimeSceneUserRecordDOMapper;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineDispatchMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.dto.RealTimeTaskConsumerConfigDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.test.util.ReflectionTestUtils;

import javax.annotation.Resource;
import java.util.Date;
import java.util.List;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GroupRetailAiEngineDispatchConsumerTest {

    @InjectMocks
    private GroupRetailAiEngineDispatchConsumer consumer;
    @Mock
    private IConsumerProcessor mockConsumer;

    /**
     * Test successful initialization of consumer
     */
    @Test
    public void testAfterPropertiesSetSuccess() throws Exception {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockProcessor = mock(IConsumerProcessor.class);
            mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), anyString())).thenReturn(mockProcessor);
            // act
            consumer.afterPropertiesSet();
            // assert
            mockedMafkaClient.verify(() -> {
                Properties props = new Properties();
                props.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
                props.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
                props.setProperty(ConsumerConstants.SubscribeGroup, "scrm.group.retail.ai.engine.dispatch.consumer");
                MafkaClient.buildCommonConsumerFactory(props, "scrm.group.retail.ai.engine.dispatch");
            });
            verify(mockProcessor).recvMessageWithParallel(eq(String.class), any());
        }
    }

    /**
     * Test exception when building consumer factory
     */
    @Test
    public void testAfterPropertiesSetBuildConsumerFailure() throws Exception {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), anyString())).thenThrow(new RuntimeException("Build consumer failed"));
            // act & assert
            Exception exception = assertThrows(RuntimeException.class, () -> {
                consumer.afterPropertiesSet();
            });
            assertEquals("Build consumer failed", exception.getMessage());
        }
    }

    /**
     * Test exception when calling recvMessageWithParallel
     */
    @Test
    public void testAfterPropertiesSetRecvMessageFailure() throws Exception {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockProcessor = mock(IConsumerProcessor.class);
            doThrow(new RuntimeException("Recv message failed")).when(mockProcessor).recvMessageWithParallel(any(), any());
            mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), anyString())).thenReturn(mockProcessor);
            // act & assert
            Exception exception = assertThrows(RuntimeException.class, () -> {
                consumer.afterPropertiesSet();
            });
            assertEquals("Recv message failed", exception.getMessage());
        }
    }

    /**
     * Test that properties are set correctly
     */
    @Test
    public void testAfterPropertiesSetPropertiesConfiguration() throws Exception {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockProcessor = mock(IConsumerProcessor.class);
            mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), anyString())).thenReturn(mockProcessor);
            // act
            consumer.afterPropertiesSet();
            // assert
            mockedMafkaClient.verify(() -> {
                Properties props = new Properties();
                props.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
                props.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
                props.setProperty(ConsumerConstants.SubscribeGroup, "scrm.group.retail.ai.engine.dispatch.consumer");
                MafkaClient.buildCommonConsumerFactory(props, "scrm.group.retail.ai.engine.dispatch");
            });
        }
    }

    /**
     * Test case when consumer is null - should do nothing
     */
    @Test
    public void testDestroyWhenConsumerIsNull() throws Throwable {
        // arrange
        GroupRetailAiEngineDispatchConsumer consumer = new GroupRetailAiEngineDispatchConsumer();
        // consumer field will be null by default
        // act & assert
        assertDoesNotThrow(() -> consumer.destroy());
        // Verify no interactions with our mock
        verifyNoInteractions(mockConsumer);
    }

    /**
     * Test case when consumer is not null - should call close()
     */
    @Test
    public void testDestroyWhenConsumerIsNotNull() throws Throwable {
        // arrange
        // @InjectMocks has already injected our mockConsumer
        // act
        consumer.destroy();
        // assert
        verify(mockConsumer).close();
        verifyNoMoreInteractions(mockConsumer);
    }

    /**
     * Test case when consumer throws exception on close - should propagate exception
     */
    @Test
    public void testDestroyWhenConsumerThrowsException() throws Throwable {
        // arrange
        Exception expectedException = new Exception("Test exception");
        doThrow(expectedException).when(mockConsumer).close();
        // act & assert
        Exception actualException = assertThrows(Exception.class, () -> consumer.destroy());
        verify(mockConsumer).close();
        verifyNoMoreInteractions(mockConsumer);
    }
}
