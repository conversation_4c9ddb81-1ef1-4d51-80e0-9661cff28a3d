package com.sankuai.scrm.core.service.dashboard.crane;

import com.sankuai.scrm.core.service.dashboard.dal.dto.EsTransactionDataSnapshot;
import com.sankuai.scrm.core.service.dashboard.dal.enums.TransactionGtvQueryFieldEnum;
import com.sankuai.scrm.core.service.dashboard.domain.DashBoardESReadDomainService;
import com.sankuai.scrm.core.service.dashboard.domain.DashBoardESWriteDomainService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class TransactionDataTaskGetDataWidelyAndPushToEsTest {

    @InjectMocks
    private TransactionDataTask transactionDataTask;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private DashBoardESReadDomainService dashBoardESReadDomainService;

    @Mock
    private DashBoardESWriteDomainService dashBoardESWriteDomainService;

    @Before
    public void setUp() {
        CorpAppConfig corpAppConfig = new CorpAppConfig();
        corpAppConfig.setCorpId("testCorpId");
        List<CorpAppConfig> allConfigs = Arrays.asList(corpAppConfig);
        when(corpAppConfigRepository.getAllConfigs()).thenReturn(allConfigs);
        when(dashBoardESReadDomainService.countTotalOrdersWidely(anyString(), any(LocalDate.class), any(LocalDate.class))).thenReturn(1L);
        when(dashBoardESReadDomainService.sumGtvWidely(eq(TransactionGtvQueryFieldEnum.GTV), anyString(), any(LocalDate.class))).thenReturn(new java.math.BigDecimal("100.0"));
        when(dashBoardESReadDomainService.sumGtvWidely(eq(TransactionGtvQueryFieldEnum.ACTUAL_GTV), anyString(), any(LocalDate.class))).thenReturn(new java.math.BigDecimal("50.0"));
    }

    /**
     * 测试getDataAndPushToEs方法，正常情况
     */
    @Test
    public void testGetDataAndPushToEsNormal() throws Throwable {
        // act
        transactionDataTask.getDataAndPushToEs();
        // assert
        verify(corpAppConfigRepository, times(1)).getAllConfigs();
    }

    /**
     * 测试getDataAndPushToEs方法，异常情况
     */
    @Test
    public void testGetDataAndPushToEsException() throws Throwable {
        // arrange
        when(corpAppConfigRepository.getAllConfigs()).thenThrow(new RuntimeException());
        // act
        transactionDataTask.getDataAndPushToEs();
        // assert
        verify(corpAppConfigRepository, times(1)).getAllConfigs();
        verify(dashBoardESReadDomainService, never()).countTotalOrdersWidely(anyString(), any(LocalDate.class), any(LocalDate.class));
        verify(dashBoardESReadDomainService, never()).sumGtvWidely(eq(TransactionGtvQueryFieldEnum.GTV), anyString(), any(LocalDate.class));
        verify(dashBoardESReadDomainService, never()).sumGtvWidely(eq(TransactionGtvQueryFieldEnum.ACTUAL_GTV), anyString(), any(LocalDate.class));
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(anyString(), any(EsTransactionDataSnapshot.class), anyString());
    }
}
