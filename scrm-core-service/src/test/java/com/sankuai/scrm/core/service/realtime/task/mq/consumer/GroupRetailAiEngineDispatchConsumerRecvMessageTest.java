package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.scrm.core.service.aigc.service.dal.entity.ScrmIntelligentFollowTaskLogDO;
import com.sankuai.scrm.core.service.aigc.service.dal.mapper.ScrmIntelligentFollowTaskLogDOMapper;
import com.sankuai.scrm.core.service.aigc.service.domainservice.ScrmFridayIntelligentFollowDomainService;
import com.sankuai.scrm.core.service.chat.domain.PrivateChatDomainService;
import com.sankuai.scrm.core.service.data.statistics.dal.babymapper.ContactUserLogDOMapper;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.acl.track.UserTrackAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.track.result.UserTrackResult;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmRealtimeSceneUserRecordDOMapper;
import com.sankuai.scrm.core.service.realtime.task.dto.RealTimeTaskConsumerConfigDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class GroupRetailAiEngineDispatchConsumerRecvMessageTest {

    @Mock
    private RealTimeTaskConsumerConfig consumerConfig;

    @Mock
    private ScrmIntelligentFollowTaskLogDOMapper intelligentFollowTaskLogDOMapper;

    @Mock
    private UserTrackAcl userTrackAcl;

    @Mock
    private ScrmFridayIntelligentFollowDomainService fridayIntelligentFollowDomainService;

    @Mock
    private ScrmRealtimeSceneUserRecordDOMapper realtimeSceneUserRecordDOMapper;

    @Mock
    private ContactUserLogDOMapper contactUserLogDOMapper;

    @Mock
    private PrivateChatDomainService privateChatDomainService;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private PoiRelationService poiRelationService;

    @InjectMocks
    private GroupRetailAiEngineDispatchConsumer consumer;

    @Mock(lenient = true)
    private RedisStoreClient redisClient;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        // Setup default config
        RealTimeTaskConsumerConfigDTO configDTO = new RealTimeTaskConsumerConfigDTO();
        configDTO.setDayNum(7);
        configDTO.setSceneId(1);
        when(consumerConfig.getConfigDTO()).thenReturn(configDTO);
    }

    private ConsumeStatus invokeRecvMessage(MafkaMessage<String> message, MessagetContext context) throws Exception {
        Method method = GroupRetailAiEngineDispatchConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        method.setAccessible(true);
        return (ConsumeStatus) method.invoke(consumer, message, context);
    }

    /**
     * Test when not in effective time
     */
    @Test
    public void testRecvMessageWhenNotInEffectiveTime() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "{}");
        when(consumerConfig.checkInEffectiveTime()).thenReturn(false);
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(consumerConfig).checkInEffectiveTime();
    }

    /**
     * Test when user is not in whitelist
     */
    @Test
    public void testRecvMessageWhenUserNotInWhitelist() throws Throwable {
        // arrange
        String messageBody = "{\"userId\":123,\"appId\":\"test\"}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", messageBody);
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(anyLong(), anyString())).thenReturn(false);
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(consumerConfig).isInWhitelist(123L, "test");
    }

    /**
     * Test when existing task log is not expired
     */
    @Test
    public void testRecvMessageWhenExistingTaskLogNotExpired() throws Throwable {
        // arrange
        String messageBody = "{\"userId\":123,\"appId\":\"test\"}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", messageBody);
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(anyLong(), anyString())).thenReturn(true);
        ScrmIntelligentFollowTaskLogDO existingLog = new ScrmIntelligentFollowTaskLogDO();
        existingLog.setUpdateTime(new Date());
        List<ScrmIntelligentFollowTaskLogDO> logList = new ArrayList<>();
        logList.add(existingLog);
        when(intelligentFollowTaskLogDOMapper.selectByExample(any())).thenReturn(logList);
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(intelligentFollowTaskLogDOMapper, never()).deleteByPrimaryKey(any());
    }

    /**
     * Test when existing task log is expired
     */
    @Test
    public void testRecvMessageWhenExistingTaskLogExpired() throws Throwable {
        // arrange
        String messageBody = "{\"userId\":123,\"appId\":\"test\"}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", messageBody);
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(anyLong(),anyString())).thenReturn(true);
        Calendar expiredTime = Calendar.getInstance();
        expiredTime.add(Calendar.MINUTE, -11);
        ScrmIntelligentFollowTaskLogDO existingLog = new ScrmIntelligentFollowTaskLogDO();
        existingLog.setId(1L);
        existingLog.setUpdateTime(expiredTime.getTime());
        List<ScrmIntelligentFollowTaskLogDO> logList = new ArrayList<>();
        logList.add(existingLog);
        when(intelligentFollowTaskLogDOMapper.selectByExample(any())).thenReturn(logList);
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
//        verify(intelligentFollowTaskLogDOMapper).deleteByPrimaryKey(1L);
    }

    /**
     * Test when task log insertion fails
     */
    @Test
    public void testRecvMessageWhenTaskLogInsertionFails() throws Throwable {
        // arrange
        String messageBody = "{\"userId\":123,\"appId\":\"test\"}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", messageBody);
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(anyLong(), anyString())).thenReturn(true);
        when(intelligentFollowTaskLogDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(intelligentFollowTaskLogDOMapper.insertSelective(any())).thenReturn(0);
        when(redisClient.setnx(any(), any(), anyInt())).thenReturn(true);
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(intelligentFollowTaskLogDOMapper).insertSelective(any());
    }

    /**
     * Test when user track results are empty
     */
    @Test
    public void testRecvMessageWhenUserTrackResultsEmpty() throws Throwable {
        // arrange
        String messageBody = "{\"userId\":123,\"appId\":\"test\"}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", messageBody);
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(anyLong(), anyString())).thenReturn(true);
        when(intelligentFollowTaskLogDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(intelligentFollowTaskLogDOMapper.insertSelective(any())).thenReturn(1);
        when(mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(anyLong(), anyString())).thenReturn("union123");
        when(userTrackAcl.queryUserTrack(any())).thenReturn(new ArrayList<>());
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userTrackAcl, atMost(2)).queryUserTrack(any());
    }

    /**
     * Test successful processing with all data
     */
    @Test
    public void testRecvMessageSuccessfullyWithAllData() throws Throwable {
        // arrange
        String messageBody = "{\"userId\":123,\"appId\":\"test\"}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", messageBody);
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(anyLong(),anyString())).thenReturn(true);
        when(intelligentFollowTaskLogDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(intelligentFollowTaskLogDOMapper.insertSelective(any())).thenReturn(1);
        // Mock other dependencies
        when(mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(anyLong(), anyString())).thenReturn("union123");
        when(realtimeSceneUserRecordDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(contactUserLogDOMapper.selectByUnionId(anyString(), any())).thenReturn(new ArrayList<>());
        when(privateChatDomainService.queryPrivateChatLogForActions(anyString(), anyString(), any())).thenReturn(new ArrayList<>());
        // Create track results
        List<UserTrackResult> mtResults = new ArrayList<>();
        UserTrackResult mtResult = new UserTrackResult();
        mtResult.setActionType(1);
        mtResult.setActionTimestamp(System.currentTimeMillis());
        mtResult.setActionContent("12345");
        mtResult.setPlatForm("mt");
        mtResults.add(mtResult);
        List<UserTrackResult> dpResults = new ArrayList<>();
        when(userTrackAcl.queryUserTrack(any())).thenReturn(mtResults, dpResults);
        when(poiRelationService.queryMtByDpIdL(anyLong())).thenReturn(new ArrayList<>());
        IntelligentFollowResultDTO resultDTO = new IntelligentFollowResultDTO();
        when(fridayIntelligentFollowDomainService.queryIntelligentFollowResult(any())).thenReturn(resultDTO);
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
//        verify(fridayIntelligentFollowDomainService).queryIntelligentFollowResult(any());
    }

    /**
     * Test exception handling
     */
    @Test
    public void testRecvMessageWhenExceptionOccurs() throws Throwable {
        // arrange
        String messageBody = "{\"userId\":123,\"appId\":\"test\"}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", messageBody);
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(anyLong(), anyString())).thenReturn(true);
        when(intelligentFollowTaskLogDOMapper.selectByExample(any())).thenThrow(new RuntimeException("Test exception"));
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }
}
