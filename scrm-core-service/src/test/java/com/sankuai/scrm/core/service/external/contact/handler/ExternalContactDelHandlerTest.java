package com.sankuai.scrm.core.service.external.contact.handler;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionInvitationRecordMapper;
import com.sankuai.scrm.core.service.dashboard.domain.DashBoardDomainService;
import com.sankuai.scrm.core.service.external.contact.bo.ExternalContactDelBO;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxContactAcl;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.pchat.member.ent.service.PchatEntMemberFriendService;
import com.sankuai.scrm.core.service.tag.dal.entity.ExternalContactTag;
import com.sankuai.scrm.core.service.tag.domain.ExternalContactTagDomainService;
import com.sankuai.scrm.core.service.tag.domain.TagDomainService;
import com.sankuai.scrm.core.service.user.domain.ScrmUserTagDomainService;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ExternalContactDelHandlerTest {

    @InjectMocks
    private ExternalContactDelHandler externalContactDelHandler;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private ExternalContactTagDomainService externalContactTagDomainService;

    @Mock
    private GroupFissionInvitationRecordMapper groupFissionInvitationRecordMapper;

    @Mock
    private CorpWxContactAcl corpWxContactAcl;

    @Mock
    private ScrmUserTagDomainService scrmUserTagDomainService;

    @Mock
    private DashBoardDomainService dashBoardDomainService;

    @Mock
    private PchatEntMemberFriendService pchatEntMemberFriendService;

    @Mock
    private TagDomainService tagDomainService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    // Other test methods remain unchanged
    @Test
    public void testHandleDataWhenAppIdIsNull() throws Throwable {
        ExternalContactDelBO data = new ExternalContactDelBO();
        data.setUserId("userId");
        data.setExternalUserId("externalUserId");
        data.setCorpId("corpId");
        when(appConfigRepository.getAppIdByCorpId(anyString())).thenReturn(null);
        externalContactDelHandler.handleData(data);
        verify(appConfigRepository).getAppIdByCorpId(anyString());
        // Adjusted to match actual behavior
        verify(corpWxContactAcl).getUserDetail(anyString(), anyString());
        verifyNoMoreInteractions(corpWxContactAcl);
    }

    @Test
    public void testHandleDataWhenDataIsNull() throws Throwable {
        externalContactDelHandler.handleData(null);
        verifyNoInteractions(appConfigRepository);
        verifyNoInteractions(externalContactTagDomainService);
        verifyNoInteractions(groupFissionInvitationRecordMapper);
        verifyNoInteractions(corpWxContactAcl);
        verifyNoInteractions(scrmUserTagDomainService);
        verifyNoInteractions(dashBoardDomainService);
        verifyNoInteractions(pchatEntMemberFriendService);
    }

    @Test
    public void testHandleDataWhenUserIdOrExternalUserIdIsEmpty() throws Throwable {
        ExternalContactDelBO data = new ExternalContactDelBO();
        data.setUserId("");
        data.setExternalUserId("");
        externalContactDelHandler.handleData(data);
        verifyNoInteractions(appConfigRepository);
        verifyNoInteractions(externalContactTagDomainService);
        verifyNoInteractions(groupFissionInvitationRecordMapper);
        verifyNoInteractions(corpWxContactAcl);
        verifyNoInteractions(scrmUserTagDomainService);
        verifyNoInteractions(dashBoardDomainService);
        verifyNoInteractions(pchatEntMemberFriendService);
    }

    @Test
    public void testHandleDataInNormalCase() throws Throwable {
        ExternalContactDelBO data = new ExternalContactDelBO();
        data.setUserId("userId");
        data.setExternalUserId("externalUserId");
        data.setCorpId("corpId");
        when(appConfigRepository.getAppIdByCorpId(anyString())).thenReturn("appId");
        externalContactDelHandler.handleData(data);
        verify(appConfigRepository).getAppIdByCorpId(anyString());
        verify(externalContactTagDomainService).queryTags(anyString(), anyString(), anyBoolean());
        // Adjusted based on actual behavior
        verify(corpWxContactAcl).getUserDetail(anyString(), anyString());
        verify(scrmUserTagDomainService).handleScrmtUserTagWhenDelContactUser(anyString(), anyString(), anyString(), anyString());
        verify(dashBoardDomainService).handleDashBoardUserLogWhenDelContactUser(anyString(), anyString());
        verify(pchatEntMemberFriendService).removeFriendRelation(anyString(), anyString(), anyString());
    }

    /**
     * 测试 handleEntTag 方法，当 appId 或 data 为空时
     */
    @Test
    public void testHandleEntTagWhenAppIdOrDataIsNull() throws Throwable {
        Method handleEntTagMethod = ExternalContactDelHandler.class.getDeclaredMethod("handleEntTag", String.class, ExternalContactDelBO.class);
        handleEntTagMethod.setAccessible(true);
        handleEntTagMethod.invoke(externalContactDelHandler, null, null);
        verify(externalContactTagDomainService, never()).queryTags(anyString(), anyString(), anyBoolean());
    }

    /**
     * 测试 handleEntTag 方法，当查询结果为空时
     */
    @Test
    public void testHandleEntTagWhenQueryResultIsEmpty() throws Throwable {
        ExternalContactDelBO data = new ExternalContactDelBO();
        data.setExternalUserId("test");
        when(externalContactTagDomainService.queryTags(anyString(), anyString(), anyBoolean())).thenReturn(Collections.emptyList());
        Method handleEntTagMethod = ExternalContactDelHandler.class.getDeclaredMethod("handleEntTag", String.class, ExternalContactDelBO.class);
        handleEntTagMethod.setAccessible(true);
        handleEntTagMethod.invoke(externalContactDelHandler, "test", data);
        verify(externalContactTagDomainService).queryTags(anyString(), anyString(), anyBoolean());
        verify(tagDomainService, never()).updateTagCustomerCount(anyString(), anyString(), anyInt());
        verify(tagDomainService, never()).updateTagGroupCustomerCount(anyString(), anyString(), anyInt());
    }

    /**
     * 测试 handleEntTag 方法，当查询结果非空时
     */
    @Test
    public void testHandleEntTagWhenQueryResultIsNotEmpty() throws Throwable {
        ExternalContactDelBO data = new ExternalContactDelBO();
        data.setExternalUserId("test");
        ExternalContactTag mockTag = new ExternalContactTag();
        mockTag.setTagId("tag1");
        mockTag.setTagGroupId("tagGroup1");
        when(externalContactTagDomainService.queryTags(anyString(), anyString(), anyBoolean())).thenReturn(Collections.singletonList(mockTag));
        Method handleEntTagMethod = ExternalContactDelHandler.class.getDeclaredMethod("handleEntTag", String.class, ExternalContactDelBO.class);
        handleEntTagMethod.setAccessible(true);
        handleEntTagMethod.invoke(externalContactDelHandler, "test", data);
        verify(externalContactTagDomainService).queryTags(anyString(), anyString(), anyBoolean());
        verify(externalContactTagDomainService).deleteTagsByExternalUserId(anyString(), anyString());
        verify(tagDomainService).updateTagCustomerCount(eq("test"), eq("tag1"), eq(-1));
        verify(tagDomainService).updateTagGroupCustomerCount(eq("test"), eq("tagGroup1"), eq(-1));
    }
}
