package com.sankuai.scrm.core.service.infrastructure.util;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.meituan.image.client.impl.ImageUploadClientImpl;
import com.meituan.image.client.pojo.ImageResult;
import com.meituan.mdp.boot.starter.mdpcache.core.Cache;
import com.meituan.mdp.boot.starter.mdpcache.core.func.CacheLoader;
import com.meituan.mdp.boot.starter.mdpcache.core.impl.proxy.RefreshPolicy;
import com.meituan.service.inf.kms.client.Kms;
import com.meituan.service.inf.kms.utils.KmsResultNullException;
import com.meituan.service.inf.kms.utils.ResultCode;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

import java.lang.reflect.Field;
import java.lang.reflect.Method;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

/**
 * VenusUtil单元测试类（JUnit 5）
 * 
 * 使用Mockito 3.4+提供的MockedStatic功能实现静态方法模拟
 */
@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class VenusUtilTest {

    private static final String APP_NAME = "test-app";
    private static final String TEST_KEY = "test.key";
    private static final String TEST_SECRET = "test-secret";
    private static final String TEST_CLIENT_ID = "test-client-id";
    private static final String TEST_TOKEN = "test-token";
    private static final int TEST_EXPIRE_TIME = 3600;
    private static final int TEST_ERROR_CODE = 0;

    @Mock
    private Cache mockCache;

    @Mock
    private ImageUploadClientImpl mockClient;

    @Mock
    private ImageResult mockImageResult;

    @Mock
    private RefreshPolicy mockRefreshPolicy;

    @InjectMocks
    private VenusUtil venusUtil;

    @BeforeEach
    public void setUp() throws Exception {
        // 设置模拟对象和行为
        when(mockImageResult.getErrCode()).thenReturn(TEST_ERROR_CODE);
        when(mockImageResult.getExpireTime()).thenReturn((long)TEST_EXPIRE_TIME);
        when(mockImageResult.getToken()).thenReturn(TEST_TOKEN);
        when(mockClient.getToken2(anyInt())).thenReturn(mockImageResult);
        
        // 使用反射设置私有字段
        setField(venusUtil, "client", mockClient);
    }

    /**
     * 通过反射设置对象的私有字段
     */
    private void setField(Object target, String fieldName, Object value) throws Exception {
        Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }

    @Test
    @DisplayName("测试获取Venus客户端ID")
    public void testGetVenusClientId() {
        try (MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class);
             MockedStatic<Lion> mockedLion = Mockito.mockStatic(Lion.class)) {
            
            // 模拟静态方法
            mockedEnvironment.when(Environment::getAppName).thenReturn(APP_NAME);
            mockedLion.when(() -> Lion.getString(
                    eq(APP_NAME), 
                    eq("venus.client.id"), 
                    eq("9rzdqdzx6td7r7wl0000000000c2bae8")))
                .thenReturn(TEST_CLIENT_ID);
            
            // 执行方法
            String result = venusUtil.getVenusClientId();
            
            // 验证结果
            assertEquals(TEST_CLIENT_ID, result);
            
            // 验证Lion.getString被调用
            mockedLion.verify(() -> Lion.getString(
                    eq(APP_NAME),
                    eq("venus.client.id"),
                    eq("9rzdqdzx6td7r7wl0000000000c2bae8")), times(1));
        }
    }

    @Test
    @DisplayName("测试从缓存获取密钥 - 缓存命中")
    public void testGetVenusSecretFromCache_CacheHit() throws Exception {
        // 设置缓存
        setField(venusUtil, "localCache", mockCache);
        
        // 模拟缓存命中
        when(mockCache.get(eq(TEST_KEY), eq(String.class))).thenReturn(TEST_SECRET);

        // 执行方法
        String result = venusUtil.getVenusSecretFromCache(TEST_KEY);

        // 验证缓存方法被调用
        verify(mockCache, times(1)).get(eq(TEST_KEY), eq(String.class));

        // 验证结果
        assertEquals(TEST_SECRET, result);
    }
    
    @Test
    @DisplayName("测试从缓存获取密钥 - 缓存未命中, KMS成功")
    public void testGetVenusSecretFromCache_CacheMiss_KmsSuccess() throws Exception {
        // 使用MockedStatic模拟静态方法
        try (MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class);
             MockedStatic<Kms> mockedKms = Mockito.mockStatic(Kms.class);
             MockedStatic<Lion> mockedLion = Mockito.mockStatic(Lion.class);
             MockedConstruction<ImageUploadClientImpl> mockedClient = 
                     Mockito.mockConstruction(ImageUploadClientImpl.class)) {
            
            // 设置缓存并模拟loader的行为
            CacheLoader<String, String> loader = key -> {
                // 模拟getVenusSecret方法的行为
                String secret = Kms.getByName(Environment.getAppName(), key);
                // 模拟ImageUploadClientImpl构造函数的调用
                new ImageUploadClientImpl(VenusUtil.SCRMUPLOADPIC, 
                        venusUtil.getVenusClientId(), secret);
                return secret;
            };
            
            Cache customCache = mock(Cache.class);
            when(customCache.get(eq(TEST_KEY), eq(String.class))).thenAnswer(invocation -> {
                // 调用loader模拟缓存未命中的情况
                return loader.load(TEST_KEY);
            });
            setField(venusUtil, "localCache", customCache);
            
            // 模拟静态方法
            mockedEnvironment.when(Environment::getAppName).thenReturn(APP_NAME);
            mockedKms.when(() -> Kms.getByName(eq(APP_NAME), eq(TEST_KEY)))
                .thenReturn(TEST_SECRET);
            mockedLion.when(() -> Lion.getString(anyString(), anyString(), anyString()))
                .thenReturn(TEST_CLIENT_ID);
            
            // 执行方法
            String result = venusUtil.getVenusSecretFromCache(TEST_KEY);
            
            // 验证结果
            assertEquals(TEST_SECRET, result);
            
            // 验证Kms.getByName被调用
            // mockedKms.verify(() -> Kms.getByName(eq(APP_NAME), eq(TEST_KEY)), times(1));
            
            // 验证ImageUploadClientImpl构造函数被调用
            assertEquals(1, mockedClient.constructed().size());
        }
    }
    
    @Test
    @DisplayName("测试从缓存获取密钥 - 缓存未命中, KMS失败")
    public void testGetVenusSecretFromCache_CacheMiss_KmsFailure() throws Exception {
        // 使用MockedStatic模拟静态方法
        try (MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class);
             MockedStatic<Kms> mockedKms = Mockito.mockStatic(Kms.class)) {
            
            // 设置缓存并模拟loader的行为
            CacheLoader<String, String> loader = key -> {
                // 模拟getVenusSecret方法的行为，抛出KmsResultNullException
                throw new KmsResultNullException(ResultCode.INTERNAL_ERROR, "Test KMS exception");
            };
            
            Cache customCache = mock(Cache.class);
            when(customCache.get(eq(TEST_KEY), eq(String.class))).thenAnswer(invocation -> {
                try {
                    // 调用loader模拟缓存未命中的情况
                    return loader.load(TEST_KEY);
                } catch (KmsResultNullException e) {
                    // 模拟getVenusSecret处理异常并返回null的行为
                    return null;
                }
            });
            setField(venusUtil, "localCache", customCache);
            
            // 模拟静态方法
            mockedEnvironment.when(Environment::getAppName).thenReturn(APP_NAME);
            mockedKms.when(() -> Kms.getByName(eq(APP_NAME), eq(TEST_KEY))).thenThrow(new KmsResultNullException(ResultCode.INTERNAL_ERROR,"Test KMS exception"));
            
            // 执行方法
            String result = venusUtil.getVenusSecretFromCache(TEST_KEY);
            
            // 验证结果
            assertNull(result);
        }
    }

    @Test
    @DisplayName("测试获取BA Token")
    public void testGetBAToken() throws Exception {
        // 创建一个实际的模拟client对象
        ImageUploadClientImpl mockClientInstance = Mockito.mock(ImageUploadClientImpl.class);
        ImageResult mockImageResultInstance = new ImageResult();
        
        // 配置模拟对象行为
        mockImageResultInstance.setToken(TEST_TOKEN);
        mockImageResultInstance.setErrCode(TEST_ERROR_CODE);
        mockImageResultInstance.setExpireTime((long)TEST_EXPIRE_TIME);
        
        // 配置client的getToken2方法行为
        when(mockClientInstance.getToken2(anyLong())).thenReturn(mockImageResultInstance);
        
        // 使用反射直接设置client字段
        Field clientField = VenusUtil.class.getDeclaredField("client");
        clientField.setAccessible(true);
        clientField.set(venusUtil, mockClientInstance);
        
        // 执行方法
        String result = venusUtil.getBAToken(TEST_EXPIRE_TIME);

        // 验证client.getToken2()被调用
        verify(mockClientInstance, times(1)).getToken2(eq((long)TEST_EXPIRE_TIME));

        // 验证结果
        assertEquals(TEST_TOKEN, result);
    }
    
    @Test
    @DisplayName("测试获取Venus密钥 - 成功场景")
    public void testGetVenusSecret_Success() throws Exception {
        // 使用MockedStatic模拟静态方法
        try (MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class);
             MockedStatic<Kms> mockedKms = Mockito.mockStatic(Kms.class);
             MockedStatic<Lion> mockedLion = Mockito.mockStatic(Lion.class);
             MockedConstruction<ImageUploadClientImpl> mockedClient = 
                     Mockito.mockConstruction(ImageUploadClientImpl.class)) {
            
            // 模拟静态方法行为
            mockedEnvironment.when(Environment::getAppName).thenReturn(APP_NAME);
            mockedKms.when(() -> Kms.getByName(eq(APP_NAME), eq(TEST_KEY)))
                    .thenReturn(TEST_SECRET);
            mockedLion.when(() -> Lion.getString(anyString(), anyString(), anyString()))
                    .thenReturn(TEST_CLIENT_ID);
            
            // 使用反射获取私有方法
            Method getVenusSecretMethod = VenusUtil.class.getDeclaredMethod("getVenusSecret", String.class);
            getVenusSecretMethod.setAccessible(true);
            
            // 调用私有方法
            String result = (String) getVenusSecretMethod.invoke(venusUtil, TEST_KEY);
            
            // 验证结果
            assertEquals(TEST_SECRET, result);
            
            // 验证ImageUploadClientImpl构造函数被调用
            assertEquals(1, mockedClient.constructed().size());
            
            // 验证client字段被设置
            Field clientField = VenusUtil.class.getDeclaredField("client");
            clientField.setAccessible(true);
            Object clientValue = clientField.get(venusUtil);
            assertNotNull(clientValue);
        }
    }
    
//    @Test
//    @DisplayName("测试获取Venus密钥 - KMS异常场景")
//    public void testGetVenusSecret_KmsException() throws Exception {
//        // 使用MockedStatic模拟静态方法
//        try (MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class);
//             MockedStatic<Kms> mockedKms = Mockito.mockStatic(Kms.class)) {
//
//            // 模拟静态方法行为 - KMS抛出异常
//            mockedEnvironment.when(Environment::getAppName).thenReturn(APP_NAME);
//            mockedKms.when(() -> Kms.getByName(eq(APP_NAME), eq(TEST_KEY)))
//                    .thenThrow(new KmsResultNullException(ResultCode.INTERNAL_ERROR, "Test KMS exception"));
//
//            // 使用反射获取私有方法
//            Method getVenusSecretMethod = VenusUtil.class.getDeclaredMethod("getVenusSecret", String.class);
//            getVenusSecretMethod.setAccessible(true);
//
//            // 调用私有方法
//            String result = (String) getVenusSecretMethod.invoke(venusUtil, TEST_KEY);
//
//            // 验证结果为null
//            assertNull(result);
//        }
//    }
} 