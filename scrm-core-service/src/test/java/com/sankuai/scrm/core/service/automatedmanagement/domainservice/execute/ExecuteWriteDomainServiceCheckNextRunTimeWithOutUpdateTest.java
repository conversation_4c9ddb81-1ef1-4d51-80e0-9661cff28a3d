package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationActionSubTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAMRealtimeSceneAndProcessMapDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackBaseInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAMRealtimeSceneAndProcessMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.CrowdFriendTouchAction;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.RefinementOperationExecuteMessageProducer;
import java.lang.reflect.Method;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

import org.mockito.junit.MockitoJUnitRunner;

/**
 * Test cases for ExecuteWriteDomainService.checkNextRunTimeWithOutUpdate method
 */
@RunWith(MockitoJUnitRunner.class)
public class ExecuteWriteDomainServiceCheckNextRunTimeWithOutUpdateTest {

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    private Method checkNextRunTimeWithOutUpdateMethod;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private CrowdFriendTouchAction crowdFriendTouchAction;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAMRealtimeSceneAndProcessMapDOMapper scrmAMRealtimeSceneAndProcessMapDOMapper;

    @Mock
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock
    private RefinementOperationExecuteMessageProducer refinementOperationExecuteMessageProducer;

    @Before
    public void setUp() throws Exception {
        // Use reflection to access the private method
        checkNextRunTimeWithOutUpdateMethod = ExecuteWriteDomainService.class.getDeclaredMethod("checkNextRunTimeWithOutUpdate", ScrmAmProcessOrchestrationInfoDO.class);
        checkNextRunTimeWithOutUpdateMethod.setAccessible(true);
    }

    private Method getTestMethod() throws Exception {
        Method method = ExecuteWriteDomainService.class.getDeclaredMethod("getSceneIdByProcessOrchestrationInfo", ScrmAmProcessOrchestrationInfoDO.class);
        method.setAccessible(true);
        return method;
    }

    private List<ScrmAmCrowdPackBaseInfoDO> createTestRecords(int count) {
        List<ScrmAmCrowdPackBaseInfoDO> records = new ArrayList<>();
        for (long i = 0; i < count; i++) {
            records.add(createTestRecord(i));
        }
        return records;
    }

    private ScrmAmCrowdPackBaseInfoDO createTestRecord(long id) {
        ScrmAmCrowdPackBaseInfoDO record = new ScrmAmCrowdPackBaseInfoDO();
        record.setId(id);
        record.setPersonaId((int) id);
        record.setAppId("test-app-" + id);
        record.setValidPackVersion("v1");
        return record;
    }

    /**
     * Test when processOrchestrationInfoDO is null
     */
    @Test(expected = NullPointerException.class)
    public void testCheckNextRunTimeWithOutUpdate_NullInput() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = null;
        // act
        try {
            Date result = (Date) checkNextRunTimeWithOutUpdateMethod.invoke(executeWriteDomainService, infoDO);
        } catch (Exception e) {
            if (e.getCause() instanceof NullPointerException) {
                throw e.getCause();
            }
            throw e;
        }
        // assert
        fail("Expected NullPointerException but no exception was thrown");
    }

    /**
     * Test when current time is after endTime
     */
    @Test
    public void testCheckNextRunTimeWithOutUpdate_AfterEndTime() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, -2);
        infoDO.setEndTime(cal.getTime());
        infoDO.setBeginTime(new Date());
        infoDO.setCronComment("2023-12-31 10:00:00");
        // act
        Date result = (Date) checkNextRunTimeWithOutUpdateMethod.invoke(executeWriteDomainService, infoDO);
        // assert
        assertNull(result);
    }

    /**
     * Test when current time is before beginTime
     */
    @Test
    public void testCheckNextRunTimeWithOutUpdate_BeforeBeginTime() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 2);
        infoDO.setBeginTime(cal.getTime());
        infoDO.setEndTime(cal.getTime());
        infoDO.setCronComment("2023-12-31 10:00:00");
        // act
        Date result = (Date) checkNextRunTimeWithOutUpdateMethod.invoke(executeWriteDomainService, infoDO);
        // assert
        assertNull(result);
    }

    /**
     * Test when cronComment is blank
     */
    @Test
    public void testCheckNextRunTimeWithOutUpdate_BlankCronComment() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setBeginTime(new Date());
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 1);
        infoDO.setEndTime(cal.getTime());
        infoDO.setCronComment("");
        // act
        Date result = (Date) checkNextRunTimeWithOutUpdateMethod.invoke(executeWriteDomainService, infoDO);
        // assert
        assertNull(result);
    }

    /**
     * Test for TIMED_PROCESS_ORCHESTRATION type
     */
    @Test
    public void testCheckNextRunTimeWithOutUpdate_TimedProcessOrchestration() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setBeginTime(new Date());
        // Set end time to tomorrow
        Calendar endCal = Calendar.getInstance();
        endCal.add(Calendar.DAY_OF_MONTH, 1);
        endCal.set(Calendar.HOUR_OF_DAY, 23);
        endCal.set(Calendar.MINUTE, 59);
        endCal.set(Calendar.SECOND, 59);
        infoDO.setEndTime(endCal.getTime());
        // Set cronComment to a time today
        Calendar cronCal = Calendar.getInstance();
        // Set to 1 hour from now
        cronCal.add(Calendar.HOUR, 1);
        infoDO.setCronComment(String.format("%1$tY-%1$tm-%1$td %1$tH:%1$tM:00", cronCal));
        // TIMED_PROCESS_ORCHESTRATION
        infoDO.setProcessOrchestrationType((byte) 4);
        // act
        Date result = (Date) checkNextRunTimeWithOutUpdateMethod.invoke(executeWriteDomainService, infoDO);
        // assert
        assertNotNull(result);
    }

    /**
     * Test for non-TIMED_PROCESS_ORCHESTRATION type
     */
    @Test
    public void testCheckNextRunTimeWithOutUpdate_NonTimedProcessOrchestration() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setBeginTime(new Date());
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 1);
        infoDO.setEndTime(cal.getTime());
        infoDO.setCron("0 0 10 * * ?");
        infoDO.setCronComment("0 0 10 * * ?");
        infoDO.setProcessOrchestrationType((byte) 1);
        // act
        Date result = (Date) checkNextRunTimeWithOutUpdateMethod.invoke(executeWriteDomainService, infoDO);
        // assert
        assertNotNull(result);
    }

    /**
     * Test when result time is before current time
     */
    @Test
    public void testCheckNextRunTimeWithOutUpdate_ResultBeforeCurrentTime() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setBeginTime(new Date());
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 1);
        infoDO.setEndTime(cal.getTime());
        cal.add(Calendar.DAY_OF_MONTH, -2);
        infoDO.setCronComment(String.format("%1$tY-%1$tm-%1$td 10:00:00", cal));
        infoDO.setProcessOrchestrationType((byte) 4);
        // act
        Date result = (Date) checkNextRunTimeWithOutUpdateMethod.invoke(executeWriteDomainService, infoDO);
        // assert
        assertNull(result);
    }

    /**
     * Test for invalid cron expression
     */
    @Test
    public void testCheckNextRunTimeWithOutUpdate_InvalidCronExpression() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setBeginTime(new Date());
        Calendar cal = Calendar.getInstance();
        cal.add(Calendar.DAY_OF_MONTH, 1);
        infoDO.setEndTime(cal.getTime());
        infoDO.setCron("invalid cron");
        infoDO.setCronComment("invalid cron");
        infoDO.setProcessOrchestrationType((byte) 1);
        // act
        Date result = (Date) checkNextRunTimeWithOutUpdateMethod.invoke(executeWriteDomainService, infoDO);
        // assert
        assertNull(result);
    }

    /**
     * Test when CompletableFuture join returns null
     */
    @Test
    public void testExecuteWxOfficialService_WhenCompletableFutureJoinReturnsNull() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        String appId = "testAppId";
        // Mock executeLogDOMapper to return non-empty list
        List<ScrmAmProcessOrchestrationExecuteLogDO> executeLogDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationExecuteLogDO logDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        logDO.setActionSubType(ScrmProcessOrchestrationActionSubTypeEnum.SEND_GROUP_MESSAGE.getValue());
        executeLogDOS.add(logDO);
        when(executeLogDOMapper.selectByExample(any())).thenReturn(executeLogDOS);
        // Create a CompletableFuture that is completed with null
        CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(null);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(processOrchestrationId, appId, processOrchestrationVersion)).thenReturn(future);
        // act
        boolean result = executeWriteDomainService.executeWxOfficialService(processOrchestrationId, processOrchestrationVersion, appId);
        // assert
        assertFalse(result);
        verify(processOrchestrationReadDomainService).queryProcessOrchestrationDetailFuture(processOrchestrationId, appId, processOrchestrationVersion);
    }

    /**
     * Test when process orchestration type is REAL_TIME_PROCESS_ORCHESTRATION
     */
    @Test
    public void testExecuteWxOfficialService_WhenRealTimeProcessOrchestration() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        String appId = "testAppId";
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue().byteValue());
        List<ScrmAmProcessOrchestrationExecuteLogDO> executeLogDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationExecuteLogDO logDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        logDO.setExecutorId("executor1");
        logDO.setActionSubType(ScrmProcessOrchestrationActionSubTypeEnum.SEND_GROUP_MESSAGE.getValue());
        executeLogDOS.add(logDO);
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        executeManagementDTO.setExecutedStaffIds(new HashSet<>());
        CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(processOrchestrationDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(processOrchestrationId, appId, processOrchestrationVersion)).thenReturn(future);
        when(executeManagementService.getExecuteMediumManagementDTO(processOrchestrationDTO)).thenReturn(executeManagementDTO);
        when(executeLogDOMapper.selectByExample(any())).thenReturn(executeLogDOS);
        // act
        boolean result = executeWriteDomainService.executeWxOfficialService(processOrchestrationId, processOrchestrationVersion, appId);
        // assert
        assertTrue(result);
        verify(executeManagementService).getExecuteMediumManagementDTO(processOrchestrationDTO);
        verify(crowdFriendTouchAction).executeAfterNodeDealLogic(eq(processOrchestrationDTO), any(), eq(executeManagementDTO), any());
    }

    /**
     * Test default case when no conditions match
     */
    @Test
    public void testExecuteWxOfficialService_WhenNoConditionsMatch() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        String appId = "testAppId";
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setProcessOrchestrationType((byte) 2);
        List<ScrmAmProcessOrchestrationExecuteLogDO> executeLogDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationExecuteLogDO logDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        logDO.setActionSubType(99);
        executeLogDOS.add(logDO);
        CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(processOrchestrationDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(processOrchestrationId, appId, processOrchestrationVersion)).thenReturn(future);
        when(executeLogDOMapper.selectByExample(any())).thenReturn(executeLogDOS);
        // act
        boolean result = executeWriteDomainService.executeWxOfficialService(processOrchestrationId, processOrchestrationVersion, appId);
        // assert
        assertFalse(result);
        verify(crowdFriendTouchAction, never()).checkStatus(any());
    }

    /**
     * Test case for null input
     */
    @Test
    public void testGetSceneIdByProcessOrchestrationInfo_NullInput() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = null;
        // act
        Long result = (Long) getTestMethod().invoke(executeWriteDomainService, infoDO);
        // assert
        assertNull(result);
    }

    /**
     * Test case for input with null required fields
     */
    @Test
    public void testGetSceneIdByProcessOrchestrationInfo_NullRequiredFields() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(null);
        infoDO.setValidVersion(null);
        // act
        Long result = (Long) getTestMethod().invoke(executeWriteDomainService, infoDO);
        // assert
        assertNull(result);
    }

    /**
     * Test case for no results found
     */
    @Test
    public void testGetSceneIdByProcessOrchestrationInfo_NoResults() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setValidVersion("v1");
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        Long result = (Long) getTestMethod().invoke(executeWriteDomainService, infoDO);
        // assert
        assertNull(result);
    }

    /**
     * Test case for normal case with single result
     */
    @Test
    public void testGetSceneIdByProcessOrchestrationInfo_NormalCase() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setValidVersion("v1");
        ScrmAMRealtimeSceneAndProcessMapDO mapDO = new ScrmAMRealtimeSceneAndProcessMapDO();
        mapDO.setSceneId(100L);
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(mapDO));
        // act
        Long result = (Long) getTestMethod().invoke(executeWriteDomainService, infoDO);
        // assert
        assertEquals(Long.valueOf(100L), result);
    }

    /**
     * Test case for multiple results (should return first one)
     */
    @Test
    public void testGetSceneIdByProcessOrchestrationInfo_MultipleResults() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setValidVersion("v1");
        List<ScrmAMRealtimeSceneAndProcessMapDO> mapDOList = new ArrayList<>();
        ScrmAMRealtimeSceneAndProcessMapDO mapDO1 = new ScrmAMRealtimeSceneAndProcessMapDO();
        mapDO1.setSceneId(100L);
        ScrmAMRealtimeSceneAndProcessMapDO mapDO2 = new ScrmAMRealtimeSceneAndProcessMapDO();
        mapDO2.setSceneId(200L);
        mapDOList.add(mapDO1);
        mapDOList.add(mapDO2);
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any())).thenReturn(mapDOList);
        // act
        Long result = (Long) getTestMethod().invoke(executeWriteDomainService, infoDO);
        // assert
        assertEquals(Long.valueOf(100L), result);
    }

    /**
     * Test when task is already running
     */
    @Test
    public void testUpdatePersonaCrowdPackMainTask_TaskAlreadyRunning() throws Throwable {
        // arrange
        when(executeManagementService.taskRunFirstTime(eq(29), eq(0L))).thenReturn(false);
        // act
        executeWriteDomainService.updatePersonaCrowdPackMainTask();
        // assert
        verify(crowdPackReadDomainService, never()).pagePersonaPackInfo(anyLong(), anyInt());
        verify(refinementOperationExecuteMessageProducer, never()).sendPersonaCrowdPackUpdateTaskExecuteMessage(any(), any(), any(), any());
    }

    /**
     * Test when no records exist
     */
    @Test
    public void testUpdatePersonaCrowdPackMainTask_NoRecords() throws Throwable {
        // arrange
        when(executeManagementService.taskRunFirstTime(eq(29), eq(0L))).thenReturn(true);
        when(crowdPackReadDomainService.pagePersonaPackInfo(eq(0L), eq(2000))).thenReturn(Collections.emptyList());
        // act
        executeWriteDomainService.updatePersonaCrowdPackMainTask();
        // assert
        verify(refinementOperationExecuteMessageProducer, never()).sendPersonaCrowdPackUpdateTaskExecuteMessage(any(), any(), any(), any());
        verify(executeManagementService).taskRunFinished(eq(29), eq(0L));
    }

    /**
     * Test with single page of records
     */
    @Test
    public void testUpdatePersonaCrowdPackMainTask_SinglePage() throws Throwable {
        // arrange
        when(executeManagementService.taskRunFirstTime(eq(29), eq(0L))).thenReturn(true);
        // Create a single record
        ScrmAmCrowdPackBaseInfoDO record = createTestRecord(0L);
        List<ScrmAmCrowdPackBaseInfoDO> records = Collections.singletonList(record);
        // Setup mock behavior - only return records for the first call
        // First call returns our record
        // First call returns our record
        // Subsequent calls return empty list
        // Subsequent calls return empty list
        when(crowdPackReadDomainService.pagePersonaPackInfo(anyLong(), anyInt())).thenReturn(records).thenReturn(Collections.emptyList());
        // act
        executeWriteDomainService.updatePersonaCrowdPackMainTask();
        // assert
        // personaId
        // personaId
        // id
        // appId
        // validPackVersion
        verify(refinementOperationExecuteMessageProducer).sendPersonaCrowdPackUpdateTaskExecuteMessage(eq(0), eq(0L), eq("test-app-0"), eq("v1"));
        verify(executeManagementService).taskRunFinished(eq(29), eq(0L));
    }

    /**
     * Test with multiple pages of records
     */
    @Test
    public void testUpdatePersonaCrowdPackMainTask_MultiplePages() throws Throwable {
        // arrange
        when(executeManagementService.taskRunFirstTime(eq(29), eq(0L))).thenReturn(true);
        List<ScrmAmCrowdPackBaseInfoDO> page1 = createTestRecords(2000);
        List<ScrmAmCrowdPackBaseInfoDO> page2 = createTestRecords(500);
        when(crowdPackReadDomainService.pagePersonaPackInfo(eq(0L), eq(2000))).thenReturn(page1);
        when(crowdPackReadDomainService.pagePersonaPackInfo(eq(1999L), eq(2000))).thenReturn(page2);
        // act
        executeWriteDomainService.updatePersonaCrowdPackMainTask();
        // assert
        verify(refinementOperationExecuteMessageProducer, times(2500)).sendPersonaCrowdPackUpdateTaskExecuteMessage(any(), any(), any(), any());
        verify(executeManagementService).taskRunFinished(eq(29), eq(0L));
    }

    /**
     * Test with null record in the list
     */
    @Test
    public void testUpdatePersonaCrowdPackMainTask_NullRecord() throws Throwable {
        // arrange
        when(executeManagementService.taskRunFirstTime(eq(29), eq(0L))).thenReturn(true);
        // Create a list with one valid record
        ScrmAmCrowdPackBaseInfoDO validRecord = createTestRecord(1L);
        List<ScrmAmCrowdPackBaseInfoDO> records = new ArrayList<>();
        records.add(validRecord);
        // Mock first call to return records with valid record
        when(crowdPackReadDomainService.pagePersonaPackInfo(eq(0L), eq(2000))).thenReturn(records);
        // Mock second call to return empty list to end the loop
        // act
        executeWriteDomainService.updatePersonaCrowdPackMainTask();
        // assert
        verify(refinementOperationExecuteMessageProducer, times(1)).sendPersonaCrowdPackUpdateTaskExecuteMessage(eq(validRecord.getPersonaId()), eq(validRecord.getId()), eq(validRecord.getAppId()), eq(validRecord.getValidPackVersion()));
        verify(executeManagementService).taskRunFinished(eq(29), eq(0L));
    }

    /**
     * Test exception handling
     */
    @Test
    public void testUpdatePersonaCrowdPackMainTask_ExceptionHandling() throws Throwable {
        // arrange
        when(executeManagementService.taskRunFirstTime(eq(29), eq(0L))).thenReturn(true);
        // Throw exception on the first call
        doThrow(new RuntimeException("Test exception")).when(crowdPackReadDomainService).pagePersonaPackInfo(eq(0L), eq(2000));
        // Ensure taskRunFinished is called even when exception occurs
        doNothing().when(executeManagementService).taskRunFinished(anyInt(), anyLong());
        // act & assert
        try {
            executeWriteDomainService.updatePersonaCrowdPackMainTask();
        } catch (Exception e) {
            // Expected exception, ignore
        }
        // verify taskRunFinished is called in finally block
        verify(executeManagementService).taskRunFinished(eq(29), eq(0L));
        verify(refinementOperationExecuteMessageProducer, never()).sendPersonaCrowdPackUpdateTaskExecuteMessage(any(), any(), any(), any());
    }

    /**
     * Test with invalid persona ID
     */
    @Test
    public void testUpdatePersonaCrowdPackMainTask_InvalidPersonaId() throws Throwable {
        // arrange
        when(executeManagementService.taskRunFirstTime(eq(29), eq(0L))).thenReturn(true);
        // Create record with null personaId
        ScrmAmCrowdPackBaseInfoDO invalidRecord = createTestRecord(1L);
        invalidRecord.setPersonaId(null);
        List<ScrmAmCrowdPackBaseInfoDO> records = new ArrayList<>();
        records.add(invalidRecord);
        when(crowdPackReadDomainService.pagePersonaPackInfo(eq(0L), eq(2000))).thenReturn(records);
        // act
        executeWriteDomainService.updatePersonaCrowdPackMainTask();
        // assert
        verify(refinementOperationExecuteMessageProducer, times(1)).sendPersonaCrowdPackUpdateTaskExecuteMessage(isNull(), eq(invalidRecord.getId()), eq(invalidRecord.getAppId()), eq(invalidRecord.getValidPackVersion()));
        verify(executeManagementService).taskRunFinished(eq(29), eq(0L));
    }

    /**
     * Test boundary condition with exactly 2000 records
     */
    @Test
    public void testUpdatePersonaCrowdPackMainTask_ExactPageSize() throws Throwable {
        // arrange
        when(executeManagementService.taskRunFirstTime(eq(29), eq(0L))).thenReturn(true);
        List<ScrmAmCrowdPackBaseInfoDO> page1 = createTestRecords(2000);
        when(crowdPackReadDomainService.pagePersonaPackInfo(eq(0L), eq(2000))).thenReturn(page1);
        when(crowdPackReadDomainService.pagePersonaPackInfo(eq(1999L), eq(2000))).thenReturn(Collections.emptyList());
        // act
        executeWriteDomainService.updatePersonaCrowdPackMainTask();
        // assert
        verify(refinementOperationExecuteMessageProducer, times(2000)).sendPersonaCrowdPackUpdateTaskExecuteMessage(any(), any(), any(), any());
        verify(executeManagementService).taskRunFinished(eq(29), eq(0L));
    }
}
