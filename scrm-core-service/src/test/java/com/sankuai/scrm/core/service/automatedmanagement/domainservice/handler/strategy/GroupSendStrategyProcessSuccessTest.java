package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.message.push.dto.MsgTaskDetailResultDTO;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushDetailStatus;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.List;

import static org.mockito.Mockito.*;

class GroupSendStrategyProcessSuccessTest {

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @InjectMocks
    private GroupSendStrategy groupSendStrategy;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS;

    private ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO;

    private List<MsgTaskDetailResultDTO> taskDetailResultDTOS;

    private String msgId;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        wxInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setId(1L);
        detailDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_CREATE.getValue().byteValue());
        wxInvokeDetailDOS.add(detailDO);
        wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        taskDetailResultDTOS = new ArrayList<>();
        msgId = "msgId";
    }

    /**
     * 测试正常场景：taskDetailResultDTOS 中没有失败的任务
     */
    @Test
    void testProcessSuccessWithNoFailedTaskDetailResult() throws Throwable {
        // arrange
        MsgTaskDetailResultDTO successTask = new MsgTaskDetailResultDTO();
        successTask.setStatus(MsgPushDetailStatus.SUCCESS.getCode());
        successTask.setReceiverId("successReceiverId");
        taskDetailResultDTOS.add(successTask);
        // act
        groupSendStrategy.processSuccess(processOrchestrationDTO, wxInvokeDetailDOS, wxInvokeLogDO, taskDetailResultDTOS, msgId);
        // assert
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * 测试正常场景：taskDetailResultDTOS 中有失败的任务
     */
    @Test
    void testProcessSuccessWithFailedTaskDetailResult() throws Throwable {
        // arrange
        MsgTaskDetailResultDTO failedTask = new MsgTaskDetailResultDTO();
        failedTask.setStatus(MsgPushDetailStatus.FAILED.getCode());
        failedTask.setReceiverId("failedReceiverId");
        taskDetailResultDTOS.add(failedTask);
        // act
        groupSendStrategy.processSuccess(processOrchestrationDTO, wxInvokeDetailDOS, wxInvokeLogDO, taskDetailResultDTOS, msgId);
        // assert
        verify(wxInvokeDetailDOMapper, times(2)).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(), any());
    }

    /**
     * 测试边界场景：taskDetailResultDTOS 为空
     */
    @Test
    void testProcessSuccessWithEmptyTaskDetailResult() throws Throwable {
        // arrange
        taskDetailResultDTOS.clear();
        // act
        groupSendStrategy.processSuccess(processOrchestrationDTO, wxInvokeDetailDOS, wxInvokeLogDO, taskDetailResultDTOS, msgId);
        // assert
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }
}
