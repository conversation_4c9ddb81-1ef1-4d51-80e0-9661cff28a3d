package com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.frog.sdk.util.CollectionUtils;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.request.QueryCrowdPackDetailInfoByPackIdRequest;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackDetailInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackDetailInfoDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackDetailInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackDetailInfoDOMapper;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CrowdPackPageQueryPackDetailDomainServiceImplTest {

    @Mock
    private ScrmAmCrowdPackDetailInfoDOMapper scrmAmCrowdPackDetailInfoDOMapper;

    @InjectMocks
    private CrowdPackPageQueryPackDetailDomainServiceImpl service;

    @Mock
    private ExtScrmAmCrowdPackDetailInfoDOMapper extScrmAmCrowdPackDetailInfoDOMapper;

    private ScrmAmCrowdPackDetailInfoDO createMockDO(Long id, Long packId, Long externalUserId, String externalUserWxUnionId, String appId, String packVersion, String wxexternaluserid) {
        ScrmAmCrowdPackDetailInfoDO mockDO = mock(ScrmAmCrowdPackDetailInfoDO.class);
        when(mockDO.getId()).thenReturn(id);
        when(mockDO.getPackId()).thenReturn(packId);
        when(mockDO.getExternalUserId()).thenReturn(externalUserId);
        when(mockDO.getExternalUserWxUnionId()).thenReturn(externalUserWxUnionId);
        when(mockDO.getAppId()).thenReturn(appId);
        when(mockDO.getPackVersion()).thenReturn(packVersion);
        when(mockDO.getWxexternaluserid()).thenReturn(wxexternaluserid);
        return mockDO;
    }

    /**
     * 测试正常情况 - 有匹配的记录
     */
    @Test
    public void testCountPageCrowdPackDetailInfoByPackIdList_WithMatchingRecords() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(1L);
        ScrmAmCrowdPackDetailInfoDO record1 = new ScrmAmCrowdPackDetailInfoDO();
        record1.setPackId(1L);
        ScrmAmCrowdPackDetailInfoDO record2 = new ScrmAmCrowdPackDetailInfoDO();
        record2.setPackId(1L);
        when(scrmAmCrowdPackDetailInfoDOMapper.selectByExample(any(ScrmAmCrowdPackDetailInfoDOExample.class))).thenReturn(Arrays.asList(record1, record2));
        // act
        Long result = service.countPageCrowdPackDetailInfoByPackIdList(request);
        // assert
        assertEquals(2L, result);
    }

    /**
     * 测试正常情况 - 没有匹配的记录
     */
    @Test
    public void testCountPageCrowdPackDetailInfoByPackIdList_WithNoMatchingRecords() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(1L);
        when(scrmAmCrowdPackDetailInfoDOMapper.selectByExample(any(ScrmAmCrowdPackDetailInfoDOExample.class))).thenReturn(Collections.emptyList());
        // act
        Long result = service.countPageCrowdPackDetailInfoByPackIdList(request);
        // assert
        assertEquals(0L, result);
    }

    /**
     * 测试边界情况 - request 为 null
     */
    @Test
    public void testCountPageCrowdPackDetailInfoByPackIdList_WithNullRequest() throws Throwable {
        // arrange
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            service.countPageCrowdPackDetailInfoByPackIdList(null);
        });
    }

    /**
     * 测试边界情况 - packId 为 null
     * 根据实际代码，这会抛出 RuntimeException
     */
    @Test
    public void testCountPageCrowdPackDetailInfoByPackIdList_WithNullPackId() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(null);
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            service.countPageCrowdPackDetailInfoByPackIdList(request);
        });
    }

    /**
     * 测试异常情况 - 数据库查询异常
     */
    @Test
    public void testCountPageCrowdPackDetailInfoByPackIdList_WithDatabaseException() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(1L);
        when(scrmAmCrowdPackDetailInfoDOMapper.selectByExample(any(ScrmAmCrowdPackDetailInfoDOExample.class))).thenThrow(new RuntimeException("Database error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            service.countPageCrowdPackDetailInfoByPackIdList(request);
        });
    }

    /**
     * 测试正常场景：有数据返回
     */
    @Test
    public void testQueryPageCrowdPackDetailInfoByPackIdList_NormalCase() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(1L);
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setLastPageId(0L);
        List<Long> idList = Arrays.asList(1L, 2L, 3L);
        ScrmAmCrowdPackDetailInfoDO detailDO = new ScrmAmCrowdPackDetailInfoDO();
        detailDO.setId(1L);
        detailDO.setPackId(1L);
        detailDO.setExternalUserId(100L);
        detailDO.setExternalUserWxUnionId("union1");
        detailDO.setAppId("app1");
        detailDO.setPackVersion("v1");
        detailDO.setWxexternaluserid("wx1");
        when(extScrmAmCrowdPackDetailInfoDOMapper.getPackDetailInfoByPackId(any())).thenReturn(idList);
        when(scrmAmCrowdPackDetailInfoDOMapper.selectByExample(any())).thenReturn(Arrays.asList(detailDO));
        // act
        List<ScrmCrowdPackDetailInfoDTO> result = service.queryPageCrowdPackDetailInfoByPackIdList(request);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getId());
        assertEquals(1L, result.get(0).getPackId());
        assertEquals("union1", result.get(0).getExternalUserWxUnionId());
    }

    /**
     * 测试空数据场景：extMapper返回空列表
     */
    @Test
    public void testQueryPageCrowdPackDetailInfoByPackIdList_EmptyData() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(1L);
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setLastPageId(0L);
        when(extScrmAmCrowdPackDetailInfoDOMapper.getPackDetailInfoByPackId(any())).thenReturn(Collections.emptyList());
        // act
        List<ScrmCrowdPackDetailInfoDTO> result = service.queryPageCrowdPackDetailInfoByPackIdList(request);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试边界场景：第一页(lastPageId=0)
     */
    @Test
    public void testQueryPageCrowdPackDetailInfoByPackIdList_FirstPage() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(1L);
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setLastPageId(0L);
        List<Long> idList = Arrays.asList(1L, 2L, 3L);
        ScrmAmCrowdPackDetailInfoDO detailDO = new ScrmAmCrowdPackDetailInfoDO();
        detailDO.setId(1L);
        detailDO.setPackId(1L);
        detailDO.setExternalUserId(100L);
        detailDO.setExternalUserWxUnionId("union1");
        detailDO.setAppId("app1");
        detailDO.setPackVersion("v1");
        detailDO.setWxexternaluserid("wx1");
        when(extScrmAmCrowdPackDetailInfoDOMapper.getPackDetailInfoByPackId(any())).thenReturn(idList);
        when(scrmAmCrowdPackDetailInfoDOMapper.selectByExample(any())).thenReturn(Arrays.asList(detailDO));
        // act
        List<ScrmCrowdPackDetailInfoDTO> result = service.queryPageCrowdPackDetailInfoByPackIdList(request);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    /**
     * 测试边界场景：最后一页
     */
    @Test
    public void testQueryPageCrowdPackDetailInfoByPackIdList_LastPage() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(1L);
        request.setPageNumber(2);
        request.setPageSize(10);
        request.setLastPageId(100L);
        List<Long> idList = Arrays.asList(101L, 102L);
        ScrmAmCrowdPackDetailInfoDO detailDO = new ScrmAmCrowdPackDetailInfoDO();
        detailDO.setId(101L);
        detailDO.setPackId(1L);
        detailDO.setExternalUserId(100L);
        detailDO.setExternalUserWxUnionId("union1");
        detailDO.setAppId("app1");
        detailDO.setPackVersion("v1");
        detailDO.setWxexternaluserid("wx1");
        when(extScrmAmCrowdPackDetailInfoDOMapper.getPackDetailInfoByPackId(any())).thenReturn(idList);
        when(scrmAmCrowdPackDetailInfoDOMapper.selectByExample(any())).thenReturn(Arrays.asList(detailDO));
        // act
        List<ScrmCrowdPackDetailInfoDTO> result = service.queryPageCrowdPackDetailInfoByPackIdList(request);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(101L, result.get(0).getId());
    }

    /**
     * 测试异常场景：request为null
     */
    @Test
    public void testQueryPageCrowdPackDetailInfoByPackIdList_NullRequest() throws Throwable {
        // arrange
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            service.queryPageCrowdPackDetailInfoByPackIdList(null);
        });
    }

    /**
     * 测试异常场景：packId为null
     */
    @Test
    public void testQueryPageCrowdPackDetailInfoByPackIdList_NullPackId() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(null);
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setLastPageId(0L);
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            service.queryPageCrowdPackDetailInfoByPackIdList(request);
        });
    }

    /**
     * 测试边界场景：pageNumber为1
     */
    @Test
    public void testQueryPageCrowdPackDetailInfoByPackIdList_MinPageNumber() throws Throwable {
        // arrange
        QueryCrowdPackDetailInfoByPackIdRequest request = new QueryCrowdPackDetailInfoByPackIdRequest();
        request.setPackId(1L);
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setLastPageId(0L);
        List<Long> idList = Arrays.asList(1L, 2L, 3L);
        ScrmAmCrowdPackDetailInfoDO detailDO = new ScrmAmCrowdPackDetailInfoDO();
        detailDO.setId(1L);
        detailDO.setPackId(1L);
        detailDO.setExternalUserId(100L);
        detailDO.setExternalUserWxUnionId("union1");
        detailDO.setAppId("app1");
        detailDO.setPackVersion("v1");
        detailDO.setWxexternaluserid("wx1");
        when(extScrmAmCrowdPackDetailInfoDOMapper.getPackDetailInfoByPackId(any())).thenReturn(idList);
        when(scrmAmCrowdPackDetailInfoDOMapper.selectByExample(any())).thenReturn(Arrays.asList(detailDO));
        // act
        List<ScrmCrowdPackDetailInfoDTO> result = service.queryPageCrowdPackDetailInfoByPackIdList(request);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
    }
}
