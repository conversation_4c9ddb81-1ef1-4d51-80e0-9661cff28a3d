package com.sankuai.scrm.core.service.dashboard.crane;

import com.sankuai.scrm.core.service.dashboard.crane.ActiveDataTask;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.mockito.Mockito.*;

public class ActiveDataTaskGetDataAndPushToEsTest {

    @InjectMocks
    private ActiveDataTask activeDataTask;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试getDataAndPushToEs方法，正常情况
     */
    @Test
    public void testGetDataAndPushToEsNormal() throws Throwable {
        // arrange
        when(corpAppConfigRepository.getAllConfigs()).thenReturn(null);
        // act
        activeDataTask.getDataAndPushToEs();
        // assert
        verify(corpAppConfigRepository, times(1)).getAllConfigs();
    }

    /**
     * 测试getDataAndPushToEs方法，异常情况
     */
    @Test
    public void testGetDataAndPushToEsException() throws Throwable {
        // arrange
        when(corpAppConfigRepository.getAllConfigs()).thenThrow(new RuntimeException());
        // act
        activeDataTask.getDataAndPushToEs();
        // assert
        verify(corpAppConfigRepository, times(1)).getAllConfigs();
    }
}
