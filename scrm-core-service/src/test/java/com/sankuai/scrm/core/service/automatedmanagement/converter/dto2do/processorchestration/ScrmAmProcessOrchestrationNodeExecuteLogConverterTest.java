package com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmAmProcessOrchestrationNodeExecuteLogDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationNodeExecuteLogDO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.junit.Assert.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNull;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/10/30
 */
@RunWith(MockitoJUnitRunner.class)
public class ScrmAmProcessOrchestrationNodeExecuteLogConverterTest {

    @InjectMocks
    private ScrmAmProcessOrchestrationNodeExecuteLogConverter converter;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 convertToDTO 方法，当输入为 null 时
     */
    @Test
    public void testConvertToDTONullInput() {
        // arrange
        ScrmAmProcessOrchestrationNodeExecuteLogDO input = null;

        // act
        ScrmAmProcessOrchestrationNodeExecuteLogDTO result = converter.convertToDTO(input);

        // assert
        assertNull(result);
    }

    /**
     * 测试 convertToDTO 方法，正常情况
     */
    @Test
    public void testConvertToDTONormal() {
        // arrange
        ScrmAmProcessOrchestrationNodeExecuteLogDO input = new ScrmAmProcessOrchestrationNodeExecuteLogDO(1L, 2L, "v1",
                3L, 4L, new Date());
        ScrmAmProcessOrchestrationNodeExecuteLogDTO expected = new ScrmAmProcessOrchestrationNodeExecuteLogDTO();
        expected.setProcessOrchestrationId(2L);
        expected.setProcessOrchestrationVersion("v1");
        expected.setProcessOrchestrationNodeId(3L);
        expected.setExecuteCount(4L);

        // act
        ScrmAmProcessOrchestrationNodeExecuteLogDTO result = converter.convertToDTO(input);

        // assert
        assertEquals(expected.getProcessOrchestrationId(), result.getProcessOrchestrationId());
        assertEquals(expected.getProcessOrchestrationVersion(), result.getProcessOrchestrationVersion());
        assertEquals(expected.getProcessOrchestrationNodeId(), result.getProcessOrchestrationNodeId());
        assertEquals(expected.getExecuteCount(), result.getExecuteCount());
    }

    /**
     * 测试 convertToDTO 方法，当输入对象的 processOrchestrationId 为 null 时
     */
    @Test
    public void testConvertToDTOWithNullProcessOrchestrationId() {
        // arrange
        ScrmAmProcessOrchestrationNodeExecuteLogDO input = new ScrmAmProcessOrchestrationNodeExecuteLogDO();
        input.setProcessOrchestrationVersion("v1");
        input.setProcessOrchestrationNodeId(3L);
        input.setExecuteCount(4L);

        // act
        ScrmAmProcessOrchestrationNodeExecuteLogDTO result = converter.convertToDTO(input);

        // assert
        assertNull(result.getProcessOrchestrationId());
        assertEquals("v1", result.getProcessOrchestrationVersion());
        assertEquals(Long.valueOf(3), result.getProcessOrchestrationNodeId());
        assertEquals(Long.valueOf(4), result.getExecuteCount());
    }

    /**
     * 测试 convertToDTO 方法，当输入对象的 processOrchestrationVersion 为 null 时
     */
    @Test
    public void testConvertToDTOWithNullProcessOrchestrationVersion() {
        // arrange
        ScrmAmProcessOrchestrationNodeExecuteLogDO input = new ScrmAmProcessOrchestrationNodeExecuteLogDO();
        input.setProcessOrchestrationId(2L);
        input.setProcessOrchestrationNodeId(3L);
        input.setExecuteCount(4L);

        // act
        ScrmAmProcessOrchestrationNodeExecuteLogDTO result = converter.convertToDTO(input);

        // assert
        assertEquals(Long.valueOf(2), result.getProcessOrchestrationId());
        assertNull(result.getProcessOrchestrationVersion());
        assertEquals(Long.valueOf(3), result.getProcessOrchestrationNodeId());
        assertEquals(Long.valueOf(4), result.getExecuteCount());
    }

    /**
     * 测试 convertToDTO 方法，当输入对象的 processOrchestrationNodeId 为 null 时
     */
    @Test
    public void testConvertToDTOWithNullProcessOrchestrationNodeId() {
        // arrange
        ScrmAmProcessOrchestrationNodeExecuteLogDO input = new ScrmAmProcessOrchestrationNodeExecuteLogDO();
        input.setProcessOrchestrationId(2L);
        input.setProcessOrchestrationVersion("v1");
        input.setExecuteCount(4L);

        // act
        ScrmAmProcessOrchestrationNodeExecuteLogDTO result = converter.convertToDTO(input);

        // assert
        assertEquals(Long.valueOf(2), result.getProcessOrchestrationId());
        assertEquals("v1", result.getProcessOrchestrationVersion());
        assertNull(result.getProcessOrchestrationNodeId());
        assertEquals(Long.valueOf(4), result.getExecuteCount());
    }

    /**
     * 测试 convertToDTO 方法，当输入对象的 executeCount 为 null 时
     */
    @Test
    public void testConvertToDTOWithNullExecuteCount() {
        // arrange
        ScrmAmProcessOrchestrationNodeExecuteLogDO input = new ScrmAmProcessOrchestrationNodeExecuteLogDO();
        input.setProcessOrchestrationId(2L);
        input.setProcessOrchestrationVersion("v1");
        input.setProcessOrchestrationNodeId(3L);

        // act
        ScrmAmProcessOrchestrationNodeExecuteLogDTO result = converter.convertToDTO(input);

        // assert
        assertEquals(Long.valueOf(2), result.getProcessOrchestrationId());
        assertEquals("v1", result.getProcessOrchestrationVersion());
        assertEquals(Long.valueOf(3), result.getProcessOrchestrationNodeId());
        assertNull(result.getExecuteCount());
    }
}