package com.sankuai.scrm.core.service.automatedmanagement.converter.dto2vo.corwdpack;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationInfoVO;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmProcessOrchestrationInfoVOConverterTest {

    private ScrmProcessOrchestrationInfoVOConverter converter = new ScrmProcessOrchestrationInfoVOConverter();

    private static final SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @Test
    public void testConvertToDTOWhenResourceIsNull() throws Throwable {
        ScrmProcessOrchestrationInfoVO resource = null;
        ScrmProcessOrchestrationDTO result = converter.convertToDTO(resource);
        assertNull(result);
    }

    @Test
    public void testConvertToDTOWhenAllPropertiesOfResourceAreNull() throws Throwable {
        ScrmProcessOrchestrationInfoVO resource = new ScrmProcessOrchestrationInfoVO();
        resource.setProcessOrchestrationType(0);
        resource.setCron("");
        resource.setCronComment("");
        resource.setParticipationRestrictionsCycle(0);
        resource.setParticipationRestrictionsTimes(0);
        ScrmProcessOrchestrationDTO result = converter.convertToDTO(resource);
        assertNotNull(result);
        assertEquals(Byte.valueOf((byte) 0), result.getParticipationRestrictionsCycle());
        assertEquals(Byte.valueOf((byte) 0), result.getParticipationRestrictionsTimes());
    }

    @Test
    public void testConvertToDTOWhenBeginTimeAndEndTimeOfResourceAreNotNull() throws Throwable {
        ScrmProcessOrchestrationInfoVO resource = new ScrmProcessOrchestrationInfoVO();
        resource.setBeginTime("2022-01-01 00:00:00");
        resource.setEndTime("2022-12-31 23:59:59");
        resource.setProcessOrchestrationType(0);
        resource.setCron("");
        resource.setCronComment("");
        resource.setParticipationRestrictionsCycle(0);
        resource.setParticipationRestrictionsTimes(0);
        ScrmProcessOrchestrationDTO result = converter.convertToDTO(resource);
        assertNotNull(result);
        assertEquals(dateFormat.parse("2022-01-01 00:00:00"), result.getBeginTime());
        assertEquals(dateFormat.parse("2022-12-31 23:59:59"), result.getEndTime());
    }

    @Test
    public void testConvertToDTOWhenProcessOrchestrationTypeOfResourceIsTimedProcessOrchestration() throws Throwable {
        ScrmProcessOrchestrationInfoVO resource = new ScrmProcessOrchestrationInfoVO();
        resource.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue());
        resource.setCronComment("2022-01-01 00:00:00");
        resource.setCron("");
        resource.setParticipationRestrictionsCycle(0);
        resource.setParticipationRestrictionsTimes(0);
        ScrmProcessOrchestrationDTO result = converter.convertToDTO(resource);
        assertNotNull(result);
        assertEquals(dateFormat.parse("2021-12-31 00:00:00"), result.getBeginTime());
        assertEquals(dateFormat.parse("2022-01-02 00:00:00"), result.getEndTime());
    }

    @Test
    public void testConvertToDTOWhenProcessOrchestrationTypeOfResourceIsNotTimedProcessOrchestration() throws Throwable {
        ScrmProcessOrchestrationInfoVO resource = new ScrmProcessOrchestrationInfoVO();
        resource.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.UNKNOWN.getValue());
        resource.setCron("");
        resource.setCronComment("");
        resource.setParticipationRestrictionsCycle(0);
        resource.setParticipationRestrictionsTimes(0);
        ScrmProcessOrchestrationDTO result = converter.convertToDTO(resource);
        assertNotNull(result);
        assertEquals(dateFormat.parse("1700-12-31 23:59:59"), result.getBeginTime());
        assertEquals(dateFormat.parse("9999-12-31 23:59:59"), result.getEndTime());
    }

    @Test
    public void testConvertToDTOWhenParticipationRestrictOfResourceIsTrue() throws Throwable {
        ScrmProcessOrchestrationInfoVO resource = new ScrmProcessOrchestrationInfoVO();
        resource.setParticipationRestrict(true);
        resource.setProcessOrchestrationType(0);
        resource.setCron("");
        resource.setCronComment("");
        resource.setParticipationRestrictionsCycle(0);
        resource.setParticipationRestrictionsTimes(0);
        ScrmProcessOrchestrationDTO result = converter.convertToDTO(resource);
        assertNotNull(result);
        assertTrue(result.isParticipationRestrict());
        assertNotNull(result.getParticipationRestrictionsCycle());
        assertNotNull(result.getParticipationRestrictionsTimes());
        assertEquals(Byte.valueOf((byte) 0), result.getParticipationRestrictionsCycle());
        assertEquals(Byte.valueOf((byte) 0), result.getParticipationRestrictionsTimes());
    }

    @Test
    public void testConvertToDTOWhenParticipationRestrictOfResourceIsFalse() throws Throwable {
        ScrmProcessOrchestrationInfoVO resource = new ScrmProcessOrchestrationInfoVO();
        resource.setParticipationRestrict(false);
        resource.setParticipationRestrictionsCycle(1);
        resource.setParticipationRestrictionsTimes(2);
        resource.setProcessOrchestrationType(0);
        resource.setCron("");
        resource.setCronComment("");
        ScrmProcessOrchestrationDTO result = converter.convertToDTO(resource);
        assertNotNull(result);
        assertFalse(result.isParticipationRestrict());
        assertEquals(Byte.valueOf((byte) 1), result.getParticipationRestrictionsCycle());
        assertEquals(Byte.valueOf((byte) 2), result.getParticipationRestrictionsTimes());
    }

    @Test
    public void testConvertToDTOWithAllFieldsPopulated() throws Throwable {
        ScrmProcessOrchestrationInfoVO resource = new ScrmProcessOrchestrationInfoVO();
        resource.setId(1L);
        resource.setName("Test Orchestration");
        resource.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue());
        resource.setCron("0 0 12 * * ?");
        resource.setCronComment("Every day at noon");
        resource.setBeginTime("2023-01-01 00:00:00");
        resource.setEndTime("2023-12-31 23:59:59");
        resource.setStatus(1);
        resource.setValidVersion("1.0");
        resource.setParticipationRestrict(true);
        resource.setParticipationRestrictionsCycle(7);
        resource.setParticipationRestrictionsTimes(3);
        resource.setCreatorId("creator123");
        resource.setLastUpdaterId("updater456");
        resource.setPreviewPic("preview.jpg");
        resource.setExecutorType(1);
        ScrmProcessOrchestrationDTO result = converter.convertToDTO(resource);
        assertNotNull(result);
        assertEquals(Long.valueOf(1L), result.getId());
        assertEquals("Test Orchestration", result.getName());
        assertEquals(Byte.valueOf((byte) ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue().intValue()), result.getProcessOrchestrationType());
        assertEquals("0 0 12 * * ?", result.getCron());
        assertEquals("Every day at noon", result.getCronComment());
        assertEquals(dateFormat.parse("2023-01-01 00:00:00"), result.getBeginTime());
        assertEquals(dateFormat.parse("2023-12-31 23:59:59"), result.getEndTime());
        assertEquals(Byte.valueOf((byte) 1), result.getStatus());
        assertEquals("1.0", result.getValidVersion());
        assertTrue(result.isParticipationRestrict());
        assertEquals(Byte.valueOf((byte) 0), result.getParticipationRestrictionsCycle());
        assertEquals(Byte.valueOf((byte) 0), result.getParticipationRestrictionsTimes());
        assertEquals("creator123", result.getCreatorId());
        assertEquals("updater456", result.getLastUpdaterId());
        assertEquals("preview.jpg", result.getPreviewPic());
        assertEquals(Integer.valueOf(1), result.getExecutorType());
    }

    @Test
    public void testConvertToDONull() throws Throwable {
        ScrmProcessOrchestrationDTO dto = null;
        ScrmProcessOrchestrationInfoVO vo = converter.convertToDO(dto);
        assertNull(vo);
    }

    @Test
    public void testConvertToDOPartialPropertiesNull() throws Throwable {
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        // Set properties to non-null values
        dto.setId(1L);
        dto.setName("test");
        dto.setProcessOrchestrationType((byte) 1);
        dto.setCron("test");
        dto.setCronComment("test");
        dto.setBeginTime(new java.util.Date());
        dto.setEndTime(new java.util.Date());
        dto.setStatus((byte) 1);
        dto.setValidVersion("test");
        dto.setParticipationRestrict(true);
        dto.setParticipationRestrictionsCycle((byte) 1);
        dto.setParticipationRestrictionsTimes((byte) 1);
        dto.setCreatorId("test");
        dto.setLastUpdaterId("test");
        dto.setPreviewPic("test");
        dto.setExecutorType(1);
        dto.setExecutorList(new ArrayList<>());
        ScrmProcessOrchestrationInfoVO vo = converter.convertToDO(dto);
        assertNotNull(vo);
        assertEquals(dto.getId(), vo.getId());
        assertEquals(dto.getName(), vo.getName());
        assertEquals(Integer.valueOf(dto.getProcessOrchestrationType().intValue()), vo.getProcessOrchestrationType());
        assertEquals(dto.getCron(), vo.getCron());
        assertEquals(dto.getCronComment(), vo.getCronComment());
        assertEquals(sdf.format(dto.getBeginTime()), vo.getBeginTime());
        assertEquals(sdf.format(dto.getEndTime()), vo.getEndTime());
        assertEquals(Integer.valueOf(dto.getStatus().intValue()), vo.getStatus());
        assertEquals(dto.getValidVersion(), vo.getValidVersion());
        assertTrue(vo.isParticipationRestrict());
        assertEquals(Integer.valueOf(dto.getParticipationRestrictionsCycle().intValue()), vo.getParticipationRestrictionsCycle());
        assertEquals(Integer.valueOf(dto.getParticipationRestrictionsTimes().intValue()), vo.getParticipationRestrictionsTimes());
        assertEquals(dto.getCreatorId(), vo.getCreatorId());
        assertEquals(dto.getLastUpdaterId(), vo.getLastUpdaterId());
        assertEquals(dto.getPreviewPic(), vo.getPreviewPic());
        assertEquals(Integer.valueOf(dto.getExecutorType().intValue()), vo.getExecutorType());
        assertEquals(dto.getExecutorList(), vo.getExecutorList());
    }

    @Test
    public void testConvertToDOAllPropertiesNotNull() throws Throwable {
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        // Set properties to non-null values
        dto.setId(1L);
        dto.setName("test");
        dto.setProcessOrchestrationType((byte) 1);
        dto.setCron("test");
        dto.setCronComment("test");
        dto.setBeginTime(new java.util.Date());
        dto.setEndTime(new java.util.Date());
        dto.setStatus((byte) 1);
        dto.setValidVersion("test");
        dto.setParticipationRestrict(true);
        dto.setParticipationRestrictionsCycle((byte) 1);
        dto.setParticipationRestrictionsTimes((byte) 1);
        dto.setCreatorId("test");
        dto.setLastUpdaterId("test");
        dto.setPreviewPic("test");
        dto.setExecutorType(1);
        dto.setExecutorList(new ArrayList<>());
        ScrmProcessOrchestrationInfoVO vo = converter.convertToDO(dto);
        assertNotNull(vo);
        assertEquals(dto.getId(), vo.getId());
        assertEquals(dto.getName(), vo.getName());
        assertEquals(Integer.valueOf(dto.getProcessOrchestrationType().intValue()), vo.getProcessOrchestrationType());
        assertEquals(dto.getCron(), vo.getCron());
        assertEquals(dto.getCronComment(), vo.getCronComment());
        assertEquals(sdf.format(dto.getBeginTime()), vo.getBeginTime());
        assertEquals(sdf.format(dto.getEndTime()), vo.getEndTime());
        assertEquals(Integer.valueOf(dto.getStatus().intValue()), vo.getStatus());
        assertEquals(dto.getValidVersion(), vo.getValidVersion());
        assertTrue(vo.isParticipationRestrict());
        assertEquals(Integer.valueOf(dto.getParticipationRestrictionsCycle().intValue()), vo.getParticipationRestrictionsCycle());
        assertEquals(Integer.valueOf(dto.getParticipationRestrictionsTimes().intValue()), vo.getParticipationRestrictionsTimes());
        assertEquals(dto.getCreatorId(), vo.getCreatorId());
        assertEquals(dto.getLastUpdaterId(), vo.getLastUpdaterId());
        assertEquals(dto.getPreviewPic(), vo.getPreviewPic());
        assertEquals(Integer.valueOf(dto.getExecutorType().intValue()), vo.getExecutorType());
        assertEquals(dto.getExecutorList(), vo.getExecutorList());
    }
}
