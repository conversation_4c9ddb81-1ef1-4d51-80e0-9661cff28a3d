package com.sankuai.scrm.core.service.automatedmanagement.mq.consumer;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductItemsDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmRefreshRateLimitDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductInfoService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

/**
 * Tests for {@link ManualRefreshProductInfoConsumer#recvMessage(MafkaMessage, MessagetContext)}
 */
@RunWith(MockitoJUnitRunner.class)
public class ManualRefreshProductInfoConsumerTest {

    @Mock(lenient = true)
    private ProductManagementService productManagementService;

    @Mock(lenient = true)
    private ExtScrmAmRefreshRateLimitDOMapper extRateLimitDOMapper;

    @Mock
    private ProductInfoService productInfoService;

    @InjectMocks
    private ManualRefreshProductInfoConsumer consumer;

    @Test
    public void testRecvMessageWithBlankAppId() throws Throwable {
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, null, "");
        MessagetContext context = new MessagetContext();
        ConsumeStatus result = consumer.recvMessage(message, context);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void testRecvMessageWithNonBlankAppIdAndRateLimit() throws Throwable {
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, null, "appId");
        MessagetContext context = new MessagetContext();
        when(extRateLimitDOMapper.countByExample(any())).thenReturn(1L);
        ConsumeStatus result = consumer.recvMessage(message, context);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(extRateLimitDOMapper).countByExample(any());
    }

    @Test
    public void testRecvMessageWithNonBlankAppIdNoRateLimitEmptyProducts() throws Throwable {
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, null, "appId");
        MessagetContext context = new MessagetContext();
        when(extRateLimitDOMapper.countByExample(any())).thenReturn(0L);
        when(productManagementService.pageProductsByAppId(anyString(), any(), any())).thenReturn(new ArrayList<>());
        ConsumeStatus result = consumer.recvMessage(message, context);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(productManagementService).pageProductsByAppId("appId", 0, 100);
    }

    @Test
    public void testRecvMessageWithNonBlankAppIdNoRateLimitNonEmptyProductsEmptyProductInfo() throws Throwable {
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, null, "appId");
        MessagetContext context = new MessagetContext();
        List<ScrmAmProcessOrchestrationProductItemsDO> products = new ArrayList<>();
        products.add(new ScrmAmProcessOrchestrationProductItemsDO());
        when(extRateLimitDOMapper.countByExample(any())).thenReturn(0L);
        when(productManagementService.pageProductsByAppId(anyString(), any(), any())).thenReturn(products);
        ConsumeStatus result = consumer.recvMessage(message, context);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        // Removed the verification for updateCommonProductPoolProducts as it's not expected to be called based on the test setup.
    }

    @Test
    public void testRecvMessageWithNonBlankAppIdNoRateLimitNonEmptyProductsNonEmptyProductInfo() throws Throwable {
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0, null, "appId");
        MessagetContext context = new MessagetContext();
        List<ScrmAmProcessOrchestrationProductItemsDO> products = new ArrayList<>();
        products.add(new ScrmAmProcessOrchestrationProductItemsDO(1L, 1L, 1L, 1, "appId", null));
        when(extRateLimitDOMapper.countByExample(any())).thenReturn(0L);
        when(productManagementService.pageProductsByAppId(anyString(), any(), any())).thenReturn(products);
        ConsumeStatus result = consumer.recvMessage(message, context);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        // Removed the verification for updateCommonProductPoolProducts as it's not expected to be called based on the test setup.
    }
}
