package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.sankuai.dz.srcm.automatedmanagement.request.ProcessOrchestrationInformationGatheringRequest;
import com.sankuai.scrm.core.service.aigc.service.SupplyMarketingTextService;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class InformationGatheringService_LogUserStatusEnumChangeDetailTest {

    @InjectMocks
    private InformationGatheringService informationGatheringService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationDistributorCodeDOMapper distributorCodeDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper invokeDetailDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteGoalDetailDOMapper executeGoalDetailDOMapper;

    private ProcessOrchestrationInformationGatheringRequest request;

    private ScrmAmProcessOrchestrationDistributorCodeDO distributorCodeDO;

    private ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;

    private ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO;

    private ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO;

    private ScrmAmProcessOrchestrationExecuteGoalDetailDO executeGoalDetailDO;

    @Mock(lenient = true)
    private SupplyMarketingTextService supplyMarketingTextService;

    @Before
    public void setUp() {
        request = new ProcessOrchestrationInformationGatheringRequest();
        request.setCode("distributorCode-clue");
        request.setMtUserId(123L);
        request.setInformationGatheringType(1L);
        distributorCodeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        distributorCodeDO.setDistributorCode("distributorCode");
        distributorCodeDO.setProcessOrchestrationId(1L);
        distributorCodeDO.setProcessOrchestrationNodeId(1L);
        distributorCodeDO.setProcessOrchestrationVersion("1");
        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        executeLogDO.setProcessOrchestrationId(1L);
        executeLogDO.setTargetMtUserId(123L);
        executeLogDO.setRemark("1");
        invokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO.setProcessOrchestrationId(1L);
        invokeDetailDO.setProcessOrchestrationNodeId(1L);
        invokeDetailDO.setExecuteLogId(1L);
        wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        wxInvokeLogDO.setProcessOrchestrationNodeId(1L);
        wxInvokeLogDO.setProcessOrchestrationId(1L);
        wxInvokeLogDO.setProcessOrchestrationVersion("1");
        executeGoalDetailDO = new ScrmAmProcessOrchestrationExecuteGoalDetailDO();
        executeGoalDetailDO.setProcessOrchestrationId(1L);
        executeGoalDetailDO.setProcessOrchestrationNodeId(1L);
        executeGoalDetailDO.setExecuteLogId(1L);
        executeGoalDetailDO.setHitHighLightTagId(1L);
    }

    @Test
    public void testLogUserStatusChangeDetailNormal() throws Throwable {
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(distributorCodeDO));
        when(executeLogDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(executeLogDO));
        when(invokeDetailDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(invokeDetailDO));
        when(executeGoalDetailDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // Ensure insert returns 1
        when(executeGoalDetailDOMapper.insert(any())).thenReturn(1);
        doNothing().when(supplyMarketingTextService).recycleUserTag(any(), any());
        boolean result = informationGatheringService.logUserStatusChangeDetail(request);
        assertTrue(result);
    }

    @Test
    public void testLogUserStatusChangeDetailException1() throws Throwable {
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        boolean result = informationGatheringService.logUserStatusChangeDetail(request);
        assertFalse(result);
    }

    @Test
    public void testLogUserStatusChangeDetailException2() throws Throwable {
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(distributorCodeDO));
        when(executeLogDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        boolean result = informationGatheringService.logUserStatusChangeDetail(request);
        assertFalse(result);
    }

    @Test
    public void testLogUserStatusChangeDetailException3() throws Throwable {
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(distributorCodeDO));
        when(executeLogDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(executeLogDO));
        when(invokeDetailDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(invokeDetailDO));
        when(executeGoalDetailDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(executeGoalDetailDO));
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(wxInvokeLogDO));
        boolean result = informationGatheringService.logUserStatusChangeDetail(request);
        assertFalse(result);
    }
}
