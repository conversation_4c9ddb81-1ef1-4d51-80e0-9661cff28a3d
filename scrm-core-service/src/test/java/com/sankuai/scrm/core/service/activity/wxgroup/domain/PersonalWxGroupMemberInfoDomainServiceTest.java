package com.sankuai.scrm.core.service.activity.wxgroup.domain;

import com.sankuai.dz.srcm.activity.fission.dto.task.FissionMatchTaskDTO;
import com.sankuai.scrm.core.service.activity.fission.mq.producer.FissionMatchTaskProducer;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmActivityAndDSPersonalWxGroupRelationMapDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmDSPersonalWxGroupMemberInfoDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmDSPersonalWxGroupMemberMatchRecordDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.example.ScrmActivityAndDSPersonalWxGroupRelationMapDOExample;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.example.ScrmDSPersonalWxGroupMemberInfoDOExample;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.example.ScrmDSPersonalWxGroupMemberMatchRecordDOExample;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmActivityAndDSPersonalWxGroupRelationMapDOMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmDSPersonalWxGroupMemberInfoDOMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmDSPersonalWxGroupMemberMatchRecordDOMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmPersonalWxUserAuthorisationInfoDOMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.dto.UpdateResultDTO;
import com.sankuai.scrm.core.service.external.user.dal.mapper.ScrmMemberBaseInfoMapper;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.mq.bo.corpwxgroupchange.OpenGroupChangeEvent;
import com.sankuai.scrm.core.service.infrastructure.mq.bo.corpwxgroupchange.OpenGroupMemberSimpleInfo;
import com.sankuai.scrm.core.service.infrastructure.mq.bo.corpwxgroupchange.OpenGroupSimpleInfo;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.util.ImageDupUtil;
import com.sankuai.service.fe.corp.wx.thrift.CorpWxService;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Nested;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Captor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * Unit tests for {@link PersonalWxGroupMemberInfoDomainService}.
 */
@ExtendWith(MockitoExtension.class)
public class PersonalWxGroupMemberInfoDomainServiceTest {

    @InjectMocks
    private PersonalWxGroupMemberInfoDomainService personalWxGroupMemberInfoDomainService;

    @Mock
    private ScrmDSPersonalWxGroupMemberInfoDOMapper personalWxGroupMemberInfoDOMapper;

    @Mock
    private ScrmActivityAndDSPersonalWxGroupRelationMapDOMapper relationMapDOMapper;

    @Mock
    private ScrmDSPersonalWxGroupMemberMatchRecordDOMapper memberMatchRecordDOMapper;

    @Mock
    private ScrmMemberBaseInfoMapper scrmMemberBaseInfoMapper;

    @Mock
    private ScrmPersonalWxUserAuthorisationInfoDOMapper scrmPersonalWxUserAuthorisationInfoDOMapper;

    @Mock
    private FissionMatchTaskProducer fissionMatchTaskProducer;

    @Mock
    private CorpWxService.Iface corpWxService;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private ImageDupUtil imageDupUtil;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;
    
    @Captor
    private ArgumentCaptor<ScrmDSPersonalWxGroupMemberInfoDOExample> memberInfoExampleCaptor;
    
    @Captor
    private ArgumentCaptor<ScrmDSPersonalWxGroupMemberMatchRecordDOExample> matchRecordExampleCaptor;
    
    @Captor
    private ArgumentCaptor<ScrmActivityAndDSPersonalWxGroupRelationMapDOExample> relationMapExampleCaptor;
    
    @Captor
    private ArgumentCaptor<ScrmDSPersonalWxGroupMemberInfoDO> memberInfoCaptor;
    
    @Captor
    private ArgumentCaptor<ScrmDSPersonalWxGroupMemberMatchRecordDO> matchRecordCaptor;
    
    @Captor
    private ArgumentCaptor<FissionMatchTaskDTO> fissionMatchTaskDTOCaptor;

    @Nested
    @DisplayName("queryMemberInfoByWxUserId 方法测试")
    public class QueryMemberInfoByWxUserIdTest {
        
        private List<ScrmActivityAndDSPersonalWxGroupRelationMapDO> createMockRelations(Long activityId) {
            ScrmActivityAndDSPersonalWxGroupRelationMapDO relation1 = new ScrmActivityAndDSPersonalWxGroupRelationMapDO();
            relation1.setActivityId(activityId);
            relation1.setDsGroupId(1001L);
            
            ScrmActivityAndDSPersonalWxGroupRelationMapDO relation2 = new ScrmActivityAndDSPersonalWxGroupRelationMapDO();
            relation2.setActivityId(activityId);
            relation2.setDsGroupId(1002L);
            
            return Arrays.asList(relation1, relation2);
        }
        
        private List<ScrmDSPersonalWxGroupMemberInfoDO> createMockMemberInfos(String wxUserId) {
            ScrmDSPersonalWxGroupMemberInfoDO member1 = new ScrmDSPersonalWxGroupMemberInfoDO();
            member1.setId(1L);
            member1.setWxuserid(wxUserId);
            member1.setDsGroupId(1001L);
            member1.setMemberName("测试用户1");
            
            ScrmDSPersonalWxGroupMemberInfoDO member2 = new ScrmDSPersonalWxGroupMemberInfoDO();
            member2.setId(2L);
            member2.setWxuserid(wxUserId);
            member2.setDsGroupId(1002L);
            member2.setMemberName("测试用户2");
            
            return Arrays.asList(member1, member2);
        }

        @Test
        @DisplayName("当wxUserId为空时，应返回空列表")
        public void shouldReturnEmptyListWhenWxUserIdIsEmpty() {
            // 执行
            List<ScrmDSPersonalWxGroupMemberInfoDO> result = personalWxGroupMemberInfoDomainService.queryMemberInfoByWxUserId("", 1L);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            verify(relationMapDOMapper, never()).selectByExample(any());
            verify(personalWxGroupMemberInfoDOMapper, never()).selectByExample(any());
        }
        
        @Test
        @DisplayName("当activityId为null时，应返回空列表")
        public void shouldReturnEmptyListWhenActivityIdIsNull() {
            // 执行
            List<ScrmDSPersonalWxGroupMemberInfoDO> result = personalWxGroupMemberInfoDomainService.queryMemberInfoByWxUserId("test_wx_user_id", null);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            verify(relationMapDOMapper, never()).selectByExample(any());
            verify(personalWxGroupMemberInfoDOMapper, never()).selectByExample(any());
        }
        
        @Test
        @DisplayName("当找不到活动关联的群时，应返回空列表")
        public void shouldReturnEmptyListWhenNoRelationFound() {
            // 准备
            String wxUserId = "test_wx_user_id";
            Long activityId = 1L;
            
            when(relationMapDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
            
            // 执行
            List<ScrmDSPersonalWxGroupMemberInfoDO> result = personalWxGroupMemberInfoDomainService.queryMemberInfoByWxUserId(wxUserId, activityId);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            verify(relationMapDOMapper).selectByExample(relationMapExampleCaptor.capture());
            verify(personalWxGroupMemberInfoDOMapper, never()).selectByExample(any());
            
            ScrmActivityAndDSPersonalWxGroupRelationMapDOExample capturedExample = relationMapExampleCaptor.getValue();
            assertEquals(activityId, capturedExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
        }
        
        @Test
        @DisplayName("当找到活动关联的群和成员时，应返回成员列表")
        public void shouldReturnMemberListWhenFound() {
            // 准备
            String wxUserId = "test_wx_user_id";
            Long activityId = 1L;
            
            List<ScrmActivityAndDSPersonalWxGroupRelationMapDO> mockRelations = createMockRelations(activityId);
            List<ScrmDSPersonalWxGroupMemberInfoDO> mockMembers = createMockMemberInfos(wxUserId);
            
            when(relationMapDOMapper.selectByExample(any())).thenReturn(mockRelations);
            when(personalWxGroupMemberInfoDOMapper.selectByExample(any())).thenReturn(mockMembers);
            
            // 执行
            List<ScrmDSPersonalWxGroupMemberInfoDO> result = personalWxGroupMemberInfoDomainService.queryMemberInfoByWxUserId(wxUserId, activityId);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertEquals(2, result.size(), "结果应包含2个成员");
            assertEquals("测试用户1", result.get(0).getMemberName());
            assertEquals("测试用户2", result.get(1).getMemberName());
            
            verify(relationMapDOMapper).selectByExample(relationMapExampleCaptor.capture());
            verify(personalWxGroupMemberInfoDOMapper).selectByExample(memberInfoExampleCaptor.capture());
            
            ScrmActivityAndDSPersonalWxGroupRelationMapDOExample relationExample = relationMapExampleCaptor.getValue();
            assertEquals(activityId, relationExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
            
            ScrmDSPersonalWxGroupMemberInfoDOExample memberExample = memberInfoExampleCaptor.getValue();
            assertEquals(wxUserId, memberExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
            
            List<Long> capturedDsGroupIds = (List<Long>) memberExample.getOredCriteria().get(0).getCriteria().get(1).getValue();
            assertEquals(2, capturedDsGroupIds.size());
            assertTrue(capturedDsGroupIds.contains(1001L));
            assertTrue(capturedDsGroupIds.contains(1002L));
        }
    }
    
    @Nested
    @DisplayName("queryInGroupMemberInfoByWxUserId 方法测试")
    public class QueryInGroupMemberInfoByWxUserIdTest {
        
        private List<ScrmActivityAndDSPersonalWxGroupRelationMapDO> createMockRelations(Long activityId) {
            ScrmActivityAndDSPersonalWxGroupRelationMapDO relation1 = new ScrmActivityAndDSPersonalWxGroupRelationMapDO();
            relation1.setActivityId(activityId);
            relation1.setDsGroupId(1001L);
            
            ScrmActivityAndDSPersonalWxGroupRelationMapDO relation2 = new ScrmActivityAndDSPersonalWxGroupRelationMapDO();
            relation2.setActivityId(activityId);
            relation2.setDsGroupId(1002L);
            
            return Arrays.asList(relation1, relation2);
        }
        
        private List<ScrmDSPersonalWxGroupMemberInfoDO> createMockMemberInfos(String wxUserId, String corpId) {
            ScrmDSPersonalWxGroupMemberInfoDO member1 = new ScrmDSPersonalWxGroupMemberInfoDO();
            member1.setId(1L);
            member1.setWxuserid(wxUserId);
            member1.setDsGroupId(1001L);
            member1.setCorpId(corpId);
            member1.setStatus(0); // 在群状态
            member1.setMemberName("测试用户1");
            
            ScrmDSPersonalWxGroupMemberInfoDO member2 = new ScrmDSPersonalWxGroupMemberInfoDO();
            member2.setId(2L);
            member2.setWxuserid(wxUserId);
            member2.setDsGroupId(1002L);
            member2.setCorpId(corpId);
            member2.setStatus(0); // 在群状态
            member2.setMemberName("测试用户2");
            
            return Arrays.asList(member1, member2);
        }

        @Test
        @DisplayName("当wxUserId为空时，应返回空列表")
        public void shouldReturnEmptyListWhenWxUserIdIsEmpty() {
            // 执行
            List<ScrmDSPersonalWxGroupMemberInfoDO> result = personalWxGroupMemberInfoDomainService.queryInGroupMemberInfoByWxUserId("", 1L, "test_corp_id");
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            verify(relationMapDOMapper, never()).selectByExample(any());
            verify(personalWxGroupMemberInfoDOMapper, never()).selectByExample(any());
        }
        
        @Test
        @DisplayName("当activityId为null时，应返回空列表")
        public void shouldReturnEmptyListWhenActivityIdIsNull() {
            // 执行
            List<ScrmDSPersonalWxGroupMemberInfoDO> result = personalWxGroupMemberInfoDomainService.queryInGroupMemberInfoByWxUserId("test_wx_user_id", null, "test_corp_id");
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            verify(relationMapDOMapper, never()).selectByExample(any());
            verify(personalWxGroupMemberInfoDOMapper, never()).selectByExample(any());
        }
        
        @Test
        @DisplayName("当找不到活动关联的群时，应返回空列表")
        public void shouldReturnEmptyListWhenNoRelationFound() {
            // 准备
            String wxUserId = "test_wx_user_id";
            Long activityId = 1L;
            String corpId = "test_corp_id";
            
            when(relationMapDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
            
            // 执行
            List<ScrmDSPersonalWxGroupMemberInfoDO> result = personalWxGroupMemberInfoDomainService.queryInGroupMemberInfoByWxUserId(wxUserId, activityId, corpId);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isEmpty(), "结果应为空列表");
            verify(relationMapDOMapper).selectByExample(relationMapExampleCaptor.capture());
            verify(personalWxGroupMemberInfoDOMapper, never()).selectByExample(any());
            
            ScrmActivityAndDSPersonalWxGroupRelationMapDOExample capturedExample = relationMapExampleCaptor.getValue();
            assertEquals(activityId, capturedExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
        }
        
        @Test
        @DisplayName("当找到活动关联的群和在群成员时，应返回成员列表")
        public void shouldReturnInGroupMemberListWhenFound() {
            // 准备
            String wxUserId = "test_wx_user_id";
            Long activityId = 1L;
            String corpId = "test_corp_id";
            
            List<ScrmActivityAndDSPersonalWxGroupRelationMapDO> mockRelations = createMockRelations(activityId);
            List<ScrmDSPersonalWxGroupMemberInfoDO> mockMembers = createMockMemberInfos(wxUserId, corpId);
            
            when(relationMapDOMapper.selectByExample(any())).thenReturn(mockRelations);
            when(personalWxGroupMemberInfoDOMapper.selectByExample(any())).thenReturn(mockMembers);
            
            // 执行
            List<ScrmDSPersonalWxGroupMemberInfoDO> result = personalWxGroupMemberInfoDomainService.queryInGroupMemberInfoByWxUserId(wxUserId, activityId, corpId);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertEquals(2, result.size(), "结果应包含2个成员");
            assertEquals("测试用户1", result.get(0).getMemberName());
            assertEquals("测试用户2", result.get(1).getMemberName());
            
            verify(relationMapDOMapper).selectByExample(relationMapExampleCaptor.capture());
            verify(personalWxGroupMemberInfoDOMapper).selectByExample(memberInfoExampleCaptor.capture());
            
            ScrmActivityAndDSPersonalWxGroupRelationMapDOExample relationExample = relationMapExampleCaptor.getValue();
            assertEquals(activityId, relationExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
            
            ScrmDSPersonalWxGroupMemberInfoDOExample memberExample = memberInfoExampleCaptor.getValue();
            assertEquals(wxUserId, memberExample.getOredCriteria().get(0).getCriteria().get(0).getValue());
            assertEquals(corpId, memberExample.getOredCriteria().get(0).getCriteria().get(2).getValue());
            assertEquals(0, memberExample.getOredCriteria().get(0).getCriteria().get(3).getValue());
            
            List<Long> capturedDsGroupIds = (List<Long>) memberExample.getOredCriteria().get(0).getCriteria().get(1).getValue();
            assertEquals(2, capturedDsGroupIds.size());
            assertTrue(capturedDsGroupIds.contains(1001L));
            assertTrue(capturedDsGroupIds.contains(1002L));
        }
    }

    @Nested
    @DisplayName("updateWxGroupMember 方法测试")
    public class UpdateWxGroupMemberTest {

        private OpenGroupChangeEvent createBasicChangeEvent() {
            OpenGroupChangeEvent changeEvent = new OpenGroupChangeEvent();
            changeEvent.setCorpId("test_corp_id");
            changeEvent.setOrgId(1234);
            changeEvent.setBusinessCode("test_business_code");
            changeEvent.setEventTime(System.currentTimeMillis());
            
            OpenGroupSimpleInfo groupInfo = new OpenGroupSimpleInfo();
            groupInfo.setDsGroupId(1001L);
            groupInfo.setWxGroupId("test_wx_group_id");
            groupInfo.setGroupName("测试群");
            
            changeEvent.setOpenGroupSimpleInfo(groupInfo);
            
            return changeEvent;
        }

        private OpenGroupMemberSimpleInfo createMemberInfo(String wxUserId, boolean isJoin) {
            OpenGroupMemberSimpleInfo memberInfo = new OpenGroupMemberSimpleInfo();
            memberInfo.setWxUserId(wxUserId);
            memberInfo.setUserName("测试用户");
            memberInfo.setGroupNickName("群昵称");
            memberInfo.setAvatar("https://test.avatar.url");
            memberInfo.setUserType(1); // 普通成员
            if (isJoin) {
                memberInfo.setJoinTime(System.currentTimeMillis() / 1000);
            } else {
                memberInfo.setExitTime(System.currentTimeMillis() / 1000);
            }
            return memberInfo;
        }

        @Test
        @DisplayName("当 changeEvent 为null时，应返回失败的结果")
        public void shouldReturnFailureWhenChangeEventIsNull() {
            // 执行
            UpdateResultDTO result = personalWxGroupMemberInfoDomainService.updateWxGroupMember(null, false);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertFalse(result.isSuccess(), "结果应表示失败");
            verify(personalWxGroupMemberInfoDOMapper, never()).selectByExample(any());
            verify(personalWxGroupMemberInfoDOMapper, never()).insert(any());
            verify(personalWxGroupMemberInfoDOMapper, never()).updateByPrimaryKeySelective(any());
        }
        
        @Test
        @DisplayName("处理成员加入事件，应插入成员信息并发送消息")
        public void shouldHandleJoinMember() {
            // 准备
            OpenGroupChangeEvent changeEvent = createBasicChangeEvent();
            OpenGroupMemberSimpleInfo memberInfo = createMemberInfo("test_wx_user_id", true);
            changeEvent.setAddedMemberList(Collections.singletonList(memberInfo));
            
            // 模拟数据库中不存在该成员
            when(personalWxGroupMemberInfoDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
            
            // 模拟插入成功
            when(personalWxGroupMemberInfoDOMapper.insert(any())).thenAnswer(invocation -> {
                ScrmDSPersonalWxGroupMemberInfoDO insertedDO = invocation.getArgument(0);
                insertedDO.setId(1L); // 模拟数据库生成ID
                return 1;
            });
            
            // 执行
            UpdateResultDTO result = personalWxGroupMemberInfoDomainService.updateWxGroupMember(changeEvent, false);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isSuccess(), "结果应表示成功");
            
            verify(personalWxGroupMemberInfoDOMapper).selectByExample(any());
            verify(personalWxGroupMemberInfoDOMapper).insert(memberInfoCaptor.capture());
            verify(personalWxGroupMemberInfoDOMapper, never()).updateByPrimaryKeySelective(any());
            verify(fissionMatchTaskProducer).sendEventMsg(fissionMatchTaskDTOCaptor.capture());
            
            // 验证插入的数据
            ScrmDSPersonalWxGroupMemberInfoDO capturedMember = memberInfoCaptor.getValue();
            assertEquals(memberInfo.getWxUserId(), capturedMember.getWxuserid());
            assertEquals(memberInfo.getUserName(), capturedMember.getMemberName());
            assertEquals(memberInfo.getGroupNickName(), capturedMember.getMemberNickName());
            assertEquals(memberInfo.getAvatar(), capturedMember.getAvatar());
            assertEquals(0, capturedMember.getStatus()); // 在群状态
            
            // 验证发送的消息
            FissionMatchTaskDTO capturedTask = fissionMatchTaskDTOCaptor.getValue();
            assertEquals(1L, capturedTask.getGroupMemberInfoId());
            assertEquals(changeEvent.getOpenGroupSimpleInfo().getDsGroupId(), capturedTask.getDsGroupId());
            assertEquals(changeEvent.getOpenGroupSimpleInfo().getWxGroupId(), capturedTask.getWxGroupId());
            assertEquals(memberInfo.getWxUserId(), capturedTask.getUserWxId());
            assertEquals(1, capturedTask.getEventType()); // 加入事件
        }
        
        @Test
        @DisplayName("处理成员退出事件，应更新成员状态并发送消息")
        public void shouldHandleQuitMember() {
            // 准备
            OpenGroupChangeEvent changeEvent = createBasicChangeEvent();
            OpenGroupMemberSimpleInfo memberInfo = createMemberInfo("test_wx_user_id", false);
            changeEvent.setRemovedMemberList(Collections.singletonList(memberInfo));
            
            // 模拟数据库中已存在该成员
            ScrmDSPersonalWxGroupMemberInfoDO existingMember = new ScrmDSPersonalWxGroupMemberInfoDO();
            existingMember.setId(1L);
            existingMember.setWxuserid(memberInfo.getWxUserId());
            existingMember.setDsGroupId(changeEvent.getOpenGroupSimpleInfo().getDsGroupId());
            existingMember.setCorpId(changeEvent.getCorpId());
            existingMember.setStatus(0); // 当前在群
            existingMember.setMemberName("原始名称");
            existingMember.setMemberNickName("原始昵称");
            existingMember.setEventTime(changeEvent.getEventTime() - 1000); // 旧事件时间
            
            when(personalWxGroupMemberInfoDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(existingMember));
            
            // 模拟更新成功
            when(personalWxGroupMemberInfoDOMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
            
            // 执行
            UpdateResultDTO result = personalWxGroupMemberInfoDomainService.updateWxGroupMember(changeEvent, false);
            
            // 验证
            assertNotNull(result, "结果不应为null");
            assertTrue(result.isSuccess(), "结果应表示成功");
            
            verify(personalWxGroupMemberInfoDOMapper).selectByExample(any());
            verify(personalWxGroupMemberInfoDOMapper, never()).insert(any());
            verify(personalWxGroupMemberInfoDOMapper).updateByPrimaryKeySelective(memberInfoCaptor.capture());
            verify(fissionMatchTaskProducer).sendEventMsg(fissionMatchTaskDTOCaptor.capture());
            
            // 验证更新的数据
            ScrmDSPersonalWxGroupMemberInfoDO capturedMember = memberInfoCaptor.getValue();
            assertEquals(1, capturedMember.getStatus()); // 离群状态
            assertNotNull(capturedMember.getLeaveTime(), "离群时间不应为null");
            
            // 验证发送的消息
            FissionMatchTaskDTO capturedTask = fissionMatchTaskDTOCaptor.getValue();
            assertEquals(1L, capturedTask.getGroupMemberInfoId());
            assertEquals(changeEvent.getOpenGroupSimpleInfo().getDsGroupId(), capturedTask.getDsGroupId());
            assertEquals(changeEvent.getOpenGroupSimpleInfo().getWxGroupId(), capturedTask.getWxGroupId());
            assertEquals(memberInfo.getWxUserId(), capturedTask.getUserWxId());
            assertEquals(0, capturedTask.getEventType()); // 退出事件
        }
    }

    // 其他测试方法将在此处添加
} 