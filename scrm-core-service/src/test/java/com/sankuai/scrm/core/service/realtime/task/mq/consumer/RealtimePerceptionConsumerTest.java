package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.meituan.mafka.client.consumer.IConsumerProcessor;
import org.mockito.Mockito;
import java.lang.reflect.Field;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class RealtimePerceptionConsumerTest {

    private RealtimePerceptionConsumer realtimePerceptionConsumer;

    private IConsumerProcessor consumer;

    @Before
    public void setUp() throws NoSuchFieldException, IllegalAccessException {
        realtimePerceptionConsumer = new RealtimePerceptionConsumer();
        consumer = Mockito.mock(IConsumerProcessor.class);
        // Use reflection to set the consumer field
        Field consumerField = RealtimePerceptionConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(realtimePerceptionConsumer, consumer);
    }

    /**
     * 测试 destroy 方法，consumer 为 null 的情况
     */
    @Test
    public void testDestroyConsumerIsNull() throws Throwable {
        // Use reflection to set the consumer field to null
        Field consumerField = RealtimePerceptionConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(realtimePerceptionConsumer, null);
        // act
        realtimePerceptionConsumer.destroy();
        // assert
        verify(consumer, never()).close();
    }

    /**
     * 测试 destroy 方法，consumer 不为 null 的情况
     */
    @Test
    public void testDestroyConsumerIsNotNull() throws Throwable {
        // arrange
        doNothing().when(consumer).close();
        // act
        realtimePerceptionConsumer.destroy();
        // assert
        verify(consumer, times(1)).close();
    }
}
