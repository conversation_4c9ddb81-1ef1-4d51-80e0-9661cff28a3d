package com.sankuai.scrm.core.service.activity.fission.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.activity.fission.dto.activity.PersonalGroupInfoDTO;
import com.sankuai.dz.srcm.activity.fission.request.*;
import com.sankuai.dz.srcm.group.dynamiccode.dto.GroupDynamicCodeChannelDTO;
import com.sankuai.dz.srcm.group.dynamiccode.dto.SaveGroupDynamicCodeChannelResult;
import com.sankuai.dz.srcm.group.dynamiccode.enums.CommonOperationResultEnum;
import com.sankuai.dz.srcm.group.dynamiccode.enums.DynamicCodeChannelSceneEnum;
import com.sankuai.scrm.core.service.activity.draw.fission.dal.mapper.FissionActivityDrawConfigMapper;
import com.sankuai.scrm.core.service.activity.fission.crane.ActivityAwardDistributeCheckTask;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionActivity;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionActivityMapper;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionPrizeMapper;
import com.sankuai.scrm.core.service.activity.fission.domain.GroupFissionActivityDomainService;
import com.sankuai.scrm.core.service.activity.fission.domain.GroupFissionPrizeService;
import com.sankuai.scrm.core.service.activity.fission.validation.FissionChainContext;
import com.sankuai.scrm.core.service.activity.fission.validation.enums.FissionChainMarkEnum;
import com.sankuai.scrm.core.service.activity.fission.validation.enums.FissionChainOperationEnum;
import com.sankuai.scrm.core.service.activity.fission.validation.exception.FissionValidatorException;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmActivityAndDSPersonalWxGroupRelationMapDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmDSPersonalWxGroupInfoDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmActivityAndDSPersonalWxGroupRelationMapDOMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmDSPersonalWxGroupInfoDOMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.PersonalWxGroupInfoDomainService;
import com.sankuai.scrm.core.service.group.dynamiccode.domain.GroupDynamicCodeChannelLocalService;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.mapper.GroupDynamicCodeChannelConfigMapper;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeChannelConfig;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class GroupFissionActivityServiceImplGetSavePosterResultTest {

    @Mock
    private GroupDynamicCodeChannelConfigMapper codeChannelConfigMapper;

    @InjectMocks
    private GroupFissionActivityServiceImpl service;

    @Mock
    private GroupFissionActivityMapper groupFissionActivityMapper;

    @Mock
    private GroupFissionPrizeMapper groupFissionPrizeMapper;

    @Mock
    private GroupFissionPrizeService groupFissionPrizeService;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private ScrmActivityAndDSPersonalWxGroupRelationMapDOMapper personalWxGroupRelationMapDOMapper;

    @Mock
    private ScrmDSPersonalWxGroupInfoDOMapper personalWxGroupInfoDOMapper;

    @Mock
    private GroupDynamicCodeChannelLocalService groupDynamicCodeChannelLocalService;

    @Mock
    private FissionChainContext<GroupFissionActivityRequest> fissionChainContext;

    @Mock
    private FissionActivityDrawConfigMapper fissionActivityDrawConfigMapper;

    @Mock
    private GroupFissionActivityDomainService groupFissionActivityDomainService;

    @Mock
    private PersonalWxGroupInfoDomainService personalWxGroupInfoDomainService;

    @Mock
    private ActivityAwardDistributeCheckTask activityAwardDistributeCheckTask;

    private boolean invokeUpdateFissionActivity(GroupFissionActivityRequest request, GroupFissionActivity groupActivityResult, boolean isActivityStart) throws Exception {
        Method method = GroupFissionActivityServiceImpl.class.getDeclaredMethod("updateFissionActivity", GroupFissionActivityRequest.class, GroupFissionActivity.class, boolean.class);
        method.setAccessible(true);
        return (boolean) method.invoke(service, request, groupActivityResult, isActivityStart);
    }

    private GroupFissionActivityRequest createValidRequest() {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setAppId("testApp");
        request.setActivityId(1L);
        ActivityBaseInfoRequest baseInfo = new ActivityBaseInfoRequest();
        baseInfo.setActivityName("Test Activity");
        baseInfo.setActivityInnerName("Test Inner");
        baseInfo.setStartTime(System.currentTimeMillis());
        baseInfo.setEndTime(System.currentTimeMillis() + 100000);
        baseInfo.setShowRankList(true);
        baseInfo.setRule("Test Rule");
        request.setActivityBaseInfo(baseInfo);
        PosterInfoRequest posterInfo = new PosterInfoRequest();
        posterInfo.setShowAvtar(true);
        posterInfo.setShowNickName(true);
        request.setPosterInfo(posterInfo);
        RewardInfoRequest rewardInfo = new RewardInfoRequest();
        rewardInfo.setStage(1);
        rewardInfo.setInvitationNum(5);
        request.setRewardInfo(Collections.singletonList(rewardInfo));
        ShareCardInfo shareCardInfo = new ShareCardInfo();
        shareCardInfo.setMinipName("testMinip");
        shareCardInfo.setCardTitle("Test Card");
        shareCardInfo.setCardImg("test.jpg");
        request.setShareCardInfo(shareCardInfo);
        return request;
    }

    private boolean invokeUpdatePrizeInfo(GroupFissionActivityRequest request, boolean isActivityStart) throws Exception {
        Method method = GroupFissionActivityServiceImpl.class.getDeclaredMethod("updatePrizeInfo", GroupFissionActivityRequest.class, boolean.class);
        method.setAccessible(true);
        return (boolean) method.invoke(service, request, isActivityStart);
    }

    private GroupFissionActivity buildUpdateGroupFissionActivity(GroupFissionActivityRequest request, ActivityBaseInfoRequest activityBaseInfo, PosterInfoRequest posterInfoRe) throws Exception {
        try {
            GroupFissionActivityServiceImpl service = new GroupFissionActivityServiceImpl();
            Method method = GroupFissionActivityServiceImpl.class.getDeclaredMethod("buildUpdateGroupFissionActivity", GroupFissionActivityRequest.class, ActivityBaseInfoRequest.class, PosterInfoRequest.class);
            method.setAccessible(true);
            return (GroupFissionActivity) method.invoke(service, request, activityBaseInfo, posterInfoRe);
        } catch (InvocationTargetException e) {
            // Unwrap the actual exception thrown by the method
            throw (Exception) e.getCause();
        }
    }

    private List<PersonalGroupInfoDTO> invokeGetPersonalGroupInfo(String appId, Long activityId) throws Exception {
        Method method = GroupFissionActivityServiceImpl.class.getDeclaredMethod("getPersonalGroupInfo", String.class, Long.class);
        method.setAccessible(true);
        return (List<PersonalGroupInfoDTO>) method.invoke(service, appId, activityId);
    }

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    private GroupFissionActivityRequest buildValidRequest() {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setAppId("testApp");
        ActivityBaseInfoRequest baseInfo = new ActivityBaseInfoRequest();
        baseInfo.setActivityName("Test Activity");
        baseInfo.setActivityType((byte) 1);
        baseInfo.setStartTime(System.currentTimeMillis());
        baseInfo.setEndTime(System.currentTimeMillis() + 86400000);
        baseInfo.setActivityHeadImg("headImg");
        baseInfo.setQuitCheck(true);
        baseInfo.setNewUserCheck(true);
        baseInfo.setRiskCheck(true);
        baseInfo.setShowRankList(true);
        baseInfo.setRule("Test Rule");
        request.setActivityBaseInfo(baseInfo);
        PosterInfoRequest posterInfo = new PosterInfoRequest();
        posterInfo.setPoster("posterUrl");
        posterInfo.setShowAvtar(true);
        posterInfo.setShowNickName(true);
        request.setPosterInfo(posterInfo);
        RewardInfoRequest rewardInfo = new RewardInfoRequest();
        rewardInfo.setStage(1);
        rewardInfo.setInvitationNum(10);
        rewardInfo.setPriceName("Test Prize");
        request.setRewardInfo(Collections.singletonList(rewardInfo));
        PersonalGroupInfoDTO groupInfo = new PersonalGroupInfoDTO();
        groupInfo.setDsGroupId(1L);
        request.setPersonalGroupInfo(Collections.singletonList(groupInfo));
        ShareCardInfo shareCardInfo = new ShareCardInfo();
        shareCardInfo.setCardImg("cardImg");
        shareCardInfo.setCardTitle("cardTitle");
        shareCardInfo.setMinipName("minipName");
        request.setShareCardInfo(shareCardInfo);
        return request;
    }

    private RemoteResponse<Boolean> invokePrivateMethod(GroupFissionActivityRequest request) throws Exception {
        Method method = GroupFissionActivityServiceImpl.class.getDeclaredMethod("saveNonWeComGroupFissionActivity", GroupFissionActivityRequest.class);
        method.setAccessible(true);
        return (RemoteResponse<Boolean>) method.invoke(service, request);
    }

    private SaveGroupDynamicCodeChannelResult invokePrivateMethod(String appId, ActivityBaseInfoRequest activityBaseInfo) throws Exception {
        Method method = GroupFissionActivityServiceImpl.class.getDeclaredMethod("saveGroupDynamicCodeChannelResult", String.class, ActivityBaseInfoRequest.class);
        method.setAccessible(true);
        return (SaveGroupDynamicCodeChannelResult) method.invoke(service, appId, activityBaseInfo);
    }

    private boolean invokeUpdateGroupList(GroupFissionActivityRequest request, boolean isActivityStart) throws Exception {
        Method method = GroupFissionActivityServiceImpl.class.getDeclaredMethod("updateGroupList", GroupFissionActivityRequest.class, boolean.class);
        method.setAccessible(true);
        return (boolean) method.invoke(service, request, isActivityStart);
    }

    /**
     * Test successful poster save operation using reflection to access private method
     */
    @Test
    public void testGetSavePosterResultWhenInsertSucceeds() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setAppId("testAppId");
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        activityBaseInfo.setActivityName("Test Activity");
        PosterInfoRequest posterInfo = new PosterInfoRequest();
        posterInfo.setPoster("testPosterUrl");
        posterInfo.setDynamicCodeIcon("testIconUrl");
        Long channelId = 1L;
        when(codeChannelConfigMapper.insertSelective(any(GroupDynamicCodeChannelConfig.class))).thenReturn(1);
        // Use reflection to access private method
        Method method = GroupFissionActivityServiceImpl.class.getDeclaredMethod("getSavePosterResult", GroupFissionActivityRequest.class, ActivityBaseInfoRequest.class, PosterInfoRequest.class, Long.class);
        method.setAccessible(true);
        // act
        boolean result = (boolean) method.invoke(service, request, activityBaseInfo, posterInfo, channelId);
        // assert
        assertTrue(result);
        verify(codeChannelConfigMapper).insertSelective(any(GroupDynamicCodeChannelConfig.class));
    }

    /**
     * Test failed poster save operation using reflection to access private method
     */
    @Test
    public void testGetSavePosterResultWhenInsertFails() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setAppId("testAppId");
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        activityBaseInfo.setActivityName("Test Activity");
        PosterInfoRequest posterInfo = new PosterInfoRequest();
        posterInfo.setPoster("testPosterUrl");
        posterInfo.setDynamicCodeIcon("testIconUrl");
        Long channelId = 1L;
        when(codeChannelConfigMapper.insertSelective(any(GroupDynamicCodeChannelConfig.class))).thenReturn(0);
        // Use reflection to access private method
        Method method = GroupFissionActivityServiceImpl.class.getDeclaredMethod("getSavePosterResult", GroupFissionActivityRequest.class, ActivityBaseInfoRequest.class, PosterInfoRequest.class, Long.class);
        method.setAccessible(true);
        // act
        boolean result = (boolean) method.invoke(service, request, activityBaseInfo, posterInfo, channelId);
        // assert
        assertFalse(result);
        verify(codeChannelConfigMapper).insertSelective(any(GroupDynamicCodeChannelConfig.class));
    }

    /**
     * Test successful update when activity is not started
     */
    @Test
    public void testUpdateFissionActivity_NotStarted_Success() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = createValidRequest();
        GroupFissionActivity existingActivity = new GroupFissionActivity();
        existingActivity.setChannelId(123L);
        existingActivity.setCardTitle("Old Title");
        when(groupFissionActivityMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        // act
        boolean result = invokeUpdateFissionActivity(request, existingActivity, false);
        // assert
        assertTrue(result);
        verify(groupFissionActivityMapper).updateByPrimaryKeySelective(any(GroupFissionActivity.class));
    }

    /**
     * Test successful update when activity is started
     */
    @Test
    public void testUpdateFissionActivity_Started_Success() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = createValidRequest();
        GroupFissionActivity existingActivity = new GroupFissionActivity();
        when(groupFissionActivityMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        // act
        boolean result = invokeUpdateFissionActivity(request, existingActivity, true);
        // assert
        assertTrue(result);
        verify(groupFissionActivityMapper).updateByPrimaryKeySelective(any(GroupFissionActivity.class));
    }

    /**
     * Test failed update when database operation returns 0
     */
    @Test
    public void testUpdateFissionActivity_DatabaseFailure() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = createValidRequest();
        GroupFissionActivity existingActivity = new GroupFissionActivity();
        when(groupFissionActivityMapper.updateByPrimaryKeySelective(any())).thenReturn(0);
        // act
        boolean result = invokeUpdateFissionActivity(request, existingActivity, false);
        // assert
        assertFalse(result);
        verify(groupFissionActivityMapper).updateByPrimaryKeySelective(any(GroupFissionActivity.class));
    }

    /**
     * Test null request parameter
     */
    @Test
    public void testUpdateFissionActivity_NullRequest() throws Throwable {
        // arrange
        GroupFissionActivity existingActivity = new GroupFissionActivity();
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> invokeUpdateFissionActivity(null, existingActivity, false));
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    /**
     * Test null existing activity parameter
     */
    @Test
    public void testUpdateFissionActivity_NullExistingActivity() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = createValidRequest();
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> invokeUpdateFissionActivity(request, null, false));
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    /**
     * Test with empty reward list when activity is not started
     */
    @Test
    public void testUpdateFissionActivity_EmptyRewardList_NotStarted() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = createValidRequest();
        request.setRewardInfo(new ArrayList<>());
        GroupFissionActivity existingActivity = new GroupFissionActivity();
        existingActivity.setChannelId(123L);
        when(groupFissionActivityMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        // act
        boolean result = invokeUpdateFissionActivity(request, existingActivity, false);
        // assert
        assertTrue(result);
        verify(groupFissionActivityMapper).updateByPrimaryKeySelective(any(GroupFissionActivity.class));
    }

    /**
     * Test when isActivityStart is true, should return true directly
     */
    @Test
    public void testUpdatePrizeInfoWhenActivityStartThenReturnTrue() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(1L);
        request.setRewardInfo(Collections.singletonList(new RewardInfoRequest()));
        // act
        boolean result = invokeUpdatePrizeInfo(request, true);
        // assert
        assertTrue(result);
        verifyNoInteractions(groupFissionPrizeMapper);
        verifyNoInteractions(groupFissionPrizeService);
    }

    /**
     * Test when isActivityStart is false and all operations succeed
     */
    @Test
    public void testUpdatePrizeInfoWhenNotActivityStartAndAllOperationsSucceedThenReturnTrue() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(1L);
        List<RewardInfoRequest> rewardInfo = Collections.singletonList(new RewardInfoRequest());
        request.setRewardInfo(rewardInfo);
        when(groupFissionPrizeMapper.deleteByExample(any())).thenReturn(1);
        when(groupFissionPrizeService.saveGroupFissionPrize(rewardInfo, 1L)).thenReturn(true);
        // act
        boolean result = invokeUpdatePrizeInfo(request, false);
        // assert
        assertTrue(result);
        verify(groupFissionPrizeMapper).deleteByExample(any());
        verify(groupFissionPrizeService).saveGroupFissionPrize(rewardInfo, 1L);
    }

    /**
     * Test when isActivityStart is false and delete fails
     */
    @Test
    public void testUpdatePrizeInfoWhenNotActivityStartAndDeleteFailsThenReturnFalse() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(1L);
        request.setRewardInfo(Collections.singletonList(new RewardInfoRequest()));
        when(groupFissionPrizeMapper.deleteByExample(any())).thenReturn(0);
        when(groupFissionPrizeService.saveGroupFissionPrize(any(), any())).thenReturn(false);
        // act
        boolean result = invokeUpdatePrizeInfo(request, false);
        // assert
        assertFalse(result);
        verify(groupFissionPrizeMapper).deleteByExample(any());
        verify(groupFissionPrizeService).saveGroupFissionPrize(any(), any());
    }

    /**
     * Test when isActivityStart is false and save fails
     */
    @Test
    public void testUpdatePrizeInfoWhenNotActivityStartAndSaveFailsThenReturnFalse() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(1L);
        List<RewardInfoRequest> rewardInfo = Collections.singletonList(new RewardInfoRequest());
        request.setRewardInfo(rewardInfo);
        when(groupFissionPrizeMapper.deleteByExample(any())).thenReturn(1);
        when(groupFissionPrizeService.saveGroupFissionPrize(rewardInfo, 1L)).thenReturn(false);
        // act
        boolean result = invokeUpdatePrizeInfo(request, false);
        // assert
        assertFalse(result);
        verify(groupFissionPrizeMapper).deleteByExample(any());
        verify(groupFissionPrizeService).saveGroupFissionPrize(rewardInfo, 1L);
    }

    /**
     * Test when request is null should throw NullPointerException
     */
    @Test
    public void testUpdatePrizeInfoWhenRequestIsNullThenThrowException() throws Throwable {
        // arrange
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> {
            invokeUpdatePrizeInfo(null, false);
        });
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    /**
     * Test when rewardInfo is empty should return false
     */
    @Test
    public void testUpdatePrizeInfoWhenRewardInfoIsEmptyThenReturnFalse() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(1L);
        request.setRewardInfo(Collections.emptyList());
        when(groupFissionPrizeMapper.deleteByExample(any())).thenReturn(1);
        when(groupFissionPrizeService.saveGroupFissionPrize(any(), any())).thenReturn(false);
        // act
        boolean result = invokeUpdatePrizeInfo(request, false);
        // assert
        assertFalse(result);
        verify(groupFissionPrizeMapper).deleteByExample(any());
        verify(groupFissionPrizeService).saveGroupFissionPrize(any(), any());
    }

    /**
     * Test when activityId is null should throw RuntimeException
     */
    @Test
    public void testUpdatePrizeInfoWhenActivityIdIsNullThenReturnFalse() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(null);
        request.setRewardInfo(Collections.singletonList(new RewardInfoRequest()));
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> {
            invokeUpdatePrizeInfo(request, false);
        });
        assertTrue(exception.getCause() instanceof RuntimeException);
    }

    /**
     * Test normal case with all fields populated
     */
    @Test
    public void testBuildUpdateGroupFissionActivityAllFields() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(123L);
        ShareCardInfo shareCardInfo = new ShareCardInfo();
        shareCardInfo.setMinipName("minip");
        shareCardInfo.setCardTitle("title");
        shareCardInfo.setCardImg("img");
        request.setShareCardInfo(shareCardInfo);
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        activityBaseInfo.setActivityName("activity");
        activityBaseInfo.setActivityInnerName("inner");
        activityBaseInfo.setActivityHeadImg("head");
        activityBaseInfo.setEndTime(System.currentTimeMillis());
        activityBaseInfo.setRule("rule");
        activityBaseInfo.setBackgroundImg("bgImg");
        activityBaseInfo.setBackgroundColor("bgColor");
        PosterInfoRequest posterInfoRe = new PosterInfoRequest();
        posterInfoRe.setShowAvtar(true);
        posterInfoRe.setShowNickName(false);
        // act
        GroupFissionActivity result = buildUpdateGroupFissionActivity(request, activityBaseInfo, posterInfoRe);
        // assert
        assertNotNull(result);
        assertEquals(123L, result.getId());
        assertEquals("activity", result.getActivityName());
        assertEquals("inner", result.getActivityInnerName());
        assertEquals("head", result.getHeadImage());
        assertEquals("title", result.getCardTitle());
        assertEquals("img", result.getCardImg());
        assertEquals("minip", result.getCardMinipName());
        assertTrue(result.getShowAvatar());
        assertFalse(result.getShowNickName());
        assertEquals("rule", result.getRule());
        assertEquals("bgImg", result.getBackgroundImg());
        assertEquals("bgColor", result.getBackgroundColor());
        assertNotNull(result.getEndTime());
    }

    /**
     * Test with null request parameter
     */
    @Test
    public void testBuildUpdateGroupFissionActivityNullRequest() throws Throwable {
        // arrange
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        PosterInfoRequest posterInfoRe = new PosterInfoRequest();
        // act & assert
        assertThrows(NullPointerException.class, () -> buildUpdateGroupFissionActivity(null, activityBaseInfo, posterInfoRe));
    }

    /**
     * Test with null activityBaseInfo parameter
     */
    @Test
    public void testBuildUpdateGroupFissionActivityNullActivityBaseInfo() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        PosterInfoRequest posterInfoRe = new PosterInfoRequest();
        // act & assert
        assertThrows(NullPointerException.class, () -> buildUpdateGroupFissionActivity(request, null, posterInfoRe));
    }

    /**
     * Test with null posterInfo parameter
     */
    @Test
    public void testBuildUpdateGroupFissionActivityNullPosterInfo() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        // act & assert
        assertThrows(NullPointerException.class, () -> buildUpdateGroupFissionActivity(request, activityBaseInfo, null));
    }

    /**
     * Test with empty strings in fields
     */
    @Test
    public void testBuildUpdateGroupFissionActivityEmptyStrings() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(123L);
        ShareCardInfo shareCardInfo = new ShareCardInfo();
        shareCardInfo.setMinipName("");
        shareCardInfo.setCardTitle("");
        shareCardInfo.setCardImg("");
        request.setShareCardInfo(shareCardInfo);
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        activityBaseInfo.setActivityName("");
        activityBaseInfo.setActivityInnerName("");
        activityBaseInfo.setActivityHeadImg("");
        activityBaseInfo.setEndTime(0L);
        activityBaseInfo.setRule("");
        activityBaseInfo.setBackgroundImg("");
        activityBaseInfo.setBackgroundColor("");
        PosterInfoRequest posterInfoRe = new PosterInfoRequest();
        posterInfoRe.setShowAvtar(false);
        posterInfoRe.setShowNickName(false);
        // act
        GroupFissionActivity result = buildUpdateGroupFissionActivity(request, activityBaseInfo, posterInfoRe);
        // assert
        assertNotNull(result);
        assertEquals(123L, result.getId());
        assertEquals("", result.getActivityName());
        assertEquals("", result.getActivityInnerName());
        assertEquals("", result.getHeadImage());
        assertEquals("", result.getCardTitle());
        assertEquals("", result.getCardImg());
        assertEquals("", result.getCardMinipName());
        assertFalse(result.getShowAvatar());
        assertFalse(result.getShowNickName());
        assertEquals("", result.getRule());
        assertEquals("", result.getBackgroundImg());
        assertEquals("", result.getBackgroundColor());
        assertNotNull(result.getEndTime());
    }

    /**
     * Test with null shareCardInfo in request
     */
    @Test
    public void testBuildUpdateGroupFissionActivityNullShareCardInfo() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(123L);
        request.setShareCardInfo(null);
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        activityBaseInfo.setActivityName("name");
        activityBaseInfo.setEndTime(System.currentTimeMillis());
        PosterInfoRequest posterInfoRe = new PosterInfoRequest();
        posterInfoRe.setShowAvtar(true);
        // act & assert
        assertThrows(NullPointerException.class, () -> buildUpdateGroupFissionActivity(request, activityBaseInfo, posterInfoRe));
    }

    /**
     * Test with minimum required fields
     */
    @Test
    public void testBuildUpdateGroupFissionActivityMinimumFields() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(123L);
        ShareCardInfo shareCardInfo = new ShareCardInfo();
        shareCardInfo.setMinipName("minip");
        shareCardInfo.setCardTitle("title");
        shareCardInfo.setCardImg("img");
        request.setShareCardInfo(shareCardInfo);
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        activityBaseInfo.setActivityName("name");
        activityBaseInfo.setEndTime(System.currentTimeMillis());
        PosterInfoRequest posterInfoRe = new PosterInfoRequest();
        // act
        GroupFissionActivity result = buildUpdateGroupFissionActivity(request, activityBaseInfo, posterInfoRe);
        // assert
        assertNotNull(result);
        assertEquals(123L, result.getId());
        assertEquals("name", result.getActivityName());
        assertNotNull(result.getEndTime());
        assertNull(result.getShowAvatar());
    }

    /**
     * Test when appId is blank or activityId is null
     */
    @Test
    public void testGetPersonalGroupInfoInvalidParams() throws Throwable {
        // Mock behavior for blank appId case
        when(corpAppConfigRepository.getCorpIdByAppId("")).thenReturn(null);
        // Test blank appId
        List<PersonalGroupInfoDTO> result1 = invokeGetPersonalGroupInfo("", 1L);
        assertNull(result1);
        // Mock behavior for null activityId case
        when(corpAppConfigRepository.getCorpIdByAppId("app1")).thenReturn("corp1");
        // Test null activityId
        List<PersonalGroupInfoDTO> result2 = invokeGetPersonalGroupInfo("app1", null);
        assertNull(result2);
        // Verify repository interactions
        verify(corpAppConfigRepository).getCorpIdByAppId("");
        verify(corpAppConfigRepository).getCorpIdByAppId("app1");
        verifyNoMoreInteractions(corpAppConfigRepository);
        verifyNoInteractions(personalWxGroupRelationMapDOMapper);
        verifyNoInteractions(personalWxGroupInfoDOMapper);
    }

    /**
     * Test when no relation map records found
     */
    @Test
    public void testGetPersonalGroupInfoNoRelationMap() throws Throwable {
        // Arrange
        when(corpAppConfigRepository.getCorpIdByAppId("app1")).thenReturn("corp1");
        when(personalWxGroupRelationMapDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // Act
        List<PersonalGroupInfoDTO> result = invokeGetPersonalGroupInfo("app1", 1L);
        // Assert
        assertNull(result);
        verify(corpAppConfigRepository).getCorpIdByAppId("app1");
        verify(personalWxGroupRelationMapDOMapper).selectByExample(any());
        verifyNoInteractions(personalWxGroupInfoDOMapper);
    }

    /**
     * Test when no group info records found
     */
    @Test
    public void testGetPersonalGroupInfoNoGroupInfo() throws Throwable {
        // Arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDO relation = new ScrmActivityAndDSPersonalWxGroupRelationMapDO();
        relation.setDsGroupId(100L);
        relation.setGroupIndex(1);
        when(corpAppConfigRepository.getCorpIdByAppId("app1")).thenReturn("corp1");
        when(personalWxGroupRelationMapDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(relation));
        when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // Act
        List<PersonalGroupInfoDTO> result = invokeGetPersonalGroupInfo("app1", 1L);
        // Assert
        assertNull(result);
        verify(corpAppConfigRepository).getCorpIdByAppId("app1");
        verify(personalWxGroupRelationMapDOMapper).selectByExample(any());
        verify(personalWxGroupInfoDOMapper).selectByExample(any());
    }

    /**
     * Test successful path with multiple groups
     */
    @Test
    public void testGetPersonalGroupInfoSuccess() throws Throwable {
        // Arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDO relation1 = new ScrmActivityAndDSPersonalWxGroupRelationMapDO();
        relation1.setDsGroupId(100L);
        // Lower index should come first
        relation1.setGroupIndex(1);
        ScrmActivityAndDSPersonalWxGroupRelationMapDO relation2 = new ScrmActivityAndDSPersonalWxGroupRelationMapDO();
        relation2.setDsGroupId(101L);
        // Higher index should come second
        relation2.setGroupIndex(2);
        ScrmDSPersonalWxGroupInfoDO group1 = new ScrmDSPersonalWxGroupInfoDO();
        group1.setDsGroupId(100L);
        group1.setGroupName("Group 100");
        group1.setWxGroupId("wx100");
        ScrmDSPersonalWxGroupInfoDO group2 = new ScrmDSPersonalWxGroupInfoDO();
        group2.setDsGroupId(101L);
        group2.setGroupName("Group 101");
        group2.setWxGroupId("wx101");
        when(corpAppConfigRepository.getCorpIdByAppId("app1")).thenReturn("corp1");
        // Unsorted input
        // Unsorted input
        when(personalWxGroupRelationMapDOMapper.selectByExample(any())).thenReturn(Arrays.asList(relation2, relation1));
        when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(Arrays.asList(group1, group2));
        // Act
        List<PersonalGroupInfoDTO> result = invokeGetPersonalGroupInfo("app1", 1L);
        // Assert
        assertNotNull(result);
        assertEquals(2, result.size());
        // Verify sorting by groupIndex (lower index first)
        assertEquals("Group 100", result.get(0).getGroupName());
        assertEquals("Group 101", result.get(1).getGroupName());
        verify(corpAppConfigRepository).getCorpIdByAppId("app1");
        verify(personalWxGroupRelationMapDOMapper).selectByExample(any());
        verify(personalWxGroupInfoDOMapper).selectByExample(any());
    }

    /**
     * Test saveDynamicCodeChannelResult with valid inputs and expect successful result.
     */
    @Test
    void testSaveDynamicCodeChannelResult_Success() throws Throwable {
        // arrange
        String appId = "testAppId";
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        activityBaseInfo.setActivityName("TestActivity");
        DynamicCodeChannelSceneEnum sceneEnum = DynamicCodeChannelSceneEnum.DYNAMIC_CODE_CHANNEL;
        GroupDynamicCodeChannelDTO groupChannelDTO = new GroupDynamicCodeChannelDTO();
        // Assuming current time formatted as HHmmss is "123456"
        groupChannelDTO.setName("TestActivity" + "123456");
        groupChannelDTO.setAppId(appId);
        groupChannelDTO.setScene(sceneEnum.getCode());
        SaveGroupDynamicCodeChannelResult expected = new SaveGroupDynamicCodeChannelResult(0, "Success", groupChannelDTO);
        when(groupDynamicCodeChannelLocalService.saveChannel(any(GroupDynamicCodeChannelDTO.class))).thenReturn(expected);
        // Use reflection to access private method
        Method method = GroupFissionActivityServiceImpl.class.getDeclaredMethod("saveDynamicCodeChannelResult", String.class, ActivityBaseInfoRequest.class, DynamicCodeChannelSceneEnum.class);
        method.setAccessible(true);
        // act
        SaveGroupDynamicCodeChannelResult result = (SaveGroupDynamicCodeChannelResult) method.invoke(service, appId, activityBaseInfo, sceneEnum);
        // assert
        assertNotNull(result);
        assertEquals(0, result.getResultCode());
        assertEquals("Success", result.getMsg());
        assertNotNull(result.getGroupDynamicCodeChannelDTO());
        assertEquals("TestActivity123456", result.getGroupDynamicCodeChannelDTO().getName());
        assertEquals(appId, result.getGroupDynamicCodeChannelDTO().getAppId());
        assertEquals(Integer.valueOf(sceneEnum.getCode()), result.getGroupDynamicCodeChannelDTO().getScene());
    }

    /**
     * Test saveDynamicCodeChannelResult with null inputs and expect NullPointerException.
     */
    @Test
    void testSaveDynamicCodeChannelResult_NullInputs() throws Throwable {
        // arrange
        String appId = null;
        ActivityBaseInfoRequest activityBaseInfo = null;
        DynamicCodeChannelSceneEnum sceneEnum = null;
        // Use reflection to access private method
        Method method = GroupFissionActivityServiceImpl.class.getDeclaredMethod("saveDynamicCodeChannelResult", String.class, ActivityBaseInfoRequest.class, DynamicCodeChannelSceneEnum.class);
        method.setAccessible(true);
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> {
            method.invoke(service, appId, activityBaseInfo, sceneEnum);
        });
        // Verify the root cause is NullPointerException
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    /**
     * Test saveDynamicCodeChannelResult with service failure and expect failure result.
     */
    @Test
    void testSaveDynamicCodeChannelResult_ServiceFailure() throws Throwable {
        // arrange
        String appId = "testAppId";
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        activityBaseInfo.setActivityName("TestActivity");
        DynamicCodeChannelSceneEnum sceneEnum = DynamicCodeChannelSceneEnum.DYNAMIC_CODE_CHANNEL;
        GroupDynamicCodeChannelDTO groupChannelDTO = new GroupDynamicCodeChannelDTO();
        // Assuming current time formatted as HHmmss is "123456"
        groupChannelDTO.setName("TestActivity" + "123456");
        groupChannelDTO.setAppId(appId);
        groupChannelDTO.setScene(sceneEnum.getCode());
        SaveGroupDynamicCodeChannelResult expected = new SaveGroupDynamicCodeChannelResult(1, "Failure");
        when(groupDynamicCodeChannelLocalService.saveChannel(any(GroupDynamicCodeChannelDTO.class))).thenReturn(expected);
        // Use reflection to access private method
        Method method = GroupFissionActivityServiceImpl.class.getDeclaredMethod("saveDynamicCodeChannelResult", String.class, ActivityBaseInfoRequest.class, DynamicCodeChannelSceneEnum.class);
        method.setAccessible(true);
        // act
        SaveGroupDynamicCodeChannelResult result = (SaveGroupDynamicCodeChannelResult) method.invoke(service, appId, activityBaseInfo, sceneEnum);
        // assert
        assertNotNull(result);
        assertEquals(1, result.getResultCode());
        assertEquals("Failure", result.getMsg());
        assertNull(result.getGroupDynamicCodeChannelDTO());
    }

    /**
     * Test successful save of non-wecom group fission activity
     */
    @Test
    public void testSaveNonWeComGroupFissionActivitySuccess() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = buildValidRequest();
        SaveGroupDynamicCodeChannelResult channelResult = new SaveGroupDynamicCodeChannelResult(CommonOperationResultEnum.SUCCESS.getCode(), "success");
        channelResult.setGroupDynamicCodeChannelDTO(new GroupDynamicCodeChannelDTO());
        channelResult.getGroupDynamicCodeChannelDTO().setId(1L);
        doNothing().when(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), any(GroupFissionActivityRequest.class), eq(FissionChainOperationEnum.INSERT.getOperation()));
        when(groupDynamicCodeChannelLocalService.saveChannel(any())).thenReturn(channelResult);
        when(codeChannelConfigMapper.insertSelective(any())).thenReturn(1);
        when(groupFissionActivityMapper.insertSelective(any())).thenReturn(1);
        when(groupFissionPrizeService.saveGroupFissionPrize(any(), any())).thenReturn(true);
        when(groupFissionActivityDomainService.insertActivityAndPersonalWxGroupRelation(any(), any(), anyInt())).thenReturn(1);
        // act
        RemoteResponse<Boolean> response = invokePrivateMethod(request);
        // assert
        assertTrue(response.isSuccess());
        assertTrue(response.getData());
        verify(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), any(GroupFissionActivityRequest.class), eq(FissionChainOperationEnum.INSERT.getOperation()));
        verify(groupDynamicCodeChannelLocalService).saveChannel(any());
        verify(codeChannelConfigMapper).insertSelective(any());
        verify(groupFissionActivityMapper).insertSelective(any());
        verify(groupFissionPrizeService).saveGroupFissionPrize(any(), any());
        verify(groupFissionActivityDomainService).insertActivityAndPersonalWxGroupRelation(any(), any(), anyInt());
    }

    /**
     * Test validation failure scenario
     */
    @Test
    public void testSaveNonWeComGroupFissionActivityValidationFailure() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = buildValidRequest();
        doThrow(new FissionValidatorException("validation failed")).when(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), any(GroupFissionActivityRequest.class), eq(FissionChainOperationEnum.INSERT.getOperation()));
        // act & assert
        try {
            invokePrivateMethod(request);
            fail("Expected FissionValidatorException to be thrown");
        } catch (Exception e) {
            assertTrue(e.getCause() instanceof FissionValidatorException);
            assertEquals("validation failed", e.getCause().getMessage());
        }
        verify(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), any(GroupFissionActivityRequest.class), eq(FissionChainOperationEnum.INSERT.getOperation()));
        verifyNoInteractions(groupDynamicCodeChannelLocalService);
    }

    /**
     * Test channel creation failure scenario
     */
    @Test
    public void testSaveNonWeComGroupFissionActivityChannelCreationFailure() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = buildValidRequest();
        SaveGroupDynamicCodeChannelResult channelResult = new SaveGroupDynamicCodeChannelResult(CommonOperationResultEnum.ILLEGAL_PARAM.getCode(), "channel creation failed");
        doNothing().when(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), any(GroupFissionActivityRequest.class), eq(FissionChainOperationEnum.INSERT.getOperation()));
        when(groupDynamicCodeChannelLocalService.saveChannel(any())).thenReturn(channelResult);
        // act
        RemoteResponse<Boolean> response = invokePrivateMethod(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("channel creation failed", response.getMsg());
        verify(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), any(GroupFissionActivityRequest.class), eq(FissionChainOperationEnum.INSERT.getOperation()));
        verify(groupDynamicCodeChannelLocalService).saveChannel(any());
        verifyNoInteractions(codeChannelConfigMapper);
    }

    /**
     * Test poster save failure scenario
     */
    @Test
    public void testSaveNonWeComGroupFissionActivityPosterSaveFailure() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = buildValidRequest();
        SaveGroupDynamicCodeChannelResult channelResult = new SaveGroupDynamicCodeChannelResult(CommonOperationResultEnum.SUCCESS.getCode(), "success");
        channelResult.setGroupDynamicCodeChannelDTO(new GroupDynamicCodeChannelDTO());
        channelResult.getGroupDynamicCodeChannelDTO().setId(1L);
        doNothing().when(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), any(GroupFissionActivityRequest.class), eq(FissionChainOperationEnum.INSERT.getOperation()));
        when(groupDynamicCodeChannelLocalService.saveChannel(any())).thenReturn(channelResult);
        when(codeChannelConfigMapper.insertSelective(any())).thenReturn(0);
        // act
        RemoteResponse<Boolean> response = invokePrivateMethod(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("活动海报保存未成功, 请核对海报参数", response.getMsg());
        verify(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), any(GroupFissionActivityRequest.class), eq(FissionChainOperationEnum.INSERT.getOperation()));
        verify(groupDynamicCodeChannelLocalService).saveChannel(any());
        verify(codeChannelConfigMapper).insertSelective(any());
        verifyNoInteractions(groupFissionActivityMapper);
    }

    /**
     * Test activity save failure scenario
     */
    @Test
    public void testSaveNonWeComGroupFissionActivitySaveFailure() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = buildValidRequest();
        SaveGroupDynamicCodeChannelResult channelResult = new SaveGroupDynamicCodeChannelResult(CommonOperationResultEnum.SUCCESS.getCode(), "success");
        channelResult.setGroupDynamicCodeChannelDTO(new GroupDynamicCodeChannelDTO());
        channelResult.getGroupDynamicCodeChannelDTO().setId(1L);
        doNothing().when(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), any(GroupFissionActivityRequest.class), eq(FissionChainOperationEnum.INSERT.getOperation()));
        when(groupDynamicCodeChannelLocalService.saveChannel(any())).thenReturn(channelResult);
        when(codeChannelConfigMapper.insertSelective(any())).thenReturn(1);
        when(groupFissionActivityMapper.insertSelective(any())).thenReturn(0);
        // act
        RemoteResponse<Boolean> response = invokePrivateMethod(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("保存群裂变活动数据未成功, 请核对活动基本信息", response.getMsg());
        verify(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), any(GroupFissionActivityRequest.class), eq(FissionChainOperationEnum.INSERT.getOperation()));
        verify(groupDynamicCodeChannelLocalService).saveChannel(any());
        verify(codeChannelConfigMapper).insertSelective(any());
        verify(groupFissionActivityMapper).insertSelective(any());
        verifyNoInteractions(groupFissionPrizeService);
    }

    /**
     * Test prize save failure scenario
     */
    @Test
    public void testSaveNonWeComGroupFissionActivityPrizeSaveFailure() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = buildValidRequest();
        SaveGroupDynamicCodeChannelResult channelResult = new SaveGroupDynamicCodeChannelResult(CommonOperationResultEnum.SUCCESS.getCode(), "success");
        channelResult.setGroupDynamicCodeChannelDTO(new GroupDynamicCodeChannelDTO());
        channelResult.getGroupDynamicCodeChannelDTO().setId(1L);
        doNothing().when(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), any(GroupFissionActivityRequest.class), eq(FissionChainOperationEnum.INSERT.getOperation()));
        when(groupDynamicCodeChannelLocalService.saveChannel(any())).thenReturn(channelResult);
        when(codeChannelConfigMapper.insertSelective(any())).thenReturn(1);
        when(groupFissionActivityMapper.insertSelective(any())).thenReturn(1);
        when(groupFissionPrizeService.saveGroupFissionPrize(any(), any())).thenReturn(false);
        // act
        RemoteResponse<Boolean> response = invokePrivateMethod(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("保存活动奖品未成功, 请核对奖品信息", response.getMsg());
        verify(fissionChainContext).validate(eq(FissionChainMarkEnum.NON_WECOM_GROUP.getMark()), any(GroupFissionActivityRequest.class), eq(FissionChainOperationEnum.INSERT.getOperation()));
        verify(groupDynamicCodeChannelLocalService).saveChannel(any());
        verify(codeChannelConfigMapper).insertSelective(any());
        verify(groupFissionActivityMapper).insertSelective(any());
        verify(groupFissionPrizeService).saveGroupFissionPrize(any(), any());
        verifyNoInteractions(groupFissionActivityDomainService);
    }

    /**
     * Test normal case with valid inputs
     */
    @Test
    public void testSaveGroupDynamicCodeChannelResultNormalCase() throws Throwable {
        // arrange
        String appId = "testApp";
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        activityBaseInfo.setActivityName("Test Activity");
        SaveGroupDynamicCodeChannelResult expectedResult = new SaveGroupDynamicCodeChannelResult(0, "success");
        when(groupDynamicCodeChannelLocalService.saveChannel(any())).thenReturn(expectedResult);
        // act
        SaveGroupDynamicCodeChannelResult result = invokePrivateMethod(appId, activityBaseInfo);
        // assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
    }

    /**
     * Test case when appId is null
     */
    @Test
    public void testSaveGroupDynamicCodeChannelResultNullAppId() throws Throwable {
        // arrange
        String appId = null;
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        activityBaseInfo.setActivityName("Test Activity");
        SaveGroupDynamicCodeChannelResult expectedResult = new SaveGroupDynamicCodeChannelResult(0, "success");
        when(groupDynamicCodeChannelLocalService.saveChannel(any())).thenReturn(expectedResult);
        // act
        SaveGroupDynamicCodeChannelResult result = invokePrivateMethod(appId, activityBaseInfo);
        // assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
    }

    /**
     * Test case when activityBaseInfo is null
     */
    @Test
    public void testSaveGroupDynamicCodeChannelResultNullActivityBaseInfo() throws Throwable {
        // arrange
        String appId = "testApp";
        ActivityBaseInfoRequest activityBaseInfo = null;
        // act & assert
        assertThrows(Exception.class, () -> {
            invokePrivateMethod(appId, activityBaseInfo);
        });
    }

    /**
     * Test case when activity name is null
     */
    @Test
    public void testSaveGroupDynamicCodeChannelResultNullActivityName() throws Throwable {
        // arrange
        String appId = "testApp";
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        activityBaseInfo.setActivityName(null);
        SaveGroupDynamicCodeChannelResult expectedResult = new SaveGroupDynamicCodeChannelResult(0, "success");
        when(groupDynamicCodeChannelLocalService.saveChannel(any())).thenReturn(expectedResult);
        // act
        SaveGroupDynamicCodeChannelResult result = invokePrivateMethod(appId, activityBaseInfo);
        // assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
    }

    /**
     * Test case when activity name is empty
     */
    @Test
    public void testSaveGroupDynamicCodeChannelResultEmptyActivityName() throws Throwable {
        // arrange
        String appId = "testApp";
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        activityBaseInfo.setActivityName("");
        SaveGroupDynamicCodeChannelResult expectedResult = new SaveGroupDynamicCodeChannelResult(0, "success");
        when(groupDynamicCodeChannelLocalService.saveChannel(any())).thenReturn(expectedResult);
        // act
        SaveGroupDynamicCodeChannelResult result = invokePrivateMethod(appId, activityBaseInfo);
        // assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
    }

    /**
     * Test case with long activity name
     */
    @Test
    public void testSaveGroupDynamicCodeChannelResultLongActivityName() throws Throwable {
        // arrange
        String appId = "testApp";
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        StringBuilder longName = new StringBuilder();
        for (int i = 0; i < 1000; i++) {
            longName.append("A");
        }
        activityBaseInfo.setActivityName(longName.toString());
        SaveGroupDynamicCodeChannelResult expectedResult = new SaveGroupDynamicCodeChannelResult(0, "success");
        when(groupDynamicCodeChannelLocalService.saveChannel(any())).thenReturn(expectedResult);
        // act
        SaveGroupDynamicCodeChannelResult result = invokePrivateMethod(appId, activityBaseInfo);
        // assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
    }

    /**
     * Test case when service call fails
     */
    @Test
    public void testSaveGroupDynamicCodeChannelResultServiceFailure() throws Throwable {
        // arrange
        String appId = "testApp";
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        activityBaseInfo.setActivityName("Test Activity");
        SaveGroupDynamicCodeChannelResult expectedResult = new SaveGroupDynamicCodeChannelResult(1, "failed");
        when(groupDynamicCodeChannelLocalService.saveChannel(any())).thenReturn(expectedResult);
        // act
        SaveGroupDynamicCodeChannelResult result = invokePrivateMethod(appId, activityBaseInfo);
        // assert
        assertNotNull(result);
        assertEquals(expectedResult.getResultCode(), result.getResultCode());
        assertEquals(expectedResult.getMsg(), result.getMsg());
    }

    /**
     * Test case with special characters in activity name
     */
    @Test
    public void testSaveGroupDynamicCodeChannelResultSpecialCharsInName() throws Throwable {
        // arrange
        String appId = "testApp";
        ActivityBaseInfoRequest activityBaseInfo = new ActivityBaseInfoRequest();
        activityBaseInfo.setActivityName("Test @#$% Activity");
        SaveGroupDynamicCodeChannelResult expectedResult = new SaveGroupDynamicCodeChannelResult(0, "success");
        when(groupDynamicCodeChannelLocalService.saveChannel(any())).thenReturn(expectedResult);
        // act
        SaveGroupDynamicCodeChannelResult result = invokePrivateMethod(appId, activityBaseInfo);
        // assert
        assertNotNull(result);
        assertEquals(expectedResult, result);
    }

    /**
     * Test activity start case with new groups to add
     */
    @Test
    public void testUpdateGroupListActivityStartWithNewGroups() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(1L);
        List<PersonalGroupInfoDTO> groupInfos = Arrays.asList(new PersonalGroupInfoDTO("Group1", 101L, null), new PersonalGroupInfoDTO("Group2", 102L, null));
        request.setPersonalGroupInfo(groupInfos);
        List<Long> existingGroupIds = Collections.singletonList(101L);
        when(personalWxGroupInfoDomainService.queryActivityOrderedGroupIds(1L)).thenReturn(existingGroupIds);
        when(groupFissionActivityDomainService.insertActivityAndPersonalWxGroupRelation(eq(1L), eq(Collections.singletonList(102L)), eq(2))).thenReturn(1);
        // act
        boolean result = invokeUpdateGroupList(request, true);
        // assert
        assertTrue(result);
        verify(personalWxGroupInfoDomainService).queryActivityOrderedGroupIds(1L);
        verify(groupFissionActivityDomainService).insertActivityAndPersonalWxGroupRelation(1L, Collections.singletonList(102L), 2);
    }

    /**
     * Test activity start case with no new groups to add
     */
    @Test
    public void testUpdateGroupListActivityStartNoNewGroups() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(1L);
        List<PersonalGroupInfoDTO> groupInfos = Arrays.asList(new PersonalGroupInfoDTO("Group1", 101L, null));
        request.setPersonalGroupInfo(groupInfos);
        List<Long> existingGroupIds = Arrays.asList(101L, 102L);
        when(personalWxGroupInfoDomainService.queryActivityOrderedGroupIds(1L)).thenReturn(existingGroupIds);
        // The method will still try to insert empty list when no new groups
        when(groupFissionActivityDomainService.insertActivityAndPersonalWxGroupRelation(eq(1L), eq(Collections.emptyList()), eq(3))).thenReturn(0);
        // act
        boolean result = invokeUpdateGroupList(request, true);
        // assert
        assertTrue(result);
        verify(personalWxGroupInfoDomainService).queryActivityOrderedGroupIds(1L);
        verify(groupFissionActivityDomainService).insertActivityAndPersonalWxGroupRelation(1L, Collections.emptyList(), 3);
    }

    /**
     * Test activity not start case with groups to update
     */
    @Test
    public void testUpdateGroupListActivityNotStartWithGroups() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(1L);
        List<PersonalGroupInfoDTO> groupInfos = Arrays.asList(new PersonalGroupInfoDTO("Group1", 101L, null), new PersonalGroupInfoDTO("Group2", 102L, null));
        request.setPersonalGroupInfo(groupInfos);
        when(groupFissionActivityDomainService.insertActivityAndPersonalWxGroupRelation(eq(1L), eq(Arrays.asList(101L, 102L)), eq(1))).thenReturn(2);
        // act
        boolean result = invokeUpdateGroupList(request, false);
        // assert
        assertTrue(result);
        verify(groupFissionActivityDomainService).deleteActivityAndPersonalWxGroupRelation(1L);
        verify(groupFissionActivityDomainService).insertActivityAndPersonalWxGroupRelation(1L, Arrays.asList(101L, 102L), 1);
    }

    /**
     * Test activity not start case with empty group list
     */
    @Test
    public void testUpdateGroupListActivityNotStartEmptyGroups() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(1L);
        request.setPersonalGroupInfo(Collections.emptyList());
        when(groupFissionActivityDomainService.insertActivityAndPersonalWxGroupRelation(eq(1L), eq(Collections.emptyList()), eq(1))).thenReturn(0);
        // act
        boolean result = invokeUpdateGroupList(request, false);
        // assert
        assertTrue(result);
        verify(groupFissionActivityDomainService).deleteActivityAndPersonalWxGroupRelation(1L);
        verify(groupFissionActivityDomainService).insertActivityAndPersonalWxGroupRelation(1L, Collections.emptyList(), 1);
    }

    /**
     * Test null activity ID case
     */
    @Test
    public void testUpdateGroupListNullActivityId() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(null);
        request.setPersonalGroupInfo(Arrays.asList(new PersonalGroupInfoDTO("Group1", 101L, null)));
        // act
        boolean result = invokeUpdateGroupList(request, false);
        // assert
        assertFalse(result);
        verify(groupFissionActivityDomainService).deleteActivityAndPersonalWxGroupRelation(null);
        verifyNoInteractions(personalWxGroupInfoDomainService);
    }

    /**
     * Test null group list case - should throw NPE
     */
    @Test
    public void testUpdateGroupListNullGroupList() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(1L);
        request.setPersonalGroupInfo(null);
        // act & assert
        Exception exception = assertThrows(Exception.class, () -> invokeUpdateGroupList(request, false));
        assertTrue(exception.getCause() instanceof NullPointerException);
        // Verify no interactions since NPE occurs before any service calls
        verifyNoInteractions(groupFissionActivityDomainService);
        verifyNoInteractions(personalWxGroupInfoDomainService);
    }

    /**
     * Test database insertion failure case
     */
    @Test
    public void testUpdateGroupListInsertionFailure() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(1L);
        List<PersonalGroupInfoDTO> groupInfos = Arrays.asList(new PersonalGroupInfoDTO("Group1", 101L, null), new PersonalGroupInfoDTO("Group2", 102L, null));
        request.setPersonalGroupInfo(groupInfos);
        // Only 1 inserted out of 2
        // Only 1 inserted out of 2
        when(groupFissionActivityDomainService.insertActivityAndPersonalWxGroupRelation(eq(1L), eq(Arrays.asList(101L, 102L)), eq(1))).thenReturn(1);
        // act
        boolean result = invokeUpdateGroupList(request, false);
        // assert
        assertFalse(result);
        verify(groupFissionActivityDomainService).deleteActivityAndPersonalWxGroupRelation(1L);
        verify(groupFissionActivityDomainService).insertActivityAndPersonalWxGroupRelation(1L, Arrays.asList(101L, 102L), 1);
    }

    /**
     * Test null request scenario
     */
    @Test
    void testSaveGroupFissionActivityNullRequest() throws Throwable {
        // act
        RemoteResponse<Boolean> response = service.saveGroupFissionActivity(null);
        // assert
        assertFalse(response.isSuccess());
        assertNotNull(response.getMsg());
    }

    /**
     * Test validation failure scenario
     */
    @Test
    void testSaveGroupFissionActivityValidationFailure() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        doThrow(new FissionValidatorException("Validation failed")).when(fissionChainContext).validate(anyString(), any(), anyString());
        // act
        RemoteResponse<Boolean> response = service.saveGroupFissionActivity(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("Validation failed", response.getMsg());
        verify(fissionChainContext).validate(anyString(), any(), anyString());
    }

    /**
     * Test general exception scenario
     */
    @Test
    void testSaveGroupFissionActivityGeneralException() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        doThrow(new RuntimeException("Unexpected error")).when(fissionChainContext).validate(anyString(), any(), anyString());
        // act
        RemoteResponse<Boolean> response = service.saveGroupFissionActivity(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("创建活动出现异常", response.getMsg());
        verify(fissionChainContext).validate(anyString(), any(), anyString());
    }

    /**
     * Test invalid activity type scenario
     */
    @Test
    void testSaveGroupFissionActivityInvalidType() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        ActivityBaseInfoRequest baseInfo = new ActivityBaseInfoRequest();
        baseInfo.setActivityType((byte) 99);
        request.setActivityBaseInfo(baseInfo);
        doNothing().when(fissionChainContext).validate(anyString(), any(), anyString());
        // act
        RemoteResponse<Boolean> response = service.saveGroupFissionActivity(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("活动类型有误", response.getMsg());
    }

    /**
     * Test validation chain context returns normally but with invalid data
     */
    @Test
    void testSaveGroupFissionActivityValidationChainNormalWithInvalidData() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        doNothing().when(fissionChainContext).validate(anyString(), any(), anyString());
        // act
        RemoteResponse<Boolean> response = service.saveGroupFissionActivity(request);
        // assert
        assertFalse(response.isSuccess());
        verify(fissionChainContext).validate(anyString(), any(), anyString());
    }

    /**
     * Test validation chain context throws exception
     */
    @Test
    void testSaveGroupFissionActivityValidationChainException() throws Throwable {
        // arrange
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        doThrow(new IllegalArgumentException("Invalid argument")).when(fissionChainContext).validate(anyString(), any(), anyString());
        // act
        RemoteResponse<Boolean> response = service.saveGroupFissionActivity(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("创建活动出现异常", response.getMsg());
        verify(fissionChainContext).validate(anyString(), any(), anyString());
    }
}
