package com.sankuai.scrm.core.service.activity.fission.validation.validator;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.activity.fission.dto.activity.PersonalGroupInfoDTO;
import com.sankuai.dz.srcm.activity.fission.enums.StatusEnum;
import com.sankuai.dz.srcm.activity.fission.request.ActivityBaseInfoRequest;
import com.sankuai.dz.srcm.activity.fission.request.GroupFissionActivityRequest;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionActivity;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionActivityMapper;
import com.sankuai.scrm.core.service.activity.fission.domain.GroupFissionActivityDomainService;
import com.sankuai.scrm.core.service.activity.fission.validation.exception.FissionValidatorException;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.PersonalWxGroupInfoDomainService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import java.lang.reflect.Method;
import java.util.*;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class FissionNonWeComGroupValidatorValidateTest {

    @Mock
    private PersonalWxGroupInfoDomainService personalWxGroupInfoDomainService;

    @Mock
    private GroupFissionActivityMapper groupFissionActivityMapper;

    @Mock
    private GroupFissionActivityDomainService groupFissionActivityDomainService;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @InjectMocks
    private FissionNonWeComGroupValidator validator;

    private GroupFissionActivityRequest request;

    private GroupFissionActivity activity;

    @BeforeEach
    public void setUp() {
        request = createRequest();
        activity = createActivity();
    }

    private GroupFissionActivityRequest createRequest() {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setActivityId(1L);
        request.setAppId("testApp");
        ActivityBaseInfoRequest baseInfo = new ActivityBaseInfoRequest();
        // 1 hour later
        baseInfo.setStartTime(System.currentTimeMillis() + 3600000);
        // 2 hours later
        baseInfo.setEndTime(System.currentTimeMillis() + 7200000);
        request.setActivityBaseInfo(baseInfo);
        return request;
    }

    private GroupFissionActivity createActivity() {
        GroupFissionActivity activity = new GroupFissionActivity();
        activity.setStartTime(new Date(System.currentTimeMillis() + 3600000));
        activity.setEndTime(new Date(System.currentTimeMillis() + 7200000));
        activity.setStatus(StatusEnum.EFFECTIvE.getCode());
        return activity;
    }

    private void invokeCommonCheck(GroupFissionActivityRequest request) throws Exception {
        Method method = FissionNonWeComGroupValidator.class.getDeclaredMethod("commonCheck", GroupFissionActivityRequest.class);
        method.setAccessible(true);
        method.invoke(validator, request);
    }

    @Test
    public void testValidateEmptyGroupListThrowsException() throws Throwable {
        request.setPersonalGroupInfo(Collections.emptyList());
        assertThrows(FissionValidatorException.class, () -> validator.validate(request, "insert"), "Should throw exception for empty group list");
    }

    @Test
    public void testValidateInvalidGroupIdsThrowsException() throws Throwable {
        PersonalGroupInfoDTO group = new PersonalGroupInfoDTO();
        group.setDsGroupId(1L);
        request.setPersonalGroupInfo(Collections.singletonList(group));
        when(corpAppConfigRepository.getCorpIdByAppId("testApp")).thenReturn("corp1");
        when(personalWxGroupInfoDomainService.queryGroupsNotExist("corp1", Collections.singletonList(1L))).thenReturn(Collections.singletonList(1L));
        assertThrows(FissionValidatorException.class, () -> validator.validate(request, "insert"), "Should throw exception for invalid group IDs");
    }

    @Test
    public void testValidateUpdateAfterStartTimeRestrictsChanges() throws Throwable {
        // 3 minutes ago
        activity.setStartTime(new Date(System.currentTimeMillis() - 180000));
        when(groupFissionActivityMapper.selectByPrimaryKey(1L)).thenReturn(activity);
        List<Long> existingGroups = Arrays.asList(1L, 2L);
        when(personalWxGroupInfoDomainService.queryActivityOrderedGroupIds(1L)).thenReturn(existingGroups);
        PersonalGroupInfoDTO group1 = new PersonalGroupInfoDTO();
        group1.setDsGroupId(1L);
        PersonalGroupInfoDTO group3 = new PersonalGroupInfoDTO();
        group3.setDsGroupId(3L);
        request.setPersonalGroupInfo(Arrays.asList(group1, group3));
        when(corpAppConfigRepository.getCorpIdByAppId("testApp")).thenReturn("corp1");
        when(personalWxGroupInfoDomainService.queryGroupsNotExist("corp1", Arrays.asList(1L, 3L))).thenReturn(Collections.emptyList());
        assertThrows(FissionValidatorException.class, () -> validator.validate(request, "update"), "Should throw exception for group changes after start time");
    }

    @Test
    public void testValidateInsertWithValidGroups() throws Throwable {
        PersonalGroupInfoDTO group1 = new PersonalGroupInfoDTO();
        group1.setDsGroupId(1L);
        request.setPersonalGroupInfo(Collections.singletonList(group1));
        when(corpAppConfigRepository.getCorpIdByAppId("testApp")).thenReturn("corp1");
        when(personalWxGroupInfoDomainService.queryGroupsNotExist("corp1", Collections.singletonList(1L))).thenReturn(Collections.emptyList());
        assertDoesNotThrow(() -> validator.validate(request, "insert"));
    }

    @Test
    public void testValidateUpdateWithOverlappingActivitiesForNewGroups() throws Throwable {
        when(groupFissionActivityMapper.selectByPrimaryKey(1L)).thenReturn(activity);
        List<Long> existingGroups = Collections.singletonList(1L);
        when(personalWxGroupInfoDomainService.queryActivityOrderedGroupIds(1L)).thenReturn(existingGroups);
        PersonalGroupInfoDTO group1 = new PersonalGroupInfoDTO();
        group1.setDsGroupId(1L);
        PersonalGroupInfoDTO group2 = new PersonalGroupInfoDTO();
        group2.setDsGroupId(2L);
        request.setPersonalGroupInfo(Arrays.asList(group1, group2));
        when(corpAppConfigRepository.getCorpIdByAppId("testApp")).thenReturn("corp1");
        when(personalWxGroupInfoDomainService.queryGroupsNotExist("corp1", Arrays.asList(1L, 2L))).thenReturn(Collections.emptyList());

        Map<Long, Set<Long>> newGroupActivities = new HashMap<>();
        newGroupActivities.put(2L, Collections.singleton(3L));
        when(personalWxGroupInfoDomainService.queryGroupActivitiesMap(Collections.singletonList(2L))).thenReturn(newGroupActivities);
        GroupFissionActivity otherActivity = new GroupFissionActivity();
        otherActivity.setId(3L);
        otherActivity.setStartTime(new Date(request.getActivityBaseInfo().getStartTime() - 1800000));
        otherActivity.setEndTime(new Date(request.getActivityBaseInfo().getEndTime() - 900000));
        otherActivity.setStatus(StatusEnum.EFFECTIvE.getCode());
        when(groupFissionActivityDomainService.queryByActivityIds(Collections.singletonList(3L))).thenReturn(Collections.singletonList(otherActivity));
        assertThrows(FissionValidatorException.class, () -> validator.validate(request, "update"), "Should throw exception for overlapping activities");
    }

    @Test
    public void testValidateInsertWithOverlappingActivities() throws Throwable {
        PersonalGroupInfoDTO group1 = new PersonalGroupInfoDTO();
        group1.setDsGroupId(1L);
        request.setPersonalGroupInfo(Collections.singletonList(group1));
        when(corpAppConfigRepository.getCorpIdByAppId("testApp")).thenReturn("corp1");
        when(personalWxGroupInfoDomainService.queryGroupsNotExist("corp1", Collections.singletonList(1L))).thenReturn(Collections.emptyList());
        Map<Long, Set<Long>> groupActivities = new HashMap<>();
        groupActivities.put(1L, Collections.singleton(2L));
        when(personalWxGroupInfoDomainService.queryGroupActivitiesMap(Collections.singletonList(1L))).thenReturn(groupActivities);
        GroupFissionActivity otherActivity = new GroupFissionActivity();
        otherActivity.setId(2L);
        otherActivity.setStartTime(new Date(request.getActivityBaseInfo().getStartTime() - 1800000));
        otherActivity.setEndTime(new Date(request.getActivityBaseInfo().getEndTime() - 900000));
        otherActivity.setStatus(StatusEnum.EFFECTIvE.getCode());
        when(groupFissionActivityDomainService.queryByActivityIds(Collections.singletonList(2L))).thenReturn(Collections.singletonList(otherActivity));
        assertThrows(FissionValidatorException.class, () -> validator.validate(request, "insert"), "Should throw exception for overlapping activities");
    }

    @Test
    public void testValidateUpdateWithUnchangedTimeSkipsExistingGroupValidation() throws Throwable {
        when(groupFissionActivityMapper.selectByPrimaryKey(1L)).thenReturn(activity);
        List<Long> existingGroups = Arrays.asList(1L, 2L);
        when(personalWxGroupInfoDomainService.queryActivityOrderedGroupIds(1L)).thenReturn(existingGroups);
        request.getActivityBaseInfo().setStartTime(activity.getStartTime().getTime());
        request.getActivityBaseInfo().setEndTime(activity.getEndTime().getTime());
        PersonalGroupInfoDTO group1 = new PersonalGroupInfoDTO();
        group1.setDsGroupId(1L);
        PersonalGroupInfoDTO group2 = new PersonalGroupInfoDTO();
        group2.setDsGroupId(2L);
        request.setPersonalGroupInfo(Arrays.asList(group1, group2));
        when(corpAppConfigRepository.getCorpIdByAppId("testApp")).thenReturn("corp1");
        when(personalWxGroupInfoDomainService.queryGroupsNotExist("corp1", Arrays.asList(1L, 2L))).thenReturn(Collections.emptyList());
        validator.validate(request, "update");
        verify(personalWxGroupInfoDomainService, never()).queryGroupActivitiesMap(existingGroups);
    }

    @Test
    public void testValidateUpdateBeforeStartTimeAllowsChanges() throws Throwable {
        // 3 minutes later
        activity.setStartTime(new Date(System.currentTimeMillis() + 180000));
        when(groupFissionActivityMapper.selectByPrimaryKey(1L)).thenReturn(activity);
        List<Long> existingGroups = Arrays.asList(1L, 2L);
        when(personalWxGroupInfoDomainService.queryActivityOrderedGroupIds(1L)).thenReturn(existingGroups);
        PersonalGroupInfoDTO group1 = new PersonalGroupInfoDTO();
        group1.setDsGroupId(1L);
        PersonalGroupInfoDTO group2 = new PersonalGroupInfoDTO();
        group2.setDsGroupId(2L);
        PersonalGroupInfoDTO group3 = new PersonalGroupInfoDTO();
        group3.setDsGroupId(3L);
        request.setPersonalGroupInfo(Arrays.asList(group1, group2, group3));
        when(corpAppConfigRepository.getCorpIdByAppId("testApp")).thenReturn("corp1");
        when(personalWxGroupInfoDomainService.queryGroupsNotExist("corp1", Arrays.asList(1L, 2L, 3L))).thenReturn(Collections.emptyList());
        // Stub for existing groups check (called with [1, 2])
        Map<Long, Set<Long>> existingGroupActivities = new HashMap<>();
        existingGroupActivities.put(1L, Collections.emptySet());
        existingGroupActivities.put(2L, Collections.emptySet());
        when(personalWxGroupInfoDomainService.queryGroupActivitiesMap(Arrays.asList(1L, 2L))).thenReturn(existingGroupActivities);
        // Stub for new groups check (called with [3])
        Map<Long, Set<Long>> newGroupActivities = new HashMap<>();
        newGroupActivities.put(3L, Collections.emptySet());
        when(personalWxGroupInfoDomainService.queryGroupActivitiesMap(Collections.singletonList(3L))).thenReturn(newGroupActivities);
        assertDoesNotThrow(() -> validator.validate(request, "update"));
    }

    @Test
    public void testCommonCheckEmptyGroupInfo() throws Throwable {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setPersonalGroupInfo(Collections.emptyList());
        request.setAppId("testAppId");
        Exception exception = assertThrows(Exception.class, () -> invokeCommonCheck(request));
        assertTrue(exception.getCause() instanceof FissionValidatorException);
        assertEquals("活动群不能为空", exception.getCause().getMessage());
    }

    @Test
    public void testCommonCheckNullDsGroupId() throws Throwable {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        PersonalGroupInfoDTO dto1 = new PersonalGroupInfoDTO();
        dto1.setDsGroupId(1L);
        PersonalGroupInfoDTO dto2 = new PersonalGroupInfoDTO();
        dto2.setDsGroupId(null);
        request.setPersonalGroupInfo(Arrays.asList(dto1, dto2));
        request.setAppId("testAppId");
        Exception exception = assertThrows(Exception.class, () -> invokeCommonCheck(request));
        assertTrue(exception.getCause() instanceof FissionValidatorException);
        assertEquals("活动群配置有误", exception.getCause().getMessage());
    }

    @Test
    public void testCommonCheckAllGroupsExist() throws Throwable {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        PersonalGroupInfoDTO dto1 = new PersonalGroupInfoDTO();
        dto1.setDsGroupId(1L);
        PersonalGroupInfoDTO dto2 = new PersonalGroupInfoDTO();
        dto2.setDsGroupId(2L);
        request.setPersonalGroupInfo(Arrays.asList(dto1, dto2));
        request.setAppId("testAppId");
        when(corpAppConfigRepository.getCorpIdByAppId("testAppId")).thenReturn("testCorpId");
        when(personalWxGroupInfoDomainService.queryGroupsNotExist("testCorpId", Arrays.asList(1L, 2L))).thenReturn(Collections.emptyList());
        invokeCommonCheck(request);
        verify(corpAppConfigRepository).getCorpIdByAppId("testAppId");
        verify(personalWxGroupInfoDomainService).queryGroupsNotExist("testCorpId", Arrays.asList(1L, 2L));
    }

    @Test
    public void testCommonCheckSomeGroupsNotExist() throws Throwable {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        PersonalGroupInfoDTO dto1 = new PersonalGroupInfoDTO();
        dto1.setDsGroupId(1L);
        PersonalGroupInfoDTO dto2 = new PersonalGroupInfoDTO();
        dto2.setDsGroupId(2L);
        request.setPersonalGroupInfo(Arrays.asList(dto1, dto2));
        request.setAppId("testAppId");
        when(corpAppConfigRepository.getCorpIdByAppId("testAppId")).thenReturn("testCorpId");
        when(personalWxGroupInfoDomainService.queryGroupsNotExist("testCorpId", Arrays.asList(1L, 2L))).thenReturn(Collections.singletonList(2L));
        Exception exception = assertThrows(Exception.class, () -> invokeCommonCheck(request));
        assertTrue(exception.getCause() instanceof FissionValidatorException);
        assertEquals("找不到群：2", exception.getCause().getMessage());
    }

    @Test
    public void testCommonCheckCorpIdLookupFails() throws Throwable {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        PersonalGroupInfoDTO dto1 = new PersonalGroupInfoDTO();
        dto1.setDsGroupId(1L);
        request.setPersonalGroupInfo(Collections.singletonList(dto1));
        request.setAppId("testAppId");
        when(corpAppConfigRepository.getCorpIdByAppId("testAppId")).thenReturn(null);
        // Mock the groups not exist query to return the group ID since corpId is null
        when(personalWxGroupInfoDomainService.queryGroupsNotExist(null, Collections.singletonList(1L))).thenReturn(Collections.singletonList(1L));
        Exception exception = assertThrows(Exception.class, () -> invokeCommonCheck(request));
        assertTrue(exception.getCause() instanceof FissionValidatorException);
        assertEquals("找不到群：1", exception.getCause().getMessage());
    }

    @Test
    public void testCommonCheckNullAppId() throws Throwable {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        PersonalGroupInfoDTO dto1 = new PersonalGroupInfoDTO();
        dto1.setDsGroupId(1L);
        request.setPersonalGroupInfo(Collections.singletonList(dto1));
        request.setAppId(null);
        // Mock the groups not exist query to return the group ID since appId is null
        when(personalWxGroupInfoDomainService.queryGroupsNotExist(null, Collections.singletonList(1L))).thenReturn(Collections.singletonList(1L));
        Exception exception = assertThrows(Exception.class, () -> invokeCommonCheck(request));
        assertTrue(exception.getCause() instanceof FissionValidatorException);
        assertEquals("找不到群：1", exception.getCause().getMessage());
    }
}
