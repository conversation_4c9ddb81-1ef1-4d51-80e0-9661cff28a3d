package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.dianping.cat.message.Transaction;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteWriteDomainService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ScrmRefinementOperationBackEndServiceImplAutoUpdatePersonaCrowdPackTest {

    @Mock
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock
    private Transaction transaction;

    private MockedStatic<Cat> mockedCat;

    @InjectMocks
    private ScrmRefinementOperationBackEndServiceImpl scrmRefinementOperationBackEndService;

    @Before
    public void setUp() {
        mockedCat = mockStatic(Cat.class);
        mockedCat.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(transaction);
    }

    @After
    public void tearDown() {
        if (mockedCat != null) {
            mockedCat.close();
        }
    }

    /**
     * Test successful execution of autoUpdatePersonaCrowdPack
     */
    @Test
    public void testAutoUpdatePersonaCrowdPack_Success() throws Throwable {
        // arrange
        doNothing().when(executeWriteDomainService).updatePersonaCrowdPackMainTask();
        doNothing().when(transaction).setStatus(Transaction.SUCCESS);
        doNothing().when(transaction).complete();
        // act
        scrmRefinementOperationBackEndService.autoUpdatePersonaCrowdPack();
        // assert
        verify(executeWriteDomainService, times(1)).updatePersonaCrowdPackMainTask();
        verify(transaction, times(1)).setStatus(Transaction.SUCCESS);
        verify(transaction, times(1)).complete();
        mockedCat.verify(() -> Cat.newTransaction("ScrmRefinementOperationBackEndService", "autoUpdatePersonaCrowdPack"), times(1));
    }

    /**
     * Test execution when updatePersonaCrowdPackMainTask throws exception
     */
    @Test
    public void testAutoUpdatePersonaCrowdPack_WhenExceptionOccurs() throws Throwable {
        // arrange
        RuntimeException expectedException = new RuntimeException("Test exception");
        doThrow(expectedException).when(executeWriteDomainService).updatePersonaCrowdPackMainTask();
        doNothing().when(transaction).setStatus(any(Throwable.class));
        doNothing().when(transaction).complete();
        // act
        scrmRefinementOperationBackEndService.autoUpdatePersonaCrowdPack();
        // assert
        verify(executeWriteDomainService, times(1)).updatePersonaCrowdPackMainTask();
        verify(transaction, times(1)).setStatus(expectedException);
        verify(transaction, times(1)).complete();
        mockedCat.verify(() -> Cat.newTransaction("ScrmRefinementOperationBackEndService", "autoUpdatePersonaCrowdPack"), times(1));
    }

    /**
     * Test when transaction.complete() throws exception - should not affect method execution
     * Note: Since the original implementation doesn't handle this exception, we expect it to propagate
     */
    @Test(expected = RuntimeException.class)
    public void testAutoUpdatePersonaCrowdPack_WhenTransactionCompleteThrowsException() throws Throwable {
        // arrange
        doNothing().when(executeWriteDomainService).updatePersonaCrowdPackMainTask();
        doNothing().when(transaction).setStatus(Transaction.SUCCESS);
        doThrow(new RuntimeException("Complete exception")).when(transaction).complete();
        // act - should throw exception
        scrmRefinementOperationBackEndService.autoUpdatePersonaCrowdPack();
        // assert - should not reach here
        verify(executeWriteDomainService, times(1)).updatePersonaCrowdPackMainTask();
        verify(transaction, times(1)).setStatus(Transaction.SUCCESS);
        verify(transaction, times(1)).complete();
    }

    /**
     * Test when Cat.newTransaction returns null
     * Note: Since the original implementation assumes transaction is not null,
     * we expect NPE to be thrown
     */
    @Test(expected = NullPointerException.class)
    public void testAutoUpdatePersonaCrowdPack_WhenTransactionIsNull() throws Throwable {
        // arrange
        mockedCat.when(() -> Cat.newTransaction(anyString(), anyString())).thenReturn(null);
        // act - should throw NPE
        scrmRefinementOperationBackEndService.autoUpdatePersonaCrowdPack();
        // assert - should not reach here
        verify(executeWriteDomainService, never()).updatePersonaCrowdPackMainTask();
    }
}
