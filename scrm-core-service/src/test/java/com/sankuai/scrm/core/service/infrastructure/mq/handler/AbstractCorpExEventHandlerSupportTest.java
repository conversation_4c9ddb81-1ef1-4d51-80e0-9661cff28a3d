package com.sankuai.scrm.core.service.infrastructure.mq.handler;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractCorpExEventHandlerSupportTest {

    @Mock
    private AbstractCorpExEventHandler handler;

    @Test
    public void testSupportDataMapIsNull() throws Throwable {
        doCallRealMethod().when(handler).support(any());
        assertFalse(handler.support(null));
    }

    @Test
    public void testSupportEventOrChangeTypeNotExist() throws Throwable {
        Map<String, String> dataMap = new HashMap<>();
        doCallRealMethod().when(handler).support(any());
        assertFalse(handler.support(dataMap));
    }

    @Test
    public void testSupportEventOrChangeTypeListIsEmpty() throws Throwable {
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("Event", "testEvent");
        dataMap.put("ChangeType", "testChangeType");
        when(handler.getSupportEvent()).thenReturn(Arrays.asList());
        doCallRealMethod().when(handler).support(any());
        assertFalse(handler.support(dataMap));
    }

    @Test
    public void testSupportEventOrChangeTypeListNotContainsValue() throws Throwable {
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("Event", "testEvent");
        dataMap.put("ChangeType", "testChangeType");
        when(handler.getSupportEvent()).thenReturn(Arrays.asList("otherEvent"));
        doCallRealMethod().when(handler).support(any());
        assertFalse(handler.support(dataMap));
    }

    @Test
    public void testSupportEventOrChangeTypeListContainsValue() throws Throwable {
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("Event", "testEvent");
        dataMap.put("ChangeType", "testChangeType");
        when(handler.getSupportEvent()).thenReturn(Arrays.asList("testEvent"));
        when(handler.getSupportChangeType()).thenReturn(Arrays.asList("testChangeType"));
        doCallRealMethod().when(handler).support(any());
        assertTrue(handler.support(dataMap));
    }
}
