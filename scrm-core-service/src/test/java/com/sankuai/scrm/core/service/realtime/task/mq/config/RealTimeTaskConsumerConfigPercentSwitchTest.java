package com.sankuai.scrm.core.service.realtime.task.mq.config;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.realtime.task.domainservice.ScrmGrowthUserInfoDomainService;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

class RealTimeTaskConsumerConfigPercentSwitchTest {

    @InjectMocks
    private RealTimeTaskConsumerConfig realTimeTaskConsumerConfig;

    @Mock
    private ScrmGrowthUserInfoDomainService scrmGrowthUserInfoDomainService;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试当percentNums为空列表时返回false
     */
    @Test
    void testPercentSwitchWhenPercentNumsIsEmpty() throws Throwable {
        // arrange
        Long userId = 123L;
        List<Long> percentNums = Collections.emptyList();
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(userId, percentNums);
        // assert
        assertFalse(result);
    }

    /**
     * 测试当percentNums只有一个元素时返回false
     */
    @Test
    void testPercentSwitchWhenPercentNumsHasSingleElement() throws Throwable {
        // arrange
        Long userId = 456L;
        List<Long> percentNums = Collections.singletonList(10L);
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(userId, percentNums);
        // assert
        assertFalse(result);
    }

    /**
     * 测试正常情况(min < max)且用户ID在范围内时返回true
     */
    @Test
    void testPercentSwitchWhenUserInRange() throws Throwable {
        // arrange
        // 123 % 100 = 23
        Long userId = 123L;
        List<Long> percentNums = Arrays.asList(20L, 30L);
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(userId, percentNums);
        // assert
        assertTrue(result);
    }

    /**
     * 测试当max < min时会自动交换值且用户ID在范围内时返回true
     */
    @Test
    void testPercentSwitchWhenMaxLessThanMinAndUserInRange() throws Throwable {
        // arrange
        // 123 % 100 = 23
        Long userId = 123L;
        // 会自动交换为20-30
        List<Long> percentNums = Arrays.asList(30L, 20L);
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(userId, percentNums);
        // assert
        assertTrue(result);
    }

    /**
     * 测试边界情况(userMod等于minValue)应返回false
     */
    @Test
    void testPercentSwitchWhenUserModEqualsMinValue() throws Throwable {
        // arrange
        // 120 % 100 = 20
        Long userId = 120L;
        List<Long> percentNums = Arrays.asList(20L, 30L);
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(userId, percentNums);
        // assert
        assertTrue(result);
    }

    /**
     * 测试边界情况(userMod等于maxValue)应返回true
     */
    @Test
    void testPercentSwitchWhenUserModEqualsMaxValue() throws Throwable {
        // arrange
        // 130 % 100 = 30
        Long userId = 130L;
        List<Long> percentNums = Arrays.asList(20L, 30L);
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(userId, percentNums);
        // assert
        assertFalse(result);
    }

    /**
     * 测试用户ID为null时应返回false
     * 注意：由于原方法在userId为null时会抛出NullPointerException，
     * 我们需要在测试中预期这个异常，而不是期望返回false
     */
    @Test
    void testPercentSwitchWhenUserIdIsNull() throws Throwable {
        // arrange
        Long userId = null;
        List<Long> percentNums = Arrays.asList(20L, 30L);
        // act & assert
        // 当userId为null时，原方法会在执行userId % 100时抛出NullPointerException
        assertThrows(NullPointerException.class, () -> {
            realTimeTaskConsumerConfig.percentSwitch(userId, percentNums);
        });
    }

    /**
     * 测试percentNums为null时应返回false
     */
    @Test
    void testPercentSwitchWhenPercentNumsIsNull() throws Throwable {
        // arrange
        Long userId = 123L;
        List<Long> percentNums = null;
        // act
        boolean result = realTimeTaskConsumerConfig.percentSwitch(userId, percentNums);
        // assert
        assertFalse(result);
    }
}
