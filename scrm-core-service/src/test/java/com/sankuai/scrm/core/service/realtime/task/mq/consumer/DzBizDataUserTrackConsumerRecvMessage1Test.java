package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.aigc.service.config.AISceneABTestConfig;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.realtime.task.dto.ScrmDzBizDataUserTrackDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineABTestRecordMessageProducer;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineUserFootprintDiversionProducer;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class DzBizDataUserTrackConsumerRecvMessage1Test {

    @Mock
    private RealTimeTaskConsumerConfig consumerConfig;

    @Mock
    private GroupRetailAiEngineUserFootprintDiversionProducer userFootprintDiversionProducer;

    @Mock
    private AISceneABTestConfig aiSceneABTestConfig;

    @Mock
    private GroupRetailAiEngineABTestRecordMessageProducer abTestRecordMessageProducer;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @InjectMocks
    private DzBizDataUserTrackConsumer dzBizDataUserTrackConsumer;

    private ConsumeStatus invokeRecvMessage(MafkaMessage<String> message, MessagetContext context) throws Exception {
        Method method = DzBizDataUserTrackConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        method.setAccessible(true);
        return (ConsumeStatus) method.invoke(dzBizDataUserTrackConsumer, message, context);
    }

    /**
     * Test when message body is null
     */
    @Test
    public void testRecvMessageNullBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(null);
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(message).getBody();
    }

    /**
     * Test when userid list is empty
     */
    @Test
    public void testRecvMessageEmptyUserId() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        ScrmDzBizDataUserTrackDTO dto = new ScrmDzBizDataUserTrackDTO();
        dto.setUserid(Collections.emptyList());
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test when JSON parsing fails
     */
    @Test
    public void testRecvMessageJsonParseError() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("invalid json");
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }
}
