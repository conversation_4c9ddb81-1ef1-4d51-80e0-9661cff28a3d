package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationActionTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationNodeTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.CouponDistributionAction;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.util.*;

import static org.junit.jupiter.api.Assertions.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class ExecuteWriteDomainServiceTest {
    @Mock
    private CouponDistributionAction couponDistributionAction;
    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;
    @Spy
    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    // 后续补充详细测试用例

    @Test
    public void testProcessNodes_AIScene_CouponDistribution_Success() {
        // 构造mock参数
        ScrmProcessOrchestrationDTO processOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        ExecuteManagementDTO executeManagementDTO = mock(ExecuteManagementDTO.class);
        ScrmProcessOrchestrationNodeDTO nodeDTO = mock(ScrmProcessOrchestrationNodeDTO.class);
        ScrmProcessOrchestrationNodeMediumDTO mediumDTO = mock(ScrmProcessOrchestrationNodeMediumDTO.class);
        ScrmProcessOrchestrationActionDTO actionDTO = mock(ScrmProcessOrchestrationActionDTO.class);
        ScrmCrowdPackDetailInfoDTO crowdPackDetailInfoDTO = mock(ScrmCrowdPackDetailInfoDTO.class);
        StepExecuteResultDTO stepResult = mock(StepExecuteResultDTO.class);
        
        List<Long> processingNodes = Collections.singletonList(1L);
        Map<Long, ScrmProcessOrchestrationNodeDTO> nodeDTOMap = new HashMap<>();
        nodeDTOMap.put(1L, nodeDTO);
        List<Long> childrenNodes = Arrays.asList(2L, 3L);
        
        // 设置mock对象的行为
        when(nodeDTO.getChildrenNodes()).thenReturn(childrenNodes);
        when(nodeDTO.getNodeId()).thenReturn(1L);
        when(nodeDTO.getNodeType()).thenReturn(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_AUTO_ACTION.getValue());
        when(processOrchestrationDTO.getNodeMediumDTO()).thenReturn(mediumDTO);
        when(mediumDTO.getActionMap()).thenReturn(Collections.singletonMap(1L, actionDTO));
        when(actionDTO.getActionType()).thenReturn(ScrmProcessOrchestrationActionTypeEnum.COUPON_DISTRIBUTION.getValue().byteValue());
        when(processOrchestrationDTO.isAiScene()).thenReturn(true);
        when(couponDistributionAction.dealCouponDistributionAction(any(), any(), any(), any(), any())).thenReturn(stepResult);
        when(stepResult.isSuccess()).thenReturn(true);
        when(stepResult.getExecuteLogId()).thenReturn(123L);
        
        ScrmAmProcessOrchestrationExecuteLogDO logDO = mock(ScrmAmProcessOrchestrationExecuteLogDO.class);
        when(executeLogDOMapper.selectByPrimaryKey(123L)).thenReturn(logDO);
        
        // 递归调用processNodes时直接返回stepResult
        doReturn(stepResult).when(executeWriteDomainService).processNodes(any(ScrmProcessOrchestrationDTO.class), any(ExecuteManagementDTO.class), anyMap(), eq(childrenNodes), any(ScrmCrowdPackDetailInfoDTO.class), any(ScrmAmProcessOrchestrationExecuteLogDO.class));
        // 执行
        StepExecuteResultDTO result = executeWriteDomainService.processNodes(
                processOrchestrationDTO, executeManagementDTO, nodeDTOMap, processingNodes, crowdPackDetailInfoDTO, null);

        // 验证
        assertNotNull(result);
        // verify(executeManagementDTO).setCurrentActionType(ScrmProcessOrchestrationActionTypeEnum.COUPON_DISTRIBUTION);
        verify(couponDistributionAction).dealCouponDistributionAction(any(), any(), any(), any(), any());
        verify(executeLogDOMapper).selectByPrimaryKey(123L);
    }
}
