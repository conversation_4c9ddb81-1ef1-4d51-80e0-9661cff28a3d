package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ExecuteManagementDTOTest {

    @Mock(lenient = true)
    private ExecuteManagementDTO executeManagementDTO;

    @Test
    public void testGetLessCountExecutorWhenStaffIdListIsEmpty() throws Throwable {
        String result = executeManagementDTO.getLessCountExecutor(Collections.emptyList());
        assertNull(result);
    }

    @Test
    public void testGetLessCountExecutorWhenStaffTaskCountsIsEmpty() throws Throwable {
        String result = executeManagementDTO.getLessCountExecutor(Arrays.asList("staff1", "staff2"));
        assertNull(result);
    }

    @Test
    public void testGetLessCountExecutorWhenStaffIdNotInStaffTaskCounts() throws Throwable {
        String result = executeManagementDTO.getLessCountExecutor(Arrays.asList("staff1", "staff2"));
        assertNull(result);
    }

    @Test
    public void testGetLessCountExecutorWhenStaffIdInStaffTaskCountsAndCountIsZero() throws Throwable {
        String result = executeManagementDTO.getLessCountExecutor(Arrays.asList("staff1", "staff2"));
        assertNull(result);
    }

    @Test
    public void testGetLessCountExecutorWhenStaffIdInStaffTaskCountsAndCountLessThanChosenStaff() throws Throwable {
        String result = executeManagementDTO.getLessCountExecutor(Arrays.asList("staff1", "staff2"));
        assertNull(result);
    }

    @Test
    public void testGetLessCountExecutorWhenStaffIdInStaffTaskCountsAndCountEqualsChosenStaff() throws Throwable {
        String result = executeManagementDTO.getLessCountExecutor(Arrays.asList("staff1", "staff2"));
        assertNull(result);
    }

    @Test
    public void testGetLessCountExecutorWhenStaffIdInStaffTaskCountsAndCountGreaterThanChosenStaff() throws Throwable {
        String result = executeManagementDTO.getLessCountExecutor(Arrays.asList("staff1", "staff2"));
        assertNull(result);
    }

    @Test
    public void testGetLessCountExecutorWhenStaffIdInStaffTaskCountsAndCountEqualsZero() throws Throwable {
        String result = executeManagementDTO.getLessCountExecutor(Arrays.asList("staff1", "staff2"));
        assertNull(result);
    }

    @Test
    public void testGetLessCountExecutorWhenStaffIdInStaffTaskCountsAndCountGreaterThanZero() throws Throwable {
        String result = executeManagementDTO.getLessCountExecutor(Arrays.asList("staff1", "staff2"));
        assertNull(result);
    }
}
