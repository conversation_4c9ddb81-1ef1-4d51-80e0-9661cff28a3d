package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.pigeon.threadpool.ResizableLinkedBlockingQueue;
import com.dianping.rhino.Rhino;
import com.dianping.rhino.threadpool.DefaultThreadPoolProperties;
import com.dianping.rhino.threadpool.ThreadPool;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGroupSendMessageResponse;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import org.junit.jupiter.api.AfterAll;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertNotNull;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.*;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import java.lang.reflect.Field;
import java.util.HashMap;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;
import org.mockito.InjectMocks;
import org.mockito.Mock;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerDealWxGroupSendSuccessTest {

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Captor
    private ArgumentCaptor<ScrmAmProcessOrchestrationWxInvokeLogDO> logCaptor;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO;

    private WxGroupSendMessageResponse response;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS;

    private static final byte WAIT_FOR_SEND_STATUS = 5;

    @BeforeEach
    void setUp() {
        // Initialize processOrchestrationDTO
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        // Initialize wxInvokeLogDO
        wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        wxInvokeLogDO.setProcessOrchestrationId(1L);
        wxInvokeLogDO.setProcessOrchestrationVersion("1.0");
        wxInvokeLogDO.setProcessOrchestrationNodeId(1L);
        wxInvokeLogDO.setExecutorId("test_executor");
        wxInvokeLogDO.setUpdateTime(new Date());
        // Initialize response
        response = new WxGroupSendMessageResponse();
        response.setMsgId("test_msg_id");
        response.setErrCode(0);
        response.setErrMsg("success");
        // Initialize wxInvokeDetailDOS
        wxInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setId(1L);
        detailDO.setProcessOrchestrationId(1L);
        detailDO.setProcessOrchestrationVersion("1.0");
        detailDO.setProcessOrchestrationNodeId(1L);
        detailDO.setExecuteLogId(1L);
        detailDO.setExecutorId("test_executor");
        detailDO.setTargetId("test_target");
        wxInvokeDetailDOS.add(detailDO);
    }




    @Test
    public void testGetCouponPageMsgPushContentDTO_WhenMapContainsNodeId() throws Throwable {
        // Arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setProcessOrchestrationNodeId(1L);
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        MsgPushContentDTO existingDTO = new MsgPushContentDTO();
        existedAttachmentMap.put("1", existingDTO);
        OfficialWxHandler handler = new OfficialWxHandler();
        // Act
        MsgPushContentDTO result = handler.getCouponPageMsgPushContentDTO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, executeManagementDTO);
        // Assert
        assertNotNull(result);
        assertEquals(existingDTO, result);
    }

    @Test
    public void testGetCouponPageMsgPushContentDTO_WhenMapDoesNotContainNodeId() throws Throwable {
        // Arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setProcessOrchestrationNodeId(1L);
        actionAttachmentDTO.setProcessOrchestrationId(1L);
        actionAttachmentDTO.setProcessOrchestrationVersion("1.0");
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setTitle("Coupon Title");
        supplyDetailDTO.setHeadpicUrl("https://example.com/pic.jpg");
        // Added required field
        supplyDetailDTO.setProductType(1);
        // Added required field
        supplyDetailDTO.setSupplyType(1);
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        OfficialWxHandler handler = new OfficialWxHandler();
        // Mock dependencies using reflection to set private fields
        InformationGatheringService mockInfoService = Mockito.mock(InformationGatheringService.class);
        setPrivateField(handler, "informationGatheringService", mockInfoService);
        Mockito.when(mockInfoService.queryCommunityDistributor(Mockito.any())).thenReturn("communityCode");
        ProductManagementService mockProductService = Mockito.mock(ProductManagementService.class);
        setPrivateField(handler, "productManagementService", mockProductService);
        // Create properly initialized sceneCodeDO
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneCode("testSceneCode");
        // Set to non-zero to avoid NPE
        sceneCodeDO.setPoiRestrict(1);
        sceneCodeDO.setActivityPageId(1L);
        Mockito.when(mockProductService.getActivSceneCodeDO(Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any(), Mockito.any())).thenReturn(sceneCodeDO);
        // Mock shortLinkUtils to avoid NPE in buildPageUrl
        ShortLinkUtils mockShortLinkUtils = Mockito.mock(ShortLinkUtils.class);
        setPrivateField(handler, "shortLinkUtils", mockShortLinkUtils);
        Mockito.when(mockShortLinkUtils.getShortLink(Mockito.anyString(), Mockito.anyInt())).thenReturn("https://short.url");
        // Act
        MsgPushContentDTO result = handler.getCouponPageMsgPushContentDTO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, executeManagementDTO);
        // Assert
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.MINI_PROGRAM, result.getContentTypeTEnum());
        assertNotNull(result.getMiniProgramDTO());
        assertEquals("Coupon Title", result.getMiniProgramDTO().getTitle());
        assertEquals("https://example.com/pic.jpg", result.getMiniProgramDTO().getThumbnail());
    }

    private void setPrivateField(Object target, String fieldName, Object value) throws Exception {
        Field field = target.getClass().getDeclaredField(fieldName);
        field.setAccessible(true);
        field.set(target, value);
    }
}
