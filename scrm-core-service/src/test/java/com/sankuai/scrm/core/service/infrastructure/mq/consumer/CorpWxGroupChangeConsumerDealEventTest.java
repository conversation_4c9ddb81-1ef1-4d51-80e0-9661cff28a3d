package com.sankuai.scrm.core.service.infrastructure.mq.consumer;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.PersonalWxGroupInfoDomainService;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.PersonalWxGroupMemberInfoDomainService;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.dto.UpdateResultDTO;
import com.sankuai.scrm.core.service.infrastructure.mq.bo.corpwxgroupchange.OpenGroupChangeEvent;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class CorpWxGroupChangeConsumerDealEventTest {

    @Mock
    private PersonalWxGroupInfoDomainService personalWxGroupInfoDomainService;

    @Mock
    private PersonalWxGroupMemberInfoDomainService personalWxGroupMemberInfoDomainService;

    @InjectMocks
    private CorpWxGroupChangeConsumer consumerUnderTest;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test case for null input
     */
    @Test
    public void testDealEventWithNullInput() throws Throwable {
        // arrange
        OpenGroupChangeEvent nullEvent = null;
        UpdateResultDTO nullResult = new UpdateResultDTO();
        nullResult.setSuccess(false);
        when(personalWxGroupInfoDomainService.updateWxGroup(null)).thenReturn(nullResult);
        // act
        consumerUnderTest.dealEvent(nullEvent);
        // assert
        verify(personalWxGroupInfoDomainService).updateWxGroup(null);
        verify(personalWxGroupMemberInfoDomainService, never()).updateWxGroupMember(any(), anyBoolean());
    }

    /**
     * Test case for successful update with needInit false
     */
    @Test
    public void testDealEventSuccessfulUpdateNeedInitFalse() throws Throwable {
        // arrange
        OpenGroupChangeEvent event = new OpenGroupChangeEvent();
        UpdateResultDTO updateResultDTO = new UpdateResultDTO();
        updateResultDTO.setSuccess(true);
        updateResultDTO.setNeedInit(false);
        when(personalWxGroupInfoDomainService.updateWxGroup(event)).thenReturn(updateResultDTO);
        // act
        consumerUnderTest.dealEvent(event);
        // assert
        verify(personalWxGroupInfoDomainService).updateWxGroup(event);
        verify(personalWxGroupMemberInfoDomainService).updateWxGroupMember(event, false);
    }

    /**
     * Test case for successful update with needInit true
     */
    @Test
    public void testDealEventSuccessfulUpdateNeedInitTrue() throws Throwable {
        // arrange
        OpenGroupChangeEvent event = new OpenGroupChangeEvent();
        UpdateResultDTO updateResultDTO = new UpdateResultDTO();
        updateResultDTO.setSuccess(true);
        updateResultDTO.setNeedInit(true);
        when(personalWxGroupInfoDomainService.updateWxGroup(event)).thenReturn(updateResultDTO);
        // act
        consumerUnderTest.dealEvent(event);
        // assert
        verify(personalWxGroupInfoDomainService).updateWxGroup(event);
        verify(personalWxGroupMemberInfoDomainService).updateWxGroupMember(event, true);
    }

    /**
     * Test case for failed update
     */
    @Test
    public void testDealEventFailedUpdate() throws Throwable {
        // arrange
        OpenGroupChangeEvent event = new OpenGroupChangeEvent();
        UpdateResultDTO updateResultDTO = new UpdateResultDTO();
        updateResultDTO.setSuccess(false);
        when(personalWxGroupInfoDomainService.updateWxGroup(event)).thenReturn(updateResultDTO);
        // act
        consumerUnderTest.dealEvent(event);
        // assert
        verify(personalWxGroupInfoDomainService).updateWxGroup(event);
        verify(personalWxGroupMemberInfoDomainService, never()).updateWxGroupMember(any(), anyBoolean());
    }

    /**
     * Test case for exception thrown by updateWxGroup
     */
    @Test
    public void testDealEventUpdateWxGroupException() throws Throwable {
        // arrange
        OpenGroupChangeEvent event = new OpenGroupChangeEvent();
        when(personalWxGroupInfoDomainService.updateWxGroup(event)).thenThrow(new RuntimeException("Test exception"));
        // act & assert
        assertThrows(RuntimeException.class, () -> consumerUnderTest.dealEvent(event));
        // verify
        verify(personalWxGroupInfoDomainService).updateWxGroup(event);
        verify(personalWxGroupMemberInfoDomainService, never()).updateWxGroupMember(any(), anyBoolean());
    }

    /**
     * Test case for exception thrown by updateWxGroupMember
     */
    @Test
    public void testDealEventUpdateWxGroupMemberException() throws Throwable {
        // arrange
        OpenGroupChangeEvent event = new OpenGroupChangeEvent();
        UpdateResultDTO updateResultDTO = new UpdateResultDTO();
        updateResultDTO.setSuccess(true);
        updateResultDTO.setNeedInit(false);
        when(personalWxGroupInfoDomainService.updateWxGroup(event)).thenReturn(updateResultDTO);
        doThrow(new RuntimeException("Test exception")).when(personalWxGroupMemberInfoDomainService).updateWxGroupMember(event, false);
        // act & assert
        assertThrows(RuntimeException.class, () -> consumerUnderTest.dealEvent(event));
        // verify
        verify(personalWxGroupInfoDomainService).updateWxGroup(event);
        verify(personalWxGroupMemberInfoDomainService).updateWxGroupMember(event, false);
    }
}
