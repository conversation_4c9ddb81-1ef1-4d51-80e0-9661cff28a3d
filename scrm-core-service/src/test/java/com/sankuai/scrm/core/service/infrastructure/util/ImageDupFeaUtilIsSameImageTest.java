package com.sankuai.scrm.core.service.infrastructure.util;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.assertFalse;

@RunWith(MockitoJUnitRunner.class)
public class ImageDupFeaUtilIsSameImageTest {

    @InjectMocks
    private ImageDupFeaUtil imageDupFeaUtil;

    /**
     * Test isSameImage method when url1 or url2 is invalid or not a valid URL, should return false.
     */
    @Test
    public void testIsSameImageUrlInvalid() throws Throwable {
        // arrange
        String url1 = "invalid_url";
        String url2 = "http://example.com";
        boolean compressSwitch = true;
        // act
        boolean result = imageDupFeaUtil.isSameImage(url1, url2, compressSwitch);
        // assert
        assertFalse(result);
    }

    /**
     * Test isSameImage method when doCompare method throws an exception, should return false.
     */
    @Test(expected = Exception.class)
    public void testIsSameImageDoCompareException() throws Throwable {
        // arrange
        String url1 = "http://example.com";
        String url2 = "http://example.com";
        boolean compressSwitch = true;
        // act
        boolean result = imageDupFeaUtil.isSameImage(url1, url2, compressSwitch);
        // assert
        assertFalse(result);
    }
}
