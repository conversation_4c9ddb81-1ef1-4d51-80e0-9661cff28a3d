package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.GroupSendStrategy;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.verifyNoInteractions;

/**
 * Test cases for OfficialWxHandler.dealNormalOfficialWxGroupMessageV2 method.
 */
public class OfficialWxHandlerDealNormalOfficialWxGroupMessageV2Test {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private MsgUnifiedPushService msgUnifiedPushService;

    @Mock
    private GroupSendStrategy groupSendStrategy;

    private static final Long PROCESS_ORCHESTRATION_ID = 1L;

    private static final String VALID_VERSION = "1.0";

    private static final Long NODE_ID = 1L;

    private static final String APP_ID = "testAppId";

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test scenario when executorIds list is empty.
     */
    @Test
    public void testDealNormalOfficialWxGroupMessageV2_EmptyExecutorIds() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createProcessOrchestrationDTO();
        List<String> executorIds = new ArrayList<>();
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("key", (byte) 1, (byte) 1, NODE_ID);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        officialWxHandler.dealNormalOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, keyObject, totalInvokeDetailDOS);
        verifyNoInteractions(msgUnifiedPushService);
    }

    /**
     * Test scenario when totalInvokeDetailDOS list is empty.
     */
    @Test
    public void testDealNormalOfficialWxGroupMessageV2_EmptyInvokeDetails() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createProcessOrchestrationDTO();
        List<String> executorIds = Collections.singletonList("executor1");
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("key", (byte) 1, (byte) 1, NODE_ID);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        officialWxHandler.dealNormalOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, keyObject, totalInvokeDetailDOS);
        verifyNoInteractions(msgUnifiedPushService);
    }

    /**
     * Helper method to create a valid ScrmProcessOrchestrationDTO
     */
    private ScrmProcessOrchestrationDTO createProcessOrchestrationDTO() {
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setId(PROCESS_ORCHESTRATION_ID);
        dto.setValidVersion(VALID_VERSION);
        dto.setAppId(APP_ID);
        return dto;
    }

    /**
     * Helper method to create a valid ScrmAmProcessOrchestrationWxInvokeDetailDO
     */
    private ScrmAmProcessOrchestrationWxInvokeDetailDO createInvokeDetailDO() {
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO.setProcessOrchestrationId(PROCESS_ORCHESTRATION_ID);
        invokeDetailDO.setProcessOrchestrationVersion(VALID_VERSION);
        invokeDetailDO.setProcessOrchestrationNodeId(NODE_ID);
        invokeDetailDO.setGroupId("testGroupId");
        invokeDetailDO.setExecutorId("executor1");
        return invokeDetailDO;
    }
}
