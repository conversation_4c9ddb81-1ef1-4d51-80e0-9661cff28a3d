package com.sankuai.scrm.core.service.external.contact.crane;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxBatchContactUserDetail;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxBatchFollowUserInfo;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxContactUserDetail;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import static org.mockito.ArgumentMatchers.*;
import com.dianping.frog.sdk.util.StringUtils;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxContactAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxContactUserDetailListResponse;
import java.util.ArrayList;
import java.util.concurrent.CompletableFuture;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.Spy;

@ExtendWith(MockitoExtension.class)
class RefreshHistoryExternalUserTaskConvertToEntityTest {

    @InjectMocks
    private RefreshHistoryExternalUserTask refreshHistoryExternalUserTask;

    @Mock
    private ContactUserDomain contactUserDomain;

    @Mock
    private CorpWxContactAcl corpWxContactAcl;

    private final String testCorpId = "testCorp";

    private final String testStaffId = "staff1";

    private Method concurrentGetUserDetailsMethod;

    /**
     * 使用反射调用私有方法
     */
    @SuppressWarnings("unchecked")
    private List<ContactUser> invokeConvertToEntity(String corpId, String staffId, List<WxBatchContactUserDetail> details) throws Exception {
        Method method = RefreshHistoryExternalUserTask.class.getDeclaredMethod("convertToEntity", String.class, String.class, List.class);
        method.setAccessible(true);
        return (List<ContactUser>) method.invoke(refreshHistoryExternalUserTask, corpId, staffId, details);
    }

    /**
     * 测试空列表输入时返回空列表
     */
    @Test
    void testConvertToEntityWithEmptyList() throws Throwable {
        // arrange
        String corpId = "testCorp";
        String staffId = "testStaff";
        List<WxBatchContactUserDetail> emptyList = Collections.emptyList();
        // act
        List<ContactUser> result = invokeConvertToEntity(corpId, staffId, emptyList);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试null列表输入时返回空列表
     */
    @Test
    void testConvertToEntityWithNullList() throws Throwable {
        // arrange
        String corpId = "testCorp";
        String staffId = "testStaff";
        // act
        List<ContactUser> result = invokeConvertToEntity(corpId, staffId, null);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * 测试当contactUserDetail为null时过滤掉该记录
     */
    @Test
    void testConvertToEntityWithNullContactUserDetail() throws Throwable {
        // arrange
        String corpId = "testCorp";
        String staffId = "testStaff";
        WxBatchContactUserDetail detail = mock(WxBatchContactUserDetail.class);
        when(detail.getContactUserDetail()).thenReturn(null);
        List<WxBatchContactUserDetail> input = Collections.singletonList(detail);
        // act
        List<ContactUser> result = invokeConvertToEntity(corpId, staffId, input);
        // assert
        assertTrue(result.isEmpty());
        verify(detail).getContactUserDetail();
    }

    /**
     * 测试当externalUserId为空时过滤掉该记录
     */
    @Test
    void testConvertToEntityWithEmptyExternalUserId() throws Throwable {
        // arrange
        String corpId = "testCorp";
        String staffId = "testStaff";
        WxContactUserDetail contactDetail = mock(WxContactUserDetail.class);
        when(contactDetail.getExternalUserId()).thenReturn("");
        WxBatchContactUserDetail detail = mock(WxBatchContactUserDetail.class);
        when(detail.getContactUserDetail()).thenReturn(contactDetail);
        List<WxBatchContactUserDetail> input = Collections.singletonList(detail);
        // act
        List<ContactUser> result = invokeConvertToEntity(corpId, staffId, input);
        // assert
        assertTrue(result.isEmpty());
        verify(detail).getContactUserDetail();
        // Only called once for blank check
        verify(contactDetail).getExternalUserId();
    }

    /**
     * 测试正常数据转换，包含所有字段
     */
    @Test
    void testConvertToEntityWithAllFields() throws Throwable {
        // arrange
        String corpId = "testCorp";
        String staffId = "testStaff";
        String externalUserId = "ext123";
        String unionId = "union123";
        Date createTime = new Date();
        WxContactUserDetail contactDetail = mock(WxContactUserDetail.class);
        when(contactDetail.getExternalUserId()).thenReturn(externalUserId);
        when(contactDetail.getUnionId()).thenReturn(unionId);
        WxBatchFollowUserInfo followInfo = mock(WxBatchFollowUserInfo.class);
        when(followInfo.getCreateTime()).thenReturn(createTime);
        WxBatchContactUserDetail detail = mock(WxBatchContactUserDetail.class);
        when(detail.getContactUserDetail()).thenReturn(contactDetail);
        when(detail.getFollowUserInfo()).thenReturn(followInfo);
        List<WxBatchContactUserDetail> input = Collections.singletonList(detail);
        // act
        List<ContactUser> result = invokeConvertToEntity(corpId, staffId, input);
        // assert
        assertEquals(1, result.size());
        ContactUser contactUser = result.get(0);
        assertEquals(corpId, contactUser.getCorpId());
        assertEquals(staffId, contactUser.getStaffId());
        assertEquals(externalUserId, contactUser.getExternalUserId());
        assertEquals(unionId, contactUser.getUnionId());
        assertEquals(1L, contactUser.getOrgId());
        assertEquals(1, contactUser.getStatus());
        assertEquals(createTime, contactUser.getAddTime());
        verify(detail).getContactUserDetail();
        verify(detail).getFollowUserInfo();
        // Called for null check, blank check and setting value
        verify(contactDetail, times(3)).getExternalUserId();
        // Called for blank check and setting value
        verify(contactDetail, times(2)).getUnionId();
        // Called for null check and getting value
        verify(followInfo, times(2)).getCreateTime();
    }

    /**
     * 测试followUserInfo为null时使用当前时间
     */
    @Test
    void testConvertToEntityWithNullFollowUserInfo() throws Throwable {
        // arrange
        String corpId = "testCorp";
        String staffId = "testStaff";
        String externalUserId = "ext123";
        WxContactUserDetail contactDetail = mock(WxContactUserDetail.class);
        when(contactDetail.getExternalUserId()).thenReturn(externalUserId);
        WxBatchContactUserDetail detail = mock(WxBatchContactUserDetail.class);
        when(detail.getContactUserDetail()).thenReturn(contactDetail);
        when(detail.getFollowUserInfo()).thenReturn(null);
        List<WxBatchContactUserDetail> input = Collections.singletonList(detail);
        Date beforeTest = new Date();
        // act
        List<ContactUser> result = invokeConvertToEntity(corpId, staffId, input);
        // assert
        assertEquals(1, result.size());
        assertNotNull(result.get(0).getAddTime());
        assertTrue(result.get(0).getAddTime().getTime() >= beforeTest.getTime());
        verify(detail).getContactUserDetail();
        verify(detail).getFollowUserInfo();
        // Called for null check, blank check and setting value
        verify(contactDetail, times(3)).getExternalUserId();
    }

    /**
     * 测试秒级时间戳转换为毫秒级
     */
    @Test
    void testConvertToEntityWithSecondLevelTimestamp() throws Throwable {
        // arrange
        String corpId = "testCorp";
        String staffId = "testStaff";
        String externalUserId = "ext123";
        long secondsTimestamp = 1000000L;
        Date expectedTime = new Date(secondsTimestamp * 1000);
        WxContactUserDetail contactDetail = mock(WxContactUserDetail.class);
        when(contactDetail.getExternalUserId()).thenReturn(externalUserId);
        WxBatchFollowUserInfo followInfo = mock(WxBatchFollowUserInfo.class);
        when(followInfo.getCreateTime()).thenReturn(new Date(secondsTimestamp));
        WxBatchContactUserDetail detail = mock(WxBatchContactUserDetail.class);
        when(detail.getContactUserDetail()).thenReturn(contactDetail);
        when(detail.getFollowUserInfo()).thenReturn(followInfo);
        List<WxBatchContactUserDetail> input = Collections.singletonList(detail);
        // act
        List<ContactUser> result = invokeConvertToEntity(corpId, staffId, input);
        // assert
        assertEquals(1, result.size());
        assertEquals(expectedTime, result.get(0).getAddTime());
        verify(detail).getContactUserDetail();
        verify(detail).getFollowUserInfo();
        // Called for null check, blank check and setting value
        verify(contactDetail, times(3)).getExternalUserId();
        // Called for null check and getting value
        verify(followInfo, times(2)).getCreateTime();
    }

    /**
     * 测试混合有效和无效数据时的过滤
     */
    @Test
    void testConvertToEntityWithMixedValidAndInvalidData() throws Throwable {
        // arrange
        String corpId = "testCorp";
        String staffId = "testStaff";
        // 有效数据1
        WxContactUserDetail validDetail1 = mock(WxContactUserDetail.class);
        when(validDetail1.getExternalUserId()).thenReturn("ext1");
        WxBatchContactUserDetail entry1 = mock(WxBatchContactUserDetail.class);
        when(entry1.getContactUserDetail()).thenReturn(validDetail1);
        // 无效数据1 (null contact detail)
        WxBatchContactUserDetail entry2 = mock(WxBatchContactUserDetail.class);
        when(entry2.getContactUserDetail()).thenReturn(null);
        // 无效数据2 (empty externalUserId)
        WxContactUserDetail invalidDetail = mock(WxContactUserDetail.class);
        when(invalidDetail.getExternalUserId()).thenReturn("");
        WxBatchContactUserDetail entry3 = mock(WxBatchContactUserDetail.class);
        when(entry3.getContactUserDetail()).thenReturn(invalidDetail);
        // 有效数据2
        WxContactUserDetail validDetail2 = mock(WxContactUserDetail.class);
        when(validDetail2.getExternalUserId()).thenReturn("ext2");
        WxBatchContactUserDetail entry4 = mock(WxBatchContactUserDetail.class);
        when(entry4.getContactUserDetail()).thenReturn(validDetail2);
        List<WxBatchContactUserDetail> input = Arrays.asList(entry1, entry2, entry3, entry4);
        // act
        List<ContactUser> result = invokeConvertToEntity(corpId, staffId, input);
        // assert
        assertEquals(2, result.size());
        assertEquals("ext1", result.get(0).getExternalUserId());
        assertEquals("ext2", result.get(1).getExternalUserId());
        verify(entry1).getContactUserDetail();
        verify(entry2).getContactUserDetail();
        verify(entry3).getContactUserDetail();
        verify(entry4).getContactUserDetail();
        // Called for null check, blank check and setting value
        verify(validDetail1, times(3)).getExternalUserId();
        // Called for null check, blank check and setting value
        verify(validDetail2, times(3)).getExternalUserId();
        // Only called once for blank check
        verify(invalidDetail).getExternalUserId();
    }

    /**
     * 测试unionId为null时的处理
     */
    @Test
    void testConvertToEntityWithNullUnionId() throws Throwable {
        // arrange
        String corpId = "testCorp";
        String staffId = "testStaff";
        String externalUserId = "ext123";
        WxContactUserDetail contactDetail = mock(WxContactUserDetail.class);
        when(contactDetail.getExternalUserId()).thenReturn(externalUserId);
        when(contactDetail.getUnionId()).thenReturn(null);
        WxBatchContactUserDetail detail = mock(WxBatchContactUserDetail.class);
        when(detail.getContactUserDetail()).thenReturn(contactDetail);
        List<WxBatchContactUserDetail> input = Collections.singletonList(detail);
        // act
        List<ContactUser> result = invokeConvertToEntity(corpId, staffId, input);
        // assert
        assertEquals(1, result.size());
        assertNull(result.get(0).getUnionId());
        verify(detail).getContactUserDetail();
        // Called for null check, blank check and setting value
        verify(contactDetail, times(3)).getExternalUserId();
        // Only called once for blank check
        verify(contactDetail).getUnionId();
    }

    private void invokeConcurrentInsertUserDetail(List<ContactUser> contactUsers, String corpId) throws Exception {
        Method method = RefreshHistoryExternalUserTask.class.getDeclaredMethod("concurrentInsertUserDetail", List.class, String.class);
        method.setAccessible(true);
        method.invoke(refreshHistoryExternalUserTask, contactUsers, corpId);
    }

    private List<ContactUser> createUsers(int count, String corpId) {
        List<ContactUser> users = new java.util.ArrayList<>();
        for (int i = 0; i < count; i++) {
            ContactUser user = new ContactUser();
            user.setExternalUserId("user" + i);
            user.setCorpId(corpId);
            user.setStaffId("staff" + i);
            users.add(user);
        }
        return users;
    }

    @Test
    public void testConcurrentInsertUserDetailEmptyList() throws Throwable {
        // arrange
        List<ContactUser> emptyList = Collections.emptyList();
        String corpId = "testCorp";
        // act
        invokeConcurrentInsertUserDetail(emptyList, corpId);
        // assert
        verify(contactUserDomain, never()).getContactUsersByCorpIdAndStaffIdAndExternalUserId(any(), any(), any());
        verify(contactUserDomain, never()).insertContactUser(any());
    }

    @Test
    public void testConcurrentInsertUserDetailWithInvalidUsers() throws Throwable {
        // arrange
        ContactUser nullUser = null;
        ContactUser blankExternalIdUser = new ContactUser();
        blankExternalIdUser.setExternalUserId("");
        ContactUser validUser = new ContactUser();
        validUser.setExternalUserId("valid1");
        validUser.setCorpId("testCorp");
        validUser.setStaffId("staff1");
        List<ContactUser> users = Arrays.asList(nullUser, blankExternalIdUser, validUser);
        String corpId = "testCorp";
        when(contactUserDomain.getContactUsersByCorpIdAndStaffIdAndExternalUserId(anyString(), anyString(), anyString())).thenReturn(null);
        // act
        invokeConcurrentInsertUserDetail(users, corpId);
        // assert
        verify(contactUserDomain, times(1)).getContactUsersByCorpIdAndStaffIdAndExternalUserId(eq("testCorp"), eq("staff1"), eq("valid1"));
        verify(contactUserDomain, times(1)).insertContactUser(validUser);
    }

    @Test
    public void testConcurrentInsertUserDetailMultipleChunks() throws Throwable {
        // arrange
        List<ContactUser> users = createUsers(51, "testCorp");
        String corpId = "testCorp";
        when(contactUserDomain.getContactUsersByCorpIdAndStaffIdAndExternalUserId(anyString(), anyString(), anyString())).thenReturn(null);
        // act
        invokeConcurrentInsertUserDetail(users, corpId);
        // assert
        verify(contactUserDomain, times(51)).getContactUsersByCorpIdAndStaffIdAndExternalUserId(anyString(), anyString(), anyString());
        verify(contactUserDomain, times(51)).insertContactUser(any());
    }


    @BeforeEach
    void setUp() throws Exception {
        // Get private method using reflection
        concurrentGetUserDetailsMethod = RefreshHistoryExternalUserTask.class.getDeclaredMethod("concurrentGetUserDetails", String.class, List.class);
        concurrentGetUserDetailsMethod.setAccessible(true);
    }

    private List<ContactUser> invokeConcurrentGetUserDetails(String corpId, List<String> staffIds) throws Exception {
        return (List<ContactUser>) concurrentGetUserDetailsMethod.invoke(refreshHistoryExternalUserTask, corpId, staffIds);
    }

    @Test
    public void testConcurrentGetUserDetailsEmptyStaffIdList() throws Throwable {
        // arrange
        List<String> staffIds = Collections.emptyList();
        // act
        List<ContactUser> result = invokeConcurrentGetUserDetails(testCorpId, staffIds);
        // assert
        assertTrue(result.isEmpty());
    }

    @Test
    public void testConcurrentGetUserDetailsFailedAfterRetries() throws Throwable {
        // arrange
        List<String> staffIds = new ArrayList<>(Collections.singletonList(testStaffId));
        when(corpWxContactAcl.batchGetUserDetail(anyString(), any(), any())).thenReturn(null);
        // act
        List<ContactUser> result = invokeConcurrentGetUserDetails(testCorpId, staffIds);
        // assert
        assertTrue(result.isEmpty());
        verify(corpWxContactAcl, times(3)).batchGetUserDetail(anyString(), any(), any());
    }


    @Test
    public void testConcurrentGetUserDetailsResponseNotSuccess() throws Throwable {
        // arrange
        List<String> staffIds = new ArrayList<>(Collections.singletonList(testStaffId));
        WxContactUserDetailListResponse mockResponse = mock(WxContactUserDetailListResponse.class);
        when(mockResponse.isSuccess()).thenReturn(false);
        when(corpWxContactAcl.batchGetUserDetail(anyString(), any(), any())).thenReturn(mockResponse);
        // act
        List<ContactUser> result = invokeConcurrentGetUserDetails(testCorpId, staffIds);
        // assert
        assertTrue(result.isEmpty());
        verify(corpWxContactAcl, times(3)).batchGetUserDetail(anyString(), any(), any());
    }
    
}
