package com.sankuai.scrm.core.service.couponIntegration.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.couponIntegration.enums.CreationSceneEnum;
import com.sankuai.dz.srcm.couponIntegration.enums.StatusEnum;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmCouponConsumeRecordMessage;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.StrategistCouponInfo;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.StrategistCouponInfoExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.StrategistCouponInfoMapper;
import java.math.BigDecimal;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class CouponConsumeRecordServiceRecordMessageToScrmSceneCouponRecordTest {

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private StrategistCouponInfoMapper strategistCouponInfoMapper;

    @Mock
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordsMapper;

    private CouponConsumeRecordService couponConsumeRecordService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        couponConsumeRecordService = new CouponConsumeRecordService();
        // Set mocked dependencies
        TestUtils.setFieldValue(couponConsumeRecordService, "executeManagementService", executeManagementService);
        TestUtils.setFieldValue(couponConsumeRecordService, "strategistCouponInfoMapper", strategistCouponInfoMapper);
        TestUtils.setFieldValue(couponConsumeRecordService, "scrmSceneCouponRecordsMapper", scrmSceneCouponRecordsMapper);
    }

    /**
     * Test case: record message is null
     * Expected: method returns early without any operations
     */
    @Test
    void testRecordMessageToScrmSceneCouponRecord_NullMessage() {
        // arrange
        ScrmCouponConsumeRecordMessage recordMessage = null;
        // act
        couponConsumeRecordService.recordMessageToScrmSceneCouponRecord(recordMessage);
        // assert
        verify(strategistCouponInfoMapper, never()).selectByExample(any());
        verify(scrmSceneCouponRecordsMapper, never()).insert(any());
        verify(scrmSceneCouponRecordsMapper, never()).updateByExample(any(), any());
    }

    /**
     * Test case: no matching strategist coupon info found
     * Expected: method returns early after logging
     */
    @Test
    void testRecordMessageToScrmSceneCouponRecord_NoStrategistCouponInfo() {
        // arrange
        ScrmCouponConsumeRecordMessage recordMessage = new ScrmCouponConsumeRecordMessage();
        recordMessage.setCouponGroupId("testGroup");
        when(strategistCouponInfoMapper.selectByExample(any(StrategistCouponInfoExample.class))).thenReturn(Collections.emptyList());
        // act
        couponConsumeRecordService.recordMessageToScrmSceneCouponRecord(recordMessage);
        // assert
        verify(strategistCouponInfoMapper).selectByExample(any());
        verify(scrmSceneCouponRecordsMapper, never()).insert(any());
        verify(scrmSceneCouponRecordsMapper, never()).updateByExample(any(), any());
    }

    /**
     * Test case: insert new record when no existing record found
     * Expected: new record is inserted
     */
    @Test
    void testRecordMessageToScrmSceneCouponRecord_InsertNewRecord() {
        // arrange
        ScrmCouponConsumeRecordMessage recordMessage = createTestRecordMessage();
        StrategistCouponInfo strategistCouponInfo = createTestStrategistCouponInfo();
        when(strategistCouponInfoMapper.selectByExample(any())).thenReturn(Collections.singletonList(strategistCouponInfo));
        when(scrmSceneCouponRecordsMapper.countByExample(any())).thenReturn(0L);
        when(executeManagementService.getMtXCXUnionIdByMtUserId(anyLong(), anyString())).thenReturn("testUnionId");
        // act
        couponConsumeRecordService.recordMessageToScrmSceneCouponRecord(recordMessage);
        // assert
        ArgumentCaptor<ScrmSceneCouponRecords> recordCaptor = ArgumentCaptor.forClass(ScrmSceneCouponRecords.class);
        verify(scrmSceneCouponRecordsMapper).insert(recordCaptor.capture());
        ScrmSceneCouponRecords insertedRecord = recordCaptor.getValue();
        assertEquals(recordMessage.getCouponGroupId(), insertedRecord.getCoupongroupid());
        assertEquals(recordMessage.getUnifiedCouponId(), insertedRecord.getUnifiedcouponid());
        assertEquals("testUnionId", insertedRecord.getUnionid());
        assertEquals(CreationSceneEnum.MANUAL_UPLOAD.getCode(), insertedRecord.getScenetype());
    }

    /**
     * Test case: update existing record
     * Expected: existing record is updated
     */
    @Test
    void testRecordMessageToScrmSceneCouponRecord_UpdateExistingRecord() {
        // arrange
        ScrmCouponConsumeRecordMessage recordMessage = createTestRecordMessage();
        StrategistCouponInfo strategistCouponInfo = createTestStrategistCouponInfo();
        when(strategistCouponInfoMapper.selectByExample(any())).thenReturn(Collections.singletonList(strategistCouponInfo));
        when(scrmSceneCouponRecordsMapper.countByExample(any())).thenReturn(1L);
        when(executeManagementService.getMtXCXUnionIdByMtUserId(anyLong(), anyString())).thenReturn("testUnionId");
        // act
        couponConsumeRecordService.recordMessageToScrmSceneCouponRecord(recordMessage);
        // assert
        ArgumentCaptor<ScrmSceneCouponRecords> recordCaptor = ArgumentCaptor.forClass(ScrmSceneCouponRecords.class);
        ArgumentCaptor<ScrmSceneCouponRecordsExample> exampleCaptor = ArgumentCaptor.forClass(ScrmSceneCouponRecordsExample.class);
        verify(scrmSceneCouponRecordsMapper).updateByExampleSelective(recordCaptor.capture(), exampleCaptor.capture());
        ScrmSceneCouponRecords updatedRecord = recordCaptor.getValue();
        assertEquals(recordMessage.getCouponGroupId(), updatedRecord.getCoupongroupid());
        assertEquals(recordMessage.getUnifiedCouponId(), updatedRecord.getUnifiedcouponid());
        assertEquals("testUnionId", updatedRecord.getUnionid());
    }

    private ScrmCouponConsumeRecordMessage createTestRecordMessage() {
        ScrmCouponConsumeRecordMessage message = new ScrmCouponConsumeRecordMessage();
        message.setCouponGroupId("testGroup");
        message.setUnifiedCouponId("testCouponId");
        message.setUserId(123L);
        message.setCouponParValue(new BigDecimal("100"));
        message.setCouponPriceLimit(new BigDecimal("50"));
        message.setClientType((byte) 1);
        message.setOrderId("testOrder");
        message.setBeginTime("2023-01-01 00:00:00");
        message.setEndTime("2023-12-31 23:59:59");
        message.setUseCouponTime("2023-06-01 12:00:00");
        return message;
    }

    private StrategistCouponInfo createTestStrategistCouponInfo() {
        StrategistCouponInfo info = new StrategistCouponInfo();
        info.setCouponGroupId("testGroup");
        info.setAppId("testApp");
        info.setCreationScene(CreationSceneEnum.MANUAL_UPLOAD.getCode());
        info.setStatus(StatusEnum.COUNTING.getCode());
        return info;
    }
}

/**
 * Test utility class for setting private fields
 */
class TestUtils {

    public static void setFieldValue(Object object, String fieldName, Object value) {
        try {
            java.lang.reflect.Field field = object.getClass().getDeclaredField(fieldName);
            field.setAccessible(true);
            field.set(object, value);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }
}
