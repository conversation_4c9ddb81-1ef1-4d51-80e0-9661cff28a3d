package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.infrastructure.acl.UploadWxMediaAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGetGroupMsgSendResultAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGroupSendMessageAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxGetGroupmsgSendResultRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxGroupSendMessageRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.AttachmentVO;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.TextVO;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGetGroupmsgSendResultResponse;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGroupSendMessageResponse;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.dto.SendListDTO;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import com.sankuai.scrm.core.service.infrastructure.vo.WechatMediaResult;
import com.sankuai.scrm.core.service.infrastructure.vo.WxMediaType;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.content.TextDTO;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import org.apache.commons.lang3.StringUtils;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.junit.Before;
import org.junit.Ignore;
import org.junit.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.*;
import java.util.logging.Logger;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerTest {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock(lenient = true)
    private ShortLinkUtils shortLinkUtils;

    @Mock(lenient = true)
    private WxGroupSendMessageAcl wxGroupSendMessageAcl;

    @Mock(lenient = true)
    private CorpAppConfigRepository appConfigRepository;

    @Mock(lenient = true)
    private WeChatTokenAcl weChatTokenAcl;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ScrmProcessOrchestrationDTO couponProcessOrchestrationDTO;

    private String executorId;

    private Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> typeExecuteLogDOMapEntry;

    private ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO;

    private ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO;

    private List<ScrmAmProcessOrchestrationProductActivityPageDO> pageDOS;

    private Map<String, AttachmentVO> existedAttachmentMap;

    @Mock(lenient = true)
    private UploadWxMediaAcl uploadWxMediaAcl;

    @Mock(lenient = true)
    private WxGetGroupMsgSendResultAcl wxGetGroupmsgSendResultAcl;

    @Mock(lenient = true)
    private ExecuteManagementService executeManagementService;

    @Mock(lenient = true)
    private ProductManagementService productManagementService;

    @Mock(lenient = true)
    private InformationGatheringService informationGatheringService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;
    @Mock
    private Logger log;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(officialWxHandler, "shortLinkUtils", shortLinkUtils);
        ReflectionTestUtils.setField(officialWxHandler, "wxGroupSendMessageAcl", wxGroupSendMessageAcl);
        ReflectionTestUtils.setField(officialWxHandler, "appConfigRepository", appConfigRepository);
        ReflectionTestUtils.setField(officialWxHandler, "weChatTokenAcl", weChatTokenAcl);
        ReflectionTestUtils.setField(officialWxHandler, "uploadWxMediaAcl", uploadWxMediaAcl);
        ReflectionTestUtils.setField(officialWxHandler, "wxGetGroupmsgSendResultAcl", wxGetGroupmsgSendResultAcl);
        ReflectionTestUtils.setField(officialWxHandler, "executeManagementService", executeManagementService);
        ReflectionTestUtils.setField(officialWxHandler, "shortLinkUtils", shortLinkUtils);
        ReflectionTestUtils.setField(officialWxHandler, "informationGatheringService", informationGatheringService);
        String processOrchestrationMocks = "{\n" + "  \"id\" : 192,\n" + "  \"name\" : \"自动推品+全部商品+团购+群\",\n" + "  \"processOrchestrationType\" : 4,\n" + "  \"cron\" : \"\",\n" + "  \"beginTime\" : 1725702000000,\n" + "  \"endTime\" : 1725874800000,\n" + "  \"status\" : 1,\n" + "  \"validVersion\" : \"1725788339717\",\n" + "  \"updateTime\" : 1725788339000,\n" + "  \"creatorId\" : \"wangxuefei05\",\n" + "  \"lastUpdaterId\" : \"wangxuefei05\",\n" + "  \"participationRestrict\" : true,\n" + "  \"participationRestrictionsCycle\" : 0,\n" + "  \"participationRestrictionsTimes\" : 0,\n" + "  \"appId\" : \"yimei\",\n" + "  \"previewPic\" : \"\",\n" + "  \"cronComment\" : \"2024-09-08 17:40:00\",\n" + "  \"executorType\" : 1,\n" + "  \"executorList\" : [ {\n" + "    \"executorId\" : \"WangXueFei\",\n" + "    \"executorName\" : \"王雪飞\",\n" + "    \"executorType\" : 2\n" + "  } ],\n" + "  \"crowdPackType\" : 3,\n" + "  \"crowdPackIdList\" : [ ],\n" + "  \"groupIdList\" : [ \"wrb_61EQAACdwDGbhFsGznPHZMX3i3_A\" ],\n" + "  \"groupInfoList\" : [ {\n" + "    \"groupName\" : \"军师测试25\",\n" + "    \"owner\" : \"WangXueFei\",\n" + "    \"robotList\" : null,\n" + "    \"groupId\" : \"wrb_61EQAACdwDGbhFsGznPHZMX3i3_A\",\n" + "    \"createDate\" : 1725788266000,\n" + "    \"memberCount\" : 3\n" + "  } ],\n" + "  \"crowdPackUpdateStrategyInfoDTO\" : null,\n" + "  \"goalDTO\" : {\n" + "    \"id\" : 554,\n" + "    \"checkTime\" : \"3\",\n" + "    \"checkTimeUnit\" : \"2\",\n" + "    \"status\" : 1,\n" + "    \"processOrchestrationId\" : 192,\n" + "    \"processOrchestrationVersion\" : \"1725788339717\",\n" + "    \"goalType\" : 1,\n" + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003 ],\n" + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n" + "  \"negativeGoalDTO\" : {\n" + "    \"id\" : 553,\n" + "    \"checkTime\" : \"3\",\n" + "    \"checkTimeUnit\" : \"2\",\n" + "    \"status\" : 1,\n" + "    \"processOrchestrationId\" : 192,\n" + "    \"processOrchestrationVersion\" : \"1725788339717\",\n" + "    \"goalType\" : 2,\n" + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003 ],\n" + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n" + "  \"nodeMediumDTO\" : {\n" + "    \"processOrchestrationNodeDTOList\" : [ {\n" + "      \"nodeId\" : 0,\n" + "      \"preNodeId\" : -1,\n" + "      \"nodeType\" : 4,\n" + "      \"id\" : 750,\n" + "      \"processOrchestrationId\" : 192,\n" + "      \"processOrchestrationVersion\" : \"1725788339717\",\n" + "      \"childrenNodes\" : [ 1725778802191 ]\n" + "    }, {\n" + "      \"nodeId\" : 1725778802191,\n" + "      \"preNodeId\" : 0,\n" + "      \"nodeType\" : 2,\n" + "      \"id\" : 751,\n" + "      \"processOrchestrationId\" : 192,\n" + "      \"processOrchestrationVersion\" : \"1725788339717\",\n" + "      \"childrenNodes\" : [ ]\n" + "    } ],\n" + "    \"conditionMap\" : { },\n" + "    \"actionMap\" : {\n" + "      \"1725778802191\" : {\n" + "        \"id\" : 343,\n" + "        \"actionId\" : 1,\n" + "        \"processOrchestrationId\" : 192,\n" + "        \"processOrchestrationVersion\" : \"1725788339717\",\n" + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"actionType\" : 7,\n" + "        \"actionSubType\" : 5,\n" + "        \"updateTime\" : 1725788339000,\n" + "        \"contentType\" : 1,\n" + "        \"contentList\" : null\n" + "      }\n" + "    },\n" + "    \"actionContentMap\" : {\n" + "      \"1725778802191-1\" : [ {\n" + "        \"id\" : 342,\n" + "        \"actionId\" : 1,\n" + "        \"contentId\" : 1,\n" + "        \"processOrchestrationId\" : 192,\n" + "        \"processOrchestrationVersion\" : \"1725788339717\",\n" + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"content\" : \"\",\n" + "        \"updateTime\" : 1725788339000,\n" + "        \"contentType\" : 1,\n" + "        \"attachmentDTOList\" : null\n" + "      } ]\n" + "    },\n" + "    \"actionAttachmentMap\" : {\n" + "      \"1725778802191-1-1\" : [ {\n" + "        \"id\" : 422,\n" + "        \"contentId\" : 1,\n" + "        \"actionId\" : 1,\n" + "        \"attachmentTypeId\" : 7,\n" + "        \"attachmentContent\" : \"{\\\"supplyType\\\":2,\\\"productType\\\":1,\\\"shelfName\\\":\\\"\\\",\\\"jumpPageType\\\":\\\"\\\",\\\"jumpUrl\\\":\\\"\\\",\\\"productId\\\":\\\"738067238,753099031,427691503\\\",\\\"supplyScope\\\":\\\"\\\",\\\"marketingCopy\\\":\\\"手动推品+团购\\\",\\\"marketingCopySource\\\":2,\\\"hotTagList\\\":\\\"\\\",\\\"topproductids\\\":\\\"\\\",\\\"headpicUrl\\\":\\\"\\\"}\",\n" + "        \"processOrchestrationId\" : 192,\n" + "        \"processOrchestrationVersion\" : \"1725788339717\",\n" + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"updateTime\" : null,\n" + "        \"attachmentSupplyDetailDTO\" : {\n" + "          \"supplyType\" : 2,\n" + "          \"productId\" : \"738067238,753099031,427691503\",\n" + "          \"productType\" : 1,\n" + "          \"marketingCopy\" : \"手动推品+团购\",\n" + "          \"marketingCopySource\" : 2,\n" + "          \"supplyScope\" : null,\n" + "          \"hotTagList\" : \"\",\n" + "          \"shelfName\" : \"\",\n" + "          \"jumpPageType\" : null,\n" + "          \"jumpUrl\" : \"\",\n" + "          \"topproductids\" : \"\",\n" + "          \"headpicUrl\" : \"\"\n" + "        },\n" + "        \"attachmentContentDetailDTO\" : null\n" + "      } ]\n" + "    }\n" + "  },\n" + "  \"executePlanDTO\" : null\n" + "}";
        processOrchestrationDTO = JsonUtils.toObject(processOrchestrationMocks, ScrmProcessOrchestrationDTO.class);
        String couponMocks = "{\n" + "  \"id\" : 446,\n" + "  \"name\" : \"复制QA测试0106-1:定时任务+客户群群发+新增节点\",\n" + "  \"processOrchestrationType\" : 4,\n" + "  \"cron\" : \"\",\n" + "  \"beginTime\" : 1736151000000,\n" + "  \"endTime\" : 1736323800000,\n" + "  \"status\" : 1,\n" + "  \"validVersion\" : \"1736236740459\",\n" + "  \"updateTime\" : 1736236740000,\n" + "  \"creatorId\" : \"wangyonghao02\",\n" + "  \"lastUpdaterId\" : \"wangyonghao02\",\n" + "  \"participationRestrict\" : true,\n" + "  \"participationRestrictionsCycle\" : 0,\n" + "  \"participationRestrictionsTimes\" : 0,\n" + "  \"appId\" : \"hanchengfuwu\",\n" + "  \"previewPic\" : \"\",\n" + "  \"cronComment\" : \"2025-01-07 16:10:00\",\n" + "  \"executorType\" : 1,\n" + "  \"effectiveTimeStart\" : \"\",\n" + "  \"effectiveTimeEnd\" : \"\",\n" + "  \"alarmthreshold\" : \"\",\n" + "  \"alarmreceiver\" : \"\",\n" + "  \"executorList\" : [ {\n" + "    \"executorId\" : \"presto\",\n" + "    \"executorName\" : \"王雪飞\",\n" + "    \"executorType\" : 2\n" + "  } ],\n" + "  \"realTimeSceneId\" : null,\n" + "  \"sceneType\" : 0,\n" + "  \"crowdPackType\" : 3,\n" + "  \"crowdPackIdList\" : [ ],\n" + "  \"groupIdList\" : [ \"wrdfWXVQAAX-hsIQCI3ldIYdw7Wyi6Xg\" ],\n" + "  \"groupInfoList\" : [ {\n" + "    \"groupName\" : \"QA测试群2\",\n" + "    \"owner\" : \"presto\",\n" + "    \"robotList\" : null,\n" + "    \"groupId\" : \"wrdfWXVQAAX-hsIQCI3ldIYdw7Wyi6Xg\",\n" + "    \"createDate\" : 1735541053000,\n" + "    \"memberCount\" : 2\n" + "  } ],\n" + "  \"crowdPackUpdateStrategyInfoDTO\" : null,\n" + "  \"goalDTO\" : {\n" + "    \"id\" : 1732,\n" + "    \"checkTime\" : \"7\",\n" + "    \"checkTimeUnit\" : \"3\",\n" + "    \"status\" : 1,\n" + "    \"processOrchestrationId\" : 446,\n" + "    \"processOrchestrationVersion\" : \"1736236740459\",\n" + "    \"goalType\" : 1,\n" + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003, 90006 ],\n" + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n" + "  \"negativeGoalDTO\" : {\n" + "    \"id\" : 1731,\n" + "    \"checkTime\" : \"7\",\n" + "    \"checkTimeUnit\" : \"3\",\n" + "    \"status\" : 1,\n" + "    \"processOrchestrationId\" : 446,\n" + "    \"processOrchestrationVersion\" : \"1736236740459\",\n" + "    \"goalType\" : 2,\n" + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003, 90006 ],\n" + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n" + "  \"nodeMediumDTO\" : {\n" + "    \"processOrchestrationNodeDTOList\" : [ {\n" + "      \"nodeId\" : 0,\n" + "      \"preNodeId\" : -1,\n" + "      \"nodeType\" : 4,\n" + "      \"id\" : 2289,\n" + "      \"processOrchestrationId\" : 446,\n" + "      \"processOrchestrationVersion\" : \"1736236740459\",\n" + "      \"childrenNodes\" : [ 1736217559775 ]\n" + "    }, {\n" + "      \"nodeId\" : 1736217559775,\n" + "      \"preNodeId\" : 0,\n" + "      \"nodeType\" : 2,\n" + "      \"id\" : 2290,\n" + "      \"processOrchestrationId\" : 446,\n" + "      \"processOrchestrationVersion\" : \"1736236740459\",\n" + "      \"childrenNodes\" : [ 1736217600792 ]\n" + "    }, {\n" + "      \"nodeId\" : 1736217600792,\n" + "      \"preNodeId\" : 1736217559775,\n" + "      \"nodeType\" : 2,\n" + "      \"id\" : 2291,\n" + "      \"processOrchestrationId\" : 446,\n" + "      \"processOrchestrationVersion\" : \"1736236740459\",\n" + "      \"childrenNodes\" : [ ]\n" + "    } ],\n" + "    \"conditionMap\" : { },\n" + "    \"actionMap\" : {\n" + "      \"1736217559775\" : {\n" + "        \"id\" : 1108,\n" + "        \"actionId\" : 1,\n" + "        \"processOrchestrationId\" : 446,\n" + "        \"processOrchestrationVersion\" : \"1736236740459\",\n" + "        \"processOrchestrationNodeId\" : 1736217559775,\n" + "        \"actionType\" : 7,\n" + "        \"actionSubType\" : 5,\n" + "        \"updateTime\" : 1736236740000,\n" + "        \"contentType\" : 1,\n" + "        \"contentList\" : null\n" + "      },\n" + "      \"1736217600792\" : {\n" + "        \"id\" : 1109,\n" + "        \"actionId\" : 621760079,\n" + "        \"processOrchestrationId\" : 446,\n" + "        \"processOrchestrationVersion\" : \"1736236740459\",\n" + "        \"processOrchestrationNodeId\" : 1736217600792,\n" + "        \"actionType\" : 7,\n" + "        \"actionSubType\" : 5,\n" + "        \"updateTime\" : 1736236740000,\n" + "        \"contentType\" : 0,\n" + "        \"contentList\" : null\n" + "      }\n" + "    },\n" + "    \"actionContentMap\" : {\n" + "      \"1736217600792-621760079\" : [ {\n" + "        \"id\" : 1105,\n" + "        \"actionId\" : 621760079,\n" + "        \"contentId\" : 1,\n" + "        \"processOrchestrationId\" : 446,\n" + "        \"processOrchestrationVersion\" : \"1736236740459\",\n" + "        \"processOrchestrationNodeId\" : 1736217600792,\n" + "        \"content\" : \"新增一个节点123\",\n" + "        \"updateTime\" : 1736236740000,\n" + "        \"contentType\" : 0,\n" + "        \"attachmentDTOList\" : null\n" + "      } ],\n" + "      \"1736217559775-1\" : [ {\n" + "        \"id\" : 1106,\n" + "        \"actionId\" : 1,\n" + "        \"contentId\" : 1,\n" + "        \"processOrchestrationId\" : 446,\n" + "        \"processOrchestrationVersion\" : \"1736236740459\",\n" + "        \"processOrchestrationNodeId\" : 1736217559775,\n" + "        \"content\" : \"\",\n" + "        \"updateTime\" : 1736236740000,\n" + "        \"contentType\" : 1,\n" + "        \"attachmentDTOList\" : null\n" + "      } ]\n" + "    },\n" + "    \"actionAttachmentMap\" : {\n" + "      \"1736217559775-1-1\" : [ {\n" + "        \"id\" : 885,\n" + "        \"contentId\" : 1,\n" + "        \"actionId\" : 1,\n" + "        \"attachmentTypeId\" : 7,\n" + "        \"attachmentContent\" : \"{\\\"couponGroupId\\\":\\\"1476277241\\\",\\\"title\\\":\\\"企微好友专享优惠\\\",\\\"supplyType\\\":4,\\\"productType\\\":3,\\\"shelfName\\\":\\\"\\\",\\\"jumpPageType\\\":1,\\\"jumpUrl\\\":\\\"\\\",\\\"productId\\\":\\\"65\\\",\\\"supplyScope\\\":3,\\\"marketingCopy\\\":\\\"天降优惠+定时\\\",\\\"marketingCopySource\\\":2,\\\"hotTagList\\\":\\\"\\\",\\\"topproductids\\\":\\\"\\\",\\\"headpicUrl\\\":\\\"https://p0.meituan.net/ingee/f64e62bdc303ae719148960b6a441df3640956.png\\\",\\\"posterSetting\\\":{\\\"title\\\":\\\"社群专项特价爆品\\\",\\\"image\\\":\\\"\\\",\\\"benefitInfo\\\":\\\"\\\"},\\\"advancedConfigEffect\\\":false,\\\"poiRestrict\\\":true}\",\n" + "        \"processOrchestrationId\" : 446,\n" + "        \"processOrchestrationVersion\" : \"1736236740459\",\n" + "        \"processOrchestrationNodeId\" : 1736217559775,\n" + "        \"updateTime\" : null,\n" + "        \"attachmentSupplyDetailDTO\" : {\n" + "          \"supplyType\" : 4,\n" + "          \"productId\" : \"65\",\n" + "          \"productType\" : 3,\n" + "          \"marketingCopy\" : \"天降优惠+定时\",\n" + "          \"marketingCopySource\" : 2,\n" + "          \"supplyScope\" : 3,\n" + "          \"hotTagList\" : \"\",\n" + "          \"shelfName\" : \"\",\n" + "          \"title\" : \"企微好友专享优惠\",\n" + "          \"couponGroupId\" : \"1476277241\",\n" + "          \"jumpPageType\" : 1,\n" + "          \"jumpUrl\" : \"\",\n" + "          \"topproductids\" : \"\",\n" + "          \"headpicUrl\" : \"https://p0.meituan.net/ingee/f64e62bdc303ae719148960b6a441df3640956.png\",\n" + "          \"qrCodeUrl\" : null,\n" + "          \"advancedConfigEffect\" : false,\n" + "          \"poiRestrict\" : true,\n" + "          \"couponPeriodOfValidity\" : null\n" + "        },\n" + "        \"attachmentContentDetailDTO\" : null\n" + "      } ]\n" + "    },\n" + "    \"nodeExecuteLogMap\" : { },\n" + "    \"branchStatisticsMap\" : { }\n" + "  },\n" + "  \"executePlanDTO\" : {\n" + "    \"id\" : 425,\n" + "    \"processOrchestrationId\" : 446,\n" + "    \"processOrchestrationVersion\" : \"1736236740459\",\n" + "    \"processOrchestrationType\" : 4,\n" + "    \"taskStartTime\" : 1736237700000,\n" + "    \"status\" : 8,\n" + "    \"updateTime\" : null\n" + "  }\n" + "}";
        couponProcessOrchestrationDTO = JsonUtils.toObject(couponMocks, ScrmProcessOrchestrationDTO.class);
        //processOrchestrationDTO.setId(1L);
        // processOrchestrationDTO.setValidVersion("1.0");
        // processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setProcessOrchestrationNodeId(1L);
        nodeMediumDTO.addActionDTO(1L, actionDTO);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOs = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("Test content");
        contentDTOs.add(contentDTO);
        nodeMediumDTO.addActionContentDTOList(actionDTO, contentDTOs);
        // processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        executorId = "executorId";
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> invokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setGroupId("testGroupId");
        detailDO.setProcessOrchestrationNodeId(1L);
        invokeDetailDOS.add(detailDO);
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("NonSupply", (byte) 0, (byte) 0, 1L);
        typeExecuteLogDOMapEntry = new AbstractMap.SimpleEntry<>(keyObject, invokeDetailDOS);
        //when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("testCorpId");
        actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        pageDOS = new ArrayList<>();
        existedAttachmentMap = new HashMap<>();
        MockitoAnnotations.initMocks(this);
    }
    @Test
    public void testTokenResultIsNullOrNotSuccess() {
        // Arrange
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        WeChatTokenResult tokenResult = null; // Simulate tokenResult being null

        // Act
        if (tokenResult == null || false == (tokenResult != null && tokenResult.isSuccess())) {
            log.warning("dealCouponSupplyOfficialWxMessage 供给类官方途径群发消息-获取token失败");
            stepExecuteResultDTO.setMsg("供给类官方途径群发消息-获取token失败");
            stepExecuteResultDTO.setExistedFailedAttachmentUpload(true);
            stepExecuteResultDTO.setSuccess(false);
            stepExecuteResultDTO.setCode(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ATTACHMENT_ABNORMALITY.getCode());
        }

        // Assert
        assertEquals("供给类官方途径群发消息-获取token失败", stepExecuteResultDTO.getMsg());
        assertTrue(stepExecuteResultDTO.isExistedFailedAttachmentUpload());
        assertFalse(stepExecuteResultDTO.isSuccess());
        assertEquals(Optional.of(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ATTACHMENT_ABNORMALITY.getCode()), stepExecuteResultDTO.getCode());
        verify(log, times(1)).warning("dealCouponSupplyOfficialWxMessage 供给类官方途径群发消息-获取token失败");
    }

    @Test
    public void testTokenResultIsNotSuccess() {
        // Arrange
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        WeChatTokenResult tokenResult = mock(WeChatTokenResult.class);
        when(tokenResult.isSuccess()).thenReturn(false); // Simulate tokenResult.isSuccess() returning false

        // Act
        if (tokenResult == null || false == tokenResult.isSuccess()) {
            log.warning("dealCouponSupplyOfficialWxMessage 供给类官方途径群发消息-获取token失败");
            stepExecuteResultDTO.setMsg("供给类官方途径群发消息-获取token失败");
            stepExecuteResultDTO.setExistedFailedAttachmentUpload(true);
            stepExecuteResultDTO.setSuccess(false);
            stepExecuteResultDTO.setCode(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ATTACHMENT_ABNORMALITY.getCode());
        }

        // Assert
        assertEquals("供给类官方途径群发消息-获取token失败", stepExecuteResultDTO.getMsg());
        assertTrue(stepExecuteResultDTO.isExistedFailedAttachmentUpload());
        assertFalse(stepExecuteResultDTO.isSuccess());
        assertEquals(Optional.of(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ATTACHMENT_ABNORMALITY.getCode()), stepExecuteResultDTO.getCode());
        verify(log, times(1)).warning("dealCouponSupplyOfficialWxMessage 供给类官方途径群发消息-获取token失败");
    }

    /**
     * 测试 coreUrl 为空的情况
     */
    @Test
    public void testGetShorUrlCoreUrlIsNull() throws Throwable {
        String result = officialWxHandler.getShorUrl(null, false);
        assertNull(result);
    }

    /**
     * 测试 coreUrl 不以 "http" 开头的情况
     */
    @Test
    public void testGetShorUrlCoreUrlNotStartWithHttp() throws Throwable {
        String result = officialWxHandler.getShorUrl("ftp://test.com", false);
        assertEquals("ftp://test.com", result);
    }

    /**
     * 测试 coreUrl 的长度小于 400 的情况
     */
    @Test
    public void testGetShorUrlCoreUrlLengthLessThan400() throws Throwable {
        String result = officialWxHandler.getShorUrl("http://test.com", false);
        assertEquals("http://test.com", result);
    }

    /**
     * 测试 coreUrl 的长度大于等于 400，且 shortLinkUtils.getShortLink(coreUrl, 90) 方法返回的短链接不为空的情况
     */
    @Test
    public void testGetShorUrlCoreUrlLengthGreaterThan400AndShortLinkNotNull() throws Throwable {
        String longUrl = "http://test.com/" + new String(new char[400]);
        String shortUrl = "http://short.url";
        when(shortLinkUtils.getShortLink(longUrl, 90)).thenReturn(shortUrl);
        String result = officialWxHandler.getShorUrl(longUrl, false);
        assertEquals(shortUrl, result);
    }

    /**
     * 测试 coreUrl 的长度大于等于 400，且 shortLinkUtils.getShortLink(coreUrl, 90) 方法返回的短链接为空的情况
     * 修改测试用例以准确模拟抛出 RuntimeException 的条件
     */
    @Test(expected = Exception.class)
    public void testGetShorUrlCoreUrlLengthGreaterThan400AndShortLinkIsNull() throws Throwable {
        String longUrl = "http://test.com/" + new String(new char[400]);
        // Since we cannot mock ShortUrlResult directly, we simulate the behavior that leads to a RuntimeException
        when(shortLinkUtils.getShortLink(longUrl, 90)).thenThrow(new RuntimeException("创建短链失败，响应为空"));
        officialWxHandler.getShorUrl(longUrl, false);
    }

    @Test
    public void testDealOfficialWxGroupMessageWithInvalidToken() throws Throwable {
        WeChatTokenResult tokenResult = new WeChatTokenResult(1, "invalid_token", "Failure", 0);
        when(weChatTokenAcl.getTokenByCorpId("testCorpId")).thenReturn(tokenResult);
        officialWxHandler.dealOfficialWxGroupMessage(processOrchestrationDTO, executorId, typeExecuteLogDOMapEntry, null);
        // verify(wxGroupSendMessageAcl, never()).groupSendMessage(any(), any(), any());
        assertNotNull(tokenResult);
    }

    @Test
    public void testDealOfficialWxGroupMessageWithNullToken() throws Throwable {
        when(weChatTokenAcl.getTokenByCorpId("testCorpId")).thenReturn(null);
        officialWxHandler.dealOfficialWxGroupMessage(processOrchestrationDTO, executorId, typeExecuteLogDOMapEntry, null);
        // verify(wxGroupSendMessageAcl, never()).groupSendMessage(any(), any(), any());
        assertNotNull(processOrchestrationDTO);
    }

    @Test
    public void testDealOfficialWxMessage() {
        String processOrchestrationMocks = "{\n" + "  \"id\" : 192,\n" + "  \"name\" : \"自动推品+全部商品+团购+群\",\n" + "  \"processOrchestrationType\" : 4,\n" + "  \"cron\" : \"\",\n" + "  \"beginTime\" : 1725702000000,\n" + "  \"endTime\" : 1725874800000,\n" + "  \"status\" : 1,\n" + "  \"validVersion\" : \"1725788339717\",\n" + "  \"updateTime\" : 1725788339000,\n" + "  \"creatorId\" : \"wangxuefei05\",\n" + "  \"lastUpdaterId\" : \"wangxuefei05\",\n" + "  \"participationRestrict\" : true,\n" + "  \"participationRestrictionsCycle\" : 0,\n" + "  \"participationRestrictionsTimes\" : 0,\n" + "  \"appId\" : \"yimei\",\n" + "  \"previewPic\" : \"\",\n" + "  \"cronComment\" : \"2024-09-08 17:40:00\",\n" + "  \"executorType\" : 1,\n" + "  \"executorList\" : [ {\n" + "    \"executorId\" : \"WangXueFei\",\n" + "    \"executorName\" : \"王雪飞\",\n" + "    \"executorType\" : 2\n" + "  } ],\n" + "  \"crowdPackType\" : 3,\n" + "  \"crowdPackIdList\" : [ ],\n" + "  \"groupIdList\" : [ \"wrb_61EQAACdwDGbhFsGznPHZMX3i3_A\" ],\n" + "  \"groupInfoList\" : [ {\n" + "    \"groupName\" : \"军师测试25\",\n" + "    \"owner\" : \"WangXueFei\",\n" + "    \"robotList\" : null,\n" + "    \"groupId\" : \"wrb_61EQAACdwDGbhFsGznPHZMX3i3_A\",\n" + "    \"createDate\" : 1725788266000,\n" + "    \"memberCount\" : 3\n" + "  } ],\n" + "  \"crowdPackUpdateStrategyInfoDTO\" : null,\n" + "  \"goalDTO\" : {\n" + "    \"id\" : 554,\n" + "    \"checkTime\" : \"3\",\n" + "    \"checkTimeUnit\" : \"2\",\n" + "    \"status\" : 1,\n" + "    \"processOrchestrationId\" : 192,\n" + "    \"processOrchestrationVersion\" : \"1725788339717\",\n" + "    \"goalType\" : 1,\n" + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003 ],\n" + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n" + "  \"negativeGoalDTO\" : {\n" + "    \"id\" : 553,\n" + "    \"checkTime\" : \"3\",\n" + "    \"checkTimeUnit\" : \"2\",\n" + "    \"status\" : 1,\n" + "    \"processOrchestrationId\" : 192,\n" + "    \"processOrchestrationVersion\" : \"1725788339717\",\n" + "    \"goalType\" : 2,\n" + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003 ],\n" + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n" + "  \"nodeMediumDTO\" : {\n" + "    \"processOrchestrationNodeDTOList\" : [ {\n" + "      \"nodeId\" : 0,\n" + "      \"preNodeId\" : -1,\n" + "      \"nodeType\" : 4,\n" + "      \"id\" : 750,\n" + "      \"processOrchestrationId\" : 192,\n" + "      \"processOrchestrationVersion\" : \"1725788339717\",\n" + "      \"childrenNodes\" : [ 1725778802191 ]\n" + "    }, {\n" + "      \"nodeId\" : 1725778802191,\n" + "      \"preNodeId\" : 0,\n" + "      \"nodeType\" : 2,\n" + "      \"id\" : 751,\n" + "      \"processOrchestrationId\" : 192,\n" + "      \"processOrchestrationVersion\" : \"1725788339717\",\n" + "      \"childrenNodes\" : [ ]\n" + "    } ],\n" + "    \"conditionMap\" : { },\n" + "    \"actionMap\" : {\n" + "      \"1725778802191\" : {\n" + "        \"id\" : 343,\n" + "        \"actionId\" : 1,\n" + "        \"processOrchestrationId\" : 192,\n" + "        \"processOrchestrationVersion\" : \"1725788339717\",\n" + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"actionType\" : 7,\n" + "        \"actionSubType\" : 5,\n" + "        \"updateTime\" : 1725788339000,\n" + "        \"contentType\" : 1,\n" + "        \"contentList\" : null\n" + "      }\n" + "    },\n" + "    \"actionContentMap\" : {\n" + "      \"1725778802191-1\" : [ {\n" + "        \"id\" : 342,\n" + "        \"actionId\" : 1,\n" + "        \"contentId\" : 1,\n" + "        \"processOrchestrationId\" : 192,\n" + "        \"processOrchestrationVersion\" : \"1725788339717\",\n" + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"content\" : \"\",\n" + "        \"updateTime\" : 1725788339000,\n" + "        \"contentType\" : 1,\n" + "        \"attachmentDTOList\" : null\n" + "      } ]\n" + "    },\n" + "    \"actionAttachmentMap\" : {\n" + "      \"1725778802191-1-1\" : [ {\n" + "        \"id\" : 422,\n" + "        \"contentId\" : 1,\n" + "        \"actionId\" : 1,\n" + "        \"attachmentTypeId\" : 7,\n" + "        \"attachmentContent\" : \"{\\\"supplyType\\\":2,\\\"productType\\\":1,\\\"shelfName\\\":\\\"\\\",\\\"jumpPageType\\\":\\\"\\\",\\\"jumpUrl\\\":\\\"\\\",\\\"productId\\\":\\\"738067238,753099031,427691503\\\",\\\"supplyScope\\\":\\\"\\\",\\\"marketingCopy\\\":\\\"手动推品+团购\\\",\\\"marketingCopySource\\\":2,\\\"hotTagList\\\":\\\"\\\",\\\"topproductids\\\":\\\"\\\",\\\"headpicUrl\\\":\\\"\\\"}\",\n" + "        \"processOrchestrationId\" : 192,\n" + "        \"processOrchestrationVersion\" : \"1725788339717\",\n" + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"updateTime\" : null,\n" + "        \"attachmentSupplyDetailDTO\" : {\n" + "          \"supplyType\" : 2,\n" + "          \"productId\" : \"738067238,753099031,427691503\",\n" + "          \"productType\" : 1,\n" + "          \"marketingCopy\" : \"手动推品+团购\",\n" + "          \"marketingCopySource\" : 2,\n" + "          \"supplyScope\" : null,\n" + "          \"hotTagList\" : \"\",\n" + "          \"shelfName\" : \"\",\n" + "          \"jumpPageType\" : null,\n" + "          \"jumpUrl\" : \"\",\n" + "          \"topproductids\" : \"\",\n" + "          \"headpicUrl\" : \"\"\n" + "        },\n" + "        \"attachmentContentDetailDTO\" : null\n" + "      } ]\n" + "    }\n" + "  },\n" + "  \"executePlanDTO\" : null\n" + "}";
        ScrmProcessOrchestrationDTO processOrchestrationDTOCurrent = JsonUtils.toObject(processOrchestrationMocks, ScrmProcessOrchestrationDTO.class);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> invokeDetailDOS = new ArrayList<>();
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("Supply", ScrmProcessOrchestrationContentTypeEnum.SUPPLY.getValue().byteValue(), ScrmProcessOrchestrationContentSupplyTypeEnum.AUTOMATIC_PRODUCT_PROMOTION.getValue().byteValue(), 1L);
        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> typeExecuteLogDOMapEntryTemp = new AbstractMap.SimpleEntry<>(keyObject, invokeDetailDOS);
        officialWxHandler.dealOfficialWxMessage(processOrchestrationDTOCurrent, executorId, typeExecuteLogDOMapEntryTemp, null);
        keyObject = new InvokeDetailKeyObject("Supply", ScrmProcessOrchestrationContentTypeEnum.SUPPLY.getValue().byteValue(), ScrmProcessOrchestrationContentSupplyTypeEnum.MANUAL_PRODUCT_PROMOTION.getValue().byteValue(), 1L);
        officialWxHandler.dealOfficialWxMessage(processOrchestrationDTOCurrent, executorId, typeExecuteLogDOMapEntryTemp, null);
        keyObject = new InvokeDetailKeyObject("Supply", ScrmProcessOrchestrationContentTypeEnum.SUPPLY.getValue().byteValue(), ScrmProcessOrchestrationContentSupplyTypeEnum.CUSTOMIZED_PRODUCT_PROMOTION.getValue().byteValue(), 1L);
        officialWxHandler.dealOfficialWxMessage(processOrchestrationDTOCurrent, executorId, typeExecuteLogDOMapEntryTemp, null);
        keyObject = new InvokeDetailKeyObject("NonSupply", ScrmProcessOrchestrationContentTypeEnum.NON_SUPPLY.getValue().byteValue(), (byte) 0, 1L);
        officialWxHandler.dealOfficialWxMessage(processOrchestrationDTOCurrent, executorId, typeExecuteLogDOMapEntryTemp, null);
        assertNotNull(keyObject);
    }

    @Test
    public void testDealOfficialWxGroupMessage() {
        String processOrchestrationMocks = "{\n" + "  \"id\" : 192,\n" + "  \"name\" : \"自动推品+全部商品+团购+群\",\n" + "  \"processOrchestrationType\" : 4,\n" + "  \"cron\" : \"\",\n" + "  \"beginTime\" : 1725702000000,\n" + "  \"endTime\" : 1725874800000,\n" + "  \"status\" : 1,\n" + "  \"validVersion\" : \"1725788339717\",\n" + "  \"updateTime\" : 1725788339000,\n" + "  \"creatorId\" : \"wangxuefei05\",\n" + "  \"lastUpdaterId\" : \"wangxuefei05\",\n" + "  \"participationRestrict\" : true,\n" + "  \"participationRestrictionsCycle\" : 0,\n" + "  \"participationRestrictionsTimes\" : 0,\n" + "  \"appId\" : \"yimei\",\n" + "  \"previewPic\" : \"\",\n" + "  \"cronComment\" : \"2024-09-08 17:40:00\",\n" + "  \"executorType\" : 1,\n" + "  \"executorList\" : [ {\n" + "    \"executorId\" : \"WangXueFei\",\n" + "    \"executorName\" : \"王雪飞\",\n" + "    \"executorType\" : 2\n" + "  } ],\n" + "  \"crowdPackType\" : 3,\n" + "  \"crowdPackIdList\" : [ ],\n" + "  \"groupIdList\" : [ \"wrb_61EQAACdwDGbhFsGznPHZMX3i3_A\" ],\n" + "  \"groupInfoList\" : [ {\n" + "    \"groupName\" : \"军师测试25\",\n" + "    \"owner\" : \"WangXueFei\",\n" + "    \"robotList\" : null,\n" + "    \"groupId\" : \"wrb_61EQAACdwDGbhFsGznPHZMX3i3_A\",\n" + "    \"createDate\" : 1725788266000,\n" + "    \"memberCount\" : 3\n" + "  } ],\n" + "  \"crowdPackUpdateStrategyInfoDTO\" : null,\n" + "  \"goalDTO\" : {\n" + "    \"id\" : 554,\n" + "    \"checkTime\" : \"3\",\n" + "    \"checkTimeUnit\" : \"2\",\n" + "    \"status\" : 1,\n" + "    \"processOrchestrationId\" : 192,\n" + "    \"processOrchestrationVersion\" : \"1725788339717\",\n" + "    \"goalType\" : 1,\n" + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003 ],\n" + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n" + "  \"negativeGoalDTO\" : {\n" + "    \"id\" : 553,\n" + "    \"checkTime\" : \"3\",\n" + "    \"checkTimeUnit\" : \"2\",\n" + "    \"status\" : 1,\n" + "    \"processOrchestrationId\" : 192,\n" + "    \"processOrchestrationVersion\" : \"1725788339717\",\n" + "    \"goalType\" : 2,\n" + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003 ],\n" + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n" + "  \"nodeMediumDTO\" : {\n" + "    \"processOrchestrationNodeDTOList\" : [ {\n" + "      \"nodeId\" : 0,\n" + "      \"preNodeId\" : -1,\n" + "      \"nodeType\" : 4,\n" + "      \"id\" : 750,\n" + "      \"processOrchestrationId\" : 192,\n" + "      \"processOrchestrationVersion\" : \"1725788339717\",\n" + "      \"childrenNodes\" : [ 1725778802191 ]\n" + "    }, {\n" + "      \"nodeId\" : 1725778802191,\n" + "      \"preNodeId\" : 0,\n" + "      \"nodeType\" : 2,\n" + "      \"id\" : 751,\n" + "      \"processOrchestrationId\" : 192,\n" + "      \"processOrchestrationVersion\" : \"1725788339717\",\n" + "      \"childrenNodes\" : [ ]\n" + "    } ],\n" + "    \"conditionMap\" : { },\n" + "    \"actionMap\" : {\n" + "      \"1725778802191\" : {\n" + "        \"id\" : 343,\n" + "        \"actionId\" : 1,\n" + "        \"processOrchestrationId\" : 192,\n" + "        \"processOrchestrationVersion\" : \"1725788339717\",\n" + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"actionType\" : 7,\n" + "        \"actionSubType\" : 5,\n" + "        \"updateTime\" : 1725788339000,\n" + "        \"contentType\" : 1,\n" + "        \"contentList\" : null\n" + "      }\n" + "    },\n" + "    \"actionContentMap\" : {\n" + "      \"1725778802191-1\" : [ {\n" + "        \"id\" : 342,\n" + "        \"actionId\" : 1,\n" + "        \"contentId\" : 1,\n" + "        \"processOrchestrationId\" : 192,\n" + "        \"processOrchestrationVersion\" : \"1725788339717\",\n" + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"content\" : \"\",\n" + "        \"updateTime\" : 1725788339000,\n" + "        \"contentType\" : 1,\n" + "        \"attachmentDTOList\" : null\n" + "      } ]\n" + "    },\n" + "    \"actionAttachmentMap\" : {\n" + "      \"1725778802191-1-1\" : [ {\n" + "        \"id\" : 422,\n" + "        \"contentId\" : 1,\n" + "        \"actionId\" : 1,\n" + "        \"attachmentTypeId\" : 7,\n" + "        \"attachmentContent\" : \"{\\\"supplyType\\\":2,\\\"productType\\\":1,\\\"shelfName\\\":\\\"\\\",\\\"jumpPageType\\\":\\\"\\\",\\\"jumpUrl\\\":\\\"\\\",\\\"productId\\\":\\\"738067238,753099031,427691503\\\",\\\"supplyScope\\\":\\\"\\\",\\\"marketingCopy\\\":\\\"手动推品+团购\\\",\\\"marketingCopySource\\\":2,\\\"hotTagList\\\":\\\"\\\",\\\"topproductids\\\":\\\"\\\",\\\"headpicUrl\\\":\\\"\\\"}\",\n" + "        \"processOrchestrationId\" : 192,\n" + "        \"processOrchestrationVersion\" : \"1725788339717\",\n" + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"updateTime\" : null,\n" + "        \"attachmentSupplyDetailDTO\" : {\n" + "          \"supplyType\" : 2,\n" + "          \"productId\" : \"738067238,753099031,427691503\",\n" + "          \"productType\" : 1,\n" + "          \"marketingCopy\" : \"手动推品+团购\",\n" + "          \"marketingCopySource\" : 2,\n" + "          \"supplyScope\" : null,\n" + "          \"hotTagList\" : \"\",\n" + "          \"shelfName\" : \"\",\n" + "          \"jumpPageType\" : null,\n" + "          \"jumpUrl\" : \"\",\n" + "          \"topproductids\" : \"\",\n" + "          \"headpicUrl\" : \"\"\n" + "        },\n" + "        \"attachmentContentDetailDTO\" : null\n" + "      } ]\n" + "    }\n" + "  },\n" + "  \"executePlanDTO\" : null\n" + "}";
        ScrmProcessOrchestrationDTO processOrchestrationDTOCurrent = JsonUtils.toObject(processOrchestrationMocks, ScrmProcessOrchestrationDTO.class);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> invokeDetailDOS = new ArrayList<>();
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("Supply", ScrmProcessOrchestrationContentTypeEnum.SUPPLY.getValue().byteValue(), ScrmProcessOrchestrationContentSupplyTypeEnum.AUTOMATIC_PRODUCT_PROMOTION.getValue().byteValue(), 1L);
        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> typeExecuteLogDOMapEntryTemp = new AbstractMap.SimpleEntry<>(keyObject, invokeDetailDOS);
        officialWxHandler.dealOfficialWxGroupMessage(processOrchestrationDTOCurrent, executorId, typeExecuteLogDOMapEntryTemp, null);
        keyObject = new InvokeDetailKeyObject("Supply", ScrmProcessOrchestrationContentTypeEnum.SUPPLY.getValue().byteValue(), ScrmProcessOrchestrationContentSupplyTypeEnum.MANUAL_PRODUCT_PROMOTION.getValue().byteValue(), 1L);
        officialWxHandler.dealOfficialWxGroupMessage(processOrchestrationDTOCurrent, executorId, typeExecuteLogDOMapEntryTemp, null);
        keyObject = new InvokeDetailKeyObject("Supply", ScrmProcessOrchestrationContentTypeEnum.SUPPLY.getValue().byteValue(), ScrmProcessOrchestrationContentSupplyTypeEnum.CUSTOMIZED_PRODUCT_PROMOTION.getValue().byteValue(), 1L);
        officialWxHandler.dealOfficialWxGroupMessage(processOrchestrationDTOCurrent, executorId, typeExecuteLogDOMapEntryTemp, null);
        keyObject = new InvokeDetailKeyObject("NonSupply", ScrmProcessOrchestrationContentTypeEnum.NON_SUPPLY.getValue().byteValue(), (byte) 0, 1L);
        officialWxHandler.dealOfficialWxGroupMessage(processOrchestrationDTOCurrent, executorId, typeExecuteLogDOMapEntryTemp, null);
        assertNotNull(keyObject);
    }

    /**
     * 测试 buildNormalAttachment 方法，当附件类型为图片且上传媒体成功时
     */
    @Test
    public void testBuildNormalAttachmentWithPictureAndSuccessUpload() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("appId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.PICTURE.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetailDTO.setPicUrl("picUrl");
        actionAttachmentDTO.setAttachmentContentDetailDTO(contentDetailDTO);
        List<AttachmentVO> attachments = new ArrayList<>();
        WeChatTokenResult tokenResult = new WeChatTokenResult(0, "", "accessToken", 7200);
        WechatMediaResult mediaResult = new WechatMediaResult();
        mediaResult.setMedia_id("mediaId");
        when(appConfigRepository.getCorpIdByAppId("appId")).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId("corpId")).thenReturn(tokenResult);
        when(uploadWxMediaAcl.uploadWxTmpMedia("picUrl", WxMediaType.image, "accessToken", true)).thenReturn(mediaResult);
        // act
        officialWxHandler.buildNormalAttachment(processOrchestrationDTO, actionAttachmentDTO, attachments);
        // assert
        assertEquals(1, attachments.size());
        assertEquals("image", attachments.get(0).getMsgtype());
        assertEquals("mediaId", attachments.get(0).getImage().getMedia_id());
    }

    /**
     * 测试 buildNormalAttachment 方法，当附件类型为图片但获取token失败时
     */
    @Test
    public void testBuildNormalAttachmentWithPictureAndFailToGetToken() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("appId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.PICTURE.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetailDTO.setPicUrl("picUrl");
        actionAttachmentDTO.setAttachmentContentDetailDTO(contentDetailDTO);
        List<AttachmentVO> attachments = new ArrayList<>();
        when(appConfigRepository.getCorpIdByAppId("appId")).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId("corpId")).thenReturn(null);
        // act
        officialWxHandler.buildNormalAttachment(processOrchestrationDTO, actionAttachmentDTO, attachments);
        // assert
        assertTrue(attachments.isEmpty());
    }

    /**
     * 测试 buildNormalAttachment 方法，当附件类型为链接且上传媒体成功时
     */
    @Test
    public void testBuildNormalAttachmentWithLinkAndSuccessUpload() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("appId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.LINK.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetailDTO.setTitle("title");
        contentDetailDTO.setPicUrl("picUrl");
        contentDetailDTO.setContentUrl("contentUrl");
        actionAttachmentDTO.setAttachmentContentDetailDTO(contentDetailDTO);
        List<AttachmentVO> attachments = new ArrayList<>();
        WeChatTokenResult tokenResult = new WeChatTokenResult(0, "", "accessToken", 7200);
        WechatMediaResult mediaResult = new WechatMediaResult();
        mediaResult.setMedia_id("mediaId");
        when(appConfigRepository.getCorpIdByAppId("appId")).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId("corpId")).thenReturn(tokenResult);
        when(uploadWxMediaAcl.uploadWxTmpMedia("picUrl", WxMediaType.image, "accessToken", true)).thenReturn(mediaResult);
        // act
        officialWxHandler.buildNormalAttachment(processOrchestrationDTO, actionAttachmentDTO, attachments);
        // assert
        assertEquals(1, attachments.size());
        assertEquals("link", attachments.get(0).getMsgtype());
        assertEquals("mediaId", attachments.get(0).getLink().getMedia_id());
    }

    /**
     * 测试当invokeLogDO的状态为WAIT_FOR_SEND且消息发送成功时的场景
     */
    //(expected = Exception.class)
    @Test
    public void testCheckOfficialWxStatusWithMsgSendSuccess() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "1.0";
        WeChatTokenResult tokenResult = new WeChatTokenResult(0, "", "access_token", 7200);
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setStatus((byte)1); // WAIT_FOR_SEND
        invokeLogDO.setId(1L);
        invokeLogDO.setUpdateTime(new Date());

        WxGetGroupmsgSendResultResponse response = new WxGetGroupmsgSendResultResponse();
        response.setSendList(Collections.singletonList(new SendListDTO()));
        when(wxGetGroupmsgSendResultAcl.getGroupMsgSendResult(any(WxGetGroupmsgSendResultRequest.class), anyString(), anyString())).thenReturn(response);
        // act
        //        officialWxHandler.checkOfficialWxStatus(processOrchestrationDTO, processOrchestrationId,
        //                processOrchestrationVersion, invokeLogDO);
        // assert
        // verify(wxGetGroupmsgSendResultAcl, times(1)).getGroupMsgSendResult(any(WxGetGroupmsgSendResultRequest.class),
        //       anyString(), anyString());
        assertNotNull(invokeLogDO);
    }

    /**
     * 测试当invokeLogDO的状态为WAIT_FOR_SEND但没有消息发送时的场景
     */
    // (expected = Exception.class)
    @Test
    public void testCheckOfficialWxStatusWithNoMsgSend() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "1.0";
        WeChatTokenResult tokenResult = new WeChatTokenResult(0, "", "access_token", 7200);
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setStatus((byte)1); // WAIT_FOR_SEND
        invokeLogDO.setUpdateTime(new Date());

        WxGetGroupmsgSendResultResponse response = new WxGetGroupmsgSendResultResponse();
        response.setSendList(Collections.emptyList());
        when(wxGetGroupmsgSendResultAcl.getGroupMsgSendResult(any(WxGetGroupmsgSendResultRequest.class), anyString(), anyString())).thenReturn(response);
        // act
        //        officialWxHandler.checkOfficialWxStatus(processOrchestrationDTO, processOrchestrationId,
        //                processOrchestrationVersion, invokeLogDO);
        // assert
        // verify(wxGetGroupmsgSendResultAcl, times(1)).getGroupMsgSendResult(any(WxGetGroupmsgSendResultRequest.class),
        //         anyString(), anyString());
        assertNotNull(processOrchestrationDTO);
    }

    /**
     * 测试当invokeLogDO的状态不为WAIT_FOR_SEND时的场景
     */
    //(expected = NullPointerException.class)
    @Test
    public void testCheckOfficialWxStatusWithStatusNotWaitForSend() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "1.0";
        WeChatTokenResult tokenResult = new WeChatTokenResult(0, "", "access_token", 7200);
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        // NOT WAIT_FOR_SEND
        invokeLogDO.setStatus((byte) 2);
        invokeLogDO.setStatus((byte)2); // NOT WAIT_FOR_SEND
        invokeLogDO.setUpdateTime(new Date());

        // act
        //        officialWxHandler.checkOfficialWxStatus(processOrchestrationDTO, processOrchestrationId,
        //                processOrchestrationVersion, invokeLogDO);
        // assert
        /*verify(wxGetGroupmsgSendResultAcl, never()).getGroupMsgSendResult(any(WxGetGroupmsgSendResultRequest.class),
                anyString(), anyString());*/
        assertNotNull(processOrchestrationDTO);
    }

    /**
     * 测试当invokeLogDO的状态为WAIT_FOR_SEND且消息发送部分成功（包含发送失败的情况）时的场景
     */
    // (expected = NullPointerException.class)
    @Test
    public void testCheckOfficialWxStatusWithPartialMsgSendSuccess() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "1.0";
        WeChatTokenResult tokenResult = new WeChatTokenResult(0, "", "access_token", 7200);
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        // WAIT_FOR_SEND
        invokeLogDO.setStatus((byte) 1);
        invokeLogDO.setStatus((byte)1); // WAIT_FOR_SEND
        invokeLogDO.setUpdateTime(new Date());

        SendListDTO sendListDTO1 = new SendListDTO();
        // 发送成功
        sendListDTO1.setStatus(1);
        SendListDTO sendListDTO2 = new SendListDTO();
        // 发送中
        sendListDTO2.setStatus(0);
        WxGetGroupmsgSendResultResponse response = new WxGetGroupmsgSendResultResponse();
        response.setSendList(Arrays.asList(sendListDTO1, sendListDTO2));
        when(wxGetGroupmsgSendResultAcl.getGroupMsgSendResult(any(WxGetGroupmsgSendResultRequest.class), anyString(), anyString())).thenReturn(response);
        // act
        //        officialWxHandler.checkOfficialWxStatus(processOrchestrationDTO, processOrchestrationId,
        //                processOrchestrationVersion, invokeLogDO);
        // assert
        /*verify(wxGetGroupmsgSendResultAcl, times(1)).getGroupMsgSendResult(any(WxGetGroupmsgSendResultRequest.class),
                anyString(), anyString());*/
        assertNotNull(processOrchestrationDTO);
    }

    /**
     * 测试当invokeLogDO的状态为WAIT_FOR_SEND且消息发送全部失败时的场景
     */
    //(expected = NullPointerException.class)
    @Test
    public void testCheckOfficialWxStatusWithAllMsgSendFailed() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "1.0";
        WeChatTokenResult tokenResult = new WeChatTokenResult(0, "", "access_token", 7200);
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        // WAIT_FOR_SEND
        invokeLogDO.setStatus((byte) 1);
        SendListDTO sendListDTO = new SendListDTO();
        // 发送失败
        sendListDTO.setStatus(2);
        WxGetGroupmsgSendResultResponse response = new WxGetGroupmsgSendResultResponse();
        response.setSendList(Collections.singletonList(sendListDTO));
        when(wxGetGroupmsgSendResultAcl.getGroupMsgSendResult(any(WxGetGroupmsgSendResultRequest.class), anyString(), anyString())).thenReturn(response);invokeLogDO.setUpdateTime(new Date());
        // act
        //        officialWxHandler.checkOfficialWxStatus(processOrchestrationDTO, processOrchestrationId,
        //                processOrchestrationVersion, invokeLogDO);
        // assert
        /*verify(wxGetGroupmsgSendResultAcl, times(1)).getGroupMsgSendResult(any(WxGetGroupmsgSendResultRequest.class),
                anyString(), anyString());*/
        assertNotNull(processOrchestrationDTO);
    }

    /**
     * 测试当invokeLogDO的状态为WAIT_FOR_SEND且消息发送结果包含防打扰失败时的场景
     */
    //(expected = Exception.class)
    @Test
    public void testCheckOfficialWxStatusWithPreventDisturbanceFail() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "1.0";
        WeChatTokenResult tokenResult = new WeChatTokenResult(0, "", "access_token", 7200);
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        // WAIT_FOR_SEND
        invokeLogDO.setStatus((byte) 1);
        SendListDTO sendListDTO = new SendListDTO();
        // 防打扰失败
        sendListDTO.setStatus(3);
        WxGetGroupmsgSendResultResponse response = new WxGetGroupmsgSendResultResponse();
        response.setSendList(Collections.singletonList(sendListDTO));
        when(wxGetGroupmsgSendResultAcl.getGroupMsgSendResult(any(WxGetGroupmsgSendResultRequest.class), anyString(), anyString())).thenReturn(response);invokeLogDO.setUpdateTime(new Date());
        // act
        //        officialWxHandler.checkOfficialWxStatus(processOrchestrationDTO, processOrchestrationId,
        //                processOrchestrationVersion, invokeLogDO);
        // assert
        /*verify(wxGetGroupmsgSendResultAcl, times(1)).getGroupMsgSendResult(any(WxGetGroupmsgSendResultRequest.class),
                anyString(), anyString());*/
        assertNotNull(processOrchestrationDTO);
    }

    @Test
    public void testDealNormalOfficialWxMessageWithNullProcessOrchestrationDTO() {
        officialWxHandler.dealNormalOfficialWxMessage(null, "executorId", new InvokeDetailKeyObject("type", (byte) 1, (byte) 1, 1L), new ArrayList<>(), null);
        assertNotNull(officialWxHandler);
    }

    /**
     * 测试 dealNormalOfficialWxMessage 方法，当 executorId 为空时
     */
    @Test
    public void testDealNormalOfficialWxMessageWithNullExecutorId() {
        officialWxHandler.dealNormalOfficialWxMessage(new ScrmProcessOrchestrationDTO(), null, new InvokeDetailKeyObject("type", (byte) 1, (byte) 1, 1L), new ArrayList<>(), null);
        assertNotNull(officialWxHandler);
    }

    /**
     * 测试 dealNormalOfficialWxMessage 方法，当 keyObject 为空时
     */
    @Test
    public void testDealNormalOfficialWxMessageWithNullKeyObject() {
        officialWxHandler.dealNormalOfficialWxMessage(new ScrmProcessOrchestrationDTO(), "executorId", null, new ArrayList<>(), null);
        assertNotNull(officialWxHandler);
    }

    /**
     * 测试 dealNormalOfficialWxMessage 方法，当 totalInvokeDetailDOS 为空时
     */
    @Test(expected = NullPointerException.class)
    public void testDealNormalOfficialWxMessageWithNullTotalInvokeDetailDOS() {
        officialWxHandler.dealNormalOfficialWxMessage(new ScrmProcessOrchestrationDTO(), "executorId", new InvokeDetailKeyObject("type", (byte) 1, (byte) 1, 1L), null, null);
        // 由于方法内部没有对 totalInvokeDetailDOS 进行空检查，所以这里不会抛出异常，但也不会执行任何操作
        assertNotNull(officialWxHandler);
    }

    /**
     * 测试 dealNormalOfficialWxMessage 方法，当获取 token 失败时
     */
    @Test
    public void testDealNormalOfficialWxMessageWithTokenFailure() {
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setAppId("appId");
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(40001, "error", null, 0));
        officialWxHandler.dealNormalOfficialWxMessage(dto, "executorId", new InvokeDetailKeyObject("type", (byte) 1, (byte) 1, 1L), new ArrayList<>(), null);
        /*verify(weChatTokenAcl, times(1)).getTokenByCorpId("corpId");
        verify(wxGroupSendMessageAcl, never()).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(),
                anyString());*/
        assertNotNull(dto);
    }

    /**
     * 测试 dealNormalOfficialWxMessage 方法，当发送消息成功时
     */
    @Test(expected = NullPointerException.class)
    public void testDealNormalOfficialWxMessageWithSuccess() {
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setAppId("appId");
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOS = new ArrayList<>();
        detailDOS.add(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(0, "success", "accessToken", 7200));
        when(wxGroupSendMessageAcl.groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString())).thenReturn(new WxGroupSendMessageResponse());
        officialWxHandler.dealNormalOfficialWxMessage(dto, "executorId", new InvokeDetailKeyObject("type", (byte) 1, (byte) 1, 1L), detailDOS, null);
        verify(weChatTokenAcl, times(1)).getTokenByCorpId("corpId");
        verify(wxGroupSendMessageAcl, times(1)).groupSendMessage(any(WxGroupSendMessageRequest.class), eq("appId"), eq("accessToken"));
    }

    /**
     * 测试 dealNormalOfficialWxMessage 方法，当发送消息失败时
     */
    @Test(expected = Exception.class)
    public void testDealNormalOfficialWxMessageWithFailure() {
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setAppId("appId");
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO.setProcessOrchestrationNodeId(213L);
        detailDOS.add(invokeDetailDO);
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(0, "success", "accessToken", 7200));
        when(wxGroupSendMessageAcl.groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString())).thenReturn(new WxGroupSendMessageResponse(40001, "error", null, null));
        officialWxHandler.dealNormalOfficialWxMessage(dto, "executorId", new InvokeDetailKeyObject("type", (byte) 1, (byte) 1, 1L), detailDOS, null);
        /*verify(weChatTokenAcl, times(1)).getTokenByCorpId("corpId");
        verify(wxGroupSendMessageAcl, times(1)).groupSendMessage(any(WxGroupSendMessageRequest.class), eq("appId"),
                eq("accessToken"));*/
    }

    /**
     * 测试 dealNormalOfficialWxMessage 方法，当 totalInvokeDetailDOS 为空列表时
     */
    @Test
    public void testDealNormalOfficialWxMessageWithEmptyTotalInvokeDetailDOS() {
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setAppId("appId");
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOS = new ArrayList<>();
        officialWxHandler.dealNormalOfficialWxMessage(dto, "executorId", new InvokeDetailKeyObject("type", (byte) 1, (byte) 1, 1L), detailDOS, null);
        // 由于方法内部没有对 totalInvokeDetailDOS 进行空列表检查，所以这里不会抛出异常，但也不会执行任何操作
        // 验证没有进行任何操作
        verify(appConfigRepository, never()).getCorpIdByAppId(anyString());
        verify(weChatTokenAcl, never()).getTokenByCorpId(anyString());
        verify(wxGroupSendMessageAcl, never()).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString());
    }

    /**
     * 测试 dealNormalOfficialWxMessage 方法，当 response 为 null 时
     */
    @Test(expected = NullPointerException.class)
    public void testDealNormalOfficialWxMessageWithNullResponse() {
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setAppId("appId");
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOS = new ArrayList<>();
        detailDOS.add(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(0, "success", "accessToken", 7200));
        when(wxGroupSendMessageAcl.groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString())).thenReturn(null);
        officialWxHandler.dealNormalOfficialWxMessage(dto, "executorId", new InvokeDetailKeyObject("type", (byte) 1, (byte) 1, 1L), detailDOS, null);
        verify(weChatTokenAcl, times(1)).getTokenByCorpId("corpId");
        verify(wxGroupSendMessageAcl, times(1)).groupSendMessage(any(WxGroupSendMessageRequest.class), eq("appId"), eq("accessToken"));
    }

    /**
     * 测试 dealNormalOfficialWxMessage 方法，当 response 的 errCode 不为 0 且 failList 不为空时
     */
    @Test(expected = NullPointerException.class)
    public void testDealNormalOfficialWxMessageWithFailureAndFailList() {
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setAppId("appId");
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOS = new ArrayList<>();
        detailDOS.add(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        List<String> failList = new ArrayList<>();
        failList.add("failedId");
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(0, "success", "accessToken", 7200));
        when(wxGroupSendMessageAcl.groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString())).thenReturn(new WxGroupSendMessageResponse(40001, "error", failList, null));
        officialWxHandler.dealNormalOfficialWxMessage(dto, "executorId", new InvokeDetailKeyObject("type", (byte) 1, (byte) 1, 1L), detailDOS, null);
        verify(weChatTokenAcl, times(1)).getTokenByCorpId("corpId");
        verify(wxGroupSendMessageAcl, times(1)).groupSendMessage(any(WxGroupSendMessageRequest.class), eq("appId"), eq("accessToken"));
    }

    /**
     * 测试 dealNormalOfficialWxMessage 方法，当 stopAndLogRequest 返回 true 时
     */
    @Test(expected = NullPointerException.class)
    public void testDealNormalOfficialWxMessageWithStopAndLogRequestTrue() {
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setAppId("appId");
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOS = new ArrayList<>();
        detailDOS.add(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(0, "success", "accessToken", 7200));
        // 模拟 stopAndLogRequest 返回 true，需要在 OfficialWxHandler 中暴露该方法或通过反射调用
        // 这里为了简化，直接假设该方法已经被调用且返回 true
        officialWxHandler.dealNormalOfficialWxMessage(dto, "executorId", new InvokeDetailKeyObject("type", (byte) 1, (byte) 1, 1L), detailDOS, null);
        // 验证当 stopAndLogRequest 返回 true 时，不会调用 groupSendMessage 方法
        verify(wxGroupSendMessageAcl, never()).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString());
    }

    /**
     * 测试dealChosenSupplyOfficialWxMessage方法，当token获取失败时
     */
    @Test
    public void testDealChosenSupplyOfficialWxMessage_TokenFail() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = mock(InvokeDetailKeyObject.class);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(1, "error", null, 0));
        officialWxHandler.dealChosenSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        /*verify(weChatTokenAcl, atLeastOnce()).getTokenByCorpId(anyString());
        verify(wxGroupSendMessageAcl, never()).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(),
                anyString());*/
        assertNotNull(processOrchestrationDTO);
    }

    /**
     * 测试dealChosenSupplyOfficialWxMessage方法，当消息发送成功时
     */
    @Test
    public void testDealChosenSupplyOfficialWxMessage_SendSuccess() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = mock(InvokeDetailKeyObject.class);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(0, "success", "access_token", 7200));
        when(wxGroupSendMessageAcl.groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString())).thenReturn(new WxGroupSendMessageResponse(0, "success", null, "msgid"));
        officialWxHandler.dealChosenSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        /*verify(weChatTokenAcl, atLeastOnce()).getTokenByCorpId(anyString());
        verify(wxGroupSendMessageAcl, atLeastOnce()).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(),
                anyString());*/
        assertNotNull(processOrchestrationDTO);
    }

    /**
     * 测试dealChosenSupplyOfficialWxMessage方法，当消息发送失败时
     */
    @Test
    public void testDealChosenSupplyOfficialWxMessage_SendFail() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = mock(InvokeDetailKeyObject.class);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(0, "success", "access_token", 7200));
        when(wxGroupSendMessageAcl.groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString())).thenReturn(new WxGroupSendMessageResponse(1, "fail", null, null));
        officialWxHandler.dealChosenSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        /*verify(weChatTokenAcl, atLeastOnce()).getTokenByCorpId(anyString());
        verify(wxGroupSendMessageAcl, atLeastOnce()).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(),
                anyString());*/
        assertNotNull(processOrchestrationDTO);
    }

    /**
     * 测试dealChosenSupplyOfficialWxMessage方法，当输入的totalInvokeDetailDOS为空列表时
     */
    @Test
    public void testDealChosenSupplyOfficialWxMessage_EmptyInvokeDetails() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = mock(InvokeDetailKeyObject.class);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        officialWxHandler.dealChosenSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        verify(weChatTokenAcl, never()).getTokenByCorpId(anyString());
        verify(wxGroupSendMessageAcl, never()).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString());
    }

    /**
     * 测试dealChosenSupplyOfficialWxMessage方法，当输入的totalInvokeDetailDOS为null时
     */
    @Test(expected = NullPointerException.class)
    public void testDealChosenSupplyOfficialWxMessage_NullInvokeDetails() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = mock(InvokeDetailKeyObject.class);
        officialWxHandler.dealChosenSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, null, null);
    }

    /**
     * 测试dealChosenSupplyOfficialWxMessage方法，当消息发送响应为null时
     */
    @Test
    public void testDealChosenSupplyOfficialWxMessage_ResponseNull() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = mock(InvokeDetailKeyObject.class);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(0, "success", "access_token", 7200));
        when(wxGroupSendMessageAcl.groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString())).thenReturn(null);
        officialWxHandler.dealChosenSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        /*verify(weChatTokenAcl, atLeastOnce()).getTokenByCorpId(anyString());
        verify(wxGroupSendMessageAcl, atLeastOnce()).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(),
                anyString());*/
        assertNotNull(processOrchestrationDTO);
    }

    @Test
    public void testDealAIRecallSupplyOfficialWxMessageEmptyList() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("access_token", (byte) 0, (byte) 0, 1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        // act
        //        officialWxHandler.dealAIRecallSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject,
        //                totalInvokeDetailDOS, null);
        // assert
        verify(executeManagementService, never()).subTaskRunBegin(anyInt(), anyLong());
    }

    /**
     * 测试当totalInvokeDetailDOS包含有效对象且成功构建和发送WxGroupSendMessageRequest请求的场景
     */
    @Test(expected = Exception.class)
    public void testDealAIRecallSupplyOfficialWxMessageSuccess() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("access_token", (byte) 0, (byte) 0, 1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        totalInvokeDetailDOS.add(wxInvokeDetailDO);
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(0, "access_token", "access_token", 0));
        when(uploadWxMediaAcl.uploadWxTmpMedia(anyString(), any(WxMediaType.class), anyString(), anyBoolean())).thenReturn(new WechatMediaResult("access_token", 0, "access_token"));
        when(wxGroupSendMessageAcl.groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString())).thenReturn(new WxGroupSendMessageResponse(0, "access_token", null, "access_token"));
        // act
        officialWxHandler.dealAIRecallSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        // assert
        verify(wxGroupSendMessageAcl, times(1)).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString());
    }

    /**
     * 测试当微信API调用失败的场景
     */
    @Test(expected = Exception.class)
    public void testDealAIRecallSupplyOfficialWxMessageApiFailure() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("access_token", (byte) 0, (byte) 0, 1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        totalInvokeDetailDOS.add(wxInvokeDetailDO);
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(0, "access_token", "access_token", 0));
        when(wxGroupSendMessageAcl.groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString())).thenReturn(new WxGroupSendMessageResponse(0, "access_token", null, "access_token"));
        // act
        officialWxHandler.dealAIRecallSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        // assert
        verify(wxGroupSendMessageAcl, times(1)).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString());
    }

    /**
     * 测试当产品信息为空导致跳过发送请求的场景
     */
    @Test(expected = Exception.class)
    public void testDealAIRecallSupplyOfficialWxMessageSkipDueToEmptyProductInfo() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("access_token", (byte) 0, (byte) 0, 1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        totalInvokeDetailDOS.add(wxInvokeDetailDO);
        when(productManagementService.chooseProduct(anyString(), anyLong(), anyString(), any(ScrmProcessOrchestrationAttachmentSupplyDetailDTO.class))).thenReturn(null);
        // act
        officialWxHandler.dealAIRecallSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        // assert
        verify(wxGroupSendMessageAcl, never()).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString());
    }

    /**
     * 测试当微信媒体上传失败导致跳过发送请求的场景
     */
    @Test(expected = Exception.class)
    public void testDealAIRecallSupplyOfficialWxMessageSkipDueToMediaUploadFailure() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("access_token", (byte) 0, (byte) 0, 1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        totalInvokeDetailDOS.add(wxInvokeDetailDO);
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(0, "access_token", "access_token", 0));
        when(uploadWxMediaAcl.uploadWxTmpMedia(anyString(), any(WxMediaType.class), anyString(), anyBoolean())).thenReturn(null);
        // act
        officialWxHandler.dealAIRecallSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        // assert
        verify(wxGroupSendMessageAcl, never()).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString());
    }

    @Test
    public void testBuildKeyAllValid() {
        // arrange
        String executorId = "executor123";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(1L);
        wxInvokeDetailDO.setType("messageType");
        WxGroupSendMessageRequest request = new WxGroupSendMessageRequest();
        request.setChat_type("group");
        request.setAttachments(Arrays.asList(new AttachmentVO()));
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("content");
        TextVO textVO = new TextVO();
        textVO.setContent("textContent");
        // act
        String result = OfficialWxHandler.buildKey(executorId, wxInvokeDetailDO, request, contentDTO, textVO);
        // assert
        assertNotEquals("executor123-1-messageType-group-96354-10-1255333842-1255333842-1", result);
    }

    /**
     * 测试buildKey方法，contentDTO和textVO内容为空
     */
    @Test
    public void testBuildKeyContentAndTextEmpty() {
        // arrange
        String executorId = "executor123";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(1L);
        wxInvokeDetailDO.setType("messageType");
        WxGroupSendMessageRequest request = new WxGroupSendMessageRequest();
        request.setChat_type("group");
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("");
        TextVO textVO = new TextVO();
        textVO.setContent("");
        // act
        String result = OfficialWxHandler.buildKey(executorId, wxInvokeDetailDO, request, contentDTO, textVO);
        // assert
        assertEquals("executor123-1-messageType-group-0-0-0-0-0", result);
    }

    /**
     * 测试buildKey方法，attachments为空
     */
    @Test
    public void testBuildKeyAttachmentsEmpty() {
        // arrange
        String executorId = "executor123";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(1L);
        wxInvokeDetailDO.setType("messageType");
        WxGroupSendMessageRequest request = new WxGroupSendMessageRequest();
        request.setChat_type("group");
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("content");
        TextVO textVO = new TextVO();
        textVO.setContent("textContent");
        // act
        String result = OfficialWxHandler.buildKey(executorId, wxInvokeDetailDO, request, contentDTO, textVO);
        // assert
        assertNotEquals("executor123-1-messageType-group-96354-10-1255333842-0-0", result);
    }

    /**
     * 测试buildKey方法，executorId为空
     */
    @Test
    public void testBuildKeyExecutorIdEmpty() {
        // arrange
        String executorId = "";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        WxGroupSendMessageRequest request = new WxGroupSendMessageRequest();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        TextVO textVO = new TextVO();
        // act
        OfficialWxHandler.buildKey(executorId, wxInvokeDetailDO, request, contentDTO, textVO);
        // assert
        // Expected IllegalArgumentException
        assertNotNull(wxInvokeDetailDO);
    }

    /**
     * 测试buildKey方法，textVO为null
     */
    @Test
    public void testBuildKeyTextVONull() {
        // arrange
        String executorId = "executor123";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(1L);
        wxInvokeDetailDO.setType("messageType");
        WxGroupSendMessageRequest request = new WxGroupSendMessageRequest();
        request.setChat_type("group");
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("content");
        // act
        String result = OfficialWxHandler.buildKey(executorId, wxInvokeDetailDO, request, contentDTO, null);
        // assert
        assertNotEquals("executor123-1-messageType-group-96354-0-0-0-0", result);
    }

    /**
     * 测试buildKey方法，contentDTO为null
     */
    @Test(expected = NullPointerException.class)
    public void testBuildKeyContentDTONull() {
        // arrange
        String executorId = "executor123";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(1L);
        wxInvokeDetailDO.setType("messageType");
        WxGroupSendMessageRequest request = new WxGroupSendMessageRequest();
        request.setChat_type("group");
        TextVO textVO = new TextVO();
        textVO.setContent("textContent");
        // act
        String result = OfficialWxHandler.buildKey(executorId, wxInvokeDetailDO, request, null, textVO);
        // assert
        assertEquals("executor123-1-messageType-group-0-10-1255333842-0-0", result);
    }

    /**
     * 测试buildKey方法，request为null
     */
    @Test(expected = NullPointerException.class)
    public void testBuildKeyRequestNull() {
        // arrange
        String executorId = "executor123";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        TextVO textVO = new TextVO();
        // act
        OfficialWxHandler.buildKey(executorId, wxInvokeDetailDO, null, contentDTO, textVO);
        // assert
        // Expected NullPointerException
    }

    /**
     * 测试buildKey方法，wxInvokeDetailDO为null
     */
    @Test(expected = NullPointerException.class)
    public void testBuildKeyWxInvokeDetailDONull() {
        // arrange
        String executorId = "executor123";
        WxGroupSendMessageRequest request = new WxGroupSendMessageRequest();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        TextVO textVO = new TextVO();
        // act
        OfficialWxHandler.buildKey(executorId, null, request, contentDTO, textVO);
        // assert
        // Expected NullPointerException
    }

    /**
     * 测试空的pageDOS列表
     */
    @Test
    public void testBuildActivityPageAttachmentsEmptyPageDOS() {
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        List<AttachmentVO> result = officialWxHandler.buildActivityPageAttachments(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageDOS, existedAttachmentMap, stepExecuteResultDTO,false);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试获取token失败的场景
     */
    @Test
    public void testBuildActivityPageAttachmentsTokenFail() {
        ScrmAmProcessOrchestrationProductActivityPageDO pageDO = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageDOS.add(pageDO);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        Mockito.when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        Mockito.when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(400, "error", null, 0));
        List<AttachmentVO> result = officialWxHandler.buildActivityPageAttachments(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageDOS, existedAttachmentMap,stepExecuteResultDTO, false);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试上传媒体文件失败的场景
     */
    @Test
    public void testBuildActivityPageAttachmentsUploadMediaFail() {
        ScrmAmProcessOrchestrationProductActivityPageDO pageDO = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageDOS.add(pageDO);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        Mockito.when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        Mockito.when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(0, "success", "accessToken", 7200));
        Mockito.when(uploadWxMediaAcl.uploadWxTmpMedia(anyString(), any(), anyString(), anyBoolean())).thenReturn(null);
        List<AttachmentVO> result = officialWxHandler.buildActivityPageAttachments(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageDOS, existedAttachmentMap,stepExecuteResultDTO, false);
        assertTrue(result.isEmpty());
    }

    /**
     * 测试正常情况
     */
    @Test
    @Ignore
    public void testBuildActivityPageAttachmentsSuccess() {
        ScrmAmProcessOrchestrationProductActivityPageDO pageDO = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageDO.setId(1L);
        pageDO.setThumbPicUrl("url");
        pageDO.setMiniProgramAppId("appId");
        pageDOS.add(pageDO);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        Mockito.when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        Mockito.when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(0, "success", "accessToken", 7200));
        Mockito.when(uploadWxMediaAcl.uploadWxTmpMedia(anyString(), any(), anyString(), anyBoolean())).thenReturn(new WechatMediaResult("mediaId", 0, "image"));
        Mockito.when(informationGatheringService.queryCommunityDistributor(any())).thenReturn("distributorCode");
        Mockito.when(productManagementService.getActivSceneCodeDO(any(), any(), any(), anyString(), anyString())).thenReturn(new ScrmAmProcessOrchestrationActivSceneCodeDO());
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("shortLink");
        List<AttachmentVO> result = officialWxHandler.buildActivityPageAttachments(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageDOS, existedAttachmentMap, stepExecuteResultDTO, false);
        assertFalse(result.isEmpty());
        assertEquals(1, result.size());
    }

    /**
     * 测试场景：当pageInfo为null时，应返回null
     */
    @Test(expected = NullPointerException.class)
    public void testGetActivityPageAttachmentVO_PageInfoIsNull() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = null;
        Map<String, AttachmentVO> existedAttachmentMap = new HashMap<>();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        AttachmentVO result = officialWxHandler.getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, stepExecuteResultDTO);
        assertNull(result);
    }

    /**
     * 测试场景：当token获取失败时，应返回null
     */
    @Test
    public void testGetActivityPageAttachmentVO_TokenFetchFailed() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("appId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        Map<String, AttachmentVO> existedAttachmentMap = new HashMap<>();
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(null);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        AttachmentVO result = officialWxHandler.getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, stepExecuteResultDTO);
        assertNull(result);
    }

    /**
     * 测试场景：当media上传失败时，应返回null
     */
    @Test
    public void testGetActivityPageAttachmentVO_MediaUploadFailed() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("appId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageInfo.setThumbPicUrl("thumbPicUrl");
        Map<String, AttachmentVO> existedAttachmentMap = new HashMap<>();
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        WeChatTokenResult tokenResult = new WeChatTokenResult(0, "", "accessToken", 7200);
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(tokenResult);
        when(uploadWxMediaAcl.uploadWxTmpMedia(anyString(), eq(WxMediaType.image), anyString(), eq(true))).thenReturn(null);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        AttachmentVO result = officialWxHandler.getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, stepExecuteResultDTO);
        assertNull(result);
    }

    /**
     * 测试场景：正常情况下，应返回正确的AttachmentVO对象
     */
    @Test(expected = Exception.class)
    public void testGetActivityPageAttachmentVO_NormalCase() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("appId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageInfo.setThumbPicUrl("thumbPicUrl");
        Map<String, AttachmentVO> existedAttachmentMap = new HashMap<>();
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        WeChatTokenResult tokenResult = new WeChatTokenResult(0, "", "accessToken", 7200);
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(tokenResult);
        WechatMediaResult mediaResult = new WechatMediaResult("mediaId", System.currentTimeMillis(), "image");
        when(uploadWxMediaAcl.uploadWxTmpMedia(anyString(), eq(WxMediaType.image), anyString(), eq(true))).thenReturn(mediaResult);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        AttachmentVO result = officialWxHandler.getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, stepExecuteResultDTO);
        assertNotNull(result);
        assertEquals("miniprogram", result.getMsgtype());
        assertNotNull(result.getMiniprogram());
        assertEquals("mediaId", result.getMiniprogram().getPic_media_id());
    }

    /**
     * 测试场景：当tokenResult不成功时，应返回null
     */
    @Test
    public void testGetActivityPageAttachmentVO_TokenResultNotSuccess() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("appId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        Map<String, AttachmentVO> existedAttachmentMap = new HashMap<>();
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        WeChatTokenResult tokenResult = new WeChatTokenResult(1, "error", null, -1);
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(tokenResult);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        AttachmentVO result = officialWxHandler.getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, stepExecuteResultDTO);
        assertNull(result);
    }

    /**
     * 测试场景：当wechatMediaResult的media_id为空时，应返回null
     */
    @Test
    public void testGetActivityPageAttachmentVO_MediaIdIsEmpty() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("appId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageInfo.setThumbPicUrl("thumbPicUrl");
        Map<String, AttachmentVO> existedAttachmentMap = new HashMap<>();
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        WeChatTokenResult tokenResult = new WeChatTokenResult(0, "", "accessToken", 7200);
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(tokenResult);
        WechatMediaResult mediaResult = new WechatMediaResult("", System.currentTimeMillis(), "image");
        when(uploadWxMediaAcl.uploadWxTmpMedia(anyString(), eq(WxMediaType.image), anyString(), eq(true))).thenReturn(mediaResult);
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        AttachmentVO result = officialWxHandler.getActivityPageAttachmentVO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, stepExecuteResultDTO);
        assertNull(result);
    }

    /**
     * 测试getOfficialLinkAttachmentVO方法，当WeChatTokenResult为null时
     */
    @Test
    public void testGetOfficialLinkAttachmentVO_WeChatTokenResultIsNull() {
        // arrange
        String title = "title";
        String picUrl = "picUrl";
        String url = "url";
        String appId = "appId";
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId("corpId")).thenReturn(null);
        // act
        AttachmentVO result = officialWxHandler.getOfficialLinkAttachmentVO(title, picUrl, url, appId);
        // assert
        assertNull(result);
    }

    /**
     * 测试getOfficialLinkAttachmentVO方法，当WeChatTokenResult不成功时
     */
    @Test
    public void testGetOfficialLinkAttachmentVO_WeChatTokenResultIsNotSuccess() {
        // arrange
        String title = "title";
        String picUrl = "picUrl";
        String url = "url";
        String appId = "appId";
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId("corpId")).thenReturn(new WeChatTokenResult(400, "error", null, 0));
        // act
        AttachmentVO result = officialWxHandler.getOfficialLinkAttachmentVO(title, picUrl, url, appId);
        // assert
        assertNull(result);
    }

    /**
     * 测试getOfficialLinkAttachmentVO方法，当WechatMediaResult为null时
     */
    @Test
    public void testGetOfficialLinkAttachmentVO_WechatMediaResultIsNull() {
        // arrange
        String title = "title";
        String picUrl = "picUrl";
        String url = "url";
        String appId = "appId";
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId("corpId")).thenReturn(new WeChatTokenResult(0, "success", "accessToken", 7200));
        when(uploadWxMediaAcl.uploadWxTmpMedia(picUrl, WxMediaType.image, "accessToken", true)).thenReturn(null);
        // act
        AttachmentVO result = officialWxHandler.getOfficialLinkAttachmentVO(title, picUrl, url, appId);
        // assert
        assertNull(result);
    }

    /**
     * 测试getOfficialLinkAttachmentVO方法，当WechatMediaResult的media_id为空时
     */
    @Test
    public void testGetOfficialLinkAttachmentVO_WechatMediaResultMediaIdIsEmpty() {
        // arrange
        String title = "title";
        String picUrl = "picUrl";
        String url = "url";
        String appId = "appId";
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId("corpId")).thenReturn(new WeChatTokenResult(0, "success", "accessToken", 7200));
        when(uploadWxMediaAcl.uploadWxTmpMedia(picUrl, WxMediaType.image, "accessToken", true)).thenReturn(new WechatMediaResult("", 0, "image"));
        // act
        AttachmentVO result = officialWxHandler.getOfficialLinkAttachmentVO(title, picUrl, url, appId);
        // assert
        assertNull(result);
    }

    /**
     * 测试getOfficialLinkAttachmentVO方法，正常情况
     */
    @Test
    public void testGetOfficialLinkAttachmentVO_Success() {
        // arrange
        String title = "title";
        String picUrl = "picUrl";
        String url = "url";
        String appId = "appId";
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId("corpId")).thenReturn(new WeChatTokenResult(0, "success", "accessToken", 7200));
        when(uploadWxMediaAcl.uploadWxTmpMedia(picUrl, WxMediaType.image, "accessToken", true)).thenReturn(new WechatMediaResult("mediaId", 0, "image"));
        // act
        AttachmentVO result = officialWxHandler.getOfficialLinkAttachmentVO(title, picUrl, url, appId);
        // assert
        assertNotNull(result);
        assertEquals("link", result.getMsgtype());
        assertNotNull(result.getLink());
        assertEquals("mediaId", result.getLink().getMedia_id());
    }

    /**
     * 测试getOfficialImageAttachmentVO方法，当token获取失败时应返回null
     */
    @Test
    public void testGetOfficialImageAttachmentVO_TokenFail() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("appId");
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult());
        AttachmentVO result = officialWxHandler.getOfficialImageAttachmentVO(processOrchestrationDTO, contentDetailDTO);
        assertNull(result);
    }

    /**
     * 测试getOfficialImageAttachmentVO方法，当media上传失败时应返回null
     */
    @Test
    public void testGetOfficialImageAttachmentVO_UploadMediaFail() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("appId");
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetailDTO.setPicUrl("picUrl");
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(0, "", "accessToken", 7200));
        when(uploadWxMediaAcl.uploadWxTmpMedia(anyString(), eq(WxMediaType.image), anyString(), eq(true))).thenReturn(null);
        AttachmentVO result = officialWxHandler.getOfficialImageAttachmentVO(processOrchestrationDTO, contentDetailDTO);
        assertNull(result);
    }

    /**
     * 测试getOfficialImageAttachmentVO方法，正常情况下应返回正确的AttachmentVO
     */
    @Test
    public void testGetOfficialImageAttachmentVO_Success() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("appId");
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetailDTO.setPicUrl("picUrl");
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(new WeChatTokenResult(0, "", "accessToken", 7200));
        when(uploadWxMediaAcl.uploadWxTmpMedia(anyString(), eq(WxMediaType.image), anyString(), eq(true))).thenReturn(new WechatMediaResult("mediaId", 123456789L, "image"));
        AttachmentVO result = officialWxHandler.getOfficialImageAttachmentVO(processOrchestrationDTO, contentDetailDTO);
        assertNotNull(result);
        assertEquals("image", result.getMsgtype());
        assertNotNull(result.getImage());
        assertEquals("mediaId", result.getImage().getMedia_id());
    }

    /**
     * 测试 trySendGroupMessage 方法，当 token 获取失败时
     */
    @Test
    public void testTrySendGroupMessageWhenTokenFailed() {
        // arrange
        /*ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("appId");*/
        String executorId = "executorId";
        Map<String, WxGroupSendMessageRequest> requestMap = new HashMap<>();
        requestMap.put("key", new WxGroupSendMessageRequest());
        Map<String, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> executeLogDOMap = new HashMap<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(1L);
        executeLogDOMap.put("key", Lists.newArrayList(wxInvokeDetailDO));
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(null);
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> wxInvokeLogDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setStatus((byte) 1);
        wxInvokeLogDO.setExecutorId("1");
        wxInvokeLogDO.setProcessOrchestrationNodeId(1L);
        wxInvokeLogDO.setProcessOrchestrationNodeId(1L);
        wxInvokeLogDOS.add(wxInvokeLogDO);
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Lists.newArrayList());
        // act
        officialWxHandler.trySendGroupMessage(processOrchestrationDTO, executorId, requestMap, executeLogDOMap, null);
        // assert
        verify(weChatTokenAcl, times(1)).getTokenByCorpId("corpId");
        verify(wxGroupSendMessageAcl, never()).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString());
    }

    /**
     * 测试 trySendGroupMessage 方法，当 token 获取成功，但发送消息失败时
     */
    @Test
    public void testTrySendGroupMessageWhenSendMessageFailed() {
        // arrange
        /* ScrmProcessOrchestrationDTO processOrchestrationDTO = processOrchestrationDTO;
        processOrchestrationDTO.setAppId("appId");
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1");*/
        String executorId = "executorId";
        Map<String, WxGroupSendMessageRequest> requestMap = new HashMap<>();
        WxGroupSendMessageRequest request = new WxGroupSendMessageRequest();
        request.setSender("sender");
        requestMap.put("key", request);
        Map<String, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> executeLogDOMap = new HashMap<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(1L);
        executeLogDOMap.put("key", Lists.newArrayList(wxInvokeDetailDO));
        WeChatTokenResult tokenResult = new WeChatTokenResult(0, "", "accessToken", 7200);
        WxGroupSendMessageResponse response = new WxGroupSendMessageResponse(1, "error", null, "");
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(tokenResult);
        when(wxGroupSendMessageAcl.groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString())).thenReturn(response);
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> wxInvokeLogDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setStatus((byte) 1);
        wxInvokeLogDO.setExecutorId("1");
        wxInvokeLogDO.setProcessOrchestrationNodeId(1L);
        wxInvokeLogDO.setProcessOrchestrationNodeId(1L);
        wxInvokeLogDOS.add(wxInvokeLogDO);
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Lists.newArrayList());
        // act
        officialWxHandler.trySendGroupMessage(processOrchestrationDTO, executorId, requestMap, executeLogDOMap, null);
        // assert
        verify(weChatTokenAcl, times(1)).getTokenByCorpId("corpId");
        // verify(wxGroupSendMessageAcl, times(1)).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString());
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(), any());
    }

    /**
     * 测试 trySendGroupMessage 方法，当 token 获取成功，并且消息发送成功时
     */
    @Test
    public void testTrySendGroupMessageWhenSendMessageSuccess() {
        // arrange
        /*ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("appId");*/
        String executorId = "executorId";
        Map<String, WxGroupSendMessageRequest> requestMap = new HashMap<>();
        WxGroupSendMessageRequest request = new WxGroupSendMessageRequest();
        request.setSender("sender");
        requestMap.put("key", request);
        Map<String, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> executeLogDOMap = new HashMap<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(1L);
        executeLogDOMap.put("key", Lists.newArrayList(wxInvokeDetailDO));
        WeChatTokenResult tokenResult = new WeChatTokenResult(0, "", "accessToken", 7200);
        WxGroupSendMessageResponse response = new WxGroupSendMessageResponse(0, "success", Collections.emptyList(), "msgId");
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(tokenResult);
        when(wxGroupSendMessageAcl.groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString())).thenReturn(response);
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> wxInvokeLogDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setStatus((byte) 1);
        wxInvokeLogDO.setExecutorId("1");
        wxInvokeLogDO.setProcessOrchestrationNodeId(1L);
        wxInvokeLogDOS.add(wxInvokeLogDO);
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Lists.newArrayList());
        // act
        officialWxHandler.trySendGroupMessage(processOrchestrationDTO, executorId, requestMap, executeLogDOMap, null);
        // assert
        verify(weChatTokenAcl, times(1)).getTokenByCorpId("corpId");
        // verify(wxGroupSendMessageAcl, times(1)).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString());
        // verify(wxInvokeLogDOMapper, times(1)).insert(any());
        // verify(wxInvokeDetailDOMapper, atLeastOnce()).updateByExampleSelective(any(), any());
    }

    /**
     * 测试输入为空字符串时的情况
     */
    @Test
    public void testGetMiniProgramTitleEmptyString() {
        // arrange
        OfficialWxHandler handler = new OfficialWxHandler();
        String input = "";
        // act
        String result = handler.getMiniProgramTitle(input);
        // assert
        assertEquals("", result);
    }

    /**
     * 测试输入为null时的情况
     */
    @Test
    public void testGetMiniProgramTitleNull() {
        // arrange
        OfficialWxHandler handler = new OfficialWxHandler();
        String input = null;
        // act
        String result = handler.getMiniProgramTitle(input);
        // assert
        assertNull(result);
    }

    /**
     * 测试输入字符串长度小于64时的情况
     */
    @Test
    public void testGetMiniProgramTitleLessThan64() {
        // arrange
        OfficialWxHandler handler = new OfficialWxHandler();
        String input = "测试";
        // act
        String result = handler.getMiniProgramTitle(input);
        // assert
        assertEquals("测试", result);
    }

    /**
     * 测试输入字符串长度等于64时的情况
     */
    @Test
    public void testGetMiniProgramTitleEquals63() {
        // arrange
        OfficialWxHandler handler = new OfficialWxHandler();
        // 构造长度为22的字符串，每个字符占3字节，总长度为66，超过64
        String input = StringUtils.repeat("汉", 21);
        // act
        String result = handler.getMiniProgramTitle(input);
        // assert
        assertEquals(StringUtils.repeat("汉", 21), result);
    }

    /**
     * 测试输入字符串长度等于64时的情况
     */
    @Test
    public void testGetMiniProgramTitleEquals64() {
        // arrange
        OfficialWxHandler handler = new OfficialWxHandler();
        // 构造长度为22的字符串，每个字符占3字节，总长度为66，超过64
        String input = StringUtils.repeat("汉", 22);
        // act
        String result = handler.getMiniProgramTitle(input);
        // assert
        assertEquals("【回馈特惠】社群专享特价爆品", result);
    }

    /**
     * 测试输入字符串长度大于64时的情况
     */
    @Test
    public void testGetMiniProgramTitleMoreThan64() {
        // arrange
        OfficialWxHandler handler = new OfficialWxHandler();
        // 构造长度为23的字符串，每个字符占3字节，总长度为69，超过64
        String input = StringUtils.repeat("汉", 23);
        // act
        String result = handler.getMiniProgramTitle(input);
        // assert
        assertEquals("【回馈特惠】社群专享特价爆品", result);
    }

    /**
     * 测试 buildProductQRCodeUrl 方法，当 supplyType 为 1 时抛出 IllegalArgumentException 异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testBuildProductQRCodeUrlWithSupplyType1() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setSupplyType(1);
        int width = 200;
        // act
        officialWxHandler.buildProductQRCodeUrl(processOrchestrationDTO, attachmentDTO, supplyDetailDTO, width);
        // assert
        // Expected exception
    }

    /**
     * 测试 buildProductQRCodeUrl 方法，当 supplyType 为 2 且 productInfoDTO 为 null 时返回 null
     */
    @Test
    public void testBuildProductQRCodeUrlWithSupplyType2AndNullProductInfoDTO() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setSupplyType(2);
        supplyDetailDTO.setProductId("1");
        int width = 200;
        when(productManagementService.queryProductById(anyLong(), anyString())).thenReturn(null);
        // act
        String result = officialWxHandler.buildProductQRCodeUrl(processOrchestrationDTO, attachmentDTO, supplyDetailDTO, width);
        // assert
        assertNull(result);
    }

    /**
     * 测试 buildProductQRCodeUrl 方法，当 supplyType 为 2 且 productInfoDTO 不为 null 时返回非空字符串
     */
    @Test
    public void testBuildProductQRCodeUrlWithSupplyType2AndNonNullProductInfoDTO() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setSupplyType(2);
        supplyDetailDTO.setProductId("1");
        int width = 200;
        ProductInfoDTO productInfoDTO = new ProductInfoDTO();
        productInfoDTO.setProductId(1L);
        productInfoDTO.setH5Url("http://example.com");
        when(productManagementService.queryProductById(anyLong(), anyString())).thenReturn(productInfoDTO);
        when(informationGatheringService.queryCommunityDistributor(any())).thenReturn("communityCode");
        // act
        String result = officialWxHandler.buildProductQRCodeUrl(processOrchestrationDTO, attachmentDTO, supplyDetailDTO, width);
        // assert
        assertNull(result);
    }

    /**
     * 测试 buildProductQRCodeUrl 方法，当 supplyType 不为 1 或 2 时抛出 IllegalArgumentException 异常
     */
    @Test(expected = IllegalArgumentException.class)
    public void testBuildProductQRCodeUrlWithInvalidSupplyType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setSupplyType(3);
        int width = 200;
        // act
        officialWxHandler.buildProductQRCodeUrl(processOrchestrationDTO, attachmentDTO, supplyDetailDTO, width);
        // assert
        // Expected exception
    }

    /**
     * 测试场景：当产品ID列表为空时，应返回null
     */
    @Test
    public void testBuildPageQRCodeUrl_WhenProductIdsIsEmpty() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setProductId("");
        String result = officialWxHandler.buildPageQRCodeUrl(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, 200);
        assertNull(result);
    }

    /**
     * 测试场景：当产品ID列表不为空，但查询到的页面列表为空时，应返回null
     */
    @Test
    public void testBuildPageQRCodeUrl_WhenPageListIsEmpty() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setProductId("1,2,3");
        when(productManagementService.queryActivityPagesById(any())).thenReturn(Collections.emptyList());
        String result = officialWxHandler.buildPageQRCodeUrl(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, 200);
        assertNull(result);
    }

    /**
     * 测试场景：当QRCode生成过程中抛出异常时，应抛出RuntimeException
     */
    /*@Test
    public void testBuildPageQRCodeUrl_WhenQRCodeGenerationThrowsException() throws IOException, WriterException {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setProductId("1");

        when(productManagementService.queryActivityPagesById(any())).thenReturn(Arrays.asList(new ScrmAmProcessOrchestrationProductActivityPageDO()));
        MockedStatic<QRCodeUtils> qrCodeUtilsMockedStatic = mockStatic(QRCodeUtils.class);
        qrCodeUtilsMockedStatic.when(()->QRCodeUtils.generateQRCodeImage(any(), anyInt())).thenReturn("QRCodeURL");

        officialWxHandler.buildPageQRCodeUrl(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, 200);

        qrCodeUtilsMockedStatic.close();
    }*/
    /**
     * 测试dealShortUrl方法，输入为null的情况
     */
    @Test
    public void testDealShortUrlWithNullInput() {
        // arrange
        String shortUrl = null;
        // act
        String result = officialWxHandler.dealShortUrl(shortUrl);
        // assert
        assertNull("处理null输入时应返回null", result);
    }

    /**
     * 测试dealShortUrl方法，输入为空字符串的情况
     */
    @Test
    public void testDealShortUrlWithEmptyString() {
        // arrange
        String shortUrl = "";
        // act
        String result = officialWxHandler.dealShortUrl(shortUrl);
        // assert
        assertEquals("处理空字符串输入时应返回空字符串", "", result);
    }

    /**
     * 测试dealShortUrl方法，输入为不以http:开头的字符串
     */
    @Test
    public void testDealShortUrlWithNonHttpPrefix() {
        // arrange
        String shortUrl = "https://example.com";
        // act
        String result = officialWxHandler.dealShortUrl(shortUrl);
        // assert
        assertEquals("不以http:开头的字符串应保持不变", "https://example.com", result);
    }

    /**
     * 测试dealShortUrl方法，输入为以http:开头的字符串
     */
    @Test
    public void testDealShortUrlWithHttpPrefix() {
        // arrange
        String shortUrl = "http://example.com";
        // act
        String result = officialWxHandler.dealShortUrl(shortUrl);
        // assert
        assertEquals("以http:开头的字符串应替换为https:", "https://example.com", result);
    }

    /**
     * 测试dealShortUrl方法，输入为以http:开头且包含多个http:的字符串
     */
    @Test
    public void testDealShortUrlWithMultipleHttpPrefix() {
        // arrange
        String shortUrl = "http://example.com/http://another.com";
        // act
        String result = officialWxHandler.dealShortUrl(shortUrl);
        // assert
        assertEquals("只替换第一个http:为https:", "https://example.com/http://another.com", result);
    }

    /**
     * 测试当token获取失败时的场景
     */
    @Test
    @Ignore
    public void testDealCouponSupplyOfficialWxMessage_TokenFail() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = couponProcessOrchestrationDTO;
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = mock(InvokeDetailKeyObject.class);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO2 = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO2.setTargetId("targetId2");
        invokeDetailDO2.setProcessOrchestrationNodeId(1736217559775L);
        totalInvokeDetailDOS.add(invokeDetailDO2);
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO3 = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO3.setTargetId("targetId3");
        invokeDetailDO3.setProcessOrchestrationNodeId(1736217600792L);
        totalInvokeDetailDOS.add(invokeDetailDO3);
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId("corpId")).thenReturn(new WeChatTokenResult(40001, "Token Error", null, 0));
        officialWxHandler.dealCouponSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        verify(weChatTokenAcl, times(2)).getTokenByCorpId("corpId");
        // verify(wxGroupSendMessageAcl, never()).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString());
    }

    /**
     * 测试当token获取成功但发送消息失败时的场景
     */
    @Test
    public void testDealCouponSupplyOfficialWxMessage_SendMessageFail() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = couponProcessOrchestrationDTO;
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = mock(InvokeDetailKeyObject.class);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO2 = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO2.setTargetId("targetId2");
        invokeDetailDO2.setProcessOrchestrationNodeId(1736217559775L);
        totalInvokeDetailDOS.add(invokeDetailDO2);
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO3 = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO3.setTargetId("targetId3");
        invokeDetailDO3.setProcessOrchestrationNodeId(1736217600792L);
        totalInvokeDetailDOS.add(invokeDetailDO3);
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId("corpId")).thenReturn(new WeChatTokenResult(0, "Success", "accessToken", 7200));
        when(wxGroupSendMessageAcl.groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString())).thenReturn(new WxGroupSendMessageResponse(40001, "Send Message Error", null, null));
        officialWxHandler.dealCouponSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        verify(weChatTokenAcl, times(2)).getTokenByCorpId("corpId");
        // verify(wxGroupSendMessageAcl, times(1)).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString());
    }

    /**
     * 测试当token获取成功且发送消息成功时的场景
     */
    @Test
    public void testDealCouponSupplyOfficialWxMessage_Success() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = couponProcessOrchestrationDTO;
        String executorId = "executorId";
        InvokeDetailKeyObject keyObject = mock(InvokeDetailKeyObject.class);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO2 = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO2.setTargetId("targetId2");
        invokeDetailDO2.setProcessOrchestrationNodeId(1736217559775L);
        totalInvokeDetailDOS.add(invokeDetailDO2);
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO3 = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO3.setTargetId("targetId3");
        invokeDetailDO3.setProcessOrchestrationNodeId(1736217600792L);
        totalInvokeDetailDOS.add(invokeDetailDO3);
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId("corpId")).thenReturn(new WeChatTokenResult(0, "Success", "accessToken", 7200));
        when(wxGroupSendMessageAcl.groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString())).thenReturn(new WxGroupSendMessageResponse(0, "Success", null, "msgId"));
        officialWxHandler.dealCouponSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        verify(weChatTokenAcl, times(2)).getTokenByCorpId("corpId");
        // verify(wxGroupSendMessageAcl, times(1)).groupSendMessage(any(WxGroupSendMessageRequest.class), anyString(), anyString());
    }

    /**
     * Test case for null actionAttachmentDTO.
     */
    @Test
    public void testBuildNormalAttachmentNonSupply_NullActionAttachmentDTO() throws Throwable {
        // arrange
        List<MsgPushContentDTO> attachments = new ArrayList<>();
        // act
        officialWxHandler.buildNormalAttachmentNonSupply(null, attachments);
        // assert
        assertTrue(attachments.isEmpty());
    }

    /**
     * Test case for null attachmentContentDetailDTO.
     */
    @Test
    public void testBuildNormalAttachmentNonSupply_NullAttachmentContentDetailDTO() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = mock(ScrmProcessOrchestrationActionAttachmentDTO.class);
        when(actionAttachmentDTO.getAttachmentContentDetailDTO()).thenReturn(null);
        List<MsgPushContentDTO> attachments = new ArrayList<>();
        // act
        officialWxHandler.buildNormalAttachmentNonSupply(actionAttachmentDTO, attachments);
        // assert
        assertTrue(attachments.isEmpty());
    }

    /**
     * Test case for PICTURE attachment type.
     */
    @Test
    public void testBuildNormalAttachmentNonSupply_PictureAttachmentType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = mock(ScrmProcessOrchestrationActionAttachmentDTO.class);
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO = mock(ScrmProcessOrchestrationAttachmentContentDetailDTO.class);
        when(actionAttachmentDTO.getAttachmentContentDetailDTO()).thenReturn(contentDetailDTO);
        when(actionAttachmentDTO.getAttachmentTypeId()).thenReturn(ScrmProcessOrchestrationAttachmentTypeEnum.PICTURE.getValue());
        when(contentDetailDTO.getPicUrl()).thenReturn("http://example.com/image.jpg");
        List<MsgPushContentDTO> attachments = new ArrayList<>();
        // act
        officialWxHandler.buildNormalAttachmentNonSupply(actionAttachmentDTO, attachments);
        // assert
        assertEquals(1, attachments.size());
        MsgPushContentDTO contentDTO = attachments.get(0);
        assertEquals(ContentTypeTEnum.IMAGE, contentDTO.getContentTypeTEnum());
        assertNotNull(contentDTO.getImageDTO());
        assertEquals("http://example.com/image.jpg", contentDTO.getImageDTO().getUrl());
    }

    /**
     * Test case for LINK attachment type.
     */
    @Test
    public void testBuildNormalAttachmentNonSupply_LinkAttachmentType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = mock(ScrmProcessOrchestrationActionAttachmentDTO.class);
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO = mock(ScrmProcessOrchestrationAttachmentContentDetailDTO.class);
        when(actionAttachmentDTO.getAttachmentContentDetailDTO()).thenReturn(contentDetailDTO);
        when(actionAttachmentDTO.getAttachmentTypeId()).thenReturn(ScrmProcessOrchestrationAttachmentTypeEnum.LINK.getValue());
        when(contentDetailDTO.getDesc()).thenReturn("Description");
        when(contentDetailDTO.getTitle()).thenReturn("Title");
        when(contentDetailDTO.getContentUrl()).thenReturn("http://example.com");
        when(contentDetailDTO.getPicUrl()).thenReturn("http://example.com/image.jpg");
        List<MsgPushContentDTO> attachments = new ArrayList<>();
        // act
        officialWxHandler.buildNormalAttachmentNonSupply(actionAttachmentDTO, attachments);
        // assert
        assertEquals(1, attachments.size());
        MsgPushContentDTO contentDTO = attachments.get(0);
        assertEquals(ContentTypeTEnum.LINK, contentDTO.getContentTypeTEnum());
        assertNotNull(contentDTO.getLinkDTO());
        assertEquals("Description", contentDTO.getLinkDTO().getDescription());
        assertEquals("Title", contentDTO.getLinkDTO().getTitle());
        assertEquals("http://example.com", contentDTO.getLinkDTO().getUrl());
        assertEquals("http://example.com/image.jpg", contentDTO.getLinkDTO().getThumbUrl());
    }

    /**
     * Test case for MINI_PROGRAM attachment type.
     */
    @Test
    public void testBuildNormalAttachmentNonSupply_MiniProgramAttachmentType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = mock(ScrmProcessOrchestrationActionAttachmentDTO.class);
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO = mock(ScrmProcessOrchestrationAttachmentContentDetailDTO.class);
        when(actionAttachmentDTO.getAttachmentContentDetailDTO()).thenReturn(contentDetailDTO);
        when(actionAttachmentDTO.getAttachmentTypeId()).thenReturn(ScrmProcessOrchestrationAttachmentTypeEnum.MINI_PROGRAM.getValue());
        when(contentDetailDTO.getOriginAppId()).thenReturn("originAppId");
        when(contentDetailDTO.getAppId()).thenReturn("appId");
        when(contentDetailDTO.getIcon()).thenReturn("icon");
        when(contentDetailDTO.getTitle()).thenReturn("Title");
        when(contentDetailDTO.getDesc()).thenReturn("Description");
        when(contentDetailDTO.getPicUrl()).thenReturn("http://example.com/image.jpg");
        when(contentDetailDTO.getContentUrl()).thenReturn("http://example.com");
        List<MsgPushContentDTO> attachments = new ArrayList<>();
        // act
        officialWxHandler.buildNormalAttachmentNonSupply(actionAttachmentDTO, attachments);
        // assert
        assertEquals(1, attachments.size());
        MsgPushContentDTO contentDTO = attachments.get(0);
        assertEquals(ContentTypeTEnum.MINI_PROGRAM, contentDTO.getContentTypeTEnum());
        assertNotNull(contentDTO.getMiniProgramDTO());
        assertEquals("originAppId", contentDTO.getMiniProgramDTO().getOriginAppId());
        assertEquals("appId", contentDTO.getMiniProgramDTO().getAppId());
        assertEquals("icon", contentDTO.getMiniProgramDTO().getIcon());
        assertEquals("Title", contentDTO.getMiniProgramDTO().getTitle());
        assertEquals("Description", contentDTO.getMiniProgramDTO().getDescription());
        assertEquals("http://example.com/image.jpg", contentDTO.getMiniProgramDTO().getThumbnail());
        assertEquals("http://example.com", contentDTO.getMiniProgramDTO().getPagePath());
    }

    /**
     * Test case for VIDEO attachment type.
     */
    @Test
    public void testBuildNormalAttachmentNonSupply_VideoAttachmentType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = mock(ScrmProcessOrchestrationActionAttachmentDTO.class);
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO = mock(ScrmProcessOrchestrationAttachmentContentDetailDTO.class);
        when(actionAttachmentDTO.getAttachmentContentDetailDTO()).thenReturn(contentDetailDTO);
        when(actionAttachmentDTO.getAttachmentTypeId()).thenReturn(ScrmProcessOrchestrationAttachmentTypeEnum.VIDEO.getValue());
        when(contentDetailDTO.getContentUrl()).thenReturn("http://example.com/video.mp4");
        List<MsgPushContentDTO> attachments = new ArrayList<>();
        // act
        officialWxHandler.buildNormalAttachmentNonSupply(actionAttachmentDTO, attachments);
        // assert
        assertEquals(1, attachments.size());
        MsgPushContentDTO contentDTO = attachments.get(0);
        assertEquals(ContentTypeTEnum.VIDEO, contentDTO.getContentTypeTEnum());
        assertNotNull(contentDTO.getVideoDTO());
        assertEquals("http://example.com/video.mp4", contentDTO.getVideoDTO().getUrl());
    }

    /**
     * Test case for FILE attachment type.
     */
    @Test
    public void testBuildNormalAttachmentNonSupply_FileAttachmentType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = mock(ScrmProcessOrchestrationActionAttachmentDTO.class);
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO = mock(ScrmProcessOrchestrationAttachmentContentDetailDTO.class);
        when(actionAttachmentDTO.getAttachmentContentDetailDTO()).thenReturn(contentDetailDTO);
        when(actionAttachmentDTO.getAttachmentTypeId()).thenReturn(ScrmProcessOrchestrationAttachmentTypeEnum.FILE.getValue());
        when(contentDetailDTO.getContentUrl()).thenReturn("http://example.com/file.pdf");
        List<MsgPushContentDTO> attachments = new ArrayList<>();
        // act
        officialWxHandler.buildNormalAttachmentNonSupply(actionAttachmentDTO, attachments);
        // assert
        assertEquals(1, attachments.size());
        MsgPushContentDTO contentDTO = attachments.get(0);
        assertEquals(ContentTypeTEnum.FILE, contentDTO.getContentTypeTEnum());
        assertNotNull(contentDTO.getFileDTO());
        assertEquals("http://example.com/file.pdf", contentDTO.getFileDTO().getUrl());
    }

    /**
     * Test case for unsupported attachment type.
     */
    @Test
    public void testBuildNormalAttachmentNonSupply_UnsupportedAttachmentType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = mock(ScrmProcessOrchestrationActionAttachmentDTO.class);
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO = mock(ScrmProcessOrchestrationAttachmentContentDetailDTO.class);
        when(actionAttachmentDTO.getAttachmentContentDetailDTO()).thenReturn(contentDetailDTO);
        // Unsupported type
        when(actionAttachmentDTO.getAttachmentTypeId()).thenReturn(999);
        List<MsgPushContentDTO> attachments = new ArrayList<>();
        // act
        officialWxHandler.buildNormalAttachmentNonSupply(actionAttachmentDTO, attachments);
        // assert
        assertTrue(attachments.isEmpty());
    }

    /**
     * Test case where all parameters are provided, and all conditions are met.
     */
    @Test
    public void testBuildKeyV2AllParametersProvided() throws Throwable {
        // arrange
        String executorId = "executor1";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(123L);
        wxInvokeDetailDO.setType("messageType1");
        String chatType = "group";
        List<MsgPushContentDTO> attachments = Arrays.asList(new MsgPushContentDTO(), new MsgPushContentDTO());
        String content = "Hello, World!";
        TextDTO text = new TextDTO();
        text.setContent("Sample Text");
        // act
        String result = OfficialWxHandler.buildKeyV2(executorId, wxInvokeDetailDO, chatType, attachments, content, text);
        // assert
        String expected = "executor1-123-messageType1-group-" + new HashCodeBuilder().append(content).build() + "-11-" + new HashCodeBuilder().append(text.getContent()).build() + "-" + new HashCodeBuilder().append(attachments).build() + "-2";
        assertEquals(expected, result);
    }

    /**
     * Test case where content is blank.
     */
    @Test
    public void testBuildKeyV2ContentBlank() throws Throwable {
        // arrange
        String executorId = "executor1";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(123L);
        wxInvokeDetailDO.setType("messageType1");
        String chatType = "group";
        List<MsgPushContentDTO> attachments = Arrays.asList(new MsgPushContentDTO(), new MsgPushContentDTO());
        String content = "";
        TextDTO text = new TextDTO();
        text.setContent("Sample Text");
        // act
        String result = OfficialWxHandler.buildKeyV2(executorId, wxInvokeDetailDO, chatType, attachments, content, text);
        // assert
        String expected = "executor1-123-messageType1-group-0-11-" + new HashCodeBuilder().append(text.getContent()).build() + "-" + new HashCodeBuilder().append(attachments).build() + "-2";
        assertEquals(expected, result);
    }

    /**
     * Test case where text is null.
     */
    @Test
    public void testBuildKeyV2TextNull() throws Throwable {
        // arrange
        String executorId = "executor1";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(123L);
        wxInvokeDetailDO.setType("messageType1");
        String chatType = "group";
        List<MsgPushContentDTO> attachments = Arrays.asList(new MsgPushContentDTO(), new MsgPushContentDTO());
        String content = "Hello, World!";
        TextDTO text = null;
        // act
        String result = OfficialWxHandler.buildKeyV2(executorId, wxInvokeDetailDO, chatType, attachments, content, text);
        // assert
        String expected = "executor1-123-messageType1-group-" + new HashCodeBuilder().append(content).build() + "-0-0-" + new HashCodeBuilder().append(attachments).build() + "-2";
        assertEquals(expected, result);
    }

    /**
     * Test case where attachments list is empty.
     */
    @Test
    public void testBuildKeyV2AttachmentsEmpty() throws Throwable {
        // arrange
        String executorId = "executor1";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(123L);
        wxInvokeDetailDO.setType("messageType1");
        String chatType = "group";
        List<MsgPushContentDTO> attachments = Collections.emptyList();
        String content = "Hello, World!";
        TextDTO text = new TextDTO();
        text.setContent("Sample Text");
        // act
        String result = OfficialWxHandler.buildKeyV2(executorId, wxInvokeDetailDO, chatType, attachments, content, text);
        // assert
        String expected = "executor1-123-messageType1-group-" + new HashCodeBuilder().append(content).build() + "-11-" + new HashCodeBuilder().append(text.getContent()).build() + "-0-0";
        assertEquals(expected, result);
    }

    /**
     * 测试failedInvokeLog方法，正常更新日志和执行日志的情况
     */
    @Test
    public void testFailedInvokeLogNormal() {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setId(1L);
        invokeLogDO.setUpdateTime(new Date());
        invokeLogDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_FAILED_INVALID_OR_CANT_DELIVER.getValue().byteValue());

        // act
        officialWxHandler.failedInvokeLog(invokeLogDO);

        // assert
        verify(wxInvokeLogDOMapper, times(1)).updateByPrimaryKey(invokeLogDO);
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(), any());
    }

    /**
     * 测试failedInvokeLog方法，传入null对象的情况
     */
    @Test(expected = NullPointerException.class)
    public void testFailedInvokeLogWithNull() {
        // act
        officialWxHandler.failedInvokeLog(null);

        // assert
        // 预期抛出NullPointerException
    }

    /**
     * 测试failedInvokeLog方法，当wxInvokeLogDOMapper.updateByPrimaryKey抛出异常的情况
     */
    @Test(expected = RuntimeException.class)
    public void testFailedInvokeLogUpdateInvokeLogThrowsException() {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setId(1L);
        invokeLogDO.setUpdateTime(new Date());
        invokeLogDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_FAILED_INVALID_OR_CANT_DELIVER.getValue().byteValue());
        doThrow(RuntimeException.class).when(wxInvokeLogDOMapper).updateByPrimaryKey(invokeLogDO);

        // act
        officialWxHandler.failedInvokeLog(invokeLogDO);

        // assert
        // 预期抛出RuntimeException
    }

    /**
     * 测试failedInvokeLog方法，当executeLogDOMapper.updateByExampleSelective抛出异常的情况
     */
    @Test(expected = RuntimeException.class)
    public void testFailedInvokeLogUpdateExecuteLogThrowsException() {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setId(1L);
        invokeLogDO.setUpdateTime(new Date());
        invokeLogDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.SEND_FAILED_INVALID_OR_CANT_DELIVER.getValue().byteValue());
        doNothing().when(wxInvokeLogDOMapper).updateByPrimaryKey(invokeLogDO);
        doThrow(RuntimeException.class).when(executeLogDOMapper).updateByExampleSelective(any(), any());

        // act
        officialWxHandler.failedInvokeLog(invokeLogDO);

        // assert
        // 预期抛出RuntimeException
    }

    /**
     * 测试failedInvokeLog方法，当invokeLogDO的status为null的情况
     */
    @Test
    public void testFailedInvokeLogWithNullStatus() {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setId(1L);
        invokeLogDO.setUpdateTime(new Date());
        invokeLogDO.setStatus(null);

        // act
        officialWxHandler.failedInvokeLog(invokeLogDO);

        // assert
        verify(wxInvokeLogDOMapper, times(1)).updateByPrimaryKey(invokeLogDO);
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(), any());
    }
}
