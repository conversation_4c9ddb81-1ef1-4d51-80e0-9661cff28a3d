package com.sankuai.scrm.core.service.external.contact.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.external.contact.dal.babymapper.ContactUserMapper;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.mockito.ArgumentMatchers.any;
import com.sankuai.scrm.core.service.external.contact.dal.example.ContactUserExample;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
public class ContactUserDomainInsertContactUser1Test {

    @Mock
    private ContactUserMapper contactUserMapper;

    @InjectMocks
    private ContactUserDomain contactUserDomain;

    /**
     * 测试当传入null对象时，不执行插入操作
     */
    @Test
    public void testInsertContactUserWhenContactUserIsNull() throws Throwable {
        // arrange
        ContactUser contactUser = null;
        // act
        contactUserDomain.insertContactUser(contactUser);
        // assert
        verify(contactUserMapper, never()).insert(any());
    }

    /**
     * 测试当传入有效对象时，执行插入操作
     */
    @Test
    public void testInsertContactUserWhenContactUserIsValid() throws Throwable {
        // arrange
        ContactUser contactUser = new ContactUser();
        contactUser.setId(1L);
        contactUser.setExternalUserId("ext123");
        when(contactUserMapper.insert(any(ContactUser.class))).thenReturn(1);
        // act
        contactUserDomain.insertContactUser(contactUser);
        // assert
        verify(contactUserMapper, times(1)).insert(contactUser);
    }

    /**
     * 测试当mapper插入返回0时，方法正常执行完成
     */
    @Test
    public void testInsertContactUserWhenMapperReturnsZero() throws Throwable {
        // arrange
        ContactUser contactUser = new ContactUser();
        contactUser.setId(1L);
        when(contactUserMapper.insert(any(ContactUser.class))).thenReturn(0);
        // act
        contactUserDomain.insertContactUser(contactUser);
        // assert
        verify(contactUserMapper, times(1)).insert(contactUser);
    }

    /**
     * 测试当mapper插入抛出异常时，方法会抛出异常
     */
    @Test
    public void testInsertContactUserWhenMapperThrowsException() throws Throwable {
        // arrange
        ContactUser contactUser = new ContactUser();
        contactUser.setId(1L);
        when(contactUserMapper.insert(any(ContactUser.class))).thenThrow(new RuntimeException("DB error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            contactUserDomain.insertContactUser(contactUser);
        });
        verify(contactUserMapper, times(1)).insert(contactUser);
    }

    /**
     * 测试当传入部分字段为null的对象时，方法正常执行
     */
    @Test
    public void testInsertContactUserWhenContactUserHasNullFields() throws Throwable {
        // arrange
        ContactUser contactUser = new ContactUser();
        contactUser.setId(1L);
        contactUser.setExternalUserId(null);
        when(contactUserMapper.insert(any(ContactUser.class))).thenReturn(1);
        // act
        contactUserDomain.insertContactUser(contactUser);
        // assert
        verify(contactUserMapper, times(1)).insert(contactUser);
    }

    @Test
    public void testGetContactUsersByCorpIdAndStaffIdAndExternalUserId_ValidParamsWithRecord() throws Throwable {
        // arrange
        String corpId = "testCorp";
        String staffId = "testStaff";
        String externalUserId = "testExternal";
        ContactUser expectedUser = new ContactUser();
        expectedUser.setCorpId(corpId);
        expectedUser.setStaffId(staffId);
        expectedUser.setExternalUserId(externalUserId);
        when(contactUserMapper.selectByExample(any(ContactUserExample.class))).thenReturn(Arrays.asList(expectedUser));
        // act
        ContactUser result = contactUserDomain.getContactUsersByCorpIdAndStaffIdAndExternalUserId(corpId, staffId, externalUserId);
        // assert
        assertNotNull(result);
        assertEquals(corpId, result.getCorpId());
        assertEquals(staffId, result.getStaffId());
        assertEquals(externalUserId, result.getExternalUserId());
    }

    @Test
    public void testGetContactUsersByCorpIdAndStaffIdAndExternalUserId_BlankCorpId() throws Throwable {
        // arrange
        String corpId = "";
        String staffId = "testStaff";
        String externalUserId = "testExternal";
        // act
        ContactUser result = contactUserDomain.getContactUsersByCorpIdAndStaffIdAndExternalUserId(corpId, staffId, externalUserId);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetContactUsersByCorpIdAndStaffIdAndExternalUserId_BlankStaffId() throws Throwable {
        // arrange
        String corpId = "testCorp";
        String staffId = "";
        String externalUserId = "testExternal";
        // act
        ContactUser result = contactUserDomain.getContactUsersByCorpIdAndStaffIdAndExternalUserId(corpId, staffId, externalUserId);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetContactUsersByCorpIdAndStaffIdAndExternalUserId_BlankExternalUserId() throws Throwable {
        // arrange
        String corpId = "testCorp";
        String staffId = "testStaff";
        String externalUserId = "";
        // act
        ContactUser result = contactUserDomain.getContactUsersByCorpIdAndStaffIdAndExternalUserId(corpId, staffId, externalUserId);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetContactUsersByCorpIdAndStaffIdAndExternalUserId_MultipleRecords() throws Throwable {
        // arrange
        String corpId = "testCorp";
        String staffId = "testStaff";
        String externalUserId = "testExternal";
        ContactUser user1 = new ContactUser();
        user1.setId(1L);
        ContactUser user2 = new ContactUser();
        user2.setId(2L);
        when(contactUserMapper.selectByExample(any(ContactUserExample.class))).thenReturn(Arrays.asList(user1, user2));
        // act
        ContactUser result = contactUserDomain.getContactUsersByCorpIdAndStaffIdAndExternalUserId(corpId, staffId, externalUserId);
        // assert
        assertNotNull(result);
        assertEquals(1L, result.getId());
    }

    @Test
    public void testGetContactUsersByCorpIdAndStaffIdAndExternalUserId_NoRecords() throws Throwable {
        // arrange
        String corpId = "testCorp";
        String staffId = "testStaff";
        String externalUserId = "testExternal";
        when(contactUserMapper.selectByExample(any(ContactUserExample.class))).thenReturn(Collections.emptyList());
        // act
        ContactUser result = contactUserDomain.getContactUsersByCorpIdAndStaffIdAndExternalUserId(corpId, staffId, externalUserId);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetContactUsersByCorpIdAndStaffIdAndExternalUserId_DatabaseError() throws Throwable {
        // arrange
        String corpId = "testCorp";
        String staffId = "testStaff";
        String externalUserId = "testExternal";
        when(contactUserMapper.selectByExample(any(ContactUserExample.class))).thenThrow(new RuntimeException("Database error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> contactUserDomain.getContactUsersByCorpIdAndStaffIdAndExternalUserId(corpId, staffId, externalUserId));
    }
}
