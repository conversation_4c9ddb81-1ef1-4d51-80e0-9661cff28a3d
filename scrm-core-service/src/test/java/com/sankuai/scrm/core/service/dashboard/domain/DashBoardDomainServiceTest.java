package com.sankuai.scrm.core.service.dashboard.domain;

import com.dianping.lion.shade.org.apache.curator.shaded.com.google.common.collect.Lists;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionParticipationRecordMapper;
import com.sankuai.scrm.core.service.activity.fission.domain.GroupFissionActivityDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteGoalDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.chat.domain.GroupChatLogDomainService;
import com.sankuai.scrm.core.service.chat.domain.PrivateChatDomainService;
import com.sankuai.scrm.core.service.dashboard.dal.dto.ActiveDataDashBoardUserLogDoc;
import com.sankuai.scrm.core.service.dashboard.dal.dto.UserDataDashboardUserLogDoc;
import com.sankuai.scrm.core.service.dashboard.dal.enums.CustomerActionTypeEnum;
import com.sankuai.scrm.core.service.dashboard.dal.enums.TradeOrderTerminalEnum;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ExternalContactBaseInfo;
import com.sankuai.scrm.core.service.external.contact.domain.ExternalContactBaseInfoDomainService;
import com.sankuai.scrm.core.service.flow.dal.mapper.ScrmFlowMaterialRelationLogMapper;
import com.sankuai.scrm.core.service.flowV2.dal.entity.FlowEntryEventLog;
import com.sankuai.scrm.core.service.flowV2.domain.FlowEntryEventLogDomainService;
import com.sankuai.scrm.core.service.friend.dynamiccode.dal.babymapper.ContactUserLogMapper;
import com.sankuai.scrm.core.service.friend.dynamiccode.dal.entity.ContactUserLog;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.group.dal.entity.OperatorHelperPrivateChat;
import com.sankuai.scrm.core.service.group.domain.GroupMemberDomainService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.session.domian.SessionMsgDomainService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DashBoardDomainServiceTest {

    @Mock
    private DashBoardESWriteDomainService dashBoardESWriteDomainService;

    @InjectMocks
    private DashBoardDomainService dashBoardDomainService;

    @Mock
    private DashBoardESReadDomainService dashBoardESReadDomainService;

    @Mock
    private ContactUserLogMapper contactUserLogMapper;

    @Mock
    private GroupMemberDomainService groupMemberDomainService;

    @Mock
    private SessionMsgDomainService sessionMsgDomainService;

    @Mock
    private GroupChatLogDomainService groupChatLogDomainService;

    @Mock
    private PrivateChatDomainService privateChatDomainService;

    @Mock
    private GroupFissionParticipationRecordMapper groupFissionParticipationRecordMapper;

    @Mock
    private ExternalContactBaseInfoDomainService externalContactBaseInfoDomainService;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecuteGoalDetailDOMapper executeGoalDetailDOMapper;

    @Mock
    private GroupFissionActivityDomainService groupFissionActivityDomainService;

    @Mock
    private ScrmFlowMaterialRelationLogMapper scrmFlowMaterialRelationLogMapper;

    @Mock
    private FlowEntryEventLogDomainService flowEntryEventLogDomainService;

    private final String unionId = "testUnionId";

    private final String corpId = "testCorpId";

    private final LocalDate nowDate = LocalDate.now();

    private final Integer terminalId = TradeOrderTerminalEnum.MINI_PROGRAM.getCode();

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 handleDashBoardUserLogWhenUpdateFlowEntryEventLog 方法，当 corpId 或 unionId 为 null 时，不执行任何操作
     */
    @Test
    public void testHandleDashBoardUserLogWhenUpdateFlowEntryEventLog_CorpIdOrUnionIdIsNull() {
        // arrange
        String corpId = null;
        String unionId = null;
        CustomerActionTypeEnum typeEnum = CustomerActionTypeEnum.ENTER_GROUP;
        // act
        dashBoardDomainService.handleDashBoardUserLogWhenUpdateFlowEntryEventLog(corpId, unionId, typeEnum);
        // assert
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(anyString(), any(), anyString());
    }

    /**
     * 测试 handleDashBoardUserLogWhenUpdateFlowEntryEventLog 方法，当 typeEnum 为 ENTER_GROUP 时，正确处理
     */
    @Test
    public void testHandleDashBoardUserLogWhenUpdateFlowEntryEventLog_EnterGroup() {
        // arrange
        String corpId = "corpId";
        String unionId = "unionId";
        CustomerActionTypeEnum typeEnum = CustomerActionTypeEnum.ENTER_GROUP;
        // act
        dashBoardDomainService.handleDashBoardUserLogWhenUpdateFlowEntryEventLog(corpId, unionId, typeEnum);
        // assert
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(anyString(), any(), anyString());
    }

    /**
     * 测试 handleDashBoardUserLogWhenUpdateFlowEntryEventLog 方法，当 typeEnum 为 LEAVE_GROUP 时，正确处理
     */
    @Test
    public void testHandleDashBoardUserLogWhenUpdateFlowEntryEventLog_LeaveGroup() {
        // arrange
        String corpId = "corpId";
        String unionId = "unionId";
        CustomerActionTypeEnum typeEnum = CustomerActionTypeEnum.LEAVE_GROUP;
        // act
        dashBoardDomainService.handleDashBoardUserLogWhenUpdateFlowEntryEventLog(corpId, unionId, typeEnum);
        // assert
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(anyString(), any(), anyString());
    }

    /**
     * 测试场景：终端ID不匹配
     */
    @Test
    public void testIsActiveOrIncrementUserFromNowDate_TerminalIdNotMatch() {
        // arrange
        Integer wrongTerminalId = 999;
        // act
        boolean result = dashBoardDomainService.isActiveOrIncrementUserFromNowDate(unionId, corpId, nowDate, wrongTerminalId);
        // assert
        assertFalse(result);
    }

    /**
     * 测试场景：ES中找到新用户日志
     */
    @Test
    public void testIsActiveOrIncrementUserFromNowDate_NewUserLogFoundInES() {
        // arrange
        UserDataDashboardUserLogDoc userLogDoc = new UserDataDashboardUserLogDoc();
        userLogDoc.setIsNewCustomer(true);
        when(dashBoardESReadDomainService.getUserLogDocByCorpIdAndUnionIdInSevenDays(any(), any(), any())).thenReturn(Collections.singletonList(userLogDoc));
        // act
        boolean result = dashBoardDomainService.isActiveOrIncrementUserFromNowDate(unionId, corpId, nowDate, terminalId);
        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：ES中找到活跃用户日志
     */
    @Test
    public void testIsActiveOrIncrementUserFromNowDate_ActiveUserLogFoundInES() {
        // arrange
        ActiveDataDashBoardUserLogDoc activeUserLogDoc = new ActiveDataDashBoardUserLogDoc();
        when(dashBoardESReadDomainService.getActiveUserLogDocCorpIdAndUnionIdInSevenDays(any(), any(), any())).thenReturn(Collections.singletonList(activeUserLogDoc));
        // act
        boolean result = dashBoardDomainService.isActiveOrIncrementUserFromNowDate(unionId, corpId, nowDate, terminalId);
        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：数据库中找到新增的联系用户日志
     */
    @Test
    public void testIsActiveOrIncrementUserFromNowDate_NewContactUserLogFoundInDB() {
        // arrange
        ContactUserLog contactUserLog = new ContactUserLog();
        contactUserLog.setActionType(1);
        when(contactUserLogMapper.selectByExample(any())).thenReturn(Collections.singletonList(contactUserLog));
        // act
        boolean result = dashBoardDomainService.isActiveOrIncrementUserFromNowDate(unionId, corpId, nowDate, terminalId);
        // assert
        assertTrue(result);
    }


    /**
     * 测试场景：私聊发言记录存在
     */
    @Test(expected = Exception.class)
    public void testIsActiveOrIncrementUserFromNowDate_PrivateChatRecordExists() {
        // arrange
        OperatorHelperPrivateChat privateChat = new OperatorHelperPrivateChat();
        privateChat.setContent("testContent");
//        when(privateChatDomainService.queryBySenderIdAndTimeBetween(any(), any(), any(), any())).thenReturn(Collections.singletonList(privateChat));
        // act
        boolean result = dashBoardDomainService.isActiveOrIncrementUserFromNowDate(unionId, corpId, nowDate, terminalId);
        // assert
        assertTrue(result);
    }

    /**
     * 测试场景：没有找到任何记录
     */
    @Test
    public void testIsActiveOrIncrementUserFromNowDate_NoRecordFound() {
        // arrange
        when(dashBoardESReadDomainService.getUserLogDocByCorpIdAndUnionIdInSevenDays(any(), any(), any())).thenReturn(Collections.emptyList());
        when(dashBoardESReadDomainService.getActiveUserLogDocCorpIdAndUnionIdInSevenDays(any(), any(), any())).thenReturn(Collections.emptyList());
        when(contactUserLogMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(groupMemberDomainService.queryMemberInOrNotInGroupByUnionId(any(), any())).thenReturn(new MemberInfoEntity());
        when(privateChatDomainService.queryBySenderIdAndTimeBetween(any(), any(), any(), any())).thenReturn(Collections.emptyList());
        when(corpAppConfigRepository.getAppIdByCorpId(any())).thenReturn("testAppId");
        when(externalContactBaseInfoDomainService.queryBaseInfoByUnionId(any(), any())).thenReturn(new ExternalContactBaseInfo());
        when(sessionMsgDomainService.queryBySenderIdAndTimeBetween(any(), any(), any(), any())).thenReturn(new ArrayList<>());
        when(groupChatLogDomainService.queryBySenderIdAndTimeBetween(any(), any(), any(), any())).thenReturn(new ArrayList<>());
        ScrmAmProcessOrchestrationExecuteLogDO scrmAmProcessOrchestrationExecuteLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        scrmAmProcessOrchestrationExecuteLogDO.setId(1L);
        when(executeLogDOMapper.selectByExample(any())).thenReturn(Lists.newArrayList(scrmAmProcessOrchestrationExecuteLogDO));
        when(executeGoalDetailDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(groupFissionActivityDomainService.getGroupFissionActivitiesByIds(any())).thenReturn(new ArrayList<>());
        // act
        boolean result = dashBoardDomainService.isActiveOrIncrementUserFromNowDate(unionId, corpId, nowDate, terminalId);
        // assert
        assertFalse(result);
    }

    @Test
    public void testConvertUnionIdToMtUserIdBaseInfoFoundAndMtUserIdValid() {
        // arrange
        String unionId = "testUnionId";
        String appId = "testAppId";
        ExternalContactBaseInfo baseInfo = new ExternalContactBaseInfo();
        baseInfo.setMtUserId(123L);
        when(externalContactBaseInfoDomainService.queryBaseInfoByUnionId(appId, unionId)).thenReturn(baseInfo);

        // act
        Long result = dashBoardDomainService.convertUnionIdToMtUserId(unionId, appId);

        // assert
        assertEquals(Long.valueOf(123), result);
    }

    @Test
    public void testCalFromInnerStationWhenDelFriend() {
        // arrange
        List<ContactUserLog> contactUserLogs = new ArrayList<>();
        contactUserLogs.add(new ContactUserLog(1L, "externalUserId", "unionId", "corpId", 1L, "staffId", "scrmqd_123_123", 1, new Date()));

        when(scrmFlowMaterialRelationLogMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(corpAppConfigRepository.getAppIdByCorpId(any())).thenReturn("appId");
        ExternalContactBaseInfo baseInfo = new ExternalContactBaseInfo();
        baseInfo.setMtUserId(1L);
        when(externalContactBaseInfoDomainService.queryBaseInfoByUnionId(any(), any())).thenReturn(baseInfo);
        FlowEntryEventLog flowEntryEventLog = new FlowEntryEventLog();
        when(flowEntryEventLogDomainService.queryEventLog(any(), any(), any(), any(), any())).thenReturn(flowEntryEventLog);

        // act
        boolean result = dashBoardDomainService.calFromInnerStationWhenDelFriend(contactUserLogs);

        // assert
        assertTrue(result);
    }
}
