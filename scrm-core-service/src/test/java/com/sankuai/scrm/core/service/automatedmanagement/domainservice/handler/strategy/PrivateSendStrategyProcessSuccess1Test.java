package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.message.push.dto.MsgTaskDetailResultDTO;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushDetailStatus;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class PrivateSendStrategyProcessSuccess1Test {

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @InjectMocks
    private PrivateSendStrategy privateSendStrategy;

    /**
     * 测试当 taskDetailResultDTOS 为空时，processSuccess 方法的行为
     */
    @Test
    public void testProcessSuccessWithEmptyTaskDetailResultDTOS() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setId(1L);
        detailDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_CREATE.getValue().byteValue());
        wxInvokeDetailDOS.add(detailDO);
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = Collections.emptyList();
        String msgId = "msgId";
        // act
        privateSendStrategy.processSuccess(processOrchestrationDTO, wxInvokeDetailDOS, wxInvokeLogDO, taskDetailResultDTOS, msgId);
        // assert
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
    }

    /**
     * 测试当 taskDetailResultDTOS 中有失败的任务时，processSuccess 方法的行为
     */
    @Test
    public void testProcessSuccessWithFailedTaskDetailResultDTOS() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setId(1L);
        detailDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_CREATE.getValue().byteValue());
        wxInvokeDetailDOS.add(detailDO);
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = new ArrayList<>();
        MsgTaskDetailResultDTO failedTask = new MsgTaskDetailResultDTO();
        failedTask.setStatus(MsgPushDetailStatus.FAILED.getCode());
        failedTask.setReceiverId("receiverId");
        taskDetailResultDTOS.add(failedTask);
        String msgId = "msgId";
        // mock behavior
        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(wxInvokeDetailDOS);
        // act
        privateSendStrategy.processSuccess(processOrchestrationDTO, wxInvokeDetailDOS, wxInvokeLogDO, taskDetailResultDTOS, msgId);
        // assert
        verify(wxInvokeDetailDOMapper, times(2)).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(executeLogDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteLogDO.class), any(ScrmAmProcessOrchestrationExecuteLogDOExample.class));
    }

    /**
     * 测试当 wxInvokeDetailDOS 为空时，processSuccess 方法的行为
     */
    @Test
    public void testProcessSuccessWithEmptyWxInvokeDetailDOS() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS = Collections.emptyList();
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = new ArrayList<>();
        String msgId = "msgId";
        // act
        privateSendStrategy.processSuccess(processOrchestrationDTO, wxInvokeDetailDOS, wxInvokeLogDO, taskDetailResultDTOS, msgId);
        // assert
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
    }

    /**
     * 测试当 processOrchestrationDTO 为空时，processSuccess 方法的行为
     */
    @Test
    public void testProcessSuccessWithNullProcessOrchestrationDTO() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = null;
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS = new ArrayList<>();
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = new ArrayList<>();
        String msgId = "msgId";
        // act
        privateSendStrategy.processSuccess(processOrchestrationDTO, wxInvokeDetailDOS, wxInvokeLogDO, taskDetailResultDTOS, msgId);
        // assert
        verify(wxInvokeDetailDOMapper, times(1)).updateByExampleSelective(any(ScrmAmProcessOrchestrationWxInvokeDetailDO.class), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
    }

    /**
     * 测试当 wxInvokeLogDO 为空时，processSuccess 方法的行为
     */
    @Test
    public void testProcessSuccessWithNullWxInvokeLogDO() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = null;
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS = new ArrayList<>();
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = new ArrayList<>();
        String msgId = "msgId";
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            privateSendStrategy.processSuccess(processOrchestrationDTO, wxInvokeDetailDOS, wxInvokeLogDO, taskDetailResultDTOS, msgId);
        });
    }
}
