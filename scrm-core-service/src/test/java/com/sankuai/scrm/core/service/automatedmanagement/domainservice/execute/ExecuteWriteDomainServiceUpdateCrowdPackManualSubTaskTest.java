package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackUpdateLockService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.utils.ConditionUtils;
import com.sankuai.scrm.core.service.infrastructure.dal.babymapper.ContactUserDoMapper;
import com.sankuai.scrm.core.service.infrastructure.dal.entity.ContactUserDo;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserTag;
import com.sankuai.scrm.core.service.user.dal.example.ScrmUserTagExample;
import com.sankuai.scrm.core.service.user.dal.mapper.ext.ExtScrmUserTagMapper;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.springframework.test.util.ReflectionTestUtils;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackUpdateStrategyDO;
import com.sankuai.scrm.core.service.user.enums.ScrmUserTagEnum;
import java.lang.reflect.Method;
import java.util.Collections;
import java.util.HashSet;
import java.util.Set;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.group.domain.GroupMemberDomainService;
import com.sankuai.scrm.core.service.util.IdConvertUtils;

@ExtendWith(MockitoExtension.class)
public class ExecuteWriteDomainServiceUpdateCrowdPackManualSubTaskTest {

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock
    private CrowdPackUpdateLockService crowdPackUpdateLockService;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private ExtScrmUserTagMapper userTagDOMapper;

    @Mock
    private ConditionUtils conditionUtils;

    @Mock
    private ContactUserDoMapper contactUserDoMapper;

    @Mock
    private ContactUserDomain contactUserDomain;

    @Mock
    private GroupMemberDomainService groupMemberDomainService;

    @Mock
    private IdConvertUtils idConvertUtils;

    private final String validAppId = "testApp";

    private final Long validPackId = 123L;

    private final String validPackVersion = "v1";

    private final Long validUserId = 456L;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        ReflectionTestUtils.setField(executeWriteDomainService, "conditionUtils", conditionUtils);
        ReflectionTestUtils.setField(executeWriteDomainService, "contactUserDoMapper", contactUserDoMapper);
        ReflectionTestUtils.setField(executeWriteDomainService, "crowdPackUpdateLockService", crowdPackUpdateLockService);
    }

    @Test
    public void testUpdateCrowdPackManualSubTaskWithEmptyUserUnionIds() throws Throwable {
        // arrange
        List<String> userUnionIds = new ArrayList<>();
        // act
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, 1L, "appId");
        // assert
        assertTrue(result);
        verify(executeManagementService, never()).subTaskRunBegin(anyInt(), anyLong());
    }

    @Test
    public void testUpdateCrowdPackManualSubTaskWithException() throws Throwable {
        // arrange
        Long crowdPackId = 1L;
        List<String> userUnionIds = Arrays.asList("unionId1");
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(anyLong(), anyString()))
                .thenThrow(new RuntimeException("Database error"));
        when(crowdPackWriteDomainService.isDeletedTempPack(crowdPackId)).thenReturn(false);

        // Mock crowdPackUpdateLockService 的方法
        when(crowdPackUpdateLockService.consumerIncrementValue(eq(crowdPackId), eq(1))).thenReturn(1L);
        when(crowdPackUpdateLockService.getProducerValue(eq(crowdPackId))).thenReturn(2L);

        // act & assert
        assertThrows(RuntimeException.class, () ->
                executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, crowdPackId, "appId"));

        // 验证调用
        verify(executeManagementService, times(1)).subTaskRunBegin(
                eq(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue()),
                eq(crowdPackId));
        verify(executeManagementService, times(1)).taskRunFinished(
                eq(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue()),
                eq(crowdPackId));
    }

    private ScrmAmCrowdPackUpdateStrategyDO invokeGetFilterFieldId(List<ScrmAmCrowdPackUpdateStrategyDO> values, Set<Long> needFilterFieldIds) throws Exception {
        Method method = ExecuteWriteDomainService.class.getDeclaredMethod("getFilterFieldId", List.class, Set.class);
        method.setAccessible(true);
        return (ScrmAmCrowdPackUpdateStrategyDO) method.invoke(executeWriteDomainService, values, needFilterFieldIds);
    }

    @Test
    void testGetFilterFieldId_EmptyList_ReturnsNull() throws Throwable {
        // arrange
        Set<Long> needFilterFieldIds = new HashSet<>(Arrays.asList(10001L, 10002L));
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Collections.emptyList(), needFilterFieldIds);
        // assert
        assertNull(result);
    }

    @Test
    void testGetFilterFieldId_AllItemsFiltered_ReturnsNull() throws Throwable {
        // arrange
        ScrmAmCrowdPackUpdateStrategyDO item1 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item1.getFilterFieldId()).thenReturn(10001L);
        ScrmAmCrowdPackUpdateStrategyDO item2 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item2.getFilterFieldId()).thenReturn(10002L);
        Set<Long> needFilterFieldIds = new HashSet<>(Arrays.asList(10001L, 10002L));
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Arrays.asList(item1, item2), needFilterFieldIds);
        // assert
        assertNull(result);
        // Verify that getFilterFieldId was called on both items
        verify(item1, atLeastOnce()).getFilterFieldId();
        verify(item2, atLeastOnce()).getFilterFieldId();
    }

    @Test
    void testGetFilterFieldId_ContainsCorpTag_ReturnsCorpTagItem() throws Throwable {
        // arrange
        ScrmAmCrowdPackUpdateStrategyDO item1 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item1.getFilterFieldId()).thenReturn(10001L);
        ScrmAmCrowdPackUpdateStrategyDO corpTagItem = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(corpTagItem.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        Set<Long> needFilterFieldIds = new HashSet<>(Collections.singletonList(10001L));
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Arrays.asList(item1, corpTagItem), needFilterFieldIds);
        // assert
        assertNotNull(result);
        assertEquals(ScrmUserTagEnum.CORP_TAG.getTagId(), result.getFilterFieldId());
        // Verify that getFilterFieldId was called on both items
        verify(item1, atLeastOnce()).getFilterFieldId();
        verify(corpTagItem, atLeastOnce()).getFilterFieldId();
    }

    @Test
    void testGetFilterFieldId_NoCorpTagButOtherItems_ReturnsFirstValidItem() throws Throwable {
        // arrange
        ScrmAmCrowdPackUpdateStrategyDO item1 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item1.getFilterFieldId()).thenReturn(10001L);
        ScrmAmCrowdPackUpdateStrategyDO item2 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item2.getFilterFieldId()).thenReturn(30001L);
        Set<Long> needFilterFieldIds = new HashSet<>(Collections.singletonList(10001L));
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Arrays.asList(item1, item2), needFilterFieldIds);
        // assert
        assertNotNull(result);
        assertEquals(30001L, result.getFilterFieldId());
        // Verify that getFilterFieldId was called on both items
        verify(item1, atLeastOnce()).getFilterFieldId();
        verify(item2, atLeastOnce()).getFilterFieldId();
    }

    @Test
    void testGetFilterFieldId_MultipleItemsWithCorpTag_ReturnsCorpTagItem() throws Throwable {
        // arrange
        ScrmAmCrowdPackUpdateStrategyDO item1 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item1.getFilterFieldId()).thenReturn(10001L);
        ScrmAmCrowdPackUpdateStrategyDO corpTagItem = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(corpTagItem.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        ScrmAmCrowdPackUpdateStrategyDO item2 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item2.getFilterFieldId()).thenReturn(30001L);
        Set<Long> needFilterFieldIds = new HashSet<>(Collections.singletonList(10001L));
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Arrays.asList(item1, corpTagItem, item2), needFilterFieldIds);
        // assert
        assertNotNull(result);
        assertEquals(ScrmUserTagEnum.CORP_TAG.getTagId(), result.getFilterFieldId());
        // Verify that getFilterFieldId was called on all items
        verify(item1, atLeastOnce()).getFilterFieldId();
        verify(corpTagItem, atLeastOnce()).getFilterFieldId();
        verify(item2, atLeastOnce()).getFilterFieldId();
    }

    @Test
    void testGetFilterFieldId_EmptyFilterSet_ReturnsFirstItem() throws Throwable {
        // arrange
        ScrmAmCrowdPackUpdateStrategyDO item1 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item1.getFilterFieldId()).thenReturn(10001L);
        ScrmAmCrowdPackUpdateStrategyDO item2 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item2.getFilterFieldId()).thenReturn(30001L);
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Arrays.asList(item1, item2), Collections.emptySet());
        // assert
        assertNotNull(result);
        assertEquals(10001L, result.getFilterFieldId());
        // Verify that getFilterFieldId was called on both items
        verify(item1, atLeastOnce()).getFilterFieldId();
        verify(item2, atLeastOnce()).getFilterFieldId();
    }

    @Test
    public void testConcurrentUpdatePersonaCrowdPackAsync_EmptyUserIds() throws Throwable {
        // arrange
        Set<Long> emptyUserIds = Collections.emptySet();
        // act
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(validAppId, emptyUserIds, validPackId, validPackVersion);
        // assert
        verifyNoInteractions(crowdPackWriteDomainService);
    }

    @Test
    public void testConcurrentUpdatePersonaCrowdPackAsync_SingleValidUser() throws Throwable {
        // arrange
        Set<Long> userIds = Collections.singleton(validUserId);
        // Setup the mock to return a valid unionId
        when(idConvertUtils.convertMtUserIdToUnionId(eq(validUserId), eq(validAppId))).thenReturn("validUnionId");
        // Setup the mock to return a valid corpId
        when(appConfigRepository.getCorpIdByAppId(eq(validAppId))).thenReturn("validCorpId");
        // Setup the mock to return non-empty contact users list
        ContactUser mockContactUser = mock(ContactUser.class);
        when(mockContactUser.getExternalUserId()).thenReturn("externalUserId");
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(eq("validCorpId"), eq("validUnionId"))).thenReturn(Collections.singletonList(mockContactUser));
        // Setup the mock to return empty member info list
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(anyString(), anyString())).thenReturn(Collections.emptyList());
        // Setup the mock to return a successful insertion
        doReturn(true).when(crowdPackWriteDomainService).insertCrowdPackDetailInfo(any(), any(), any(), any(), any());
        // act
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(validAppId, userIds, validPackId, validPackVersion);
        // assert
        verify(crowdPackWriteDomainService, times(1)).insertCrowdPackDetailInfo(any(), any(), any(), any(), any());
    }

    @Test
    public void testConcurrentUpdatePersonaCrowdPackAsync_MultipleValidUsers() throws Throwable {
        // arrange
        Set<Long> userIds = new HashSet<>();
        userIds.add(validUserId);
        userIds.add(789L);
        // Setup the mock to return valid unionIds
        when(idConvertUtils.convertMtUserIdToUnionId(eq(validUserId), eq(validAppId))).thenReturn("validUnionId1");
        when(idConvertUtils.convertMtUserIdToUnionId(eq(789L), eq(validAppId))).thenReturn("validUnionId2");
        // Setup the mock to return a valid corpId
        when(appConfigRepository.getCorpIdByAppId(eq(validAppId))).thenReturn("validCorpId");
        // Setup the mock to return non-empty contact users list
        ContactUser mockContactUser = mock(ContactUser.class);
        when(mockContactUser.getExternalUserId()).thenReturn("externalUserId");
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(eq("validCorpId"), anyString())).thenReturn(Collections.singletonList(mockContactUser));
        // Setup the mock to return empty member info list
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(anyString(), anyString())).thenReturn(Collections.emptyList());
        // Setup the mock to return a successful insertion
        doReturn(true).when(crowdPackWriteDomainService).insertCrowdPackDetailInfo(any(), any(), any(), any(), any());
        // act
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(validAppId, userIds, validPackId, validPackVersion);
        // assert
        verify(crowdPackWriteDomainService, times(2)).insertCrowdPackDetailInfo(any(), any(), any(), any(), any());
    }

    @Test
    public void testConcurrentUpdatePersonaCrowdPackAsync_PartialFailure() throws Throwable {
        // arrange
        Set<Long> userIds = new HashSet<>();
        userIds.add(validUserId);
        userIds.add(789L);
        // Setup the mock to return valid unionIds
        when(idConvertUtils.convertMtUserIdToUnionId(eq(validUserId), eq(validAppId))).thenReturn("validUnionId1");
        when(idConvertUtils.convertMtUserIdToUnionId(eq(789L), eq(validAppId))).thenReturn("validUnionId2");
        // Setup the mock to return a valid corpId
        when(appConfigRepository.getCorpIdByAppId(eq(validAppId))).thenReturn("validCorpId");
        // Setup the mock to return non-empty contact users list for first user, empty for second
        ContactUser mockContactUser = mock(ContactUser.class);
        when(mockContactUser.getExternalUserId()).thenReturn("externalUserId");
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(eq("validCorpId"), eq("validUnionId1"))).thenReturn(Collections.singletonList(mockContactUser));
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(eq("validCorpId"), eq("validUnionId2"))).thenReturn(Collections.emptyList());
        // Setup the mock to return empty member info list for first user, non-empty for second
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(eq("validCorpId"), eq("validUnionId1"))).thenReturn(Collections.emptyList());
        MemberInfoEntity mockMemberInfo = mock(MemberInfoEntity.class);
        when(mockMemberInfo.getGroupMemberId()).thenReturn("groupMemberId");
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(eq("validCorpId"), eq("validUnionId2"))).thenReturn(Collections.singletonList(mockMemberInfo));
        // Setup the mock to return a successful insertion for both users
        doReturn(true).when(crowdPackWriteDomainService).insertCrowdPackDetailInfo(any(), any(), any(), any(), any());
        // act
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(validAppId, userIds, validPackId, validPackVersion);
        // assert
        // Should be called twice, once for each user
        verify(crowdPackWriteDomainService, times(2)).insertCrowdPackDetailInfo(any(), any(), any(), any(), any());
    }

    @Test
    public void testConcurrentUpdatePersonaCrowdPackAsync_NullAppId() throws Throwable {
        // arrange
        Set<Long> userIds = Collections.singleton(validUserId);
        // Setup the mock to return null unionId (since appId is null)
        when(idConvertUtils.convertMtUserIdToUnionId(eq(validUserId), isNull())).thenReturn(null);
        // act
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(null, userIds, validPackId, validPackVersion);
        // assert
        // Should not call insertCrowdPackDetailInfo since unionId is null
        verify(crowdPackWriteDomainService, never()).insertCrowdPackDetailInfo(any(), any(), any(), any(), any());
    }

    @Test
    public void testConcurrentUpdatePersonaCrowdPackAsync_NullPackId() throws Throwable {
        // arrange
        Set<Long> userIds = Collections.singleton(validUserId);
        // Setup the mock to return a valid unionId
        when(idConvertUtils.convertMtUserIdToUnionId(eq(validUserId), eq(validAppId))).thenReturn("validUnionId");
        // Setup the mock to return a valid corpId
        when(appConfigRepository.getCorpIdByAppId(eq(validAppId))).thenReturn("validCorpId");
        // Setup the mock to return non-empty contact users list
        ContactUser mockContactUser = mock(ContactUser.class);
        when(mockContactUser.getExternalUserId()).thenReturn("externalUserId");
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(eq("validCorpId"), eq("validUnionId"))).thenReturn(Collections.singletonList(mockContactUser));
        // Setup the mock to return empty member info list
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(anyString(), anyString())).thenReturn(Collections.emptyList());
        // Setup the mock to return a failed insertion
        doReturn(false).when(crowdPackWriteDomainService).insertCrowdPackDetailInfo(isNull(), anyString(), anyString(), anyString(), anyString());
        // act
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(validAppId, userIds, null, validPackVersion);
        // assert
        // Should call insertCrowdPackDetailInfo with null packId
        verify(crowdPackWriteDomainService, times(1)).insertCrowdPackDetailInfo(isNull(), anyString(), anyString(), anyString(), anyString());
    }

    @Test
    public void testConcurrentUpdatePersonaCrowdPackAsync_NullPackVersion() throws Throwable {
        // arrange
        Set<Long> userIds = Collections.singleton(validUserId);
        // Setup the mock to return a valid unionId
        when(idConvertUtils.convertMtUserIdToUnionId(eq(validUserId), eq(validAppId))).thenReturn("validUnionId");
        // Setup the mock to return a valid corpId
        when(appConfigRepository.getCorpIdByAppId(eq(validAppId))).thenReturn("validCorpId");
        // Setup the mock to return non-empty contact users list
        ContactUser mockContactUser = mock(ContactUser.class);
        when(mockContactUser.getExternalUserId()).thenReturn("externalUserId");
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(eq("validCorpId"), eq("validUnionId"))).thenReturn(Collections.singletonList(mockContactUser));
        // Setup the mock to return empty member info list
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(anyString(), anyString())).thenReturn(Collections.emptyList());
        // Setup the mock to return a failed insertion
        doReturn(false).when(crowdPackWriteDomainService).insertCrowdPackDetailInfo(anyLong(), anyString(), anyString(), anyString(), isNull());
        // act
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(validAppId, userIds, validPackId, null);
        // assert
        // Should call insertCrowdPackDetailInfo with null packVersion
        verify(crowdPackWriteDomainService, times(1)).insertCrowdPackDetailInfo(anyLong(), anyString(), anyString(), anyString(), isNull());
    }

    @Test
    public void testConcurrentUpdatePersonaCrowdPackAsync_NullUserId() throws Throwable {
        // arrange
        Set<Long> userIds = new HashSet<>();
        userIds.add(null);
        // Setup the mock to return null unionId (since userId is null)
        // act
        executeWriteDomainService.concurrentUpdatePersonaCrowdPackAsync(validAppId, userIds, validPackId, validPackVersion);
        // assert
        // Should not call insertCrowdPackDetailInfo since userId is null and unionId is null
        verify(crowdPackWriteDomainService, never()).insertCrowdPackDetailInfo(any(), any(), any(), any(), any());
    }
}
