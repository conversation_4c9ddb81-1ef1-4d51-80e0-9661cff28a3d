package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.Mockito.mockStatic;
import com.dianping.cat.Cat;
import com.dianping.lion.Environment;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.nio.charset.StandardCharsets;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;

@ExtendWith(MockitoExtension.class)
class AbstractWxHandlerBuildPageUrlTest {

    private static final String TEST_SHORT_URL = "https://test-short-url.com";

    private static final String TEST_DISTRIBUTOR_CODE = "testCode";

    private static final String TEST_APP_ID = "testAppId";

    private final AbstractWxHandler handler = new TestAbstractWxHandler();

    private static final String ROOT_URL = "/index/pages/h5/gnc/h5.html?weburl=";

    private static final String ROOT_URL_DEEP_SEA = "/index/pages/h5/gnc/h5?weburl=";

    private static final String ROOT_URL_FOR_QRCODE = "/index/pages/h5/gnc/h5?weburl=";

    private static final String ROOT_URL_DEEP_SEA_FOR_QRCODE = "/index/pages/h5/gnc/h5?weburl=";

    private static final String ROOT_URL_POSTFIX = "&f_userId=1&f_openId=1&f_token=1&f_ci=1&f_pos=1&f_openIdCipher=1&f_finger=1";

    private static final String MID_PAGE_TEST = "https://test-g.meituan.com/dzcsr/biz-growth/community/product-transfer.html?redirectUrl={0}&distributorcode={1}&appId={2}";

    private static final String MID_PAGE_PRODUCT = "https://g.meituan.com/dzcsr/biz-growth/community/product-transfer.html?redirectUrl={0}&distributorcode={1}&appId={2}";

    private static final String SHELF_PAGE_TEST = "https://test-g.meituan.com/dzcsr/biz-growth/community/product-shelf.html?scenecode={0}&appId={1}&poiId={2}";

    private static final String SHELF_PAGE_PRODUCT = "https://g.meituan.com/dzcsr/biz-growth/community/product-shelf.html?scenecode={0}&appId={1}&poiId={2}";

    private final AbstractWxHandler abstractWxHandler = new AbstractWxHandler() {

        @Override
        protected void logRequest(String request, String appId) {
        }

        @Override
        protected String getShorUrl(String coreUrl, boolean forceShortUrl) {
            return coreUrl;
        }
    };

    /**
     * Test buildPageUrl when in production environment and isForQrcode is true
     */
    @Test
    public void testBuildPageUrlWhenProdEnvAndQrcode() throws Throwable {
        // Arrange
        TestWxHandler handler = new TestWxHandler();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO detailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        detailDTO.setJumpPageType(1);
        detailDTO.setJumpUrl("https://test.com?param=1");
        try (MockedStatic<Environment> environmentMock = mockStatic(Environment.class);
            MockedStatic<InformationGatheringService> infoGatheringMock = mockStatic(InformationGatheringService.class);
            MockedStatic<Cat> catMock = mockStatic(Cat.class)) {
            // Mock Environment.isProductEnv() to return true (simulate production environment)
            environmentMock.when(Environment::isProductEnv).thenReturn(true);
            // Mock InformationGatheringService.buildPassParamString to return a valid pass_param
            infoGatheringMock.when(() -> InformationGatheringService.buildPassParamString(TEST_DISTRIBUTOR_CODE)).thenReturn("testPassParam");
            // Act
            String result = handler.buildPageUrl(detailDTO, TEST_DISTRIBUTOR_CODE, TEST_APP_ID, false, true);
            // Assert
            // Encode the TEST_SHORT_URL to match the URL encoding in the buildPageUrl method
            String encodedShortUrl = URLEncoder.encode(TEST_SHORT_URL, StandardCharsets.UTF_8.toString());
            assertTrue(result.contains(encodedShortUrl), "The result URL should contain the encoded short URL");
        }
    }

    // Test implementation of AbstractWxHandler
    private static class TestWxHandler extends AbstractWxHandler {

        @Override
        protected void logRequest(String request, String appId) {
            // Do nothing for test
        }

        @Override
        protected String getShorUrl(String coreUrl, boolean forceShortUrl) {
            return TEST_SHORT_URL;
        }
    }

    private static class TestAbstractWxHandler extends AbstractWxHandler {

        @Override
        protected void logRequest(String request, String appId) {
            // Do nothing for test
        }

        @Override
        protected String getShorUrl(String coreUrl, boolean forceShortUrl) {
            return coreUrl;
        }
    }

    @Test
    public void testBuildProductUrlWithoutQueryParamsInNonProdEnv() throws Throwable {
        String originpath = "https://example.com";
        try (MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class);
            MockedStatic<InformationGatheringService> mockedService = Mockito.mockStatic(InformationGatheringService.class)) {
            mockedEnvironment.when(Environment::isProductEnv).thenReturn(false);
            mockedService.when(() -> InformationGatheringService.buildPassParamString("distributor123")).thenReturn("pass_param_value");
            String result = handler.buildProductUrl(originpath, 123L, "distributor123", "app123", false, false);
            // Decode the URL to check its components
            String decodedResult = URLDecoder.decode(result, StandardCharsets.UTF_8.name());
            // Check for test environment URL components
            assertTrue(decodedResult.contains("/index/pages/h5/gnc/h5"));
            assertTrue(decodedResult.contains("weburl="));
            assertTrue(decodedResult.contains("test-g.meituan.com"));
            assertTrue(decodedResult.contains("dzcsr/biz-growth/community/product-transfer.html"));
            assertTrue(decodedResult.contains("distributorcode=distributor123-123"));
            assertTrue(decodedResult.contains("appId=app123"));
        }
    }

    @Test
    public void testBuildProductUrlWithQueryParamsInProdEnv() throws Throwable {
        String originpath = "https://example.com?param=value";
        try (MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class);
            MockedStatic<InformationGatheringService> mockedService = Mockito.mockStatic(InformationGatheringService.class)) {
            mockedEnvironment.when(Environment::isProductEnv).thenReturn(true);
            mockedService.when(() -> InformationGatheringService.buildPassParamString("distributor123")).thenReturn("pass_param_value");
            String result = handler.buildProductUrl(originpath, 123L, "distributor123", "app123", false, false);
            // Decode the URL to check its components
            String decodedResult = URLDecoder.decode(result, StandardCharsets.UTF_8.name());
            // Check for production environment URL components
            assertTrue(decodedResult.contains("/index/pages/h5/gnc/h5"));
            assertTrue(decodedResult.contains("weburl="));
            assertTrue(decodedResult.contains("g.meituan.com"));
            assertTrue(decodedResult.contains("dzcsr/biz-growth/community/product-transfer.html"));
            assertTrue(decodedResult.contains("distributorcode=distributor123-123"));
            assertTrue(decodedResult.contains("appId=app123"));
        }
    }

    @Test
    public void testBuildProductUrlForDeepSea() throws Throwable {
        String originpath = "https://example.com";
        try (MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class);
            MockedStatic<InformationGatheringService> mockedService = Mockito.mockStatic(InformationGatheringService.class)) {
            mockedEnvironment.when(Environment::isProductEnv).thenReturn(false);
            mockedService.when(() -> InformationGatheringService.buildPassParamString("distributor123")).thenReturn("pass_param_value");
            String result = handler.buildProductUrl(originpath, 123L, "distributor123", "app123", true, false);
            assertTrue(result.contains("/index/pages/h5/gnc/h5?weburl="));
        }
    }

    @Test
    public void testBuildProductUrlForQrCode() throws Throwable {
        String originpath = "https://example.com";
        try (MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class);
            MockedStatic<InformationGatheringService> mockedService = Mockito.mockStatic(InformationGatheringService.class)) {
            mockedEnvironment.when(Environment::isProductEnv).thenReturn(false);
            mockedService.when(() -> InformationGatheringService.buildPassParamString("distributor123")).thenReturn("pass_param_value");
            String result = handler.buildProductUrl(originpath, 123L, "distributor123", "app123", false, true);
            assertTrue(result.contains("/index/pages/h5/gnc/h5?weburl="));
        }
    }

    @Test
    public void testBuildProductUrlWithNonHttpUrl() throws Throwable {
        String originpath = "custom://example.com";
        try (MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class);
            MockedStatic<InformationGatheringService> mockedService = Mockito.mockStatic(InformationGatheringService.class)) {
            mockedEnvironment.when(Environment::isProductEnv).thenReturn(false);
            mockedService.when(() -> InformationGatheringService.buildPassParamString("distributor123")).thenReturn("pass_param_value");
            String result = handler.buildProductUrl(originpath, 123L, "distributor123", "app123", false, false);
            assertTrue(!result.contains("product=mtwxapp"));
        }
    }

    @Test
    public void testBuildProductUrlForDeepSeaAndQrCode() throws Throwable {
        String originpath = "https://example.com";
        try (MockedStatic<Environment> mockedEnvironment = Mockito.mockStatic(Environment.class);
            MockedStatic<InformationGatheringService> mockedService = Mockito.mockStatic(InformationGatheringService.class)) {
            mockedEnvironment.when(Environment::isProductEnv).thenReturn(false);
            mockedService.when(() -> InformationGatheringService.buildPassParamString("distributor123")).thenReturn("pass_param_value");
            String result = handler.buildProductUrl(originpath, 123L, "distributor123", "app123", true, true);
            assertTrue(result.contains("/index/pages/h5/gnc/h5?weburl="));
        }
    }

    @Test
    void testBuildPageUrlWithoutDeepSeaAndQrcode() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneDO.setPoiRestrict(1);
        sceneDO.setSceneCode("test-scene");
        try (MockedStatic<Environment> environmentMock = Mockito.mockStatic(Environment.class);
            MockedStatic<Cat> catMock = Mockito.mockStatic(Cat.class)) {
            environmentMock.when(Environment::isProductEnv).thenReturn(false);
            catMock.when(() -> Cat.logEvent(anyString(), anyString(), anyString(), anyString())).thenAnswer(invocation -> null);
            // act
            String result = abstractWxHandler.buildPageUrl(sceneDO, "distributor", "app123", false, false, null);
            // assert
            assertNotNull(result);
            String decodedResult = URLDecoder.decode(result, StandardCharsets.UTF_8.name());
            assertTrue(decodedResult.startsWith(ROOT_URL));
            assertTrue(decodedResult.contains("test-g.meituan.com"));
            assertTrue(decodedResult.contains(ROOT_URL_POSTFIX));
        }
    }
}
