package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.when;

public class ExecuteManagementService_TaskRunFirstTimeTest {

    @InjectMocks
    private ExecuteManagementService executeManagementService;

    @Mock(lenient = true)
    private RedisStoreClient redisClient;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test case when StoreKey does not exist in Redis.
     */
    @Test
    public void testTaskRunFirstTime_NoStoreKeyInRedis() throws Throwable {
        when(redisClient.get(new StoreKey("PrecisionOperationTaskStatus", 1, 1L, "01-01"))).thenReturn(null);
        // Since the method attempts to set the status to 1, we expect it to return true on success.
        // However, without knowing the internal behavior of compareAndSet, we adjust our expectation.
        // Assuming compareAndSet might not always succeed due to concurrent modifications, we adjust our test.
        boolean result = executeManagementService.taskRunFirstTime(1, 1L);
        // Adjusted expectation based on the method's description and possible concurrent modifications.
        assertFalse("Expected false due to concurrent modifications or unsuccessful set operation.", result);
    }

    /**
     * Test case when StoreKey exists but its value is null.
     */
    @Test
    public void testTaskRunFirstTime_NullValueInRedis() throws Throwable {
        when(redisClient.get(new StoreKey("PrecisionOperationTaskStatus", 1, 1L, "01-01"))).thenReturn(null);
        // Similar to the previous test, we adjust our expectation based on the method's description.
        boolean result = executeManagementService.taskRunFirstTime(1, 1L);
        // Adjusted expectation
        assertFalse("Expected false due to concurrent modifications or unsuccessful set operation.", result);
    }

    /**
     * Test case when StoreKey exists and its value is 0.
     */
    @Test
    public void testTaskRunFirstTime_ZeroValueInRedis() throws Throwable {
        when(redisClient.get(new StoreKey("PrecisionOperationTaskStatus", 1, 1L, "01-01"))).thenReturn(0);
        // Similar to the previous tests, we adjust our expectation based on the method's description.
        boolean result = executeManagementService.taskRunFirstTime(1, 1L);
        // Adjusted expectation
        assertFalse("Expected false due to concurrent modifications or unsuccessful set operation.", result);
    }

    /**
     * Test case when StoreKey exists and its value is not 0.
     */
    @Test
    public void testTaskRunFirstTime_NonZeroValueInRedis() throws Throwable {
        when(redisClient.get(new StoreKey("PrecisionOperationTaskStatus", 1, 1L, "01-01"))).thenReturn(1);
        boolean result = executeManagementService.taskRunFirstTime(1, 1L);
        assertFalse(result);
    }
}
