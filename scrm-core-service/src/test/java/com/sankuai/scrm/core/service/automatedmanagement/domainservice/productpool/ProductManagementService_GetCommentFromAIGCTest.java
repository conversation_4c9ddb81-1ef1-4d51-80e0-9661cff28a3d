package com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.dianping.general.unified.search.api.productshopsearch.GeneralProductShopSearchService;
import com.dianping.general.unified.search.api.productshopsearch.dto.LocationInfoDTO;
import com.dianping.general.unified.search.api.productshopsearch.dto.option.BaseSearchOption;
import com.dianping.general.unified.search.api.productshopsearch.dto.option.PageOption;
import com.dianping.general.unified.search.api.productshopsearch.dto.option.RetrievalOption;
import com.dianping.general.unified.search.api.productshopsearch.dto.option.SortOption;
import com.dianping.general.unified.search.api.productshopsearch.dto.unit.RetrievalUnit;
import com.dianping.general.unified.search.api.productshopsearch.dto.unit.SortUnit;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductBizTypeEnum;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopRetrievalExtendFieldEnum;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopSearchIdTypeEnum;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopSortFieldEnum;
import com.dianping.general.unified.search.api.productshopsearch.enums.ProductShopSortOrderEnum;
import com.dianping.general.unified.search.api.productshopsearch.request.GeneralProductShopSearchRequest;
import com.dianping.general.unified.search.api.productshopsearch.response.GeneralProductShopSearchResponse;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.automatedmanagement.dto.activitypage.ShelfProductInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.request.activitypage.QueryActivityPageShelfProductRequest;
import com.sankuai.scrm.core.service.aigc.service.SupplyMarketingTextService;
import com.sankuai.scrm.core.service.aigc.service.request.QuerySupplyMarketingTextRequest;
import com.sankuai.scrm.core.service.aigc.service.request.SupplyMarketingTextGenerationRequest;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductManagementService_GetCommentFromAIGCTest {

    @InjectMocks
    private ProductManagementService productManagementService;

    @Mock(lenient = true)
    private SupplyMarketingTextService supplyMarketingTextService;

    @Mock
    private GeneralProductShopSearchService generalProductShopSearchService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 getCommentFromAIGC 方法，当 productInfoDTO 为 null 时
     */
    @Test
    public void testGetCommentFromAIGCWhenProductInfoDTOIsNull() throws Throwable {
        String result = productManagementService.getCommentFromAIGC(null, Arrays.asList(1L, 2L, 3L), "appId");
        assertNull(result);
    }

    /**
     * Test getGeneralProductShopSearchResponse with all valid parameters
     */
    @Test
    public void testGetGeneralProductShopSearchResponse_Success() throws Throwable {
        // arrange
        int platform = 1;
        List<Integer> productIds = Arrays.asList(1, 2, 3);
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setCityId(1);
        request.setPageNo(1);
        request.setPageSize(10);
        request.setLat(31.23);
        request.setLng(121.47);
        ProductBizTypeEnum mockBizType = mock(ProductBizTypeEnum.class);
        GeneralProductShopSearchResponse expectedResponse = new GeneralProductShopSearchResponse();
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(expectedResponse);
        // act
        GeneralProductShopSearchResponse actualResponse = productManagementService.getGeneralProductShopSearchResponse(platform, productIds, request, mockBizType);
        // assert
        assertNotNull(actualResponse);
        assertEquals(expectedResponse, actualResponse);
        ArgumentCaptor<GeneralProductShopSearchRequest> requestCaptor = ArgumentCaptor.forClass(GeneralProductShopSearchRequest.class);
        verify(generalProductShopSearchService).searchProductShops(requestCaptor.capture());
        GeneralProductShopSearchRequest capturedRequest = requestCaptor.getValue();
        assertEquals(ProductShopSearchIdTypeEnum.MT_PP, capturedRequest.getIdPlatform());
        assertEquals(Collections.singletonList((long) request.getCityId()), capturedRequest.getBaseSearchOption().getCityIds());
    }

    /**
     * Test getGeneralProductShopSearchResponse with null productIds
     */
    @Test
    public void testGetGeneralProductShopSearchResponse_NullProductIds() throws Throwable {
        // arrange
        int platform = 1;
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setCityId(1);
        request.setPageNo(1);
        request.setPageSize(10);
        ProductBizTypeEnum mockBizType = mock(ProductBizTypeEnum.class);
        GeneralProductShopSearchResponse expectedResponse = new GeneralProductShopSearchResponse();
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(expectedResponse);
        // act
        GeneralProductShopSearchResponse actualResponse = productManagementService.getGeneralProductShopSearchResponse(platform, null, request, mockBizType);
        // assert
        assertNotNull(actualResponse);
        ArgumentCaptor<GeneralProductShopSearchRequest> requestCaptor = ArgumentCaptor.forClass(GeneralProductShopSearchRequest.class);
        verify(generalProductShopSearchService).searchProductShops(requestCaptor.capture());
        GeneralProductShopSearchRequest capturedRequest = requestCaptor.getValue();
        assertNull(capturedRequest.getBaseSearchOption().getProductIds());
    }

    /**
     * Test getGeneralProductShopSearchResponse with location info
     */
    @Test
    public void testGetGeneralProductShopSearchResponse_WithLocation() throws Throwable {
        // arrange
        int platform = 1;
        List<Integer> productIds = Arrays.asList(1);
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setCityId(1);
        request.setPageNo(1);
        request.setPageSize(10);
        request.setLat(31.23);
        request.setLng(121.47);
        ProductBizTypeEnum mockBizType = mock(ProductBizTypeEnum.class);
        GeneralProductShopSearchResponse expectedResponse = new GeneralProductShopSearchResponse();
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(expectedResponse);
        // act
        GeneralProductShopSearchResponse actualResponse = productManagementService.getGeneralProductShopSearchResponse(platform, productIds, request, mockBizType);
        // assert
        ArgumentCaptor<GeneralProductShopSearchRequest> requestCaptor = ArgumentCaptor.forClass(GeneralProductShopSearchRequest.class);
        verify(generalProductShopSearchService).searchProductShops(requestCaptor.capture());
        GeneralProductShopSearchRequest capturedRequest = requestCaptor.getValue();
        LocationInfoDTO locationInfo = capturedRequest.getLocationInfo();
        assertNotNull(locationInfo);
        assertEquals(BigDecimal.valueOf(request.getLat()), locationInfo.getLat());
        assertEquals(BigDecimal.valueOf(request.getLng()), locationInfo.getLng());
    }

    /**
     * Test getGeneralProductShopSearchResponse verifying sort options
     */
    @Test
    public void testGetGeneralProductShopSearchResponse_VerifySortOptions() throws Throwable {
        // arrange
        int platform = 1;
        List<Integer> productIds = Arrays.asList(1);
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setCityId(1);
        request.setPageNo(1);
        request.setPageSize(10);
        ProductBizTypeEnum mockBizType = mock(ProductBizTypeEnum.class);
        GeneralProductShopSearchResponse expectedResponse = new GeneralProductShopSearchResponse();
        when(generalProductShopSearchService.searchProductShops(any())).thenReturn(expectedResponse);
        // act
        GeneralProductShopSearchResponse actualResponse = productManagementService.getGeneralProductShopSearchResponse(platform, productIds, request, mockBizType);
        // assert
        ArgumentCaptor<GeneralProductShopSearchRequest> requestCaptor = ArgumentCaptor.forClass(GeneralProductShopSearchRequest.class);
        verify(generalProductShopSearchService).searchProductShops(requestCaptor.capture());
        GeneralProductShopSearchRequest capturedRequest = requestCaptor.getValue();
        SortOption sortOption = capturedRequest.getSortOption();
        assertNotNull(sortOption);
        List<SortUnit> sortUnits = sortOption.getSortUnits();
        assertNotNull(sortUnits);
        assertEquals(1, sortUnits.size());
        SortUnit sortUnit = sortUnits.get(0);
        assertEquals(ProductShopSortFieldEnum.SHOP_DISTANCE.getCode(), sortUnit.getSortField());
        assertEquals(ProductShopSortOrderEnum.ASC, sortUnit.getSortOrder());
    }

    /**
     * Test getGeneralProductShopSearchResponse when service throws exception
     */
    @Test(expected = RuntimeException.class)
    public void testGetGeneralProductShopSearchResponse_ServiceException() throws Throwable {
        // arrange
        int platform = 1;
        List<Integer> productIds = Arrays.asList(1);
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setCityId(1);
        ProductBizTypeEnum mockBizType = mock(ProductBizTypeEnum.class);
        when(generalProductShopSearchService.searchProductShops(any())).thenThrow(new RuntimeException("Service error"));
        // act
        productManagementService.getGeneralProductShopSearchResponse(platform, productIds, request, mockBizType);
    }
}
