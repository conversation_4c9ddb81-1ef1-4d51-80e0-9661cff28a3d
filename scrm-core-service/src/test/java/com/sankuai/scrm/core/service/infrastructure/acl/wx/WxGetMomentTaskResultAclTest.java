package com.sankuai.scrm.core.service.infrastructure.acl.wx;

import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.io.IOException;

@RunWith(MockitoJUnitRunner.class)
public class WxGetMomentTaskResultAclTest {

    @InjectMocks
    private WxGetMomentTaskResultAcl wxGetMomentTaskResultAcl;

    @Mock(lenient = true)
    private WeChatTokenAcl weChatTokenAcl;

    @Mock(lenient = true)
    private CorpAppConfigRepository appConfigRepository;

    private String jobId;

    private String appId;

    private String corpId;

    private WeChatTokenResult tokenResult;

    @Before
    public void setUp() {
        jobId = "jobId";
        appId = "appId";
        corpId = "corpId";
        tokenResult = new WeChatTokenResult();
        tokenResult.setAccess_token("access_token");
    }

    @Test(expected = Exception.class)
    public void testGetMomentTaskResultJobIdIsNull() throws Throwable {
        wxGetMomentTaskResultAcl.getMomentTaskResult(null, appId);
    }

    @Test(expected = Exception.class)
    public void testGetMomentTaskResultAppIdIsNull() throws Throwable {
        wxGetMomentTaskResultAcl.getMomentTaskResult(jobId, null);
    }

    @Test(expected = Exception.class)
    public void testGetMomentTaskResultAccessTokenIsNull() throws Throwable {
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
        tokenResult.setAccess_token(null);
        when(weChatTokenAcl.getTokenByCorpId(corpId)).thenReturn(tokenResult);
        wxGetMomentTaskResultAcl.getMomentTaskResult(jobId, appId);
    }

    @Test
    public void testGetMomentTaskResultSuccess() throws Throwable {
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
        when(weChatTokenAcl.getTokenByCorpId(corpId)).thenReturn(tokenResult);
        assertNotNull(wxGetMomentTaskResultAcl.getMomentTaskResult(jobId, appId));
    }
}
