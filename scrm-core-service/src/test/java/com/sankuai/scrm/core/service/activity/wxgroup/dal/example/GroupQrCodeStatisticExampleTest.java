package com.sankuai.scrm.core.service.activity.wxgroup.dal.example;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.util.ArrayList;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("GroupQrCodeStatisticExample Clear Method Tests")
class GroupQrCodeStatisticExampleTest {

    @Spy
    private GroupQrCodeStatisticExample exampleSpy;

    @Spy
    private GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();

    /**
     * 测试正常场景 - 正数页码和页大小
     */
    @Test
    public void testPageNormalCase() throws Throwable {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        int page = 2;
        int pageSize = 10;
        // act
        GroupQrCodeStatisticExample result = example.page(page, pageSize);
        // assert
        assertEquals(20, result.getOffset());
        assertEquals(10, result.getRows());
        assertSame(example, result);
    }

    /**
     * 测试边界场景 - 页码为0
     */
    @Test
    public void testPageWithPageZero() throws Throwable {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        int page = 0;
        int pageSize = 10;
        // act
        GroupQrCodeStatisticExample result = example.page(page, pageSize);
        // assert
        assertEquals(0, result.getOffset());
        assertEquals(10, result.getRows());
        assertSame(example, result);
    }

    /**
     * 测试边界场景 - 页大小为0
     */
    @Test
    public void testPageWithPageSizeZero() throws Throwable {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        int page = 2;
        int pageSize = 0;
        // act
        GroupQrCodeStatisticExample result = example.page(page, pageSize);
        // assert
        assertEquals(0, result.getOffset());
        assertEquals(0, result.getRows());
        assertSame(example, result);
    }

    /**
     * 测试边界场景 - 页码为负数
     */
    @Test
    public void testPageWithNegativePage() throws Throwable {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        int page = -1;
        int pageSize = 10;
        // act
        GroupQrCodeStatisticExample result = example.page(page, pageSize);
        // assert
        assertEquals(-10, result.getOffset());
        assertEquals(10, result.getRows());
        assertSame(example, result);
    }

    /**
     * 测试边界场景 - 页大小为负数
     */
    @Test
    public void testPageWithNegativePageSize() throws Throwable {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        int page = 2;
        int pageSize = -5;
        // act
        GroupQrCodeStatisticExample result = example.page(page, pageSize);
        // assert
        assertEquals(-10, result.getOffset());
        assertEquals(-5, result.getRows());
        assertSame(example, result);
    }

    /**
     * 测试边界场景 - 大数值计算
     */
    @Test
    public void testPageWithLargeNumbers() throws Throwable {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        int page = Integer.MAX_VALUE / 1000;
        int pageSize = 1000;
        int expectedOffset = (Integer.MAX_VALUE / 1000) * 1000;
        // act
        GroupQrCodeStatisticExample result = example.page(page, pageSize);
        // assert
        assertEquals(expectedOffset, result.getOffset());
        assertEquals(1000, result.getRows());
        assertSame(example, result);
    }

    /**
     * 测试异常场景 - 页码为null
     */
    @Test
    public void testPageWithNullPage() throws Throwable {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        Integer page = null;
        int pageSize = 10;
        // act & assert
        assertThrows(NullPointerException.class, () -> example.page(page, pageSize));
    }

    /**
     * 测试异常场景 - 页大小为null
     */
    @Test
    public void testPageWithNullPageSize() throws Throwable {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        int page = 2;
        Integer pageSize = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> example.page(page, pageSize));
    }

    /**
     * Test clear() method when all fields are populated
     * Verifies that all fields are properly reset to their default values
     */
    @Test
    @DisplayName("Should clear all populated fields")
    public void testClear_WhenFieldsArePopulated() {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        example.setOrderByClause("id DESC");
        example.setDistinct(true);
        example.setRows(10);
        example.setOffset(5);
        example.createCriteria().andIdEqualTo(1L);
        int initialCriteriaSize = example.getOredCriteria().size();
        assertTrue(initialCriteriaSize > 0, "Should have criteria before clearing");
        // act
        example.clear();
        // assert
        assertAll(() -> assertTrue(example.getOredCriteria().isEmpty(), "oredCriteria should be empty"), () -> assertNull(example.getOrderByClause(), "orderByClause should be null"), () -> assertFalse(example.isDistinct(), "distinct should be false"), () -> assertNull(example.getRows(), "rows should be null"), () -> assertNull(example.getOffset(), "offset should be null"));
    }

    /**
     * Test clear() method when fields are already empty/null
     * Verifies that clearing empty fields maintains the expected state
     */
    @Test
    @DisplayName("Should handle clearing already empty fields")
    public void testClear_WhenFieldsAreEmpty() {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        // act
        example.clear();
        // assert
        assertAll(() -> assertTrue(example.getOredCriteria().isEmpty(), "oredCriteria should be empty"), () -> assertNull(example.getOrderByClause(), "orderByClause should be null"), () -> assertFalse(example.isDistinct(), "distinct should be false"), () -> assertNull(example.getRows(), "rows should be null"), () -> assertNull(example.getOffset(), "offset should be null"));
    }

    /**
     * Test clear() method's idempotency
     * Verifies that multiple clear operations maintain consistent state
     */
    @Test
    @DisplayName("Should maintain consistent state after multiple clears")
    public void testClear_MultipleCalls() {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        example.setOrderByClause("id DESC");
        example.setDistinct(true);
        example.setRows(10);
        example.setOffset(5);
        example.createCriteria().andIdEqualTo(1L);
        // act
        example.clear();
        // second clear
        example.clear();
        // assert
        assertAll(() -> assertTrue(example.getOredCriteria().isEmpty(), "oredCriteria should be empty"), () -> assertNull(example.getOrderByClause(), "orderByClause should be null"), () -> assertFalse(example.isDistinct(), "distinct should be false"), () -> assertNull(example.getRows(), "rows should be null"), () -> assertNull(example.getOffset(), "offset should be null"));
    }

    /**
     * Test clear() method's behavior with extreme values
     * Verifies handling of boundary cases
     */
    @Test
    @DisplayName("Should handle boundary cases properly")
    public void testClear_BoundaryCases() {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        example.setRows(Integer.MAX_VALUE);
        example.setOffset(Integer.MIN_VALUE);
        // empty string
        example.setOrderByClause("");
        example.createCriteria();
        // multiple criteria
        example.createCriteria();
        // act
        example.clear();
        // assert
        assertAll(() -> assertTrue(example.getOredCriteria().isEmpty(), "oredCriteria should be empty"), () -> assertNull(example.getOrderByClause(), "orderByClause should be null"), () -> assertFalse(example.isDistinct(), "distinct should be false"), () -> assertNull(example.getRows(), "rows should be null"), () -> assertNull(example.getOffset(), "offset should be null"));
    }

    /**
     * Test that createCriteriaInternal creates a valid Criteria instance
     */
    @Test
    void testCreateCriteriaInternalCreatesValidInstance() {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        // act
        GroupQrCodeStatisticExample.Criteria criteria = example.createCriteriaInternal();
        // assert
        assertNotNull(criteria, "Created Criteria should not be null");
        assertTrue(criteria instanceof GroupQrCodeStatisticExample.Criteria, "Created instance should be of type Criteria");
    }

    /**
     * Test that createCriteriaInternal creates new instance each time
     */
    @Test
    void testCreateCriteriaInternalCreatesDifferentInstances() {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        // act
        GroupQrCodeStatisticExample.Criteria criteria1 = example.createCriteriaInternal();
        GroupQrCodeStatisticExample.Criteria criteria2 = example.createCriteriaInternal();
        // assert
        assertNotNull(criteria1, "First created Criteria should not be null");
        assertNotNull(criteria2, "Second created Criteria should not be null");
        assertNotSame(criteria1, criteria2, "Different calls should create different instances");
    }

    /**
     * Test that created Criteria has initialized criteria list
     */
    @Test
    void testCreateCriteriaInternalInitializesList() {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        // act
        GroupQrCodeStatisticExample.Criteria criteria = example.createCriteriaInternal();
        // assert
        assertNotNull(criteria.getCriteria(), "Criteria list should be initialized");
        assertTrue(criteria.getCriteria().isEmpty(), "Criteria list should be empty initially");
    }

    /**
     * Test createCriteriaInternal method invocation using spy
     */
    @Test
    void testCreateCriteriaInternalInvocation() {
        // act
        GroupQrCodeStatisticExample.Criteria criteria = exampleSpy.createCriteriaInternal();
        // assert
        verify(exampleSpy, times(1)).createCriteriaInternal();
        assertNotNull(criteria, "Created Criteria should not be null");
    }

    /**
     * Test limit method with valid positive numbers
     * Expected: Should set offset and rows values and return the same instance
     */
    @Test
    @DisplayName("Test limit with positive numbers")
    public void testLimitWithPositiveNumbers() {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        Integer offset = 10;
        Integer rows = 20;
        // act
        GroupQrCodeStatisticExample result = example.limit(offset, rows);
        // assert
        assertAll(() -> assertEquals(offset, result.getOffset(), "Offset should match the input value"), () -> assertEquals(rows, result.getRows(), "Rows should match the input value"), () -> assertSame(example, result, "Should return the same instance for chaining"));
    }

    /**
     * Test limit method with zero values
     * Expected: Should accept zero values and return the same instance
     */
    @Test
    @DisplayName("Test limit with zero values")
    public void testLimitWithZeroValues() {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        Integer offset = 0;
        Integer rows = 0;
        // act
        GroupQrCodeStatisticExample result = example.limit(offset, rows);
        // assert
        assertAll(() -> assertEquals(offset, result.getOffset(), "Offset should be zero"), () -> assertEquals(rows, result.getRows(), "Rows should be zero"), () -> assertSame(example, result, "Should return the same instance for chaining"));
    }

    /**
     * Test limit method with null values
     * Expected: Should accept null values and return the same instance
     */
    @Test
    @DisplayName("Test limit with null values")
    public void testLimitWithNullValues() {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        Integer offset = null;
        Integer rows = null;
        // act
        GroupQrCodeStatisticExample result = example.limit(offset, rows);
        // assert
        assertAll(() -> assertNull(result.getOffset(), "Offset should be null"), () -> assertNull(result.getRows(), "Rows should be null"), () -> assertSame(example, result, "Should return the same instance for chaining"));
    }

    /**
     * Test limit method chaining capability
     * Expected: Should update values on subsequent calls and maintain instance reference
     */
    @Test
    @DisplayName("Test limit method chaining")
    public void testLimitMethodChaining() {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        Integer firstOffset = 10;
        Integer firstRows = 20;
        Integer secondOffset = 30;
        Integer secondRows = 40;
        // act
        GroupQrCodeStatisticExample result = example.limit(firstOffset, firstRows).limit(secondOffset, secondRows);
        // assert
        assertAll(() -> assertEquals(secondOffset, result.getOffset(), "Offset should match last set value"), () -> assertEquals(secondRows, result.getRows(), "Rows should match last set value"), () -> assertSame(example, result, "Should return the same instance for chaining"));
    }

    /**
     * Test limit method with maximum integer values
     * Expected: Should handle maximum integer values correctly
     */
    @Test
    @DisplayName("Test limit with maximum integer values")
    public void testLimitWithMaxValues() {
        // arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        Integer offset = Integer.MAX_VALUE;
        Integer rows = Integer.MAX_VALUE;
        // act
        GroupQrCodeStatisticExample result = example.limit(offset, rows);
        // assert
        assertAll(() -> assertEquals(Integer.MAX_VALUE, result.getOffset(), "Offset should handle max integer"), () -> assertEquals(Integer.MAX_VALUE, result.getRows(), "Rows should handle max integer"), () -> assertSame(example, result, "Should return the same instance for chaining"));
    }

    /**
     * 测试正常情况 - 传入正整数
     */
    @Test
    public void testLimitWithPositiveNumber() {
        // arrange
        GroupQrCodeStatisticExample example = spy(new GroupQrCodeStatisticExample());
        int expectedRows = 10;
        // act
        GroupQrCodeStatisticExample result = example.limit(expectedRows);
        // assert
        assertEquals(expectedRows, result.getRows());
        assertSame(example, result);
        verify(example, times(1)).limit(expectedRows);
    }

    /**
     * 测试边界情况 - 传入0
     */
    @Test
    public void testLimitWithZero() {
        // arrange
        GroupQrCodeStatisticExample example = spy(new GroupQrCodeStatisticExample());
        int expectedRows = 0;
        // act
        GroupQrCodeStatisticExample result = example.limit(expectedRows);
        // assert
        assertEquals(expectedRows, result.getRows());
        assertSame(example, result);
        verify(example, times(1)).limit(expectedRows);
    }

    /**
     * 测试边界情况 - 传入负数
     */
    @Test
    public void testLimitWithNegativeNumber() {
        // arrange
        GroupQrCodeStatisticExample example = spy(new GroupQrCodeStatisticExample());
        int expectedRows = -5;
        // act
        GroupQrCodeStatisticExample result = example.limit(expectedRows);
        // assert
        assertEquals(expectedRows, result.getRows());
        assertSame(example, result);
        verify(example, times(1)).limit(expectedRows);
    }

    /**
     * 测试边界情况 - 传入null
     */
    @Test
    public void testLimitWithNull() {
        // arrange
        GroupQrCodeStatisticExample example = spy(new GroupQrCodeStatisticExample());
        // act
        GroupQrCodeStatisticExample result = example.limit(null);
        // assert
        assertNull(result.getRows());
        assertSame(example, result);
        verify(example, times(1)).limit(null);
    }

    /**
     * 测试边界情况 - 传入Integer.MAX_VALUE
     */
    @Test
    public void testLimitWithMaxValue() {
        // arrange
        GroupQrCodeStatisticExample example = spy(new GroupQrCodeStatisticExample());
        int expectedRows = Integer.MAX_VALUE;
        // act
        GroupQrCodeStatisticExample result = example.limit(expectedRows);
        // assert
        assertEquals(expectedRows, result.getRows());
        assertSame(example, result);
        verify(example, times(1)).limit(expectedRows);
    }

    /**
     * 测试方法链式调用
     */
    @Test
    public void testMethodChaining() {
        // arrange
        GroupQrCodeStatisticExample example = spy(new GroupQrCodeStatisticExample());
        // act
        GroupQrCodeStatisticExample result = example.limit(10).limit(20);
        // assert
        assertEquals(20, result.getRows());
        assertSame(example, result);
        verify(example, times(1)).limit(10);
        verify(example, times(1)).limit(20);
    }

    /**
     * Tests the scenario when oredCriteria list is empty.
     * Verifies that:
     * 1. The created criteria is not null
     * 2. The oredCriteria list size becomes 1
     * 3. The created criteria is added to oredCriteria
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsEmpty() throws Throwable {
        // Arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        // Act
        GroupQrCodeStatisticExample.Criteria criteria = example.createCriteria();
        // Assert
        assertNotNull(criteria, "Created criteria should not be null");
        assertEquals(1, example.getOredCriteria().size(), "OredCriteria size should be 1 after first creation");
        assertSame(criteria, example.getOredCriteria().get(0), "Created criteria should be the same instance in oredCriteria");
    }

    /**
     * Tests the scenario when oredCriteria list is not empty.
     * Verifies that:
     * 1. The second criteria is not null
     * 2. The oredCriteria list size remains 1 (no new criteria added)
     * 3. The original criteria remains in oredCriteria
     * 4. The second criteria is a different instance
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsNotEmpty() throws Throwable {
        // Arrange
        GroupQrCodeStatisticExample example = new GroupQrCodeStatisticExample();
        GroupQrCodeStatisticExample.Criteria criteria1 = example.createCriteria();
        // Act
        GroupQrCodeStatisticExample.Criteria criteria2 = example.createCriteria();
        // Assert
        assertNotNull(criteria2, "Second criteria should not be null");
        assertEquals(1, example.getOredCriteria().size(), "OredCriteria size should remain 1 when list is not empty");
        assertSame(criteria1, example.getOredCriteria().get(0), "Original criteria should remain in oredCriteria");
        assertNotSame(criteria1, criteria2, "Second criteria should be a different instance");
    }

    /**
     * Test case: Verify or() method creates new criteria and adds it to oredCriteria list
     * Expected: New criteria should be created and added to list
     */
    @Test
    @DisplayName("Test or() creates and adds new criteria to list")
    public void testOr_ShouldCreateAndAddNewCriteria() throws Throwable {
        // arrange
        int initialSize = example.getOredCriteria().size();
        // act
        GroupQrCodeStatisticExample.Criteria result = example.or();
        // assert
        assertNotNull(result, "Returned Criteria should not be null");
        assertEquals(initialSize + 1, example.getOredCriteria().size(), "OredCriteria list should increase by 1");
        assertTrue(example.getOredCriteria().contains(result), "Created criteria should be in the oredCriteria list");
    }

    /**
     * Test case: Verify or() method returns unique instances for multiple calls
     * Expected: Each call should return a new unique Criteria instance
     */
    @Test
    @DisplayName("Test or() returns unique instances for multiple calls")
    public void testOr_ShouldReturnUniqueInstances() throws Throwable {
        // act
        GroupQrCodeStatisticExample.Criteria result1 = example.or();
        GroupQrCodeStatisticExample.Criteria result2 = example.or();
        // assert
        assertNotNull(result1, "First criteria should not be null");
        assertNotNull(result2, "Second criteria should not be null");
        assertNotSame(result1, result2, "Each call should return different instance");
        assertEquals(2, example.getOredCriteria().size(), "Should have two criteria in the list");
    }

    /**
     * Test case: Verify or() method maintains correct order in oredCriteria list
     * Expected: Criteria should be added to list in the order they are created
     */
    @Test
    @DisplayName("Test or() maintains correct order in criteria list")
    public void testOr_ShouldMaintainListOrder() throws Throwable {
        // act
        GroupQrCodeStatisticExample.Criteria first = example.or();
        GroupQrCodeStatisticExample.Criteria second = example.or();
        GroupQrCodeStatisticExample.Criteria third = example.or();
        // assert
        ArrayList<GroupQrCodeStatisticExample.Criteria> criteria = (ArrayList<GroupQrCodeStatisticExample.Criteria>) example.getOredCriteria();
        assertEquals(first, criteria.get(0), "First criteria should be at index 0");
        assertEquals(second, criteria.get(1), "Second criteria should be at index 1");
        assertEquals(third, criteria.get(2), "Third criteria should be at index 2");
        assertEquals(3, criteria.size(), "Should have three criteria in the list");
    }

    /**
     * Test case: Verify or() method behavior with existing criteria in list
     * Expected: New criteria should be added to existing list
     */
    @Test
    @DisplayName("Test or() with existing criteria in list")
    public void testOr_WithExistingCriteria() throws Throwable {
        // arrange
        GroupQrCodeStatisticExample.Criteria existingCriteria = example.or();
        int initialSize = example.getOredCriteria().size();
        // act
        GroupQrCodeStatisticExample.Criteria result = example.or();
        // assert
        assertNotNull(result, "Returned Criteria should not be null");
        assertEquals(initialSize + 1, example.getOredCriteria().size(), "Should have increased the list size by 1");
        assertEquals(existingCriteria, example.getOredCriteria().get(0), "Existing criteria should remain at first position");
        assertEquals(result, example.getOredCriteria().get(1), "New criteria should be at second position");
    }
}
