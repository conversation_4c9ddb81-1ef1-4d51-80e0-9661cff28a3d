package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationConditionDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationNodeTypeEnum;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class StaffMomentTouchActionGetStaffMomentTouchCorpTagListTest {

    @InjectMocks
    private StaffMomentTouchAction staffMomentTouchAction;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ScrmProcessOrchestrationNodeDTO currentProcessingNode;

    @Mock
    private ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
    }

    /**
     * Test case for demo scene path
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_DemoScene() throws Throwable {
        // arrange
        processOrchestrationDTO.setDemoScene(true);
        List<String> demoCorpTagList = Arrays.asList("tag1", "tag2");
        processOrchestrationDTO.setDemoCorpTagList(demoCorpTagList);
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertNotNull(result);
        assertEquals(demoCorpTagList, result);
    }

    /**
     * Test case for null nodeMediumDTO
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_NullNodeMediumDTO() throws Throwable {
        // arrange
        processOrchestrationDTO.setNodeMediumDTO(null);
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for empty processOrchestrationNodeDTOList
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_EmptyNodeList() throws Throwable {
        // arrange
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        when(nodeMediumDTO.getProcessOrchestrationNodeDTOList()).thenReturn(new ArrayList<>());
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for crowdPackType == 0
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_CrowdPackTypeZero() throws Throwable {
        // arrange
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        processOrchestrationDTO.setCrowdPackType(0);
        List<ScrmProcessOrchestrationNodeDTO> nodeList = new ArrayList<>();
        nodeList.add(new ScrmProcessOrchestrationNodeDTO());
        when(nodeMediumDTO.getProcessOrchestrationNodeDTOList()).thenReturn(nodeList);
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("ALL_USER_TAG", result.get(0));
    }

    /**
     * Test case for crowdPackType != 0 with valid strategy
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_ValidStrategy() throws Throwable {
        // arrange
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        processOrchestrationDTO.setCrowdPackType(1);
        ScrmProcessOrchestrationNodeDTO rootNode = new ScrmProcessOrchestrationNodeDTO();
        rootNode.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ROOT.getValue());
        List<ScrmProcessOrchestrationNodeDTO> nodeList = Arrays.asList(rootNode);
        when(nodeMediumDTO.getProcessOrchestrationNodeDTOList()).thenReturn(nodeList);
        ScrmCrowdPackUpdateStrategyInfoDTO strategyInfo = new ScrmCrowdPackUpdateStrategyInfoDTO();
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetails = new ArrayList<>();
        ScrmCrowdPackUpdateStrategyDetailDTO detail = new ScrmCrowdPackUpdateStrategyDetailDTO();
        // CORP_TAG
        detail.setFilterFieldId(40001L);
        detail.setParam(Arrays.asList("tag1"));
        detail.setGroupId(1);
        strategyDetails.add(detail);
        strategyInfo.setCrowdPackUpdateStrategy(strategyDetails);
        processOrchestrationDTO.setCrowdPackUpdateStrategyInfoDTO(strategyInfo);
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("tag1", result.get(0));
    }

    /**
     * Test case for valid condition nodes
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_ValidConditionNodes() throws Throwable {
        // arrange
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        processOrchestrationDTO.setCrowdPackType(1);
        // Setup root node
        ScrmProcessOrchestrationNodeDTO rootNode = new ScrmProcessOrchestrationNodeDTO();
        rootNode.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ROOT.getValue());
        rootNode.setId(0L);
        // Setup condition node
        ScrmProcessOrchestrationNodeDTO conditionNode = new ScrmProcessOrchestrationNodeDTO();
        conditionNode.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_CONDITION.getValue());
        conditionNode.setId(1L);
        // Set parent to root node
        conditionNode.setPreNodeId(0L);
        // Setup current processing node
        currentProcessingNode.setId(2L);
        currentProcessingNode.setPreNodeId(1L);
        currentProcessingNode.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ACTION.getValue());
        List<ScrmProcessOrchestrationNodeDTO> nodeList = Arrays.asList(rootNode, conditionNode, currentProcessingNode);
        when(nodeMediumDTO.getProcessOrchestrationNodeDTOList()).thenReturn(nodeList);
        // Setup condition details
        List<ScrmProcessOrchestrationConditionDetailDTO> conditionDetails = new ArrayList<>();
        ScrmProcessOrchestrationConditionDetailDTO detail = new ScrmProcessOrchestrationConditionDetailDTO();
        // CORP_TAG
        detail.setFilterFieldId(40001L);
        detail.setParam(Arrays.asList("tag1"));
        detail.setGroupId(1);
        conditionDetails.add(detail);
        // Setup condition map
        Map<Long, List<ScrmProcessOrchestrationConditionDetailDTO>> conditionMap = new HashMap<>();
        conditionMap.put(1L, conditionDetails);
        when(nodeMediumDTO.getConditionDetailDTOList(1L)).thenReturn(conditionDetails);
        // Setup root node corp tag list
        ScrmCrowdPackUpdateStrategyInfoDTO strategyInfo = new ScrmCrowdPackUpdateStrategyInfoDTO();
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyDetails = new ArrayList<>();
        ScrmCrowdPackUpdateStrategyDetailDTO strategyDetail = new ScrmCrowdPackUpdateStrategyDetailDTO();
        // CORP_TAG
        strategyDetail.setFilterFieldId(40001L);
        strategyDetail.setParam(Arrays.asList("tag1"));
        strategyDetail.setGroupId(1);
        strategyDetails.add(strategyDetail);
        strategyInfo.setCrowdPackUpdateStrategy(strategyDetails);
        processOrchestrationDTO.setCrowdPackUpdateStrategyInfoDTO(strategyInfo);
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals("tag1", result.get(0));
    }

    /**
     * Test case for empty condition nodes
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_EmptyConditionNodes() throws Throwable {
        // arrange
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        processOrchestrationDTO.setCrowdPackType(1);
        when(nodeMediumDTO.getProcessOrchestrationNodeDTOList()).thenReturn(new ArrayList<>());
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
