package com.sankuai.scrm.core.service.external.contact.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.meituan.image.client.utils.Md5Utils;
import com.sankuai.dz.srcm.external.contact.enums.MobileAddFriendRealTimeTaskStatuEnum;
import com.sankuai.dz.srcm.external.contact.request.MobileAddFriendRealTimeRequest;
import com.sankuai.inf.kms.pangolin.api.service.IEncryptService;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ScrmMobileAddFriendTaskDO;
import com.sankuai.scrm.core.service.external.contact.dal.mapper.ext.ExtScrmMobileAddFriendTaskDOMapper;
import java.security.GeneralSecurityException;
import java.util.Date;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MobileAddFriendRealTimeDomainServiceCreateMobileAddFriendRealTimeTaskTest {

    @Mock
    private ExtScrmMobileAddFriendTaskDOMapper mobileAddFriendTaskDOMapper;

    @Mock
    private IEncryptService phoneEncryptService;

    @InjectMocks
    private MobileAddFriendRealTimeDomainService mobileAddFriendRealTimeDomainService;

    private MobileAddFriendRealTimeRequest validRequest;

    private Date executeTime;

    private ScrmMobileAddFriendTaskDO savedTask;

    @BeforeEach
    void setUp() {
        validRequest = new MobileAddFriendRealTimeRequest();
        validRequest.setAppId("app123");
        validRequest.setAccountId("acc123");
        validRequest.setAddNumber("***********");
        validRequest.setNumberType(1);
        validRequest.setWelcomeContent("welcome");
        executeTime = new Date();
        savedTask = new ScrmMobileAddFriendTaskDO();
        savedTask.setId(12345L);
    }

    /**
     * 测试正常创建任务场景
     */
    @Test
    void testCreateMobileAddFriendRealTimeTask_Success() throws Throwable {
        // arrange
        when(phoneEncryptService.encryptUTF8String(any())).thenReturn("encryptedNumber");
        when(mobileAddFriendTaskDOMapper.insertSelective(any())).thenAnswer(invocation -> {
            ScrmMobileAddFriendTaskDO task = invocation.getArgument(0);
            task.setId(savedTask.getId());
            return 1;
        });
        // act
        Long result = mobileAddFriendRealTimeDomainService.createMobileAddFriendRealTimeTask(validRequest, executeTime);
        // assert
        assertNotNull(result);
        assertEquals(savedTask.getId(), result);
        verify(phoneEncryptService).encryptUTF8String(validRequest.getAddNumber());
        verify(mobileAddFriendTaskDOMapper).insertSelective(any(ScrmMobileAddFriendTaskDO.class));
    }

    /**
     * 测试加密手机号失败场景
     */
    @Test
    void testCreateMobileAddFriendRealTimeTask_EncryptFailure() throws Throwable {
        // arrange
        when(phoneEncryptService.encryptUTF8String(any())).thenThrow(new GeneralSecurityException("encrypt error"));
        // act
        Long result = mobileAddFriendRealTimeDomainService.createMobileAddFriendRealTimeTask(validRequest, executeTime);
        // assert
        assertNull(result);
        verify(phoneEncryptService).encryptUTF8String(validRequest.getAddNumber());
        verify(mobileAddFriendTaskDOMapper, never()).insertSelective(any());
    }

    /**
     * 测试请求参数为null场景
     */
    @Test
    void testCreateMobileAddFriendRealTimeTask_NullRequest() throws Throwable {
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            mobileAddFriendRealTimeDomainService.createMobileAddFriendRealTimeTask(null, executeTime);
        });
    }

    /**
     * 测试执行时间为null场景
     */
    @Test
    void testCreateMobileAddFriendRealTimeTask_NullExecuteTime() throws Throwable {
        // arrange
        when(phoneEncryptService.encryptUTF8String(any())).thenReturn("encryptedNumber");
        when(mobileAddFriendTaskDOMapper.insertSelective(any())).thenAnswer(invocation -> {
            ScrmMobileAddFriendTaskDO task = invocation.getArgument(0);
            task.setId(savedTask.getId());
            return 1;
        });
        // act
        Long result = mobileAddFriendRealTimeDomainService.createMobileAddFriendRealTimeTask(validRequest, null);
        // assert
        assertNotNull(result);
        assertEquals(savedTask.getId(), result);
        verify(phoneEncryptService).encryptUTF8String(validRequest.getAddNumber());
        verify(mobileAddFriendTaskDOMapper).insertSelective(any(ScrmMobileAddFriendTaskDO.class));
    }

    /**
     * 测试空手机号场景
     */
    @Test
    void testCreateMobileAddFriendRealTimeTask_EmptyAddNumber() throws Throwable {
        // arrange
        validRequest.setAddNumber("");
        when(phoneEncryptService.encryptUTF8String(any())).thenReturn("encryptedNumber");
        when(mobileAddFriendTaskDOMapper.insertSelective(any())).thenAnswer(invocation -> {
            ScrmMobileAddFriendTaskDO task = invocation.getArgument(0);
            task.setId(savedTask.getId());
            return 1;
        });
        // act
        Long result = mobileAddFriendRealTimeDomainService.createMobileAddFriendRealTimeTask(validRequest, executeTime);
        // assert
        assertNotNull(result);
        assertEquals(savedTask.getId(), result);
        verify(phoneEncryptService).encryptUTF8String(validRequest.getAddNumber());
        verify(mobileAddFriendTaskDOMapper).insertSelective(any(ScrmMobileAddFriendTaskDO.class));
    }

    /**
     * 测试空欢迎语场景
     */
    @Test
    void testCreateMobileAddFriendRealTimeTask_EmptyWelcomeContent() throws Throwable {
        // arrange
        validRequest.setWelcomeContent("");
        when(phoneEncryptService.encryptUTF8String(any())).thenReturn("encryptedNumber");
        when(mobileAddFriendTaskDOMapper.insertSelective(any())).thenAnswer(invocation -> {
            ScrmMobileAddFriendTaskDO task = invocation.getArgument(0);
            task.setId(savedTask.getId());
            return 1;
        });
        // act
        Long result = mobileAddFriendRealTimeDomainService.createMobileAddFriendRealTimeTask(validRequest, executeTime);
        // assert
        assertNotNull(result);
        assertEquals(savedTask.getId(), result);
        verify(phoneEncryptService).encryptUTF8String(validRequest.getAddNumber());
        verify(mobileAddFriendTaskDOMapper).insertSelective(any(ScrmMobileAddFriendTaskDO.class));
    }

    /**
     * 测试MD5生成逻辑
     */
    @Test
    void testCreateMobileAddFriendRealTimeTask_Md5Generation() throws Throwable {
        // arrange
        String expectedMd5 = Md5Utils.md5ToString(validRequest.getAddNumber().getBytes());
        when(phoneEncryptService.encryptUTF8String(any())).thenReturn("encryptedNumber");
        when(mobileAddFriendTaskDOMapper.insertSelective(any())).thenAnswer(invocation -> {
            ScrmMobileAddFriendTaskDO task = invocation.getArgument(0);
            task.setId(savedTask.getId());
            return 1;
        });
        // act
        Long result = mobileAddFriendRealTimeDomainService.createMobileAddFriendRealTimeTask(validRequest, executeTime);
        // assert
        assertNotNull(result);
        assertEquals(savedTask.getId(), result);
        verify(mobileAddFriendTaskDOMapper).insertSelective(argThat(task -> expectedMd5.equals(task.getAddNumberMd5())));
    }

    /**
     * 测试任务状态设置
     */
    @Test
    void testCreateMobileAddFriendRealTimeTask_TaskStatus() throws Throwable {
        // arrange
        when(phoneEncryptService.encryptUTF8String(any())).thenReturn("encryptedNumber");
        when(mobileAddFriendTaskDOMapper.insertSelective(any())).thenAnswer(invocation -> {
            ScrmMobileAddFriendTaskDO task = invocation.getArgument(0);
            task.setId(savedTask.getId());
            return 1;
        });
        // act
        Long result = mobileAddFriendRealTimeDomainService.createMobileAddFriendRealTimeTask(validRequest, executeTime);
        // assert
        assertNotNull(result);
        assertEquals(savedTask.getId(), result);
        verify(mobileAddFriendTaskDOMapper).insertSelective(argThat(task -> MobileAddFriendRealTimeTaskStatuEnum.NOT_STARTED.getCode() == task.getTaskStatus()));
    }

    /**
     * 测试所有字段正确设置
     */
    @Test
    void testCreateMobileAddFriendRealTimeTask_AllFieldsSetCorrectly() throws Throwable {
        // arrange
        when(phoneEncryptService.encryptUTF8String(any())).thenReturn("encryptedNumber");
        when(mobileAddFriendTaskDOMapper.insertSelective(any())).thenAnswer(invocation -> {
            ScrmMobileAddFriendTaskDO task = invocation.getArgument(0);
            task.setId(savedTask.getId());
            return 1;
        });
        // act
        Long result = mobileAddFriendRealTimeDomainService.createMobileAddFriendRealTimeTask(validRequest, executeTime);
        // assert
        assertNotNull(result);
        assertEquals(savedTask.getId(), result);
        verify(mobileAddFriendTaskDOMapper).insertSelective(argThat(task -> validRequest.getAppId().equals(task.getAppId()) && validRequest.getAccountId().equals(task.getAccountId()) && "encryptedNumber".equals(task.getAddNumber()) && validRequest.getNumberType().equals(task.getNumberType()) && validRequest.getWelcomeContent().equals(task.getWelcomeContent()) && task.getAddNumberMd5() != null));
    }
}
