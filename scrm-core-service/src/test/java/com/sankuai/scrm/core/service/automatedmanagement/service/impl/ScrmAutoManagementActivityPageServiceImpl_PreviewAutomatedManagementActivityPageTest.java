package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.scrm.core.service.automatedmanagement.service.impl.ScrmAutoManagementActivityPageServiceImpl;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.activitypage.ActivityPageManagementService;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import com.sankuai.dz.srcm.automatedmanagement.request.activitypage.PreviewActivityPageByIdRequest;
import org.junit.runner.RunWith;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.dz.srcm.automatedmanagement.response.activitypage.ShelfProductInfoResultVO;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmAutoManagementActivityPageServiceImpl_PreviewAutomatedManagementActivityPageTest {

    @InjectMocks
    private ScrmAutoManagementActivityPageServiceImpl scrmAutoManagementActivityPageService;

    @Mock(lenient = true)
    private ProductManagementService productManagementService;

    @Mock(lenient = true)
    private ActivityPageManagementService activityPageManagementService;

    private PreviewActivityPageByIdRequest request;

    @Before
    public void setUp() {
        request = new PreviewActivityPageByIdRequest();
        request.setAppId("appId");
        request.setActivityPageId(1L);
    }

    @Test
    public void testPreviewAutomatedManagementActivityPageRequestIsNull() {
        RemoteResponse<ShelfProductInfoResultVO> response = scrmAutoManagementActivityPageService.previewAutomatedManagementActivityPage(null);
        assertEquals("请求参数不能为空", response.getMsg());
    }

    @Test
    public void testPreviewAutomatedManagementActivityPageAppIdIsNull() {
        request.setAppId(null);
        RemoteResponse<ShelfProductInfoResultVO> response = scrmAutoManagementActivityPageService.previewAutomatedManagementActivityPage(request);
        assertEquals("appId不能为空", response.getMsg());
    }

    @Test
    public void testPreviewAutomatedManagementActivityPageActivityPageIdIsNull() {
        request.setActivityPageId(null);
        RemoteResponse<ShelfProductInfoResultVO> response = scrmAutoManagementActivityPageService.previewAutomatedManagementActivityPage(request);
        assertEquals("活动页id不能为空", response.getMsg());
    }

    @Test
    public void testPreviewAutomatedManagementActivityPageNormal() {
        when(productManagementService.queryPreviewShelfProductInfo(anyLong(), anyString())).thenReturn(null);
        RemoteResponse<ShelfProductInfoResultVO> response = scrmAutoManagementActivityPageService.previewAutomatedManagementActivityPage(request);
        assertEquals("success", response.getMsg());
    }

    @Test
    public void testPreviewAutomatedManagementActivityPageException() {
        when(productManagementService.queryPreviewShelfProductInfo(anyLong(), anyString())).thenThrow(new RuntimeException("exception"));
        RemoteResponse<ShelfProductInfoResultVO> response = scrmAutoManagementActivityPageService.previewAutomatedManagementActivityPage(request);
        assertEquals("exception", response.getMsg());
    }
}
