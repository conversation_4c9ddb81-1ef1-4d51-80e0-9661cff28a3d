package com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool;

import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductPoolUpdateObjectTemplateDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationUploadProductsEnum;
import com.sankuai.medicalcosmetology.product.selectify.api.service.CommonSelectPoolItemService;
import com.sankuai.medicalcosmetology.product.selectify.api.service.CommonSelectPoolService;
import com.sankuai.mpproduct.idservice.api.enums.BizProductIdType;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductItemsDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductTagsDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationProductTagMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.DownloadUploadingResultDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductManagementService_UploadProductsTest {

    @Spy
    @InjectMocks
    private ProductManagementService productManagementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductItemsDOMapper productItemsDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductTagsDOMapper productTagsDOMapper;

    @Mock(lenient = true)
    private ExtScrmAmProcessOrchestrationProductTagMapDOMapper extScrmAmProcessOrchestrationProductTagMapDOMapper;

    @Mock(lenient = true)
    private ProductIdService productIdService;

    @Mock(lenient = true)
    private ProductInfoService productInfoService;

    @Mock
    private CommonSelectPoolService commonSelectPoolService;

    @Mock(lenient = true)
    private CommonSelectPoolItemService commonSelectPoolItemService;

    private List<ProductPoolUpdateObjectTemplateDTO> products;

    private String appId;

    @Before
    public void setUp() {
        products = new ArrayList<>();
        appId = "testAppId";
    }

    @Test
    public void testUploadProductsAppIdOrProductsIsNull() throws Throwable {
        appId = null;
        products = null;
        Map<Integer, List<DownloadUploadingResultDTO>> result = productManagementService.uploadProducts(products, appId);
        assertNotNull(result);
        assertTrue(result.containsKey(ScrmAmProcessOrchestrationUploadProductsEnum.FAILED.getCode()));
    }

    @Test
    public void testUploadProductsProductTypeNotExist() throws Throwable {
        ProductPoolUpdateObjectTemplateDTO product = new ProductPoolUpdateObjectTemplateDTO();
        product.setProductId(1L);
        product.setType("999");
        product.setTags(Arrays.asList("tag1"));
        products.add(product);
        when(productIdService.isProductIdExists(anyLong(), any(BizProductIdType.class))).thenReturn(false);
        Map<Integer, List<DownloadUploadingResultDTO>> result = productManagementService.uploadProducts(products, appId);
        assertNotNull(result);
        assertTrue(result.containsKey(ScrmAmProcessOrchestrationUploadProductsEnum.FAILED.getCode()));
    }

    @Test
    public void testUploadProductsTagIsEmpty() throws Throwable {
        ProductPoolUpdateObjectTemplateDTO product = new ProductPoolUpdateObjectTemplateDTO();
        product.setProductId(1L);
        product.setType("1");
        product.setTags(new ArrayList<>());
        products.add(product);
        when(productIdService.isProductIdExists(anyLong(), any(BizProductIdType.class))).thenReturn(true);
        Map<Integer, List<DownloadUploadingResultDTO>> result = productManagementService.uploadProducts(products, appId);
        assertNotNull(result);
        assertTrue(result.containsKey(ScrmAmProcessOrchestrationUploadProductsEnum.FAILED.getCode()));
    }

    @Test
    public void testUploadProductsQueryProductsInfoFailed() throws Throwable {
        ProductPoolUpdateObjectTemplateDTO product = new ProductPoolUpdateObjectTemplateDTO();
        product.setProductId(1L);
        product.setType("1");
        product.setTags(Arrays.asList("tag1"));
        products.add(product);
        when(productIdService.isProductIdExists(anyLong(), any(BizProductIdType.class))).thenReturn(true);
        doReturn(new HashMap<>()).when(productInfoService).queryProductsInfo(any());
        Map<Integer, List<DownloadUploadingResultDTO>> result = productManagementService.uploadProducts(products, appId);
        assertNotNull(result);
        assertTrue(result.containsKey(ScrmAmProcessOrchestrationUploadProductsEnum.FAILED.getCode()));
    }

//    @Test
//    public void testUploadProductsTagsExist() throws Throwable {
////        ProductPoolUpdateObjectTemplateDTO product = new ProductPoolUpdateObjectTemplateDTO();
////        product.setProductId(1L);
////        product.setType("团购");
////        product.setTags(Arrays.asList("tag1"));
////        products.add(product);
////        when(productIdService.isProductIdExists(anyLong(), any(BizProductIdType.class))).thenReturn(true);
////        Map<Integer, Map<Long, QueryProductInfoDTO>> productInfo = new HashMap<>();
////        Map<Long, QueryProductInfoDTO> id2Info = new HashMap<>();
////        QueryProductInfoDTO info = QueryProductInfoDTO.builder()
////                .originalSalePrice("100")
////                .marketPrice("200")
////                .mtStores("10")
////                .dpStores("20")
////                .mtCityIds(Arrays.asList(1, 2))
////                .dpCityIds(Arrays.asList(3, 4))
////                .productInfo(ProductInfo.builder().dpProductId(1L).productId(1L).picture("pic").title("title").build())
////                .build();
////        id2Info.put(1L, info);
////        productInfo.put(1, id2Info);
////        when(productInfoService.queryProductsInfo(any())).thenReturn(productInfo);
////        when(productManagementService.getVisibleTagsNotExist(eq(appId), any())).thenReturn(new ArrayList<>());
////        Map<Integer, List<DownloadUploadingResultDTO>> result = productManagementService.uploadProducts(products, appId);
////        assertNotNull(result);
////        assertTrue(result.containsKey(ScrmAmProcessOrchestrationUploadProductsEnum.UPDATE_SUCCESS.getCode()));
//    }


}
