package com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.impl;

import com.sankuai.dz.srcm.automatedmanagement.request.CreateExcelUploadingCrowdPackRequest;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.exception.ExcelUploadingCrowdPackException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.fail;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

/**
 * Test class for CrowdPackInnerServiceImpl's createExcelUploadingCrowdPack method
 */
@RunWith(MockitoJUnitRunner.Silent.class)
public class CrowdPackInnerServiceImplCreateExcelUploadingCrowdPackTest {

    @InjectMocks
    private CrowdPackInnerServiceImpl crowdPackInnerService;

    @Mock
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock
    private ExecuteWriteDomainService executeWriteDomainService;

    private CreateExcelUploadingCrowdPackRequest request;

    @Before
    public void setUp() {
        request = new CreateExcelUploadingCrowdPackRequest();
        request.setMisId("test_mis");
        request.setSsoMisId("test_mis");
        request.setAppId("test_app");
        request.setCrowdPackName("test_pack");
        request.setDesc("test_desc");
        request.setUrl("test_url");
    }

    /**
     * Test case when misId doesn't match ssoMisId
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testCreateExcelUploadingCrowdPack_MisIdMismatch() throws Throwable {
        // arrange
        request.setMisId("test_mis1");
        request.setSsoMisId("test_mis2");
        // act & assert
        try {
            crowdPackInnerService.createExcelUploadingCrowdPack(request);
            fail("Should throw ExcelUploadingCrowdPackException");
        } finally {
            verify(crowdPackWriteDomainService, never()).insertCrowdPackBaseInfo(any());
            verify(executeWriteDomainService, never()).concurrentUpdateExcelCrowdPackAsync(anyString(), any(), anyLong(), anyString());
        }
    }

    /**
     * Test case when request is null
     */
    @Test(expected = NullPointerException.class)
    public void testCreateExcelUploadingCrowdPack_NullRequest() throws Throwable {
        crowdPackInnerService.createExcelUploadingCrowdPack(null);
    }

    /**
     * Test case for empty crowd pack name
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testCreateExcelUploadingCrowdPack_EmptyCrowdPackName() throws Throwable {
        // arrange
        request.setCrowdPackName("");
        // act & assert
        try {
            crowdPackInnerService.createExcelUploadingCrowdPack(request);
            fail("Should throw ExcelUploadingCrowdPackException");
        } finally {
            verify(crowdPackWriteDomainService, never()).insertCrowdPackBaseInfo(any());
            verify(executeWriteDomainService, never()).concurrentUpdateExcelCrowdPackAsync(anyString(), any(), anyLong(), anyString());
        }
    }

    /**
     * Test case for null crowd pack name
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testCreateExcelUploadingCrowdPack_NullCrowdPackName() throws Throwable {
        // arrange
        request.setCrowdPackName(null);
        // act & assert
        try {
            crowdPackInnerService.createExcelUploadingCrowdPack(request);
            fail("Should throw ExcelUploadingCrowdPackException");
        } finally {
            verify(crowdPackWriteDomainService, never()).insertCrowdPackBaseInfo(any());
            verify(executeWriteDomainService, never()).concurrentUpdateExcelCrowdPackAsync(anyString(), any(), anyLong(), anyString());
        }
    }

    /**
     * Test case for empty URL
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testCreateExcelUploadingCrowdPack_EmptyUrl() throws Throwable {
        // arrange
        request.setUrl("");
        // act & assert
        try {
            crowdPackInnerService.createExcelUploadingCrowdPack(request);
            fail("Should throw ExcelUploadingCrowdPackException");
        } finally {
            verify(crowdPackWriteDomainService, never()).insertCrowdPackBaseInfo(any());
            verify(executeWriteDomainService, never()).concurrentUpdateExcelCrowdPackAsync(anyString(), any(), anyLong(), anyString());
        }
    }

    /**
     * Test case for null URL
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testCreateExcelUploadingCrowdPack_NullUrl() throws Throwable {
        // arrange
        request.setUrl(null);
        // act & assert
        try {
            crowdPackInnerService.createExcelUploadingCrowdPack(request);
            fail("Should throw ExcelUploadingCrowdPackException");
        } finally {
            verify(crowdPackWriteDomainService, never()).insertCrowdPackBaseInfo(any());
            verify(executeWriteDomainService, never()).concurrentUpdateExcelCrowdPackAsync(anyString(), any(), anyLong(), anyString());
        }
    }

    /**
     * Test case for empty appId
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testCreateExcelUploadingCrowdPack_EmptyAppId() throws Throwable {
        // arrange
        request.setAppId("");
        // act & assert
        try {
            crowdPackInnerService.createExcelUploadingCrowdPack(request);
            fail("Should throw ExcelUploadingCrowdPackException");
        } finally {
            verify(crowdPackWriteDomainService, never()).insertCrowdPackBaseInfo(any());
            verify(executeWriteDomainService, never()).concurrentUpdateExcelCrowdPackAsync(anyString(), any(), anyLong(), anyString());
        }
    }

    /**
     * Test case for null appId
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testCreateExcelUploadingCrowdPack_NullAppId() throws Throwable {
        // arrange
        request.setAppId(null);
        // act & assert
        try {
            crowdPackInnerService.createExcelUploadingCrowdPack(request);
            fail("Should throw ExcelUploadingCrowdPackException");
        } finally {
            verify(crowdPackWriteDomainService, never()).insertCrowdPackBaseInfo(any());
            verify(executeWriteDomainService, never()).concurrentUpdateExcelCrowdPackAsync(anyString(), any(), anyLong(), anyString());
        }
    }

    /**
     * Test case for null misId
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testCreateExcelUploadingCrowdPack_NullMisId() throws Throwable {
        // arrange
        request.setMisId(null);
        // act & assert
        try {
            crowdPackInnerService.createExcelUploadingCrowdPack(request);
            fail("Should throw ExcelUploadingCrowdPackException");
        } finally {
            verify(crowdPackWriteDomainService, never()).insertCrowdPackBaseInfo(any());
            verify(executeWriteDomainService, never()).concurrentUpdateExcelCrowdPackAsync(anyString(), any(), anyLong(), anyString());
        }
    }

    /**
     * Test case for null ssoMisId
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testCreateExcelUploadingCrowdPack_NullSsoMisId() throws Throwable {
        // arrange
        request.setSsoMisId(null);
        // act & assert
        try {
            crowdPackInnerService.createExcelUploadingCrowdPack(request);
            fail("Should throw ExcelUploadingCrowdPackException");
        } finally {
            verify(crowdPackWriteDomainService, never()).insertCrowdPackBaseInfo(any());
            verify(executeWriteDomainService, never()).concurrentUpdateExcelCrowdPackAsync(anyString(), any(), anyLong(), anyString());
        }
    }

    /**
     * Test case when crowd pack creation returns null
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testCreateExcelUploadingCrowdPack_NullCrowdPackId() throws Throwable {
        // arrange
        when(crowdPackWriteDomainService.insertCrowdPackBaseInfo(any())).thenReturn(null);
        // act & assert
        try {
            crowdPackInnerService.createExcelUploadingCrowdPack(request);
            fail("Should throw ExcelUploadingCrowdPackException");
        } finally {
            verify(executeWriteDomainService, never()).concurrentUpdateExcelCrowdPackAsync(anyString(), any(), anyLong(), anyString());
        }
    }

    /**
     * Test case for missing required fields
     */
    @Test(expected = ExcelUploadingCrowdPackException.class)
    public void testCreateExcelUploadingCrowdPack_MissingRequiredFields() throws Throwable {
        // arrange
        CreateExcelUploadingCrowdPackRequest request = new CreateExcelUploadingCrowdPackRequest();
        // Missing misId, ssoMisId, and other required fields
        // act
        crowdPackInnerService.createExcelUploadingCrowdPack(request);
    }
}
