package com.sankuai.scrm.core.service.dashboard.domain;

import com.sankuai.scrm.core.service.activity.fission.domain.FissionInvitationDomainService;
import com.sankuai.scrm.core.service.dashboard.constants.DashBoardESIndexConstant;
import com.sankuai.scrm.core.service.dashboard.dal.dto.UserDataDashboardUserLogDoc;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.group.domain.GroupMemberDomainService;
import com.sankuai.scrm.core.service.group.dynamiccode.enums.UserGroupActionType;
import com.sankuai.scrm.core.service.group.dynamiccode.mq.entity.UserEnterLeaveGroupEvent;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Collections;
import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DashBoardDomainServiceOnGroupMemberChangeEventTest {

    @InjectMocks
    private DashBoardDomainService dashBoardDomainService;

    @Mock
    private GroupMemberDomainService groupMemberDomainService;

    @Mock
    private ContactUserDomain contactUserDomain;

    @Mock
    private FissionInvitationDomainService fissionInvitationDomainService;

    @Mock
    private DashBoardESWriteDomainService dashBoardESWriteDomainService;

    private UserEnterLeaveGroupEvent event;

    private ContactUser contactUser;

    private MemberInfoEntity memberInfoEntity;

    @Before
    public void setUp() {
        event = new UserEnterLeaveGroupEvent();
        contactUser = ContactUser.builder().status(1).updateTime(new Date()).unionId("someUnionId").corpId("someCorpId").build();
        memberInfoEntity = MemberInfoEntity.builder().updateTime(new Date()).corpId("someCorpId").unionId("someUnionId").status((byte) 0).build();
    }

    @Test
    public void testOnGroupMemberChangeEventUnionIdIsNull() throws Throwable {
        event.setUnionId(null);
        dashBoardDomainService.onGroupMemberChangeEvent(event);
        verify(groupMemberDomainService, never()).getMemberInfoEntitiesByCorpIdAndUnionId(any(), any());
        verify(contactUserDomain, never()).getContactUsersByCorpIdAndUnionId(any(), any());
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(any(), any(), any());
    }

    @Test
    public void testOnGroupMemberChangeEventUserEntersGroup() throws Throwable {
        event.setUnionId("someUnionId");
        event.setActionType(UserGroupActionType.ENTER.getCode());
        event.setActionTime(System.currentTimeMillis());
        event.setCorpId("someCorpId");
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(any(), any())).thenReturn(Collections.singletonList(contactUser));
//        when(fissionInvitationDomainService.getGroupFissionInvitationRecordsByUnionId(any())).thenReturn(Collections.emptyList());
        dashBoardDomainService.onGroupMemberChangeEvent(event);
        verify(dashBoardESWriteDomainService, times(1)).updateESDocByIdSelective(eq(DashBoardESIndexConstant.USER_DATA_DASHBOARD_USER_LOG_INDEX), any(UserDataDashboardUserLogDoc.class), any());
    }

    @Test
    public void testOnGroupMemberChangeEventUserEntersGroupButIsNewCustomer() throws Throwable {
        event.setUnionId("someUnionId");
        event.setActionType(UserGroupActionType.ENTER.getCode());
        event.setActionTime(System.currentTimeMillis());
        event.setCorpId("someCorpId");
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(any(), any())).thenReturn(Collections.emptyList());
        when(fissionInvitationDomainService.getGroupFissionInvitationRecordsByUnionId(any())).thenReturn(Collections.emptyList());
        dashBoardDomainService.onGroupMemberChangeEvent(event);
        verify(dashBoardESWriteDomainService, times(1)).updateESDocByIdSelective(eq(DashBoardESIndexConstant.USER_DATA_DASHBOARD_USER_LOG_INDEX), any(UserDataDashboardUserLogDoc.class), any());
    }

    @Test
    public void testOnGroupMemberChangeEventUserEntersGroupButIsLostCustomer() throws Throwable {
        event.setUnionId("someUnionId");
        event.setActionType(UserGroupActionType.ENTER.getCode());
        event.setActionTime(System.currentTimeMillis());
        event.setCorpId("someCorpId");
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(any(), any())).thenReturn(Collections.singletonList(contactUser));
//        when(fissionInvitationDomainService.getGroupFissionInvitationRecordsByUnionId(any())).thenReturn(Collections.emptyList());
        dashBoardDomainService.onGroupMemberChangeEvent(event);
        verify(dashBoardESWriteDomainService, times(1)).updateESDocByIdSelective(eq(DashBoardESIndexConstant.USER_DATA_DASHBOARD_USER_LOG_INDEX), any(UserDataDashboardUserLogDoc.class), any());
    }

    @Test
    public void testOnGroupMemberChangeEventUserLeavesGroupButIsNewCustomer() throws Throwable {
        event.setUnionId("someUnionId");
        event.setActionType(UserGroupActionType.LEAVE.getCode());
        event.setActionTime(System.currentTimeMillis());
        event.setCorpId("someCorpId");
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(any(), any())).thenReturn(Collections.emptyList());
        when(fissionInvitationDomainService.getGroupFissionInvitationRecordsByUnionId(any())).thenReturn(Collections.emptyList());
        dashBoardDomainService.onGroupMemberChangeEvent(event);
        verify(dashBoardESWriteDomainService, times(1)).updateESDocByIdSelective(eq(DashBoardESIndexConstant.USER_DATA_DASHBOARD_USER_LOG_INDEX), any(UserDataDashboardUserLogDoc.class), any());
    }

    @Test
    public void testOnGroupMemberChangeEventExceptionOccurs() throws Throwable {
        // Setup
        event.setUnionId("someUnionId");
        event.setActionType(UserGroupActionType.ENTER.getCode());
        event.setActionTime(System.currentTimeMillis());
        event.setCorpId("someCorpId");
        // Mock the exception
        RuntimeException expectedException = new RuntimeException("Test exception");
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(any(), any())).thenThrow(expectedException);
        // Execute
        dashBoardDomainService.onGroupMemberChangeEvent(event);
        // Verify
        verify(contactUserDomain).getContactUsersByCorpIdAndUnionId(event.getCorpId(), event.getUnionId());
        verify(groupMemberDomainService, never()).getMemberInfoEntitiesByCorpIdAndUnionId(any(), any());
        verify(fissionInvitationDomainService, never()).getGroupFissionInvitationRecordsByUnionId(any());
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(any(), any(), any());
    }
}
