package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.message.push.dto.MsgTaskDetailResultDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Collections;
import java.util.List;

import static org.mockito.Mockito.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.function.InvokeDetailColumnGetter;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.BizDistributorServiceKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.TextVO;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.dto.SendListDTO;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import com.sankuai.scrm.core.service.message.push.response.MsgPushResponse;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.scrm.core.service.util.QRCodeUtils;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import com.sankuai.scrm.core.service.util.ThreadPoolUtils;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.content.MiniProgramDTO;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import java.net.URLEncoder;
import java.util.*;
import java.util.concurrent.ConcurrentHashMap;
import java.util.concurrent.ExecutorService;
import java.util.concurrent.Executors;
import java.util.stream.Collectors;
import javax.annotation.Resource;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.collections4.MapUtils;
import org.apache.commons.lang.StringUtils;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.apache.commons.lang3.math.NumberUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Component;
import org.springframework.test.util.ReflectionTestUtils;

@ExtendWith(MockitoExtension.class)
class OfficialWxHandlerDealNotFriendResultV21Test {

    private static final String GROUP_MESSAGE = "groupMessage";

    private static final String MESSAGE = "message";

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private ShortLinkUtils shortLinkUtils;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test when taskDetailResultDTOS is empty, method should return immediately
     */
    @Test
    public void testDealNotFriendResultV2EmptyTaskDetailList() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = Collections.emptyList();
        // act
        officialWxHandler.dealNotFriendResultV2(processOrchestrationId, processOrchestrationVersion, invokeLogDO, taskDetailResultDTOS);
        // assert
        verifyNoInteractions(wxInvokeDetailDOMapper);
        verifyNoInteractions(executeLogDOMapper);
    }

    /**
     * Test when no matching records found in database
     */
    @Test
    public void testDealNotFriendResultV2NoMatchingRecords() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setType(MESSAGE);
        MsgTaskDetailResultDTO taskDetail = new MsgTaskDetailResultDTO();
        taskDetail.setReceiverId("user1");
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = Collections.singletonList(taskDetail);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        officialWxHandler.dealNotFriendResultV2(processOrchestrationId, processOrchestrationVersion, invokeLogDO, taskDetailResultDTOS);
        // assert
        verify(wxInvokeDetailDOMapper).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(wxInvokeDetailDOMapper, never()).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * Test when executeLogId is null in detail record
     */
    @Test
    public void testDealNotFriendResultV2NullExecuteLogId() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setType(MESSAGE);
        MsgTaskDetailResultDTO taskDetail = new MsgTaskDetailResultDTO();
        taskDetail.setReceiverId("user1");
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = Collections.singletonList(taskDetail);
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(null);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> failedList = Collections.singletonList(detailDO);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(failedList);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // act
        officialWxHandler.dealNotFriendResultV2(processOrchestrationId, processOrchestrationVersion, invokeLogDO, taskDetailResultDTOS);
        // assert
        verify(wxInvokeDetailDOMapper).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    @Test
    public void testGetActivityPageAttachmentVOV2WhenPageInfoIsNull() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        // act
        MsgPushContentDTO result = officialWxHandler.getActivityPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, null, existedAttachmentMap, executeManagementDTO);
        // assert
        assertNull(result);
        verify(informationGatheringService, never()).queryCommunityDistributor(any());
    }

    @Test
    public void testGetActivityPageAttachmentVOV2WhenAttachmentExistsInCache() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setProcessOrchestrationNodeId(123L);
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageInfo.setId(456L);
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        MsgPushContentDTO cachedValue = new MsgPushContentDTO();
        existedAttachmentMap.put("123-456", cachedValue);
        // act
        MsgPushContentDTO result = officialWxHandler.getActivityPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, executeManagementDTO);
        // assert
        assertSame(cachedValue, result);
        verify(informationGatheringService, never()).queryCommunityDistributor(any());
    }

    @Test
    public void testGetActivityPageAttachmentVOV2SuccessfullyCreatesNewAttachment() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setProcessOrchestrationNodeId(123L);
        actionAttachmentDTO.setProcessOrchestrationId(789L);
        actionAttachmentDTO.setProcessOrchestrationVersion("1.0");
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageInfo.setId(456L);
        pageInfo.setActivityTitle("Test Activity");
        pageInfo.setMiniProgramOriginAppId("originApp");
        pageInfo.setMiniProgramAppId("miniApp");
        pageInfo.setThumbPicUrl("thumbUrl");
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        String mockDistributorCode = "distributor123";
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class))).thenReturn(mockDistributorCode);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneCode("testSceneCode");
        sceneCodeDO.setPoiRestrict(1);
        when(productManagementService.getActivSceneCodeDO(any(ScrmProcessOrchestrationActionAttachmentDTO.class), any(ScrmProcessOrchestrationAttachmentSupplyDetailDTO.class), any(ScrmAmProcessOrchestrationProductActivityPageDO.class), eq(mockDistributorCode), eq(processOrchestrationDTO.getAppId()))).thenReturn(sceneCodeDO);
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("http://short.url/abc");
        // act
        MsgPushContentDTO result = officialWxHandler.getActivityPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, executeManagementDTO);
        // assert
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.MINI_PROGRAM, result.getContentTypeTEnum());
        MiniProgramDTO miniProgramDTO = result.getMiniProgramDTO();
        assertNotNull(miniProgramDTO);
        assertEquals("Test Activity", miniProgramDTO.getTitle());
        assertEquals("originApp", miniProgramDTO.getOriginAppId());
        assertEquals("miniApp", miniProgramDTO.getAppId());
        assertEquals("thumbUrl", miniProgramDTO.getThumbnail());
    }

    @Test
    public void testGetActivityPageAttachmentVOV2WhenInformationGatheringServiceFails() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setProcessOrchestrationNodeId(123L);
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageInfo.setId(456L);
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class))).thenThrow(new RuntimeException("Service failed"));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            officialWxHandler.getActivityPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, executeManagementDTO);
        });
    }

    @Test
    public void testGetActivityPageAttachmentVOV2WithPoiIdInExecuteManagement() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setProcessOrchestrationNodeId(123L);
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        executeManagementDTO.setPoiId("testPoiId");
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        pageInfo.setId(456L);
        pageInfo.setActivityTitle("Test Activity");
        pageInfo.setMiniProgramOriginAppId("originApp");
        pageInfo.setMiniProgramAppId("miniApp");
        pageInfo.setThumbPicUrl("thumbUrl");
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        String mockDistributorCode = "distributor123";
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class))).thenReturn(mockDistributorCode);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        sceneCodeDO.setSceneCode("testSceneCode");
        sceneCodeDO.setPoiRestrict(1);
        when(productManagementService.getActivSceneCodeDO(any(ScrmProcessOrchestrationActionAttachmentDTO.class), any(ScrmProcessOrchestrationAttachmentSupplyDetailDTO.class), any(ScrmAmProcessOrchestrationProductActivityPageDO.class), eq(mockDistributorCode), eq(processOrchestrationDTO.getAppId()))).thenReturn(sceneCodeDO);
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("http://short.url/abc");
        // act
        MsgPushContentDTO result = officialWxHandler.getActivityPageAttachmentVOV2(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, pageInfo, existedAttachmentMap, executeManagementDTO);
        // assert
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.MINI_PROGRAM, result.getContentTypeTEnum());
        MiniProgramDTO miniProgramDTO = result.getMiniProgramDTO();
        assertNotNull(miniProgramDTO);
        assertEquals("Test Activity", miniProgramDTO.getTitle());
        assertEquals("originApp", miniProgramDTO.getOriginAppId());
        assertEquals("miniApp", miniProgramDTO.getAppId());
        assertEquals("thumbUrl", miniProgramDTO.getThumbnail());
    }
}
