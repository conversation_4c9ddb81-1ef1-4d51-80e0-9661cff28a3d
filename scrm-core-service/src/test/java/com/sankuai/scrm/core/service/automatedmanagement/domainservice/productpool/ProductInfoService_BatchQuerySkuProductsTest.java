package com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool;

import com.dianping.tpfun.product.api.sku.ProductService;
import com.dianping.tpfun.product.api.sku.model.Product;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

public class ProductInfoService_BatchQuerySkuProductsTest {

    @InjectMocks
    private ProductInfoService productInfoService;

    @Mock(lenient = true)
    private ProductService skuProductService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试输入的 id 列表为空的情况
     */
    @Test
    public void testBatchQuerySkuProductsEmptyIds() throws Throwable {
        // arrange
        List<Long> ids = Collections.emptyList();
        // act
        List<Product> result = productInfoService.batchQuerySkuProducts(ids);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试输入的 id 列表不为空，但 skuProductService.mGetBaseProductByIds 方法返回的产品列表为空的情况
     */
    @Test
    public void testBatchQuerySkuProductsEmptyProducts() throws Throwable {
        // arrange
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        when(skuProductService.mGetBaseProductByIds(anyList())).thenReturn(Collections.emptyList());
        // act
        List<Product> result = productInfoService.batchQuerySkuProducts(ids);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试输入的 id 列表不为空，skuProductService.mGetBaseProductByIds 方法返回的产品列表也不为空的情况
     */
    @Test
    public void testBatchQuerySkuProductsNotEmptyProducts() throws Throwable {
        // arrange
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        List<Product> products = Arrays.asList(new Product(), new Product(), new Product());
        when(skuProductService.mGetBaseProductByIds(anyList())).thenReturn(products);
        // act
        List<Product> result = productInfoService.batchQuerySkuProducts(ids);
        // assert
        assertEquals(products, result);
    }

    /**
     * 测试在查询过程中发生异常的情况
     */
    @Test
    public void testBatchQuerySkuProductsException() throws Throwable {
        // arrange
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        when(skuProductService.mGetBaseProductByIds(anyList())).thenThrow(new RuntimeException());
        // act
        List<Product> result = productInfoService.batchQuerySkuProducts(ids);
        // assert
        assertEquals(Collections.emptyList(), result);
    }
}
