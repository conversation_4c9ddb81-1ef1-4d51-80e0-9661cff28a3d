package com.sankuai.scrm.core.service.realtime.task.mq.producer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import java.lang.reflect.Field;
import java.util.Properties;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GroupRetailAiEngineABTestRecordMessageProducerAfterPropertiesSet1Test {

    private GroupRetailAiEngineABTestRecordMessageProducer producer;

    private MockedStatic<MafkaClient> mockedMafkaClient;

    @BeforeEach
    void setUp() {
        producer = new GroupRetailAiEngineABTestRecordMessageProducer();
        // 重置静态producer字段
        setStaticProducerField(null);
    }

    @AfterEach
    void tearDown() {
        if (mockedMafkaClient != null) {
            mockedMafkaClient.close();
        }
    }

    // 辅助方法，用于获取静态producer字段
    private IProducerProcessor getStaticProducerField() throws Exception {
        Field field = GroupRetailAiEngineABTestRecordMessageProducer.class.getDeclaredField("producer");
        field.setAccessible(true);
        return (IProducerProcessor) field.get(null);
    }

    // 辅助方法，用于设置静态producer字段
    private void setStaticProducerField(IProducerProcessor producer) {
        try {
            Field field = GroupRetailAiEngineABTestRecordMessageProducer.class.getDeclaredField("producer");
            field.setAccessible(true);
            field.set(null, producer);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试正常初始化场景
     * 验证属性正确设置且生产者成功创建
     */
    @Test
    void testAfterPropertiesSetNormalCase() throws Throwable {
        // arrange
        IProducerProcessor mockProducer = mock(IProducerProcessor.class);
        mockedMafkaClient = mockStatic(MafkaClient.class);
        // 验证传入的参数是否正确
        mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenAnswer(invocation -> {
            Properties props = invocation.getArgument(0);
            String topic = invocation.getArgument(1);
            assertEquals("daozong", props.getProperty(ConsumerConstants.MafkaBGNamespace));
            assertEquals("com.sankuai.medicalcosmetology.scrm.core", props.getProperty(ConsumerConstants.MafkaClientAppkey));
            assertEquals("scrm.group.retail.ai.engine.test.record.message", topic);
            return mockProducer;
        });
        // act
        producer.afterPropertiesSet();
        // assert
        mockedMafkaClient.verify(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString()));
        assertNotNull(getStaticProducerField());
    }

    /**
     * 测试MafkaClient.buildProduceFactory抛出异常的场景
     * 验证异常被正确抛出
     */
    @Test
    void testAfterPropertiesSetWhenBuildFactoryThrowsException() throws Throwable {
        // arrange
        mockedMafkaClient = mockStatic(MafkaClient.class);
        when(MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenThrow(new RuntimeException("Build factory failed"));
        // act & assert
        assertThrows(RuntimeException.class, () -> producer.afterPropertiesSet());
    }

    /**
     * 测试属性缺失场景
     * 验证当缺少必要属性时抛出异常
     */
    @Test
    void testAfterPropertiesSetWhenMissingRequiredProperties() throws Throwable {
        // arrange
        mockedMafkaClient = mockStatic(MafkaClient.class);
        when(MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenAnswer(invocation -> {
            Properties props = invocation.getArgument(0);
            if (!props.containsKey(ConsumerConstants.MafkaBGNamespace) || !props.containsKey(ConsumerConstants.MafkaClientAppkey)) {
                throw new IllegalArgumentException("Missing required properties");
            }
            return mock(IProducerProcessor.class);
        });
        // act & assert
        assertDoesNotThrow(() -> producer.afterPropertiesSet());
    }

    /**
     * 测试多次调用afterPropertiesSet方法
     * 验证生产者实例是否相同
     */
    @Test
    void testAfterPropertiesSetCalledMultipleTimes() throws Throwable {
        // arrange
        IProducerProcessor mockProducer = mock(IProducerProcessor.class);
        mockedMafkaClient = mockStatic(MafkaClient.class);
        when(MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenReturn(mockProducer);
        // act
        producer.afterPropertiesSet();
        IProducerProcessor firstProducer = getStaticProducerField();
        producer.afterPropertiesSet();
        IProducerProcessor secondProducer = getStaticProducerField();
        // assert
        assertSame(firstProducer, secondProducer, "Producer should be the same instance after multiple calls");
    }
}
