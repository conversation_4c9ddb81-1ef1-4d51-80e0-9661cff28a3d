package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.function.InvokeDetailColumnGetter;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.SendStrategy;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.AttachmentVO;
import com.sankuai.scrm.core.service.message.push.dto.MsgTaskDetailResultDTO;
import com.sankuai.scrm.core.service.message.push.request.MsgPushRequest;
import com.sankuai.scrm.core.service.message.push.request.MsgPushResultRequest;
import com.sankuai.scrm.core.service.message.push.response.MsgPushResponse;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackDetailInfoDOMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerSendRequestAndDealResultTest {

    @Spy
    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private MsgUnifiedPushService msgUnifiedPushService;

    @Mock
    private SendStrategy sendStrategy;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOList;

    private MsgPushRequest pushRequest;

    private ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO;

    private Method sendRequestAndDealResultMethod;

    @Mock
    private OfficialWxHandler handler;

    private String executorId;

    private InvokeDetailKeyObject keyObject;

    private StepExecuteResultDTO stepExecuteResultDTO;

    @Before
    public void setUp() throws Exception {
        // Setup processOrchestrationDTO
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("test_app_id");
        // Setup wxInvokeDetailDOList
        wxInvokeDetailDOList = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setTargetId("test_target");
        wxInvokeDetailDOList.add(detailDO);
        // Setup pushRequest
        pushRequest = new MsgPushRequest();
        // Setup wxInvokeLogDO
        wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        // Setup reflection method
        sendRequestAndDealResultMethod = OfficialWxHandler.class.getDeclaredMethod("sendRequestAndDealResult", ScrmProcessOrchestrationDTO.class, String.class, List.class, MsgPushRequest.class, ScrmAmProcessOrchestrationWxInvokeLogDO.class, SendStrategy.class);
        sendRequestAndDealResultMethod.setAccessible(true);
        // Mock stopAndLogRequest
        doReturn(false).when(officialWxHandler).stopAndLogRequest(any(), any(), any());
        // Mock getColumnGetter
        InvokeDetailColumnGetter columnGetter = wxInvokeDetailDO -> wxInvokeDetailDO.getTargetId();
        when(sendStrategy.getColumnGetter()).thenReturn(columnGetter);
    }

    @Before
    public void setup() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        executorId = "executor123";
        keyObject = new InvokeDetailKeyObject("key", (byte) 1, (byte) 1, 1L);
        stepExecuteResultDTO = new StepExecuteResultDTO();
    }

    private void invokePrivateSendRequestAndDealResult(ScrmProcessOrchestrationDTO processOrchestrationDTO, String executorId, List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOList, MsgPushRequest pushRequest, ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO, SendStrategy sendStrategy) throws Exception {
        Method method = OfficialWxHandler.class.getDeclaredMethod("sendRequestAndDealResult", ScrmProcessOrchestrationDTO.class, String.class, List.class, MsgPushRequest.class, ScrmAmProcessOrchestrationWxInvokeLogDO.class, SendStrategy.class);
        method.setAccessible(true);
        method.invoke(officialWxHandler, processOrchestrationDTO, executorId, wxInvokeDetailDOList, pushRequest, wxInvokeLogDO, sendStrategy);
    }



}