package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationAttachmentTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.UploadWxAttachmentAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxMomentSendRequest;
import com.sankuai.scrm.core.service.infrastructure.vo.WechatMediaResult;
import com.sankuai.scrm.core.service.infrastructure.vo.WxMediaType;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class StaffMomentTouchActionGetStaffMomentTouchCorpTagList1Test {

    @InjectMocks
    private StaffMomentTouchAction staffMomentTouchAction;

    @Mock
    private UploadWxAttachmentAcl uploadWxAttachmentAcl;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;

    private List<ScrmProcessOrchestrationActionContentDTO> contentDTOS;

    private ExecuteManagementDTO executeManagementDTO;

    private StepExecuteResultDTO result;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setAppId("testAppId");
        // Initialize NodeMediumDTO
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        // Initialize actionAttachmentMap
        nodeMediumDTO.setActionAttachmentMap(new HashMap<>());
        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        // Initialize contentDTO with proper IDs
        contentDTOS = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("test content");
        contentDTO.setProcessOrchestrationNodeId(1L);
        contentDTO.setActionId(1);
        contentDTO.setContentId(1);
        contentDTOS.add(contentDTO);
        executeManagementDTO = new ExecuteManagementDTO();
        Set<String> staffIds = new HashSet<>(Arrays.asList("staff1"));
        executeManagementDTO.setStaffLimitSet(staffIds);
        result = new StepExecuteResultDTO();
        result.setSuccess(true);
    }

    /**
     * Test case for empty processOrchestrationNodeDTOList
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_EmptyNodeList() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for null crowdPackUpdateStrategyInfoDTO
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_NullStrategyInfo() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(Lists.newArrayList(new ScrmProcessOrchestrationNodeDTO()));
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        processOrchestrationDTO.setCrowdPackType(1);
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for empty strategyDetailDTOList
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_EmptyStrategyDetailList() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(Lists.newArrayList(new ScrmProcessOrchestrationNodeDTO()));
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        processOrchestrationDTO.setCrowdPackType(1);
        processOrchestrationDTO.setCrowdPackUpdateStrategyInfoDTO(new ScrmCrowdPackUpdateStrategyInfoDTO());
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for empty rootNodeCorpTagSet
     */
    @Test
    public void testGetStaffMomentTouchCorpTagList_EmptyRootNodeCorpTagSet() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(Lists.newArrayList(new ScrmProcessOrchestrationNodeDTO()));
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        processOrchestrationDTO.setCrowdPackType(1);
        ScrmCrowdPackUpdateStrategyInfoDTO strategyInfoDTO = new ScrmCrowdPackUpdateStrategyInfoDTO();
        strategyInfoDTO.setCrowdPackUpdateStrategy(Lists.newArrayList(new ScrmCrowdPackUpdateStrategyDetailDTO()));
        processOrchestrationDTO.setCrowdPackUpdateStrategyInfoDTO(strategyInfoDTO);
        // act
        List<String> result = staffMomentTouchAction.getStaffMomentTouchCorpTagList(processOrchestrationDTO, currentProcessingNode);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for empty attachments
     */
    @Test
    public void testGetNormalWxMomentSendRequest_EmptyAttachments() throws Throwable {
        // arrange
        // nodeId-actionId-contentId
        String key = "1-1-1";
        processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentMap().put(key, new ArrayList<>());
        // act
        WxMomentSendRequest request = staffMomentTouchAction.getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
        // assert
        assertNotNull(request);
        assertNull(request.getAttachments());
        assertTrue(result.isSuccess());
    }

    /**
     * Test case for successful link attachment upload
     */
    @Test
    public void testGetNormalWxMomentSendRequest_SuccessfulLinkAttachment() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationActionAttachmentDTO> attachmentDTOList = new ArrayList<>();
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachmentDTO.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.LINK.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetailDTO.setTitle("Test Title");
        contentDetailDTO.setPicUrl("http://test.com/pic.jpg");
        contentDetailDTO.setDesc("Test Desc");
        contentDetailDTO.setContentUrl("http://test.com/content");
        attachmentDTO.setAttachmentContent(JsonUtils.toStr(contentDetailDTO));
        attachmentDTOList.add(attachmentDTO);
        // nodeId-actionId-contentId
        String key = "1-1-1";
        processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentMap().put(key, attachmentDTOList);
        WechatMediaResult mediaResult = new WechatMediaResult();
        mediaResult.setErrcode(0);
        mediaResult.setMedia_id("test_media_id");
        when(uploadWxAttachmentAcl.uploadWxAttachment(eq("http://test.com/pic.jpg"), eq(WxMediaType.image), eq("testAppId"), eq(true))).thenReturn(mediaResult);
        // act
        WxMomentSendRequest request = staffMomentTouchAction.getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
        // assert
        assertNotNull(request);
        assertNotNull(request.getAttachments());
        assertEquals(1, request.getAttachments().size());
        assertEquals("link", request.getAttachments().get(0).getMsgtype());
        assertEquals("test_media_id", request.getAttachments().get(0).getLink().getMedia_id());
        assertTrue(result.isSuccess());
    }

    /**
     * Test case for failed link attachment upload
     */
    @Test
    public void testGetNormalWxMomentSendRequest_FailedLinkAttachment() throws Throwable {
        // arrange
        List<ScrmProcessOrchestrationActionAttachmentDTO> attachmentDTOList = new ArrayList<>();
        ScrmProcessOrchestrationActionAttachmentDTO attachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachmentDTO.setAttachmentTypeId(ScrmProcessOrchestrationAttachmentTypeEnum.LINK.getValue());
        ScrmProcessOrchestrationAttachmentContentDetailDTO contentDetailDTO = new ScrmProcessOrchestrationAttachmentContentDetailDTO();
        contentDetailDTO.setPicUrl("http://test.com/pic.jpg");
        attachmentDTO.setAttachmentContent(JsonUtils.toStr(contentDetailDTO));
        attachmentDTOList.add(attachmentDTO);
        // nodeId-actionId-contentId
        String key = "1-1-1";
        processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentMap().put(key, attachmentDTOList);
        WechatMediaResult mediaResult = new WechatMediaResult();
        mediaResult.setErrcode(1);
        when(uploadWxAttachmentAcl.uploadWxAttachment(any(), any(), any(), eq(true))).thenReturn(mediaResult);
        // act
        WxMomentSendRequest request = staffMomentTouchAction.getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
        // assert
        assertNull(request);
        assertFalse(result.isSuccess());
        assertTrue(result.isExistedFailedAttachmentUpload());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ATTACHMENT_UPLOAD_FAIL.getCode(), result.getCode());
    }

    /**
     * Test case for empty attachments and empty text content
     */
    @Test
    public void testGetNormalWxMomentSendRequest_EmptyAttachmentsAndEmptyContent() throws Throwable {
        // arrange
        contentDTOS.get(0).setContent("");
        // nodeId-actionId-contentId
        String key = "1-1-1";
        processOrchestrationDTO.getNodeMediumDTO().getActionAttachmentMap().put(key, new ArrayList<>());
        // act
        WxMomentSendRequest request = staffMomentTouchAction.getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
        // assert
        assertNull(request);
    }
}
