package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationInformationGatheringEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.function.InvokeDetailColumnGetter;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.BizDistributorServiceKeyObject;
import com.sankuai.scrm.core.service.group.dynamiccode.constant.GroupDynamicCodeConstants;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.dto.SendListDTO;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import com.sankuai.scrm.core.service.message.push.dto.MsgTaskDetailResultDTO;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushDetailStatus;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.content.MiniProgramDTO;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.content.TextDTO;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import java.util.ArrayList;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.apache.commons.lang3.builder.HashCodeBuilder;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerGetCustomizedProductPromotionMsgPushContentDTOTest {

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private ShortLinkUtils shortLinkUtils;

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO;

    private ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO;

    private Map<String, MsgPushContentDTO> existedAttachmentMap;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setProductType(1);
        supplyDetailDTO.setJumpUrl("http://test.com");
        supplyDetailDTO.setShelfName("Test Shelf");
        supplyDetailDTO.setHeadpicUrl("http://test.com/pic.jpg");
        // Changed to Integer
        supplyDetailDTO.setJumpPageType(1);
        existedAttachmentMap = new HashMap<>();
    }

    /**
     * Test cache hit scenario - should return existing value
     */
    @Test
    public void testGetCustomizedProductPromotionMsgPushContentDTO_CacheHit() throws Throwable {
        // arrange
        String mapKey = supplyDetailDTO.getProductType() + "-" + supplyDetailDTO.getJumpUrl();
        MsgPushContentDTO expectedContent = new MsgPushContentDTO();
        existedAttachmentMap.put(mapKey, expectedContent);
        // act
        MsgPushContentDTO result = officialWxHandler.getCustomizedProductPromotionMsgPushContentDTO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, existedAttachmentMap);
        // assert
        assertSame(expectedContent, result);
    }

    /**
     * Test cache miss scenario - should create new content
     */
    @Test
    public void testGetCustomizedProductPromotionMsgPushContentDTO_CacheMiss() throws Throwable {
        // arrange
        String distributorCode = "testDistributor";
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class))).thenReturn(distributorCode);
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("shortUrl");
        // act
        MsgPushContentDTO result = officialWxHandler.getCustomizedProductPromotionMsgPushContentDTO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, existedAttachmentMap);
        // assert
        assertNotNull(result);
        assertEquals(ContentTypeTEnum.MINI_PROGRAM, result.getContentTypeTEnum());
        MiniProgramDTO miniProgram = result.getMiniProgramDTO();
        assertNotNull(miniProgram);
        assertEquals(GroupDynamicCodeConstants.MX_MINIP_APPID, miniProgram.getAppId());
        assertEquals(GroupDynamicCodeConstants.ORIGIN_MX_MINIP_APPID, miniProgram.getOriginAppId());
        assertEquals(supplyDetailDTO.getHeadpicUrl(), miniProgram.getThumbnail());
        assertEquals(supplyDetailDTO.getShelfName(), miniProgram.getTitle());
    }

    /**
     * Test with null supply detail
     */
    @Test
    public void testGetCustomizedProductPromotionMsgPushContentDTO_NullSupplyDetail() throws Throwable {
        // act & assert
        assertThrows(NullPointerException.class, () -> officialWxHandler.getCustomizedProductPromotionMsgPushContentDTO(processOrchestrationDTO, actionAttachmentDTO, null, existedAttachmentMap));
    }

    /**
     * Test with empty shelf name
     */
    @Test
    public void testGetCustomizedProductPromotionMsgPushContentDTO_EmptyShelfName() throws Throwable {
        // arrange
        supplyDetailDTO.setShelfName("");
        String distributorCode = "testDistributor";
        when(informationGatheringService.queryCommunityDistributor(any(BizDistributorServiceKeyObject.class))).thenReturn(distributorCode);
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("shortUrl");
        // act
        MsgPushContentDTO result = officialWxHandler.getCustomizedProductPromotionMsgPushContentDTO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, existedAttachmentMap);
        // assert
        assertNotNull(result);
        assertEquals("", result.getMiniProgramDTO().getTitle());
    }

    /**
     * Test with null existedAttachmentMap
     */
    @Test
    public void testGetCustomizedProductPromotionMsgPushContentDTO_NullExistedMap() throws Throwable {
        // arrange
        String distributorCode = "testDistributor";
        // act & assert
        assertThrows(NullPointerException.class, () -> officialWxHandler.getCustomizedProductPromotionMsgPushContentDTO(processOrchestrationDTO, actionAttachmentDTO, supplyDetailDTO, null));
    }

    /**
     * Test buildKeyV2 with all valid non-null parameters
     */
    @Test
    public void testBuildKeyV2AllValidParameters() throws Throwable {
        // arrange
        String executorId = "executor123";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(1L);
        wxInvokeDetailDO.setType("type1");
        String chatType = "group";
        List<MsgPushContentDTO> attachments = new ArrayList<>();
        MsgPushContentDTO contentDTO = new MsgPushContentDTO();
        attachments.add(contentDTO);
        String content = "Test content";
        TextDTO text = new TextDTO();
        text.setContent("Text content");
        // act
        String result = OfficialWxHandler.buildKeyV2(executorId, wxInvokeDetailDO, chatType, attachments, content, text);
        // assert
        assertNotNull(result);
        String expectedStart = String.format("%s-%d-%s-%s", executorId, wxInvokeDetailDO.getProcessOrchestrationNodeId(), wxInvokeDetailDO.getType(), chatType);
        assertTrue(result.startsWith(expectedStart));
    }

    /**
     * Test buildKeyV2 with null attachments and content but valid text
     */
    @Test
    public void testBuildKeyV2NullAttachmentsAndContent() throws Throwable {
        // arrange
        String executorId = "executor456";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(2L);
        wxInvokeDetailDO.setType("type2");
        String chatType = "private";
        TextDTO text = new TextDTO();
        text.setContent("Only text");
        // act
        String result = OfficialWxHandler.buildKeyV2(executorId, wxInvokeDetailDO, chatType, null, null, text);
        // assert
        assertNotNull(result);
        String expectedStart = String.format("%s-%d-%s-%s", executorId, wxInvokeDetailDO.getProcessOrchestrationNodeId(), wxInvokeDetailDO.getType(), chatType);
        assertTrue(result.startsWith(expectedStart));
        // Verify hash calculation for text content
        int textHash = new HashCodeBuilder().append(text.getContent()).build();
        assertTrue(result.contains(String.valueOf(textHash)));
    }

    /**
     * Test buildKeyV2 with empty attachments and null text but valid content
     */
    @Test
    public void testBuildKeyV2EmptyAttachmentsNullText() throws Throwable {
        // arrange
        String executorId = "executor789";
        ScrmAmProcessOrchestrationWxInvokeDetailDO wxInvokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        wxInvokeDetailDO.setProcessOrchestrationNodeId(3L);
        wxInvokeDetailDO.setType("type3");
        String chatType = "group";
        List<MsgPushContentDTO> attachments = new ArrayList<>();
        String content = "Only content";
        // act
        String result = OfficialWxHandler.buildKeyV2(executorId, wxInvokeDetailDO, chatType, attachments, content, null);
        // assert
        assertNotNull(result);
        String expectedStart = String.format("%s-%d-%s-%s", executorId, wxInvokeDetailDO.getProcessOrchestrationNodeId(), wxInvokeDetailDO.getType(), chatType);
        assertTrue(result.startsWith(expectedStart));
        // Verify content hash
        int contentHash = new HashCodeBuilder().append(content).build();
        assertTrue(result.contains(String.valueOf(contentHash)));
    }

    /**
     * Test buildKeyV2 with null wxInvokeDetailDO
     */
    @Test
    public void testBuildKeyV2NullWxInvokeDetailDO() throws Throwable {
        // arrange
        String executorId = "executor000";
        String chatType = "private";
        List<MsgPushContentDTO> attachments = new ArrayList<>();
        attachments.add(new MsgPushContentDTO());
        String content = "Test content";
        TextDTO text = new TextDTO();
        text.setContent("Text content");
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            OfficialWxHandler.buildKeyV2(executorId, null, chatType, attachments, content, text);
        });
    }

    /**
     * Test buildKeyV2 with all null parameters except executorId
     */
    @Test
    public void testBuildKeyV2AllNullParametersExceptExecutorId() throws Throwable {
        // arrange
        String executorId = "executor999";
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            OfficialWxHandler.buildKeyV2(executorId, null, null, null, null, null);
        });
    }

    /**
     * Test when taskDetailResultDTOS is empty
     */
    @Test
    public void testDealSuccessResultV2EmptyTaskDetailResults() throws Throwable {
        // arrange
        java.util.List<MsgTaskDetailResultDTO> emptyList = new java.util.ArrayList<>();
        // act
        officialWxHandler.dealSuccessResultV2(null, 1L, "v1", null, emptyList);
        // assert
        verifyNoInteractions(wxInvokeDetailDOMapper);
        verifyNoInteractions(informationGatheringService);
        verifyNoInteractions(executeLogDOMapper);
    }

    /**
     * Test when no matching invoke details found
     */
    @Test
    public void testDealSuccessResultV2NoMatchingInvokeDetails() throws Throwable {
        // arrange
        MsgTaskDetailResultDTO result = new MsgTaskDetailResultDTO();
        result.setReceiverId("user1");
        result.setStatus(MsgPushDetailStatus.SUCCESS.getCode());
        java.util.List<MsgTaskDetailResultDTO> taskResults = new java.util.ArrayList<>();
        taskResults.add(result);
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLog = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLog.setType("message");
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(new java.util.ArrayList<>());
        // act
        officialWxHandler.dealSuccessResultV2(null, 1L, "v1", invokeLog, taskResults);
        // assert
        verify(wxInvokeDetailDOMapper).selectByExample(any());
        verifyNoMoreInteractions(wxInvokeDetailDOMapper);
        verifyNoInteractions(informationGatheringService);
    }

    /**
     * Test null invokeLogDO case
     */
    @Test
    public void testDealSuccessResultV2NullInvokeLog() throws Throwable {
        // arrange
        MsgTaskDetailResultDTO result = new MsgTaskDetailResultDTO();
        result.setReceiverId("user1");
        result.setStatus(MsgPushDetailStatus.SUCCESS.getCode());
        java.util.List<MsgTaskDetailResultDTO> taskResults = new java.util.ArrayList<>();
        taskResults.add(result);
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            officialWxHandler.dealSuccessResultV2(null, 1L, "v1", null, taskResults);
        });
    }

    /**
     * Test unknown message type case
     */
    @Test
    public void testDealSuccessResultV2UnknownMessageType() throws Throwable {
        // arrange
        MsgTaskDetailResultDTO result = new MsgTaskDetailResultDTO();
        result.setReceiverId("user1");
        result.setStatus(MsgPushDetailStatus.SUCCESS.getCode());
        java.util.List<MsgTaskDetailResultDTO> taskResults = new java.util.ArrayList<>();
        taskResults.add(result);
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLog = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLog.setType("unknown");
        // act
        officialWxHandler.dealSuccessResultV2(null, 1L, "v1", invokeLog, taskResults);
        // assert
        verify(wxInvokeDetailDOMapper).selectByExample(any());
    }

    /**
     * Test when send list is empty - should do nothing
     */
    @Test
    public void testDealPreventDisturbanceResultEmptyList() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        List<SendListDTO> sendListDTOS = Collections.emptyList();
        // act
        officialWxHandler.dealPreventDisturbanceResult(processOrchestrationId, processOrchestrationVersion, invokeLogDO, sendListDTOS);
        // assert
        verifyNoInteractions(wxInvokeDetailDOMapper);
        verifyNoInteractions(executeLogDOMapper);
    }

    /**
     * Test when no failed sends (status != 3) - should do nothing
     */
    @Test
    public void testDealPreventDisturbanceResultNoFailedSends() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        SendListDTO sendDTO = new SendListDTO();
        // not failed status
        sendDTO.setStatus(1);
        List<SendListDTO> sendListDTOS = Collections.singletonList(sendDTO);
        // act
        officialWxHandler.dealPreventDisturbanceResult(processOrchestrationId, processOrchestrationVersion, invokeLogDO, sendListDTOS);
        // assert
        verifyNoInteractions(wxInvokeDetailDOMapper);
        verifyNoInteractions(executeLogDOMapper);
    }

    /**
     * Test when executeLogId is null - should skip that record
     */
    @Test
    public void testDealPreventDisturbanceResultNullExecuteLogId() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setType("message");
        SendListDTO sendDTO = new SendListDTO();
        // failed status
        sendDTO.setStatus(3);
        sendDTO.setExternalUserid("user1");
        List<SendListDTO> sendListDTOS = Collections.singletonList(sendDTO);
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        // null executeLogId
        detailDO.setExecuteLogId(null);
        // WAIT_FOR_SEND status
        detailDO.setStatus((byte) 5);
        // Match the external user ID
        detailDO.setTargetId("user1");
        detailDO.setProcessOrchestrationId(processOrchestrationId);
        detailDO.setProcessOrchestrationVersion(processOrchestrationVersion);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOs = Collections.singletonList(detailDO);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(detailDOs);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // act
        officialWxHandler.dealPreventDisturbanceResult(processOrchestrationId, processOrchestrationVersion, invokeLogDO, sendListDTOS);
        // assert
        verify(wxInvokeDetailDOMapper).selectByExample(any());
        verify(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }
}
