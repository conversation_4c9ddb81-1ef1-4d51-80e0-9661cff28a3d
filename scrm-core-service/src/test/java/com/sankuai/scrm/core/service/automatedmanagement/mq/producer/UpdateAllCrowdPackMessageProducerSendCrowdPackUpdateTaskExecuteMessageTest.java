package com.sankuai.scrm.core.service.automatedmanagement.mq.producer;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.dz.srcm.automatedmanagement.dto.mq.RefinementOperationExecuteMessage;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class UpdateAllCrowdPackMessageProducerSendCrowdPackUpdateTaskExecuteMessageTest {

    @Mock
    private IProducerProcessor producer;

    private IProducerProcessor originalProducer;

    @InjectMocks
    private UpdateAllCrowdPackMessageProducer allPakcProducer;

    @BeforeEach
    public void setUp() throws Exception {
        // 保存原始producer
        Field producerField = UpdateAllCrowdPackMessageProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        originalProducer = (IProducerProcessor) producerField.get(null);
        // 设置mock的producer
        producerField.set(null, producer);
    }

    @AfterEach
    public void tearDown() throws Exception {
        // 恢复原始producer
        Field producerField = UpdateAllCrowdPackMessageProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, originalProducer);
    }

    /**
     * 测试正常发送消息成功的情况
     */
    @Test
    public void testSendCrowdPackUpdateTaskExecuteMessage_Success() throws Throwable {
        // arrange
        List<String> userUnionIds = Arrays.asList("user1", "user2");
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(producer.sendMessage(anyString())).thenReturn(successResult);
        try (MockedStatic<JsonUtils> mockedJsonUtils = Mockito.mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("testMessage");
            // act
            allPakcProducer.sendCrowdPackUpdateTaskExecuteMessage(userUnionIds);
            // assert
            verify(producer, times(1)).sendMessage(anyString());
        }
    }

    /**
     * 测试发送消息失败后重试3次的情况
     */
    @Test
    public void testSendCrowdPackUpdateTaskExecuteMessage_Retry3Times() throws Throwable {
        // arrange
        List<String> userUnionIds = Arrays.asList("user1", "user2");
        ProducerResult failResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        when(producer.sendMessage(anyString())).thenReturn(failResult);
        try (MockedStatic<JsonUtils> mockedJsonUtils = Mockito.mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("testMessage");
            // act
            allPakcProducer.sendCrowdPackUpdateTaskExecuteMessage(userUnionIds);
            // assert
            verify(producer, times(3)).sendMessage(anyString());
        }
    }

    /**
     * 测试传入空userUnionIds列表的情况
     */
    @Test
    public void testSendCrowdPackUpdateTaskExecuteMessage_EmptyUserUnionIds() throws Throwable {
        // arrange
        List<String> userUnionIds = Collections.emptyList();
        ProducerResult failResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        when(producer.sendMessage(anyString())).thenReturn(failResult);
        try (MockedStatic<JsonUtils> mockedJsonUtils = Mockito.mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("testMessage");
            // act
            allPakcProducer.sendCrowdPackUpdateTaskExecuteMessage(userUnionIds);
            // assert
            verify(producer, times(3)).sendMessage(anyString());
        }
    }

    /**
     * 测试传入null userUnionIds列表的情况
     */
    @Test
    public void testSendCrowdPackUpdateTaskExecuteMessage_NullUserUnionIds() throws Throwable {
        // arrange
        ProducerResult failResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        when(producer.sendMessage(anyString())).thenReturn(failResult);
        try (MockedStatic<JsonUtils> mockedJsonUtils = Mockito.mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("testMessage");
            // act
            allPakcProducer.sendCrowdPackUpdateTaskExecuteMessage(null);
            // assert
            verify(producer, times(3)).sendMessage(anyString());
        }
    }

    /**
     * 测试发送消息时抛出异常的情况
     */
    @Test
    public void testSendCrowdPackUpdateTaskExecuteMessage_ThrowException() throws Throwable {
        // arrange
        List<String> userUnionIds = Arrays.asList("user1", "user2");
        when(producer.sendMessage(anyString())).thenThrow(new RuntimeException("test exception"));
        try (MockedStatic<JsonUtils> mockedJsonUtils = Mockito.mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("testMessage");
            // act
            allPakcProducer.sendCrowdPackUpdateTaskExecuteMessage(userUnionIds);
            // assert
            verify(producer, times(3)).sendMessage(anyString());
        }
    }
}
