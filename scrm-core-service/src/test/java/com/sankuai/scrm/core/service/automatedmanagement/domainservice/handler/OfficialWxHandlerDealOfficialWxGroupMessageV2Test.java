package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.GroupSendStrategy;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.PrivateSendStrategy;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerDealOfficialWxGroupMessageV2Test {

    @Spy
    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private GroupSendStrategy groupSendStrategy;

    @Mock
    private PrivateSendStrategy privateSendStrategy;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private List<String> executorIds;

    private InvokeDetailKeyObject keyObject;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> detailDOList;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        executorIds = new ArrayList<>();
        executorIds.add("executor1");
        detailDOList = new ArrayList<>();
        keyObject = new // key
        // contentType
        // contentSubType
        // processOrchestrationNodeId
        InvokeDetailKeyObject("testKey", (byte) 0, (byte) 0, 1L);
    }

    /**
     * Test case for non-supply type message
     */
    @Test
    public void testDealOfficialWxGroupMessageV2_NonSupplyType() throws Throwable {
        // arrange
        // NON_SUPPLY type
        keyObject.setContentType((byte) 0);
        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(keyObject, detailDOList);
        doNothing().when(officialWxHandler).dealNormalOfficialWxGroupMessageV2(any(), anyList(), any(), anyList());
        // act
        officialWxHandler.dealOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, entry, null);
        // assert
        verify(officialWxHandler, times(1)).dealNormalOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, keyObject, detailDOList);
    }



    /**
     * Test case for manual product promotion supply type
     */
    @Test
    public void testDealOfficialWxGroupMessageV2_ManualProductPromotion() throws Throwable {
        // arrange
        // SUPPLY type
        keyObject.setContentType((byte) 1);
        // MANUAL_PRODUCT_PROMOTION
        keyObject.setContentSubType((byte) 2);
        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(keyObject, detailDOList);
        doNothing().when(officialWxHandler).dealChosenSupplyOfficialWxGroupMessageV2(any(), anyList(), any(), anyList(),any() );
        // act
        officialWxHandler.dealOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, entry,null );
        // assert
        verify(officialWxHandler, times(1)).dealChosenSupplyOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, keyObject, detailDOList, null);
    }



    /**
     * Test case for coupon promotion supply type
     */
    @Test
    public void testDealOfficialWxGroupMessageV2_CouponPromotion() throws Throwable {
        // arrange
        // SUPPLY type
        keyObject.setContentType((byte) 1);
        // COUPON_PROMOTION
        keyObject.setContentSubType((byte) 4);
        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(keyObject, detailDOList);
        doNothing().when(officialWxHandler).dealCouponSupplyOfficialWxGroupMessageV2(any(), anyList(), any(), anyList(), any());
        // act
        officialWxHandler.dealOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, entry, null);
        // assert
        verify(officialWxHandler, times(1)).dealCouponSupplyOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, keyObject, detailDOList, null);
    }

    /**
     * Test case for unknown content type
     */
    @Test
    public void testDealOfficialWxGroupMessageV2_UnknownContentType() throws Throwable {
        // arrange
        // Unknown type
        keyObject.setContentType((byte) 99);
        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(keyObject, detailDOList);
        // act
        officialWxHandler.dealOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, entry,null );
        // assert
        verify(officialWxHandler, never()).dealNormalOfficialWxGroupMessageV2(any(), anyList(), any(), anyList());
        verify(officialWxHandler, never()).dealAIRecallSupplyOfficialWxGroupMessageV2(any(), anyList(), any(), anyList(),any() );
        verify(officialWxHandler, never()).dealChosenSupplyOfficialWxGroupMessageV2(any(), anyList(), any(), anyList(), any());
        verify(officialWxHandler, never()).dealCouponSupplyOfficialWxGroupMessageV2(any(), anyList(), any(), anyList(), any());
    }

    /**
     * Test case for null input parameters
     */
    @Test
    public void testDealOfficialWxGroupMessageV2_NullInputs() throws Throwable {
        // arrange & act & assert
        assertThrows(NullPointerException.class, () -> {
            officialWxHandler.dealOfficialWxGroupMessageV2(null, null, null,null );
        });
        // verify no methods were called
        verify(officialWxHandler, never()).dealNormalOfficialWxGroupMessageV2(any(), anyList(), any(), anyList());
        verify(officialWxHandler, never()).dealAIRecallSupplyOfficialWxGroupMessageV2(any(), anyList(), any(), anyList(), any());
        verify(officialWxHandler, never()).dealChosenSupplyOfficialWxGroupMessageV2(any(), anyList(), any(), anyList(), any());
        verify(officialWxHandler, never()).dealCouponSupplyOfficialWxGroupMessageV2(any(), anyList(), any(), anyList(), any());
    }

    /**
     * Test case for null entry value but valid key
     */
    @Test
    public void testDealOfficialWxGroupMessageV2_NullEntryValue() throws Throwable {
        // arrange
        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> entry = new AbstractMap.SimpleEntry<>(keyObject, null);
        doNothing().when(officialWxHandler).dealNormalOfficialWxGroupMessageV2(any(), anyList(), any(), any());
        // act
        officialWxHandler.dealOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, entry, null);
        // assert
        verify(officialWxHandler, times(1)).dealNormalOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, keyObject, null);
    }
}
