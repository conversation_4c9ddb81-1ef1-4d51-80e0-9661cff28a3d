package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.ArgumentMatchers.isNull;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteWriteDomainService;
import com.sankuai.scrm.core.service.message.push.dto.MsgTaskDetailResultDTO;
import com.sankuai.scrm.core.service.message.push.enums.MsgPushDetailStatus;
import com.sankuai.scrm.core.service.message.push.request.MsgPushRequest;
import com.sankuai.scrm.core.service.message.push.response.MsgPushResponse;
import java.lang.reflect.Field;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

@ExtendWith(MockitoExtension.class)
public class GroupSendStrategyTest {

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @InjectMocks
    private GroupSendStrategy groupSendStrategy;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS;

    private ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO;

    private List<MsgTaskDetailResultDTO> taskDetailResultDTOS;

    private String msgId = "msgId";

    @Mock
    private ExecuteWriteDomainService executeWriteDomainService;

    @BeforeEach
    public void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        wxInvokeDetailDOS = new ArrayList<>();
        wxInvokeDetailDOS.add(new ScrmAmProcessOrchestrationWxInvokeDetailDO());
        wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        wxInvokeLogDO.setId(1L);
        taskDetailResultDTOS = new ArrayList<>();
    }

    /**
     * 测试场景：当任务详情结果和wxInvokeDetailDOS都不为空，且任务详情结果中包含有效的sender时，应正确调用updateExecuteLogSenderByIds方法
     */
    @Test
    public void testUpdateExecuteLogSenderWithValidData() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        // arrange
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setGroupId("receiverId");
        detailDO.setExecuteLogId(1L);
        wxInvokeDetailDOS.add(detailDO);
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = new ArrayList<>();
        MsgTaskDetailResultDTO taskDetailResultDTO = new MsgTaskDetailResultDTO();
        taskDetailResultDTO.setSender("sender");
        taskDetailResultDTO.setReceiverId("receiverId");
        taskDetailResultDTO.setStatus(MsgPushDetailStatus.SUCCESS.getCode());
        taskDetailResultDTOS.add(taskDetailResultDTO);
        // act
        Method method = groupSendStrategy.getClass().getDeclaredMethod("updateExecuteLogSender", List.class, List.class);
        method.setAccessible(true);
        method.invoke(groupSendStrategy, wxInvokeDetailDOS, taskDetailResultDTOS);
        // assert
        verify(executeWriteDomainService, times(1)).updateExecuteLogSenderByIds(anyString(), anyList());
    }

    /**
     * 测试场景：当任务详情结果为空时，不应调用updateExecuteLogSenderByIds方法
     */
    @Test
    public void testUpdateExecuteLogSenderWithEmptyTaskDetailResultDTOs() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        // arrange
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS = new ArrayList<>();
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = new ArrayList<>();
        // act
        Method method = groupSendStrategy.getClass().getDeclaredMethod("updateExecuteLogSender", List.class, List.class);
        method.setAccessible(true);
        method.invoke(groupSendStrategy, wxInvokeDetailDOS, taskDetailResultDTOS);
        // assert
        verify(executeWriteDomainService, never()).updateExecuteLogSenderByIds(anyString(), anyList());
    }

    /**
     * 测试场景：当wxInvokeDetailDOS为空时，不应调用updateExecuteLogSenderByIds方法
     */
    @Test
    public void testUpdateExecuteLogSenderWithEmptyWxInvokeDetailDOS() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        // arrange
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS = new ArrayList<>();
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = new ArrayList<>();
        MsgTaskDetailResultDTO taskDetailResultDTO = new MsgTaskDetailResultDTO();
        taskDetailResultDTO.setSender("sender");
        taskDetailResultDTO.setReceiverId("receiverId");
        taskDetailResultDTO.setStatus(MsgPushDetailStatus.SUCCESS.getCode());
        taskDetailResultDTOS.add(taskDetailResultDTO);
        // act
        Method method = groupSendStrategy.getClass().getDeclaredMethod("updateExecuteLogSender", List.class, List.class);
        method.setAccessible(true);
        method.invoke(groupSendStrategy, wxInvokeDetailDOS, taskDetailResultDTOS);
        // assert
        verify(executeWriteDomainService, never()).updateExecuteLogSenderByIds(anyString(), anyList());
    }

    /**
     * 测试场景：当任务详情结果中不包含有效的sender时，不应调用updateExecuteLogSenderByIds方法
     */
    @Test
    public void testUpdateExecuteLogSenderWithInvalidSender() throws NoSuchMethodException, InvocationTargetException, IllegalAccessException {
        // arrange
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> wxInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setGroupId("receiverId");
        detailDO.setExecuteLogId(1L);
        wxInvokeDetailDOS.add(detailDO);
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = new ArrayList<>();
        MsgTaskDetailResultDTO taskDetailResultDTO = new MsgTaskDetailResultDTO();
        taskDetailResultDTO.setSender("");
        taskDetailResultDTO.setReceiverId("receiverId");
        taskDetailResultDTO.setStatus(MsgPushDetailStatus.SUCCESS.getCode());
        taskDetailResultDTOS.add(taskDetailResultDTO);
        // act
        Method method = groupSendStrategy.getClass().getDeclaredMethod("updateExecuteLogSender", List.class, List.class);
        method.setAccessible(true);
        method.invoke(groupSendStrategy, wxInvokeDetailDOS, taskDetailResultDTOS);
        // assert
        verify(executeWriteDomainService, never()).updateExecuteLogSenderByIds(anyString(), anyList());
    }

    /**
     * Tests processFailure with empty detail list
     */
    @Test
    public void testProcessFailureWithEmptyList() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> details = Collections.emptyList();
        MsgPushRequest request = new MsgPushRequest();
        MsgPushResponse<String> response = MsgPushResponse.success("test");
        // act
        groupSendStrategy.processFailure(details, request, response);
        // assert
        verify(executeWriteDomainService).updateExecuteLogStatusByIds(eq(ScrmProcessOrchestrationExecuteStatusTypeEnum.FAILED_CALL_EXTERNAL_SERVICE), eq(Collections.emptyList()));
    }

    /**
     * Tests processFailure with null detail list
     */
    @Test
    public void testProcessFailureWithNullList() throws Throwable {
        // arrange
        MsgPushRequest request = new MsgPushRequest();
        MsgPushResponse<String> response = MsgPushResponse.success("test");
        // act & assert
        assertThrows(NullPointerException.class, () -> groupSendStrategy.processFailure(null, request, response));
    }

    /**
     * Tests processFailure with single detail item
     */
    @Test
    public void testProcessFailureWithSingleDetail() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setExecuteLogId(1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> details = Collections.singletonList(detail);
        MsgPushRequest request = new MsgPushRequest();
        MsgPushResponse<String> response = MsgPushResponse.success("test");
        // act
        groupSendStrategy.processFailure(details, request, response);
        // assert
        verify(executeWriteDomainService).updateExecuteLogStatusByIds(eq(ScrmProcessOrchestrationExecuteStatusTypeEnum.FAILED_CALL_EXTERNAL_SERVICE), eq(Collections.singletonList(1L)));
    }

    /**
     * Tests processFailure with multiple detail items
     */
    @Test
    public void testProcessFailureWithMultipleDetails() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail1 = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail1.setExecuteLogId(1L);
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail2 = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail2.setExecuteLogId(2L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> details = Arrays.asList(detail1, detail2);
        MsgPushRequest request = new MsgPushRequest();
        MsgPushResponse<String> response = MsgPushResponse.success("test");
        // act
        groupSendStrategy.processFailure(details, request, response);
        // assert
        verify(executeWriteDomainService).updateExecuteLogStatusByIds(eq(ScrmProcessOrchestrationExecuteStatusTypeEnum.FAILED_CALL_EXTERNAL_SERVICE), eq(Arrays.asList(1L, 2L)));
    }

    /**
     * Tests processFailure with failed response
     */
    @Test
    public void testProcessFailureWithFailedResponse() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setExecuteLogId(1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> details = Collections.singletonList(detail);
        MsgPushRequest request = new MsgPushRequest();
        MsgPushResponse<String> response = MsgPushResponse.fail("error");
        // act
        groupSendStrategy.processFailure(details, request, response);
        // assert
        verify(executeWriteDomainService).updateExecuteLogStatusByIds(eq(ScrmProcessOrchestrationExecuteStatusTypeEnum.FAILED_CALL_EXTERNAL_SERVICE), eq(Collections.singletonList(1L)));
    }

    /**
     * Tests processFailure with null request
     */
    @Test
    public void testProcessFailureWithNullRequest() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setExecuteLogId(1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> details = Collections.singletonList(detail);
        MsgPushResponse<String> response = MsgPushResponse.success("test");
        // act
        groupSendStrategy.processFailure(details, null, response);
        // assert
        verify(executeWriteDomainService).updateExecuteLogStatusByIds(eq(ScrmProcessOrchestrationExecuteStatusTypeEnum.FAILED_CALL_EXTERNAL_SERVICE), eq(Collections.singletonList(1L)));
    }

    /**
     * Tests processFailure with null response
     */
    @Test
    public void testProcessFailureWithNullResponse() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setExecuteLogId(1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> details = Collections.singletonList(detail);
        MsgPushRequest request = new MsgPushRequest();
        // act
        groupSendStrategy.processFailure(details, request, null);
        // assert
        verify(executeWriteDomainService).updateExecuteLogStatusByIds(eq(ScrmProcessOrchestrationExecuteStatusTypeEnum.FAILED_CALL_EXTERNAL_SERVICE), eq(Collections.singletonList(1L)));
    }
}
