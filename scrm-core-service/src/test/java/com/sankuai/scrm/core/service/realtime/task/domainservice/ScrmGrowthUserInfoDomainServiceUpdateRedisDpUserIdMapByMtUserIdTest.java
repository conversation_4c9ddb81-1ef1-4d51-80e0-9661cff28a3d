package com.sankuai.scrm.core.service.realtime.task.domainservice;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.dianping.tgc.process.enums.PlatformEnum;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmUserGrowthIDMappingDO;
import com.sankuai.scrm.core.service.realtime.task.dal.example.ScrmUserGrowthIDMappingDOExample;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmUserGrowthIDMappingDOMapper;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ScrmGrowthUserInfoDomainServiceUpdateRedisDpUserIdMapByMtUserIdTest {

    @Mock
    private RedisStoreClient redisClient;

    @InjectMocks
    private ScrmGrowthUserInfoDomainService scrmGrowthUserInfoDomainService;

    @Mock
    private ScrmUserGrowthIDMappingDOMapper idMappingDOMapper;

    private static final Long VALID_MT_USER_ID = 12345L;

    private static final Long VALID_DP_USER_ID = 67890L;

    private static final String PLATFORM = "mt";

    private String userIdMapKey;

    /**
     * Test normal case with valid inputs
     */
    @Test
    public void testUpdateRedisDpUserIdMapByMtUserIdNormalCase() throws Throwable {
        // arrange
        Long dpUserId = 12345L;
        Long mtUserId = 67890L;
        String platForm = "wechat";
        StoreKey expectedKey = new StoreKey("scrmUserIdMap", platForm, mtUserId);
        // act
        scrmGrowthUserInfoDomainService.updateRedisDpUserIdMapByMtUserId(dpUserId, mtUserId, platForm);
        // assert
        verify(redisClient).set(expectedKey, dpUserId);
    }

    /**
     * Test with minimum Long values
     */
    @Test
    public void testUpdateRedisDpUserIdMapByMtUserIdMinLongValues() throws Throwable {
        // arrange
        Long dpUserId = Long.MIN_VALUE;
        Long mtUserId = Long.MIN_VALUE;
        String platForm = "miniapp";
        StoreKey expectedKey = new StoreKey("scrmUserIdMap", platForm, mtUserId);
        // act
        scrmGrowthUserInfoDomainService.updateRedisDpUserIdMapByMtUserId(dpUserId, mtUserId, platForm);
        // assert
        verify(redisClient).set(expectedKey, dpUserId);
    }

    /**
     * Test with maximum Long values
     */
    @Test
    public void testUpdateRedisDpUserIdMapByMtUserIdMaxLongValues() throws Throwable {
        // arrange
        Long dpUserId = Long.MAX_VALUE;
        Long mtUserId = Long.MAX_VALUE;
        String platForm = "alipay";
        StoreKey expectedKey = new StoreKey("scrmUserIdMap", platForm, mtUserId);
        // act
        scrmGrowthUserInfoDomainService.updateRedisDpUserIdMapByMtUserId(dpUserId, mtUserId, platForm);
        // assert
        verify(redisClient).set(expectedKey, dpUserId);
    }

    /**
     * Test with empty platform string
     */
    @Test
    public void testUpdateRedisDpUserIdMapByMtUserIdEmptyPlatform() throws Throwable {
        // arrange
        Long dpUserId = 12345L;
        Long mtUserId = 67890L;
        String platForm = "";
        StoreKey expectedKey = new StoreKey("scrmUserIdMap", platForm, mtUserId);
        // act
        scrmGrowthUserInfoDomainService.updateRedisDpUserIdMapByMtUserId(dpUserId, mtUserId, platForm);
        // assert
        verify(redisClient).set(expectedKey, dpUserId);
    }

    /**
     * Test with null dpUserId - should still work as StoreKey creation doesn't check dpUserId
     */
    @Test
    public void testUpdateRedisDpUserIdMapByMtUserIdNullDpUserId() throws Throwable {
        // arrange
        Long dpUserId = null;
        Long mtUserId = 67890L;
        String platForm = "wechat";
        StoreKey expectedKey = new StoreKey("scrmUserIdMap", platForm, mtUserId);
        // act
        scrmGrowthUserInfoDomainService.updateRedisDpUserIdMapByMtUserId(dpUserId, mtUserId, platForm);
        // assert
        verify(redisClient).set(expectedKey, dpUserId);
    }

    /**
     * Test with null mtUserId - should still work as StoreKey accepts null values
     */
    @Test
    public void testUpdateRedisDpUserIdMapByMtUserIdNullMtUserId() throws Throwable {
        // arrange
        Long dpUserId = 12345L;
        Long mtUserId = null;
        String platForm = "wechat";
        StoreKey expectedKey = new StoreKey("scrmUserIdMap", platForm, mtUserId);
        // act
        scrmGrowthUserInfoDomainService.updateRedisDpUserIdMapByMtUserId(dpUserId, mtUserId, platForm);
        // assert
        verify(redisClient).set(expectedKey, dpUserId);
    }

    /**
     * Test with null platform - should still work as StoreKey accepts null values
     */
    @Test
    public void testUpdateRedisDpUserIdMapByMtUserIdNullPlatform() throws Throwable {
        // arrange
        Long dpUserId = 12345L;
        Long mtUserId = 67890L;
        String platForm = null;
        StoreKey expectedKey = new StoreKey("scrmUserIdMap", platForm, mtUserId);
        // act
        scrmGrowthUserInfoDomainService.updateRedisDpUserIdMapByMtUserId(dpUserId, mtUserId, platForm);
        // assert
        verify(redisClient).set(expectedKey, dpUserId);
    }

    /**
     * Test when Redis operation fails
     */
    @Test
    public void testUpdateRedisDpUserIdMapByMtUserIdRedisFailure() throws Throwable {
        // arrange
        Long dpUserId = 12345L;
        Long mtUserId = 67890L;
        String platForm = "wechat";
        StoreKey expectedKey = new StoreKey("scrmUserIdMap", platForm, mtUserId);
        doThrow(new RuntimeException("Redis operation failed")).when(redisClient).set(expectedKey, dpUserId);
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            scrmGrowthUserInfoDomainService.updateRedisDpUserIdMapByMtUserId(dpUserId, mtUserId, platForm);
        });
    }

    @BeforeEach
    void setUp() throws Exception {
        Field field = ScrmGrowthUserInfoDomainService.class.getDeclaredField("USER_ID_MAP_KEY");
        field.setAccessible(true);
        userIdMapKey = (String) field.get(null);
    }

    @Test
    public void testQueryDpUserIdByMtUserId_NullInput() throws Throwable {
        // arrange - no setup needed
        // act
        Long result = scrmGrowthUserInfoDomainService.queryDpUserIdByMtUserId(null);
        // assert
        assertNull(result);
        verifyNoInteractions(redisClient, idMappingDOMapper);
    }

    @Test
    public void testQueryDpUserIdByMtUserId_InvalidInput() throws Throwable {
        // arrange - no setup needed
        // act
        Long result = scrmGrowthUserInfoDomainService.queryDpUserIdByMtUserId(0L);
        // assert
        assertNull(result);
        verifyNoInteractions(redisClient, idMappingDOMapper);
    }

    @Test
    public void testQueryDpUserIdByMtUserId_FoundInRedis() throws Throwable {
        // arrange
        StoreKey expectedKey = new StoreKey(userIdMapKey, PLATFORM, VALID_MT_USER_ID);
        when(redisClient.get(expectedKey)).thenReturn(VALID_MT_USER_ID);
        // act
        Long result = scrmGrowthUserInfoDomainService.queryDpUserIdByMtUserId(VALID_MT_USER_ID);
        // assert
        assertEquals(VALID_MT_USER_ID, result);
        verify(redisClient).get(expectedKey);
        verifyNoInteractions(idMappingDOMapper);
    }

    @Test
    public void testQueryDpUserIdByMtUserId_FoundInDatabase() throws Throwable {
        // arrange
        StoreKey redisKey = new StoreKey(userIdMapKey, PLATFORM, VALID_MT_USER_ID);
        when(redisClient.get(redisKey)).thenReturn(null);
        ScrmUserGrowthIDMappingDO mapping = new ScrmUserGrowthIDMappingDO();
        mapping.setMtUserid(VALID_MT_USER_ID);
        mapping.setDpUserid(VALID_DP_USER_ID);
        when(idMappingDOMapper.selectByExample(any(ScrmUserGrowthIDMappingDOExample.class))).thenReturn(Collections.singletonList(mapping));
        // act
        Long result = scrmGrowthUserInfoDomainService.queryDpUserIdByMtUserId(VALID_MT_USER_ID);
        // assert
        assertEquals(VALID_DP_USER_ID, result);
        verify(redisClient).get(redisKey);
        verify(redisClient).set(redisKey, VALID_DP_USER_ID);
        verify(idMappingDOMapper).selectByExample(any(ScrmUserGrowthIDMappingDOExample.class));
    }

    @Test
    public void testQueryDpUserIdByMtUserId_NotFound() throws Throwable {
        // arrange
        StoreKey redisKey = new StoreKey(userIdMapKey, PLATFORM, VALID_MT_USER_ID);
        when(redisClient.get(redisKey)).thenReturn(null);
        when(idMappingDOMapper.selectByExample(any(ScrmUserGrowthIDMappingDOExample.class))).thenReturn(Collections.emptyList());
        // act
        Long result = scrmGrowthUserInfoDomainService.queryDpUserIdByMtUserId(VALID_MT_USER_ID);
        // assert
        assertNull(result);
        verify(redisClient).get(redisKey);
        verify(redisClient, never()).set(any(), any());
        verify(idMappingDOMapper).selectByExample(any(ScrmUserGrowthIDMappingDOExample.class));
    }

    @Test
    public void testQueryDpUserIdByMtUserId_MultipleRecordsInDb() throws Throwable {
        // arrange
        StoreKey redisKey = new StoreKey(userIdMapKey, PLATFORM, VALID_MT_USER_ID);
        when(redisClient.get(redisKey)).thenReturn(null);
        ScrmUserGrowthIDMappingDO mapping1 = new ScrmUserGrowthIDMappingDO();
        mapping1.setMtUserid(VALID_MT_USER_ID);
        mapping1.setDpUserid(VALID_DP_USER_ID);
        ScrmUserGrowthIDMappingDO mapping2 = new ScrmUserGrowthIDMappingDO();
        mapping2.setMtUserid(VALID_MT_USER_ID);
        mapping2.setDpUserid(99999L);
        when(idMappingDOMapper.selectByExample(any(ScrmUserGrowthIDMappingDOExample.class))).thenReturn(Arrays.asList(mapping1, mapping2));
        // act
        Long result = scrmGrowthUserInfoDomainService.queryDpUserIdByMtUserId(VALID_MT_USER_ID);
        // assert
        assertEquals(VALID_DP_USER_ID, result);
        verify(redisClient).get(redisKey);
        verify(redisClient).set(redisKey, VALID_DP_USER_ID);
        verify(idMappingDOMapper).selectByExample(any(ScrmUserGrowthIDMappingDOExample.class));
    }

    @Test
    public void testExistUserOfAppId_WhenMtUserIdMatches() throws Throwable {
        // arrange
        when(idMappingDOMapper.countByExample(any(ScrmUserGrowthIDMappingDOExample.class))).thenReturn(1L);
        // act
        boolean result = scrmGrowthUserInfoDomainService.existUserOfAppId(123L, "testApp");
        // assert
        assertTrue(result);
        verify(idMappingDOMapper, times(1)).countByExample(any(ScrmUserGrowthIDMappingDOExample.class));
    }

    @Test
    public void testExistUserOfAppId_WhenDpUserIdMatches() throws Throwable {
        // arrange
        when(idMappingDOMapper.countByExample(any(ScrmUserGrowthIDMappingDOExample.class))).thenReturn(1L);
        // act
        boolean result = scrmGrowthUserInfoDomainService.existUserOfAppId(456L, "testApp");
        // assert
        assertTrue(result);
        verify(idMappingDOMapper, times(1)).countByExample(any(ScrmUserGrowthIDMappingDOExample.class));
    }

    @Test
    public void testExistUserOfAppId_WhenBothIdsMatch() throws Throwable {
        // arrange
        when(idMappingDOMapper.countByExample(any(ScrmUserGrowthIDMappingDOExample.class))).thenReturn(2L);
        // act
        boolean result = scrmGrowthUserInfoDomainService.existUserOfAppId(789L, "testApp");
        // assert
        assertTrue(result);
        verify(idMappingDOMapper, times(1)).countByExample(any(ScrmUserGrowthIDMappingDOExample.class));
    }

    @Test
    public void testExistUserOfAppId_WhenUserDoesNotExist() throws Throwable {
        // arrange
        when(idMappingDOMapper.countByExample(any(ScrmUserGrowthIDMappingDOExample.class))).thenReturn(0L);
        // act
        boolean result = scrmGrowthUserInfoDomainService.existUserOfAppId(999L, "testApp");
        // assert
        assertFalse(result);
        verify(idMappingDOMapper, times(1)).countByExample(any(ScrmUserGrowthIDMappingDOExample.class));
    }

    @Test
    public void testExistUserOfAppId_WhenUserIdIsNull() throws Throwable {
        // arrange
        // No mock setup needed as we expect exception before DB call
        // act & assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            scrmGrowthUserInfoDomainService.existUserOfAppId(null, "testApp");
        });
        assertEquals("Value for mtUserid cannot be null", exception.getMessage());
        verify(idMappingDOMapper, never()).countByExample(any());
    }

    @Test
    public void testExistUserOfAppId_WhenAppIdIsNull() throws Throwable {
        // arrange
        // No mock setup needed as we expect exception before DB call
        // act & assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> {
            scrmGrowthUserInfoDomainService.existUserOfAppId(123L, null);
        });
        assertEquals("Value for appId cannot be null", exception.getMessage());
        verify(idMappingDOMapper, never()).countByExample(any());
    }

    @Test
    public void testExistUserOfAppId_WhenAppIdIsEmpty() throws Throwable {
        // arrange
        when(idMappingDOMapper.countByExample(any(ScrmUserGrowthIDMappingDOExample.class))).thenReturn(0L);
        // act
        boolean result = scrmGrowthUserInfoDomainService.existUserOfAppId(123L, "");
        // assert
        assertFalse(result);
        verify(idMappingDOMapper, times(1)).countByExample(any(ScrmUserGrowthIDMappingDOExample.class));
    }

    @Test
    public void testExistUserOfAppId_WhenDatabaseError() throws Throwable {
        // arrange
        when(idMappingDOMapper.countByExample(any(ScrmUserGrowthIDMappingDOExample.class))).thenThrow(new RuntimeException("DB error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            scrmGrowthUserInfoDomainService.existUserOfAppId(123L, "testApp");
        });
        verify(idMappingDOMapper, times(1)).countByExample(any(ScrmUserGrowthIDMappingDOExample.class));
    }

    @Test
    public void testExistUserOfAppId_MTPlatformUserExists() throws Throwable {
        // arrange
        Long userId = 123L;
        List<String> appIds = Arrays.asList("app1", "app2");
        when(idMappingDOMapper.countByExample(any(ScrmUserGrowthIDMappingDOExample.class))).thenReturn(1L);
        // act
        boolean result = scrmGrowthUserInfoDomainService.existUserOfAppId(userId, appIds, PlatformEnum.MT);
        // assert
        assertTrue(result);
    }

    @Test
    public void testExistUserOfAppId_DPPlatformUserExists() throws Throwable {
        // arrange
        Long userId = 123L;
        List<String> appIds = Arrays.asList("app1", "app2");
        when(idMappingDOMapper.countByExample(any(ScrmUserGrowthIDMappingDOExample.class))).thenReturn(1L);
        // act
        boolean result = scrmGrowthUserInfoDomainService.existUserOfAppId(userId, appIds, PlatformEnum.DP);
        // assert
        assertTrue(result);
    }

    @Test
    public void testExistUserOfAppId_MTPlatformUserNotExists() throws Throwable {
        // arrange
        Long userId = 123L;
        List<String> appIds = Arrays.asList("app1", "app2");
        when(idMappingDOMapper.countByExample(any(ScrmUserGrowthIDMappingDOExample.class))).thenReturn(0L);
        // act
        boolean result = scrmGrowthUserInfoDomainService.existUserOfAppId(userId, appIds, PlatformEnum.MT);
        // assert
        assertFalse(result);
    }

    @Test
    public void testExistUserOfAppId_DPPlatformUserNotExists() throws Throwable {
        // arrange
        Long userId = 123L;
        List<String> appIds = Arrays.asList("app1", "app2");
        when(idMappingDOMapper.countByExample(any(ScrmUserGrowthIDMappingDOExample.class))).thenReturn(0L);
        // act
        boolean result = scrmGrowthUserInfoDomainService.existUserOfAppId(userId, appIds, PlatformEnum.DP);
        // assert
        assertFalse(result);
    }

    @Test
    public void testExistUserOfAppId_EmptyAppIds() throws Throwable {
        // arrange
        Long userId = 123L;
        List<String> appIds = Collections.emptyList();
        when(idMappingDOMapper.countByExample(any(ScrmUserGrowthIDMappingDOExample.class))).thenReturn(0L);
        // act
        boolean result = scrmGrowthUserInfoDomainService.existUserOfAppId(userId, appIds, PlatformEnum.MT);
        // assert
        assertFalse(result);
    }

    @Test
    public void testExistUserOfAppId_NullAppIds() throws Throwable {
        // arrange
        Long userId = 123L;
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            scrmGrowthUserInfoDomainService.existUserOfAppId(userId, null, PlatformEnum.MT);
        });
    }

    @Test
    public void testExistUserOfAppId_NullPlatform() throws Throwable {
        // arrange
        Long userId = 123L;
        List<String> appIds = Arrays.asList("app1", "app2");
        when(idMappingDOMapper.countByExample(any(ScrmUserGrowthIDMappingDOExample.class))).thenReturn(0L);
        // act
        boolean result = scrmGrowthUserInfoDomainService.existUserOfAppId(userId, appIds, null);
        // assert
        assertFalse(result);
    }

    @Test
    public void testExistUserOfAppId_MultipleCounts() throws Throwable {
        // arrange
        Long userId = 123L;
        List<String> appIds = Arrays.asList("app1", "app2");
        when(idMappingDOMapper.countByExample(any(ScrmUserGrowthIDMappingDOExample.class))).thenReturn(2L);
        // act
        boolean result = scrmGrowthUserInfoDomainService.existUserOfAppId(userId, appIds, PlatformEnum.MT);
        // assert
        assertTrue(result);
    }
}
