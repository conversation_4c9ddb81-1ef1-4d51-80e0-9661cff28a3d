package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.cat.message.Transaction;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.dz.srcm.activity.fission.dto.task.FissionMatchTaskDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.request.ExecuteProcessOrchestrationRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.ExecuteProcessOrchestrationResultVO;
import com.sankuai.scrm.core.service.activity.fission.mq.consumer.FissionMatchTaskConsumer;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.infrastructure.mq.bo.corpwxgroupchange.OpenGroupChangeEvent;
import com.sankuai.scrm.core.service.infrastructure.mq.consumer.CorpWxGroupChangeConsumer;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ScrmAutoManagementServiceImplExecuteProcessOrchestrationExecuteLogByUnionIdTest {

    @Mock
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private FissionMatchTaskConsumer fissionMatchTaskConsumer;

    @Mock
    private CorpWxGroupChangeConsumer corpWxGroupChangeConsumer;

    @Mock
    private Transaction transaction;

    @InjectMocks
    private ScrmAutoManagementServiceImpl scrmAutoManagementService;

    private ExecuteProcessOrchestrationRequest request;

    @BeforeEach
    void setUp() {
        request = new ExecuteProcessOrchestrationRequest();
        request.setAppId("testAppId");
    }

    /**
     * Test null request returns failure response
     */
    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionId_NullRequest() throws Throwable {
        // act
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(null);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("请求参数不能为空", response.getMsg());
    }

    /**
     * Test blank appId returns failure response
     */
    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionId_BlankAppId() throws Throwable {
        // arrange
        request.setAppId("");
        // act
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("appId不能为空", response.getMsg());
    }

    /**
     * Test real time process orchestration type
     */
    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionId_RealTimeProcess() throws Throwable {
        // arrange
        request.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue());
        request.setUnionId("testUnionId");
        when(executeWriteDomainService.executeByUserUnionId(anyString(), anyString())).thenReturn(true);
        // act
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        // assert
        assertTrue(response.isSuccess());
        assertTrue(response.getData().getSuccess());
        verify(executeWriteDomainService).executeByUserUnionId("testUnionId", "testAppId");
    }

    /**
     * Test manual crowd pack update type with unionIds
     */
    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionId_ManualCrowdPackWithUnionIds() throws Throwable {
        // arrange
        request.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.MANUAL_CROWD_PACK_UPDATE.getValue());
        request.setCrowdPackId(123L);
        List<String> unionIds = Arrays.asList("union1", "union2");
        request.setUnionIds(unionIds);
        when(executeWriteDomainService.updateCrowdPackManualSubTask(any(), anyLong(), anyString())).thenReturn(true);
        // act
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        // assert
        assertTrue(response.isSuccess());
        assertTrue(response.getData().getSuccess());
        verify(executeWriteDomainService).updateCrowdPackManualSubTask(any(), eq(123L), eq("testAppId"));
    }

    /**
     * Test manual crowd pack update type with single unionId
     */
    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionId_ManualCrowdPackWithSingleUnionId() throws Throwable {
        // arrange
        request.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.MANUAL_CROWD_PACK_UPDATE.getValue());
        request.setCrowdPackId(123L);
        request.setUnionId("testUnionId");
        when(executeWriteDomainService.updateCrowdPackManualSubTask(any(), anyLong(), anyString())).thenReturn(true);
        // act
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        // assert
        assertTrue(response.isSuccess());
        assertTrue(response.getData().getSuccess());
        verify(executeWriteDomainService).updateCrowdPackManualSubTask(any(), eq(123L), eq("testAppId"));
    }

    /**
     * Test deleted timed process orchestration type
     */
    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionId_DeletedTimedProcess() throws Throwable {
        // arrange
        request.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.DELETED_TIMED_PROCESS_ORCHESTRATION.getValue());
        request.setProcessOrchestrationId(123L);
        request.setProcessOrchestrationVersion("v1");
        when(executeWriteDomainService.executeWxOfficialService(anyLong(), anyString(), anyString())).thenReturn(true);
        // act
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        // assert
        assertTrue(response.isSuccess());
        assertTrue(response.getData().getSuccess());
        verify(executeWriteDomainService).executeWxOfficialService(123L, "v1", "testAppId");
    }

    /**
     * Test deleted periodic timed task type
     */
    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionId_DeletedPeriodicTimedTask() throws Throwable {
        // arrange
        request.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.DELETED_PERIODIC_TIMED_TASK.getValue());
        request.setProcessOrchestrationVersion("{\"channel\":1,\"operateType\":1}");
        when(informationGatheringService.dealCommunityOrder(any())).thenReturn(ConsumeStatus.CONSUME_SUCCESS);
        // act
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        // assert
        assertTrue(response.isSuccess());
        assertTrue(response.getData().getSuccess());
        verify(informationGatheringService).dealCommunityOrder(any());
    }

    /**
     * Test deleted special timed task type
     */
    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionId_DeletedSpecialTimedTask() throws Throwable {
        // arrange
        request.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.DELETED_SPECIAL_TIMED_TASK.getValue());
        FissionMatchTaskDTO taskDTO = new FissionMatchTaskDTO();
        request.setProcessOrchestrationVersion(JsonUtils.toStr(taskDTO));
        when(fissionMatchTaskConsumer.dealFissionMatchTaskDTO(any(FissionMatchTaskDTO.class))).thenReturn(ConsumeStatus.CONSUME_SUCCESS);
        // act
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        // assert
        assertTrue(response.isSuccess());
        assertTrue(response.getData().getSuccess());
        verify(fissionMatchTaskConsumer).dealFissionMatchTaskDTO(any(FissionMatchTaskDTO.class));
    }

    /**
     * Test deleted external service call status update type
     */
    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionId_DeletedExternalServiceCall() throws Throwable {
        // arrange
        request.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.DELETED_EXTERNAL_SERVICE_CALL_STATUS_UPDATE.getValue());
        OpenGroupChangeEvent event = new OpenGroupChangeEvent();
        request.setProcessOrchestrationVersion(JsonUtils.toStr(event));
        doNothing().when(corpWxGroupChangeConsumer).dealEvent(any(OpenGroupChangeEvent.class));
        // act
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        // assert
        assertTrue(response.isSuccess());
        assertTrue(response.getData().getSuccess());
        verify(corpWxGroupChangeConsumer).dealEvent(any(OpenGroupChangeEvent.class));
    }

    /**
     * Test unknown process orchestration type returns success false
     */
    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionId_UnknownType() throws Throwable {
        // arrange
        // Unknown type
        request.setProcessOrchestrationType(999);
        // act
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        // assert
        assertTrue(response.isSuccess());
        assertFalse(response.getData().getSuccess());
    }

    /**
     * Test exception handling
     */
    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionId_Exception() throws Throwable {
        // arrange
        request.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue());
        request.setUnionId("testUnionId");
        when(executeWriteDomainService.executeByUserUnionId(anyString(), anyString())).thenThrow(new RuntimeException("Test exception"));
        // act
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("系统异常", response.getMsg());
    }

    /**
     * Test real time process orchestration type with execution failure
     */
    @Test
    public void testExecuteProcessOrchestrationExecuteLogByUnionId_RealTimeProcessFailure() throws Throwable {
        // arrange
        request.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue());
        request.setUnionId("testUnionId");
        when(executeWriteDomainService.executeByUserUnionId(anyString(), anyString())).thenReturn(false);
        // act
        RemoteResponse<ExecuteProcessOrchestrationResultVO> response = scrmAutoManagementService.executeProcessOrchestrationExecuteLogByUnionId(request);
        // assert
        assertTrue(response.isSuccess());
        assertFalse(response.getData().getSuccess());
    }
}
