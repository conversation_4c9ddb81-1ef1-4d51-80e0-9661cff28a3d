package com.sankuai.scrm.core.service.automatedmanagement.mq.consumer;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import java.lang.reflect.Field;
import java.util.Properties;
import static org.mockito.ArgumentMatchers.*;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import org.mockito.InjectMocks;

@RunWith(MockitoJUnitRunner.class)
public class ManualRefreshProductInfoConsumer_AfterPropertiesSetTest {

    @Mock(lenient = true)
    private IConsumerProcessor consumer;

    private ManualRefreshProductInfoConsumer consumerUnderTest;

    private MockedStatic<MafkaClient> mockedMafkaClient;

    @Before
    public void setUp() throws Exception {
        consumerUnderTest = new ManualRefreshProductInfoConsumer();
        // Use reflection to set the static consumer field
        Field consumerField = ManualRefreshProductInfoConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        // Set static field
        consumerField.set(null, consumer);
        // Mock static MafkaClient
        mockedMafkaClient = mockStatic(MafkaClient.class);
        mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenReturn(consumer);
    }

    @After
    public void tearDown() {
        mockedMafkaClient.close();
    }

    /**
     * 测试afterPropertiesSet方法，正常情况
     */
    @Test
    public void testAfterPropertiesSetNormal() throws Throwable {
        // arrange
        doNothing().when(consumer).recvMessageWithParallel(any(), any());
        // act
        consumerUnderTest.afterPropertiesSet();
        // assert
        verify(consumer, times(1)).recvMessageWithParallel(eq(String.class), any());
        // Verify MafkaClient.buildConsumerFactory was called with correct parameters
        mockedMafkaClient.verify(() -> MafkaClient.buildConsumerFactory(argThat((Properties properties) -> "daozong".equals(properties.getProperty(ConsumerConstants.MafkaBGNamespace)) && "com.sankuai.medicalcosmetology.scrm.core".equals(properties.getProperty(ConsumerConstants.MafkaClientAppkey)) && "scrm.product.pool.refresh.task.consumer".equals(properties.getProperty(ConsumerConstants.SubscribeGroup))), eq("scrm.product.pool.refresh.task")));
    }

    /**
     * 测试afterPropertiesSet方法，异常情况
     */
    @Test(expected = Exception.class)
    public void testAfterPropertiesSetException() throws Throwable {
        // arrange
        doThrow(new Exception()).when(consumer).recvMessageWithParallel(any(), any());
        // act
        consumerUnderTest.afterPropertiesSet();
        // No need to verify here as we're expecting an exception
    }
}
