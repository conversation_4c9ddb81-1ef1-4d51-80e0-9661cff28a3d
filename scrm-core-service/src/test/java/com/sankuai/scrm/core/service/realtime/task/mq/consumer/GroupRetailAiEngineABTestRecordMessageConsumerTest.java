package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmAIAgentTestGroupRecordDO;
import com.sankuai.scrm.core.service.realtime.task.dal.example.ScrmAIAgentTestGroupRecordDOExample;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmAIAgentTestGroupRecordDOMapper;
import com.sankuai.scrm.core.service.realtime.task.domainservice.ScrmGrowthUserInfoDomainService;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineABTestRecordMessageDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.List;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GroupRetailAiEngineABTestRecordMessageConsumerTest {

    @InjectMocks
    private GroupRetailAiEngineABTestRecordMessageConsumer consumer;

    @Mock
    private ScrmGrowthUserInfoDomainService scrmGrowthUserInfoDomainService;

    @Mock
    private ScrmAIAgentTestGroupRecordDOMapper aiAgentTestGroupRecordDOMapper;

    @Mock
    private RedisStoreClient redisClient;

    private Method recvMessageMethod;

    /**
     * Test successful initialization with all properties set correctly
     */
    @Test
    public void testAfterPropertiesSetSuccess() throws Exception {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockConsumer = mock(IConsumerProcessor.class);
            mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), anyString())).thenReturn(mockConsumer);
            // act
            consumer.afterPropertiesSet();
            // assert
            mockedMafkaClient.verify(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), eq("scrm.group.retail.ai.engine.test.record.message")));
            verify(mockConsumer).recvMessageWithParallel(eq(String.class), any());
        }
    }

    /**
     * Test when MafkaClient throws exception during consumer factory creation
     */
    @Test
    public void testAfterPropertiesSetWhenMafkaClientThrowsException() throws Exception {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), anyString())).thenThrow(new RuntimeException("Mafka client error"));
            // act & assert
            Exception exception = assertThrows(RuntimeException.class, () -> {
                consumer.afterPropertiesSet();
            });
            assertEquals("Mafka client error", exception.getMessage());
        }
    }

    /**
     * Test when recvMessageWithParallel throws exception
     */
    @Test
    public void testAfterPropertiesSetWhenRecvMessageThrowsException() throws Exception {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockConsumer = mock(IConsumerProcessor.class);
            doThrow(new RuntimeException("Recv message error")).when(mockConsumer).recvMessageWithParallel(any(), any());
            mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), anyString())).thenReturn(mockConsumer);
            // act & assert
            Exception exception = assertThrows(RuntimeException.class, () -> {
                consumer.afterPropertiesSet();
            });
            assertEquals("Recv message error", exception.getMessage());
        }
    }

    /**
     * Test properties are set correctly
     */
    @Test
    public void testAfterPropertiesSetPropertiesAreCorrect() throws Exception {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockConsumer = mock(IConsumerProcessor.class);
            mockedMafkaClient.when(() -> MafkaClient.buildCommonConsumerFactory(any(Properties.class), anyString())).thenAnswer(invocation -> {
                Properties props = invocation.getArgument(0);
                assertEquals("daozong", props.getProperty(ConsumerConstants.MafkaBGNamespace));
                assertEquals("com.sankuai.medicalcosmetology.scrm.core", props.getProperty(ConsumerConstants.MafkaClientAppkey));
                assertEquals("scrm.group.retail.ai.engine.test.record.message.consumer", props.getProperty(ConsumerConstants.SubscribeGroup));
                return mockConsumer;
            });
            // act
            consumer.afterPropertiesSet();
            // assert - verification happens in the thenAnswer block
        }
    }

    @BeforeEach
    void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        // Get private method via reflection
        recvMessageMethod = GroupRetailAiEngineABTestRecordMessageConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        recvMessageMethod.setAccessible(true);
    }

    @Test
    public void testRecvMessageEmptyBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("");
        MessagetContext context = mock(MessagetContext.class);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void testRecvMessageActionType1() throws Throwable {
        // arrange
        GroupRetailAiEngineABTestRecordMessageDTO dto = new GroupRetailAiEngineABTestRecordMessageDTO();
        dto.setActionType(1);
        dto.setMtUserId(123L);
        dto.setAppId("testApp");
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        MessagetContext context = mock(MessagetContext.class);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(scrmGrowthUserInfoDomainService).updateDBMapInfoByMtUserId(123L, "testApp");
    }

    @Test
    public void testRecvMessageExistingRecord() throws Throwable {
        // arrange
        GroupRetailAiEngineABTestRecordMessageDTO dto = new GroupRetailAiEngineABTestRecordMessageDTO();
        dto.setActionType(2);
        dto.setMtUserId(123L);
        dto.setTestVersion("v1");
        dto.setStatus(1);
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        MessagetContext context = mock(MessagetContext.class);
        List<ScrmAIAgentTestGroupRecordDO> records = new ArrayList<>();
        records.add(new ScrmAIAgentTestGroupRecordDO());
        when(aiAgentTestGroupRecordDOMapper.selectByExample(any(ScrmAIAgentTestGroupRecordDOExample.class))).thenReturn(records);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(aiAgentTestGroupRecordDOMapper).updateByExampleSelective(any(), any());
    }

    @Test
    public void testRecvMessageRedisSetnxFailure() throws Throwable {
        // arrange
        GroupRetailAiEngineABTestRecordMessageDTO dto = new GroupRetailAiEngineABTestRecordMessageDTO();
        dto.setActionType(2);
        dto.setMtUserId(123L);
        dto.setTestVersion("v1");
        dto.setStatus(1);
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        MessagetContext context = mock(MessagetContext.class);
        when(aiAgentTestGroupRecordDOMapper.selectByExample(any(ScrmAIAgentTestGroupRecordDOExample.class))).thenReturn(new ArrayList<>());
        when(redisClient.setnx(any(StoreKey.class), eq(true), eq(60))).thenReturn(false);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(redisClient).setnx(any(StoreKey.class), eq(true), eq(60));
    }

    @Test
    public void testRecvMessageSuccessfulInsert() throws Throwable {
        // arrange
        GroupRetailAiEngineABTestRecordMessageDTO dto = new GroupRetailAiEngineABTestRecordMessageDTO();
        dto.setActionType(2);
        dto.setMtUserId(123L);
        dto.setTestVersion("v1");
        dto.setStatus(1);
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        MessagetContext context = mock(MessagetContext.class);
        when(aiAgentTestGroupRecordDOMapper.selectByExample(any(ScrmAIAgentTestGroupRecordDOExample.class))).thenReturn(new ArrayList<>());
        when(redisClient.setnx(any(StoreKey.class), eq(true), eq(60))).thenReturn(true);
        when(aiAgentTestGroupRecordDOMapper.insertSelective(any())).thenReturn(1);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(aiAgentTestGroupRecordDOMapper).insertSelective(any());
    }

    @Test
    public void testRecvMessageInsertFailure() throws Throwable {
        // arrange
        GroupRetailAiEngineABTestRecordMessageDTO dto = new GroupRetailAiEngineABTestRecordMessageDTO();
        dto.setActionType(2);
        dto.setMtUserId(123L);
        dto.setTestVersion("v1");
        dto.setStatus(1);
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        MessagetContext context = mock(MessagetContext.class);
        when(aiAgentTestGroupRecordDOMapper.selectByExample(any(ScrmAIAgentTestGroupRecordDOExample.class))).thenReturn(new ArrayList<>());
        when(redisClient.setnx(any(StoreKey.class), eq(true), eq(60))).thenReturn(true);
        when(aiAgentTestGroupRecordDOMapper.insertSelective(any())).thenReturn(0);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(aiAgentTestGroupRecordDOMapper).insertSelective(any());
    }

    @Test
    public void testRecvMessageExceptionScenario() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn("invalid json");
        MessagetContext context = mock(MessagetContext.class);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumer, message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }
}
