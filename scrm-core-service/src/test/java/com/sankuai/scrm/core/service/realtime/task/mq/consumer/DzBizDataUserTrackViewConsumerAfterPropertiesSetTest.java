package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DzBizDataUserTrackViewConsumerAfterPropertiesSetTest {

    @InjectMocks
    private DzBizDataUserTrackViewConsumer consumer;

    private MockedStatic<MafkaClient> mafkaClientMock;

    private IConsumerProcessor mockConsumerProcessor;

    @BeforeEach
    void setUp() {
        mockConsumerProcessor = mock(IConsumerProcessor.class);
        mafkaClientMock = mockStatic(MafkaClient.class);
    }

    @AfterEach
    void tearDown() {
        mafkaClientMock.close();
    }

    private void setConsumerField(IConsumerProcessor value) throws Exception {
        Field field = DzBizDataUserTrackViewConsumer.class.getDeclaredField("consumer");
        field.setAccessible(true);
        field.set(consumer, value);
    }

    private IConsumerProcessor getConsumerField() throws Exception {
        Field field = DzBizDataUserTrackViewConsumer.class.getDeclaredField("consumer");
        field.setAccessible(true);
        return (IConsumerProcessor) field.get(consumer);
    }

    /**
     * 测试正常初始化流程
     */
    @Test
    void testAfterPropertiesSet_Success() throws Throwable {
        // Arrange
        mafkaClientMock.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenReturn(mockConsumerProcessor);
        // Act
        consumer.afterPropertiesSet();
        // Assert
        mafkaClientMock.verify(() -> MafkaClient.buildConsumerFactory(any(Properties.class), eq("dz_biz_data_user_track_view")));
        verify(mockConsumerProcessor).recvMessageWithParallel(eq(String.class), any());
        assertNotNull(getConsumerField());
    }

    /**
     * 测试MafkaClient初始化抛出异常的情况
     */
    @Test
    void testAfterPropertiesSet_MafkaClientThrowsException() throws Throwable {
        // Arrange
        mafkaClientMock.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenThrow(new RuntimeException("Mafka client error"));
        // Act & Assert
        assertThrows(RuntimeException.class, () -> consumer.afterPropertiesSet());
        assertNull(getConsumerField());
    }

    /**
     * 测试recvMessageWithParallel抛出异常的情况
     */
    @Test
    void testAfterPropertiesSet_RecvMessageThrowsException() throws Throwable {
        // Arrange
        mafkaClientMock.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenReturn(mockConsumerProcessor);
        doThrow(new RuntimeException("Recv message error")).when(mockConsumerProcessor).recvMessageWithParallel(any(), any());
        // Act & Assert
        assertThrows(RuntimeException.class, () -> consumer.afterPropertiesSet());
        // Consumer should still be set
        assertNotNull(getConsumerField());
    }
}
