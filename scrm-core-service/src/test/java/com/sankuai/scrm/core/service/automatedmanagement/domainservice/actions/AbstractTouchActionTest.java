package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Collections;
import java.util.Date;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractTouchActionTest {

    @InjectMocks
    private AbstractTouchAction abstractTouchAction = mock(AbstractTouchAction.class, CALLS_REAL_METHODS);

    @Mock(lenient = true)
    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    @Mock(lenient = true)
    private ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationNodeDTO currentProcessingNode;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDO existedExecuteLogDO;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOExample executeLogDOExample;

    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_ParticipationRestrictTrue() throws Throwable {
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractTouchAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        assertNotNull(result);
    }

    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_ParticipationRestrictFalse() throws Throwable {
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractTouchAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        assertNotNull(result);
    }

    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_ExistedExecuteLogDONotNull() throws Throwable {
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractTouchAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        assertNotNull(result);
    }
}
