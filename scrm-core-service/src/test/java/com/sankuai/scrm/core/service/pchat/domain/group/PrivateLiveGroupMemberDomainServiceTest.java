package com.sankuai.scrm.core.service.pchat.domain.group;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.pchat.dal.entity.ScrmPersonalWxGroupMemberInfoEntity;
import com.sankuai.scrm.core.service.pchat.dal.example.ScrmPersonalWxGroupMemberInfoEntityExample;
import com.sankuai.scrm.core.service.pchat.dal.mapper.ScrmPersonalWxGroupMemberInfoEntityMapper;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PrivateLiveGroupMemberDomainServiceTest {

    @InjectMocks
    private PrivateLiveGroupMemberDomainService privateLiveGroupMemberDomainService;

    @Mock
    private ScrmPersonalWxGroupMemberInfoEntityMapper memberInfoEntityMapper;

    private String appId;

    private String userSerialNo;

    private List<String> groupIdList;

    @Before
    public void setUp() {
        appId = "appId";
        userSerialNo = "userSerialNo";
        groupIdList = Arrays.asList("groupId1", "groupId2");
    }

    /**
     * Test case when appId, userSerialNoList, or groupIdList is null.
     */
    @Test
    public void testQueryGroupMemberByUserSerialNoAndGroupWithNullInput() throws Throwable {
        // Explicitly casting null to List<String> to resolve ambiguity
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNoAndGroup(null, (List<String>) null, (List<String>) null);
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test case when appId, userSerialNoList, and groupIdList are not null, but there is no matching data in the database.
     */
    @Test
    public void testQueryGroupMemberByUserSerialNoAndGroupWithNoData() throws Throwable {
        when(memberInfoEntityMapper.selectByExample(any(ScrmPersonalWxGroupMemberInfoEntityExample.class))).thenReturn(Collections.emptyList());
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNoAndGroup("appId", Arrays.asList("userSerialNo1", "userSerialNo2"), Arrays.asList("groupId1", "groupId2"));
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test case when appId, userSerialNoList, and groupIdList are not null, and there is matching data in the database.
     */
    @Test
    public void testQueryGroupMemberByUserSerialNoAndGroupWithData() throws Throwable {
        ScrmPersonalWxGroupMemberInfoEntity entity = new ScrmPersonalWxGroupMemberInfoEntity();
        entity.setAppId("appId");
        entity.setUserSerialNo("userSerialNo1");
        entity.setGroupId("groupId1");
        when(memberInfoEntityMapper.selectByExample(any(ScrmPersonalWxGroupMemberInfoEntityExample.class))).thenReturn(Arrays.asList(entity));
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNoAndGroup("appId", Arrays.asList("userSerialNo1"), Arrays.asList("groupId1"));
        assertEquals(1, result.size());
        assertEquals("appId", result.get(0).getAppId());
        assertEquals("userSerialNo1", result.get(0).getUserSerialNo());
        assertEquals("groupId1", result.get(0).getGroupId());
    }

    /**
     * Test case when unionId is null.
     */
    @Test
    public void testQueryGroupMemberRecordByUnionIdUnionIdIsNull() throws Throwable {
        // Arrange
        String appId = "appId";
        String unionId = null;
        // Act
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberRecordByUnionId(appId, unionId);
        // Assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test case when unionId is not null, but appId is null.
     */
    @Test
    public void testQueryGroupMemberRecordByUnionIdAppIdIsNull() throws Throwable {
        // Arrange
        String appId = null;
        String unionId = "unionId";
        // Act
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberRecordByUnionId(appId, unionId);
        // Assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test case when both unionId and appId are not null.
     */
    @Test
    public void testQueryGroupMemberRecordByUnionIdBothNotNull() throws Throwable {
        // Arrange
        String appId = "appId";
        String unionId = "unionId";
        List<ScrmPersonalWxGroupMemberInfoEntity> expected = Collections.singletonList(new ScrmPersonalWxGroupMemberInfoEntity());
        when(memberInfoEntityMapper.selectByExample(any(ScrmPersonalWxGroupMemberInfoEntityExample.class))).thenReturn(expected);
        // Act
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberRecordByUnionId(appId, unionId);
        // Assert
        assertEquals(expected, result);
    }

    /**
     * 测试 unionIdList 为空的情况
     */
    @Test
    public void testQueryGroupMemberByUnionIdEmptyList() throws Throwable {
        // arrange
        List<String> unionIdList = Collections.emptyList();
        // act
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUnionId(unionIdList);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 unionIdList 不为空，但数据库中没有符合条件的记录的情况
     */
    @Test
    public void testQueryGroupMemberByUnionIdNoRecord() throws Throwable {
        // arrange
        List<String> unionIdList = Arrays.asList("unionId1", "unionId2");
        when(memberInfoEntityMapper.selectByExample(any(ScrmPersonalWxGroupMemberInfoEntityExample.class))).thenReturn(Collections.emptyList());
        // act
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUnionId(unionIdList);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 unionIdList 不为空，数据库中有符合条件的记录的情况
     */
    @Test
    public void testQueryGroupMemberByUnionIdWithRecord() throws Throwable {
        // arrange
        List<String> unionIdList = Arrays.asList("unionId1", "unionId2");
        ScrmPersonalWxGroupMemberInfoEntity entity = new ScrmPersonalWxGroupMemberInfoEntity();
        entity.setUnionId("unionId1");
        when(memberInfoEntityMapper.selectByExample(any(ScrmPersonalWxGroupMemberInfoEntityExample.class))).thenReturn(Arrays.asList(entity));
        // act
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUnionId(unionIdList);
        // assert
        assertEquals(1, result.size());
        assertEquals("unionId1", result.get(0).getUnionId());
    }

    /**
     * Test case when appId or userSerialNo is null.
     */
    @Test
    public void testQueryGroupMemberByUserSerialNo_EmptyInput() throws Throwable {
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNo(null, "userSerialNo");
        assertEquals(Collections.emptyList(), result);
        // Explicitly casting null to String to resolve ambiguity
        result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNo("appId", (String) null);
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test case when appId and userSerialNo are not null, but there is no matching data in the database.
     */
    @Test
    public void testQueryGroupMemberByUserSerialNo_NoDataInDB() throws Throwable {
        when(memberInfoEntityMapper.selectByExample(any(ScrmPersonalWxGroupMemberInfoEntityExample.class))).thenReturn(Collections.emptyList());
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNo("appId", "userSerialNo");
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test case when appId and userSerialNo are not null, and there is matching data in the database.
     */
    @Test
    public void testQueryGroupMemberByUserSerialNo_DataInDB() throws Throwable {
        ScrmPersonalWxGroupMemberInfoEntity entity = new ScrmPersonalWxGroupMemberInfoEntity();
        entity.setAppId("appId");
        entity.setUserSerialNo("userSerialNo");
        when(memberInfoEntityMapper.selectByExample(any(ScrmPersonalWxGroupMemberInfoEntityExample.class))).thenReturn(Collections.singletonList(entity));
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNo("appId", "userSerialNo");
        assertEquals(1, result.size());
        assertEquals("appId", result.get(0).getAppId());
        assertEquals("userSerialNo", result.get(0).getUserSerialNo());
    }

    /**
     * Test case when input parameters are empty.
     */
    @Test
    public void testQueryGroupMemberByUnionIdAndGroupEmptyInput() throws Throwable {
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUnionIdAndGroup(null, null, null);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case when input parameters are not empty, but there is no matching data in the database.
     */
    @Test
    public void testQueryGroupMemberByUnionIdAndGroupNoData() throws Throwable {
        when(memberInfoEntityMapper.selectByExample(any(ScrmPersonalWxGroupMemberInfoEntityExample.class))).thenReturn(Collections.emptyList());
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUnionIdAndGroup("appId", "unionId", Collections.singletonList("groupId"));
        assertTrue(result.isEmpty());
    }

    /**
     * Test case when input parameters are not empty, and there is matching data in the database.
     */
    @Test
    public void testQueryGroupMemberByUnionIdAndGroupWithData() throws Throwable {
        ScrmPersonalWxGroupMemberInfoEntity entity = new ScrmPersonalWxGroupMemberInfoEntity();
        entity.setAppId("appId");
        entity.setUnionId("unionId");
        entity.setGroupId("groupId");
        when(memberInfoEntityMapper.selectByExample(any(ScrmPersonalWxGroupMemberInfoEntityExample.class))).thenReturn(Collections.singletonList(entity));
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUnionIdAndGroup("appId", "unionId", Collections.singletonList("groupId"));
        assertTrue(result.size() == 1);
        assertTrue(result.get(0).getAppId().equals("appId"));
        assertTrue(result.get(0).getUnionId().equals("unionId"));
        assertTrue(result.get(0).getGroupId().equals("groupId"));
    }

    /**
     * Test queryGroupMemberByUnionId method when unionId is null
     */
    @Test
    public void testQueryGroupMemberByUnionIdUnionIdIsNull() throws Throwable {
        // Arrange
        String appId = "appId";
        String unionId = null;
        // Act
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUnionId(appId, unionId);
        // Assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test queryGroupMemberByUnionId method when unionId is not null, but appId is null
     */
    @Test
    public void testQueryGroupMemberByUnionIdAppIdIsNull() throws Throwable {
        // Arrange
        String appId = null;
        String unionId = "unionId";
        // Act
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUnionId(appId, unionId);
        // Assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test queryGroupMemberByUnionId method when both unionId and appId are not null
     */
    @Test
    public void testQueryGroupMemberByUnionIdBothNotNull() throws Throwable {
        // Arrange
        String appId = "appId";
        String unionId = "unionId";
        List<ScrmPersonalWxGroupMemberInfoEntity> expected = Collections.singletonList(new ScrmPersonalWxGroupMemberInfoEntity());
        when(memberInfoEntityMapper.selectByExample(any(ScrmPersonalWxGroupMemberInfoEntityExample.class))).thenReturn(expected);
        // Act
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUnionId(appId, unionId);
        // Assert
        assertEquals(expected, result);
    }

    /**
     * 测试 appId，userSerialNo 或 groupIdList 为空的情况
     */
    @Test
    public void testQueryGroupMemberByUserSerialNoAndGroupWithEmptyInput() throws Throwable {
        // Explicitly casting null to String to resolve ambiguity
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNoAndGroup(null, userSerialNo, groupIdList);
        assertEquals(Collections.emptyList(), result);
        result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNoAndGroup(appId, (String) null, groupIdList);
        assertEquals(Collections.emptyList(), result);
        result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNoAndGroup(appId, userSerialNo, null);
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 appId，userSerialNo 和 groupIdList 都不为空，但数据库中没有匹配的数据的情况
     */
    @Test
    public void testQueryGroupMemberByUserSerialNoAndGroupWithNoMatchedData() throws Throwable {
        when(memberInfoEntityMapper.selectByExample(any(ScrmPersonalWxGroupMemberInfoEntityExample.class))).thenReturn(Collections.emptyList());
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNoAndGroup(appId, userSerialNo, groupIdList);
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试 appId，userSerialNo 和 groupIdList 都不为空，数据库中有匹配的数据的情况
     */
    @Test
    public void testQueryGroupMemberByUserSerialNoAndGroupWithMatchedData() throws Throwable {
        ScrmPersonalWxGroupMemberInfoEntity entity = new ScrmPersonalWxGroupMemberInfoEntity();
        entity.setAppId(appId);
        entity.setUserSerialNo(userSerialNo);
        entity.setGroupId(groupIdList.get(0));
        when(memberInfoEntityMapper.selectByExample(any(ScrmPersonalWxGroupMemberInfoEntityExample.class))).thenReturn(Arrays.asList(entity));
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNoAndGroup(appId, userSerialNo, groupIdList);
        assertEquals(1, result.size());
        assertEquals(appId, result.get(0).getAppId());
        assertEquals(userSerialNo, result.get(0).getUserSerialNo());
        assertEquals(groupIdList.get(0), result.get(0).getGroupId());
    }

    @Test
    public void testQueryGroupMemberByUserSerialNo_EmptyAppIdAndUserSerialNoList() throws Throwable {
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNo("", Collections.emptyList());
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryGroupMemberByUserSerialNo_EmptyAppIdAndNonEmptyUserSerialNoList() throws Throwable {
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNo("", Arrays.asList("userSerialNo1", "userSerialNo2"));
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryGroupMemberByUserSerialNo_NonEmptyAppIdAndEmptyUserSerialNoList() throws Throwable {
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNo("appId", Collections.emptyList());
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryGroupMemberByUserSerialNo_NonEmptyAppIdAndNonEmptyUserSerialNoListEmpty() throws Throwable {
        when(memberInfoEntityMapper.selectByExample(any(ScrmPersonalWxGroupMemberInfoEntityExample.class))).thenReturn(Collections.emptyList());
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNo("appId", Arrays.asList("userSerialNo1", "userSerialNo2"));
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryGroupMemberByUserSerialNo_NonEmptyAppIdAndNonEmptyUserSerialNoListNoMatch() throws Throwable {
        when(memberInfoEntityMapper.selectByExample(any(ScrmPersonalWxGroupMemberInfoEntityExample.class))).thenReturn(Collections.emptyList());
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNo("appId", Arrays.asList("userSerialNo1", "userSerialNo2"));
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryGroupMemberByUserSerialNo_NonEmptyAppIdAndNonEmptyUserSerialNoListMatch() throws Throwable {
        ScrmPersonalWxGroupMemberInfoEntity entity = new ScrmPersonalWxGroupMemberInfoEntity();
        entity.setAppId("appId");
        entity.setUserSerialNo("userSerialNo1");
        when(memberInfoEntityMapper.selectByExample(any(ScrmPersonalWxGroupMemberInfoEntityExample.class))).thenReturn(Arrays.asList(entity));
        List<ScrmPersonalWxGroupMemberInfoEntity> result = privateLiveGroupMemberDomainService.queryGroupMemberByUserSerialNo("appId", Arrays.asList("userSerialNo1"));
        assertEquals(1, result.size());
        assertEquals("appId", result.get(0).getAppId());
        assertEquals("userSerialNo1", result.get(0).getUserSerialNo());
    }
}
