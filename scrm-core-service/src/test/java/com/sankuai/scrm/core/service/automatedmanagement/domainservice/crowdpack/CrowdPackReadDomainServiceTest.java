package com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack;

import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.crowdpack.ScrmCrowdPackBaseInfoConverter;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.crowdpack.ScrmCrowdPackDetailInfoConverter;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.crowdpack.ScrmCrowdPackUpdateStrategyConverter;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackBaseInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackDetailInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackUpdateStrategyDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ext.ScrmAmCrowdPackDetailCountDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackBaseInfoDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackDetailInfoDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackUpdateStrategyDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackBaseInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackDetailInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.config.ConfigDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.anyLong;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CrowdPackReadDomainServiceTest {

    @InjectMocks
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock(lenient = true)
    private ExtScrmAmCrowdPackDetailInfoDOMapper extScrmAmCrowdPackDetailInfoDOMapper;

    @Mock(lenient = true)
    private ScrmCrowdPackDetailInfoConverter crowdPackDetailInfoConverter;

    private ScrmAmCrowdPackDetailInfoDO scrmAmCrowdPackDetailInfoDO;

    private ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO;

    @Mock(lenient = true)
    private ExecuteManagementService executeManagementService;

    private String appId = "appId";

    private String groupId = "groupId";

    @Mock(lenient = true)
    private ExtScrmAmCrowdPackBaseInfoDOMapper extScrmAmCrowdPackBaseInfoDOMapper;

    @Mock(lenient = true)
    private ScrmAmCrowdPackUpdateStrategyDOMapper updateStrategyDOMapper;

    @Mock(lenient = true)
    private ScrmCrowdPackBaseInfoConverter packBaseInfoConverter;

    @Mock(lenient = true)
    private ScrmCrowdPackUpdateStrategyConverter updateStrategyConverter;

    @Mock(lenient = true)
    private ConfigDomainService configDomainService;

    private List<Long> crowdPackIds = Arrays.asList(1L, 2L, 3L);

    private List<ScrmAmCrowdPackBaseInfoDO> scrmAmCrowdPackBaseInfoDOList = Arrays.asList(new ScrmAmCrowdPackBaseInfoDO(), new ScrmAmCrowdPackBaseInfoDO(), new ScrmAmCrowdPackBaseInfoDO());

    private List<ScrmCrowdPackDTO> scrmCrowdPackDTOS = Arrays.asList(new ScrmCrowdPackDTO(), new ScrmCrowdPackDTO(), new ScrmCrowdPackDTO());

    private List<ScrmAmCrowdPackDetailInfoDO> scrmAmCrowdPackDetailInfoDOList = Arrays.asList(new ScrmAmCrowdPackDetailInfoDO(), new ScrmAmCrowdPackDetailInfoDO(), new ScrmAmCrowdPackDetailInfoDO());

    private List<ScrmAmCrowdPackDetailCountDO> scrmAmCrowdPackDetailCountDOList = Arrays.asList(new ScrmAmCrowdPackDetailCountDO(), new ScrmAmCrowdPackDetailCountDO(), new ScrmAmCrowdPackDetailCountDO());

    @Mock(lenient = true)
    private ScrmAmCrowdPackBaseInfoDO baseInfoDO;

    @Mock(lenient = true)
    private ScrmAmCrowdPackUpdateStrategyDO strategyDO;

    @Before
    public void setUp() {
        scrmAmCrowdPackDetailInfoDO = new ScrmAmCrowdPackDetailInfoDO();
        scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
    }

    private void setUpCommonMocks() {
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(anyLong())).thenReturn(baseInfoDO);
        when(baseInfoDO.getAppId()).thenReturn("appId");
        when(baseInfoDO.getValidPackVersion()).thenReturn("1.0");
        when(updateStrategyDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(strategyDO));
        when(strategyDO.getFilterFieldId()).thenReturn(1L);
        // Mock the countByExample method
        when(extScrmAmCrowdPackDetailInfoDOMapper.countByExample(any())).thenReturn(1L);
    }

    @Test
    public void testQueryUserListInCrowdPack_StartIdAndPageSizeNull() throws Throwable {
        when(extScrmAmCrowdPackDetailInfoDOMapper.selectByExample(any(ScrmAmCrowdPackDetailInfoDOExample.class))).thenReturn(Collections.singletonList(scrmAmCrowdPackDetailInfoDO));
        when(crowdPackDetailInfoConverter.convertToDTOsSafety(any(List.class))).thenReturn(Collections.singletonList(scrmCrowdPackDetailInfoDTO));
        List<ScrmCrowdPackDetailInfoDTO> result = crowdPackReadDomainService.queryUserListInCrowdPack("appId", 0L, "packVersion", 0L, 0);
        assertEquals(1, result.size());
        assertEquals(scrmCrowdPackDetailInfoDTO, result.get(0));
    }

    @Test
    public void testQueryUserListInCrowdPack_StartIdNullAndPageSizeNotNull() throws Throwable {
        when(extScrmAmCrowdPackDetailInfoDOMapper.selectByExample(any(ScrmAmCrowdPackDetailInfoDOExample.class))).thenReturn(Collections.singletonList(scrmAmCrowdPackDetailInfoDO));
        when(crowdPackDetailInfoConverter.convertToDTOsSafety(any(List.class))).thenReturn(Collections.singletonList(scrmCrowdPackDetailInfoDTO));
        List<ScrmCrowdPackDetailInfoDTO> result = crowdPackReadDomainService.queryUserListInCrowdPack("appId", 0L, "packVersion", 0L, 1000);
        assertEquals(1, result.size());
        assertEquals(scrmCrowdPackDetailInfoDTO, result.get(0));
    }

    @Test
    public void testQueryUserListInCrowdPack_StartIdNotNullAndPageSizeNull() throws Throwable {
        when(extScrmAmCrowdPackDetailInfoDOMapper.selectByExample(any(ScrmAmCrowdPackDetailInfoDOExample.class))).thenReturn(Collections.singletonList(scrmAmCrowdPackDetailInfoDO));
        when(crowdPackDetailInfoConverter.convertToDTOsSafety(any(List.class))).thenReturn(Collections.singletonList(scrmCrowdPackDetailInfoDTO));
        List<ScrmCrowdPackDetailInfoDTO> result = crowdPackReadDomainService.queryUserListInCrowdPack("appId", 0L, "packVersion", 1L, 0);
        assertEquals(1, result.size());
        assertEquals(scrmCrowdPackDetailInfoDTO, result.get(0));
    }

    @Test
    public void testQueryUserListInCrowdPack_StartIdAndPageSizeNotNull() throws Throwable {
        when(extScrmAmCrowdPackDetailInfoDOMapper.selectByExample(any(ScrmAmCrowdPackDetailInfoDOExample.class))).thenReturn(Collections.singletonList(scrmAmCrowdPackDetailInfoDO));
        when(crowdPackDetailInfoConverter.convertToDTOsSafety(any(List.class))).thenReturn(Collections.singletonList(scrmCrowdPackDetailInfoDTO));
        List<ScrmCrowdPackDetailInfoDTO> result = crowdPackReadDomainService.queryUserListInCrowdPack("appId", 0L, "packVersion", 1L, 1000);
        assertEquals(1, result.size());
        assertEquals(scrmCrowdPackDetailInfoDTO, result.get(0));
    }

    @Test
    public void testQueryUserListInCrowdPack_PackIdZero() throws Throwable {
        when(extScrmAmCrowdPackDetailInfoDOMapper.selectByExample(any(ScrmAmCrowdPackDetailInfoDOExample.class))).thenReturn(Collections.singletonList(scrmAmCrowdPackDetailInfoDO));
        when(crowdPackDetailInfoConverter.convertToDTOsSafety(any(List.class))).thenReturn(Collections.singletonList(scrmCrowdPackDetailInfoDTO));
        List<ScrmCrowdPackDetailInfoDTO> result = crowdPackReadDomainService.queryUserListInCrowdPack("appId", 0L, "packVersion", 1L, 1000);
        assertEquals(1, result.size());
        assertEquals(scrmCrowdPackDetailInfoDTO, result.get(0));
    }

    @Test
    public void testQueryUserListInCrowdPack_PackIdNotZero() throws Throwable {
        when(extScrmAmCrowdPackDetailInfoDOMapper.selectByExample(any(ScrmAmCrowdPackDetailInfoDOExample.class))).thenReturn(Collections.singletonList(scrmAmCrowdPackDetailInfoDO));
        when(crowdPackDetailInfoConverter.convertToDTOsSafety(any(List.class))).thenReturn(Collections.singletonList(scrmCrowdPackDetailInfoDTO));
        List<ScrmCrowdPackDetailInfoDTO> result = crowdPackReadDomainService.queryUserListInCrowdPack("appId", 1L, "packVersion", 1L, 1000);
        assertEquals(1, result.size());
        assertEquals(scrmCrowdPackDetailInfoDTO, result.get(0));
    }

    @Test
    public void testQueryCrowdInCrowdPackCountEmptyList() throws Throwable {
        List<ScrmCrowdPackDTO> crowdPackDTOS = Arrays.asList();
        Map<ScrmCrowdPackDTO, Long> result = crowdPackReadDomainService.queryCrowdInCrowdPackCount(crowdPackDTOS);
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryCrowdInCrowdPackCountZeroCount() throws Throwable {
        ScrmCrowdPackDTO crowdPackDTO = new ScrmCrowdPackDTO();
        crowdPackDTO.setId(1L);
        List<ScrmCrowdPackDTO> crowdPackDTOS = Arrays.asList(crowdPackDTO);
        when(extScrmAmCrowdPackDetailInfoDOMapper.countByExample(any(ScrmAmCrowdPackDetailInfoDOExample.class))).thenReturn(0L);
        Map<ScrmCrowdPackDTO, Long> result = crowdPackReadDomainService.queryCrowdInCrowdPackCount(crowdPackDTOS);
        assertEquals(1, result.size());
        assertEquals(Long.valueOf(0), result.get(crowdPackDTO));
    }

    @Test
    public void testQueryCrowdInCrowdPackCountNonZeroCount() throws Throwable {
        ScrmCrowdPackDTO crowdPackDTO = new ScrmCrowdPackDTO();
        crowdPackDTO.setId(1L);
        List<ScrmCrowdPackDTO> crowdPackDTOS = Arrays.asList(crowdPackDTO);
        when(extScrmAmCrowdPackDetailInfoDOMapper.countByExample(any(ScrmAmCrowdPackDetailInfoDOExample.class))).thenReturn(10L);
        Map<ScrmCrowdPackDTO, Long> result = crowdPackReadDomainService.queryCrowdInCrowdPackCount(crowdPackDTOS);
        assertEquals(1, result.size());
        assertEquals(Long.valueOf(10), result.get(crowdPackDTO));
    }

    @Test
    public void testQueryUserListByGroupId_Normal() throws Throwable {
        List<MemberInfoEntity> memberInfoEntities = Arrays.asList(new MemberInfoEntity(), new MemberInfoEntity());
        List<ScrmAmCrowdPackDetailInfoDO> scrmAmCrowdPackDetailInfoDOs = Arrays.asList(new ScrmAmCrowdPackDetailInfoDO(), new ScrmAmCrowdPackDetailInfoDO());
        List<ScrmCrowdPackDetailInfoDTO> expectedDTOs = Arrays.asList(new ScrmCrowdPackDetailInfoDTO(), new ScrmCrowdPackDetailInfoDTO());
        when(executeManagementService.getUserListByGroupId(groupId, appId)).thenReturn(memberInfoEntities);
        when(extScrmAmCrowdPackDetailInfoDOMapper.selectByExample(any(ScrmAmCrowdPackDetailInfoDOExample.class))).thenReturn(scrmAmCrowdPackDetailInfoDOs);
        // Corrected mock setup to return a list of DTOs
        when(crowdPackDetailInfoConverter.convertToDTOsSafety(scrmAmCrowdPackDetailInfoDOs)).thenReturn(expectedDTOs);
        List<ScrmCrowdPackDetailInfoDTO> result = crowdPackReadDomainService.queryUserListByGroupId(appId, groupId);
        assertEquals(expectedDTOs.size(), result.size());
    }

    @Test
    public void testQueryUserListByGroupId_EmptyMemberInfoEntities() throws Throwable {
        when(executeManagementService.getUserListByGroupId(groupId, appId)).thenReturn(Collections.emptyList());
        List<ScrmCrowdPackDetailInfoDTO> result = crowdPackReadDomainService.queryUserListByGroupId(appId, groupId);
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryUserListByGroupId_EmptyScrmAmCrowdPackDetailInfoDOs() throws Throwable {
        List<MemberInfoEntity> memberInfoEntities = Arrays.asList(new MemberInfoEntity(), new MemberInfoEntity());
        when(executeManagementService.getUserListByGroupId(groupId, appId)).thenReturn(memberInfoEntities);
        when(extScrmAmCrowdPackDetailInfoDOMapper.selectByExample(any(ScrmAmCrowdPackDetailInfoDOExample.class))).thenReturn(Collections.emptyList());
        // Corrected mock setup to return an empty list of DTOs
        when(crowdPackDetailInfoConverter.convertToDTOsSafety(Collections.emptyList())).thenReturn(Collections.emptyList());
        List<ScrmCrowdPackDetailInfoDTO> result = crowdPackReadDomainService.queryUserListByGroupId(appId, groupId);
        assertEquals(0, result.size());
    }

    // Removed @Before annotation and moved setup into each test method where necessary
    @Test
    public void testQueryCrowdPackListByPackIdsWhenCrowdPackIdsIsEmpty() throws Throwable {
        List<ScrmCrowdPackDTO> result = crowdPackReadDomainService.queryCrowdPackListByPackIds(Collections.emptyList());
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryCrowdPackListByPackIdsWhenSelectByExampleReturnEmpty() throws Throwable {
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        List<ScrmCrowdPackDTO> result = crowdPackReadDomainService.queryCrowdPackListByPackIds(crowdPackIds);
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryCrowdPackListByPackIdsWhenSelectByExampleReturnNotEmptyButDetailReturnEmpty() throws Throwable {
        // Setup
        for (int i = 0; i < scrmCrowdPackDTOS.size(); i++) {
            ScrmCrowdPackDTO dto = scrmCrowdPackDTOS.get(i);
            dto.setId(crowdPackIds.get(i));
            // Ensure validPackVersion is set
            dto.setValidPackVersion("1.0");
        }
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByExample(any())).thenReturn(scrmAmCrowdPackBaseInfoDOList);
        when(packBaseInfoConverter.convertToDTOsSafety(scrmAmCrowdPackBaseInfoDOList)).thenReturn(scrmCrowdPackDTOS);
        List<ScrmCrowdPackDTO> result = crowdPackReadDomainService.queryCrowdPackListByPackIds(crowdPackIds);
        assertEquals(3, result.size());
    }

    @Test
    public void testQueryCrowdPackListByPackIdsWhenSelectByExampleAndDetailReturnNotEmpty() throws Throwable {
        // Setup
        for (int i = 0; i < scrmCrowdPackDTOS.size(); i++) {
            ScrmCrowdPackDTO dto = scrmCrowdPackDTOS.get(i);
            dto.setId(crowdPackIds.get(i));
            // Ensure validPackVersion is set
            dto.setValidPackVersion("1.0");
        }
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByExample(any())).thenReturn(scrmAmCrowdPackBaseInfoDOList);
        when(packBaseInfoConverter.convertToDTOsSafety(scrmAmCrowdPackBaseInfoDOList)).thenReturn(scrmCrowdPackDTOS);
        List<ScrmCrowdPackDTO> result = crowdPackReadDomainService.queryCrowdPackListByPackIds(crowdPackIds);
        assertEquals(3, result.size());
    }

    @Test
    public void testQueryCrowdPackDetailInfoPackIdIsNull() throws Throwable {
        ScrmCrowdPackDTO result = crowdPackReadDomainService.queryCrowdPackDetailInfo(null, "appId");
        assertNull(result);
    }

    @Test
    public void testQueryCrowdPackDetailInfoNoRecordInDB() throws Throwable {
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(anyLong())).thenReturn(null);
        ScrmCrowdPackDTO result = crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId");
        assertNull(result);
    }

    @Test
    public void testQueryCrowdPackDetailInfoAppIdNotEqual() throws Throwable {
        ScrmCrowdPackDTO result = crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId");
        assertNull(result);
    }

    @Test
    public void testQueryCrowdPackDetailInfoNoStrategyRecordInDB() throws Throwable {
        ScrmCrowdPackDTO result = crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId");
        assertNull(result);
    }

    @Test
    public void testQueryCrowdPackDetailInfoFilterFieldIdNotEqualStaffFriendsTagId() throws Throwable {
        setUpCommonMocks();
        ScrmCrowdPackDTO mockScrmCrowdPackDTO = new ScrmCrowdPackDTO();
        mockScrmCrowdPackDTO.setId(1L);
        mockScrmCrowdPackDTO.setValidPackVersion("1.0");
        when(packBaseInfoConverter.convertToDTO(baseInfoDO)).thenReturn(mockScrmCrowdPackDTO);
        ScrmCrowdPackDTO result = crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId");
        assertNotNull(result);
    }

    @Test
    public void testQueryCrowdPackDetailInfoFilterFieldIdEqualStaffFriendsTagId() throws Throwable {
        setUpCommonMocks();
        ScrmCrowdPackDTO mockScrmCrowdPackDTO = new ScrmCrowdPackDTO();
        mockScrmCrowdPackDTO.setId(1L);
        mockScrmCrowdPackDTO.setValidPackVersion("1.0");
        when(packBaseInfoConverter.convertToDTO(baseInfoDO)).thenReturn(mockScrmCrowdPackDTO);
        ScrmCrowdPackDTO result = crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId");
        assertNotNull(result);
    }

    @Test
    public void testQueryCrowdPackCountWithNoSpecificType() {
        when(extScrmAmCrowdPackBaseInfoDOMapper.countByExample(any(ScrmAmCrowdPackBaseInfoDOExample.class))).thenReturn(10L);
        ScrmCrowdPackDTO request = new ScrmCrowdPackDTO();
        request.setAppId("hanchengfuwu");
        request.setType(null);
        long result = crowdPackReadDomainService.queryCrowdPackCount(request);
        assertEquals(10L, result);
    }
}
