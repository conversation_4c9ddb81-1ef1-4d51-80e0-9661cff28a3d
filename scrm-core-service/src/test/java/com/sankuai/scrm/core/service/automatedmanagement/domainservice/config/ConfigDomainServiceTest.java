package com.sankuai.scrm.core.service.automatedmanagement.domainservice.config;

import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mdp.boot.starter.mdpcache.core.Cache;
import com.meituan.service.mobile.group.geo.bean.CityInfo;
import com.meituan.service.mobile.group.geo.service.CityService;
import com.sankuai.dz.srcm.automatedmanagement.dto.config.CityInfosDTO;
import com.sankuai.scrm.core.service.BaseMockTest;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.when;

public class ConfigDomainServiceTest extends BaseMockTest {

    @Mock(lenient = true)
    private Cache cityLocalCache;

    @Mock(lenient = true)
    private CityService cityService;

    @InjectMocks
    private ConfigDomainService configDomainService;

    private Map<Integer, CityInfosDTO> cityInfosDTOMap;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
        configDomainService = new ConfigDomainService();
        ReflectionTestUtils.setField(configDomainService, "cityLocalCache", cityLocalCache);
        // Initialize the cityInfosDTOMap with mock data
        cityInfosDTOMap = new HashMap<>();
        CityInfosDTO city1 = new CityInfosDTO();
        city1.setId(1);
        city1.setName("北京");
        city1.setPinyinName("Beijing");
        cityInfosDTOMap.put(1, city1);
        CityInfosDTO city2 = new CityInfosDTO();
        city2.setId(2);
        city2.setName("上海");
        city2.setPinyinName("Shanghai");
        cityInfosDTOMap.put(2, city2);
        // Mock the cityLocalCache.get() method
        when(cityLocalCache.get(eq("all"), any(TypeReference.class))).thenReturn(cityInfosDTOMap);
    }

    @Test
    public void testGetCityInfosWithEmptySearchKeyAndParentCity() throws Throwable {
        String searchKey = "";
        List<CityInfosDTO> result = configDomainService.getCityInfos(searchKey);
        assertEquals(2, result.size());
    }

    @Test
    public void testGetCityInfosWithEmptySearchKeyAndNoParentCity() throws Throwable {
        String searchKey = "";
        List<CityInfosDTO> result = configDomainService.getCityInfos(searchKey);
        assertEquals(2, result.size());
    }

    @Test
    public void testGetCityInfosWithNonEmptySearchKeyAndCityMatched() throws Throwable {
        String searchKey = "上";
        List<CityInfosDTO> result = configDomainService.getCityInfos(searchKey);
        assertEquals(1, result.size());
        assertEquals("上海", result.get(0).getName());
    }

    @Test
    public void testGetCityInfosWithNonEmptySearchKeyAndNoCityMatched() throws Throwable {
        String searchKey = "广州";
        List<CityInfosDTO> result = configDomainService.getCityInfos(searchKey);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetAllCities() throws Throwable {
        List<CityInfo> cityInfos = new ArrayList<>();
        CityInfo cityInfo1 = new CityInfo();
        cityInfo1.setId(1);
        cityInfo1.setName("北京");
        cityInfo1.setPinyin("beijing");
        cityInfo1.setOriginCityID(0);
        CityInfo cityInfo2 = new CityInfo();
        cityInfo2.setId(2);
        cityInfo2.setName("上海");
        cityInfo2.setPinyin("shanghai");
        cityInfo2.setOriginCityID(0);

        cityInfos.add(cityInfo1);
        cityInfos.add(cityInfo2);

        Mockito.when(cityService.listAllCities()).thenReturn(cityInfos);
        ReflectionTestUtils.setField(configDomainService, "cityService", cityService);

        Method testMethod = configDomainService.getClass().getDeclaredMethod("getAllCities", String.class);
        testMethod.setAccessible(true);
        
        Map<Integer, CityInfosDTO> result = (Map<Integer, CityInfosDTO>)testMethod.invoke(configDomainService, "all");
        assertEquals(2, result.size());
    }
}
