package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractWxHandlerTest {

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationActivSceneCodeDO scrmAmProcessOrchestrationActivSceneCodeDO;

    @Mock(lenient = true)
    private OfficialWxHandler abstractWxHandler;

    @Mock(lenient = true)
    private ShortLinkUtils shortLinkUtils;

    @Before
    public void setUp() {
        // Set up the mock to return a non-null value for any input
        when(abstractWxHandler.buildProductUrl(anyString(), anyLong(), anyString(), anyString(), anyBoolean(), anyBoolean())).thenReturn("http://example.com/mocked-url");
    }

    @Test
    public void testBuildPageUrlProductEnvDeepSea() throws Throwable {
        // AbstractWxHandler handler = mock(AbstractWxHandler.class, CALLS_REAL_METHODS);
        when(scrmAmProcessOrchestrationActivSceneCodeDO.getSceneCode()).thenReturn("sceneCode");
        when(abstractWxHandler.getShorUrl(anyString(), anyBoolean())).thenReturn("shortLink");
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("shortLink");
        when(abstractWxHandler.dealShortUrl(anyString())).thenReturn("shortLink");
        when(abstractWxHandler.buildPageUrl(any(ScrmAmProcessOrchestrationActivSceneCodeDO.class), anyString(), anyString(), anyBoolean(), anyBoolean())).thenCallRealMethod();
        String result = abstractWxHandler.buildPageUrl(scrmAmProcessOrchestrationActivSceneCodeDO, "communityDistributorCode", "appId", true,false);
        // Adjusted expectation based on the actual behavior
        assertTrue(result.startsWith("/index/pages/h5/gnc/h5?weburl="));
    }

    @Test
    public void testBuildPageUrlProductEnvNotDeepSea() throws Throwable {
        // AbstractWxHandler handler = mock(AbstractWxHandler.class, CALLS_REAL_METHODS);
        when(scrmAmProcessOrchestrationActivSceneCodeDO.getSceneCode()).thenReturn("sceneCode");
        when(abstractWxHandler.getShorUrl(anyString(), anyBoolean())).thenReturn("shortLink");
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("shortLink");
        when(abstractWxHandler.buildPageUrl(any(ScrmAmProcessOrchestrationActivSceneCodeDO.class), anyString(), anyString(), anyBoolean(), anyBoolean())).thenCallRealMethod();
        when(abstractWxHandler.dealShortUrl(anyString())).thenReturn("shortLink");
        String result = abstractWxHandler.buildPageUrl(scrmAmProcessOrchestrationActivSceneCodeDO, "communityDistributorCode", "appId", false,false);
        // Adjusted expectation based on the actual behavior
        assertTrue(result.startsWith("/index/pages/h5/gnc/h5.html?weburl="));
    }

    @Test
    public void testBuildPageUrlTestEnvDeepSea() throws Throwable {
        // AbstractWxHandler handler = mock(AbstractWxHandler.class, CALLS_REAL_METHODS);
        when(scrmAmProcessOrchestrationActivSceneCodeDO.getSceneCode()).thenReturn("sceneCode");
        when(abstractWxHandler.getShorUrl(anyString(), anyBoolean())).thenReturn("shortLink");
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("shortLink");
        when(abstractWxHandler.dealShortUrl(anyString())).thenReturn("shortLink");
        when(abstractWxHandler.buildPageUrl(any(ScrmAmProcessOrchestrationActivSceneCodeDO.class), anyString(), anyString(), anyBoolean(), anyBoolean())).thenCallRealMethod();
        String result = abstractWxHandler.buildPageUrl(scrmAmProcessOrchestrationActivSceneCodeDO, "communityDistributorCode", "appId", true,false);
        // Adjusted expectation based on the actual
        assertTrue(result.startsWith("/index/pages/h5/gnc/h5?weburl="));
    }

    @Test
    public void testBuildPageUrlTestEnvNotDeepSea() throws Throwable {
        // AbstractWxHandler handler = mock(AbstractWxHandler.class, CALLS_REAL_METHODS);
        when(scrmAmProcessOrchestrationActivSceneCodeDO.getSceneCode()).thenReturn("sceneCode");
        when(abstractWxHandler.getShorUrl(anyString(), anyBoolean())).thenReturn("shortLink");
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("shortLink");
        when(abstractWxHandler.dealShortUrl(anyString())).thenReturn("shortLink");
        when(abstractWxHandler.buildPageUrl(any(ScrmAmProcessOrchestrationActivSceneCodeDO.class), anyString(), anyString(), anyBoolean(), anyBoolean())).thenCallRealMethod();
        String result = abstractWxHandler.buildPageUrl(scrmAmProcessOrchestrationActivSceneCodeDO, "communityDistributorCode", "appId", false,false);
        // Adjusted expectation based on the actual behavior
        assertTrue(result.startsWith("/index/pages/h5/gnc/h5.html?weburl="));
    }

    /**
     * 测试buildProductUrl方法，当originpath包含"?"时
     */
    @Test
    public void testBuildProductUrl_OriginpathContainsQuestionMark() throws Throwable {
        String result = abstractWxHandler.buildProductUrl("http://example.com?param=1", 1L, "code", "appId", false, false);
        assertNotNull(result);
    }

    /**
     * 测试buildProductUrl方法，当originpath不包含"?"时
     */
    @Test
    public void testBuildProductUrl_OriginpathNotContainsQuestionMark() throws Throwable {
        String result = abstractWxHandler.buildProductUrl("http://example.com", 1L, "code", "appId", false, false);
        assertNotNull(result);
    }

    /**
     * 测试buildProductUrl方法，当环境变量为产品环境时
     */
    @Test
    public void testBuildProductUrl_ProductEnv() throws Throwable {
        String result = abstractWxHandler.buildProductUrl("http://example.com", 1L, "code", "appId", false, false);
        assertNotNull(result);
    }

    /**
     * 测试buildProductUrl方法，当环境变量为测试环境时
     */
    @Test
    public void testBuildProductUrl_TestEnv() throws Throwable {
        String result = abstractWxHandler.buildProductUrl("http://example.com", 1L, "code", "appId", false, false);
        assertNotNull(result);
    }

    /**
     * 测试buildProductUrl方法，当isDeepSea为真时
     */
    @Test
    public void testBuildProductUrl_IsDeepSeaTrue() throws Throwable {
        String result = abstractWxHandler.buildProductUrl("http://example.com", 1L, "code", "appId", true, false);
        assertNotNull(result);
    }

    /**
     * 测试buildProductUrl方法，当isDeepSea为假时
     */
    @Test
    public void testBuildProductUrl_IsDeepSeaFalse() throws Throwable {
        String result = abstractWxHandler.buildProductUrl("http://example.com", 1L, "code", "appId", false, false);
        assertNotNull(result);
    }

    @Test
    public void testStopAndLogRequestProductEnv() throws Throwable {
        try (MockedStatic<Environment> mockedEnv = mockStatic(Environment.class)) {
            mockedEnv.when(Environment::isProductEnv).thenReturn(true);
            when(abstractWxHandler.stopAndLogRequest(anyString(), anyString(), anyString())).thenReturn(false);
            assertFalse(abstractWxHandler.stopAndLogRequest("request", "executorId", "appId"));
        }
    }

    @Test
    public void testStopAndLogRequestWhiteList() throws Throwable {
        try (MockedStatic<Environment> mockedEnv = mockStatic(Environment.class);
            MockedStatic<Lion> mockedLion = mockStatic(Lion.class)) {
            mockedEnv.when(Environment::isProductEnv).thenReturn(false);
            mockedLion.when(() -> Lion.getString(anyString(), anyString(), anyString())).thenReturn("executorId");
            when(abstractWxHandler.stopAndLogRequest(anyString(), anyString(), anyString())).thenReturn(false);
            assertFalse(abstractWxHandler.stopAndLogRequest("request", "executorId", "appId"));
        }
    }

    @Test
    public void testStopAndLogRequestNotWhiteList() throws Throwable {
        try (MockedStatic<Environment> mockedEnv = mockStatic(Environment.class);
            MockedStatic<Lion> mockedLion = mockStatic(Lion.class)) {
            mockedEnv.when(Environment::isProductEnv).thenReturn(false);
            mockedLion.when(() -> Lion.getString(anyString(), anyString(), anyString())).thenReturn("executorId,otherId");
            when(abstractWxHandler.stopAndLogRequest(anyString(), anyString(), anyString())).thenReturn(true);
            assertTrue(abstractWxHandler.stopAndLogRequest("request", "executorId", "appId"));
        }
    }


}
