package com.sankuai.scrm.core.service.infrastructure.acl.ds;

import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.service.fe.corp.wx.thrift.AssisantInfoModel;
import com.sankuai.service.fe.corp.wx.thrift.CorpWxService;
import com.sankuai.service.fe.corp.wx.thrift.GetCorpAssistantRequest;
import com.sankuai.service.fe.corp.wx.thrift.GetCorpAssistantResponse;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DsAssistantAclGetAllAssistantListTest {

    @InjectMocks
    private DsAssistantAcl dsAssistantAcl;

    @Mock
    private CorpWxService.Iface corpWxService;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    private String corpId;

    private long orgId;

    private String appId;

    private CorpAppConfig config;

    @Before
    public void setUp() {
        corpId = "testCorpId";
        orgId = 123L;
    }

    @Test
    public void testGetAllAssistantListCorpIdIsNull() throws Throwable {
        List<AssisantInfoModel> result = dsAssistantAcl.getAllAssistantList(null, orgId);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetAllAssistantListResponseIsNull() throws Throwable {
        when(corpWxService.getCorpAssistant(any(GetCorpAssistantRequest.class))).thenReturn(null);
        List<AssisantInfoModel> result = dsAssistantAcl.getAllAssistantList(corpId, orgId);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetAllAssistantListResponseCodeIsNotZero() throws Throwable {
        GetCorpAssistantResponse response = new GetCorpAssistantResponse();
        response.setCode(1);
        when(corpWxService.getCorpAssistant(any(GetCorpAssistantRequest.class))).thenReturn(response).thenReturn(null);
        List<AssisantInfoModel> result = dsAssistantAcl.getAllAssistantList(corpId, orgId);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetAllAssistantListResponseAssistantsIsEmpty() throws Throwable {
        GetCorpAssistantResponse response = new GetCorpAssistantResponse();
        response.setCode(0);
        response.setAssistants(Collections.emptyList());
        when(corpWxService.getCorpAssistant(any(GetCorpAssistantRequest.class))).thenReturn(response).thenReturn(null);
        List<AssisantInfoModel> result = dsAssistantAcl.getAllAssistantList(corpId, orgId);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testGetAllAssistantListResponseIsValid() throws Throwable {
        GetCorpAssistantResponse response = new GetCorpAssistantResponse();
        response.setCode(0);
        response.setAssistants(Collections.singletonList(new AssisantInfoModel()));
        when(corpWxService.getCorpAssistant(any(GetCorpAssistantRequest.class))).thenReturn(response).thenReturn(null);
        List<AssisantInfoModel> result = dsAssistantAcl.getAllAssistantList(corpId, orgId);
        assertEquals(1, result.size());
    }

    @Test
    public void testGetAllAssistantListThrowsTException() throws Throwable {
        when(corpWxService.getCorpAssistant(any(GetCorpAssistantRequest.class))).thenThrow(TException.class);
        List<AssisantInfoModel> result = dsAssistantAcl.getAllAssistantList(corpId, orgId);
        assertEquals(Collections.emptyList(), result);
    }

    // Removed @Before annotation and moved initialization to each test method where necessary
    @Test
    public void testGetAllAssistantListAppIdIsNull() throws Throwable {
        // arrange
        appId = null;
        // act
        List<AssisantInfoModel> result = dsAssistantAcl.getAllAssistantList(appId);
        // assert
        assertEquals(0, result.size());
    }

    @Test
    public void testGetAllAssistantListConfigIsNull() throws Throwable {
        // arrange
        appId = "testAppId";
        when(appConfigRepository.getConfigByAppId(appId)).thenReturn(null);
        // act
        List<AssisantInfoModel> result = dsAssistantAcl.getAllAssistantList(appId);
        // assert
        assertEquals(0, result.size());
    }

    @Test
    public void testGetAllAssistantListConfigIsNotNull() throws Throwable {
        // arrange
        appId = "testAppId";
        config = new CorpAppConfig();
        config.setAppId(appId);
        config.setCorpId("testCorpId");
        config.setOrgId(123L);
        when(appConfigRepository.getConfigByAppId(appId)).thenReturn(config);
        GetCorpAssistantResponse response = new GetCorpAssistantResponse();
        response.setCode(0);
        List<AssisantInfoModel> assistantInfos = new ArrayList<>();
        AssisantInfoModel assistantInfo = new AssisantInfoModel();
        assistantInfo.setAssistantId(1L);
        // Assuming ONLINE is represented by 1, using integer directly
        assistantInfo.setStatus(1);
        // Assuming PC is represented by 1, using integer directly
        // Assuming this is the correct way to set the device type
        assistantInfo.setDeviceType(1);
        assistantInfos.add(assistantInfo);
        response.setAssistants(assistantInfos);
        when(corpWxService.getCorpAssistant(any(GetCorpAssistantRequest.class))).thenReturn(response).thenReturn(null);
        // act
        List<AssisantInfoModel> result = dsAssistantAcl.getAllAssistantList(appId);
        // assert
        assertEquals(1, result.size());
    }
}
