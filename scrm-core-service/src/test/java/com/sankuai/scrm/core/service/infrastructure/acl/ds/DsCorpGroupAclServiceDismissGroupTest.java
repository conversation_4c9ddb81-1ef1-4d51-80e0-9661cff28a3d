package com.sankuai.scrm.core.service.infrastructure.acl.ds;

import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsCorpGroupAclService;
import com.sankuai.service.fe.corp.wx.thrift.CorpWxService;
import com.sankuai.service.fe.corp.wx.thrift.DismissChatRequest;
import com.sankuai.service.fe.corp.wx.thrift.DismissChatReseponse;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DsCorpGroupAclServiceDismissGroupTest {

    @InjectMocks
    private DsCorpGroupAclService dsCorpGroupAclService;

    @Mock
    private CorpWxService.Iface corpWxService;

    @Before
    public void setUp() throws Exception {
        when(corpWxService.dismissGroupChat(any(DismissChatRequest.class))).thenReturn(new DismissChatReseponse());
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testDismissGroupNormal() throws Throwable {
        // arrange
        String corpId = "corpId";
        long orgId = 1L;
        String groupId = "groupId";
        String groupName = "groupName";
        // act
        boolean result = dsCorpGroupAclService.dismissGroup(corpId, orgId, groupId, groupName);
        // assert
        assertTrue(result);
    }

    /**
     * 测试异常情况1：corpId为空
     */
    @Test(expected = IllegalArgumentException.class)
    public void testDismissGroupCorpIdEmpty() throws Throwable {
        // arrange
        String corpId = "";
        long orgId = 1L;
        String groupId = "groupId";
        String groupName = "groupName";
        // act
        dsCorpGroupAclService.dismissGroup(corpId, orgId, groupId, groupName);
    }

    /**
     * 测试异常情况2：groupId为空
     */
    @Test(expected = IllegalArgumentException.class)
    public void testDismissGroupGroupIdEmpty() throws Throwable {
        // arrange
        String corpId = "corpId";
        long orgId = 1L;
        String groupId = "";
        String groupName = "groupName";
        // act
        dsCorpGroupAclService.dismissGroup(corpId, orgId, groupId, groupName);
    }

    /**
     * 测试异常情况3：groupName为空
     */
    @Test(expected = IllegalArgumentException.class)
    public void testDismissGroupGroupNameEmpty() throws Throwable {
        // arrange
        String corpId = "corpId";
        long orgId = 1L;
        String groupId = "groupId";
        String groupName = "";
        // act
        dsCorpGroupAclService.dismissGroup(corpId, orgId, groupId, groupName);
    }

    /**
     * 测试异常情况4：dismissGroupChat方法抛出异常
     */
    @Test
    public void testDismissGroupException() throws Throwable {
        // arrange
        String corpId = "corpId";
        long orgId = 1L;
        String groupId = "groupId";
        String groupName = "groupName";
        when(corpWxService.dismissGroupChat(any(DismissChatRequest.class))).thenThrow(TException.class);
        // act
        boolean result = dsCorpGroupAclService.dismissGroup(corpId, orgId, groupId, groupName);
        // assert
        assertFalse(result);
    }
}
