package com.sankuai.scrm.core.service.infrastructure.mq.handler;

import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class AbstractCorpExEventHandlerTest {

    @Mock
    private AbstractCorpExEventHandler handler;

    @Test
    public void testSupportDataMapIsNull() throws Throwable {
        assertFalse(handler.support(null));
    }

    @Test
    public void testSupportEventOrChangeTypeIsNull() throws Throwable {
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("Event", "event");
        assertFalse(handler.support(dataMap));
    }

    @Test
    public void testSupportSupportEventOrChangeTypeIsEmpty() throws Throwable {
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("Event", "event");
        dataMap.put("ChangeType", "changeType");
        assertFalse(handler.support(dataMap));
    }

    @Test
    public void testSupportEventNotInSupportEvent() throws Throwable {
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("Event", "event");
        dataMap.put("ChangeType", "changeType");
        assertFalse(handler.support(dataMap));
    }

    @Test
    public void testSupportChangeTypeNotInSupportChangeType() throws Throwable {
        Map<String, String> dataMap = new HashMap<>();
        dataMap.put("Event", "event");
        dataMap.put("ChangeType", "changeType");
        assertFalse(handler.support(dataMap));
    }
}
