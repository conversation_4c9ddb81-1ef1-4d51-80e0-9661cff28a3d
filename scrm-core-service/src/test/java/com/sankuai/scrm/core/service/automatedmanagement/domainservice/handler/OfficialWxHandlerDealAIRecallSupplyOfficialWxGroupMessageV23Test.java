package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.GroupSendStrategy;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerDealAIRecallSupplyOfficialWxGroupMessageV23Test {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private MsgUnifiedPushService msgUnifiedPushService;

    @Mock
    private GroupSendStrategy groupSendStrategy;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private List<String> executorIds;

    private InvokeDetailKeyObject keyObject;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setAppId("test_app_id");
        processOrchestrationDTO.setValidVersion("1.0");
        executorIds = Arrays.asList("executor1", "executor2");
        // Correctly initialize InvokeDetailKeyObject with required parameters
        keyObject = new // executorId
        // targetIdType
        // contentType
        // processOrchestrationNodeId
        InvokeDetailKeyObject("test_executor_id", (byte) 1, (byte) 1, 1L);
        totalInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setProcessOrchestrationNodeId(1L);
        detailDO.setGroupId("group1");
        totalInvokeDetailDOS.add(detailDO);
    }

    /**
     * Test case for empty invoke detail list
     */
    @Test
    public void testDealAIRecallSupplyOfficialWxGroupMessageV2_EmptyInvokeDetails() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> emptyList = Collections.emptyList();
        // act
        officialWxHandler.dealAIRecallSupplyOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, keyObject, emptyList, null);
        // assert
        verify(msgUnifiedPushService, never()).saveMsgPushTask(any());
    }
}
