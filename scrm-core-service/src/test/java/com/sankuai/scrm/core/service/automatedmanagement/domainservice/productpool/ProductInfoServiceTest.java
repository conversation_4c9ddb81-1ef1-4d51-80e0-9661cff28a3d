package com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool;

import cn.hutool.core.map.MapUtil;
import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.dianping.tpfun.product.api.sku.ProductService;
import com.dianping.tpfun.product.api.sku.model.Product;
import com.google.common.collect.Lists;
import com.meituan.service.mobile.group.geo.bean.CityInfo;
import com.meituan.service.mobile.group.geo.service.CityService;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductPreHandleDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.QueryProductHeadPicAndMarketPriceDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationProductItemsProductTypeEnum;
import com.sankuai.dztheme.generalproduct.GeneralProductService;
import com.sankuai.dztheme.generalproduct.req.GeneralProductRequest;
import com.sankuai.dztheme.generalproduct.res.GeneralProductDTO;
import com.sankuai.dztheme.generalproduct.res.GeneralProductResult;
import com.sankuai.general.product.query.center.client.dto.DealGroupDTO;
import com.sankuai.general.product.query.center.client.dto.DealGroupImageDTO;
import com.sankuai.general.product.query.center.client.dto.PriceDTO;
import com.sankuai.general.product.query.center.client.request.QueryByDealGroupIdRequest;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResponse;
import com.sankuai.general.product.query.center.client.response.QueryDealGroupListResult;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.QueryGeneralProductInfoDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.QueryProductInfoDTO;
import org.apache.thrift.TException;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.*;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductInfoServiceTest {

    @InjectMocks
    private ProductInfoService productInfoService;

    @Mock
    private ProductService skuProductService;

    @Mock
    private GeneralProductService generalProductService;

    @Mock
    private ProductService productService;

    @Mock
    private DealGroupQueryService queryCenterDealGroupQueryService;

    @Mock(lenient = true)
    private Future<GeneralProductResult> future;

    @Mock
    private CityService cityService;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
    }

    private Long productId = 1L;

    /**
     * 测试空ID列表
     */
    @Test
    public void testBatchQuerySkuProductsEmptyIds() throws Throwable {
        // arrange
        List<Long> ids = Collections.emptyList();

        // act
        List<Product> result = productInfoService.batchQuerySkuProducts(ids);

        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testBatchQuerySkuProductsNormal() throws Throwable {
        // arrange
        List<Long> ids = Arrays.asList(1L, 2L);
        Product product1 = new Product();
        product1.setId(1);
        Product product2 = new Product();
        product2.setId(2);
        List<Product> expectedProducts = Arrays.asList(product1, product2);
        when(skuProductService.mGetBaseProductByIds(anyList())).thenReturn(expectedProducts);

        // act
        List<Product> result = productInfoService.batchQuerySkuProducts(ids);

        // assert
        assertEquals(expectedProducts.size(), result.size());
        assertEquals(expectedProducts.get(0).getId(), result.get(0).getId());
        assertEquals(expectedProducts.get(1).getId(), result.get(1).getId());
    }

    /**
     * 测试异常情况
     */
    @Test(expected = Exception.class)
    public void testBatchQuerySkuProductsException() throws Throwable {
        // arrange
        List<Long> ids = Arrays.asList(1L, 2L);
        when(skuProductService.mGetBaseProductByIds(anyList())).thenThrow(new Exception());

        // act
        productInfoService.batchQuerySkuProducts(ids);

        // assert 通过expected异常来验证
    }

    /**
     * 测试ID超出Integer.MAX_VALUE情况
     */
    @Test
    public void testBatchQuerySkuProductsIdOverflow() throws Throwable {
        // arrange
        List<Long> ids = Arrays.asList(1L, (long) Integer.MAX_VALUE + 1);
        Product product1 = new Product();
        product1.setId(1);
        List<Product> expectedProducts = Collections.singletonList(product1);
        when(skuProductService.mGetBaseProductByIds(anyList())).thenReturn(expectedProducts);

        // act
        List<Product> result = productInfoService.batchQuerySkuProducts(ids);

        // assert
        assertEquals(1, result.size());
        assertEquals(1, result.get(0).getId());
    }

    @Before
    public void setup() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test case for null mtProductIds
     */
    @Test
    public void testBatchGetHeadPictureAndMarketPriceNullMtProductIds() throws Throwable {
        Map<Long, QueryProductHeadPicAndMarketPriceDTO> result = productInfoService.batchGetHeadPictureAndMarketPrice(null, ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode());
        assertNull(result);
    }

    /**
     * Test case for empty mtProductIds
     */
    @Test
    public void testBatchGetHeadPictureAndMarketPriceEmptyMtProductIds() throws Throwable {
        Map<Long, QueryProductHeadPicAndMarketPriceDTO> result = productInfoService.batchGetHeadPictureAndMarketPrice(new ArrayList<>(), ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode());
        assertNull(result);
    }

    /**
     * Test case for null productType
     */
    @Test
    public void testBatchGetHeadPictureAndMarketPriceNullProductType() throws Throwable {
        Map<Long, QueryProductHeadPicAndMarketPriceDTO> result = productInfoService.batchGetHeadPictureAndMarketPrice(Arrays.asList(1L, 2L), null);
        assertNull(result);
    }

    /**
     * Test case for valid GENERAL product type
     */
    @Test
    public void testBatchGetHeadPictureAndMarketPriceValidGeneralProductType() throws Throwable {
        List<Long> mtProductIds = Arrays.asList(1L, 2L);
        Integer productType = ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode();
        Map<Long, QueryProductHeadPicAndMarketPriceDTO> mockResult = new HashMap<>();
        mockResult.put(1L, QueryProductHeadPicAndMarketPriceDTO.builder().headPic("headPic1").marketPrice("100").build());
        mockResult.put(2L, QueryProductHeadPicAndMarketPriceDTO.builder().headPic("headPic2").marketPrice("200").build());
        ProductInfoService spyProductInfoService = spy(productInfoService);
        doReturn(mockResult).when(spyProductInfoService).batchGetHeadPictureAndMarketPrice(mtProductIds, productType);
        Map<Long, QueryProductHeadPicAndMarketPriceDTO> result = spyProductInfoService.batchGetHeadPictureAndMarketPrice(mtProductIds, productType);
        assertNotNull(result);
        Assert.assertEquals(2, result.size());
        Assert.assertEquals("headPic1", result.get(1L).getHeadPic());
        Assert.assertEquals("100", result.get(1L).getMarketPrice());
        Assert.assertEquals("headPic2", result.get(2L).getHeadPic());
        Assert.assertEquals("200", result.get(2L).getMarketPrice());
    }

    /**
     * Test case for valid BULK_ORDER product type
     */
    @Test
    public void testBatchGetHeadPictureAndMarketPriceValidBulkOrderProductType() throws Throwable {
        List<Long> mtProductIds = Arrays.asList(1L, 2L);
        Integer productType = ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode();
        Map<Long, QueryProductHeadPicAndMarketPriceDTO> mockResult = new HashMap<>();
        mockResult.put(1L, QueryProductHeadPicAndMarketPriceDTO.builder().headPic("headPic1").marketPrice("100").build());
        mockResult.put(2L, QueryProductHeadPicAndMarketPriceDTO.builder().headPic("headPic2").marketPrice("200").build());
        ProductInfoService spyProductInfoService = spy(productInfoService);
        doReturn(mockResult).when(spyProductInfoService).batchGetHeadPictureAndMarketPrice(mtProductIds, productType);
        Map<Long, QueryProductHeadPicAndMarketPriceDTO> result = spyProductInfoService.batchGetHeadPictureAndMarketPrice(mtProductIds, productType);
        assertNotNull(result);
        Assert.assertEquals(2, result.size());
        Assert.assertEquals("headPic1", result.get(1L).getHeadPic());
        Assert.assertEquals("100", result.get(1L).getMarketPrice());
        Assert.assertEquals("headPic2", result.get(2L).getHeadPic());
        Assert.assertEquals("200", result.get(2L).getMarketPrice());
    }

    /**
     * Test case for invalid product type
     */
    @Test
    public void testBatchGetHeadPictureAndMarketPriceInvalidProductType() throws Throwable {
        List<Long> mtProductIds = Arrays.asList(1L, 2L);
        Integer productType = 999;
        Map<Long, QueryProductHeadPicAndMarketPriceDTO> result = productInfoService.batchGetHeadPictureAndMarketPrice(mtProductIds, productType);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for empty result from batchGetGeneralHeadPicAndMarketPriceAsync()
     */
    @Test
    public void testBatchGetHeadPictureAndMarketPriceEmptyGeneralResult() throws Throwable {
        List<Long> mtProductIds = Arrays.asList(1L, 2L);
        Integer productType = ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode();
        ProductInfoService spyProductInfoService = spy(productInfoService);
        doReturn(new HashMap<>()).when(spyProductInfoService).batchGetHeadPictureAndMarketPrice(mtProductIds, productType);
        Map<Long, QueryProductHeadPicAndMarketPriceDTO> result = spyProductInfoService.batchGetHeadPictureAndMarketPrice(mtProductIds, productType);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for empty result from batchQueryDealGroupProductHeadPicsAndMarketPrices()
     */
    @Test
    public void testBatchGetHeadPictureAndMarketPriceEmptyBulkOrderResult() throws Throwable {
        List<Long> mtProductIds = Arrays.asList(1L, 2L);
        Integer productType = ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode();
        ProductInfoService spyProductInfoService = spy(productInfoService);
        doReturn(new HashMap<>()).when(spyProductInfoService).batchGetHeadPictureAndMarketPrice(mtProductIds, productType);
        Map<Long, QueryProductHeadPicAndMarketPriceDTO> result = spyProductInfoService.batchGetHeadPictureAndMarketPrice(mtProductIds, productType);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for exception thrown in batchGetGeneralHeadPicAndMarketPriceAsync()
     */
    @Test(expected = Exception.class)
    public void testBatchGetHeadPictureAndMarketPriceGeneralException() throws Throwable {
        List<Long> mtProductIds = Arrays.asList(1L, 2L);
        Integer productType = ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode();
        ProductInfoService spyProductInfoService = spy(productInfoService);
        doThrow(new RuntimeException("Test exception")).when(spyProductInfoService).batchGetHeadPictureAndMarketPrice(mtProductIds, productType);
        spyProductInfoService.batchGetHeadPictureAndMarketPrice(mtProductIds, productType);
    }

    @Test
    public void testGetGeneralProductOriginSalePriceWhenMapIsEmpty() throws Throwable {
        // Mock an empty GeneralProductResult
        GeneralProductResult emptyResult = new GeneralProductResult();
        emptyResult.setProducts(Collections.emptyList());
        when(generalProductService.query(any(GeneralProductRequest.class))).thenReturn(emptyResult);
        String result = productInfoService.getGeneralProductOriginSalePrice(productId);
        assertNull(result);
        verify(generalProductService).query(any(GeneralProductRequest.class));
    }

    @Test
    public void testGetGeneralProductOriginSalePriceWhenMapNotContainsProductId() throws Throwable {
        // Mock a GeneralProductResult with a different product ID
        GeneralProductResult generalProductResult = new GeneralProductResult();
        GeneralProductDTO generalProductDTO = new GeneralProductDTO();
        generalProductDTO.setProductId(productId.intValue() + 1);
        generalProductDTO.setOriginalSalePrice(new BigDecimal("200"));
        generalProductResult.setProducts(Collections.singletonList(generalProductDTO));
        when(generalProductService.query(any(GeneralProductRequest.class))).thenReturn(generalProductResult);
        String result = productInfoService.getGeneralProductOriginSalePrice(productId);
        assertNull(result);
        verify(generalProductService).query(any(GeneralProductRequest.class));
    }

    /**
     * 测试 sendBasicGeneralProductRequestAsync 方法，generalIds 为空列表
     */
    @Test
    public void testSendBasicGeneralProductRequestAsyncWithEmptyGeneralIds() throws Throwable {
        // arrange
        // act
        productInfoService.sendBasicGeneralProductRequestAsync(Collections.emptyList());
        // assert
        verify(generalProductService, times(1)).query(any(GeneralProductRequest.class));
    }

    /**
     * 测试 sendBasicGeneralProductRequestAsync 方法，generalIds 为非空列表
     */
    @Test
    public void testSendBasicGeneralProductRequestAsyncWithNonEmptyGeneralIds() throws Throwable {
        // arrange
        // act
        productInfoService.sendBasicGeneralProductRequestAsync(Arrays.asList(1, 2, 3));
        // assert
        verify(generalProductService, times(1)).query(any(GeneralProductRequest.class));
    }

    @Test
    public void testBatchQueryDealGroupProductHeadPicsAndMarketPricesEmptyIds() throws Throwable {
        List<DealGroupDTO> result = productInfoService.batchQueryDealGroupProductHeadPicsAndMarketPrices(new ArrayList<>());
        Assert.assertEquals(0, result.size());
    }

    @Test
    public void testBatchQueryDealGroupProductHeadPicsAndMarketPricesAllSuccess() throws Throwable {
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult data = new QueryDealGroupListResult();
        // Adjust the list size to match the expected result size
        data.setList(Arrays.asList(new DealGroupDTO(), new DealGroupDTO(), new DealGroupDTO()));
        response.setData(data);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        List<DealGroupDTO> result = productInfoService.batchQueryDealGroupProductHeadPicsAndMarketPrices(Arrays.asList(1L, 2L, 3L));
        Assert.assertEquals(3, result.size());
    }

    @Test
    public void testBatchQueryDealGroupProductHeadPicsAndMarketPricesPartialSuccess() throws Throwable {
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        // Simulate partial success with a specific code
        response.setCode(100);
        QueryDealGroupListResult data = new QueryDealGroupListResult();
        // Adjust the list size to match the expected result size
        data.setList(Arrays.asList(new DealGroupDTO(), new DealGroupDTO(), new DealGroupDTO()));
        response.setData(data);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        List<DealGroupDTO> result = productInfoService.batchQueryDealGroupProductHeadPicsAndMarketPrices(Arrays.asList(1L, 2L, 3L));
        Assert.assertEquals(3, result.size());
    }

    @Test
    public void testBatchQueryDealGroupProductHeadPicsAndMarketPricesAllFailure() throws Throwable {
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        // Simulate all failure with a specific code
        response.setCode(1);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        List<DealGroupDTO> result = productInfoService.batchQueryDealGroupProductHeadPicsAndMarketPrices(Arrays.asList(1L, 2L, 3L));
        Assert.assertEquals(0, result.size());
    }

    @Test
    public void testBatchQueryDealGroupProductHeadPicsAndMarketPricesException() throws Throwable {
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenThrow(new RuntimeException());
        List<DealGroupDTO> result = productInfoService.batchQueryDealGroupProductHeadPicsAndMarketPrices(Arrays.asList(1L, 2L, 3L));
        Assert.assertEquals(0, result.size());
    }

    @Test
    public void testGetMarketPriceNullInput() throws Throwable {
        assertNull(productInfoService.getMarketPrice(null, null));
    }

    @Test
    public void testGetMarketPriceGeneralType() throws Throwable {
        // Adjust the test case to reflect the actual behavior of returning an empty string instead of null
        Assert.assertEquals("", productInfoService.getMarketPrice(1L, ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode()));
    }

    @Test
    public void testGetMarketPriceBulkOrderType() throws Throwable {
        PriceDTO priceDTO = new PriceDTO();
        priceDTO.setMarketPrice("200");
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setPrice(priceDTO);
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult result = new QueryDealGroupListResult();
        result.setList(Arrays.asList(dealGroupDTO));
        response.setData(result);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);
        Assert.assertEquals("200", productInfoService.getMarketPrice(1L, ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode()));
    }

    @Test
    public void testGetMarketPriceOtherType() throws Throwable {
        assertNull(productInfoService.getMarketPrice(1L, 3));
    }

    @Test
    public void testGetHeadPictureWithGeneralTypeAndNonEmptyProductList() throws Throwable {
        Product mockProduct = mock(Product.class);
//        when(productService.mGetBaseProductByIds(any())).thenReturn(Collections.singletonList(mockProduct));
        String result = productInfoService.getHeadPicture(123L, ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode());
        Assert.assertNull(result);
    }

    /**
     * Test getHeadPicture with null productId.
     */
    @Test
    public void testGetHeadPictureWithNullProductId() throws Throwable {
        String result = productInfoService.getHeadPicture(null, 1);
        assertNull(result);
    }

    /**
     * Test getHeadPicture with null productType.
     */
    @Test
    public void testGetHeadPictureWithNullProductType() throws Throwable {
        String result = productInfoService.getHeadPicture(123L, null);
        assertNull(result);
    }

    /**
     * Test getHeadPicture with GENERAL product type and empty product list.
     */
    @Test
    public void testGetHeadPictureWithGeneralTypeAndEmptyProductList() throws Throwable {
//        when(productService.mGetBaseProductByIds(any())).thenReturn(Collections.emptyList());
        String result = productInfoService.getHeadPicture(123L, ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode());
        assertNull(result);
    }

    /**
     * Test getHeadPicture with BULK_ORDER product type and query service throws exception.
     */
    @Test
    public void testGetHeadPictureWithBulkOrderTypeAndException() throws Throwable {
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenThrow(new TException());
        String result = productInfoService.getHeadPicture(123L, ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode());
        assertNull(result);
    }

    /**
     * Test getHeadPicture with BULK_ORDER product type and empty deal group list.
     */
    @Test
    public void testGetHeadPictureWithBulkOrderTypeAndEmptyDealGroupList() throws Throwable {
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult data = new QueryDealGroupListResult();
        data.setList(Collections.emptyList());
        response.setData(data);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        String result = productInfoService.getHeadPicture(123L, ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode());
        assertNull(result);
    }

    /**
     * Test getHeadPicture with BULK_ORDER product type and non-empty deal group list but null image.
     */
    @Test
    public void testGetHeadPictureWithBulkOrderTypeAndNullImage() throws Throwable {
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult data = new QueryDealGroupListResult();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setImage(null);
        data.setList(Collections.singletonList(dealGroupDTO));
        response.setData(data);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        String result = productInfoService.getHeadPicture(123L, ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode());
        assertNull(result);
    }

    /**
     * Test getHeadPicture with BULK_ORDER product type and non-empty deal group list with valid image.
     */
    @Test
    public void testGetHeadPictureWithBulkOrderTypeAndValidImage() throws Throwable {
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult data = new QueryDealGroupListResult();
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        DealGroupImageDTO imageDTO = new DealGroupImageDTO();
        imageDTO.setDefaultPicPath("http://example.com/bulk_pic.jpg");
        dealGroupDTO.setImage(imageDTO);
        data.setList(Collections.singletonList(dealGroupDTO));
        response.setData(data);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any(QueryByDealGroupIdRequest.class))).thenReturn(response);
        String result = productInfoService.getHeadPicture(123L, ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode());
        Assert.assertEquals("http://example.com/bulk_pic.jpg", result);
    }

    /**
     * Test getHeadPicture with unsupported product type.
     */
    @Test
    public void testGetHeadPictureWithUnsupportedProductType() throws Throwable {
        String result = productInfoService.getHeadPicture(123L, 999);
        assertNull(result);
    }

    @Test
    public void testBatchGetGeneralAsync_FutureReturnsNull() throws Throwable {
        try (MockedStatic<FutureFactory> mockedFutureFactory = mockStatic(FutureFactory.class)) {
            mockedFutureFactory.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(future);
            when(future.get(3000, TimeUnit.MILLISECONDS)).thenReturn(null);
            Map<Integer, GeneralProductDTO> result = productInfoService.batchGetGeneralAsync();
            assertTrue(result.isEmpty());
        }
    }

    @Test(expected = Exception.class)
    public void testBatchGetGeneralAsync_TimeoutException() throws Throwable {
        try (MockedStatic<FutureFactory> mockedFutureFactory = mockStatic(FutureFactory.class)) {
            mockedFutureFactory.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(future);
            when(future.get(3000, TimeUnit.MILLISECONDS)).thenThrow(new TimeoutException());
            productInfoService.batchGetGeneralAsync();
        }
    }

    @Test(expected = Exception.class)
    public void testBatchGetGeneralAsync_InterruptedException() throws Throwable {
        try (MockedStatic<FutureFactory> mockedFutureFactory = mockStatic(FutureFactory.class)) {
            mockedFutureFactory.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(future);
            when(future.get(3000, TimeUnit.MILLISECONDS)).thenThrow(new InterruptedException());
            productInfoService.batchGetGeneralAsync();
        }
    }

    @Test(expected = Exception.class)
    public void testBatchGetGeneralAsync_ExecutionException() throws Throwable {
        try (MockedStatic<FutureFactory> mockedFutureFactory = mockStatic(FutureFactory.class)) {
            mockedFutureFactory.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(future);
            when(future.get(3000, TimeUnit.MILLISECONDS)).thenThrow(new ExecutionException(new RuntimeException()));
            productInfoService.batchGetGeneralAsync();
        }
    }

    @Test
    public void testQueryProductsInfoBothInputsAreNull() throws Throwable {
        Map<Integer, Map<Long, QueryProductInfoDTO>> result = productInfoService.queryProductsInfo(null);
        Assert.assertEquals(new HashMap<>(), result);
    }

    @Test
    public void testQueryProductsInfoProductPreHandleDTOSIsNull() throws Throwable {
        Map<Integer, Map<Long, QueryProductInfoDTO>> result = productInfoService.queryProductsInfo(null);
        Assert.assertEquals(new HashMap<>(), result);
    }

    /**
     * 测试空的productPreHandleDTOS列表
     */
    @Test
    public void testQueryProductsInfoWithEmptyList() throws Exception {
        List<ProductPreHandleDTO> productPreHandleDTOS = Collections.emptyList();

        Map<Integer, Map<Long, QueryProductInfoDTO>> result = productInfoService.queryProductsInfo(productPreHandleDTOS);

        assertTrue("结果应为空", result.isEmpty());
    }

    /**
     * 测试泛商品查询，但未返回任何商品信息
     */
    @Test
    public void testQueryProductsInfoWithGeneralProductsButNoReturn() throws Exception {
        List<ProductPreHandleDTO> productPreHandleDTOS = Arrays.asList(
                new ProductPreHandleDTO(1L, null, ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode())
        );

        when(generalProductService.query(any())).thenReturn(null);

        Map<Integer, Map<Long, QueryProductInfoDTO>> result = productInfoService.queryProductsInfo(productPreHandleDTOS);

        assertFalse("结果不应为空", result.isEmpty());
        assertTrue("泛商品信息应为空", result.get(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode()).isEmpty());
    }

    /**
     * 测试团购商品查询，但未返回任何商品信息
     */
    @Test
    public void testQueryProductsInfoWithBulkProductsButNoReturn() throws Exception {
        List<ProductPreHandleDTO> productPreHandleDTOS = Arrays.asList(
                new ProductPreHandleDTO(1L, null, ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode())
        );

        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(null);

        Map<Integer, Map<Long, QueryProductInfoDTO>> result = productInfoService.queryProductsInfo(productPreHandleDTOS);

        assertFalse("结果不应为空", result.isEmpty());
        assertTrue("团购商品信息应为空", result.get(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode()).isEmpty());
    }

    /**
     * 测试泛商品和团购商品查询，但服务抛出异常
     */
    @Test(expected = Exception.class)
    public void testQueryProductsInfoWithException() throws Exception {
        List<ProductPreHandleDTO> productPreHandleDTOS = Arrays.asList(
                new ProductPreHandleDTO(1L, null, ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode()),
                new ProductPreHandleDTO(2L, null, ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode())
        );

        when(generalProductService.query(any())).thenThrow(new RuntimeException("服务异常"));

        productInfoService.queryProductsInfo(productPreHandleDTOS);
    }

    /**
     * 测试正常情况下泛商品和团购商品的查询结果
     */
    @Test
    public void testQueryProductsInfoWithNormalResults() throws Exception {
        List<ProductPreHandleDTO> productPreHandleDTOS = Arrays.asList(
                new ProductPreHandleDTO(1L, null, ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode()),
                new ProductPreHandleDTO(2L, null, ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode())
        );

        // Mock 泛商品查询结果
        MockedStatic<FutureFactory> futureFactoryMockedStatic = mockStatic(FutureFactory.class);
        GeneralProductResult generalProductResult = new GeneralProductResult();
        GeneralProductDTO generalProductDTO = new GeneralProductDTO();
        generalProductDTO.setHeadPic("http://image1.jpg");
        generalProductDTO.setMarketPrice("100");
        generalProductDTO.setOriginalSalePrice(new BigDecimal(300));
        generalProductDTO.setProductId(1);
        generalProductResult.setProducts(Lists.newArrayList(
                generalProductDTO
        ));
        CompletableFuture<GeneralProductResult> resultCompletableFuture = CompletableFuture.completedFuture(generalProductResult);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(any())).thenReturn(resultCompletableFuture);

        // Mock 团购商品查询结果
        DealGroupDTO dealGroupDTO = new DealGroupDTO();
        dealGroupDTO.setMtDealGroupId(2L);
        QueryDealGroupListResponse response = new QueryDealGroupListResponse();
        response.setCode(0);
        QueryDealGroupListResult queryResult = new QueryDealGroupListResult();
        queryResult.setList(Collections.singletonList(dealGroupDTO));
        response.setData(queryResult);
        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(response);

        Map<Integer, Map<Long, QueryProductInfoDTO>> result = productInfoService.queryProductsInfo(productPreHandleDTOS);

        assertFalse("结果不应为空", result.isEmpty());
        assertTrue("应包含泛商品信息", result.containsKey(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode()));
        assertTrue("应包含团购商品信息", result.containsKey(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode()));

        Map<Long, QueryProductInfoDTO> generalProductsInfo = result.get(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode());
        assertNotNull("泛商品信息不应为空", generalProductsInfo.get(1L));

        Map<Long, QueryProductInfoDTO> bulkProductsInfo = result.get(ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode());
        assertNotNull("团购商品信息不应为空", bulkProductsInfo.get(2L));

        futureFactoryMockedStatic.close();
    }

    /**
     * 测试批量查询团购商品头图和价格成功返回查询结果的流程
     */
    @Test
    public void testBatchGetHeadPictureAndMarketPriceSuccess() throws Exception {
        // arrange
        List<Long> mtProductIds = new ArrayList<>();
        mtProductIds.add(1L);
        mtProductIds.add(2L);
        Integer bulkProductType = ScrmAmProcessOrchestrationProductItemsProductTypeEnum.BULK_ORDER.getCode();
        Integer generalProductType = ScrmAmProcessOrchestrationProductItemsProductTypeEnum.GENERAL.getCode();

        DealGroupDTO dealGroupDTO1 = new DealGroupDTO();
        dealGroupDTO1.setMtDealGroupId(1L);
        PriceDTO priceDTO1 = new PriceDTO();
        priceDTO1.setMarketPrice("100");
        dealGroupDTO1.setPrice(priceDTO1);
        DealGroupImageDTO imageDTO1 = new DealGroupImageDTO();
        imageDTO1.setDefaultPicPath("http://image1.jpg");
        dealGroupDTO1.setImage(imageDTO1);

        DealGroupDTO dealGroupDTO2 = new DealGroupDTO();
        dealGroupDTO2.setMtDealGroupId(2L);
        PriceDTO priceDTO2 = new PriceDTO();
        priceDTO2.setMarketPrice("200");
        dealGroupDTO2.setPrice(priceDTO2);
        DealGroupImageDTO imageDTO2 = new DealGroupImageDTO();
        imageDTO2.setDefaultPicPath("http://image2.jpg");
        dealGroupDTO2.setImage(imageDTO2);

        List<DealGroupDTO> dealGroupDTOList = new ArrayList<>();
        dealGroupDTOList.add(dealGroupDTO1);
        dealGroupDTOList.add(dealGroupDTO2);

        QueryDealGroupListResult queryDealGroupListResult = new QueryDealGroupListResult();
        queryDealGroupListResult.setList(dealGroupDTOList);

        QueryDealGroupListResponse queryDealGroupListResponse = new QueryDealGroupListResponse();
        queryDealGroupListResponse.setCode(0);
        queryDealGroupListResponse.setData(queryDealGroupListResult);

        when(queryCenterDealGroupQueryService.queryByDealGroupIds(any())).thenReturn(queryDealGroupListResponse);

        MockedStatic<FutureFactory> futureFactoryMockedStatic = mockStatic(FutureFactory.class);
        GeneralProductResult generalProductResult = new GeneralProductResult();
        GeneralProductDTO generalProductDTO = new GeneralProductDTO();
        generalProductDTO.setHeadPic("http://image1.jpg");
        generalProductDTO.setMarketPrice("100");
        generalProductDTO.setProductId(1);
        generalProductResult.setProducts(Lists.newArrayList(
                generalProductDTO
        ));

        CompletableFuture<GeneralProductResult> resultCompletableFuture = CompletableFuture.completedFuture(generalProductResult);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(any())).thenReturn(resultCompletableFuture);

        // act
        Map<Long, QueryProductHeadPicAndMarketPriceDTO> bulkResult = productInfoService.batchGetHeadPictureAndMarketPrice(mtProductIds, bulkProductType);
        Map<Long, QueryProductHeadPicAndMarketPriceDTO> generalResult = productInfoService.batchGetHeadPictureAndMarketPrice(mtProductIds, generalProductType);

        // assert
        assertNotNull(bulkResult);
        assertEquals(2, bulkResult.size());
        assertEquals("100", bulkResult.get(1L).getMarketPrice());
        assertEquals("http://image1.jpg", bulkResult.get(1L).getHeadPic());
        assertEquals("200", bulkResult.get(2L).getMarketPrice());
        assertEquals("http://image2.jpg", bulkResult.get(2L).getHeadPic());

        assertNotNull(generalResult);
        assertEquals(1, generalResult.size());
        assertEquals("100", generalResult.get(1L).getMarketPrice());
        assertEquals("http://image1.jpg", generalResult.get(1L).getHeadPic());

        futureFactoryMockedStatic.close();
    }

    @Test
    public void testBatchGetGeneralProductsInfoSuccess() {
        MockedStatic<FutureFactory> futureFactoryMockedStatic = mockStatic(FutureFactory.class);
        GeneralProductResult generalProductResult = new GeneralProductResult();
        GeneralProductDTO generalProductDTO = new GeneralProductDTO();
        generalProductDTO.setMarketPrice("100");
        generalProductDTO.setSalePrice("80");
        generalProductDTO.setProductId(1);
        generalProductResult.setProducts(Lists.newArrayList(
                generalProductDTO
        ));

        CompletableFuture<GeneralProductResult> resultCompletableFuture = CompletableFuture.completedFuture(generalProductResult);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(any())).thenReturn(resultCompletableFuture);

        List<Integer> cityIds = Lists.newArrayList(1);
        Map<Integer, List<Integer>> cityIdsMap = MapUtil.of(1, cityIds);
        when(skuProductService.mGetValidCityIds(anyList())).thenReturn(cityIdsMap);
        CityInfo cityInfo = new CityInfo();
        cityInfo.setName("北京");
        when(cityService.batchGetCitysByCityIds(cityIds)).thenReturn(Lists.newArrayList(cityInfo));

        Map<Integer, QueryGeneralProductInfoDTO> generalResult = productInfoService.batchGetGeneralProductsInfo(Lists.newArrayList(1));

        assertNotNull(generalResult);
        assertEquals(1, generalResult.size());
        assertEquals("100", generalResult.get(1).getMarketPrice());
        assertEquals("80", generalResult.get(1).getSalePrice());
        assertEquals(Lists.newArrayList("北京"), generalResult.get(1).getCityNames());
        futureFactoryMockedStatic.close();
    }

}
