package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import java.util.Calendar;
import java.util.Date;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

class // Other test methods remain unchanged
StaffTaskAssignActionDealExternalStaffTaskAssignActionTest {

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @InjectMocks
    private StaffTaskAssignAction staffTaskAssignAction;

    public StaffTaskAssignActionDealExternalStaffTaskAssignActionTest() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试 mobilePhoneNum 不为空，finalCheckTime 为空的情况
     */
    @Test
    public void testDealExternalStaffTaskAssignActionFinalCheckTimeIsNull() throws Throwable {
        // arrange
        String corpId = "corpId";
        String staffUserId = "staffUserId";
        String mobilePhoneNum = "1234567890";
        String avatar = "avatar";
        String nickName = "nickName";
        Date taskStartTime = new Date();
        Date finalCheckTime = null;
        when(appConfigRepository.getAppIdByCorpId(corpId)).thenReturn("appId");
        // Calculate the expected finalCheckTime (2 days later)
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 2);
        Date expectedFinalCheckTime = calendar.getTime();
        // act
        staffTaskAssignAction.dealExternalStaffTaskAssignAction(corpId, staffUserId, mobilePhoneNum, avatar, nickName, taskStartTime, finalCheckTime);
        // assert
        verify(executeLogDOMapper, times(1)).insert(argThat(log -> {
            // Compare the timestamps of the dates to allow for minor differences
            long expectedTime = expectedFinalCheckTime.getTime();
            long actualTime = log.getFinalCheckTime().getTime();
            // Allow up to 1 second difference
            return Math.abs(expectedTime - actualTime) < 1000;
        }));
    }

    /**
     * 测试 mobilePhoneNum 为空的情况
     */
    @Test
    public void testDealExternalStaffTaskAssignActionMobilePhoneNumIsNull() throws Throwable {
        // arrange
        String corpId = "corpId";
        String staffUserId = "staffUserId";
        String mobilePhoneNum = null;
        String avatar = "avatar";
        String nickName = "nickName";
        Date taskStartTime = new Date();
        Date finalCheckTime = new Date();
        // act
        staffTaskAssignAction.dealExternalStaffTaskAssignAction(corpId, staffUserId, mobilePhoneNum, avatar, nickName, taskStartTime, finalCheckTime);
        // assert
        verify(executeLogDOMapper, never()).insert(any());
    }

    /**
     * 测试 mobilePhoneNum 不为空，staffUserId 为空的情况
     */
    @Test
    public void testDealExternalStaffTaskAssignActionStaffUserIdIsNull() throws Throwable {
        // arrange
        String corpId = "corpId";
        String staffUserId = null;
        String mobilePhoneNum = "1234567890";
        String avatar = "avatar";
        String nickName = "nickName";
        Date taskStartTime = new Date();
        Date finalCheckTime = new Date();
        when(appConfigRepository.getAppIdByCorpId(corpId)).thenReturn("appId");
        // act
        staffTaskAssignAction.dealExternalStaffTaskAssignAction(corpId, staffUserId, mobilePhoneNum, avatar, nickName, taskStartTime, finalCheckTime);
        // assert
        verify(executeLogDOMapper, times(1)).insert(argThat(log -> log.getExecutorId().equals("all")));
    }

    /**
     * 测试 mobilePhoneNum 不为空，staffUserId 不为空的情况
     */
    @Test
    public void testDealExternalStaffTaskAssignActionStaffUserIdIsNotNull() throws Throwable {
        // arrange
        String corpId = "corpId";
        String staffUserId = "staffUserId";
        String mobilePhoneNum = "1234567890";
        String avatar = "avatar";
        String nickName = "nickName";
        Date taskStartTime = new Date();
        Date finalCheckTime = new Date();
        when(appConfigRepository.getAppIdByCorpId(corpId)).thenReturn("appId");
        // act
        staffTaskAssignAction.dealExternalStaffTaskAssignAction(corpId, staffUserId, mobilePhoneNum, avatar, nickName, taskStartTime, finalCheckTime);
        // assert
        verify(executeLogDOMapper, times(1)).insert(argThat(log -> log.getExecutorId().equals(staffUserId)));
    }

    /**
     * 测试 mobilePhoneNum 不为空，taskStartTime 为空的情况
     */
    @Test
    public void testDealExternalStaffTaskAssignActionTaskStartTimeIsNull() throws Throwable {
        // arrange
        String corpId = "corpId";
        String staffUserId = "staffUserId";
        String mobilePhoneNum = "1234567890";
        String avatar = "avatar";
        String nickName = "nickName";
        Date taskStartTime = null;
        Date finalCheckTime = new Date();
        when(appConfigRepository.getAppIdByCorpId(corpId)).thenReturn("appId");
        // act
        staffTaskAssignAction.dealExternalStaffTaskAssignAction(corpId, staffUserId, mobilePhoneNum, avatar, nickName, taskStartTime, finalCheckTime);
        // assert
        verify(executeLogDOMapper, times(1)).insert(argThat(log -> log.getTaskStartTime() != null));
    }

    /**
     * 测试 mobilePhoneNum 不为空，nickName 为空的情况
     */
    @Test
    public void testDealExternalStaffTaskAssignActionNickNameIsNull() throws Throwable {
        // arrange
        String corpId = "corpId";
        String staffUserId = "staffUserId";
        String mobilePhoneNum = "1234567890";
        String avatar = "avatar";
        String nickName = null;
        Date taskStartTime = new Date();
        Date finalCheckTime = new Date();
        when(appConfigRepository.getAppIdByCorpId(corpId)).thenReturn("appId");
        // act
        staffTaskAssignAction.dealExternalStaffTaskAssignAction(corpId, staffUserId, mobilePhoneNum, avatar, nickName, taskStartTime, finalCheckTime);
        // assert
        verify(executeLogDOMapper, times(1)).insert(argThat(log -> log.getMemberName().equals(mobilePhoneNum)));
    }
}
