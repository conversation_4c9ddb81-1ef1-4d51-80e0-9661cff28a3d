package com.sankuai.scrm.core.service.realtime.task.mq.producer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import java.lang.reflect.Field;
import java.util.Properties;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GroupRetailAiEngineDispatchProducerTest {

    private GroupRetailAiEngineDispatchProducer producer;

    private RealTimeTaskConsumerConfig consumerConfig;

    private MockedStatic<MafkaClient> mafkaClientMockedStatic;

    private IProducerProcessor mockProducer;

    @BeforeEach
    void setUp() throws Exception {
        consumerConfig = mock(RealTimeTaskConsumerConfig.class);
        producer = new GroupRetailAiEngineDispatchProducer();
        // 使用反射设置私有字段consumerConfig
        Field consumerConfigField = GroupRetailAiEngineDispatchProducer.class.getDeclaredField("consumerConfig");
        consumerConfigField.setAccessible(true);
        consumerConfigField.set(producer, consumerConfig);
        mockProducer = mock(IProducerProcessor.class);
        mafkaClientMockedStatic = mockStatic(MafkaClient.class);
    }

    @AfterEach
    void tearDown() throws Exception {
        mafkaClientMockedStatic.close();
        // 使用反射重置静态producer字段
        Field producerField = GroupRetailAiEngineDispatchProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, null);
    }

    /**
     * 测试正常场景 - 成功创建producer
     */
    @Test
    void testAfterPropertiesSetSuccess() throws Throwable {
        // arrange
        mafkaClientMockedStatic.when(() -> MafkaClient.buildDelayProduceFactory(any(Properties.class), anyString())).thenReturn(mockProducer);
        // act
        producer.afterPropertiesSet();
        // assert
        mafkaClientMockedStatic.verify(() -> MafkaClient.buildDelayProduceFactory(any(Properties.class), eq("scrm.group.retail.ai.engine.dispatch")));
        // 使用反射获取静态producer字段
        Field producerField = GroupRetailAiEngineDispatchProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        assertNotNull(producerField.get(null));
    }

    /**
     * 测试异常场景 - MafkaClient.buildDelayProduceFactory抛出异常
     */
    @Test
    void testAfterPropertiesSetWhenMafkaClientThrowsException() throws Throwable {
        // arrange
        mafkaClientMockedStatic.when(() -> MafkaClient.buildDelayProduceFactory(any(Properties.class), anyString())).thenThrow(new RuntimeException("Mafka client error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> producer.afterPropertiesSet());
        // 使用反射获取静态producer字段
        Field producerField = GroupRetailAiEngineDispatchProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        assertNull(producerField.get(null));
    }

    /**
     * 测试边界场景 - 验证设置的Properties是否正确
     */
    @Test
    void testAfterPropertiesSetVerifyProperties() throws Throwable {
        // arrange
        mafkaClientMockedStatic.when(() -> MafkaClient.buildDelayProduceFactory(any(Properties.class), anyString())).thenAnswer(invocation -> {
            Properties props = invocation.getArgument(0);
            assertEquals("daozong", props.getProperty(ConsumerConstants.MafkaBGNamespace));
            assertEquals("com.sankuai.medicalcosmetology.scrm.core", props.getProperty(ConsumerConstants.MafkaClientAppkey));
            return mockProducer;
        });
        // act
        producer.afterPropertiesSet();
        // assert
        mafkaClientMockedStatic.verify(() -> MafkaClient.buildDelayProduceFactory(any(Properties.class), anyString()));
    }

    /**
     * 测试边界场景 - 多次调用afterPropertiesSet方法
     */
    @Test
    void testAfterPropertiesSetMultipleCalls() throws Throwable {
        // arrange
        mafkaClientMockedStatic.when(() -> MafkaClient.buildDelayProduceFactory(any(Properties.class), anyString())).thenReturn(mockProducer);
        // act - first call
        producer.afterPropertiesSet();
        // Reset the static producer field to simulate a new call
        Field producerField = GroupRetailAiEngineDispatchProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, null);
        // act - second call
        producer.afterPropertiesSet();
        // assert - verify the method was called exactly twice (once per afterPropertiesSet call)
        mafkaClientMockedStatic.verify(() -> MafkaClient.buildDelayProduceFactory(any(Properties.class), anyString()), times(2));
    }
}
