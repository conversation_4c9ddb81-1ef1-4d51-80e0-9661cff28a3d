package com.sankuai.scrm.core.service.external.contact.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.external.contact.request.QueryPageMobileAddFriendRealTimeRequest;
import com.sankuai.inf.kms.pangolin.api.service.IEncryptService;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ScrmMobileAddFriendTaskDO;
import com.sankuai.scrm.core.service.external.contact.dal.example.ScrmMobileAddFriendTaskDOExample;
import com.sankuai.scrm.core.service.external.contact.dal.mapper.ext.ExtScrmMobileAddFriendTaskDOMapper;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MobileAddFriendRealTimeDomainServiceQueryPageMobileAddFriendRealTimeTaskTest {

    @Mock
    private ExtScrmMobileAddFriendTaskDOMapper mobileAddFriendTaskDOMapper;

    @Mock
    private IEncryptService phoneEncryptService;

    @InjectMocks
    private MobileAddFriendRealTimeDomainService mobileAddFriendRealTimeDomainService;

    private QueryPageMobileAddFriendRealTimeRequest request;

    private ScrmMobileAddFriendTaskDO taskDO;

    @BeforeEach
    void setUp() {
        request = new QueryPageMobileAddFriendRealTimeRequest();
        request.setAppId("testApp");
        request.setPageNum(1);
        request.setPageSize(10);
        taskDO = new ScrmMobileAddFriendTaskDO();
        taskDO.setId(1L);
        taskDO.setAppId("testApp");
        taskDO.setAddNumber("encryptedNumber");
        taskDO.setAddNumberMd5("md5Hash");
        taskDO.setNumberType(1);
    }

    /**
     * 测试基本分页查询场景，包含所有必填和可选条件
     */
    @Test
    void testQueryPageMobileAddFriendRealTimeTaskWithAllConditions() throws Throwable {
        // arrange
        request.setAccountId("testAccount");
        request.setAddNumber("***********");
        request.setNumberType(1);
        List<Long> ids = Arrays.asList(1L, 2L);
        when(mobileAddFriendTaskDOMapper.queryPageMobileAddFriendTaskIds(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(ids);
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(Arrays.asList(taskDO));
        when(phoneEncryptService.decryptUTF8String(anyString())).thenReturn("***********");
        // act
        List<ScrmMobileAddFriendTaskDO> result = mobileAddFriendRealTimeDomainService.queryPageMobileAddFriendRealTimeTask(request);
        // assert
        assertEquals(1, result.size());
        assertEquals("***********", result.get(0).getAddNumber());
        verify(mobileAddFriendTaskDOMapper).queryPageMobileAddFriendTaskIds(any(ScrmMobileAddFriendTaskDOExample.class));
        verify(mobileAddFriendTaskDOMapper).selectByExample(any(ScrmMobileAddFriendTaskDOExample.class));
    }

    /**
     * 测试只包含必填条件appId的查询场景
     */
    @Test
    void testQueryPageMobileAddFriendRealTimeTaskWithOnlyRequiredCondition() throws Throwable {
        // arrange
        List<Long> ids = Collections.singletonList(1L);
        when(mobileAddFriendTaskDOMapper.queryPageMobileAddFriendTaskIds(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(ids);
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(Collections.singletonList(taskDO));
        when(phoneEncryptService.decryptUTF8String(anyString())).thenReturn("***********");
        // act
        List<ScrmMobileAddFriendTaskDO> result = mobileAddFriendRealTimeDomainService.queryPageMobileAddFriendRealTimeTask(request);
        // assert
        assertEquals(1, result.size());
        assertNotNull(result.get(0).getAddNumber());
        verify(mobileAddFriendTaskDOMapper).queryPageMobileAddFriendTaskIds(any(ScrmMobileAddFriendTaskDOExample.class));
    }

    /**
     * 测试查询结果为空的情况
     */
    @Test
    void testQueryPageMobileAddFriendRealTimeTaskWithEmptyResult() throws Throwable {
        // arrange
        when(mobileAddFriendTaskDOMapper.queryPageMobileAddFriendTaskIds(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(new ArrayList<>());
        // act
        List<ScrmMobileAddFriendTaskDO> result = mobileAddFriendRealTimeDomainService.queryPageMobileAddFriendRealTimeTask(request);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
        verify(mobileAddFriendTaskDOMapper, never()).selectByExample(any());
    }

    /**
     * 测试手机号解密失败的情况
     */
    @Test
    void testQueryPageMobileAddFriendRealTimeTaskWithDecryptError() throws Throwable {
        // arrange
        List<Long> ids = Collections.singletonList(1L);
        when(mobileAddFriendTaskDOMapper.queryPageMobileAddFriendTaskIds(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(ids);
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(Collections.singletonList(taskDO));
        when(phoneEncryptService.decryptUTF8String(anyString())).thenThrow(new GeneralSecurityException("Decrypt error"));
        // act
        List<ScrmMobileAddFriendTaskDO> result = mobileAddFriendRealTimeDomainService.queryPageMobileAddFriendRealTimeTask(request);
        // assert
        assertEquals(1, result.size());
        assertNull(result.get(0));
        verify(phoneEncryptService).decryptUTF8String(anyString());
    }

    /**
     * 测试只包含addNumber条件的查询场景
     */
    @Test
    void testQueryPageMobileAddFriendRealTimeTaskWithAddNumberOnly() throws Throwable {
        // arrange
        request.setAddNumber("***********");
        List<Long> ids = Collections.singletonList(1L);
        when(mobileAddFriendTaskDOMapper.queryPageMobileAddFriendTaskIds(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(ids);
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(Collections.singletonList(taskDO));
        when(phoneEncryptService.decryptUTF8String(anyString())).thenReturn("***********");
        // act
        List<ScrmMobileAddFriendTaskDO> result = mobileAddFriendRealTimeDomainService.queryPageMobileAddFriendRealTimeTask(request);
        // assert
        assertEquals(1, result.size());
        assertEquals("***********", result.get(0).getAddNumber());
        verify(mobileAddFriendTaskDOMapper).queryPageMobileAddFriendTaskIds(any(ScrmMobileAddFriendTaskDOExample.class));
    }

    /**
     * 测试多结果分页查询场景
     */
    @Test
    void testQueryPageMobileAddFriendRealTimeTaskWithMultipleResults() throws Throwable {
        // arrange
        ScrmMobileAddFriendTaskDO taskDO2 = new ScrmMobileAddFriendTaskDO();
        taskDO2.setId(2L);
        taskDO2.setAppId("testApp");
        taskDO2.setAddNumber("encryptedNumber2");
        taskDO2.setAddNumberMd5("md5Hash2");
        taskDO2.setNumberType(1);
        List<Long> ids = Arrays.asList(1L, 2L);
        when(mobileAddFriendTaskDOMapper.queryPageMobileAddFriendTaskIds(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(ids);
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(Arrays.asList(taskDO, taskDO2));
        when(phoneEncryptService.decryptUTF8String(anyString())).thenReturn("***********").thenReturn("13800138001");
        // act
        List<ScrmMobileAddFriendTaskDO> result = mobileAddFriendRealTimeDomainService.queryPageMobileAddFriendRealTimeTask(request);
        // assert
        assertEquals(2, result.size());
        assertEquals("***********", result.get(0).getAddNumber());
        assertEquals("13800138001", result.get(1).getAddNumber());
        verify(mobileAddFriendTaskDOMapper).queryPageMobileAddFriendTaskIds(any(ScrmMobileAddFriendTaskDOExample.class));
    }
}
