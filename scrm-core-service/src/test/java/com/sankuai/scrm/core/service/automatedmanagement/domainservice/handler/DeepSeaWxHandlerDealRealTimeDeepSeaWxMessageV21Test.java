package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.*;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.BizDistributorServiceKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductInfoService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.message.push.dto.*;
import com.sankuai.scrm.core.service.message.push.request.*;
import com.sankuai.scrm.core.service.message.push.response.*;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.content.*;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import java.util.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DeepSeaWxHandlerDealRealTimeDeepSeaWxMessageV21Test {

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private ProductInfoService productInfoService;

    @Mock
    private MsgUnifiedPushService msgUnifiedPushService;

    @InjectMocks
    private DeepSeaWxHandler deepSeaWxHandler;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private List<String> executorIds;

    private InvokeDetailKeyObject keyObject;

    private ExecuteManagementDTO executeManagementDTO;

    private ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO;

    private ScrmProcessOrchestrationNodeMediumDTO mediumDTO;

    @BeforeEach
    void setUp() {
        // Initialize processOrchestrationDTO
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("test-app");
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        // Initialize mediumDTO
        mediumDTO = mock(ScrmProcessOrchestrationNodeMediumDTO.class);
        processOrchestrationDTO.setNodeMediumDTO(mediumDTO);
        // Initialize executorIds and keyObject
        executorIds = Arrays.asList("executor1");
        keyObject = new InvokeDetailKeyObject("TEST_TYPE", (byte) 1, (byte) 1, 1L);
        // Initialize executeManagementDTO
        executeManagementDTO = new ExecuteManagementDTO();
        executeManagementDTO.setPoiId("123");
        // Initialize detailDO
        detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setId(1L);
        detailDO.setProcessOrchestrationId(1L);
        detailDO.setProcessOrchestrationNodeId(1L);
        detailDO.setExecuteLogId(1L);
        detailDO.setTargetId("target1");
        detailDO.setStatus(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_CREATE.getValue().byteValue());
    }

    /**
     * Test empty invoke details list
     */
    @Test
    public void testDealRealTimeDeepSeaWxMessageV2WithEmptyDetails() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> emptyDetails = Collections.emptyList();
        // act
        deepSeaWxHandler.dealRealTimeDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, keyObject, emptyDetails, executeManagementDTO);
        // assert
        verifyNoInteractions(wxInvokeDetailDOMapper);
        verifyNoInteractions(wxInvokeLogDOMapper);
        verifyNoInteractions(executeLogDOMapper);
    }

    /**
     * Test successful text message sending
     */
    @Test
    public void testDealRealTimeDeepSeaWxMessageV2SuccessfulTextMessage() throws Throwable {
        // arrange
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("Test message");
        when(wxInvokeDetailDOMapper.selectByPrimaryKey(1L)).thenReturn(detailDO);
        when(mediumDTO.getActionDTO(1L)).thenReturn(actionDTO);
        when(mediumDTO.getActionContentDTOList(actionDTO)).thenReturn(Collections.singletonList(contentDTO));
        MsgPushResponse<Long> pushResponse = new MsgPushResponse<>();
        pushResponse.setData(100L);
        pushResponse.setCode(0);
        when(msgUnifiedPushService.saveMsgPushTask(any())).thenReturn(pushResponse);
        MsgPushResponse<List<MsgTaskDetailResultDTO>> queryResponse = new MsgPushResponse<>();
        queryResponse.setCode(0);
        MsgTaskDetailResultDTO resultDTO = new MsgTaskDetailResultDTO();
        resultDTO.setSender("executor1");
        queryResponse.setData(Collections.singletonList(resultDTO));
        when(msgUnifiedPushService.queryTaskResultFromMaster(any())).thenReturn(queryResponse);
        // act
        deepSeaWxHandler.dealRealTimeDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, keyObject, Arrays.asList(detailDO), executeManagementDTO);
        // assert
        verify(wxInvokeDetailDOMapper).selectByPrimaryKey(1L);
        verify(msgUnifiedPushService).saveMsgPushTask(any());
        verify(msgUnifiedPushService).queryTaskResultFromMaster(any());
        verify(wxInvokeLogDOMapper).insertSelective(any());
        verify(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper).updateByExampleSelective(any(), any());
    }
}
