package com.sankuai.scrm.core.service.realtime.task.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import com.sankuai.scrm.core.service.realtime.task.domainservice.SceneDomainService;
import com.sankuai.dz.srcm.realtime.task.response.SceneDeleteResultVO;
import com.sankuai.dz.srcm.realtime.task.request.SceneDeleteRequest;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class SceneServiceImplDeleteSceneTest {

    @InjectMocks
    private SceneServiceImpl sceneService;

    @Mock
    private SceneDomainService sceneDomainService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testDeleteSceneRequestIsNull() {
        RemoteResponse<SceneDeleteResultVO> response = sceneService.deleteScene(null);
        assertEquals("请求参数错误", response.getMsg());
    }

    @Test
    public void testDeleteSceneAppIdIsNull() {
        SceneDeleteRequest request = new SceneDeleteRequest();
        request.setAppId(null);
        RemoteResponse<SceneDeleteResultVO> response = sceneService.deleteScene(request);
        assertEquals("请求参数错误", response.getMsg());
    }

    @Test
    public void testDeleteSceneSceneIdIsNull() {
        SceneDeleteRequest request = new SceneDeleteRequest();
        request.setAppId("appId");
        request.setSceneId(null);
        RemoteResponse<SceneDeleteResultVO> response = sceneService.deleteScene(request);
        assertEquals("请求参数错误", response.getMsg());
    }

    @Test
    public void testDeleteSceneSuccess() {
        SceneDeleteRequest request = new SceneDeleteRequest();
        request.setAppId("appId");
        request.setSceneId(1L);
        when(sceneDomainService.deleteSceneByRequest(request)).thenReturn(SceneDeleteResultVO.builder().success(true).build());
        RemoteResponse<SceneDeleteResultVO> response = sceneService.deleteScene(request);
        assertEquals(true, response.getData().isSuccess());
    }

    @Test
    public void testDeleteSceneFail() {
        SceneDeleteRequest request = new SceneDeleteRequest();
        request.setAppId("appId");
        request.setSceneId(1L);
        when(sceneDomainService.deleteSceneByRequest(request)).thenReturn(SceneDeleteResultVO.builder().success(false).build());
        RemoteResponse<SceneDeleteResultVO> response = sceneService.deleteScene(request);
        assertEquals(false, response.getData().isSuccess());
    }

    @Test
    public void testDeleteSceneException() {
        SceneDeleteRequest request = new SceneDeleteRequest();
        request.setAppId("appId");
        request.setSceneId(1L);
        when(sceneDomainService.deleteSceneByRequest(request)).thenThrow(new RuntimeException("error"));
        RemoteResponse<SceneDeleteResultVO> response = sceneService.deleteScene(request);
        assertEquals("error", response.getMsg());
    }
}
