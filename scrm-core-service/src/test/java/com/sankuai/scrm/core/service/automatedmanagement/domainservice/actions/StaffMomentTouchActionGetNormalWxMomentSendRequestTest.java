package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationAttachmentTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.UploadWxAttachmentAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxMomentSendRequest;
import com.sankuai.scrm.core.service.infrastructure.vo.WechatMediaResult;
import com.sankuai.scrm.core.service.infrastructure.vo.WxMediaType;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class StaffMomentTouchActionGetNormalWxMomentSendRequestTest {

    @InjectMocks
    private StaffMomentTouchAction staffMomentTouchAction;

    @Mock
    private UploadWxAttachmentAcl uploadWxAttachmentAcl;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test getNormalWxMomentSendRequest with non-empty content and empty attachments.
     */
    @Test
    public void testGetNormalWxMomentSendRequest_ContentNotEmptyAttachmentsEmpty() throws Throwable {
        // Arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = mock(ScrmAmProcessOrchestrationExecuteLogDO.class);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        ExecuteManagementDTO executeManagementDTO = mock(ExecuteManagementDTO.class);
        when(executeManagementDTO.getStaffLimitSet()).thenReturn(new HashSet<>());
        StepExecuteResultDTO result = new StepExecuteResultDTO();
        // Mock NodeMediumDTO and its behavior
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = mock(ScrmProcessOrchestrationNodeMediumDTO.class);
        when(processOrchestrationDTO.getNodeMediumDTO()).thenReturn(nodeMediumDTO);
        // Add a contentDTO with content but no attachments
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("Test content");
        contentDTOS.add(contentDTO);
        when(nodeMediumDTO.getActionAttachmentDTOList(any())).thenReturn(new ArrayList<>());
        // Act
        WxMomentSendRequest wxMomentSendRequest = staffMomentTouchAction.getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, contentDTOS, executeManagementDTO, result);
        // Assert
        assertNotNull(wxMomentSendRequest);
        assertTrue(CollectionUtils.isEmpty(wxMomentSendRequest.getAttachments()));
    }

    /**
     * Test getNormalWxMomentSendRequest with null content and attachments.
     */
    @Test
    public void testGetNormalWxMomentSendRequest_NullContentAndAttachments() throws Throwable {
        // Arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = mock(ScrmAmProcessOrchestrationExecuteLogDO.class);
        ExecuteManagementDTO executeManagementDTO = mock(ExecuteManagementDTO.class);
        StepExecuteResultDTO result = new StepExecuteResultDTO();
        // Mock NodeMediumDTO
        when(processOrchestrationDTO.getNodeMediumDTO()).thenReturn(mock(ScrmProcessOrchestrationNodeMediumDTO.class));
        // Act & Assert
        try {
            WxMomentSendRequest wxMomentSendRequest = staffMomentTouchAction.getNormalWxMomentSendRequest(processOrchestrationDTO, executeLogDO, null, executeManagementDTO, result);
            assertNull(wxMomentSendRequest);
        } catch (NullPointerException e) {
            // Expected exception for null content
            assertTrue(true);
        }
    }
}