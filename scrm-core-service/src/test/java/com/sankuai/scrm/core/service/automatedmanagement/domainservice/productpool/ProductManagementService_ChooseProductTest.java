package com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductInfoDTO;
import com.sankuai.medicalcosmetology.product.selectify.api.dto.commonpool.DzAdvisorProductInfo;
import com.sankuai.medicalcosmetology.product.selectify.api.response.PageBaseResponse;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserTag;
import com.sankuai.scrm.core.service.user.domain.ScrmUserTagDomainService;
import com.sankuai.medicalcosmetology.product.selectify.api.service.CommonRecommendService;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductItemsDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductItemsDO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;
import static org.mockito.ArgumentMatchers.any;
import com.google.common.collect.Lists;
import com.sankuai.medicalcosmetology.product.selectify.api.dto.commonpool.CommonProductActionContext;
import com.sankuai.medicalcosmetology.product.selectify.api.enums.SelectPoolActionTypeEnum;
import com.sankuai.medicalcosmetology.product.selectify.api.enums.SelectifySceneTypeEnum;
import com.sankuai.medicalcosmetology.product.selectify.api.service.CommonSelectPoolItemService;
import java.util.ArrayList;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@RunWith(MockitoJUnitRunner.class)
@ExtendWith(MockitoExtension.class)
public class ProductManagementService_ChooseProductTest {

    @InjectMocks
    private ProductManagementService productManagementService;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO;

    @Mock(lenient = true)
    private ScrmUserTagDomainService userTagDomainService;

    @Mock(lenient = true)
    private CommonRecommendService commonRecommendService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductItemsDOMapper productItemsDOMapper;

    private final String unionId = "unionId";

    private final Long userid = 1L;

    private final String appId = "appId";

    @Mock
    private CommonSelectPoolItemService commonSelectPoolItemService;

    @Before
    public void setUp() {
        ScrmUserTag userTag = new ScrmUserTag();
        userTag.setTagValue("1");
        when(userTagDomainService.getScrmUserTag(anyString(), anyString(), anyLong())).thenReturn(Collections.singletonList(userTag));
        when(supplyDetailDTO.getHotTagList()).thenReturn("tag1,tag2");
    }

    // ... (other test methods remain the same)
    @Test
    public void testChooseProductWhenProductInfoIsNotFound() throws Throwable {
        PageBaseResponse<List<DzAdvisorProductInfo>> mockResponse = new PageBaseResponse<>();
        mockResponse.setCode(200);
        DzAdvisorProductInfo productInfo = new DzAdvisorProductInfo();
        productInfo.setMtProductId(1L);
        mockResponse.setData(Collections.singletonList(productInfo));
        when(commonRecommendService.recommend(any())).thenReturn(mockResponse);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        ProductInfoDTO result = productManagementService.chooseProduct(unionId, userid, appId, supplyDetailDTO);
        assertNull(result);
    }

    @Test
    public void testChooseProductWhenUserTagsAreEmpty() throws Throwable {
        when(userTagDomainService.getScrmUserTag(anyString(), anyString(), anyLong())).thenReturn(Collections.emptyList());
        ProductInfoDTO result = productManagementService.chooseProduct(unionId, userid, appId, supplyDetailDTO);
        assertNull(result);
    }

    @Test
    public void testChooseProductWhenRecommendResponseIsNull() throws Throwable {
        when(commonRecommendService.recommend(any())).thenReturn(null);
        ProductInfoDTO result = productManagementService.chooseProduct(unionId, userid, appId, supplyDetailDTO);
        assertNull(result);
    }

    @Test
    public void testChooseProductWhenRecommendResponseIsNotSuccessful() throws Throwable {
        PageBaseResponse<List<DzAdvisorProductInfo>> mockResponse = new PageBaseResponse<>();
        // Assuming non-200 code means not successful
        mockResponse.setCode(500);
        when(commonRecommendService.recommend(any())).thenReturn(mockResponse);
        ProductInfoDTO result = productManagementService.chooseProduct(unionId, userid, appId, supplyDetailDTO);
        assertNull(result);
    }

    @Test
    public void testChooseProductWhenRecommendResponseDataIsEmpty() throws Throwable {
        PageBaseResponse<List<DzAdvisorProductInfo>> mockResponse = new PageBaseResponse<>();
        mockResponse.setCode(200);
        mockResponse.setData(Collections.emptyList());
        when(commonRecommendService.recommend(any())).thenReturn(mockResponse);
        ProductInfoDTO result = productManagementService.chooseProduct(unionId, userid, appId, supplyDetailDTO);
        assertNull(result);
    }

    @Test
    public void testChooseProductWhenProductItemsAreEmpty() throws Throwable {
        PageBaseResponse<List<DzAdvisorProductInfo>> mockResponse = new PageBaseResponse<>();
        mockResponse.setCode(200);
        DzAdvisorProductInfo productInfo = new DzAdvisorProductInfo();
        productInfo.setMtProductId(1L);
        mockResponse.setData(Collections.singletonList(productInfo));
        when(commonRecommendService.recommend(any())).thenReturn(mockResponse);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        ProductInfoDTO result = productManagementService.chooseProduct(unionId, userid, appId, supplyDetailDTO);
        assertNull(result);
    }

    @Test
    public void testDeleteCommonProductPoolsItem_WhenAppIdIsNull() {
        // arrange
        String appId = null;
        Long mtProductId = 1L;
        Long dpProductId = 1L;
        Integer productType = 1;
        List<String> outBizIds = Lists.newArrayList("1");
        // act
        productManagementService.deleteCommonProductPoolsItem(appId, mtProductId, dpProductId, productType, outBizIds);
        // assert
        verify(commonSelectPoolItemService, never()).selectPoolProductAction(any());
    }

    @Test
    public void testDeleteCommonProductPoolsItem_WhenMtProductIdIsNull() {
        // arrange
        String appId = "testApp";
        Long mtProductId = null;
        Long dpProductId = 1L;
        Integer productType = 1;
        List<String> outBizIds = Lists.newArrayList("1");
        // act
        productManagementService.deleteCommonProductPoolsItem(appId, mtProductId, dpProductId, productType, outBizIds);
        // assert
        verify(commonSelectPoolItemService, never()).selectPoolProductAction(any());
    }

    @Test
    public void testDeleteCommonProductPoolsItem_WhenDpProductIdIsNull() {
        // arrange
        String appId = "testApp";
        Long mtProductId = 1L;
        Long dpProductId = null;
        Integer productType = 1;
        List<String> outBizIds = Lists.newArrayList("1");
        // act
        productManagementService.deleteCommonProductPoolsItem(appId, mtProductId, dpProductId, productType, outBizIds);
        // assert
        verify(commonSelectPoolItemService, never()).selectPoolProductAction(any());
    }

    @Test
    public void testDeleteCommonProductPoolsItem_WhenProductTypeIsNull() {
        // arrange
        String appId = "testApp";
        Long mtProductId = 1L;
        Long dpProductId = 1L;
        Integer productType = null;
        List<String> outBizIds = Lists.newArrayList("1");
        // act
        productManagementService.deleteCommonProductPoolsItem(appId, mtProductId, dpProductId, productType, outBizIds);
        // assert
        verify(commonSelectPoolItemService, never()).selectPoolProductAction(any());
    }

    @Test
    public void testDeleteCommonProductPoolsItem_WhenOutBizIdsIsEmpty() {
        // arrange
        String appId = "testApp";
        Long mtProductId = 1L;
        Long dpProductId = 1L;
        Integer productType = 1;
        List<String> outBizIds = new ArrayList<>();
        // act
        productManagementService.deleteCommonProductPoolsItem(appId, mtProductId, dpProductId, productType, outBizIds);
        // assert
        verify(commonSelectPoolItemService, never()).selectPoolProductAction(any());
    }

    @Test
    public void testDeleteCommonProductPoolsItem_WithSingleOutBizId() {
        // arrange
        String appId = "testApp";
        Long mtProductId = 1L;
        Long dpProductId = 1L;
        Integer productType = 1;
        List<String> outBizIds = Lists.newArrayList("1");
        // act
        productManagementService.deleteCommonProductPoolsItem(appId, mtProductId, dpProductId, productType, outBizIds);
        // assert
        verify(commonSelectPoolItemService, times(1)).selectPoolProductAction(any(CommonProductActionContext.class));
    }

    @Test
    public void testDeleteCommonProductPoolsItem_WithMultipleOutBizIds() {
        // arrange
        String appId = "testApp";
        Long mtProductId = 1L;
        Long dpProductId = 1L;
        Integer productType = 1;
        List<String> outBizIds = Lists.newArrayList("1", "2", "3");
        // act
        productManagementService.deleteCommonProductPoolsItem(appId, mtProductId, dpProductId, productType, outBizIds);
        // assert
        verify(commonSelectPoolItemService, times(3)).selectPoolProductAction(any(CommonProductActionContext.class));
    }
}
