package com.sankuai.scrm.core.service.automatedmanagement.utils;

import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.config.ScrmFilterFieldConfigDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.config.ScrmOperatorDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationExecutorDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmSupportedFilterFieldOperatorTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.config.ConfigDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserTag;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.*;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ConditionUtils_IsCrowdPackConditionMatchTest {

    @InjectMocks
    private ConditionUtils conditionUtils;

    @Mock(lenient = true)
    private OperateUtilsV2 operateUtils;

    @Mock(lenient = true)
    private ConfigDomainService configDomainService;

    @Mock(lenient = true)
    private ExecuteManagementService executeManagementService;

    private ScrmCrowdPackDTO packDTO;

    private String unionId;

    private ScrmCrowdPackUpdateStrategyInfoDTO strategyInfoDTO;

    private List<ScrmUserTag> userTagDOS;

    @Before
    public void setUp() {
        packDTO = new ScrmCrowdPackDTO();
        packDTO.setAppId("testAppId");
        unionId = "testUnionId";
        strategyInfoDTO = new ScrmCrowdPackUpdateStrategyInfoDTO();
        ScrmCrowdPackUpdateStrategyDetailDTO strategyDetailDTO = new ScrmCrowdPackUpdateStrategyDetailDTO();
        strategyDetailDTO.setGroupId(1);
        strategyDetailDTO.setFilterFieldId(1L);
        strategyDetailDTO.setOperatorId(1L);
        strategyDetailDTO.setParam(Collections.singletonList("param"));
        strategyInfoDTO.setCrowdPackUpdateStrategy(Collections.singletonList(strategyDetailDTO));
        ScrmProcessOrchestrationExecutorDTO executorDTO = new ScrmProcessOrchestrationExecutorDTO();
        executorDTO.setExecutorType(1);
        executorDTO.setExecutorId("testExecutorId");
        strategyInfoDTO.setCrowdPackUpdateStrategyStaff(Collections.singletonList(executorDTO));
        packDTO.setScrmCrowdPackUpdateStrategyInfoDTO(strategyInfoDTO);
        userTagDOS = Collections.singletonList(new ScrmUserTag(1L, "testAppId", "testCorpId", 1L, "testUnionId", 1L, "testValue", 1, new Date()));
        when(executeManagementService.getUsersGroupList(anyString(), anyString())).thenReturn(Collections.emptyList());
        when(executeManagementService.getContactUserByExternalUnionId(anyString(), anyString())).thenReturn(Collections.emptyList());
        when(executeManagementService.queryStaffSetByDepartmentId(anyString(), any())).thenReturn(new HashSet<>());
        Map<Long, ScrmFilterFieldConfigDTO> filterFieldConfigDTOMap = new HashMap<>();
        ScrmFilterFieldConfigDTO filterFieldConfigDTO = new ScrmFilterFieldConfigDTO();
        filterFieldConfigDTO.setOperatorDTOMap(new HashMap<>());
        filterFieldConfigDTO.getOperatorDTOMap().put(1L, new ScrmOperatorDTO());
        filterFieldConfigDTOMap.put(1L, filterFieldConfigDTO);
        when(configDomainService.getFilterFieldConfigsMapByAppId(anyString())).thenReturn(filterFieldConfigDTOMap);
    }

    @Test
    public void testIsCrowdPackConditionMatchStrategyInfoDTOIsNull() throws Throwable {
        boolean result = conditionUtils.isCrowdPackConditionMatch(packDTO, unionId, null, userTagDOS);
        assertFalse(result);
    }

    @Test
    public void testIsCrowdPackConditionMatchStrategyIsNull() throws Throwable {
        strategyInfoDTO.setCrowdPackUpdateStrategy(null);
        boolean result = conditionUtils.isCrowdPackConditionMatch(packDTO, unionId, strategyInfoDTO, userTagDOS);
        assertFalse(result);
    }

    @Test
    public void testIsCrowdPackConditionMatchFilterFieldConfigDTOIsNull() throws Throwable {
        when(configDomainService.getFilterFieldConfigsMapByAppId(anyString())).thenReturn(new HashMap<>());
        boolean result = conditionUtils.isCrowdPackConditionMatch(packDTO, unionId, strategyInfoDTO, userTagDOS);
        assertFalse(result);
    }

    @Test
    public void testIsCrowdPackConditionMatchOperatorDTOIsNull() throws Throwable {
        Map<Long, ScrmFilterFieldConfigDTO> filterFieldConfigDTOMap = new HashMap<>();
        ScrmFilterFieldConfigDTO filterFieldConfigDTO = new ScrmFilterFieldConfigDTO();
        filterFieldConfigDTO.setOperatorDTOMap(new HashMap<>());
        filterFieldConfigDTOMap.put(1L, filterFieldConfigDTO);
        when(configDomainService.getFilterFieldConfigsMapByAppId(anyString())).thenReturn(filterFieldConfigDTOMap);
        boolean result = conditionUtils.isCrowdPackConditionMatch(packDTO, unionId, strategyInfoDTO, userTagDOS);
        assertFalse(result);
    }

    @Test
    public void testIsCrowdPackConditionMatchEmptyStrategy() throws Throwable {
        strategyInfoDTO.setCrowdPackUpdateStrategy(Collections.emptyList());
        boolean result = conditionUtils.isCrowdPackConditionMatch(packDTO, unionId, strategyInfoDTO, userTagDOS);
        assertFalse(result);
    }

    @Test
    public void testIsCrowdPackConditionMatchUnsupportedFilterField() throws Throwable {
        ScrmCrowdPackUpdateStrategyDetailDTO strategyDetailDTO = new ScrmCrowdPackUpdateStrategyDetailDTO();
        strategyDetailDTO.setGroupId(1);
        // Unsupported filter field
        strategyDetailDTO.setFilterFieldId(999L);
        strategyDetailDTO.setOperatorId(1L);
        strategyDetailDTO.setParam(Collections.singletonList("param"));
        strategyInfoDTO.setCrowdPackUpdateStrategy(Collections.singletonList(strategyDetailDTO));
        boolean result = conditionUtils.isCrowdPackConditionMatch(packDTO, unionId, strategyInfoDTO, userTagDOS);
        assertFalse(result);
    }
}
