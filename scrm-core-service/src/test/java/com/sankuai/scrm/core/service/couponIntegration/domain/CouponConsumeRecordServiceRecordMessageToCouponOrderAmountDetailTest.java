package com.sankuai.scrm.core.service.couponIntegration.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmCouponConsumeRecordMessage;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmCouponOrderAmountDetail;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmCouponOrderAmountDetailExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmCouponOrderAmountDetailMapper;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CouponConsumeRecordServiceRecordMessageToCouponOrderAmountDetailTest {

    @Mock
    private ScrmCouponOrderAmountDetailMapper scrmCouponOrderAmountDetailMapper;

    @InjectMocks
    private CouponConsumeRecordService couponConsumeRecordService;

    private Method recordMessageMethod;

    @BeforeEach
    void setUp() throws Exception {
        recordMessageMethod = CouponConsumeRecordService.class.getDeclaredMethod("recordMessageToCouponOrderAmountDetail", ScrmCouponConsumeRecordMessage.class);
        recordMessageMethod.setAccessible(true);
    }

    private ScrmCouponConsumeRecordMessage createValidRecordMessage() {
        ScrmCouponConsumeRecordMessage message = new ScrmCouponConsumeRecordMessage();
        message.setCouponGroupId("group1");
        message.setUnifiedCouponId("coupon1");
        message.setOrderId("order1");
        message.setOrderAmt(new BigDecimal("100.00"));
        message.setActualPayAmt(new BigDecimal("80.00"));
        return message;
    }

    /**
     * Test when recordMessage is null, method should return early without any operations
     */
    @Test
    public void testRecordMessageToCouponOrderAmountDetail_NullMessage() throws Throwable {
        // arrange - no setup needed for null case
        // act
        recordMessageMethod.invoke(couponConsumeRecordService, (Object) null);
        // assert
        verifyNoInteractions(scrmCouponOrderAmountDetailMapper);
    }

    /**
     * Test when no existing record found (count=0), should insert new record
     */
    @Test
    public void testRecordMessageToCouponOrderAmountDetail_NewRecord() throws Throwable {
        // arrange
        ScrmCouponConsumeRecordMessage message = createValidRecordMessage();
        when(scrmCouponOrderAmountDetailMapper.countByExample(any(ScrmCouponOrderAmountDetailExample.class))).thenReturn(0L);
        // act
        recordMessageMethod.invoke(couponConsumeRecordService, message);
        // assert
        verify(scrmCouponOrderAmountDetailMapper).countByExample(any(ScrmCouponOrderAmountDetailExample.class));
        verify(scrmCouponOrderAmountDetailMapper).insert(any(ScrmCouponOrderAmountDetail.class));
        verify(scrmCouponOrderAmountDetailMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * Test when existing record found (count>0), should update record
     */
    @Test
    public void testRecordMessageToCouponOrderAmountDetail_ExistingRecord() throws Throwable {
        // arrange
        ScrmCouponConsumeRecordMessage message = createValidRecordMessage();
        when(scrmCouponOrderAmountDetailMapper.countByExample(any(ScrmCouponOrderAmountDetailExample.class))).thenReturn(1L);
        // act
        recordMessageMethod.invoke(couponConsumeRecordService, message);
        // assert
        verify(scrmCouponOrderAmountDetailMapper).countByExample(any(ScrmCouponOrderAmountDetailExample.class));
        verify(scrmCouponOrderAmountDetailMapper).updateByExampleSelective(any(ScrmCouponOrderAmountDetail.class), any(ScrmCouponOrderAmountDetailExample.class));
        verify(scrmCouponOrderAmountDetailMapper, never()).insert(any());
    }

    /**
     * Test when database insert fails, should log error but not throw exception
     */
    @Test
    public void testRecordMessageToCouponOrderAmountDetail_InsertFailure() throws Throwable {
        // arrange
        ScrmCouponConsumeRecordMessage message = createValidRecordMessage();
        when(scrmCouponOrderAmountDetailMapper.countByExample(any(ScrmCouponOrderAmountDetailExample.class))).thenReturn(0L);
        // insert returns 0 rows affected
        // insert returns 0 rows affected
        when(scrmCouponOrderAmountDetailMapper.insert(any(ScrmCouponOrderAmountDetail.class))).thenReturn(0);
        // act
        recordMessageMethod.invoke(couponConsumeRecordService, message);
        // assert
        verify(scrmCouponOrderAmountDetailMapper).countByExample(any(ScrmCouponOrderAmountDetailExample.class));
        verify(scrmCouponOrderAmountDetailMapper).insert(any(ScrmCouponOrderAmountDetail.class));
    }

    /**
     * Test when database update fails, should log error but not throw exception
     */
    @Test
    public void testRecordMessageToCouponOrderAmountDetail_UpdateFailure() throws Throwable {
        // arrange
        ScrmCouponConsumeRecordMessage message = createValidRecordMessage();
        when(scrmCouponOrderAmountDetailMapper.countByExample(any(ScrmCouponOrderAmountDetailExample.class))).thenReturn(1L);
        // update returns 0 rows affected
        // update returns 0 rows affected
        when(scrmCouponOrderAmountDetailMapper.updateByExampleSelective(any(ScrmCouponOrderAmountDetail.class), any(ScrmCouponOrderAmountDetailExample.class))).thenReturn(0);
        // act
        recordMessageMethod.invoke(couponConsumeRecordService, message);
        // assert
        verify(scrmCouponOrderAmountDetailMapper).countByExample(any(ScrmCouponOrderAmountDetailExample.class));
        verify(scrmCouponOrderAmountDetailMapper).updateByExampleSelective(any(ScrmCouponOrderAmountDetail.class), any(ScrmCouponOrderAmountDetailExample.class));
    }

    /**
     * Test with minimum required fields in record message
     */
    @Test
    public void testRecordMessageToCouponOrderAmountDetail_MinimumFields() throws Throwable {
        // arrange
        ScrmCouponConsumeRecordMessage message = new ScrmCouponConsumeRecordMessage();
        message.setCouponGroupId("group1");
        message.setUnifiedCouponId("coupon1");
        message.setOrderId("order1");
        when(scrmCouponOrderAmountDetailMapper.countByExample(any(ScrmCouponOrderAmountDetailExample.class))).thenReturn(0L);
        // act
        recordMessageMethod.invoke(couponConsumeRecordService, message);
        // assert
        verify(scrmCouponOrderAmountDetailMapper).countByExample(any(ScrmCouponOrderAmountDetailExample.class));
        verify(scrmCouponOrderAmountDetailMapper).insert(argThat(detail -> detail.getCouponGroupId().equals("group1") && detail.getUnifiedCouponId().equals("coupon1") && detail.getOrderId().equals("order1") && detail.getOrderAmount() == null && detail.getActualPayAmount() == null));
    }
}
