package com.sankuai.scrm.core.service.realtime.task.mq.producer;

import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineDispatchMessageDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GroupRetailAiEngineDispatchProducerSendMessageTest {

    @Mock
    private IProducerProcessor producer;

    @InjectMocks
    private GroupRetailAiEngineDispatchProducer producerUnderTest;

    private GroupRetailAiEngineDispatchMessageDTO message;

    @BeforeEach
    void setUp() throws Exception {
        message = new GroupRetailAiEngineDispatchMessageDTO();
        message.setUserId(123L);
        message.setAppId("testApp");
        // 使用反射设置私有静态字段
        Field producerField = GroupRetailAiEngineDispatchProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, producer);
        // 重置producer的所有交互
        reset(producer);
    }

    @BeforeEach
    void tearDown() throws Exception {
        // 测试结束后重置静态字段
        Field producerField = GroupRetailAiEngineDispatchProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, null);
    }

    /**
     * 测试输入参数为null时方法直接返回
     */
    @Test
    public void testSendMessageWhenInputIsNull() throws Throwable {
        // arrange - no setup needed
        // act
        producerUnderTest.sendMessage(null);
        // assert
        verifyNoInteractions(producer);
    }

    /**
     * 测试第一次发送就成功的情况
     */
    @Test
    public void testSendMessageSuccessOnFirstTry() throws Throwable {
        // arrange
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(producer.sendDelayMessage(anyString(), anyLong())).thenReturn(successResult);
        // act
        producerUnderTest.sendMessage(message);
        // assert
        verify(producer, times(1)).sendDelayMessage(anyString(),anyLong());
        verifyNoMoreInteractions(producer);
    }

    /**
     * 测试需要重试但最终成功的情况
     */
    @Test
    public void testSendMessageSuccessAfterRetry() throws Throwable {
        // arrange
        ProducerResult failureResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(producer.sendDelayMessage(anyString(), anyLong())).thenReturn(failureResult).thenReturn(failureResult).thenReturn(successResult);
        // act
        producerUnderTest.sendMessage(message);
        // assert
        verify(producer, times(3)).sendDelayMessage(anyString(),anyLong());
    }

    /**
     * 测试重试3次都失败的情况
     */
    @Test
    public void testSendMessageFailAfterAllRetries() throws Throwable {
        // arrange
        ProducerResult failureResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        when(producer.sendDelayMessage(anyString(), anyLong())).thenReturn(failureResult);
        // act
        producerUnderTest.sendMessage(message);
        // assert
        verify(producer, times(3)).sendDelayMessage(anyString(),anyLong());
    }

    /**
     * 测试发送过程中抛出异常的情况
     */
    @Test
    public void testSendMessageWithException() throws Throwable {
        // arrange
        when(producer.sendDelayMessage(anyString(), anyLong())).thenThrow(new RuntimeException("Test exception"));
        // act
        producerUnderTest.sendMessage(message);
        // assert
        verify(producer, times(3)).sendDelayMessage(anyString(),anyLong());
    }

    /**
     * 测试序列化失败的情况
     */
    @Test
    public void testSendMessageWithSerializationFailure() throws Throwable {
        // arrange
        try (MockedStatic<JsonUtils> mockedJsonUtils = mockStatic(JsonUtils.class)) {
            mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenThrow(new RuntimeException("Serialization failed"));
            // act
            producerUnderTest.sendMessage(message);
            // assert
            verifyNoInteractions(producer);
        }
    }
}
