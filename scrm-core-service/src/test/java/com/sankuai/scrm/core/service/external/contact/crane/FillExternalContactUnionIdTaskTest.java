package com.sankuai.scrm.core.service.external.contact.crane;

import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class FillExternalContactUnionIdTaskTest {

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @InjectMocks
    private FillExternalContactUnionIdTask fillExternalContactUnionIdTask;

    /**
     * Test case: when configs list is empty
     * Expected: method returns early without processing
     */
    @Test
    public void testFillContactUserUnionId_EmptyConfigs() {
        // arrange
        when(appConfigRepository.getConfigs()).thenReturn(new ArrayList<>());
        // act
        fillExternalContactUnionIdTask.fillContactUserUnionId();
        // assert
        verify(appConfigRepository).getConfigs();
        verifyNoMoreInteractions(appConfigRepository);
    }

    /**
     * Test case: when configs list is null
     * Expected: method returns early without processing
     */
    @Test
    public void testFillContactUserUnionId_NullConfigs() {
        // arrange
        when(appConfigRepository.getConfigs()).thenReturn(null);
        // act
        fillExternalContactUnionIdTask.fillContactUserUnionId();
        // assert
        verify(appConfigRepository).getConfigs();
        verifyNoMoreInteractions(appConfigRepository);
    }

    /**
     * Test case: when configs list contains multiple valid configs
     * Expected: fillContactUserUnionId is called for each corpId
     */
    @Test
    public void testFillContactUserUnionId_MultipleConfigs() {
        // arrange
        List<CorpAppConfig> configs = Arrays.asList(createCorpAppConfig("corp1"), createCorpAppConfig("corp2"), createCorpAppConfig("corp3"));
        when(appConfigRepository.getConfigs()).thenReturn(configs);
        FillExternalContactUnionIdTask spyTask = spy(fillExternalContactUnionIdTask);
        doNothing().when(spyTask).fillContactUserUnionId(anyString());
        // act
        spyTask.fillContactUserUnionId();
        // assert
        verify(appConfigRepository).getConfigs();
        verify(spyTask).fillContactUserUnionId("corp1");
        verify(spyTask).fillContactUserUnionId("corp2");
        verify(spyTask).fillContactUserUnionId("corp3");
    }

    /**
     * Test case: when configs list contains single valid config
     * Expected: fillContactUserUnionId is called once with correct corpId
     */
    @Test
    public void testFillContactUserUnionId_SingleConfig() {
        // arrange
        CorpAppConfig config = createCorpAppConfig("corp1");
        List<CorpAppConfig> configs = Arrays.asList(config);
        when(appConfigRepository.getConfigs()).thenReturn(configs);
        FillExternalContactUnionIdTask spyTask = spy(fillExternalContactUnionIdTask);
        doNothing().when(spyTask).fillContactUserUnionId(anyString());
        // act
        spyTask.fillContactUserUnionId();
        // assert
        verify(appConfigRepository).getConfigs();
        verify(spyTask).fillContactUserUnionId("corp1");
        verify(spyTask, times(1)).fillContactUserUnionId(anyString());
    }

    /**
     * Helper method to create CorpAppConfig instance
     */
    private CorpAppConfig createCorpAppConfig(String corpId) {
        CorpAppConfig config = new CorpAppConfig();
        config.setCorpId(corpId);
        return config;
    }
}
