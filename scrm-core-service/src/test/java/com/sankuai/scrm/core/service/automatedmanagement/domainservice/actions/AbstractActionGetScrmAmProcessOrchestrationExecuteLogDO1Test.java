package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationExecutePlanDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;

class AbstractActionGetScrmAmProcessOrchestrationExecuteLogDO1Test {

    private final AbstractAction abstractAction = Mockito.mock(AbstractAction.class, Mockito.CALLS_REAL_METHODS);

    /**
     * Test getScrmAmProcessOrchestrationExecuteLogDO when existedExecuteLogDO is not null.
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_WithExistedLog() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        // Ensure processOrchestrationId is set
        processOrchestrationDTO.setId(1L);
        // Ensure appId is set
        processOrchestrationDTO.setAppId("appId");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(1L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);
        // Ensure validVersion is set
        processOrchestrationDTO.setValidVersion("validVersion");
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        // Ensure externalUserWxUnionId is set
        scrmCrowdPackDetailInfoDTO.setExternalUserWxUnionId("unionId");
        // Ensure externalUserId is set
        scrmCrowdPackDetailInfoDTO.setExternalUserId(123L);
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        // Ensure nodeId is set
        currentProcessingNode.setNodeId(456L);
        ScrmAmProcessOrchestrationExecuteLogDO existedExecuteLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        // assert
        assertEquals(existedExecuteLogDO, result);
    }

    /**
     * Test getScrmAmProcessOrchestrationExecuteLogDO when participation restrict is true and no logs exist.
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_ParticipationRestrictTrue_NoLogs() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setParticipationRestrict(true);
        // Ensure processOrchestrationId is set
        processOrchestrationDTO.setId(1L);
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(1L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);
        // Ensure appId is set
        processOrchestrationDTO.setAppId("appId");
        // Ensure validVersion is set
        processOrchestrationDTO.setValidVersion("validVersion");
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        // Ensure externalUserWxUnionId is set
        scrmCrowdPackDetailInfoDTO.setExternalUserWxUnionId("unionId");
        // Ensure externalUserId is set
        scrmCrowdPackDetailInfoDTO.setExternalUserId(123L);
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        // Ensure nodeId is set
        currentProcessingNode.setNodeId(456L);
        Mockito.when(abstractAction.getExecuteLogByExample(any())).thenReturn(Collections.emptyList());
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertNotNull(result);
        assertEquals("unionId", result.getTargetUnionId());
        assertEquals(456L, result.getProcessOrchestrationNodeId());
        assertEquals(1L, result.getProcessOrchestrationId());
        assertEquals("validVersion", result.getProcessOrchestrationVersion());
    }

    /**
     * Test getScrmAmProcessOrchestrationExecuteLogDO when participation restrict is false and logs exist but under limit.
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_ParticipationRestrictFalse_LogsUnderLimit() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setParticipationRestrict(false);
        processOrchestrationDTO.setParticipationRestrictionsTimes((byte) 5);
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(1L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);
        // Ensure processOrchestrationId is set
        processOrchestrationDTO.setId(1L);
        // Ensure appId is set
        processOrchestrationDTO.setAppId("appId");
        // Ensure validVersion is set
        processOrchestrationDTO.setValidVersion("validVersion");
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        // Ensure externalUserWxUnionId is set
        scrmCrowdPackDetailInfoDTO.setExternalUserWxUnionId("unionId");
        // Ensure externalUserId is set
        scrmCrowdPackDetailInfoDTO.setExternalUserId(123L);
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        // Ensure nodeId is set
        currentProcessingNode.setNodeId(456L);
        ScrmAmProcessOrchestrationExecuteLogDO logDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        logDO.setAddTime(new Date());
        Mockito.when(abstractAction.getExecuteLogByExample(any())).thenReturn(Arrays.asList(logDO, logDO));
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertNotNull(result);
        assertEquals("unionId", result.getTargetUnionId());
        assertEquals(456L, result.getProcessOrchestrationNodeId());
        assertEquals(1L, result.getProcessOrchestrationId());
        assertEquals("validVersion", result.getProcessOrchestrationVersion());
    }

    /**
     * Test getScrmAmProcessOrchestrationExecuteLogDO when participation restrict is true and logs exist over limit.
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_ParticipationRestrictTrue_LogsOverLimit() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setParticipationRestrict(true);
        processOrchestrationDTO.setParticipationRestrictionsTimes((byte) 1);
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(1L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);
        // Ensure processOrchestrationId is set
        processOrchestrationDTO.setId(1L);
        // Ensure appId is set
        processOrchestrationDTO.setAppId("appId");
        // Ensure validVersion is set
        processOrchestrationDTO.setValidVersion("validVersion");
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        // Ensure externalUserWxUnionId is set
        scrmCrowdPackDetailInfoDTO.setExternalUserWxUnionId("unionId");
        // Ensure externalUserId is set
        scrmCrowdPackDetailInfoDTO.setExternalUserId(123L);
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        // Ensure nodeId is set
        currentProcessingNode.setNodeId(456L);
        ScrmAmProcessOrchestrationExecuteLogDO logDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        logDO.setAddTime(new Date());
        Mockito.when(abstractAction.getExecuteLogByExample(any())).thenReturn(Arrays.asList(logDO, logDO));
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertNull(result);
    }

    /**
     * Test getScrmAmProcessOrchestrationExecuteLogDO when participation restrict is false and logs exist at limit.
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_ParticipationRestrictFalse_LogsAtLimit() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setParticipationRestrict(false);
        processOrchestrationDTO.setParticipationRestrictionsTimes((byte) 2);
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(1L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);
        // Ensure processOrchestrationId is set
        processOrchestrationDTO.setId(1L);
        // Ensure appId is set
        processOrchestrationDTO.setAppId("appId");
        // Ensure validVersion is set
        processOrchestrationDTO.setValidVersion("validVersion");
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        // Ensure externalUserWxUnionId is set
        scrmCrowdPackDetailInfoDTO.setExternalUserWxUnionId("unionId");
        // Ensure externalUserId is set
        scrmCrowdPackDetailInfoDTO.setExternalUserId(123L);
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        // Ensure nodeId is set
        currentProcessingNode.setNodeId(456L);
        ScrmAmProcessOrchestrationExecuteLogDO logDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        logDO.setAddTime(new Date());
        Mockito.when(abstractAction.getExecuteLogByExample(any())).thenReturn(Arrays.asList(logDO, logDO));
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertNull(result);
    }

    /**
     * Test getScrmAmProcessOrchestrationExecuteLogDO when demo scene is true.
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_DemoSceneTrue() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setDemoScene(true);
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(1L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);
        // Ensure processOrchestrationId is set
        processOrchestrationDTO.setId(1L);
        // Ensure appId is set
        processOrchestrationDTO.setAppId("appId");
        // Ensure validVersion is set
        processOrchestrationDTO.setValidVersion("validVersion");
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        // Ensure externalUserWxUnionId is set
        scrmCrowdPackDetailInfoDTO.setExternalUserWxUnionId("unionId");
        // Ensure externalUserId is set
        scrmCrowdPackDetailInfoDTO.setExternalUserId(123L);
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        // Ensure nodeId is set
        currentProcessingNode.setNodeId(456L);
        Mockito.when(abstractAction.getExecuteLogByExample(any())).thenReturn(Collections.emptyList());
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertNotNull(result);
        assertEquals("system", result.getExecutorId());
        assertEquals("unionId", result.getTargetUnionId());
        assertEquals(456L, result.getProcessOrchestrationNodeId());
        assertEquals(1L, result.getProcessOrchestrationId());
        assertEquals("validVersion", result.getProcessOrchestrationVersion());
    }
}
