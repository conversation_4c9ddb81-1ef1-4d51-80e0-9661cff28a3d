package com.sankuai.scrm.core.service.envrequestforwarding.enums;

import com.sankuai.dz.srcm.envrequestforwarding.enums.ResultTypeEnum;
import org.junit.Test;

import static org.junit.Assert.assertEquals;

public class ResultTypeEnumTest {

    /**
     * 测试 getActionTypeByCode 方法，当 code 为 null 时，应返回 UNKNOWN
     */
    @Test
    public void testGetActionTypeByCodeWhenCodeIsNull() throws Throwable {
        // arrange
        Integer code = null;
        // act
        ResultTypeEnum result = ResultTypeEnum.getActionTypeByCode(code);
        // assert
        assertEquals(ResultTypeEnum.UNKNOWN, result);
    }

    /**
     * 测试 getActionTypeByCode 方法，当 code 对应的枚举类型存在时，应返回对应的枚举类型
     */
    @Test
    public void testGetActionTypeByCodeWhenCodeExists() throws Throwable {
        // arrange
        Integer code = 1;
        // act
        ResultTypeEnum result = ResultTypeEnum.getActionTypeByCode(code);
        // assert
        assertEquals(ResultTypeEnum.SUCCESS, result);
    }

    /**
     * 测试 getActionTypeByCode 方法，当 code 对应的枚举类型不存在时，应返回 UNKNOWN
     */
    @Test
    public void testGetActionTypeByCodeWhenCodeNotExists() throws Throwable {
        // arrange
        Integer code = 9999;
        // act
        ResultTypeEnum result = ResultTypeEnum.getActionTypeByCode(code);
        // assert
        assertEquals(ResultTypeEnum.UNKNOWN, result);
    }
}
