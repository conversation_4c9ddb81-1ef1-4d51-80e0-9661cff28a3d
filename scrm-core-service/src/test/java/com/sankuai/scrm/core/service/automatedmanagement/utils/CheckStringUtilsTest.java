package com.sankuai.scrm.core.service.automatedmanagement.utils;

import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class CheckStringUtilsTest {

    /**
     * 测试输入为空字符串时的情况
     */
    @Test
    public void testIsHashCodeWithEmptyString() {
        // arrange
        String input = "";

        // act
        boolean result = CheckStringUtils.isHashCode(input);

        // assert
        assertFalse("输入为空字符串时应返回false", result);
    }

    /**
     * 测试输入仅包含数字时的情况
     */
    @Test
    public void testIsHashCodeWithOnlyNumbers() {
        // arrange
        String input = "123456";

        // act
        boolean result = CheckStringUtils.isHashCode(input);

        // assert
        assertFalse("输入仅包含数字时应返回false", result);
    }

    /**
     * 测试输入包含数字和字母时的情况
     */
    @Test
    public void testIsHashCodeWithNumbersAndLetters() {
        // arrange
        String input = "abc123";

        // act
        boolean result = CheckStringUtils.isHashCode(input);

        // assert
        assertTrue("输入包含数字和字母时应返回true", result);
    }

    /**
     * 测试输入仅包含字母时的情况
     */
    @Test
    public void testIsHashCodeWithOnlyLetters() {
        // arrange
        String input = "abcdef";

        // act
        boolean result = CheckStringUtils.isHashCode(input);

        // assert
        assertTrue("输入仅包含字母时应返回true", result);
    }

    /**
     * 测试输入包含非数字和非字母字符时的情况
     */
    @Test
    public void testIsHashCodeWithSpecialCharacters() {
        // arrange
        String input = "abc123!@#";

        // act
        boolean result = CheckStringUtils.isHashCode(input);

        // assert
        assertFalse("输入包含非数字和非字母字符时应返回false", result);
    }
}
