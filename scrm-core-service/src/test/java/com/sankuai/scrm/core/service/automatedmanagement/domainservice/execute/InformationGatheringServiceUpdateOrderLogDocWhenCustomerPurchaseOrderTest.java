package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.sankuai.scrm.core.service.dashboard.domain.DashBoardESWriteDomainService;
import com.sankuai.scrm.core.service.dashboard.dal.dto.TradeDataDashBoardOrderLogDoc;
import com.sankuai.scrm.core.service.dashboard.constants.DashBoardESIndexConstant;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.slf4j.Logger;
import java.lang.reflect.Method;
import java.util.Date;
import static org.mockito.Mockito.*;
import org.mockito.junit.MockitoJUnitRunner;
import org.mockito.Spy;
import java.lang.reflect.Field;
import org.slf4j.LoggerFactory;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class InformationGatheringServiceUpdateOrderLogDocWhenCustomerPurchaseOrderTest {

    @InjectMocks
    private InformationGatheringService informationGatheringService;

    @Mock
    private DashBoardESWriteDomainService dashBoardESWriteDomainService;

    private UnifiedOrderWithId order;

    private ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;

    @Before
    public void setUp() {
        order = mock(UnifiedOrderWithId.class);
        executeLogDO = mock(ScrmAmProcessOrchestrationExecuteLogDO.class);
    }

    private void invokePrivateMethod(UnifiedOrderWithId order, ScrmAmProcessOrchestrationExecuteLogDO executeLogDO) throws Exception {
        Method method = InformationGatheringService.class.getDeclaredMethod("updateOrderLogDocWhenCustomerPurchaseOrder", UnifiedOrderWithId.class, ScrmAmProcessOrchestrationExecuteLogDO.class);
        method.setAccessible(true);
        method.invoke(informationGatheringService, order, executeLogDO);
    }

    /**
     * 测试正常情况：executeLogDO.getFinalCheckTime() 在 order.getBuySuccessTime() 之后
     */
    @Test
    public void testUpdateOrderLogDocWhenCustomerPurchaseOrder_NarrowCaliberTrue() throws Throwable {
        // arrange
        Date buySuccessTime = new Date(System.currentTimeMillis());
        Date finalCheckTime = new Date(buySuccessTime.getTime() + 1000);
        when(order.getOrderId()).thenReturn(123);
        when(executeLogDO.getTargetUnionId()).thenReturn("unionId123");
        when(executeLogDO.getFinalCheckTime()).thenReturn(finalCheckTime);
        when(order.getBuySuccessTime()).thenReturn(buySuccessTime);
        // act
        invokePrivateMethod(order, executeLogDO);
        // assert
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(eq(DashBoardESIndexConstant.TRADE_DATA_DASHBOARD_ORDER_LOG), argThat(doc -> {
            TradeDataDashBoardOrderLogDoc logDoc = (TradeDataDashBoardOrderLogDoc) doc;
            return logDoc.getId().equals("123-unionId123") && logDoc.getIsNarrowCaliber();
        }), eq("123-unionId123"));
    }

    /**
     * 测试正常情况：executeLogDO.getFinalCheckTime() 在 order.getBuySuccessTime() 之前
     */
    @Test
    public void testUpdateOrderLogDocWhenCustomerPurchaseOrder_NarrowCaliberFalse() throws Throwable {
        // arrange
        Date buySuccessTime = new Date(System.currentTimeMillis());
        Date finalCheckTime = new Date(buySuccessTime.getTime() - 1000);
        when(order.getOrderId()).thenReturn(123);
        when(executeLogDO.getTargetUnionId()).thenReturn("unionId123");
        when(executeLogDO.getFinalCheckTime()).thenReturn(finalCheckTime);
        when(order.getBuySuccessTime()).thenReturn(buySuccessTime);
        // act
        invokePrivateMethod(order, executeLogDO);
        // assert
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(eq(DashBoardESIndexConstant.TRADE_DATA_DASHBOARD_ORDER_LOG), argThat(doc -> {
            TradeDataDashBoardOrderLogDoc logDoc = (TradeDataDashBoardOrderLogDoc) doc;
            return logDoc.getId().equals("123-unionId123") && !logDoc.getIsNarrowCaliber();
        }), eq("123-unionId123"));
    }

    /**
     * 测试异常情况：dashBoardESWriteDomainService.updateESDocByIdSelective 抛出异常
     */
    @Test
    public void testUpdateOrderLogDocWhenCustomerPurchaseOrder_ExceptionThrown() throws Throwable {
        // arrange
        RuntimeException expectedException = new RuntimeException("ES update failed");
        when(order.getOrderId()).thenReturn(123);
        when(executeLogDO.getTargetUnionId()).thenReturn("unionId123");
        when(executeLogDO.getFinalCheckTime()).thenReturn(new Date(System.currentTimeMillis() + 1000));
        when(order.getBuySuccessTime()).thenReturn(new Date());
        doThrow(expectedException).when(dashBoardESWriteDomainService).updateESDocByIdSelective(anyString(), any(TradeDataDashBoardOrderLogDoc.class), anyString());
        // act
        invokePrivateMethod(order, executeLogDO);
        // verify that exception was thrown and caught
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(eq(DashBoardESIndexConstant.TRADE_DATA_DASHBOARD_ORDER_LOG), any(TradeDataDashBoardOrderLogDoc.class), anyString());
    }
}
