package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.response.QueryProcessOrchestrationSuccessRateStatisticsResultVO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationNodeExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationRelatedOrderInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationNodeExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationRelatedOrderInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.Assert.assertNotNull;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mock;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ExecuteReadDomainServiceTest {

    @InjectMocks
    private ExecuteReadDomainService executeReadDomainService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationNodeExecuteLogDOMapper scrmAmProcessOrchestrationNodeExecuteLogDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper scrmAmProcessOrchestrationExecuteLogDOMapper;

    @Mock(lenient = true)
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationRelatedOrderInfoDOMapper scrmAmProcessOrchestrationRelatedOrderInfoDOMapper;

    private final String appId = "appId";

    private final Long processOrchestrationId = 1L;

    private final String processOrchestrationVersion = "1.0.0";

    @Test
    public void testQueryProcessOrchestrationSuccessRateStatisticsWhenNodeExecuteLogDOListIsEmpty() throws Throwable {
        ScrmProcessOrchestrationDTO mockProcessOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        when(scrmAmProcessOrchestrationNodeExecuteLogDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        QueryProcessOrchestrationSuccessRateStatisticsResultVO result = executeReadDomainService.queryProcessOrchestrationSuccessRateStatistics(appId, processOrchestrationId, processOrchestrationVersion);
        assertNull(result);
    }

    @Test
    public void testQueryProcessOrchestrationSuccessRateStatisticsWhenProcessOrchestrationDTOIsNull() throws Throwable {
        QueryProcessOrchestrationSuccessRateStatisticsResultVO result = executeReadDomainService.queryProcessOrchestrationSuccessRateStatistics(appId, processOrchestrationId, processOrchestrationVersion);
        assertNull(result);
    }

    @Test
    public void testQueryProcessOrchestrationSuccessRateStatisticsWhenNodeExecuteLogDOListAndProcessOrchestrationDTOAreNotNull() throws Throwable {
        ScrmProcessOrchestrationDTO mockProcessOrchestrationDTO = mock(ScrmProcessOrchestrationDTO.class);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(processOrchestrationId)).thenReturn(java.util.concurrent.CompletableFuture.completedFuture(mockProcessOrchestrationDTO));
        ScrmAmProcessOrchestrationNodeExecuteLogDO mockNodeExecuteLogDO = new ScrmAmProcessOrchestrationNodeExecuteLogDO();
        // Ensure executeCount is not null
        mockNodeExecuteLogDO.setExecuteCount(1L);
        // Ensure processOrchestrationNodeId is not null
        mockNodeExecuteLogDO.setProcessOrchestrationNodeId(0L);
        when(scrmAmProcessOrchestrationNodeExecuteLogDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(mockNodeExecuteLogDO));
        when(scrmAmProcessOrchestrationExecuteLogDOMapper.countByExample(any())).thenReturn(0L);
        ScrmAmProcessOrchestrationRelatedOrderInfoDO mockRelatedOrderInfoDO = new ScrmAmProcessOrchestrationRelatedOrderInfoDO();
        mockRelatedOrderInfoDO.setOrderTotalAmount(new BigDecimal("100"));
        mockRelatedOrderInfoDO.setUniOrderId("uniOrderId");
        mockRelatedOrderInfoDO.setProcessOrchestrationId(processOrchestrationId);
        mockRelatedOrderInfoDO.setProcessOrchestrationVersion(processOrchestrationVersion);
        mockRelatedOrderInfoDO.setExecuteLogId(1L);
        mockRelatedOrderInfoDO.setTargetMtUserId(1L);
        List<ScrmAmProcessOrchestrationRelatedOrderInfoDO> relatedOrderInfoDOS = new ArrayList<>();
        relatedOrderInfoDOS.add(mockRelatedOrderInfoDO);
        when(scrmAmProcessOrchestrationRelatedOrderInfoDOMapper.selectByExample(any())).thenReturn(relatedOrderInfoDOS);
        QueryProcessOrchestrationSuccessRateStatisticsResultVO result = executeReadDomainService.queryProcessOrchestrationSuccessRateStatistics(appId, processOrchestrationId, processOrchestrationVersion);
        assertNotNull(result);
    }
}
