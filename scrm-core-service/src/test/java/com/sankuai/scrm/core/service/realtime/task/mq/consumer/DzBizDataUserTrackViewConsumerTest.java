package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.dianping.lion.client.Lion;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.aigc.service.config.AISceneABTestConfig;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.realtime.task.dto.ScrmDzBizDataUserTrackDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineABTestRecordMessageProducer;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineUserFootprintDiversionProducer;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Field;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DzBizDataUserTrackViewConsumerTest {

    @Mock
    private IConsumerProcessor mockConsumer;
    @InjectMocks
    private DzBizDataUserTrackViewConsumer consumer;

    @Mock
    private RealTimeTaskConsumerConfig consumerConfig;

    @Mock
    private GroupRetailAiEngineUserFootprintDiversionProducer userFootprintDiversionProducer;

    private MafkaMessage<String> message;

    private MessagetContext messagetContext;

    @Mock
    private AISceneABTestConfig aiSceneABTestConfig;

    @Mock
    private GroupRetailAiEngineABTestRecordMessageProducer abTestRecordMessageProducer;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    private void setPrivateConsumerField(DzBizDataUserTrackViewConsumer instance, IConsumerProcessor value) throws Exception {
        Field field = DzBizDataUserTrackViewConsumer.class.getDeclaredField("consumer");
        field.setAccessible(true);
        field.set(instance, value);
    }

    private IConsumerProcessor getPrivateConsumerField(DzBizDataUserTrackViewConsumer instance) throws Exception {
        Field field = DzBizDataUserTrackViewConsumer.class.getDeclaredField("consumer");
        field.setAccessible(true);
        return (IConsumerProcessor) field.get(instance);
    }

    /**
     * 测试当consumer不为null时，destroy方法会调用close()
     */
    @Test
    public void testDestroyWhenConsumerNotNullThenCloseCalled() throws Throwable {
        // arrange
        DzBizDataUserTrackViewConsumer consumer = new DzBizDataUserTrackViewConsumer();
        setPrivateConsumerField(consumer, mockConsumer);
        // act
        consumer.destroy();
        // assert
        verify(mockConsumer, times(1)).close();
    }

    /**
     * 测试当consumer为null时，destroy方法不会抛出异常且不执行任何操作
     */
    @Test
    public void testDestroyWhenConsumerNullThenNoAction() throws Throwable {
        // arrange
        DzBizDataUserTrackViewConsumer consumer = new DzBizDataUserTrackViewConsumer();
        setPrivateConsumerField(consumer, null);
        // act
        consumer.destroy();
        // assert
        assertNull(getPrivateConsumerField(consumer));
        verifyNoInteractions(mockConsumer);
    }

    /**
     * 测试 recvMessage 方法，当消息体包含黑名单中的页面ID时
     */
    @Test
    public void testRecvMessageWithBlackListedPageId() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, null, "This message contains page ID 42291648");
        MessagetContext messagetContext = new MessagetContext();
        try (MockedStatic<Lion> lionMockedStatic = mockStatic(Lion.class)) {
            lionMockedStatic.when(() -> Lion.getList(any(), any(), any(), any()))
                    .thenReturn(Arrays.asList("42291648", "43048887", "40459248"));
            lionMockedStatic.when(() -> Lion.getBoolean(any(), any(), any()))
                    .thenReturn(true);
            // act
            ConsumeStatus result = (ConsumeStatus) ReflectionTestUtils.invokeMethod(consumer, "recvMessage", message, messagetContext);

            // assert
            org.junit.jupiter.api.Assertions.assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        }
    }

    @BeforeEach
    void setUp() {
        message = new MafkaMessage<>("topic", 0, 0L, "key", "{}");
        messagetContext = new MessagetContext();
    }

    private ConsumeStatus invokeRecvMessage(MafkaMessage<String> message, MessagetContext context) throws Exception {
        Method method = DzBizDataUserTrackViewConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        method.setAccessible(true);
        return (ConsumeStatus) method.invoke(consumer, message, context);
    }

    @Test
    public void testRecvMessageWhenDtoIsNull() throws Throwable {
        // arrange
        message = new MafkaMessage<>("topic", 0, 0L, "key", "invalid json");
        // act
        ConsumeStatus result = invokeRecvMessage(message, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootprintDiversionProducer, never()).sendMessage(any());
    }

    @Test
    public void testRecvMessageWhenMessageIsNull() throws Throwable {
        // act
        ConsumeStatus result = invokeRecvMessage(null, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootprintDiversionProducer, never()).sendMessage(any());
    }

    @Test
    public void testRecvMessage_NullMessageBody() throws Throwable {
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(null);
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(message).getBody();
        verifyNoMoreInteractions(message, consumerConfig, userFootprintDiversionProducer, aiSceneABTestConfig, abTestRecordMessageProducer, mtUserCenterAclService);
    }

    @Test
    public void testRecvMessage_EmptyUserIdList() throws Throwable {
        MafkaMessage<String> message = mock(MafkaMessage.class);
        ScrmDzBizDataUserTrackDTO dto = new ScrmDzBizDataUserTrackDTO();
        dto.setUserid(Collections.emptyList());
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(message).getBody();
        verifyNoMoreInteractions(message, consumerConfig, userFootprintDiversionProducer, aiSceneABTestConfig, abTestRecordMessageProducer, mtUserCenterAclService);
    }

    @Test
    public void testRecvMessage_ExceptionHandling() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenThrow(new RuntimeException("Test exception"));
        // act & assert
        Exception ex = assertThrows(Exception.class, () -> invokeRecvMessage(message, new MessagetContext()));
        Throwable cause = ex;
        boolean found = false;
        while (cause != null) {
            if (cause.getMessage() != null && cause.getMessage().contains("序列化失败")) {
                found = true;
                break;
            }
            cause = cause.getCause();
        }
        assertTrue(found, "异常链中应包含'序列化失败'字样");
    }
}
