package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionContentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGroupSendMessageAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGroupSendMessageResponse;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class OfficialWxHandlerDealAIRecallSupplyOfficialWxMessageTest {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private WxGroupSendMessageAcl wxGroupSendMessageAcl;

    @Mock
    private WeChatTokenAcl weChatTokenAcl;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private String executorId;

    private InvokeDetailKeyObject keyObject;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS;

    private StepExecuteResultDTO stepExecuteResultDTO;

    @Before
    public void setUp() {
        // Setup basic DTOs
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setAppId("testAppId");
        processOrchestrationDTO.setValidVersion("1.0");
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        // Setup ActionDTO and ContentDTO
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setId(1L);
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("Test Content");
        // Setup mocking behavior
        when(nodeMediumDTO.getActionDTO(anyLong())).thenReturn(actionDTO);
        when(nodeMediumDTO.getActionContentDTOList(any(ScrmProcessOrchestrationActionDTO.class))).thenReturn(Arrays.asList(contentDTO));
        executorId = "testExecutorId";
        keyObject = new InvokeDetailKeyObject("testKey", (byte) 1, (byte) 1, 1L);
        totalInvokeDetailDOS = new ArrayList<>();
        stepExecuteResultDTO = new StepExecuteResultDTO();
        // Common setup for executeManagementService
        doNothing().when(executeManagementService).subTaskRunBegin(any(), any());
        doNothing().when(executeManagementService).taskRunFinished(any(), any());
    }

    /**
     * Test empty invoke details list scenario
     */
    @Test
    public void testDealAIRecallSupplyOfficialWxMessage_EmptyInvokeDetails() throws Throwable {
        // arrange
        totalInvokeDetailDOS = new ArrayList<>();
        // act
        officialWxHandler.dealAIRecallSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, stepExecuteResultDTO);
        // assert
        verify(executeManagementService, never()).subTaskRunBegin(any(), any());
        verify(wxInvokeLogDOMapper, never()).insert(any());
    }

    /**
     * Test token failure scenario
     */
    @Test
    public void testDealAIRecallSupplyOfficialWxMessage_TokenFailure() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setProcessOrchestrationNodeId(1L);
        detailDO.setExecuteLogId(1L);
        totalInvokeDetailDOS.add(detailDO);
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("testCorpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(null);
        // act
        officialWxHandler.dealAIRecallSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, stepExecuteResultDTO);
        // assert
        assertFalse(stepExecuteResultDTO.isSuccess());
        assertEquals((long) ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ATTACHMENT_ABNORMALITY.getCode(), (long) stepExecuteResultDTO.getCode());
    }

    /**
     * Test media upload failure scenario
     */
    @Test
    public void testDealAIRecallSupplyOfficialWxMessage_MediaUploadFailure() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setProcessOrchestrationNodeId(1L);
        detailDO.setExecuteLogId(1L);
        totalInvokeDetailDOS.add(detailDO);
        WeChatTokenResult tokenResult = WeChatTokenResult.builder().access_token("testToken").errcode(0).errmsg("success").build();
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("testCorpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(tokenResult);
        // act
        officialWxHandler.dealAIRecallSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, stepExecuteResultDTO);
        // assert
        verify(executeManagementService, atLeastOnce()).taskRunFinished(any(), any());
    }

    /**
     * Test message sending failure scenario
     */
    @Test
    public void testDealAIRecallSupplyOfficialWxMessage_SendingFailure() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setProcessOrchestrationNodeId(1L);
        detailDO.setTargetId("testTargetId");
        detailDO.setExecuteLogId(1L);
        totalInvokeDetailDOS.add(detailDO);
        WeChatTokenResult tokenResult = WeChatTokenResult.builder().access_token("testToken").errcode(0).errmsg("success").build();
        WxGroupSendMessageResponse response = new WxGroupSendMessageResponse();
        response.setErrCode(1);
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("testCorpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(tokenResult);
        // act
        officialWxHandler.dealAIRecallSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, stepExecuteResultDTO);
        // assert
        verify(executeLogDOMapper).updateByExampleSelective(any(), any());
    }
}