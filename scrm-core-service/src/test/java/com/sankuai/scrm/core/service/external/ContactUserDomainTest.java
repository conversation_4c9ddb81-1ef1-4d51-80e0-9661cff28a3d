package com.sankuai.scrm.core.service.external;

import com.google.common.collect.Lists;
import com.sankuai.scrm.core.service.BaseMockTest;
import com.sankuai.scrm.core.service.external.contact.dal.babymapper.ContactUserMapper;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.dal.example.ContactUserExample;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static groovy.util.GroovyTestCase.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.when;
import static org.mockito.Mockito.verify;

/**
 * 功能描述：
 *
 * <AUTHOR>
 * @version 1.0 2024-2024/11/12-10:20
 * @description: TODO
 * @since 1.0
 */
public class ContactUserDomainTest extends BaseMockTest {

    @InjectMocks
    private ContactUserDomain contactUserDomain;

    @Mock
    private ContactUserMapper contactUserMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试所有参数为空时返回空列表
     */
    @Test
    public void testQueryContactUserByUnionIdAndStaffId_AllParamsEmpty_ReturnEmptyList() {
        List<ContactUser> result = contactUserDomain.queryContactUserByUnionIdAndStaffId("", Collections.emptyList(), "", null, null);
        assertEquals(0, result.size());
    }

    /**
     * 测试正常情况下返回用户列表
     */
    @Test
    public void testQueryContactUserByUnionIdAndStaffId_Normal_ReturnUserList() {
        Date startTime = new Date();
        Date endTime = new Date(startTime.getTime() + 10000); // 10秒后
        List<String> unionIds = Arrays.asList("unionId1", "unionId2");
        String corpId = "corpId";
        String staffId = "staffId";

        List<ContactUser> expectedUsers = Lists.newArrayList(new ContactUser());
        when(contactUserMapper.selectByExample(any(ContactUserExample.class))).thenReturn(expectedUsers);

        List<ContactUser> result = contactUserDomain.queryContactUserByUnionIdAndStaffId(corpId, unionIds, staffId, startTime, endTime);
        assertEquals(expectedUsers.size(), result.size());

        verify(contactUserMapper, times(1)).selectByExample(any(ContactUserExample.class));
    }

    /**
     * 测试开始时间为null时的情况
     */
    @Test
    public void testQueryContactUserByUnionIdAndStaffId_StartTimeNull_ReturnUserList() {
        Date endTime = new Date();
        List<String> unionIds = Arrays.asList("unionId1", "unionId2");
        String corpId = "corpId";
        String staffId = "staffId";

        List<ContactUser> expectedUsers = Lists.newArrayList(new ContactUser());
        when(contactUserMapper.selectByExample(any(ContactUserExample.class))).thenReturn(expectedUsers);

        List<ContactUser> result = contactUserDomain.queryContactUserByUnionIdAndStaffId(corpId, unionIds, staffId, null, endTime);
        assertEquals(expectedUsers.size(), result.size());

        verify(contactUserMapper, times(1)).selectByExample(any(ContactUserExample.class));
    }

    /**
     * 测试结束时间为null时的情况
     */
    @Test
    public void testQueryContactUserByUnionIdAndStaffId_EndTimeNull_ReturnUserList() {
        Date startTime = new Date();
        List<String> unionIds = Arrays.asList("unionId1", "unionId2");
        String corpId = "corpId";
        String staffId = "staffId";

        List<ContactUser> expectedUsers = Lists.newArrayList(new ContactUser());
        when(contactUserMapper.selectByExample(any(ContactUserExample.class))).thenReturn(expectedUsers);

        List<ContactUser> result = contactUserDomain.queryContactUserByUnionIdAndStaffId(corpId, unionIds, staffId, startTime, null);
        assertEquals(expectedUsers.size(), result.size());

        verify(contactUserMapper, times(1)).selectByExample(any(ContactUserExample.class));
    }

    /**
     * 测试开始时间和结束时间都为null时的情况
     */
    @Test
    public void testQueryContactUserByUnionIdAndStaffId_StartTimeAndEndTimeNull_ReturnUserList() {
        List<String> unionIds = Arrays.asList("unionId1", "unionId2");
        String corpId = "corpId";
        String staffId = "staffId";

        List<ContactUser> expectedUsers = Lists.newArrayList(new ContactUser());
        when(contactUserMapper.selectByExample(any(ContactUserExample.class))).thenReturn(expectedUsers);

        List<ContactUser> result = contactUserDomain.queryContactUserByUnionIdAndStaffId(corpId, unionIds, staffId, null, null);
        assertEquals(expectedUsers.size(), result.size());

        verify(contactUserMapper, times(1)).selectByExample(any(ContactUserExample.class));
    }
}
