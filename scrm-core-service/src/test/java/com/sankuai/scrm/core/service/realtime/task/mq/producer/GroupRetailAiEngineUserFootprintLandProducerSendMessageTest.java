package com.sankuai.scrm.core.service.realtime.task.mq.producer;

import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineMqMessageDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Field;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

class GroupRetailAiEngineUserFootprintLandProducerSendMessageTest {

    private GroupRetailAiEngineUserFootprintLandProducer producer;

    private static IProducerProcessor mockProducer;

    private MockedStatic<JsonUtils> mockedJsonUtils;

    @BeforeEach
    void setUp() throws Exception {
        mockProducer = mock(IProducerProcessor.class);
        producer = new GroupRetailAiEngineUserFootprintLandProducer();
        // 使用反射设置静态producer字段
        Field field = GroupRetailAiEngineUserFootprintLandProducer.class.getDeclaredField("producer");
        field.setAccessible(true);
        field.set(null, mockProducer);
        mockedJsonUtils = Mockito.mockStatic(JsonUtils.class);
    }

    @AfterEach
    void tearDown() {
        mockedJsonUtils.close();
    }

    /**
     * 测试空消息处理，期望直接返回不发送消息
     */
    @Test
    void testSendMessageWithNullMessage() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO nullMessage = null;
        // act
        producer.sendMessage(nullMessage);
        // assert
        verify(mockProducer, never()).sendMessage(anyString());
    }

    /**
     * 测试一次发送成功场景
     */
    @Test
    void testSendMessageSuccessOnFirstTry() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO message = new GroupRetailAiEngineMqMessageDTO();
        String jsonMessage = "test-json";
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        mockedJsonUtils.when(() -> JsonUtils.toStr(message)).thenReturn(jsonMessage);
        when(mockProducer.sendMessage(jsonMessage)).thenReturn(successResult);
        // act
        producer.sendMessage(message);
        // assert
        verify(mockProducer, times(1)).sendMessage(jsonMessage);
        mockedJsonUtils.verify(() -> JsonUtils.toStr(message), times(1));
    }

    /**
     * 测试重试后成功场景
     */
    @Test
    void testSendMessageSuccessAfterRetry() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO message = new GroupRetailAiEngineMqMessageDTO();
        String jsonMessage = "test-json";
        ProducerResult failResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        mockedJsonUtils.when(() -> JsonUtils.toStr(message)).thenReturn(jsonMessage);
        when(mockProducer.sendMessage(jsonMessage)).thenThrow(new RuntimeException("First fail")).thenReturn(failResult).thenReturn(successResult);
        // act
        producer.sendMessage(message);
        // assert
        verify(mockProducer, times(3)).sendMessage(jsonMessage);
        mockedJsonUtils.verify(() -> JsonUtils.toStr(message), times(3));
    }

    /**
     * 测试达到最大重试次数仍失败场景
     */
    @Test
    void testSendMessageFailAfterMaxRetry() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO message = new GroupRetailAiEngineMqMessageDTO();
        String jsonMessage = "test-json";
        ProducerResult failResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        mockedJsonUtils.when(() -> JsonUtils.toStr(message)).thenReturn(jsonMessage);
        when(mockProducer.sendMessage(jsonMessage)).thenReturn(failResult);
        // act
        producer.sendMessage(message);
        // assert
        verify(mockProducer, times(3)).sendMessage(jsonMessage);
        mockedJsonUtils.verify(() -> JsonUtils.toStr(message), times(3));
    }

    /**
     * 测试序列化异常场景
     */
    @Test
    void testSendMessageWithSerializationError() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO message = new GroupRetailAiEngineMqMessageDTO();
        mockedJsonUtils.when(() -> JsonUtils.toStr(message)).thenThrow(new RuntimeException("Serialization error"));
        // act
        producer.sendMessage(message);
        // assert
        verify(mockProducer, never()).sendMessage(anyString());
        mockedJsonUtils.verify(() -> JsonUtils.toStr(message), times(3));
    }

    /**
     * 测试发送过程中持续抛出异常场景
     */
    @Test
    void testSendMessageWithPersistentException() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO message = new GroupRetailAiEngineMqMessageDTO();
        String jsonMessage = "test-json";
        mockedJsonUtils.when(() -> JsonUtils.toStr(message)).thenReturn(jsonMessage);
        when(mockProducer.sendMessage(jsonMessage)).thenThrow(new RuntimeException("Network error"));
        // act
        producer.sendMessage(message);
        // assert
        verify(mockProducer, times(3)).sendMessage(jsonMessage);
        mockedJsonUtils.verify(() -> JsonUtils.toStr(message), times(3));
    }
}
