package com.sankuai.scrm.core.service.infrastructure.acl.ds;

import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsCorpGroupAclService;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.Assert.assertEquals;
import com.sankuai.service.fe.corp.wx.thrift.CorpWxService;
import org.junit.Before;
import com.sankuai.service.fe.corp.wx.thrift.SetupWelComeMsgResponse;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class DsCorpGroupAclServiceSetGroupWelcomeMsgTest {

    @InjectMocks
    private DsCorpGroupAclService dsCorpGroupAclService;

    @Mock
    private CorpWxService.Iface corpWxService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 setGroupWelcomeMsg 方法，正常情况
     */
    @Test
    public void testSetGroupWelcomeMsgNormal() throws Throwable {
        // arrange
        Integer welcomeMsgId = 1;
        String chatId = "chatId";
        String mobile = "mobile";
        SetupWelComeMsgResponse expectedResponse = new SetupWelComeMsgResponse();
        when(corpWxService.setupWelComeMsg(any())).thenReturn(expectedResponse);
        // act
        SetupWelComeMsgResponse actualResponse = dsCorpGroupAclService.setGroupWelcomeMsg(welcomeMsgId, chatId, mobile);
        // assert
        assertEquals(expectedResponse, actualResponse);
        verify(corpWxService, times(1)).setupWelComeMsg(any());
    }

    /**
     * 测试 setGroupWelcomeMsg 方法，异常情况
     */
    @Test
    public void testSetGroupWelcomeMsgException() throws Throwable {
        // arrange
        Integer welcomeMsgId = 1;
        String chatId = "chatId";
        String mobile = "mobile";
        when(corpWxService.setupWelComeMsg(any())).thenThrow(new RuntimeException());
        // act
        SetupWelComeMsgResponse actualResponse = dsCorpGroupAclService.setGroupWelcomeMsg(welcomeMsgId, chatId, mobile);
        // assert
        assertEquals(new SetupWelComeMsgResponse(), actualResponse);
        verify(corpWxService, times(1)).setupWelComeMsg(any());
    }
}
