package com.sankuai.scrm.core.service.couponIntegration.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import java.text.SimpleDateFormat;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.LocalTime;
import java.time.ZoneId;
import java.util.Date;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DateUtilsTest {

    /**
     * 测试获取7天前开始时间的方法
     */
    @Test
    public void testGetStartOfSevenDaysAgo() throws Throwable {
        // arrange
        LocalDate today = LocalDate.now();
        LocalDate sevenDaysAgo = today.minusDays(7);
        long expectedTime = sevenDaysAgo.atStartOfDay(ZoneId.systemDefault()).toInstant().toEpochMilli();
        // act
        Date actual = DateUtils.getStartOfSevenDaysAgo();
        // assert
        // Direct comparison of the time values
        assertTrue(Math.abs(actual.getTime() - expectedTime) < 60000);
    }

    /**
     * 测试 isToday 方法，当传入的日期为 null 时
     */
    @Test
    public void testIsTodayWhenDateIsNull() throws Throwable {
        // arrange
        Date date = null;
        // act
        boolean result = DateUtils.isToday(date);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 isToday 方法，当传入的日期不为 null，且与今天相等时
     */
    @Test
    public void testIsTodayWhenDateIsNotNullAndEqualsToday() throws Throwable {
        // arrange
        Date date = new Date();
        // act
        boolean result = DateUtils.isToday(date);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 isToday 方法，当传入的日期不为 null，且与今天不相等时
     */
    @Test
    public void testIsTodayWhenDateIsNotNullAndNotEqualsToday() throws Throwable {
        // arrange
        Date date = new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000);
        // act
        boolean result = DateUtils.isToday(date);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 stringToDate 方法，当 dateString 为空时，应返回 null
     */
    @Test
    public void testStringToDateWhenDateStringIsNull() throws Throwable {
        // arrange
        String dateString = null;
        boolean isEndDate = false;
        // act
        Date result = DateUtils.stringToDate(dateString, isEndDate);
        // assert
        assertNull(result);
    }

    /**
     * 测试 stringToDate 方法，当 dateString 不是一个有效的日期字符串时，应抛出 RuntimeException
     */
    @Test(expected = RuntimeException.class)
    public void testStringToDateWhenDateStringIsInvalid() throws Throwable {
        // arrange
        String dateString = "invalid date string";
        boolean isEndDate = false;
        // act
        DateUtils.stringToDate(dateString, isEndDate);
    }

    /**
     * 测试 stringToDate 方法，当 dateString 是一个有效的日期字符串，且 isEndDate 为 true 时，应返回当天的最后一刻
     */
    @Test
    public void testStringToDateWhenDateStringIsValidAndIsEndDateIsTrue() throws Throwable {
        // arrange
        String dateString = "2022-01-01";
        boolean isEndDate = true;
        // act
        Date result = DateUtils.stringToDate(dateString, isEndDate);
        // assert
        assertNotNull(result);
        // Adjust the assertion to compare without considering nanoseconds
        assertEquals(LocalDate.of(2022, 1, 1).atTime(LocalTime.MAX).atZone(ZoneId.systemDefault()).toInstant().toEpochMilli(), result.toInstant().toEpochMilli());
    }

    /**
     * 测试 stringToDate 方法，当 dateString 是一个有效的日期字符串，且 isEndDate 为 false 时，应返回当天的开始时间
     */
    @Test
    public void testStringToDateWhenDateStringIsValidAndIsEndDateIsFalse() throws Throwable {
        // arrange
        String dateString = "2022-01-01";
        boolean isEndDate = false;
        // act
        Date result = DateUtils.stringToDate(dateString, isEndDate);
        // assert
        assertNotNull(result);
        assertEquals(LocalDate.of(2022, 1, 1).atStartOfDay().atZone(ZoneId.systemDefault()).toInstant(), result.toInstant());
    }

    /**
     * 测试 getStartOfToday 方法是否能正确返回当前日期的开始时间
     */
    @Test
    public void testGetStartOfToday() throws Throwable {
        // arrange
        LocalDateTime expected = LocalDate.now().atStartOfDay();
        Date actual = DateUtils.getStartOfToday();
        // act
        // Convert the actual Date to a LocalDateTime
        LocalDateTime actualDateTime = actual.toInstant().atZone(ZoneId.systemDefault()).toLocalDateTime();
        // assert
        assertEquals(expected.getYear(), actualDateTime.getYear());
        assertEquals(expected.getMonth(), actualDateTime.getMonth());
        assertEquals(expected.getDayOfMonth(), actualDateTime.getDayOfMonth());
        assertEquals(0, actualDateTime.getHour());
        assertEquals(0, actualDateTime.getMinute());
        assertEquals(0, actualDateTime.getSecond());
    }

    /**
     * 测试 dateToString 方法，当 date 为 null 时
     */
    @Test
    public void testDateToStringWhenDateIsNull() throws Throwable {
        // arrange
        Date date = null;
        // act
        String result = DateUtils.dateToString(date);
        // assert
        assertNull(result);
    }

    /**
     * 测试 dateToString 方法，当 date 不为 null，且可以正常格式化时
     */
    @Test
    public void testDateToStringWhenDateIsNotNullAndCanFormat() throws Throwable {
        // arrange
        Date date = new Date();
        // act
        String result = DateUtils.dateToString(date);
        // assert
        assertNotNull(result);
        assertTrue(result.matches("\\d{4}-\\d{2}-\\d{2} \\d{2}:\\d{2}:\\d{2}"));
    }

    /**
     * 测试获取昨天开始时间的方法
     */
    @Test
    public void testGetYesterdayStartTime() throws Throwable {
        // arrange
        Date yesterdayStart = DateUtils.getStartTime(1);
        // act
        // 无需执行任何操作
        // assert
        // 验证获取到的昨天开始时间是否不为空
        assertNotNull(yesterdayStart);
    }

    /**
     * 测试 addOneDay 方法，输入日期为 null 的情况
     */
    @Test
    public void testAddOneDayNullDate() throws Throwable {
        // arrange
        Date date = null;
        // act
        Date result = DateUtils.addOneDay(date);
        // assert
        assertNull(result);
    }

    /**
     * 测试 addOneDay 方法，输入日期不为 null 的情况
     */
    @Test
    public void testAddOneDayNonNullDate() throws Throwable {
        // arrange
        Date date = new Date();
        // act
        Date result = DateUtils.addOneDay(date);
        // assert
        assertNotNull(result);
        assertTrue(result.getTime() > date.getTime());
    }
}