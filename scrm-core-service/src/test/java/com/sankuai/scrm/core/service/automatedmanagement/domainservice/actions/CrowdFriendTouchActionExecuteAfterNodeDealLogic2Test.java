package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.DeepSeaWxHandler;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.OfficialWxHandler;
import com.sankuai.scrm.core.service.message.push.constant.MsgPushConstant;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class CrowdFriendTouchActionExecuteAfterNodeDealLogic2Test {

    @InjectMocks
    private CrowdFriendTouchAction crowdFriendTouchAction;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private DeepSeaWxHandler deepSeaWxHandler;

    @Mock
    private OfficialWxHandler officialWxHandler;

    private MockedStatic<Lion> lionMock;

    private MockedStatic<Environment> environmentMock;

    @BeforeEach
    void setUp() {
        lionMock = mockStatic(Lion.class);
        environmentMock = mockStatic(Environment.class);
        when(Environment.getAppName()).thenReturn("test-app");
    }

    @AfterEach
    void tearDown() {
        lionMock.close();
        environmentMock.close();
    }

    private ScrmProcessOrchestrationDTO createValidProcessOrchestrationDTO() {
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        // Required field
        dto.setId(12345L);
        // Required field
        dto.setValidVersion("1.0");
        // Required field
        dto.setAppId("test-app");
        return dto;
    }

    /**
     * Test case: Empty executorIds list should return early
     */
    @Test
    public void testExecuteAfterNodeDealLogic_EmptyExecutorIds() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createValidProcessOrchestrationDTO();
        List<String> executorIds = Collections.emptyList();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        // act
        crowdFriendTouchAction.executeAfterNodeDealLogic(processOrchestrationDTO, executorIds, executeManagementDTO, stepExecuteResultDTO);
        // assert
        verify(wxInvokeDetailDOMapper, never()).selectByExample(any());
    }

    /**
     * Test case: No invoke details found should continue to next executor
     */
    @Test
    public void testExecuteAfterNodeDealLogic_NoInvokeDetails() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createValidProcessOrchestrationDTO();
        List<String> executorIds = Collections.singletonList("executor1");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(Lion.getBoolean(anyString(), anyString(), anyBoolean())).thenReturn(false);
        // act
        crowdFriendTouchAction.executeAfterNodeDealLogic(processOrchestrationDTO, executorIds, executeManagementDTO, stepExecuteResultDTO);
        // assert
        verify(wxInvokeDetailDOMapper).selectByExample(any());
        verify(deepSeaWxHandler, never()).dealRealTimeDeepSeaWxMessage(any(), any(), any(), any(), any());
        verify(officialWxHandler, never()).dealOfficialWxMessage(any(), any(), any(), any());
    }



    /**
     * Test case: Deep sea message type with old logic
     */
    @Test
    public void testExecuteAfterNodeDealLogic_DeepSeaMessageTypeOldLogic() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createValidProcessOrchestrationDTO();
        // Non-realtime
        processOrchestrationDTO.setProcessOrchestrationType((byte) 1);
        List<String> executorIds = Collections.singletonList("executor1");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> invokeDetails = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setType("deepSeaMessage");
        detail.setId(1L);
        invokeDetails.add(detail);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(invokeDetails);
        when(Lion.getBoolean(anyString(), eq(MsgPushConstant.PUSH_INTEGRATION_SWITCH_LION_KEY), anyBoolean())).thenReturn(false);
        // act
        crowdFriendTouchAction.executeAfterNodeDealLogic(processOrchestrationDTO, executorIds, executeManagementDTO, stepExecuteResultDTO);
        // assert
        verify(deepSeaWxHandler).dealDeepSeaWxMessage(eq(processOrchestrationDTO), eq("executor1"), any(), eq(stepExecuteResultDTO));
    }

    /**
     * Test case: Group message type with old logic
     */
    @Test
    public void testExecuteAfterNodeDealLogic_GroupMessageTypeOldLogic() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createValidProcessOrchestrationDTO();
        // Non-realtime
        processOrchestrationDTO.setProcessOrchestrationType((byte) 1);
        List<String> executorIds = Collections.singletonList("executor1");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> invokeDetails = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setType("groupMessage");
        detail.setId(1L);
        invokeDetails.add(detail);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(invokeDetails);
        when(Lion.getBoolean(anyString(), eq(MsgPushConstant.PUSH_INTEGRATION_SWITCH_LION_KEY), anyBoolean())).thenReturn(false);
        // act
        crowdFriendTouchAction.executeAfterNodeDealLogic(processOrchestrationDTO, executorIds, executeManagementDTO, stepExecuteResultDTO);
        // assert
        verify(officialWxHandler).dealOfficialWxGroupMessage(eq(processOrchestrationDTO), eq("executor1"), any(), eq(stepExecuteResultDTO));
    }
}
