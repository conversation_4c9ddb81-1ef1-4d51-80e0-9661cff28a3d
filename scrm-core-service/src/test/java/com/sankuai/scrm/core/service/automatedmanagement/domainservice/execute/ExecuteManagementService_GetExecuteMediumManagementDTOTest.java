package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationExecutePlanDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.config.ConfigDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.member.dao.mapper.DepartmentMemberInfoMapper;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.utils.DsGroupMessageUtils;
import com.meituan.mdp.boot.starter.mdpcache.core.Cache;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;

import java.util.Date;
import java.lang.reflect.Field;

import static org.junit.Assert.*;
import org.junit.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ExecuteManagementService_GetExecuteMediumManagementDTOTest {

    @Spy
    @InjectMocks
    private ExecuteManagementService executeManagementService;

    @Mock(lenient = true)
    private ConfigDomainService configDomainService;

    @Mock(lenient = true)
    private CorpAppConfigRepository appConfigRepository;

    @Mock(lenient = true)
    private DepartmentMemberInfoMapper departmentMemberInfoMapper;

    @Mock(lenient = true)
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock(lenient = true)
    private DsGroupMessageUtils dsGroupMessageUtils;

    @Mock(lenient = true)
    private Cache localCache;

    private ScrmProcessOrchestrationDTO scrmProcessOrchestrationDTO;

    @Before
    public void setUp() throws Exception {
        scrmProcessOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setTaskStartTime(new Date());
        scrmProcessOrchestrationDTO.setExecutePlanDTO(executePlanDTO);
        scrmProcessOrchestrationDTO.setId(1L);
        scrmProcessOrchestrationDTO.setAppId("testAppId");
        // Inject mocked localCache into executeManagementService
        Field localCacheField = ExecuteManagementService.class.getDeclaredField("localCache");
        localCacheField.setAccessible(true);
        localCacheField.set(executeManagementService, localCache);
    }

    @Test
    public void testGetExecuteMediumManagementDTOWhenScrmProcessOrchestrationDTOIsNull() throws Throwable {
        ExecuteManagementDTO result = executeManagementService.getExecuteMediumManagementDTO(null);
        assertNull(result);
    }
}
