package com.sankuai.scrm.core.service.realtime.task.service;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.realtime.task.dto.PersonaSceneDTO;
import com.sankuai.dz.srcm.realtime.task.dto.SceneSendCouponResponse;
import com.sankuai.dz.srcm.realtime.task.request.SceneSearchByIdRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.persona.PersonaService;
import com.sankuai.scrm.core.service.infrastructure.acl.sso.SsosvOpenApi;
import com.sankuai.scrm.core.service.realtime.task.domainservice.SceneDomainService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

public class SceneServiceImplTest {

    @InjectMocks
    private SceneServiceImpl sceneService;

    @Mock
    private SceneDomainService sceneDomainService;

    @Mock
    private PersonaService personaService;

    @Mock
    private SsosvOpenApi ssosvOpenApi;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试请求参数为null的情况
     */
    @Test
    public void testQueryPersonaSceneByIdRequestIsNull() throws Throwable {
        PageRemoteResponse<PersonaSceneDTO> response = sceneService.queryPersonaSceneById(null);
        assertEquals(400, response.getCode());
        assertEquals("请求参数错误", response.getMsg());
    }

    /**
     * 测试请求参数appId为空的情况
     */
    @Test
    public void testQueryPersonaSceneByIdAppIdIsEmpty() throws Throwable {
        SceneSearchByIdRequest request = new SceneSearchByIdRequest();
        request.setSceneId(1);
        request.setAccessToken("validToken");
        PageRemoteResponse<PersonaSceneDTO> response = sceneService.queryPersonaSceneById(request);
        assertEquals(400, response.getCode());
        assertEquals("请求参数错误", response.getMsg());
    }

    /**
     * 测试accessToken无效的情况
     */
    @Test
    public void testQueryPersonaSceneByIdAccessTokenInvalid() throws Throwable {
        SceneSearchByIdRequest request = new SceneSearchByIdRequest();
        request.setSceneId(1);
        request.setAppId("appId");
        request.setAccessToken("invalidToken");
        when(ssosvOpenApi.queryLoginNameByAccessToken("invalidToken")).thenReturn("");
        PageRemoteResponse<PersonaSceneDTO> response = sceneService.queryPersonaSceneById(request);
        assertEquals(400, response.getCode());
        assertEquals("未登录", response.getMsg());
    }

    /**
     * 测试未查询到数据的情况
     */
    @Test
    public void testQueryPersonaSceneByIdDataNotFound() throws Throwable {
        SceneSearchByIdRequest request = new SceneSearchByIdRequest();
        request.setSceneId(1);
        request.setAppId("appId");
        request.setAccessToken("validToken");
        when(ssosvOpenApi.queryLoginNameByAccessToken("validToken")).thenReturn("misId");
        when(personaService.getPersonaSceneDTO(any(SceneSearchByIdRequest.class), any(String.class))).thenReturn(Collections.emptyList());
        PageRemoteResponse<PersonaSceneDTO> response = sceneService.queryPersonaSceneById(request);
        assertEquals(400, response.getCode());
        assertEquals("未查询到数据", response.getMsg());
    }

    /**
     * 测试查询成功的情况
     */
    @Test
    public void testQueryPersonaSceneByIdSuccess() throws Throwable {
        SceneSearchByIdRequest request = new SceneSearchByIdRequest();
        request.setSceneId(1);
        request.setAppId("appId");
        request.setAccessToken("validToken");
        PersonaSceneDTO personaSceneDTO = new PersonaSceneDTO("desc", 1, "name", 1, "version");
        when(ssosvOpenApi.queryLoginNameByAccessToken("validToken")).thenReturn("misId");
        when(personaService.getPersonaSceneDTO(any(SceneSearchByIdRequest.class), any(String.class))).thenReturn(Arrays.asList(personaSceneDTO));
        PageRemoteResponse<PersonaSceneDTO> response = sceneService.queryPersonaSceneById(request);
        assertEquals(200, response.getCode());
        assertEquals(1, response.getData().size());
        assertEquals("desc", response.getData().get(0).getSceneDesc());
    }

//    /**
//     * 测试 sceneDomainService.sceneSendCoupon 抛出异常的情况
//     */
//    @Test
//    public void testToTestsceneSendCoupon_SceneSendCouponThrowsException() throws Throwable {
//        // arrange
//        String unionId = "unionId";
//        String couponId = "couponId";
//        String appId = "appId";
//        String distributeCode = "distributeCode";
//        String sceneDetail = "sceneDetail";
//        Integer sceneType = 1;
//        // Create a failed response
//        SceneSendCouponResponse failedResponse = SceneSendCouponResponse.builder().success(false).build();
//        when(sceneDomainService.sceneSendCoupon(eq(unionId), eq(couponId), eq(appId), eq(distributeCode), eq(sceneDetail), eq(sceneType),any(), any())).thenReturn(failedResponse);
//        // act
//        RemoteResponse<SceneSendCouponResponse> response = sceneService.toTestsceneSendCoupon(unionId, couponId, appId, distributeCode, sceneDetail, sceneType);
//        // assert
//        assertNotNull(response);
//        assertNotNull(response.getData());
//        assertFalse(response.getData().isSuccess());
//        verify(sceneDomainService).sceneSendCoupon(eq(unionId), eq(couponId), eq(appId), eq(distributeCode), eq(sceneDetail), eq(sceneType), any(), any());
//    }
//
//    /**
//     * 测试 unionId 为空的情况
//     */
//    @Test
//    public void testToTestsceneSendCoupon_UnionIdIsNull() throws Throwable {
//        // arrange
//        String unionId = null;
//        String couponId = "couponId";
//        String appId = "appId";
//        String distributeCode = "distributeCode";
//        String sceneDetail = "sceneDetail";
//        Integer sceneType = 1;
//        // act
//        RemoteResponse<SceneSendCouponResponse> response = sceneService.toTestsceneSendCoupon(unionId, couponId, appId, distributeCode, sceneDetail, sceneType);
//        // assert
//        assertNull(response.getData());
//    }
//
//    /**
//     * 测试 couponId 为空的情况
//     */
//    @Test
//    public void testToTestsceneSendCoupon_CouponIdIsNull() throws Throwable {
//        // arrange
//        String unionId = "unionId";
//        String couponId = null;
//        String appId = "appId";
//        String distributeCode = "distributeCode";
//        String sceneDetail = "sceneDetail";
//        Integer sceneType = 1;
//        // act
//        RemoteResponse<SceneSendCouponResponse> response = sceneService.toTestsceneSendCoupon(unionId, couponId, appId, distributeCode, sceneDetail, sceneType);
//        // assert
//        assertNull(response.getData());
//    }
//
//    /**
//     * 测试 appId 为空的情况
//     */
//    @Test
//    public void testToTestsceneSendCoupon_AppIdIsNull() throws Throwable {
//        // arrange
//        String unionId = "unionId";
//        String couponId = "couponId";
//        String appId = null;
//        String distributeCode = "distributeCode";
//        String sceneDetail = "sceneDetail";
//        Integer sceneType = 1;
//        // act
//        RemoteResponse<SceneSendCouponResponse> response = sceneService.toTestsceneSendCoupon(unionId, couponId, appId, distributeCode, sceneDetail, sceneType);
//        // assert
//        assertNull(response.getData());
//    }
//
//    /**
//     * 测试 distributeCode 为空的情况
//     */
//    @Test
//    public void testToTestsceneSendCoupon_DistributeCodeIsNull() throws Throwable {
//        // arrange
//        String unionId = "unionId";
//        String couponId = "couponId";
//        String appId = "appId";
//        String distributeCode = null;
//        String sceneDetail = "sceneDetail";
//        Integer sceneType = 1;
//        // act
//        RemoteResponse<SceneSendCouponResponse> response = sceneService.toTestsceneSendCoupon(unionId, couponId, appId, distributeCode, sceneDetail, sceneType);
//        // assert
//        assertNull(response.getData());
//    }
//
//    /**
//     * 测试 sceneDomainService.sceneSendCoupon 返回 null 的情况
//     */
//    @Test
//    public void testToTestsceneSendCoupon_SceneSendCouponReturnsNull() throws Throwable {
//        // arrange
//        String unionId = "unionId";
//        String couponId = "couponId";
//        String appId = "appId";
//        String distributeCode = "distributeCode";
//        String sceneDetail = "sceneDetail";
//        Integer sceneType = 1;
//        when(sceneDomainService.sceneSendCoupon(eq(unionId), eq(couponId), eq(appId), eq(distributeCode), eq(sceneDetail), eq(sceneType), any(), any())).thenReturn(null);
//        // act
//        RemoteResponse<SceneSendCouponResponse> response = sceneService.toTestsceneSendCoupon(unionId, couponId, appId, distributeCode, sceneDetail, sceneType);
//        // assert
//        assertNull(response.getData());
//    }
//
//    /**
//     * 测试 sceneDomainService.sceneSendCoupon 返回成功的情况
//     */
//    @Test
//    public void testToTestsceneSendCoupon_SceneSendCouponReturnsSuccess() throws Throwable {
//        // arrange
//        String unionId = "unionId";
//        String couponId = "couponId";
//        String appId = "appId";
//        String distributeCode = "distributeCode";
//        String sceneDetail = "sceneDetail";
//        Integer sceneType = 1;
//        SceneSendCouponResponse sceneSendCouponResponse = SceneSendCouponResponse.builder().success(true).build();
//        when(sceneDomainService.sceneSendCoupon(eq(unionId), eq(couponId), eq(appId), eq(distributeCode), eq(sceneDetail), eq(sceneType), any(), any())).thenReturn(sceneSendCouponResponse);
//        // act
//        RemoteResponse<SceneSendCouponResponse> response = sceneService.toTestsceneSendCoupon(unionId, couponId, appId, distributeCode, sceneDetail, sceneType);
//        // assert
//        assertTrue(response.getData().isSuccess());
//    }
//
//    /**
//     * 测试 sceneDomainService.sceneSendCoupon 返回失败的情况
//     */
//    @Test
//    public void testToTestsceneSendCoupon_SceneSendCouponReturnsFailure() throws Throwable {
//        // arrange
//        String unionId = "unionId";
//        String couponId = "couponId";
//        String appId = "appId";
//        String distributeCode = "distributeCode";
//        String sceneDetail = "sceneDetail";
//        Integer sceneType = 1;
//        SceneSendCouponResponse sceneSendCouponResponse = SceneSendCouponResponse.builder().success(false).build();
//        when(sceneDomainService.sceneSendCoupon(eq(unionId), eq(couponId), eq(appId), eq(distributeCode), eq(sceneDetail), eq(sceneType), any(), any())).thenReturn(sceneSendCouponResponse);
//        // act
//        RemoteResponse<SceneSendCouponResponse> response = sceneService.toTestsceneSendCoupon(unionId, couponId, appId, distributeCode, sceneDetail, sceneType);
//        // assert
//        assertFalse(response.getData().isSuccess());
//    }
}