package com.sankuai.scrm.core.service.activity.fission.service;

import com.sankuai.dz.srcm.activity.fission.dto.data.ActivityDataDTO;
import com.sankuai.dz.srcm.activity.fission.dto.data.CityDataOverviewDTO;
import com.sankuai.dz.srcm.activity.fission.dto.data.GroupDataOverviewDTO;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionInvitationRecord;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmActivityAndDSPersonalWxGroupRelationMapDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmDSPersonalWxGroupInfoDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmDSPersonalWxGroupMemberInfoDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmDSPersonalWxGroupMemberMatchRecordDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmActivityAndDSPersonalWxGroupRelationMapDOMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmDSPersonalWxGroupInfoDOMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmDSPersonalWxGroupMemberInfoDOMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.mapper.ScrmDSPersonalWxGroupMemberMatchRecordDOMapper;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.util.FileBodyBuilder;
import com.sankuai.scrm.core.service.util.model.FileBody;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ActivityDataOverviewServiceImplGetDownloadUrlTest {

    @InjectMocks
    private ActivityDataOverviewServiceImpl service;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private ScrmActivityAndDSPersonalWxGroupRelationMapDOMapper personalWxGroupRelationMapDOMapper;

    @Mock
    private ScrmDSPersonalWxGroupInfoDOMapper personalWxGroupInfoDOMapper;

    @Mock
    private ScrmDSPersonalWxGroupMemberInfoDOMapper personalWxGroupMemberInfoDOMapper;

    @Mock
    private ScrmDSPersonalWxGroupMemberMatchRecordDOMapper memberMatchRecordDOMapper;

    private String validAppId = "validAppId";

    private Long validActivityId = 1L;

    private String validCorpId = "validCorpId";

    private GroupFissionInvitationRecord validRecord;

    private String invokeGetDownloadUrl(ActivityDataDTO dto) throws Exception {
        Method method = ActivityDataOverviewServiceImpl.class.getDeclaredMethod("getDownloadUrl", ActivityDataDTO.class);
        method.setAccessible(true);
        return (String) method.invoke(service, dto);
    }

    @BeforeEach
    void setUp() {
        validRecord = new GroupFissionInvitationRecord();
        validRecord.setInviteeUnionId("invitee1");
        validRecord.setInviterUnionId("inviter1");
        validRecord.setInGroupStatus(true);
    }

    /**
     * Test when input ActivityDataDTO is null
     */
    @Test
    public void testGetDownloadUrl_NullInput() throws Throwable {
        // act
        String result = invokeGetDownloadUrl(null);
        // assert
        assertNull(result);
    }

    /**
     * Test when ActivityDataDTO has only city overview data
     */
    @Test
    public void testGetDownloadUrl_WithCityOverview() throws Throwable {
        // arrange
        ActivityDataDTO dto = new ActivityDataDTO();
        dto.setJoinGroupTotalSize(100);
        dto.setQuitGroupSize(20);
        dto.setGrowthSize(80);
        List<CityDataOverviewDTO> cities = new ArrayList<>();
        cities.add(CityDataOverviewDTO.builder().cityName("Beijing").cityJoinGroupTotalSize(50).cityQuitGroupSize(10).cityGrowthSize(40).build());
        dto.setCityOverview(cities);
        try (MockedStatic<FileBodyBuilder> mocked = mockStatic(FileBodyBuilder.class)) {
            FileBody mockFileBody = new FileBody();
            mockFileBody.setUrl("http://test.url");
            mocked.when(() -> FileBodyBuilder.buildExcelFileBody(anyString(), any())).thenReturn(mockFileBody);
            // act
            String result = invokeGetDownloadUrl(dto);
            // assert
            assertEquals("http://test.url", result);
            mocked.verify(() -> FileBodyBuilder.buildExcelFileBody(anyString(), any()));
        }
    }

    /**
     * Test when ActivityDataDTO has only group overview data
     */
    @Test
    public void testGetDownloadUrl_WithGroupOverview() throws Throwable {
        // arrange
        ActivityDataDTO dto = new ActivityDataDTO();
        dto.setJoinGroupTotalSize(100);
        dto.setQuitGroupSize(20);
        dto.setGrowthSize(80);
        List<GroupDataOverviewDTO> groups = new ArrayList<>();
        groups.add(GroupDataOverviewDTO.builder().groupName("Group1").groupJoinGroupTotalSize(30).groupQuitGroupSize(5).groupGrowthSize(25).build());
        dto.setGroupOverview(groups);
        try (MockedStatic<FileBodyBuilder> mocked = mockStatic(FileBodyBuilder.class)) {
            FileBody mockFileBody = new FileBody();
            mockFileBody.setUrl("http://test.url");
            mocked.when(() -> FileBodyBuilder.buildExcelFileBody(anyString(), any())).thenReturn(mockFileBody);
            // act
            String result = invokeGetDownloadUrl(dto);
            // assert
            assertEquals("http://test.url", result);
            mocked.verify(() -> FileBodyBuilder.buildExcelFileBody(anyString(), any()));
        }
    }

    /**
     * Test when file building fails
     */
    @Test
    public void testGetDownloadUrl_FileBuildFailure() throws Throwable {
        // arrange
        ActivityDataDTO dto = new ActivityDataDTO();
        dto.setJoinGroupTotalSize(100);
        dto.setQuitGroupSize(20);
        dto.setGrowthSize(80);
        try (MockedStatic<FileBodyBuilder> mocked = mockStatic(FileBodyBuilder.class)) {
            mocked.when(() -> FileBodyBuilder.buildExcelFileBody(anyString(), any())).thenThrow(new RuntimeException("Test exception"));
            // act
            String result = invokeGetDownloadUrl(dto);
            // assert
            assertNull(result);
            mocked.verify(() -> FileBodyBuilder.buildExcelFileBody(anyString(), any()));
        }
    }

    /**
     * Test when ActivityDataDTO has empty overview collections
     */
    @Test
    public void testGetDownloadUrl_EmptyOverviewCollections() throws Throwable {
        // arrange
        ActivityDataDTO dto = new ActivityDataDTO();
        dto.setJoinGroupTotalSize(100);
        dto.setQuitGroupSize(20);
        dto.setGrowthSize(80);
        dto.setCityOverview(Collections.emptyList());
        dto.setGroupOverview(Collections.emptyList());
        try (MockedStatic<FileBodyBuilder> mocked = mockStatic(FileBodyBuilder.class)) {
            FileBody mockFileBody = new FileBody();
            mockFileBody.setUrl("http://test.url");
            mocked.when(() -> FileBodyBuilder.buildExcelFileBody(anyString(), any())).thenReturn(mockFileBody);
            // act
            String result = invokeGetDownloadUrl(dto);
            // assert
            assertEquals("http://test.url", result);
            mocked.verify(() -> FileBodyBuilder.buildExcelFileBody(anyString(), any()));
        }
    }

    /**
     * Test when ActivityDataDTO has null overview collections
     */
    @Test
    public void testGetDownloadUrl_NullOverviewCollections() throws Throwable {
        // arrange
        ActivityDataDTO dto = new ActivityDataDTO();
        dto.setJoinGroupTotalSize(100);
        dto.setQuitGroupSize(20);
        dto.setGrowthSize(80);
        dto.setCityOverview(null);
        dto.setGroupOverview(null);
        try (MockedStatic<FileBodyBuilder> mocked = mockStatic(FileBodyBuilder.class)) {
            FileBody mockFileBody = new FileBody();
            mockFileBody.setUrl("http://test.url");
            mocked.when(() -> FileBodyBuilder.buildExcelFileBody(anyString(), any())).thenReturn(mockFileBody);
            // act
            String result = invokeGetDownloadUrl(dto);
            // assert
            assertEquals("http://test.url", result);
            mocked.verify(() -> FileBodyBuilder.buildExcelFileBody(anyString(), any()));
        }
    }

    /**
     * Test when ActivityDataDTO has null items in overview collections
     */
    @Test
    public void testGetDownloadUrl_NullItemsInOverviewCollections() throws Throwable {
        // arrange
        ActivityDataDTO dto = new ActivityDataDTO();
        dto.setJoinGroupTotalSize(100);
        dto.setQuitGroupSize(20);
        dto.setGrowthSize(80);
        List<CityDataOverviewDTO> cities = new ArrayList<>();
        cities.add(null);
        cities.add(CityDataOverviewDTO.builder().cityName("Shanghai").cityJoinGroupTotalSize(50).cityQuitGroupSize(10).cityGrowthSize(40).build());
        dto.setCityOverview(cities);
        try (MockedStatic<FileBodyBuilder> mocked = mockStatic(FileBodyBuilder.class)) {
            FileBody mockFileBody = new FileBody();
            mockFileBody.setUrl("http://test.url");
            mocked.when(() -> FileBodyBuilder.buildExcelFileBody(anyString(), any())).thenReturn(mockFileBody);
            // act
            String result = invokeGetDownloadUrl(dto);
            // assert
            assertEquals("http://test.url", result);
            mocked.verify(() -> FileBodyBuilder.buildExcelFileBody(anyString(), any()));
        }
    }

    /**
     * 测试当inviters为空时返回null
     */
    @Test
    public void testGetActivityDataDTOWithEmptyInviters() throws Throwable {
        // arrange
        List<GroupFissionInvitationRecord> emptyInviters = Collections.emptyList();
        // act
        ActivityDataDTO result = service.getActivityDataDTO(validAppId, validActivityId, false, emptyInviters);
        // assert
        assertNull(result, "当inviters为空时，应返回null");
    }

    /**
     * 测试当appId为空时返回null
     */
    @Test
    public void testGetActivityDataDTOWithBlankAppId() throws Throwable {
        // arrange
        List<GroupFissionInvitationRecord> inviters = Collections.singletonList(validRecord);
        // act
        ActivityDataDTO result = service.getActivityDataDTO(null, validActivityId, false, inviters);
        // assert
        assertNull(result, "当appId为空时，应返回null");
    }

    /**
     * 测试当activityId为空时返回null
     */
    @Test
    public void testGetActivityDataDTOWithNullActivityId() throws Throwable {
        // arrange
        List<GroupFissionInvitationRecord> inviters = Collections.singletonList(validRecord);
        // act
        ActivityDataDTO result = service.getActivityDataDTO(validAppId, null, false, inviters);
        // assert
        assertNull(result, "当activityId为空时，应返回null");
    }

    /**
     * 测试当corpId获取失败时返回null
     */
    @Test
    public void testGetActivityDataDTOWithNullCorpId() throws Throwable {
        // arrange
        List<GroupFissionInvitationRecord> inviters = Collections.singletonList(validRecord);
        when(corpAppConfigRepository.getCorpIdByAppId(anyString())).thenReturn(null);
        // act
        ActivityDataDTO result = service.getActivityDataDTO(validAppId, validActivityId, false, inviters);
        // assert
        assertNull(result, "当corpId获取失败时，应返回null");
    }

    /**
     * 测试当群组ID和名称映射获取失败时返回null
     */
    @Test
    public void testGetActivityDataDTOWithNullGroupIdNameMap() throws Throwable {
        // arrange
        List<GroupFissionInvitationRecord> inviters = Collections.singletonList(validRecord);
        when(corpAppConfigRepository.getCorpIdByAppId(anyString())).thenReturn(validCorpId);
        when(personalWxGroupRelationMapDOMapper.selectByExample(any())).thenReturn(null);
        // act
        ActivityDataDTO result = service.getActivityDataDTO(validAppId, validActivityId, false, inviters);
        // assert
        assertNull(result, "当群组ID和名称映射获取失败时，应返回null");
    }

    /**
     * 测试当unionId和群组ID映射获取失败时返回null
     */
    @Test
    public void testGetActivityDataDTOWithNullUnionIdGroupIdMap() throws Throwable {
        // arrange
        List<GroupFissionInvitationRecord> inviters = Collections.singletonList(validRecord);
        when(corpAppConfigRepository.getCorpIdByAppId(anyString())).thenReturn(validCorpId);
        List<ScrmActivityAndDSPersonalWxGroupRelationMapDO> relationMap = new ArrayList<>();
        relationMap.add(new ScrmActivityAndDSPersonalWxGroupRelationMapDO());
        when(personalWxGroupRelationMapDOMapper.selectByExample(any())).thenReturn(relationMap);
        List<ScrmDSPersonalWxGroupInfoDO> groupInfos = new ArrayList<>();
        groupInfos.add(new ScrmDSPersonalWxGroupInfoDO());
        when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(groupInfos);
        when(memberMatchRecordDOMapper.selectByExample(any())).thenReturn(null);
        // act
        ActivityDataDTO result = service.getActivityDataDTO(validAppId, validActivityId, false, inviters);
        // assert
        assertNull(result, "当unionId和群组ID映射获取失败时，应返回null");
    }

    /**
     * 测试正常情况下的数据处理
     */
    @Test
    public void testGetActivityDataDTOWithValidData() throws Throwable {
        // arrange
        List<GroupFissionInvitationRecord> inviters = Collections.singletonList(validRecord);
        when(corpAppConfigRepository.getCorpIdByAppId(anyString())).thenReturn(validCorpId);
        // 设置群组关系数据
        List<ScrmActivityAndDSPersonalWxGroupRelationMapDO> relationMap = new ArrayList<>();
        ScrmActivityAndDSPersonalWxGroupRelationMapDO relation = new ScrmActivityAndDSPersonalWxGroupRelationMapDO();
        relation.setDsGroupId(1L);
        relationMap.add(relation);
        when(personalWxGroupRelationMapDOMapper.selectByExample(any())).thenReturn(relationMap);
        // 设置群组信息数据
        List<ScrmDSPersonalWxGroupInfoDO> groupInfos = new ArrayList<>();
        ScrmDSPersonalWxGroupInfoDO groupInfo = new ScrmDSPersonalWxGroupInfoDO();
        groupInfo.setDsGroupId(1L);
        groupInfo.setGroupName("Test Group");
        groupInfos.add(groupInfo);
        when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(groupInfos);
        // 设置匹配记录数据
        List<ScrmDSPersonalWxGroupMemberMatchRecordDO> matchRecords = new ArrayList<>();
        ScrmDSPersonalWxGroupMemberMatchRecordDO matchRecord = new ScrmDSPersonalWxGroupMemberMatchRecordDO();
        matchRecord.setWxUnionid("inviter1");
        matchRecord.setDsWxUserId("wxUserId1");
        matchRecords.add(matchRecord);
        when(memberMatchRecordDOMapper.selectByExample(any())).thenReturn(matchRecords);
        // 设置成员信息数据
        List<ScrmDSPersonalWxGroupMemberInfoDO> memberInfos = new ArrayList<>();
        ScrmDSPersonalWxGroupMemberInfoDO memberInfo = new ScrmDSPersonalWxGroupMemberInfoDO();
        memberInfo.setUnionid("inviter1");
        memberInfo.setDsGroupId(1L);
        memberInfos.add(memberInfo);
        when(personalWxGroupMemberInfoDOMapper.selectByExample(any())).thenReturn(memberInfos);
        // act
        ActivityDataDTO result = service.getActivityDataDTO(validAppId, validActivityId, false, inviters);
        // assert
        assertNotNull(result, "正常情况应返回非null结果");
        assertEquals(1, result.getJoinGroupTotalSize(), "入群人数应为1");
        assertEquals(0, result.getQuitGroupSize(), "退群人数应为0");
        assertEquals(1, result.getGrowthSize(), "净增人数应为1");
        assertNotNull(result.getGroupOverview(), "群组概览不应为null");
    }

    /**
     * 测试下载标志为true时生成下载URL
     */
    @Test
    public void testGetActivityDataDTOWithDownloadTrue() throws Throwable {
        // arrange
        List<GroupFissionInvitationRecord> inviters = Collections.singletonList(validRecord);
        when(corpAppConfigRepository.getCorpIdByAppId(anyString())).thenReturn(validCorpId);
        // 设置群组关系数据
        List<ScrmActivityAndDSPersonalWxGroupRelationMapDO> relationMap = new ArrayList<>();
        ScrmActivityAndDSPersonalWxGroupRelationMapDO relation = new ScrmActivityAndDSPersonalWxGroupRelationMapDO();
        relation.setDsGroupId(1L);
        relationMap.add(relation);
        when(personalWxGroupRelationMapDOMapper.selectByExample(any())).thenReturn(relationMap);
        // 设置群组信息数据
        List<ScrmDSPersonalWxGroupInfoDO> groupInfos = new ArrayList<>();
        ScrmDSPersonalWxGroupInfoDO groupInfo = new ScrmDSPersonalWxGroupInfoDO();
        groupInfo.setDsGroupId(1L);
        groupInfo.setGroupName("Test Group");
        groupInfos.add(groupInfo);
        when(personalWxGroupInfoDOMapper.selectByExample(any())).thenReturn(groupInfos);
        // 设置匹配记录数据
        List<ScrmDSPersonalWxGroupMemberMatchRecordDO> matchRecords = new ArrayList<>();
        ScrmDSPersonalWxGroupMemberMatchRecordDO matchRecord = new ScrmDSPersonalWxGroupMemberMatchRecordDO();
        matchRecord.setWxUnionid("inviter1");
        matchRecord.setDsWxUserId("wxUserId1");
        matchRecords.add(matchRecord);
        when(memberMatchRecordDOMapper.selectByExample(any())).thenReturn(matchRecords);
        // 设置成员信息数据
        List<ScrmDSPersonalWxGroupMemberInfoDO> memberInfos = new ArrayList<>();
        ScrmDSPersonalWxGroupMemberInfoDO memberInfo = new ScrmDSPersonalWxGroupMemberInfoDO();
        memberInfo.setUnionid("inviter1");
        memberInfo.setDsGroupId(1L);
        memberInfos.add(memberInfo);
        when(personalWxGroupMemberInfoDOMapper.selectByExample(any())).thenReturn(memberInfos);
        // 使用Mockito.mockStatic来mock静态方法
        try (MockedStatic<FileBodyBuilder> mockedFileBodyBuilder = Mockito.mockStatic(FileBodyBuilder.class)) {
            // 准备mock的FileBody
            FileBody mockFileBody = new FileBody();
            mockFileBody.setUrl("http://mock.download.url");
            // 设置FileBodyBuilder.buildExcelFileBody的mock行为
            mockedFileBodyBuilder.when(() -> FileBodyBuilder.buildExcelFileBody(anyString(), any())).thenReturn(mockFileBody);
            // act
            ActivityDataDTO result = service.getActivityDataDTO(validAppId, validActivityId, true, inviters);
            // assert
            assertNotNull(result, "正常情况应返回非null结果");
            assertEquals("http://mock.download.url", result.getDownloadUrl(), "下载URL应为mock值");
        }
    }
}
