package com.sankuai.scrm.core.service.realtime.task.mq.config;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.ConfigEvent;
import com.dianping.lion.client.ConfigListener;
import com.dianping.lion.client.ConfigRepository;
import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.mdpcache.core.Cache;
import com.sankuai.scrm.core.service.realtime.task.dto.RealTimeTaskConsumerConfigDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class RealTimeTaskConsumerConfigInitTest {

    @Mock
    private Cache mockCache;

    @InjectMocks
    private RealTimeTaskConsumerConfig realTimeTaskConsumerConfig;

    private final String LION_KEY = "real_time_task_consumer_config";

    private final String APP_NAME = "test-app";

    private Method initMethod;

    private Field configDtoField;

    @Mock
    private ConfigRepository mockConfigRepository;

    @BeforeEach
    void setUp() throws Exception {
        // Get private init method via reflection
        initMethod = RealTimeTaskConsumerConfig.class.getDeclaredMethod("init");
        initMethod.setAccessible(true);
        // Get private configDTO field via reflection
        configDtoField = RealTimeTaskConsumerConfig.class.getDeclaredField("configDTO");
        configDtoField.setAccessible(true);
    }

    /**
     * Test normal initialization with valid config
     */
    @Test
    public void testInitNormalCase() throws Throwable {
        try (MockedStatic<Environment> mockedEnvironment = mockStatic(Environment.class);
            MockedStatic<Lion> mockedLion = mockStatic(Lion.class)) {
            RealTimeTaskConsumerConfigDTO expectedConfig = new RealTimeTaskConsumerConfigDTO();
            // Mock static calls
            mockedEnvironment.when(Environment::getAppName).thenReturn(APP_NAME);
            mockedLion.when(() -> Lion.getBean(APP_NAME, LION_KEY, RealTimeTaskConsumerConfigDTO.class)).thenReturn(expectedConfig);
            // Mock void method
            doNothing().when(mockConfigRepository).addConfigListener(eq(LION_KEY), any(ConfigListener.class));
            // Act
            initMethod.invoke(realTimeTaskConsumerConfig);
            // Assert
            RealTimeTaskConsumerConfigDTO actualConfig = (RealTimeTaskConsumerConfigDTO) configDtoField.get(realTimeTaskConsumerConfig);
            assertNotNull(actualConfig);
            assertEquals(expectedConfig, actualConfig);
        }
    }

    /**
     * Test initialization when Lion throws exception
     */
    @Test
    public void testInitWhenLionThrowsException() throws Throwable {
        try (MockedStatic<Environment> mockedEnvironment = mockStatic(Environment.class);
            MockedStatic<Lion> mockedLion = mockStatic(Lion.class)) {
            // Set initial config
            RealTimeTaskConsumerConfigDTO initialConfig = new RealTimeTaskConsumerConfigDTO();
            configDtoField.set(realTimeTaskConsumerConfig, initialConfig);
            // Mock static calls
            mockedEnvironment.when(Environment::getAppName).thenReturn(APP_NAME);
            mockedLion.when(() -> Lion.getBean(APP_NAME, LION_KEY, RealTimeTaskConsumerConfigDTO.class)).thenThrow(new RuntimeException("Lion error"));
            // Act & Assert
            try {
                initMethod.invoke(realTimeTaskConsumerConfig);
            } catch (Exception e) {
                // Expected exception
                assertTrue(e.getCause() instanceof RuntimeException);
                assertEquals("Lion error", e.getCause().getMessage());
            }
            // Verify state remains unchanged
            RealTimeTaskConsumerConfigDTO actualConfig = (RealTimeTaskConsumerConfigDTO) configDtoField.get(realTimeTaskConsumerConfig);
            assertNotNull(actualConfig);
            assertSame(initialConfig, actualConfig);
        }
    }

    /**
     * Test initialization when config logging fails
     */
    @Test
    public void testInitWhenConfigLoggingFails() throws Throwable {
        try (MockedStatic<Environment> mockedEnvironment = mockStatic(Environment.class);
            MockedStatic<Lion> mockedLion = mockStatic(Lion.class);
            MockedStatic<JsonUtils> mockedJsonUtils = mockStatic(JsonUtils.class)) {
            RealTimeTaskConsumerConfigDTO expectedConfig = new RealTimeTaskConsumerConfigDTO();
            // Mock static calls
            mockedEnvironment.when(Environment::getAppName).thenReturn(APP_NAME);
            mockedLion.when(() -> Lion.getBean(APP_NAME, LION_KEY, RealTimeTaskConsumerConfigDTO.class)).thenReturn(expectedConfig);
            mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenThrow(new RuntimeException("JSON error"));
            // Act & Assert
            try {
                initMethod.invoke(realTimeTaskConsumerConfig);
            } catch (Exception e) {
                // Expected exception
                assertTrue(e.getCause() instanceof RuntimeException);
                assertEquals("JSON error", e.getCause().getMessage());
            }
            // Verify config was set despite logging error
            RealTimeTaskConsumerConfigDTO actualConfig = (RealTimeTaskConsumerConfigDTO) configDtoField.get(realTimeTaskConsumerConfig);
            assertNotNull(actualConfig);
            assertEquals(expectedConfig, actualConfig);
        }
    }

    /**
     * Test initialization when cache creation fails
     */
    @Test
    public void testInitWhenCacheCreationFails() throws Throwable {
        try (MockedStatic<Environment> mockedEnvironment = mockStatic(Environment.class);
            MockedStatic<Lion> mockedLion = mockStatic(Lion.class)) {
            RealTimeTaskConsumerConfigDTO expectedConfig = new RealTimeTaskConsumerConfigDTO();
            // Mock static calls
            mockedEnvironment.when(Environment::getAppName).thenReturn(APP_NAME);
            mockedLion.when(() -> Lion.getBean(APP_NAME, LION_KEY, RealTimeTaskConsumerConfigDTO.class)).thenReturn(expectedConfig);
            // Mock void method to throw exception
            doThrow(new RuntimeException("Cache error")).when(mockConfigRepository).addConfigListener(eq(LION_KEY), any(ConfigListener.class));
            // Act & Assert
            try {
                initMethod.invoke(realTimeTaskConsumerConfig);
            } catch (Exception e) {
                // Expected exception
                assertTrue(e.getCause() instanceof RuntimeException);
                assertEquals("Cache error", e.getCause().getMessage());
            }
            // Verify config was set despite cache error
            RealTimeTaskConsumerConfigDTO actualConfig = (RealTimeTaskConsumerConfigDTO) configDtoField.get(realTimeTaskConsumerConfig);
            assertNotNull(actualConfig);
            assertEquals(expectedConfig, actualConfig);
        }
    }
}
