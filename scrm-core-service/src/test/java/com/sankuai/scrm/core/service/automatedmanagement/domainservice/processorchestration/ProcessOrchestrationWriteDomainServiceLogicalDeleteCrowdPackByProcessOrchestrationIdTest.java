package com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.never;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.verifyNoInteractions;
import static org.mockito.Mockito.verifyNoMoreInteractions;
import static org.mockito.Mockito.when;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackAndProcessMapDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackAndProcessMapDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationInfoDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackAndProcessMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import java.util.Arrays;
import java.util.Collections;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmCrowdPackTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.dz.srcm.group.dto.GroupListDTO;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationExecutePlanDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackUpdateLockService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.DdlResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.RefinementOperationExecuteMessageProducer;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.scrm.core.service.util.model.CronModel;
import java.util.*;
import java.util.stream.Collectors;
import org.junit.jupiter.api.BeforeEach;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
class ProcessOrchestrationWriteDomainServiceLogicalDeleteCrowdPackByProcessOrchestrationIdTest {

    @Mock
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock
    private ScrmAmCrowdPackAndProcessMapDOMapper scrmAmCrowdPackAndProcessMapDOMapper;

    @Mock
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @InjectMocks
    private ProcessOrchestrationWriteDomainService processOrchestrationWriteDomainService;

    private static final Long validProcessOrchestrationId = 123L;

    private static final String validVersion = "v1";

    private static final Long validPackId = 456L;

    @Mock
    private ExtScrmAmProcessOrchestrationExecutePlanDOMapper extScrmAmProcessOrchestrationExecutePlanDOMapper;

    @Mock
    private ScrmAMRealtimeSceneAndProcessMapDOMapper scrmAMRealtimeSceneAndProcessMapDOMapper;

    @Mock
    private ScrmProcessOrchestrationConverter scrmProcessOrchestrationConverter;

    @Mock
    private ScrmProcessOrchestrationNodeConverter scrmProcessOrchestrationNodeConverter;

    @Mock
    private ScrmProcessOrchestrationConditionDetailConverter scrmProcessOrchestrationConditionDetailConverter;

    @Mock
    private ScrmProcessOrchestrationActionConverter scrmProcessOrchestrationActionConverter;

    @Mock
    private ScrmProcessOrchestrationActionContentConverter scrmProcessOrchestrationActionContentConverter;

    @Mock
    private ScrmProcessOrchestrationActionAttachmentConverter scrmProcessOrchestrationActionAttachmentConverter;

    @Mock
    private ScrmProcessOrchestrationGoalConverter scrmProcessOrchestrationGoalConverter;

    @Mock
    private ScrmProcessOrchestrationGoalDetailConverter scrmProcessOrchestrationGoalDetailConverter;

    @Mock
    private ScrmAmConfigurationChangeLogDOMapper configurationChangeLogDOMapper;

    @Mock
    private RefinementOperationExecuteMessageProducer refinementOperationExecuteMessageProducer;

    @Mock
    private CrowdPackUpdateLockService crowdPackUpdateLockService;

    @Mock
    private ScrmProcessOrchestrationExecutorConverter scrmProcessOrchestrationExecutorConverter;

    @Mock
    private ScrmAmProcessOrchestrationGoalDOMapper scrmAmProcessOrchestrationGoalDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationGoalDetailDOMapper scrmAmProcessOrchestrationGoalDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationNodeInfoDOMapper scrmAmProcessOrchestrationNodeInfoDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationConditionDOMapper scrmAmProcessOrchestrationConditionDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationActionDOMapper scrmAmProcessOrchestrationActionDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationActionContentDOMapper scrmAmProcessOrchestrationActionContentDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationActionAttachmentDOMapper scrmAmProcessOrchestrationActionAttachmentDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationNodeExecuteLogDOMapper scrmAmProcessOrchestrationNodeExecuteLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecutorLimitDOMapper scrmAmProcessOrchestrationExecutorLimitDOMapper;

    @Spy
    @InjectMocks
    private ProcessOrchestrationWriteDomainService service;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO;

    private ScrmAmProcessOrchestrationInfoDO originDO;

    /**
     * Test when no process orchestration info is found
     */
    @Test
    public void testLogicalDeleteCrowdPackByProcessOrchestrationIdWhenProcessInfoNotFound() throws Throwable {
        // arrange
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class))).thenReturn(Collections.emptyList());
        // act
        processOrchestrationWriteDomainService.logicalDeleteCrowdPackByProcessOrchestrationId(validProcessOrchestrationId);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class));
        verifyNoInteractions(scrmAmCrowdPackAndProcessMapDOMapper);
        verifyNoInteractions(crowdPackWriteDomainService);
    }

    /**
     * Test when process info exists but no mapping is found
     */
    @Test
    public void testLogicalDeleteCrowdPackByProcessOrchestrationIdWhenMappingNotFound() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processInfo = new ScrmAmProcessOrchestrationInfoDO();
        processInfo.setValidVersion(validVersion);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class))).thenReturn(Arrays.asList(processInfo));
        when(scrmAmCrowdPackAndProcessMapDOMapper.selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class))).thenReturn(Collections.emptyList());
        // act
        processOrchestrationWriteDomainService.logicalDeleteCrowdPackByProcessOrchestrationId(validProcessOrchestrationId);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class));
        verify(scrmAmCrowdPackAndProcessMapDOMapper).selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class));
        verifyNoInteractions(crowdPackWriteDomainService);
    }

    /**
     * Test when both process info and mapping exist
     */
    @Test
    public void testLogicalDeleteCrowdPackByProcessOrchestrationIdWhenBothExist() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processInfo = new ScrmAmProcessOrchestrationInfoDO();
        processInfo.setValidVersion(validVersion);
        ScrmAmCrowdPackAndProcessMapDO mapDO = new ScrmAmCrowdPackAndProcessMapDO();
        mapDO.setPackId(validPackId);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class))).thenReturn(Arrays.asList(processInfo));
        when(scrmAmCrowdPackAndProcessMapDOMapper.selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class))).thenReturn(Arrays.asList(mapDO));
        // act
        processOrchestrationWriteDomainService.logicalDeleteCrowdPackByProcessOrchestrationId(validProcessOrchestrationId);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class));
        verify(scrmAmCrowdPackAndProcessMapDOMapper).selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class));
        verify(crowdPackWriteDomainService).logicalDeleteCrowdPack(validPackId);
    }

    /**
     * Test when multiple process infos are found (should use first one)
     */
    @Test
    public void testLogicalDeleteCrowdPackByProcessOrchestrationIdWhenMultipleProcessInfos() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processInfo1 = new ScrmAmProcessOrchestrationInfoDO();
        processInfo1.setValidVersion(validVersion);
        ScrmAmProcessOrchestrationInfoDO processInfo2 = new ScrmAmProcessOrchestrationInfoDO();
        processInfo2.setValidVersion("v2");
        ScrmAmCrowdPackAndProcessMapDO mapDO = new ScrmAmCrowdPackAndProcessMapDO();
        mapDO.setPackId(validPackId);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class))).thenReturn(Arrays.asList(processInfo1, processInfo2));
        when(scrmAmCrowdPackAndProcessMapDOMapper.selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class))).thenReturn(Arrays.asList(mapDO));
        // act
        processOrchestrationWriteDomainService.logicalDeleteCrowdPackByProcessOrchestrationId(validProcessOrchestrationId);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class));
        verify(scrmAmCrowdPackAndProcessMapDOMapper).selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class));
        verify(crowdPackWriteDomainService).logicalDeleteCrowdPack(validPackId);
    }

    /**
     * Test when multiple mappings are found (should use first one)
     */
    @Test
    public void testLogicalDeleteCrowdPackByProcessOrchestrationIdWhenMultipleMappings() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processInfo = new ScrmAmProcessOrchestrationInfoDO();
        processInfo.setValidVersion(validVersion);
        ScrmAmCrowdPackAndProcessMapDO mapDO1 = new ScrmAmCrowdPackAndProcessMapDO();
        mapDO1.setPackId(validPackId);
        ScrmAmCrowdPackAndProcessMapDO mapDO2 = new ScrmAmCrowdPackAndProcessMapDO();
        Long secondPackId = 789L;
        mapDO2.setPackId(secondPackId);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class))).thenReturn(Arrays.asList(processInfo));
        when(scrmAmCrowdPackAndProcessMapDOMapper.selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class))).thenReturn(Arrays.asList(mapDO1, mapDO2));
        // act
        processOrchestrationWriteDomainService.logicalDeleteCrowdPackByProcessOrchestrationId(validProcessOrchestrationId);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class));
        verify(scrmAmCrowdPackAndProcessMapDOMapper).selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class));
        verify(crowdPackWriteDomainService, times(1)).logicalDeleteCrowdPack(validPackId);
        verify(crowdPackWriteDomainService, never()).logicalDeleteCrowdPack(secondPackId);
    }

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setAppId("testApp");
        processOrchestrationDTO.setValidVersion("1.0");
        processOrchestrationDTO.setLastUpdaterId("user1");
        processOrchestrationDTO.setExecutorList(new ArrayList<>());
        // 设置默认的人群包类型，避免空指针异常
        processOrchestrationDTO.setCrowdPackType(0);
        processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setAppId("testApp");
        processOrchestrationInfoDO.setValidVersion("1.0");
        processOrchestrationInfoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_TIMED_TASK.getValue().byteValue());
        originDO = new ScrmAmProcessOrchestrationInfoDO();
        originDO.setId(1L);
        originDO.setAppId("testApp");
        originDO.setValidVersion("0.9");
        originDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.VALID.getValue().byteValue());
    }

    @Test
    public void testUpdateProcessOrchestrationSuccessWithAllComponents() throws Throwable {
        // arrange
        // 使用类型0避免需要人群包列表
        processOrchestrationDTO.setCrowdPackType(0);
        processOrchestrationDTO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_TIMED_TASK.getValue().byteValue());
        // Setup node medium DTO
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(new ArrayList<>());
        nodeMediumDTO.setConditionMap(new HashMap<>());
        nodeMediumDTO.setActionMap(new HashMap<>());
        nodeMediumDTO.setActionContentMap(new HashMap<>());
        nodeMediumDTO.setActionAttachmentMap(new HashMap<>());
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        // Setup goal DTO
        ScrmProcessOrchestrationGoalDTO goalDTO = new ScrmProcessOrchestrationGoalDTO();
        goalDTO.setGoalConditionList(new ArrayList<>());
        processOrchestrationDTO.setGoalDTO(goalDTO);
        // Setup negative goal DTO
        ScrmProcessOrchestrationGoalDTO negativeGoalDTO = new ScrmProcessOrchestrationGoalDTO();
        negativeGoalDTO.setGoalConditionList(new ArrayList<>());
        processOrchestrationDTO.setNegativeGoalDTO(negativeGoalDTO);
        // Setup executors
        List<ScrmProcessOrchestrationExecutorDTO> executorList = new ArrayList<>();
        processOrchestrationDTO.setExecutorList(executorList);
        when(scrmProcessOrchestrationConverter.convertToDO(processOrchestrationDTO)).thenReturn(processOrchestrationInfoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(1L)).thenReturn(originDO);
        when(scrmProcessOrchestrationNodeConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        when(scrmProcessOrchestrationConditionDetailConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        when(scrmProcessOrchestrationActionConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        when(scrmProcessOrchestrationActionContentConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        when(scrmProcessOrchestrationActionAttachmentConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        when(scrmProcessOrchestrationGoalConverter.convertToDO(any())).thenReturn(new ScrmAmProcessOrchestrationGoalDO());
        when(scrmProcessOrchestrationGoalDetailConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        when(scrmProcessOrchestrationExecutorConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        // 直接模拟configurationChangeLogDOMapper.insert方法
        when(configurationChangeLogDOMapper.insert(any())).thenReturn(1);
        // 模拟insertSubassemblies方法，避免空指针异常
        doNothing().when(service).insertSubassemblies(anyList(), any(), anyList(), anyList(), anyList(), anyList(), any(), anyList(), any(), anyList(), anyList(), anyList());
        // act
        DdlResultDTO result = service.updateProcessOrchestration(processOrchestrationDTO);
        // assert
        assertTrue(result.isSuccess());
        assertEquals("编排更新成功", result.getMsg());
        verify(configurationChangeLogDOMapper).insert(any());
    }

    @Test
    public void testUpdateProcessOrchestrationWithTempCrowdPack() throws Throwable {
        // arrange
        processOrchestrationDTO.setCrowdPackType(2);
        processOrchestrationDTO.setName("Test Process");
        processOrchestrationDTO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_TIMED_TASK.getValue().byteValue());
        ScrmCrowdPackDTO crowdPackDTO = new ScrmCrowdPackDTO();
        crowdPackDTO.setName("Test Process零时包");
        crowdPackDTO.setType(ScrmCrowdPackTypeEnum.TEMP_PACK.getValue().byteValue());
        DdlResultDTO createResult = new DdlResultDTO(true, "Created", 100L);
        when(scrmProcessOrchestrationConverter.convertToDO(processOrchestrationDTO)).thenReturn(processOrchestrationInfoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(1L)).thenReturn(originDO);
        when(crowdPackWriteDomainService.createCrowdPack(any())).thenReturn(createResult);
        when(scrmProcessOrchestrationExecutorConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        // 模拟logicalDeleteCrowdPackByProcessOrchestrationId方法
        doNothing().when(service).logicalDeleteCrowdPackByProcessOrchestrationId(anyLong());
        // 直接模拟configurationChangeLogDOMapper.insert方法
        when(configurationChangeLogDOMapper.insert(any())).thenReturn(1);
        // 模拟insertSubassemblies方法，避免空指针异常
        doNothing().when(service).insertSubassemblies(anyList(), any(), anyList(), anyList(), anyList(), anyList(), any(), anyList(), any(), anyList(), anyList(), anyList());
        // act
        DdlResultDTO result = service.updateProcessOrchestration(processOrchestrationDTO);
        // assert
        assertTrue(result.isSuccess());
        assertEquals("编排更新成功", result.getMsg());
        verify(service).logicalDeleteCrowdPackByProcessOrchestrationId(1L);
        verify(crowdPackWriteDomainService).createCrowdPack(any());
    }

    @Test
    public void testUpdateProcessOrchestrationWithNonExistentProcess() throws Throwable {
        // arrange
        when(scrmProcessOrchestrationConverter.convertToDO(processOrchestrationDTO)).thenReturn(processOrchestrationInfoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(1L)).thenReturn(null);
        // act
        DdlResultDTO result = service.updateProcessOrchestration(processOrchestrationDTO);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("目标编排不存在", result.getMsg());
    }

    @Test
    public void testUpdateProcessOrchestrationWithWrongAppId() throws Throwable {
        // arrange
        originDO.setAppId("differentApp");
        when(scrmProcessOrchestrationConverter.convertToDO(processOrchestrationDTO)).thenReturn(processOrchestrationInfoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(1L)).thenReturn(originDO);
        // act
        DdlResultDTO result = service.updateProcessOrchestration(processOrchestrationDTO);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("不属于此业务", result.getMsg());
    }

    @Test
    public void testUpdateProcessOrchestrationWithInvalidStatus() throws Throwable {
        // arrange
        originDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.FINISHED.getValue().byteValue());
        when(scrmProcessOrchestrationConverter.convertToDO(processOrchestrationDTO)).thenReturn(processOrchestrationInfoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(1L)).thenReturn(originDO);
        // act
        DdlResultDTO result = service.updateProcessOrchestration(processOrchestrationDTO);
        // assert
        assertFalse(result.isSuccess());
        assertEquals("任务已执行/完成，无法修改", result.getMsg());
    }

    @Test
    public void testUpdateProcessOrchestrationWithRealTimeProcess() throws Throwable {
        // arrange
        // 设置为0避免需要人群包列表
        processOrchestrationDTO.setCrowdPackType(0);
        processOrchestrationDTO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue().byteValue());
        processOrchestrationDTO.setRealTimeSceneId(100L);
        processOrchestrationDTO.setBeginTime(new Date());
        processOrchestrationDTO.setEndTime(new Date(System.currentTimeMillis() + 3600000));
        processOrchestrationInfoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue().byteValue());
        when(scrmProcessOrchestrationConverter.convertToDO(processOrchestrationDTO)).thenReturn(processOrchestrationInfoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(1L)).thenReturn(originDO);
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(scrmProcessOrchestrationExecutorConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        // 直接模拟configurationChangeLogDOMapper.insert方法
        when(configurationChangeLogDOMapper.insert(any())).thenReturn(1);
        // 模拟insertSubassemblies方法，避免空指针异常
        doNothing().when(service).insertSubassemblies(anyList(), any(), anyList(), anyList(), anyList(), anyList(), any(), anyList(), any(), anyList(), anyList(), anyList());
        // 模拟checkStatusAndUpdateMap方法，避免多次调用selectByExample
        doNothing().when(service).checkStatusAndUpdateMap(anyLong(), any(ScrmProcessOrchestrationDTO.class), anyInt());
        // act
        DdlResultDTO result = service.updateProcessOrchestration(processOrchestrationDTO);
        // assert
        assertTrue(result.isSuccess());
        assertEquals("编排更新成功", result.getMsg());
        // 不再验证selectByExample的调用次数，因为我们已经模拟了checkStatusAndUpdateMap方法
    }

    @Test
    public void testUpdateProcessOrchestrationWithOverlappingTimeWindow() throws Throwable {
        // arrange
        // 设置为0避免需要人群包列表
        processOrchestrationDTO.setCrowdPackType(0);
        processOrchestrationDTO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue().byteValue());
        processOrchestrationDTO.setRealTimeSceneId(100L);
        processOrchestrationDTO.setBeginTime(new Date());
        processOrchestrationDTO.setEndTime(new Date(System.currentTimeMillis() + 3600000));
        processOrchestrationInfoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue().byteValue());
        ScrmAMRealtimeSceneAndProcessMapDO existingMap = new ScrmAMRealtimeSceneAndProcessMapDO();
        existingMap.setProcessOrchestrationId(2L);
        existingMap.setBeginTime(new Date());
        existingMap.setEndTime(new Date(System.currentTimeMillis() + 1800000));
        when(scrmProcessOrchestrationConverter.convertToDO(processOrchestrationDTO)).thenReturn(processOrchestrationInfoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(1L)).thenReturn(originDO);
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(existingMap));
        // 使用doThrow来模拟checkStatusAndUpdateMap方法，使其抛出IllegalAccessException
        doThrow(new IllegalAccessException("该时段目标实时场景已被占用： 占用编排：2")).when(service).checkStatusAndUpdateMap(anyLong(), any(ScrmProcessOrchestrationDTO.class), anyInt());
        // act & assert
        assertThrows(IllegalAccessException.class, () -> {
            service.updateProcessOrchestration(processOrchestrationDTO);
        });
    }

    @Test
    public void testUpdateProcessOrchestrationWithEmptyNodeMedium() throws Throwable {
        // arrange
        // 设置为0避免需要人群包列表
        processOrchestrationDTO.setCrowdPackType(0);
        processOrchestrationDTO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_TIMED_TASK.getValue().byteValue());
        when(scrmProcessOrchestrationConverter.convertToDO(processOrchestrationDTO)).thenReturn(processOrchestrationInfoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(1L)).thenReturn(originDO);
        when(scrmProcessOrchestrationExecutorConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        // 直接模拟configurationChangeLogDOMapper.insert方法
        when(configurationChangeLogDOMapper.insert(any())).thenReturn(1);
        // 模拟insertSubassemblies方法，避免空指针异常
        doNothing().when(service).insertSubassemblies(anyList(), any(), anyList(), anyList(), anyList(), anyList(), any(), anyList(), any(), anyList(), anyList(), anyList());
        // act
        DdlResultDTO result = service.updateProcessOrchestration(processOrchestrationDTO);
        // assert
        assertTrue(result.isSuccess());
        assertEquals("编排更新成功", result.getMsg());
    }

    @Test
    public void testUpdateProcessOrchestrationWithEmptyGoals() throws Throwable {
        // arrange
        // 设置为0避免需要人群包列表
        processOrchestrationDTO.setCrowdPackType(0);
        processOrchestrationDTO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_TIMED_TASK.getValue().byteValue());
        // Setup node medium DTO
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(new ArrayList<>());
        nodeMediumDTO.setConditionMap(new HashMap<>());
        nodeMediumDTO.setActionMap(new HashMap<>());
        nodeMediumDTO.setActionContentMap(new HashMap<>());
        nodeMediumDTO.setActionAttachmentMap(new HashMap<>());
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        when(scrmProcessOrchestrationConverter.convertToDO(processOrchestrationDTO)).thenReturn(processOrchestrationInfoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(1L)).thenReturn(originDO);
        when(scrmProcessOrchestrationNodeConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        when(scrmProcessOrchestrationConditionDetailConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        when(scrmProcessOrchestrationActionConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        when(scrmProcessOrchestrationActionContentConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        when(scrmProcessOrchestrationActionAttachmentConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        when(scrmProcessOrchestrationExecutorConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        // 直接模拟configurationChangeLogDOMapper.insert方法
        when(configurationChangeLogDOMapper.insert(any())).thenReturn(1);
        // 模拟insertSubassemblies方法，避免空指针异常
        doNothing().when(service).insertSubassemblies(anyList(), any(), anyList(), anyList(), anyList(), anyList(), any(), anyList(), any(), anyList(), anyList(), anyList());
        // act
        DdlResultDTO result = service.updateProcessOrchestration(processOrchestrationDTO);
        // assert
        assertTrue(result.isSuccess());
        assertEquals("编排更新成功", result.getMsg());
    }

    @Test
    public void testUpdateProcessOrchestrationWithFailedCrowdPackCreation() throws Throwable {
        // arrange
        processOrchestrationDTO.setCrowdPackType(2);
        processOrchestrationDTO.setName("Test Process");
        processOrchestrationDTO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_TIMED_TASK.getValue().byteValue());
        DdlResultDTO createResult = new DdlResultDTO(false, "Failed", 0L);
        when(scrmProcessOrchestrationConverter.convertToDO(processOrchestrationDTO)).thenReturn(processOrchestrationInfoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(1L)).thenReturn(originDO);
        when(crowdPackWriteDomainService.createCrowdPack(any())).thenReturn(createResult);
        // 模拟logicalDeleteCrowdPackByProcessOrchestrationId方法
        doNothing().when(service).logicalDeleteCrowdPackByProcessOrchestrationId(anyLong());
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            service.updateProcessOrchestration(processOrchestrationDTO);
        });
    }

    @Test
    public void testUpdateProcessOrchestrationWithWillRunToday() throws Throwable {
        // arrange
        processOrchestrationDTO.setCrowdPackType(2);
        processOrchestrationDTO.setName("Test Process");
        processOrchestrationDTO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        processOrchestrationDTO.setCronComment(JsonUtils.toStr(new CronModel()));
        processOrchestrationInfoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        DdlResultDTO createResult = new DdlResultDTO(true, "Created", 100L);
        when(scrmProcessOrchestrationConverter.convertToDO(processOrchestrationDTO)).thenReturn(processOrchestrationInfoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByPrimaryKey(1L)).thenReturn(originDO);
        when(crowdPackWriteDomainService.createCrowdPack(any())).thenReturn(createResult);
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(scrmProcessOrchestrationExecutorConverter.convertToDOs(anyList())).thenReturn(new ArrayList<>());
        // 模拟logicalDeleteCrowdPackByProcessOrchestrationId方法
        doNothing().when(service).logicalDeleteCrowdPackByProcessOrchestrationId(anyLong());
        // 直接模拟configurationChangeLogDOMapper.insert方法
        when(configurationChangeLogDOMapper.insert(any())).thenReturn(1);
        // 模拟insertSubassemblies方法，避免空指针异常
        doNothing().when(service).insertSubassemblies(anyList(), any(), anyList(), anyList(), anyList(), anyList(), any(), anyList(), any(), anyList(), anyList(), anyList());
        // 使用doAnswer来模拟checkNextRunTimeForUpdate方法返回willRunToday=true的结果
        doAnswer(invocation -> {
            DdlResultDTO result = invocation.getArgument(1);
            result.setWillRunToday(true);
            return result;
        }).when(service).checkNextRunTimeForUpdate(eq(processOrchestrationInfoDO), any(DdlResultDTO.class));
        // act
        DdlResultDTO result = service.updateProcessOrchestration(processOrchestrationDTO);
        // assert
        assertTrue(result.isSuccess());
        assertEquals("编排更新成功", result.getMsg());
        verify(crowdPackUpdateLockService).tryProducerLock(eq(100L), eq(60 * 60 * 2));
        verify(refinementOperationExecuteMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(anyList(), eq("testApp"), eq(100L));
    }
}
