package com.sankuai.scrm.core.service.realtime.task.domainservice;

import com.dianping.unified.coupon.issue.api.dto.UnifiedCouponIssueDetail;
import com.dianping.unified.coupon.issue.api.dto.UnifiedCouponIssueResult;
import com.dianping.unified.coupon.issue.api.response.UnifiedCouponIssueResponse;
import com.sankuai.dz.srcm.couponIntegration.enums.FunctionModuleEnum;
import com.sankuai.dz.srcm.realtime.task.dto.SceneSendCouponResponse;
import com.sankuai.scrm.core.service.activity.miniprogram.domain.CouponDomainService;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.couponIntegration.domain.CouponDashBoardDomainService;
import com.sankuai.scrm.core.service.couponIntegration.utils.CouponIntegrationUtil;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.google.common.collect.Lists;
import com.meituan.service.mobile.group.geo.bean.CityInfo;
import com.meituan.service.mobile.group.geo.service.CityService;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ExecuteResultDTO;
import com.sankuai.dz.srcm.automatedmanagement.service.ScrmRefinementOperationBackEndService;
import com.sankuai.dz.srcm.realtime.task.dto.ExtendFieldsDTO;
import com.sankuai.dz.srcm.realtime.task.dto.RealtimePerceptionMessage;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmSceneProcessPriorityDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmSceneProcessPriorityDOMapper;
import com.sankuai.scrm.core.service.external.contact.dal.babymapper.ContactUserMapper;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.infrastructure.config.CorpIdCategoryIdConfig;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmRealtimeSceneDO;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmRealtimeSceneUserRecordDO;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmRealtimeSceneDOMapper;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmRealtimeSceneUserRecordDOMapper;
import java.util.Collections;
import java.util.List;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
public class SceneDomainServiceSceneSendCoupon1Test {

    @InjectMocks
    private SceneDomainService sceneDomainService;

    @Mock
    private CouponIntegrationUtil util;

    @Mock
    private CouponDomainService couponDomainService;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordDOMapper;

    @Mock
    private CouponDashBoardDomainService couponDashBoardDomainService;

    private String unionId;

    private String couponGroupId;

    private String appId;

    private String distributorCode;

    private String sceneDetail;

    private Integer sceneType;

    private Date taskStartTime;

    private static final String CORP_ID_CATEGORY_ID_MAPPING_LION_KEY = "com.sankuai.scrm.core.service.infrastructure.config.corp.id.category.id.mapping";

    private static final String TEST_CORP_ID = "ww975965cae5f5138f";

    private static final String XIUYU_APP_ID = "wxde8ac0a21135c07d";

    @Mock
    private ScrmRealtimeSceneDOMapper scrmRealtimeSceneDOMapper;

    @Mock
    private ScrmRealtimeSceneUserRecordDOMapper scrmRealtimeSceneUserRecordDOMapper;

    @Mock
    private ContactUserMapper contactUserMapper;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private ScrmRefinementOperationBackEndService scrmRefinementOperationBackEndService;

    @Mock
    private CityService cityService;

    @Mock
    private ScrmAmSceneProcessPriorityDOMapper sceneProcessPriorityDOMapper;

    @BeforeEach
    void setUp() {
        unionId = "testUnionId";
        couponGroupId = "testCouponId";
        appId = "testAppId";
        distributorCode = "testDistCode";
        sceneDetail = "testDetail";
        sceneType = 1;
        taskStartTime = new Date();
    }

    /**
     * Test case to cover the exception handling in preValidateAndAddCouponList
     */
    @Test
    public void testSceneSendCoupon_PreValidateAndAddCouponListException() {
        // arrange
        doThrow(new RuntimeException("Test exception")).when(util).preValidateAndAddCouponList(any(), any(), any(), any(), any(), any());
        when(mtUserCenterAclService.getUserIdByUnionIdFromMtUserCenter(anyString())).thenReturn(123L);
        UnifiedCouponIssueResponse response = new UnifiedCouponIssueResponse();
        response.setSuccess(true);
        response.setResult(new UnifiedCouponIssueResult());
        response.getResult().setResult(new ArrayList<>());
        when(couponDomainService.sendMtCouponAndResponse(anyLong(), anyString(), anyString(), eq(null))).thenReturn(response);
        // act
        SceneSendCouponResponse result = sceneDomainService.sceneSendCoupon(unionId, couponGroupId, appId, distributorCode, sceneDetail, sceneType, FunctionModuleEnum.AUTOMATED_MARKETING, taskStartTime);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        verify(util).preValidateAndAddCouponList(any(), any(), any(), any(), any(), any());
    }

    /**
     * Test case to cover the empty result handling
     */
    @Test
    public void testSceneSendCoupon_EmptyResult() {
        // arrange
        when(mtUserCenterAclService.getUserIdByUnionIdFromMtUserCenter(anyString())).thenReturn(123L);
        UnifiedCouponIssueResponse response = new UnifiedCouponIssueResponse();
        response.setSuccess(true);
        response.setResult(new UnifiedCouponIssueResult());
        response.getResult().setResult(new ArrayList<>());
        when(couponDomainService.sendMtCouponAndResponse(anyLong(), anyString(), anyString(), eq(null))).thenReturn(response);
        // act
        SceneSendCouponResponse result = sceneDomainService.sceneSendCoupon(unionId, couponGroupId, appId, distributorCode, sceneDetail, sceneType, FunctionModuleEnum.AUTOMATED_MARKETING, taskStartTime);
        // assert
        assertNotNull(result);
        assertTrue(result.isSuccess());
        assertNull(result.getSuccessList());
        assertNull(result.getFailList());
    }

    /**
     * Test case for invalid input parameters
     */
    @Test
    public void testSceneSendCoupon_InvalidParameters() {
        // act
        SceneSendCouponResponse result = sceneDomainService.sceneSendCoupon("", "", "", "", sceneDetail, sceneType, FunctionModuleEnum.AUTOMATED_MARKETING, taskStartTime);
        // assert
        assertNull(result);
    }

    /**
     * Test case for failed coupon issue response
     */
    @Test
    public void testSceneSendCoupon_FailedCouponIssue() {
        // arrange
        when(mtUserCenterAclService.getUserIdByUnionIdFromMtUserCenter(anyString())).thenReturn(123L);
        UnifiedCouponIssueResponse response = new UnifiedCouponIssueResponse();
        response.setSuccess(false);
        when(couponDomainService.sendMtCouponAndResponse(anyLong(), anyString(), anyString(),anyInt())).thenReturn(response);
        // act
        SceneSendCouponResponse result = sceneDomainService.sceneSendCoupon(unionId, couponGroupId, appId, distributorCode, sceneDetail, sceneType, FunctionModuleEnum.AUTOMATED_MARKETING, taskStartTime);
        // assert
        assertNotNull(result);
        assertFalse(result.isSuccess());
    }

    /**
     * Test case for successful coupon issue with complete flow
     */
    @Test
    public void testSceneSendCoupon_SuccessfulFlow() {
        // arrange
        long userId = 123L;
        when(mtUserCenterAclService.getUserIdByUnionIdFromMtUserCenter(anyString())).thenReturn(userId);
        UnifiedCouponIssueDetail detail = new UnifiedCouponIssueDetail();
        detail.setUnifiedCouponId("testCouponId");
        detail.setDiscountAmount(BigDecimal.TEN);
        detail.setCouponGroupName("testGroup");
        detail.setBeginTime(new Date());
        detail.setEndTime(new Date());
        detail.setPriceLimit(BigDecimal.ONE);
        detail.setRedirectLink("testLink");
        UnifiedCouponIssueResponse response = new UnifiedCouponIssueResponse();
        response.setSuccess(true);
        UnifiedCouponIssueResult result = new UnifiedCouponIssueResult();
        result.setResult(Arrays.asList(detail));
        response.setResult(result);
        when(couponDomainService.sendMtCouponAndResponse(anyLong(), anyString(), anyString(),eq(null))).thenReturn(response);
        when(couponDashBoardDomainService.getStatisticStatus(anyString(), anyString(), anyString(), anyInt())).thenReturn(1);
        // act
        SceneSendCouponResponse actualResponse = sceneDomainService.sceneSendCoupon(unionId, couponGroupId, appId, distributorCode, sceneDetail, sceneType, FunctionModuleEnum.AUTOMATED_MARKETING, taskStartTime);
        // assert
        assertNotNull(actualResponse);
        assertTrue(actualResponse.isSuccess());
        assertEquals(1, actualResponse.getSuccessList().size());
        verify(scrmSceneCouponRecordDOMapper).batchInsert(anyList());
    }

    @Test
    public void testHandleRealtimePerceptionMessage_NullMessage() throws Throwable {
        // arrange
        RealtimePerceptionMessage message = null;
        // act
        sceneDomainService.handleRealtimePerceptionMessage(message);
        // assert
        verify(scrmRealtimeSceneDOMapper, never()).selectByExample(any());
    }

    @Test
    public void testHandleRealtimePerceptionMessage_NullRequiredFields() throws Throwable {
        // arrange
        RealtimePerceptionMessage message = new RealtimePerceptionMessage();
        // act
        sceneDomainService.handleRealtimePerceptionMessage(message);
        // assert
        verify(scrmRealtimeSceneDOMapper, never()).selectByExample(any());
    }

    @Test
    public void testHandleRealtimePerceptionMessage_NoScenesFound() throws Throwable {
        // arrange
        RealtimePerceptionMessage message = createValidMessage();
        when(scrmRealtimeSceneDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        sceneDomainService.handleRealtimePerceptionMessage(message);
        // assert
        verify(mtUserCenterAclService, never()).getUnionIdByUserIdFromMtUserCenter(anyLong(), anyString());
    }

    @Test
    public void testHandleRealtimePerceptionMessage_NormalFlow_NewRecord() throws Throwable {
        // arrange
        RealtimePerceptionMessage message = createValidMessage();
        ScrmRealtimeSceneDO sceneDO = createSceneDO("test_app_id");
        setupPriorityMock(sceneDO.getId());
        setupNormalFlowMocks(sceneDO);
        when(scrmRealtimeSceneUserRecordDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        sceneDomainService.handleRealtimePerceptionMessage(message);
        // assert
        verify(scrmRealtimeSceneUserRecordDOMapper).insertSelective(any());
    }

    @Test
    public void testHandleRealtimePerceptionMessage_NormalFlow_UpdateRecord() throws Throwable {
        // arrange
        RealtimePerceptionMessage message = createValidMessage();
        ScrmRealtimeSceneDO sceneDO = createSceneDO("test_app_id");
        setupPriorityMock(sceneDO.getId());
        setupNormalFlowMocks(sceneDO);
        when(scrmRealtimeSceneUserRecordDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(createExistingRecord()));
        // act
        sceneDomainService.handleRealtimePerceptionMessage(message);
        // assert
        verify(scrmRealtimeSceneUserRecordDOMapper).updateByExampleSelective(any(), any());
    }

    private RealtimePerceptionMessage createValidMessage() {
        RealtimePerceptionMessage message = new RealtimePerceptionMessage();
        message.setSceneId(1);
        message.setUserId(1234L);
        ExtendFieldsDTO extendFields = new ExtendFieldsDTO();
        extendFields.setMt_poi_city_id("1");
        extendFields.setSensor_mt_poi_id("test_poi");
        message.setExtendFields(extendFields);
        return message;
    }

    private ScrmRealtimeSceneDO createSceneDO(String appId) {
        return ScrmRealtimeSceneDO.builder().id(1L).appid(appId).sceneid(1).isDeleted("0").build();
    }

    private ScrmRealtimeSceneUserRecordDO createExistingRecord() {
        return ScrmRealtimeSceneUserRecordDO.builder().id(1L).visitcount(1L).build();
    }

    private void setupNormalFlowMocks(ScrmRealtimeSceneDO sceneDO) {
        when(scrmRealtimeSceneDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(sceneDO));
        when(mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(anyLong(), anyString())).thenReturn("test_union_id");
        when(corpAppConfigRepository.getConfigByAppId(anyString())).thenReturn(createCorpAppConfig());
        when(contactUserMapper.selectByExample(any())).thenReturn(Collections.singletonList(new ContactUser()));
        lenient().when(scrmRefinementOperationBackEndService.executeRealTimeTask(any(), any(), any())).thenReturn(createSuccessExecuteResult());
    }

    private void setupPriorityMock(Long sceneId) {
        ScrmAmSceneProcessPriorityDO priorityDO = new ScrmAmSceneProcessPriorityDO();
        priorityDO.setSceneId(sceneId);
        priorityDO.setCouponamount(new BigDecimal("100"));
        when(sceneProcessPriorityDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(priorityDO));
    }

    private CorpAppConfig createCorpAppConfig() {
        CorpAppConfig config = new CorpAppConfig();
        config.setCorpId(TEST_CORP_ID);
        return config;
    }

    private ExecuteResultDTO createSuccessExecuteResult() {
        ExecuteResultDTO result = new ExecuteResultDTO();
        result.setSuccess(true);
        result.setNeedExecute(true);
        return result;
    }
}