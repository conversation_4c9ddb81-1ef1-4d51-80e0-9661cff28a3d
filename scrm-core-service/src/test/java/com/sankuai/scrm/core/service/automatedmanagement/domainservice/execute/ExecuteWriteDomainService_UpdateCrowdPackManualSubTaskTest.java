package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.activity.fission.dto.activity.MktCouponInfoDTO;
import com.sankuai.dz.srcm.activity.fission.service.ActivityFissionService;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.AutoActionContentDetailDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationInfoDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.*;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.utils.ConditionUtils;
import com.sankuai.scrm.core.service.infrastructure.dal.babymapper.ContactUserDoMapper;
import com.sankuai.scrm.core.service.infrastructure.dal.entity.ContactUserDo;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserTag;
import com.sankuai.scrm.core.service.user.dal.mapper.ext.ExtScrmUserTagMapper;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.math.BigDecimal;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ExecuteWriteDomainService_UpdateCrowdPackManualSubTaskTest {

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock(lenient = true)
    private ExecuteManagementService executeManagementService;

    @Mock(lenient = true)
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock(lenient = true)
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock(lenient = true)
    private ExtScrmUserTagMapper userTagDOMapper;

    @Mock(lenient = true)
    private CorpAppConfigRepository appConfigRepository;

    @Mock(lenient = true)
    private ConditionUtils conditionUtils;

    @Mock(lenient = true)
    private ContactUserDoMapper contactUserDoMapper;

    @Mock
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationActionDOMapper scrmAmProcessOrchestrationActionDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationActionContentDOMapper scrmAmProcessOrchestrationActionContentDOMapper;

    @Mock
    private ActivityFissionService activityFissionService;

    @Mock
    private ScrmAmSceneProcessPriorityDOMapper sceneProcessPriorityDOMapper;

    @Mock
    private ScrmAMRealtimeSceneAndProcessMapDOMapper scrmAMRealtimeSceneAndProcessMapDOMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    @Test
    public void testUpdateCrowdPackManualSubTaskWithUserTags() throws Throwable {
        List<String> userUnionIds = Collections.singletonList("user1");
        Long crowdPackId = 1L;
        String appId = "testAppId";
        ScrmCrowdPackDTO mockPackDTO = new ScrmCrowdPackDTO();
        mockPackDTO.setId(crowdPackId);
        mockPackDTO.setAppId(appId);
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(crowdPackId, appId)).thenReturn(mockPackDTO);
        ScrmUserTag mockUserTag = new ScrmUserTag();
        mockUserTag.setUserId(100L);
        when(userTagDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(mockUserTag));
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn("testCorpId");
        ContactUserDo mockContactUserDo = new ContactUserDo();
        mockContactUserDo.setExternalUserId("externalUserId100");
        when(contactUserDoMapper.selectByExample(any())).thenReturn(Collections.singletonList(mockContactUserDo));
        when(conditionUtils.isCrowdPackConditionMatch(any(), anyString(), any(), anyList())).thenReturn(true);
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, crowdPackId, appId);
        assertTrue(result);
        verify(crowdPackWriteDomainService, times(1)).updateCrowdPackDetailInfo(any(ScrmAmCrowdPackDetailInfoDO.class), eq(true));
    }

    @Test
    public void testUpdateCrowdPackManualSubTaskEmptyUserUnionIds() throws Throwable {
        List<String> userUnionIds = Collections.emptyList();
        Long crowdPackId = 1L;
        String appId = "testAppId";
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, crowdPackId, appId);
        assertTrue(result);
        verify(executeManagementService, never()).subTaskRunBegin(anyInt(), anyLong());
        verify(crowdPackReadDomainService, never()).queryCrowdPackDetailInfo(anyLong(), anyString());
    }

    @Test
    public void testUpdateCrowdPackManualSubTaskMultipleUsers() throws Throwable {
        List<String> userUnionIds = Arrays.asList("user1", "user2", "user3");
        Long crowdPackId = 1L;
        String appId = "testAppId";
        ScrmCrowdPackDTO mockPackDTO = new ScrmCrowdPackDTO();
        mockPackDTO.setId(crowdPackId);
        mockPackDTO.setAppId(appId);
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(crowdPackId, appId)).thenReturn(mockPackDTO);
        ScrmUserTag mockUserTag = new ScrmUserTag();
        mockUserTag.setUserId(100L);
        when(userTagDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(mockUserTag));
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn("testCorpId");
        ContactUserDo mockContactUserDo = new ContactUserDo();
        mockContactUserDo.setExternalUserId("externalUserId100");
        when(contactUserDoMapper.selectByExample(any())).thenReturn(Collections.singletonList(mockContactUserDo));
        when(conditionUtils.isCrowdPackConditionMatch(any(), anyString(), any(), anyList())).thenReturn(true);
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, crowdPackId, appId);
        assertTrue(result);
        verify(crowdPackWriteDomainService, times(userUnionIds.size())).updateCrowdPackDetailInfo(any(ScrmAmCrowdPackDetailInfoDO.class), eq(true));
    }

    @Test
    public void testUpdateCrowdPackManualSubTaskEmptyList() throws Throwable {
        List<String> userUnionIds = Collections.emptyList();
        Long crowdPackId = 1L;
        String appId = "testAppId";
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, crowdPackId, appId);
        assertTrue(result);
        verify(executeManagementService, never()).subTaskRunBegin(anyInt(), anyLong());
        verify(crowdPackReadDomainService, never()).queryCrowdPackDetailInfo(anyLong(), anyString());
    }

    /**
     * Test checkAndSavePriority when priorityInfoDTO is null
     */
    @Test
    public void testCheckAndSavePriority_WhenPriorityInfoDTOIsNull() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setValidVersion("1.0");
        // VALID status
        infoDO.setStatus((byte) 1);
        infoDO.setBeginTime(new Date(System.currentTimeMillis() - 1000));
        infoDO.setEndTime(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000));
        // REAL_TIME_PROCESS_ORCHESTRATION
        infoDO.setProcessOrchestrationType((byte) 3);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class))).thenReturn(Collections.singletonList(infoDO));
        when(scrmAmProcessOrchestrationActionDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        executeWriteDomainService.checkAndSavePriority();
        // assert
        verify(sceneProcessPriorityDOMapper, never()).insertSelective(any());
    }

    /**
     * Test checkAndSavePriority when sceneId is null
     */
    @Test
    public void testCheckAndSavePriority_WhenSceneIdIsNull() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setValidVersion("1.0");
        infoDO.setAppId("testApp");
        // VALID status
        infoDO.setStatus((byte) 1);
        infoDO.setBeginTime(new Date(System.currentTimeMillis() - 1000));
        infoDO.setEndTime(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000));
        // REAL_TIME_PROCESS_ORCHESTRATION
        infoDO.setProcessOrchestrationType((byte) 3);
        ScrmAmProcessOrchestrationActionDO actionDO = new ScrmAmProcessOrchestrationActionDO();
        // COUPON_DISTRIBUTION
        actionDO.setActionType((byte) 8);
        actionDO.setProcessOrchestrationNodeId(1L);
        AutoActionContentDetailDTO contentDetailDTO = new AutoActionContentDetailDTO();
        contentDetailDTO.setCouponGroupId("123");
        ScrmAmProcessOrchestrationActionContentDO contentDO = new ScrmAmProcessOrchestrationActionContentDO();
        contentDO.setContent(JsonUtils.toStr(contentDetailDTO));
        contentDO.setProcessOrchestrationNodeId(1L);
        MktCouponInfoDTO couponInfoDTO = new MktCouponInfoDTO();
        couponInfoDTO.setDiscountAmount(new BigDecimal("10.0"));
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class))).thenReturn(Collections.singletonList(infoDO));
        when(scrmAmProcessOrchestrationActionDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(actionDO));
        when(scrmAmProcessOrchestrationActionContentDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(contentDO));
        when(activityFissionService.queryMktCouponInfo(anyString())).thenReturn(RemoteResponse.success(couponInfoDTO));
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        executeWriteDomainService.checkAndSavePriority();
        // assert
        verify(sceneProcessPriorityDOMapper, never()).insertSelective(any());
    }

    /**
     * Test checkAndSavePriority with valid data
     */
    @Test
    public void testCheckAndSavePriority_WithValidData() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setValidVersion("1.0");
        infoDO.setAppId("testApp");
        // VALID status
        infoDO.setStatus((byte) 1);
        infoDO.setBeginTime(new Date(System.currentTimeMillis() - 1000));
        infoDO.setEndTime(new Date(System.currentTimeMillis() + 24 * 60 * 60 * 1000));
        // REAL_TIME_PROCESS_ORCHESTRATION
        infoDO.setProcessOrchestrationType((byte) 3);
        ScrmAmProcessOrchestrationActionDO actionDO = new ScrmAmProcessOrchestrationActionDO();
        // COUPON_DISTRIBUTION
        actionDO.setActionType((byte) 8);
        actionDO.setProcessOrchestrationNodeId(1L);
        AutoActionContentDetailDTO contentDetailDTO = new AutoActionContentDetailDTO();
        contentDetailDTO.setCouponGroupId("123");
        ScrmAmProcessOrchestrationActionContentDO contentDO = new ScrmAmProcessOrchestrationActionContentDO();
        contentDO.setContent(JsonUtils.toStr(contentDetailDTO));
        contentDO.setProcessOrchestrationNodeId(1L);
        ScrmAMRealtimeSceneAndProcessMapDO mapDO = new ScrmAMRealtimeSceneAndProcessMapDO();
        mapDO.setSceneId(100L);
        MktCouponInfoDTO couponInfoDTO = new MktCouponInfoDTO();
        couponInfoDTO.setDiscountAmount(new BigDecimal("10.0"));
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class))).thenReturn(Collections.singletonList(infoDO));
        when(scrmAmProcessOrchestrationActionDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(actionDO));
        when(scrmAmProcessOrchestrationActionContentDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(contentDO));
        when(activityFissionService.queryMktCouponInfo(anyString())).thenReturn(RemoteResponse.success(couponInfoDTO));
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(mapDO));
        when(sceneProcessPriorityDOMapper.insertSelective(any())).thenReturn(1);
        // act
        executeWriteDomainService.checkAndSavePriority();
        // assert
        verify(sceneProcessPriorityDOMapper).insertSelective(any(ScrmAmSceneProcessPriorityDO.class));
    }
}
