package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationGoalDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import java.util.Arrays;
import java.util.Calendar;
import java.util.Date;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ExecuteWriteDomainServiceCheckTimedProcessOrchestrationTaskStatusTest {

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    private ScrmAmProcessOrchestrationInfoDO infoDO;

    private Date taskStartTime;

    @Before
    public void setUp() {
        infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        taskStartTime = new Date();
        infoDO.setCronComment("2024-01-20 10:00:00");
    }

    /**
     * Test case: When goal type is positive and status is 1
     */
    @Test
    public void testCheckTimedProcessOrchestrationTaskStatus_WhenPositiveGoalAndStatusIsOne() throws Throwable {
        // arrange
        infoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENT.getValue().byteValue());
        ScrmProcessOrchestrationGoalDTO positiveGoal = new ScrmProcessOrchestrationGoalDTO();
        positiveGoal.setGoalType((byte) 1);
        positiveGoal.setStatus((byte) 1);
        positiveGoal.setCheckTimeUnit("1");
        positiveGoal.setCheckTime("2");
        CompletableFuture<java.util.List<ScrmProcessOrchestrationGoalDTO>> future = CompletableFuture.completedFuture(Arrays.asList(positiveGoal));
        when(processOrchestrationReadDomainService.queryGoalFuture(any())).thenReturn(future);
        // act
        executeWriteDomainService.checkTimedProcessOrchestrationTaskStatus(infoDO);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).updateByPrimaryKey(argThat(info -> info.getStatus() == ScrmProcessOrchestrationStatusTypeEnum.FINISHED.getValue().byteValue()));
    }

    /**
     * Test case: When goal type is negative and status is 1
     */
    @Test
    public void testCheckTimedProcessOrchestrationTaskStatus_WhenNegativeGoalAndStatusIsOne() throws Throwable {
        // arrange
        infoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENT.getValue().byteValue());
        ScrmProcessOrchestrationGoalDTO negativeGoal = new ScrmProcessOrchestrationGoalDTO();
        negativeGoal.setGoalType((byte) 2);
        negativeGoal.setStatus((byte) 1);
        negativeGoal.setCheckTimeUnit("2");
        negativeGoal.setCheckTime("3");
        CompletableFuture<java.util.List<ScrmProcessOrchestrationGoalDTO>> future = CompletableFuture.completedFuture(Arrays.asList(negativeGoal));
        when(processOrchestrationReadDomainService.queryGoalFuture(any())).thenReturn(future);
        // act
        executeWriteDomainService.checkTimedProcessOrchestrationTaskStatus(infoDO);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).updateByPrimaryKey(argThat(info -> info.getStatus() == ScrmProcessOrchestrationStatusTypeEnum.FINISHED.getValue().byteValue()));
    }

    /**
     * Test case: When no goal is present, should use default values
     */
    @Test
    public void testCheckTimedProcessOrchestrationTaskStatus_WhenNoGoal_ShouldUseDefaultValues() throws Throwable {
        // arrange
        infoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENT.getValue().byteValue());
        CompletableFuture<java.util.List<ScrmProcessOrchestrationGoalDTO>> future = CompletableFuture.completedFuture(Arrays.asList());
        when(processOrchestrationReadDomainService.queryGoalFuture(any())).thenReturn(future);
        // act
        executeWriteDomainService.checkTimedProcessOrchestrationTaskStatus(infoDO);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).updateByPrimaryKey(argThat(info -> info.getStatus() == ScrmProcessOrchestrationStatusTypeEnum.FINISHED.getValue().byteValue()));
    }

    /**
     * Test case: When time unit is MINUTE
     */
    @Test
    public void testCheckTimedProcessOrchestrationTaskStatus_WhenTimeUnitIsMinute() throws Throwable {
        // arrange
        infoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENT.getValue().byteValue());
        ScrmProcessOrchestrationGoalDTO goal = new ScrmProcessOrchestrationGoalDTO();
        goal.setGoalType((byte) 1);
        goal.setStatus((byte) 1);
        goal.setCheckTimeUnit("3");
        goal.setCheckTime("30");
        CompletableFuture<java.util.List<ScrmProcessOrchestrationGoalDTO>> future = CompletableFuture.completedFuture(Arrays.asList(goal));
        when(processOrchestrationReadDomainService.queryGoalFuture(any())).thenReturn(future);
        // act
        executeWriteDomainService.checkTimedProcessOrchestrationTaskStatus(infoDO);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).updateByPrimaryKey(argThat(info -> info.getStatus() == ScrmProcessOrchestrationStatusTypeEnum.FINISHED.getValue().byteValue()));
    }

    /**
     * Test case: When time unit is HOUR
     */
    @Test
    public void testCheckTimedProcessOrchestrationTaskStatus_WhenTimeUnitIsHour() throws Throwable {
        // arrange
        infoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENT.getValue().byteValue());
        ScrmProcessOrchestrationGoalDTO goal = new ScrmProcessOrchestrationGoalDTO();
        goal.setGoalType((byte) 1);
        goal.setStatus((byte) 1);
        goal.setCheckTimeUnit("2");
        goal.setCheckTime("4");
        CompletableFuture<java.util.List<ScrmProcessOrchestrationGoalDTO>> future = CompletableFuture.completedFuture(Arrays.asList(goal));
        when(processOrchestrationReadDomainService.queryGoalFuture(any())).thenReturn(future);
        // act
        executeWriteDomainService.checkTimedProcessOrchestrationTaskStatus(infoDO);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).updateByPrimaryKey(argThat(info -> info.getStatus() == ScrmProcessOrchestrationStatusTypeEnum.FINISHED.getValue().byteValue()));
    }

    /**
     * Test case: When time unit is DAY
     */
    @Test
    public void testCheckTimedProcessOrchestrationTaskStatus_WhenTimeUnitIsDay() throws Throwable {
        // arrange
        infoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENT.getValue().byteValue());
        ScrmProcessOrchestrationGoalDTO goal = new ScrmProcessOrchestrationGoalDTO();
        goal.setGoalType((byte) 1);
        goal.setStatus((byte) 1);
        goal.setCheckTimeUnit("1");
        goal.setCheckTime("1");
        CompletableFuture<java.util.List<ScrmProcessOrchestrationGoalDTO>> future = CompletableFuture.completedFuture(Arrays.asList(goal));
        when(processOrchestrationReadDomainService.queryGoalFuture(any())).thenReturn(future);
        // act
        executeWriteDomainService.checkTimedProcessOrchestrationTaskStatus(infoDO);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).updateByPrimaryKey(argThat(info -> info.getStatus() == ScrmProcessOrchestrationStatusTypeEnum.FINISHED.getValue().byteValue()));
    }

    /**
     * Test case: When final check time is before current time
     */
    @Test
    public void testCheckTimedProcessOrchestrationTaskStatus_WhenFinalCheckTimeBeforeCurrentTime() throws Throwable {
        // arrange
        infoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENT.getValue().byteValue());
        Calendar pastTime = Calendar.getInstance();
        pastTime.add(Calendar.DAY_OF_MONTH, -2);
        infoDO.setCronComment(DateUtil.formatYMdHms(pastTime.getTime()));
        ScrmProcessOrchestrationGoalDTO goal = new ScrmProcessOrchestrationGoalDTO();
        goal.setGoalType((byte) 1);
        goal.setStatus((byte) 1);
        goal.setCheckTimeUnit("1");
        goal.setCheckTime("1");
        CompletableFuture<java.util.List<ScrmProcessOrchestrationGoalDTO>> future = CompletableFuture.completedFuture(Arrays.asList(goal));
        when(processOrchestrationReadDomainService.queryGoalFuture(any())).thenReturn(future);
        // act
        executeWriteDomainService.checkTimedProcessOrchestrationTaskStatus(infoDO);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).updateByPrimaryKey(argThat(info -> info.getStatus() == ScrmProcessOrchestrationStatusTypeEnum.FINISHED.getValue().byteValue()));
    }

    /**
     * Test case: When invoke logs exist, status should change to MESSAGE_SENDING
     */
    @Test
    public void testCheckTimedProcessOrchestrationTaskStatus_WhenInvokeLogsExist_ShouldChangeToMessageSending() throws Throwable {
        // arrange
        infoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.RUNNING.getValue().byteValue());
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Arrays.asList(invokeLogDO));
        // act
        executeWriteDomainService.checkTimedProcessOrchestrationTaskStatus(infoDO);
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).updateByPrimaryKey(argThat(info -> info.getStatus() == ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENDING.getValue().byteValue()));
    }

    /**
     * Test case: When no pending invoke logs exist after end time, status should change to MESSAGE_SENT
     */
    @Test
    public void testCheckTimedProcessOrchestrationTaskStatus_WhenNoPendingInvokeLogsAfterEndTime_ShouldChangeToMessageSent() throws Throwable {
        // arrange
        infoDO.setStatus(ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENDING.getValue().byteValue());
        // Set task start time to 3 hours ago
        Calendar startTime = Calendar.getInstance();
        startTime.add(Calendar.HOUR_OF_DAY, -3);
        infoDO.setCronComment(DateUtil.formatYMdHms(startTime.getTime()));
        // Mock wxInvokeLogDOMapper for waiting logs check
        ScrmAmProcessOrchestrationWxInvokeLogDOExample waitingExample = new ScrmAmProcessOrchestrationWxInvokeLogDOExample();
        waitingExample.createCriteria().andProcessOrchestrationIdEqualTo(infoDO.getId()).andStatusEqualTo(ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Arrays.asList());
        // Mock processOrchestrationReadDomainService
        when(processOrchestrationReadDomainService.queryGoalFuture(any())).thenReturn(CompletableFuture.completedFuture(Arrays.asList()));
        // act
        executeWriteDomainService.checkTimedProcessOrchestrationTaskStatus(infoDO);
        // assert - verify the status change to MESSAGE_SENT
        verify(scrmAmProcessOrchestrationInfoDOMapper, times(1)).updateByPrimaryKey(argThat(info -> info.getStatus() == ScrmProcessOrchestrationStatusTypeEnum.MESSAGE_SENT.getValue().byteValue()));
    }
}
