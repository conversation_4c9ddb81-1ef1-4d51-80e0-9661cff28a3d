package com.sankuai.scrm.core.service.automatedmanagement.mq.producer;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.dz.srcm.automatedmanagement.dto.mq.RefinementOperationExecuteMessage;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Field;
import java.lang.reflect.Method;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class UpdateAllCrowdPackMessageProducerSendMessageTest {

    @InjectMocks
    private UpdateAllCrowdPackMessageProducer producer;

    private IProducerProcessor mockProducer;

    private Method sendMessageMethod;

    @BeforeEach
    public void setUp() throws Exception {
        // 创建mock对象
        mockProducer = mock(IProducerProcessor.class);
        // 使用反射设置静态字段
        Field producerField = UpdateAllCrowdPackMessageProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, mockProducer);
        // 获取私有方法
        sendMessageMethod = UpdateAllCrowdPackMessageProducer.class.getDeclaredMethod("sendMessage", RefinementOperationExecuteMessage.class);
        sendMessageMethod.setAccessible(true);
    }

    /**
     * 测试空消息场景，方法应该直接返回不做任何处理
     */
    @Test
    public void testSendMessageWithNullMessage() throws Throwable {
        // arrange - 无需准备，直接传入null
        // act
        sendMessageMethod.invoke(producer, (Object) null);
        // assert - 验证没有调用任何发送方法
        verify(mockProducer, never()).sendMessage(any());
    }

    /**
     * 测试第一次发送就成功的场景
     */
    @Test
    public void testSendMessageSuccessOnFirstTry() throws Throwable {
        // arrange
        RefinementOperationExecuteMessage message = new RefinementOperationExecuteMessage();
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(mockProducer.sendMessage(anyString())).thenReturn(successResult);
        // act
        sendMessageMethod.invoke(producer, message);
        // assert
        verify(mockProducer, times(1)).sendMessage(anyString());
    }

    /**
     * 测试重试后成功的场景
     */
    @Test
    public void testSendMessageSuccessAfterRetry() throws Throwable {
        // arrange
        RefinementOperationExecuteMessage message = new RefinementOperationExecuteMessage();
        ProducerResult failureResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(mockProducer.sendMessage(anyString())).thenThrow(new RuntimeException("First failure")).thenReturn(failureResult).thenReturn(successResult);
        // act
        sendMessageMethod.invoke(producer, message);
        // assert
        verify(mockProducer, times(3)).sendMessage(anyString());
    }

    /**
     * 测试所有重试都失败的场景
     */
    @Test
    public void testSendMessageAllRetriesFailed() throws Throwable {
        // arrange
        RefinementOperationExecuteMessage message = new RefinementOperationExecuteMessage();
        ProducerResult failureResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        when(mockProducer.sendMessage(anyString())).thenReturn(failureResult).thenReturn(failureResult).thenReturn(failureResult);
        // act
        sendMessageMethod.invoke(producer, message);
        // assert
        verify(mockProducer, times(3)).sendMessage(anyString());
    }

    /**
     * 测试序列化异常场景
     */
    @Test
    public void testSendMessageWithSerializationError() throws Throwable {
        // arrange
        RefinementOperationExecuteMessage message = new RefinementOperationExecuteMessage();
        // 不使用MockedStatic，因为我们不能控制JsonUtils.toStr的行为
        // 而是直接mock producer.sendMessage抛出异常来模拟序列化失败的情况
        when(mockProducer.sendMessage(anyString())).thenThrow(new RuntimeException("Serialization error")).thenThrow(new RuntimeException("Serialization error")).thenThrow(new RuntimeException("Serialization error"));
        // act
        sendMessageMethod.invoke(producer, message);
        // assert
        verify(mockProducer, times(3)).sendMessage(anyString());
    }

    /**
     * 测试每次发送都抛出异常的场景
     */
    @Test
    public void testSendMessageWithConsistentException() throws Throwable {
        // arrange
        RefinementOperationExecuteMessage message = new RefinementOperationExecuteMessage();
        when(mockProducer.sendMessage(anyString())).thenThrow(new RuntimeException("Send error")).thenThrow(new RuntimeException("Send error")).thenThrow(new RuntimeException("Send error"));
        // act
        sendMessageMethod.invoke(producer, message);
        // assert
        verify(mockProducer, times(3)).sendMessage(anyString());
    }

    /**
     * 测试结果为null的场景
     */
    @Test
    public void testSendMessageWithNullResult() throws Throwable {
        // arrange
        RefinementOperationExecuteMessage message = new RefinementOperationExecuteMessage();
        when(mockProducer.sendMessage(anyString())).thenReturn(null).thenReturn(null).thenReturn(null);
        // act
        sendMessageMethod.invoke(producer, message);
        // assert
        verify(mockProducer, times(3)).sendMessage(anyString());
    }
}
