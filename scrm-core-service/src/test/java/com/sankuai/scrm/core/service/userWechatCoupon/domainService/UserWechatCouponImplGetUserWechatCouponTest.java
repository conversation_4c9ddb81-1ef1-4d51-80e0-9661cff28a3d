package com.sankuai.scrm.core.service.userWechatCoupon.domainService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.unified.coupon.manage.api.UnifiedCouponInfoService;
import com.dianping.unified.coupon.manage.api.dto.UnifiedCouponDTO;
import com.dianping.unified.coupon.manage.api.request.BatchLoadCouponRequest;
import com.dianping.unified.coupon.manage.api.response.UnifiedCouponManageResponse;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.activity.fission.dto.activity.MktCouponInfoDTO;
import com.sankuai.dz.srcm.userWechatCoupon.dto.UserWechatCouponRequest;
import com.sankuai.dz.srcm.userWechatCoupon.dto.UserWechatCouponResponse;
import com.sankuai.scrm.core.service.activity.fission.service.ActivityFissionServiceImpl;
import com.sankuai.scrm.core.service.userWechatCoupon.dal.entity.ScrmUserWechatCoupon;
import com.sankuai.scrm.core.service.userWechatCoupon.dal.enums.UserWechatCouponUsed;
import com.sankuai.scrm.core.service.userWechatCoupon.dal.mapper.ScrmUserWechatCouponMapper;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

class UserWechatCouponImplGetUserWechatCouponTest {

    @Mock
    private ActivityFissionServiceImpl activityFissionService;

    @Mock
    private UnifiedCouponInfoService unifiedCouponInfoService;

    @InjectMocks
    private UserWechatCouponImpl userWechatCouponImpl;

    @Mock
    private ScrmUserWechatCouponMapper scrmUserWechatCouponMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    private UserWechatCouponRequest createValidRequest() {
        return UserWechatCouponRequest.builder().categoryIds("1,2,3").poiId(123L).mtCityId(1).dpCityId(2).mtUserId(456L).unionId("union123").build();
    }

    private List<ScrmUserWechatCoupon> createDbCoupons() {
        List<ScrmUserWechatCoupon> coupons = new ArrayList<>();
        coupons.add(createDbCoupon("coupon1", "code1"));
        return coupons;
    }

    private ScrmUserWechatCoupon createDbCoupon(String couponId, String couponCode) {
        ScrmUserWechatCoupon coupon = new ScrmUserWechatCoupon();
        coupon.setCouponid(couponId);
        coupon.setCouponcode(couponCode);
        coupon.setCategoryids("1,2,3");
        coupon.setPoiid(123L);
        coupon.setMtcityid(1);
        coupon.setDpcityid(2);
        coupon.setMtuserid(456L);
        coupon.setUnionid("union123");
        coupon.setUsed(UserWechatCouponUsed.NEVER_USED.getCode());
        return coupon;
    }

    private List<UnifiedCouponDTO> createMockUnifiedCoupons(boolean available, boolean unused) {
        UnifiedCouponDTO dto = mock(UnifiedCouponDTO.class);
        when(dto.getUnifiedCouponId()).thenReturn("coupon1");
        when(dto.isAvailable()).thenReturn(available);
        when(dto.isUsed()).thenReturn(!unused);
        return Collections.singletonList(dto);
    }

    private MktCouponInfoDTO createCouponInfo() {
        MktCouponInfoDTO dto = new MktCouponInfoDTO();
        dto.setCouponId("coupon1");
        dto.setCouponGroupName("Test Coupon");
        dto.setDiscountAmount(BigDecimal.TEN);
        dto.setPriceLimit(BigDecimal.valueOf(100));
        dto.setValidBeginTime(new Date());
        dto.setValidEndTime(new Date(System.currentTimeMillis() + 86400000));
        dto.setRemainStock(100);
        return dto;
    }

    /**
     * Test when request parameters are invalid
     */
    @Test
    public void testGetUserWechatCouponInvalidParams() throws Throwable {
        // arrange
        UserWechatCouponRequest request = new UserWechatCouponRequest();
        // act
        List<UserWechatCouponResponse> result = userWechatCouponImpl.getUserWechatCoupon(request);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when no coupons found in database
     */
    @Test
    public void testGetUserWechatCouponNoCouponsInDB() throws Throwable {
        // arrange
        UserWechatCouponRequest request = createValidRequest();
        when(scrmUserWechatCouponMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        List<UserWechatCouponResponse> result = userWechatCouponImpl.getUserWechatCoupon(request);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when unified coupon service fails
     */
    @Test
    public void testGetUserWechatCouponUnifiedServiceFails() throws Throwable {
        // arrange
        UserWechatCouponRequest request = createValidRequest();
        List<ScrmUserWechatCoupon> dbCoupons = createDbCoupons();
        when(scrmUserWechatCouponMapper.selectByExample(any())).thenReturn(dbCoupons);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> serviceResponse = new UnifiedCouponManageResponse<>();
        serviceResponse.setFail(500, "Service error");
        when(unifiedCouponInfoService.batchLoadCoupon(any())).thenReturn(serviceResponse);
        // act
        List<UserWechatCouponResponse> result = userWechatCouponImpl.getUserWechatCoupon(request);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when no available coupons from unified service
     */
    @Test
    public void testGetUserWechatCouponNoAvailableCoupons() throws Throwable {
        // arrange
        UserWechatCouponRequest request = createValidRequest();
        List<ScrmUserWechatCoupon> dbCoupons = createDbCoupons();
        when(scrmUserWechatCouponMapper.selectByExample(any())).thenReturn(dbCoupons);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> serviceResponse = new UnifiedCouponManageResponse<>();
        serviceResponse.setSuc(Collections.emptyList());
        when(unifiedCouponInfoService.batchLoadCoupon(any())).thenReturn(serviceResponse);
        // act
        List<UserWechatCouponResponse> result = userWechatCouponImpl.getUserWechatCoupon(request);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test when activity service fails to get coupon details
     */
    @Test
    public void testGetUserWechatCouponActivityServiceFails() throws Throwable {
        // arrange
        UserWechatCouponRequest request = createValidRequest();
        List<ScrmUserWechatCoupon> dbCoupons = createDbCoupons();
        List<UnifiedCouponDTO> unifiedCoupons = createMockUnifiedCoupons(true, true);
        when(scrmUserWechatCouponMapper.selectByExample(any())).thenReturn(dbCoupons);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> serviceResponse = new UnifiedCouponManageResponse<>();
        serviceResponse.setSuc(unifiedCoupons);
        when(unifiedCouponInfoService.batchLoadCoupon(any())).thenReturn(serviceResponse);
        when(activityFissionService.queryMktCouponInfo(any())).thenReturn(RemoteResponse.fail("Service error"));
        // act
        List<UserWechatCouponResponse> result = userWechatCouponImpl.getUserWechatCoupon(request);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test successful flow with unused coupons
     */
    @Test
    public void testGetUserWechatCouponSuccessWithUnusedCoupons() throws Throwable {
        // arrange
        UserWechatCouponRequest request = createValidRequest();
        List<ScrmUserWechatCoupon> dbCoupons = createDbCoupons();
        List<UnifiedCouponDTO> unifiedCoupons = createMockUnifiedCoupons(true, true);
        MktCouponInfoDTO couponInfo = createCouponInfo();
        when(scrmUserWechatCouponMapper.selectByExample(any())).thenReturn(dbCoupons);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> serviceResponse = new UnifiedCouponManageResponse<>();
        serviceResponse.setSuc(unifiedCoupons);
        when(unifiedCouponInfoService.batchLoadCoupon(any())).thenReturn(serviceResponse);
        when(activityFissionService.queryMktCouponInfo(any())).thenReturn(RemoteResponse.success(couponInfo));
        // act
        List<UserWechatCouponResponse> result = userWechatCouponImpl.getUserWechatCoupon(request);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(UserWechatCouponUsed.ALREADY_USED.getCode(), dbCoupons.get(0).getUsed());
    }

    /**
     * Test successful flow with already used coupons
     */
    @Test
    public void testGetUserWechatCouponSuccessWithUsedCoupons() throws Throwable {
        // arrange
        UserWechatCouponRequest request = createValidRequest();
        List<ScrmUserWechatCoupon> dbCoupons = createDbCoupons();
        dbCoupons.get(0).setUsed(UserWechatCouponUsed.ALREADY_USED.getCode());
        List<UnifiedCouponDTO> unifiedCoupons = createMockUnifiedCoupons(true, true);
        MktCouponInfoDTO couponInfo = createCouponInfo();
        when(scrmUserWechatCouponMapper.selectByExample(any())).thenReturn(dbCoupons);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> serviceResponse = new UnifiedCouponManageResponse<>();
        serviceResponse.setSuc(unifiedCoupons);
        when(unifiedCouponInfoService.batchLoadCoupon(any())).thenReturn(serviceResponse);
        when(activityFissionService.queryMktCouponInfo(any())).thenReturn(RemoteResponse.success(couponInfo));
        // act
        List<UserWechatCouponResponse> result = userWechatCouponImpl.getUserWechatCoupon(request);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(UserWechatCouponUsed.ALREADY_USED.getCode(), dbCoupons.get(0).getUsed());
    }

    /**
     * Test when some coupons are not available
     */
    @Test
    public void testGetUserWechatCouponSomeCouponsNotAvailable() throws Throwable {
        // arrange
        UserWechatCouponRequest request = createValidRequest();
        List<ScrmUserWechatCoupon> dbCoupons = new ArrayList<>();
        dbCoupons.add(createDbCoupon("coupon1", "code1"));
        dbCoupons.add(createDbCoupon("coupon2", "code2"));
        List<UnifiedCouponDTO> unifiedCoupons = new ArrayList<>();
        UnifiedCouponDTO availableCoupon = mock(UnifiedCouponDTO.class);
        when(availableCoupon.getUnifiedCouponId()).thenReturn("coupon1");
        when(availableCoupon.isAvailable()).thenReturn(false);
        when(availableCoupon.isUsed()).thenReturn(false);
        UnifiedCouponDTO unavailableCoupon = mock(UnifiedCouponDTO.class);
        when(unavailableCoupon.getUnifiedCouponId()).thenReturn("coupon2");
        when(unavailableCoupon.isAvailable()).thenReturn(true);
        when(unavailableCoupon.isUsed()).thenReturn(false);
        unifiedCoupons.add(availableCoupon);
        unifiedCoupons.add(unavailableCoupon);
        MktCouponInfoDTO couponInfo = createCouponInfo();
        when(scrmUserWechatCouponMapper.selectByExample(any())).thenReturn(dbCoupons);
        UnifiedCouponManageResponse<List<UnifiedCouponDTO>> serviceResponse = new UnifiedCouponManageResponse<>();
        serviceResponse.setSuc(unifiedCoupons);
        when(unifiedCouponInfoService.batchLoadCoupon(any())).thenReturn(serviceResponse);
        when(activityFissionService.queryMktCouponInfo(any())).thenReturn(RemoteResponse.success(couponInfo));
        // act
        List<UserWechatCouponResponse> result = userWechatCouponImpl.getUserWechatCoupon(request);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
    }

    /**
     * Test when request is null
     */
    @Test
    public void testGetUserWechatCouponNullRequest() throws Throwable {
        // arrange
        // act
        List<UserWechatCouponResponse> result = userWechatCouponImpl.getUserWechatCoupon(null);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }
}
