package com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.when;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutePlanDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecutePlanDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationExecutePlanDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.DdlResultDTO;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import com.sankuai.scrm.core.service.util.CronUtils;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ProcessOrchestrationWriteDomainServiceCheckNextRunTimeForUpdateTest {

    @Mock
    private ExtScrmAmProcessOrchestrationExecutePlanDOMapper extScrmAmProcessOrchestrationExecutePlanDOMapper;

    @InjectMocks
    private ProcessOrchestrationWriteDomainService processOrchestrationWriteDomainService;

    private Date now;

    private Date tomorrow;

    private Date endTime;

    private Date futureTimeToday;

    private String futureDateString;

    @BeforeEach
    void setUp() {
        now = new Date();
        Calendar futureCalendar = Calendar.getInstance();
        futureCalendar.setTime(now);
        futureCalendar.add(Calendar.HOUR_OF_DAY, 1);
        futureTimeToday = futureCalendar.getTime();
        futureDateString = DateUtil.formatYMdHms(futureTimeToday);
        Calendar calendar = Calendar.getInstance();
        calendar.add(Calendar.DAY_OF_MONTH, 1);
        calendar.set(Calendar.HOUR_OF_DAY, 0);
        calendar.set(Calendar.MINUTE, 0);
        calendar.set(Calendar.SECOND, 0);
        calendar.set(Calendar.MILLISECOND, 0);
        tomorrow = calendar.getTime();
        calendar.add(Calendar.DAY_OF_MONTH, 2);
        endTime = calendar.getTime();
    }

    /**
     * Test when current time is after end time
     */
    @Test
    public void testCheckNextRunTimeForUpdate_WhenNowAfterEndTime() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setEndTime(new Date(now.getTime() - 100000));
        DdlResultDTO result = new DdlResultDTO();
        // act
        DdlResultDTO actualResult = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(processOrchestrationInfoDO, result);
        // assert
        Assertions.assertNotNull(actualResult);
        Assertions.assertSame(result, actualResult);
    }

    /**
     * Test when execute plans exist and start time is before now
     */
    @Test
    public void testCheckNextRunTimeForUpdate_WhenExecutePlanExistsAndStartTimeBeforeNow() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setEndTime(endTime);
        processOrchestrationInfoDO.setCronComment(DateUtil.formatYMdHms(new Date(now.getTime() - 1000)));
        processOrchestrationInfoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        processOrchestrationInfoDO.setValidVersion("1.0");
        DdlResultDTO result = new DdlResultDTO();
        List<ScrmAmProcessOrchestrationExecutePlanDO> executePlanDOs = new ArrayList<>();
        ScrmAmProcessOrchestrationExecutePlanDO executePlanDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        executePlanDOs.add(executePlanDO);
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(executePlanDOs);
        // act
        DdlResultDTO actualResult = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(processOrchestrationInfoDO, result);
        // assert
        Assertions.assertNotNull(actualResult);
        Assertions.assertSame(result, actualResult);
    }

    /**
     * Test when execute plans exist, is timed process and start time is today
     */
    @Test
    public void testCheckNextRunTimeForUpdate_WhenExecutePlanExistsAndTimedProcessAndStartTimeToday() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setEndTime(endTime);
        processOrchestrationInfoDO.setCronComment(futureDateString);
        processOrchestrationInfoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        processOrchestrationInfoDO.setValidVersion("1.0");
        DdlResultDTO result = new DdlResultDTO();
        List<ScrmAmProcessOrchestrationExecutePlanDO> executePlanDOs = new ArrayList<>();
        ScrmAmProcessOrchestrationExecutePlanDO executePlanDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        executePlanDOs.add(executePlanDO);
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(executePlanDOs);
        // act
        DdlResultDTO actualResult = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(processOrchestrationInfoDO, result);
        // assert
        Assertions.assertNotNull(actualResult);
        Assertions.assertTrue(actualResult.isWillRunToday());
        Assertions.assertNotNull(actualResult.getNextRunTime());
    }

    /**
     * Test when execute plans exist, is not timed process and start time is today
     */
    @Test
    public void testCheckNextRunTimeForUpdate_WhenExecutePlanExistsAndNotTimedProcessAndStartTimeToday() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setEndTime(endTime);
        processOrchestrationInfoDO.setCron("0 * * * * ?");
        processOrchestrationInfoDO.setCronComment(futureDateString);
        processOrchestrationInfoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue().byteValue());
        processOrchestrationInfoDO.setValidVersion("1.0");
        DdlResultDTO result = new DdlResultDTO();
        List<ScrmAmProcessOrchestrationExecutePlanDO> executePlanDOs = new ArrayList<>();
        ScrmAmProcessOrchestrationExecutePlanDO executePlanDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        executePlanDO.setTaskStartTime(futureTimeToday);
        executePlanDOs.add(executePlanDO);
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(executePlanDOs);
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        try (MockedStatic<CronUtils> cronUtilsMock = Mockito.mockStatic(CronUtils.class)) {
            cronUtilsMock.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(futureTimeToday);
            // act
            DdlResultDTO actualResult = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(processOrchestrationInfoDO, result);
            // assert
            Assertions.assertNotNull(actualResult);
            Assertions.assertTrue(actualResult.isWillRunToday());
            Assertions.assertNotNull(actualResult.getNextRunTime());
        }
    }

    /**
     * Test when execute plans exist and start time is tomorrow but before end time
     */
    @Test
    public void testCheckNextRunTimeForUpdate_WhenExecutePlanExistsAndStartTimeTomorrowBeforeEndTime() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setEndTime(endTime);
        processOrchestrationInfoDO.setCronComment(DateUtil.formatYMdHms(new Date(tomorrow.getTime() + 10000)));
        processOrchestrationInfoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        processOrchestrationInfoDO.setValidVersion("1.0");
        DdlResultDTO result = new DdlResultDTO();
        List<ScrmAmProcessOrchestrationExecutePlanDO> executePlanDOs = new ArrayList<>();
        ScrmAmProcessOrchestrationExecutePlanDO executePlanDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        executePlanDOs.add(executePlanDO);
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(executePlanDOs);
        // act
        DdlResultDTO actualResult = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(processOrchestrationInfoDO, result);
        // assert
        Assertions.assertNotNull(actualResult);
        Assertions.assertNotNull(actualResult.getNextRunTime());
        Assertions.assertFalse(actualResult.isWillRunToday());
    }

    /**
     * Test when execute plans exist but cron comment is blank
     */
    @Test
    public void testCheckNextRunTimeForUpdate_WhenExecutePlanExistsButCronCommentBlank() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setEndTime(endTime);
        processOrchestrationInfoDO.setCronComment("");
        processOrchestrationInfoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        DdlResultDTO result = new DdlResultDTO();
        List<ScrmAmProcessOrchestrationExecutePlanDO> executePlanDOs = new ArrayList<>();
        ScrmAmProcessOrchestrationExecutePlanDO executePlanDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        executePlanDOs.add(executePlanDO);
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(executePlanDOs);
        // act
        DdlResultDTO actualResult = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(processOrchestrationInfoDO, result);
        // assert
        Assertions.assertNotNull(actualResult);
        Assertions.assertSame(result, actualResult);
    }

    /**
     * Test when no execute plans exist and is timed process
     */
    @Test
    public void testCheckNextRunTimeForUpdate_WhenNoExecutePlansAndTimedProcess() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setEndTime(endTime);
        processOrchestrationInfoDO.setCronComment(futureDateString);
        processOrchestrationInfoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        processOrchestrationInfoDO.setValidVersion("1.0");
        DdlResultDTO result = new DdlResultDTO();
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.emptyList());
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.insert(any())).thenReturn(1);
        // act
        DdlResultDTO actualResult = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(processOrchestrationInfoDO, result);
        // assert
        Assertions.assertNotNull(actualResult);
        Assertions.assertTrue(actualResult.isWillRunToday());
        Assertions.assertNotNull(actualResult.getNextRunTime());
    }

    /**
     * Test when no execute plans exist and is not timed process
     */
    @Test
    public void testCheckNextRunTimeForUpdate_WhenNoExecutePlansAndNotTimedProcess() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setEndTime(endTime);
        processOrchestrationInfoDO.setCron("0 * * * * ?");
        processOrchestrationInfoDO.setCronComment(futureDateString);
        processOrchestrationInfoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue().byteValue());
        processOrchestrationInfoDO.setValidVersion("1.0");
        DdlResultDTO result = new DdlResultDTO();
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.emptyList());
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.insert(any(ScrmAmProcessOrchestrationExecutePlanDO.class))).thenAnswer(invocation -> {
            ScrmAmProcessOrchestrationExecutePlanDO planDO = invocation.getArgument(0);
            planDO.setTaskStartTime(futureTimeToday);
            return 1;
        });
        try (MockedStatic<CronUtils> cronUtilsMock = Mockito.mockStatic(CronUtils.class)) {
            cronUtilsMock.when(() -> CronUtils.getCronNextTime(anyString())).thenReturn(futureTimeToday);
            // act
            DdlResultDTO actualResult = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(processOrchestrationInfoDO, result);
            // assert
            Assertions.assertNotNull(actualResult);
            Assertions.assertTrue(actualResult.isWillRunToday());
            Assertions.assertNotNull(actualResult.getNextRunTime());
        }
    }

    /**
     * Test when no execute plans exist and start time is tomorrow
     */
    @Test
    public void testCheckNextRunTimeForUpdate_WhenNoExecutePlansAndStartTimeTomorrow() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setEndTime(endTime);
        processOrchestrationInfoDO.setCronComment(DateUtil.formatYMdHms(new Date(tomorrow.getTime() + 10000)));
        processOrchestrationInfoDO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        processOrchestrationInfoDO.setValidVersion("1.0");
        DdlResultDTO result = new DdlResultDTO();
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.emptyList());
        // act
        DdlResultDTO actualResult = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(processOrchestrationInfoDO, result);
        // assert
        Assertions.assertNotNull(actualResult);
        Assertions.assertNotNull(actualResult.getNextRunTime());
        Assertions.assertFalse(actualResult.isWillRunToday());
    }

    /*@Test(expected = Exception.class)
    public void testCreateProcessOrchestrationNullDTO() throws Throwable {
        processOrchestrationWriteDomainService.createProcessOrchestration(null);
    }

    @Test(expected = Exception.class)
    public void testCreateProcessOrchestrationNullCrowdPackType() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createProcessOrchestrationDTO();
        processOrchestrationDTO.setCrowdPackType(null);
        processOrchestrationWriteDomainService.createProcessOrchestration(processOrchestrationDTO);
    }*/
    /**
     * Test when exception occurs
     */
    @Test
    public void testCheckNextRunTimeForUpdate_WhenExceptionOccurs() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationInfoDO processOrchestrationInfoDO = new ScrmAmProcessOrchestrationInfoDO();
        processOrchestrationInfoDO.setId(1L);
        processOrchestrationInfoDO.setEndTime(endTime);
        processOrchestrationInfoDO.setCronComment(futureDateString);
        DdlResultDTO result = new DdlResultDTO();
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenThrow(new RuntimeException("Test exception"));
        // act
        DdlResultDTO actualResult = processOrchestrationWriteDomainService.checkNextRunTimeForUpdate(processOrchestrationInfoDO, result);
        // assert
        Assertions.assertNull(actualResult);
    }
}
