package com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack;

import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackBaseInfoDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackBaseInfoDOMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class CrowdPackReadDomainServiceTest2 {

    @InjectMocks
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock(lenient = true)
    private ExtScrmAmCrowdPackBaseInfoDOMapper extScrmAmCrowdPackBaseInfoDOMapper;

    @Test
    public void testQueryCrowdPackCountWithNoSpecificType() {
        when(extScrmAmCrowdPackBaseInfoDOMapper.countByExample(any(ScrmAmCrowdPackBaseInfoDOExample.class))).thenReturn(10L);
        ScrmCrowdPackDTO request = new ScrmCrowdPackDTO();
        request.setAppId("hanchengfuwu");
        request.setType(null);
        long result = crowdPackReadDomainService.queryCrowdPackCount(request);
        assertEquals(10L, result);
    }
}
