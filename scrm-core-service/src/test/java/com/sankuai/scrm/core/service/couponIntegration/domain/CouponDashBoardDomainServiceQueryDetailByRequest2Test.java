package com.sankuai.scrm.core.service.couponIntegration.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.argThat;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.couponIntegration.dto.CouponDataDetailDTO;
import com.sankuai.dz.srcm.couponIntegration.enums.CreationSceneEnum;
import com.sankuai.dz.srcm.couponIntegration.enums.StatusEnum;
import com.sankuai.dz.srcm.couponIntegration.request.CouponDataDetailQueryRequest;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.couponIntegration.utils.DateUtils;
import com.sankuai.scrm.core.service.dashboard.domain.Utils;
import com.sankuai.scrm.core.service.infrastructure.acl.sso.SsosvOpenApi;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Date;
import java.util.List;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

/**
 * Test class for CouponDashBoardDomainService.queryDetailByRequest(CouponDataDetailQueryRequest) method
 */
public class CouponDashBoardDomainServiceQueryDetailByRequest2Test {

    @InjectMocks
    private CouponDashBoardDomainService couponDashBoardDomainService;

    @Mock
    private SsosvOpenApi ssosvOpenApi;

    @Mock
    private Utils utils;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private ScrmSceneCouponRecordsMapper sceneCouponRecordDOMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试 corpId 为空的情况
     */
    //    @Test
    //    public void testDailyCouponDataSummaryCorpIdIsNull() {
    /**
     * Test case: Normal query with valid data
     * Expected: Return list with converted DTOs
     */
    @Test
    public void testQueryDetailByRequest_WhenQuerySuccessful() throws Throwable {
        // arrange
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setAppId("testAppId");
        ScrmSceneCouponRecords record = new ScrmSceneCouponRecords();
        record.setCoupongroupid("group1");
        record.setUnifiedcouponid("coupon1");
        record.setUserid(123L);
        record.setAppid("testAppId");
        record.setCouponamount(new BigDecimal("100"));
        record.setAddTime(new Date());
        record.setScenetype(CreationSceneEnum.MANUAL_UPLOAD.getCode());
        record.setStatisticstatus(StatusEnum.COUNTING.getCode());
        when(sceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(Arrays.asList(record));
        when(utils.getCorpNameByAppId("testAppId")).thenReturn("TestCorp");
        // act
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        // assert
        assertFalse(CollectionUtils.isEmpty(result));
        assertEquals(1, result.size());
        CouponDataDetailDTO dto = result.get(0);
        assertEquals("group1", dto.getCouponGroupId());
        assertEquals("coupon1", dto.getCouponId());
        assertEquals("TestCorp", dto.getCorpName());
        assertEquals("100", dto.getCouponValue());
        assertEquals("否", dto.getIsUsed());
    }

    //        // arrange
    //        String corpId = null;
    //        // act
    //        couponDashBoardDomainService.dailyCouponDataSummary(corpId);
    //        // assert
    //        verify(corpAppConfigRepository, never()).getAppIdByCorpId(anyString());
    //    }
    /**
     * 测试 corpId 不为空，但 appId 为空的情况
     */
    //    @Test
    //    public void testDailyCouponDataSummaryAppIdIsNull() {
    //        // arrange
    //        String corpId = "corpId";
    //        when(corpAppConfigRepository.getAppIdByCorpId(corpId)).thenReturn(null);
    //        // act
    //        couponDashBoardDomainService.dailyCouponDataSummary(corpId);
    //        // assert
    //        verify(sceneCouponRecordDOMapper, never()).selectByExample(any());
    //        verify(couponDataSummaryDOMapper, never()).insertSelective(any(ScrmCouponDataSummaryDO.class));
    //    }
    /**
     * 测试 corpId 和 appId 都不为空，但查询 ScrmSceneCouponRecordDO 对象列表为空的情况
     */
    //    @Test
    //    public void testDailyCouponDataSummaryCouponRecordDOListIsEmpty() {
    //        // arrange
    //        String corpId = "corpId";
    //        when(sceneCouponRecordDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
    //        // act
    //        couponDashBoardDomainService.dailyCouponDataSummary(corpId);
    //        // assert
    //        verify(couponDataSummaryDOMapper, times(2)).insertSelective(any(ScrmCouponDataSummaryDO.class));
    /**
     * Test case: AppId is empty, using authenticated appIds
     * Expected: Query with authenticated appIds
     */
    @Test
    public void testQueryDetailByRequest_WhenAppIdIsEmpty() throws Throwable {
        // arrange
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setPathAppId("pathAppId");
        request.setAccessToken("token");
        List<String> corpIds = Arrays.asList("corp1", "corp2");
        List<String> appIds = Arrays.asList("app1", "app2");
        when(ssosvOpenApi.queryLoginNameByAccessToken("token")).thenReturn("misId");
        when(utils.authentication("misId", "pathAppId")).thenReturn(corpIds);
        when(corpAppConfigRepository.getAppIdByCorpId("corp1")).thenReturn("app1");
        when(corpAppConfigRepository.getAppIdByCorpId("corp2")).thenReturn("app2");
        when(sceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(new ArrayList<>());
        // act
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
        ArgumentCaptor<ScrmSceneCouponRecordsExample> exampleCaptor = ArgumentCaptor.forClass(ScrmSceneCouponRecordsExample.class);
        verify(sceneCouponRecordDOMapper).selectByExample(exampleCaptor.capture());
        ScrmSceneCouponRecordsExample capturedExample = exampleCaptor.getValue();
        assertNotNull(capturedExample);
        assertFalse(capturedExample.getOredCriteria().isEmpty());
    }

    /**
     * Test case: Query with date range conditions
     * Expected: Query includes date range criteria
     */
    @Test
    public void testQueryDetailByRequest_WithDateRangeConditions() throws Throwable {
        // arrange
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setAppId("testAppId");
        request.setSendStartTime("2023-01-01");
        request.setSendEndTime("2023-12-31");
        request.setUseStartTime("2023-01-01");
        request.setUseEndTime("2023-12-31");
        when(sceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(new ArrayList<>());
        // act
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
        ArgumentCaptor<ScrmSceneCouponRecordsExample> exampleCaptor = ArgumentCaptor.forClass(ScrmSceneCouponRecordsExample.class);
        verify(sceneCouponRecordDOMapper).selectByExample(exampleCaptor.capture());
        ScrmSceneCouponRecordsExample capturedExample = exampleCaptor.getValue();
        assertNotNull(capturedExample);
        assertFalse(capturedExample.getOredCriteria().isEmpty());
    }

    /**
     * Test case: Query with coupon group id condition
     * Expected: Query includes coupon group id criteria
     */
    @Test
    public void testQueryDetailByRequest_WithCouponGroupIdCondition() throws Throwable {
        // arrange
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setAppId("testAppId");
        request.setCouponGroupId("testGroupId");
        when(sceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(new ArrayList<>());
        // act
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
        ArgumentCaptor<ScrmSceneCouponRecordsExample> exampleCaptor = ArgumentCaptor.forClass(ScrmSceneCouponRecordsExample.class);
        verify(sceneCouponRecordDOMapper).selectByExample(exampleCaptor.capture());
        ScrmSceneCouponRecordsExample capturedExample = exampleCaptor.getValue();
        assertNotNull(capturedExample);
        assertFalse(capturedExample.getOredCriteria().isEmpty());
    }

    /**
     * Test case: Query with user id condition
     * Expected: Query includes user id criteria
     */
    @Test
    public void testQueryDetailByRequest_WithUserIdCondition() throws Throwable {
        // arrange
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageNumber(1);
        request.setPageSize(10);
        request.setAppId("testAppId");
        request.setUserId(123L);
        when(sceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(new ArrayList<>());
        // act
        List<CouponDataDetailDTO> result = couponDashBoardDomainService.queryDetailByRequest(request);
        // assert
        assertTrue(CollectionUtils.isEmpty(result));
        ArgumentCaptor<ScrmSceneCouponRecordsExample> exampleCaptor = ArgumentCaptor.forClass(ScrmSceneCouponRecordsExample.class);
        verify(sceneCouponRecordDOMapper).selectByExample(exampleCaptor.capture());
        ScrmSceneCouponRecordsExample capturedExample = exampleCaptor.getValue();
        assertNotNull(capturedExample);
        assertFalse(capturedExample.getOredCriteria().isEmpty());
    }
}
