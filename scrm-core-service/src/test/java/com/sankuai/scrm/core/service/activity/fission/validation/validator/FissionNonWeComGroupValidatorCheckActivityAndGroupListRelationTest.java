package com.sankuai.scrm.core.service.activity.fission.validation.validator;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.activity.fission.enums.StatusEnum;
import com.sankuai.dz.srcm.activity.fission.request.GroupFissionActivityRequest;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionActivity;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionActivityMapper;
import com.sankuai.scrm.core.service.activity.fission.domain.GroupFissionActivityDomainService;
import com.sankuai.scrm.core.service.activity.fission.validation.enums.FissionChainMarkEnum;
import com.sankuai.scrm.core.service.activity.fission.validation.enums.FissionChainOperationEnum;
import com.sankuai.scrm.core.service.activity.fission.validation.exception.FissionValidatorException;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.PersonalWxGroupInfoDomainService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import java.lang.reflect.Method;
import java.util.*;
import java.util.Date;
import org.junit.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class FissionNonWeComGroupValidatorCheckActivityAndGroupListRelationTest {

    @InjectMocks
    private FissionNonWeComGroupValidator fissionNonWeComGroupValidator;

    @Mock
    private PersonalWxGroupInfoDomainService personalWxGroupInfoDomainService;

    @Mock
    private GroupFissionActivityDomainService groupFissionActivityDomainService;

    private List<Long> groupIdList;

    private Long activityId;

    private Long startTime;

    private Long endTime;

    @BeforeEach
    public void setUp() {
        groupIdList = Arrays.asList(1L, 2L, 3L);
        activityId = 1L;
        startTime = 1000L;
        endTime = 2000L;
    }

    private List<Long> invokePrivateMethod(List<Long> groupIdList, Long activityId, Long startTime, Long endTime) throws Exception {
        Method method = FissionNonWeComGroupValidator.class.getDeclaredMethod("checkActivityAndGroupListRelation", List.class, Long.class, Long.class, Long.class);
        method.setAccessible(true);
        return (List<Long>) method.invoke(fissionNonWeComGroupValidator, groupIdList, activityId, startTime, endTime);
    }

    /**
     * Test when groupIdList is empty
     */
    @Test
    public void testCheckActivityAndGroupListRelationWhenGroupIdListIsEmpty() throws Throwable {
        // arrange
        groupIdList = Collections.emptyList();
        // act
        List<Long> result = invokePrivateMethod(groupIdList, activityId, startTime, endTime);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test when activityId is null
     */
    @Test
    public void testCheckActivityAndGroupListRelationWhenActivityIdIsNull() throws Throwable {
        // arrange
        activityId = null;
        // act
        List<Long> result = invokePrivateMethod(groupIdList, activityId, startTime, endTime);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test when groupIdList has groups but no activities
     */
    @Test
    public void testCheckActivityAndGroupListRelationWhenGroupIdListHasNoActivities() throws Throwable {
        // arrange
        when(personalWxGroupInfoDomainService.queryGroupActivitiesMap(groupIdList)).thenReturn(new HashMap<>());
        // act
        List<Long> result = invokePrivateMethod(groupIdList, activityId, startTime, endTime);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test when groupIdList has groups with activities but no time overlap
     */
    @Test
    public void testCheckActivityAndGroupListRelationWhenGroupIdListHasActivitiesButNoOverlap() throws Throwable {
        // arrange
        Map<Long, Set<Long>> groupActivitiesMap = new HashMap<>();
        groupActivitiesMap.put(1L, new HashSet<>(Arrays.asList(1L)));
        groupActivitiesMap.put(2L, new HashSet<>(Arrays.asList(2L)));
        when(personalWxGroupInfoDomainService.queryGroupActivitiesMap(groupIdList)).thenReturn(groupActivitiesMap);
        List<GroupFissionActivity> activities = new ArrayList<>();
        GroupFissionActivity activity1 = new GroupFissionActivity();
        activity1.setId(1L);
        // No overlap
        activity1.setStartTime(new Date(startTime + 1000));
        activity1.setEndTime(new Date(endTime + 1000));
        activity1.setStatus(StatusEnum.EFFECTIvE.getCode());
        GroupFissionActivity activity2 = new GroupFissionActivity();
        activity2.setId(2L);
        // No overlap
        activity2.setStartTime(new Date(startTime - 2000));
        activity2.setEndTime(new Date(endTime - 2000));
        activity2.setStatus(StatusEnum.EFFECTIvE.getCode());
        activities.add(activity1);
        activities.add(activity2);
        when(groupFissionActivityDomainService.queryByActivityIds(any())).thenReturn(activities);
        // act
        List<Long> result = invokePrivateMethod(groupIdList, activityId, startTime, endTime);
        // assert
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test when groupIdList has groups with activities and time overlap
     */
    @Test
    public void testCheckActivityAndGroupListRelationWhenGroupIdListHasActivitiesWithOverlap() throws Throwable {
        // arrange
        Map<Long, Set<Long>> groupActivitiesMap = new HashMap<>();
        // Group 1 has two activities
        groupActivitiesMap.put(1L, new HashSet<>(Arrays.asList(1L, 2L)));
        // Group 2 has one activity
        groupActivitiesMap.put(2L, new HashSet<>(Arrays.asList(3L)));
        when(personalWxGroupInfoDomainService.queryGroupActivitiesMap(groupIdList)).thenReturn(groupActivitiesMap);
        List<GroupFissionActivity> activities = new ArrayList<>();
        // Activity 1 overlaps with test time range
        GroupFissionActivity activity1 = new GroupFissionActivity();
        activity1.setId(1L);
        activity1.setStartTime(new Date(startTime - 500));
        activity1.setEndTime(new Date(endTime - 500));
        activity1.setStatus(StatusEnum.EFFECTIvE.getCode());
        // Activity 2 also overlaps with test time range
        GroupFissionActivity activity2 = new GroupFissionActivity();
        activity2.setId(2L);
        activity2.setStartTime(new Date(startTime + 500));
        activity2.setEndTime(new Date(endTime + 500));
        activity2.setStatus(StatusEnum.EFFECTIvE.getCode());
        // Activity 3 doesn't overlap (shouldn't be included in result)
        GroupFissionActivity activity3 = new GroupFissionActivity();
        activity3.setId(3L);
        activity3.setStartTime(new Date(startTime + 2000));
        activity3.setEndTime(new Date(endTime + 2000));
        activity3.setStatus(StatusEnum.EFFECTIvE.getCode());
        activities.add(activity1);
        activities.add(activity2);
        activities.add(activity3);
        when(groupFissionActivityDomainService.queryByActivityIds(any())).thenReturn(activities);
        // act
        List<Long> result = invokePrivateMethod(groupIdList, activityId, startTime, endTime);
        // assert
        // Only group 1 should have overlap
        assertEquals(Collections.singletonList(1L), result);
    }

    /**
     * Test when activities exist but are not EFFECTIVE
     */
    @Test
    public void testCheckActivityAndGroupListRelationWhenActivitiesAreNotEffective() throws Throwable {
        // arrange
        Map<Long, Set<Long>> groupActivitiesMap = new HashMap<>();
        groupActivitiesMap.put(1L, new HashSet<>(Arrays.asList(1L)));
        when(personalWxGroupInfoDomainService.queryGroupActivitiesMap(groupIdList)).thenReturn(groupActivitiesMap);
        List<GroupFissionActivity> activities = new ArrayList<>();
        GroupFissionActivity activity1 = new GroupFissionActivity();
        activity1.setId(1L);
        // Would overlap if effective
        activity1.setStartTime(new Date(startTime - 500));
        activity1.setEndTime(new Date(endTime - 500));
        // Not effective
        activity1.setStatus(StatusEnum.FINISHED.getCode());
        activities.add(activity1);
        when(groupFissionActivityDomainService.queryByActivityIds(any())).thenReturn(activities);
        // act
        List<Long> result = invokePrivateMethod(groupIdList, activityId, startTime, endTime);
        // assert
        assertEquals(Collections.emptyList(), result);
    }
}
