package com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.crowdpack;

import static org.junit.Assert.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackDetailInfoDO;
import org.junit.Assert;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ScrmCrowdPackDetailInfoConverterTest {

    private ScrmCrowdPackDetailInfoConverter converter = new ScrmCrowdPackDetailInfoConverter();

    /**
     * 测试 convertToDTO 方法，当输入为 null 时，应返回 null
     */
    @Test
    public void testConvertToDTOWhenInputIsNull() {
        // arrange
        ScrmAmCrowdPackDetailInfoDO input = null;
        // act
        ScrmCrowdPackDetailInfoDTO result = converter.convertToDTO(input);
        // assert
        assertNull(result);
    }

    /**
     * 测试 convertToDTO 方法，当输入不为 null 时，应返回一个新的 ScrmCrowdPackDetailInfoDTO 对象，其属性值与输入对象的属性值相同
     */
    @Test
    public void testConvertToDTOWhenInputIsNotNull() {
        // arrange
        ScrmAmCrowdPackDetailInfoDO input = new ScrmAmCrowdPackDetailInfoDO();
        input.setId(1L);
        input.setPackId(2L);
        input.setExternalUserId(3L);
        input.setExternalUserWxUnionId("externalUserWxUnionId");
        input.setPackVersion("packVersion");
        input.setWxexternaluserid("wxexternaluserid");
        input.setAppId("appId");
        // act
        ScrmCrowdPackDetailInfoDTO result = converter.convertToDTO(input);
        // assert
        assertNotNull(result);
        assertEquals(input.getId(), result.getId());
        assertEquals(input.getPackId(), result.getPackId());
        assertEquals(input.getExternalUserId(), result.getExternalUserId());
        assertEquals(input.getExternalUserWxUnionId(), result.getExternalUserWxUnionId());
        assertEquals(input.getPackVersion(), result.getPackVersion());
        assertEquals(input.getWxexternaluserid(), result.getWxexternaluserid());
        assertEquals(input.getAppId(), result.getAppId());
    }

    /**
     * 测试 convertToDO 方法，当输入为 null 时，应返回 null
     */
    @Test
    public void testConvertToDONullInput() {
        // arrange
        ScrmCrowdPackDetailInfoDTO input = null;
        // act
        ScrmAmCrowdPackDetailInfoDO result = converter.convertToDO(input);
        // assert
        Assert.assertNull(result);
    }

    /**
     * 测试 convertToDO 方法，当输入不为 null 时，应返回一个新的 ScrmAmCrowdPackDetailInfoDO 对象，其属性值与输入对象的属性值相同
     */
    @Test
    public void testConvertToDONonNullInput() {
        // arrange
        ScrmCrowdPackDetailInfoDTO input = new ScrmCrowdPackDetailInfoDTO();
        input.setId(1L);
        input.setPackId(2L);
        input.setExternalUserId(3L);
        input.setExternalUserWxUnionId("externalUserWxUnionId");
        input.setPackVersion("packVersion");
        input.setWxexternaluserid("wxexternaluserid");
        input.setAppId("appId");
        // act
        ScrmAmCrowdPackDetailInfoDO result = converter.convertToDO(input);
        // assert
        Assert.assertNotNull(result);
        Assert.assertEquals(input.getId(), result.getId());
        Assert.assertEquals(input.getPackId(), result.getPackId());
        Assert.assertEquals(input.getExternalUserId(), result.getExternalUserId());
        Assert.assertEquals(input.getExternalUserWxUnionId(), result.getExternalUserWxUnionId());
        Assert.assertEquals(input.getPackVersion(), result.getPackVersion());
        Assert.assertEquals(input.getWxexternaluserid(), result.getWxexternaluserid());
        Assert.assertEquals(input.getAppId(), result.getAppId());
    }
}
