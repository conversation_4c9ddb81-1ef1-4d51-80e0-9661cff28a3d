package com.sankuai.scrm.core.service.couponIntegration.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.edc.open.common.util.GsonUtils;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmCouponConsumeRecordMessage;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.StrategistCouponInfo;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmCouponOrderAmountDetailMapper;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.StrategistCouponInfoMapper;
import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.util.Collections;
import java.util.Date;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CouponConsumeRecordServiceCheckToUpdateOrInsertFromScrmSceneCouponRecordsTest {

    @InjectMocks
    private CouponConsumeRecordService couponConsumeRecordService;

    @Mock
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordsMapper;

    @Mock
    private StrategistCouponInfoMapper strategistCouponInfoMapper;

    @Mock
    private ScrmCouponOrderAmountDetailMapper scrmCouponOrderAmountDetailMapper;

    @Mock
    private ExecuteManagementService executeManagementService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * 测试场景：ScrmCouponConsumeRecordMessage对象在ScrmSceneCouponRecords表中存在
     */
    @Test
    public void testCheckToUpdateOrInsertFromScrmSceneCouponRecords_Exist() throws Throwable {
        // arrange
        ScrmCouponConsumeRecordMessage recordMessage = new ScrmCouponConsumeRecordMessage();
        recordMessage.setCouponGroupId("1");
        recordMessage.setUnifiedCouponId("1");
        when(scrmSceneCouponRecordsMapper.countByExample(any())).thenReturn(1L);
        // act
        Method method = CouponConsumeRecordService.class.getDeclaredMethod("checkToUpdateOrInsertFromScrmSceneCouponRecords", ScrmCouponConsumeRecordMessage.class);
        method.setAccessible(true);
        Long result = (Long) method.invoke(couponConsumeRecordService, recordMessage);
        // assert
        assertEquals(1L, result);
    }

    /**
     * 测试场景：ScrmCouponConsumeRecordMessage对象在ScrmSceneCouponRecords表中不存在
     */
    @Test
    public void testCheckToUpdateOrInsertFromScrmSceneCouponRecords_NotExist() throws Throwable {
        // arrange
        ScrmCouponConsumeRecordMessage recordMessage = new ScrmCouponConsumeRecordMessage();
        recordMessage.setCouponGroupId("1");
        recordMessage.setUnifiedCouponId("1");
        when(scrmSceneCouponRecordsMapper.countByExample(any())).thenReturn(0L);
        // act
        Method method = CouponConsumeRecordService.class.getDeclaredMethod("checkToUpdateOrInsertFromScrmSceneCouponRecords", ScrmCouponConsumeRecordMessage.class);
        method.setAccessible(true);
        Long result = (Long) method.invoke(couponConsumeRecordService, recordMessage);
        // assert
        assertEquals(0L, result);
    }

    /**
     * 测试正常日期字符串转换为Date对象
     */
    @Test
    public void testGetDateFromStrValidDate() throws Throwable {
        // arrange
        String dateStr = "2023-10-01 12:34:56";
        LocalDateTime expectedDateTime = LocalDateTime.parse(dateStr, DateTimeFormatter.ofPattern("yyyy-MM-dd HH:mm:ss"));
        Date expectedDate = Date.from(expectedDateTime.atZone(ZoneId.systemDefault()).toInstant());
        // act
        Date result = couponConsumeRecordService.getDateFromStr(dateStr);
        // assert
        assertNotNull(result);
        assertEquals(expectedDate, result);
    }

    /**
     * 测试输入字符串为null时返回null
     */
    @Test
    public void testGetDateFromStrNullInput() throws Throwable {
        // arrange
        String dateStr = null;
        // act
        Date result = couponConsumeRecordService.getDateFromStr(dateStr);
        // assert
        assertNull(result);
    }

    /**
     * 测试输入字符串为空字符串时返回null
     */
    @Test
    public void testGetDateFromStrEmptyInput() throws Throwable {
        // arrange
        String dateStr = "";
        // act
        Date result = couponConsumeRecordService.getDateFromStr(dateStr);
        // assert
        assertNull(result);
    }

    /**
     * 测试输入字符串格式不正确时抛出DateTimeParseException
     */
    @Test
    public void testGetDateFromStrInvalidFormat() throws Throwable {
        // arrange
        String dateStr = "2023/10/01 12:34:56";
        // act & assert
        assertThrows(java.time.format.DateTimeParseException.class, () -> {
            couponConsumeRecordService.getDateFromStr(dateStr);
        });
    }

    /**
     * Test handling of valid message
     */
    @Test
    void testHandleCouponConsumeRecordMsgWithValidMessage() throws Throwable {
        // arrange
        String msg = "{\"couponGroupId\":\"123\"}";
        ScrmCouponConsumeRecordMessage recordMessage = new ScrmCouponConsumeRecordMessage();
        recordMessage.setCouponGroupId("123");
        recordMessage.setUnifiedCouponId("456");
        StrategistCouponInfo strategistCouponInfo = new StrategistCouponInfo();
        strategistCouponInfo.setAppId("testAppId");
        when(strategistCouponInfoMapper.selectByExample(any())).thenReturn(Collections.singletonList(strategistCouponInfo));
        when(executeManagementService.getMtXCXUnionIdByMtUserId(any(), any())).thenReturn("testUnionId");
        try (MockedStatic<GsonUtils> gsonUtils = Mockito.mockStatic(GsonUtils.class)) {
            gsonUtils.when(() -> GsonUtils.fromJson(msg, ScrmCouponConsumeRecordMessage.class)).thenReturn(recordMessage);
            // act
            couponConsumeRecordService.handleCouponConsumeRecordMsg(msg);
            // assert
            gsonUtils.verify(() -> GsonUtils.fromJson(msg, ScrmCouponConsumeRecordMessage.class));
            Mockito.verify(strategistCouponInfoMapper).selectByExample(any());
        }
    }

    /**
     * Test handling of null message
     */
    @Test
    void testHandleCouponConsumeRecordMsgWithNullMessage() throws Throwable {
        // arrange
        String msg = null;
        try (MockedStatic<GsonUtils> gsonUtils = Mockito.mockStatic(GsonUtils.class)) {
            // act
            couponConsumeRecordService.handleCouponConsumeRecordMsg(msg);
            // assert
            gsonUtils.verifyNoInteractions();
        }
    }

    /**
     * Test handling of empty message
     */
    @Test
    void testHandleCouponConsumeRecordMsgWithEmptyMessage() throws Throwable {
        // arrange
        String msg = "";
        try (MockedStatic<GsonUtils> gsonUtils = Mockito.mockStatic(GsonUtils.class)) {
            // act
            couponConsumeRecordService.handleCouponConsumeRecordMsg(msg);
            // assert
            gsonUtils.verifyNoInteractions();
        }
    }

    /**
     * Test handling of invalid JSON message
     */
    @Test
    void testHandleCouponConsumeRecordMsgWithInvalidJsonMessage() throws Throwable {
        // arrange
        String msg = "invalid json";
        try (MockedStatic<GsonUtils> gsonUtils = Mockito.mockStatic(GsonUtils.class)) {
            gsonUtils.when(() -> GsonUtils.fromJson(msg, ScrmCouponConsumeRecordMessage.class)).thenReturn(null);
            // act
            couponConsumeRecordService.handleCouponConsumeRecordMsg(msg);
            // assert
            gsonUtils.verify(() -> GsonUtils.fromJson(msg, ScrmCouponConsumeRecordMessage.class));
        }
    }
}
