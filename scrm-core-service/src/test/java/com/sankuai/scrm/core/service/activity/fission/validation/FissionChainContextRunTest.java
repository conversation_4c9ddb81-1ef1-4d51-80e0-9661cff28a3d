package com.sankuai.scrm.core.service.activity.fission.validation;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import cn.hutool.core.map.MapUtil;
import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.context.ApplicationContext;

@ExtendWith(MockitoExtension.class)
class FissionChainContextRunTest {

    @Mock
    private ApplicationContext applicationContext;

    @InjectMocks
    private FissionChainContext<Object> fissionChainContext;

    private Map<String, List<AbstractFissionChainValidator>> getChainsGroupByMarkUsingReflection() throws NoSuchFieldException, IllegalAccessException {
        Field field = FissionChainContext.class.getDeclaredField("chainsGroupByMark");
        field.setAccessible(true);
        return (Map<String, List<AbstractFissionChainValidator>>) field.get(fissionChainContext);
    }

    @Test
    public void testRunWhenNoValidatorsFound() throws Throwable {
        when(applicationContext.getBeansOfType(AbstractFissionChainValidator.class)).thenReturn(Collections.emptyMap());
        fissionChainContext.run();
        assertTrue(getChainsGroupByMarkUsingReflection().isEmpty());
        verify(applicationContext).getBeansOfType(AbstractFissionChainValidator.class);
    }

    @Test
    public void testRunWithSingleValidator() throws Throwable {
        AbstractFissionChainValidator validator = mock(AbstractFissionChainValidator.class);
        when(validator.getMark()).thenReturn("testMark");
        Map<String, AbstractFissionChainValidator> beans = new HashMap<>();
        beans.put("validator1", validator);
        when(applicationContext.getBeansOfType(AbstractFissionChainValidator.class)).thenReturn(beans);
        fissionChainContext.run();
        Map<String, List<AbstractFissionChainValidator>> result = getChainsGroupByMarkUsingReflection();
        assertEquals(1, result.size());
        assertEquals(1, result.get("testMark").size());
        assertEquals(validator, result.get("testMark").get(0));
    }

    @Test
    public void testRunWithMultipleValidatorsSameMarkDifferentOrder() throws Throwable {
        AbstractFissionChainValidator validator1 = mock(AbstractFissionChainValidator.class);
        when(validator1.getMark()).thenReturn("testMark");
        when(validator1.getOrder()).thenReturn(2);
        AbstractFissionChainValidator validator2 = mock(AbstractFissionChainValidator.class);
        when(validator2.getMark()).thenReturn("testMark");
        when(validator2.getOrder()).thenReturn(1);
        Map<String, AbstractFissionChainValidator> beans = new HashMap<>();
        beans.put("validator1", validator1);
        beans.put("validator2", validator2);
        when(applicationContext.getBeansOfType(AbstractFissionChainValidator.class)).thenReturn(beans);
        fissionChainContext.run();
        Map<String, List<AbstractFissionChainValidator>> result = getChainsGroupByMarkUsingReflection();
        assertEquals(1, result.size());
        assertEquals(2, result.get("testMark").size());
        // order 1 comes first
        assertEquals(validator2, result.get("testMark").get(0));
        // order 2 comes second
        assertEquals(validator1, result.get("testMark").get(1));
    }

    @Test
    public void testRunWithMultipleValidatorsDifferentMarks() throws Throwable {
        AbstractFissionChainValidator validator1 = mock(AbstractFissionChainValidator.class);
        when(validator1.getMark()).thenReturn("mark1");
        AbstractFissionChainValidator validator2 = mock(AbstractFissionChainValidator.class);
        when(validator2.getMark()).thenReturn("mark2");
        Map<String, AbstractFissionChainValidator> beans = new HashMap<>();
        beans.put("validator1", validator1);
        beans.put("validator2", validator2);
        when(applicationContext.getBeansOfType(AbstractFissionChainValidator.class)).thenReturn(beans);
        fissionChainContext.run();
        Map<String, List<AbstractFissionChainValidator>> result = getChainsGroupByMarkUsingReflection();
        assertEquals(2, result.size());
        assertEquals(1, result.get("mark1").size());
        assertEquals(1, result.get("mark2").size());
        assertEquals(validator1, result.get("mark1").get(0));
        assertEquals(validator2, result.get("mark2").get(0));
    }

    @Test
    public void testRunWithMultipleValidatorsSameMarkSameOrder() throws Throwable {
        AbstractFissionChainValidator validator1 = mock(AbstractFissionChainValidator.class);
        when(validator1.getMark()).thenReturn("testMark");
        when(validator1.getOrder()).thenReturn(1);
        AbstractFissionChainValidator validator2 = mock(AbstractFissionChainValidator.class);
        when(validator2.getMark()).thenReturn("testMark");
        when(validator2.getOrder()).thenReturn(1);
        Map<String, AbstractFissionChainValidator> beans = new HashMap<>();
        beans.put("validator1", validator1);
        beans.put("validator2", validator2);
        when(applicationContext.getBeansOfType(AbstractFissionChainValidator.class)).thenReturn(beans);
        fissionChainContext.run();
        Map<String, List<AbstractFissionChainValidator>> result = getChainsGroupByMarkUsingReflection();
        assertEquals(1, result.size());
        assertEquals(2, result.get("testMark").size());
        assertTrue(result.get("testMark").contains(validator1));
        assertTrue(result.get("testMark").contains(validator2));
    }
}
