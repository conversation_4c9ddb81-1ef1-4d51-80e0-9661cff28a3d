package com.sankuai.scrm.core.service.automatedmanagement.dal.example;

import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ScrmAmCrowdPackAddCorpTagLogExampleTest {

    private ScrmAmCrowdPackAddCorpTagLogExample example;

    @Before
    public void setUp() {
        example = new ScrmAmCrowdPackAddCorpTagLogExample();
    }

    /**
     * 测试 createCriteriaInternal 方法是否能正确创建并返回一个新的 Criteria 对象
     */
    @Test
    public void testCreateCriteriaInternal() throws Throwable {
        // arrange
        ScrmAmCrowdPackAddCorpTagLogExample example = new ScrmAmCrowdPackAddCorpTagLogExample();
        // act
        ScrmAmCrowdPackAddCorpTagLogExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertNotNull(result);
    }

    /**
     * 测试 clear 方法
     */
    @Test
    public void testClear() {
        // arrange
        example.setOrderByClause("test");
        example.setDistinct(true);
        example.getOredCriteria().add(new ScrmAmCrowdPackAddCorpTagLogExample.Criteria());
        example.setOffset(1);
        example.setRows(10);
        // act
        example.clear();
        // assert
        assertTrue(example.getOredCriteria().isEmpty());
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertNull(example.getOffset());
        assertNull(example.getRows());
    }

    /**
     * 测试 page 方法，正常情况
     */
    @Test
    public void testPageNormal() throws Throwable {
        // arrange
        ScrmAmCrowdPackAddCorpTagLogExample example = new ScrmAmCrowdPackAddCorpTagLogExample();
        Integer page = 1;
        Integer pageSize = 10;
        // act
        ScrmAmCrowdPackAddCorpTagLogExample result = example.page(page, pageSize);
        // assert
        assertEquals((Integer) (page * pageSize), result.getOffset());
        assertEquals(pageSize, result.getRows());
    }

    /**
     * 测试 page 方法，边界情况
     */
    @Test
    public void testPageBoundary() throws Throwable {
        // arrange
        ScrmAmCrowdPackAddCorpTagLogExample example = new ScrmAmCrowdPackAddCorpTagLogExample();
        Integer page = 0;
        Integer pageSize = 10;
        // act
        ScrmAmCrowdPackAddCorpTagLogExample result = example.page(page, pageSize);
        // assert
        assertEquals((Integer) (page * pageSize), result.getOffset());
        assertEquals(pageSize, result.getRows());
    }

    /**
     * 测试 page 方法，异常情况
     * Note: Adjusted to reflect the actual behavior of the method under test.
     */
    @Test
    public void testPageException() throws Throwable {
        // arrange
        ScrmAmCrowdPackAddCorpTagLogExample example = new ScrmAmCrowdPackAddCorpTagLogExample();
        Integer page = -1;
        Integer pageSize = 10;
        // act
        ScrmAmCrowdPackAddCorpTagLogExample result = example.page(page, pageSize);
        // assert
        // Assuming the method does not validate and simply sets the offset and rows.
        assertEquals((Integer) (page * pageSize), result.getOffset());
        assertEquals(pageSize, result.getRows());
    }

    /**
     * 测试 page 方法，异常情况
     */
    @Test(expected = NullPointerException.class)
    public void testPageExceptionNull() throws Throwable {
        // arrange
        ScrmAmCrowdPackAddCorpTagLogExample example = new ScrmAmCrowdPackAddCorpTagLogExample();
        Integer page = null;
        Integer pageSize = 10;
        // act
        example.page(page, pageSize);
    }

    /**
     * 测试limit方法，输入正常的非负整数值
     */
    @Test
    public void testLimitNormal() throws Throwable {
        // arrange
        ScrmAmCrowdPackAddCorpTagLogExample example = new ScrmAmCrowdPackAddCorpTagLogExample();
        Integer rows = 10;
        // act
        ScrmAmCrowdPackAddCorpTagLogExample result = example.limit(rows);
        // assert
        assertEquals(rows, result.getRows());
    }

    /**
     * 测试limit方法，输入null
     */
    @Test
    public void testLimitNull() throws Throwable {
        // arrange
        ScrmAmCrowdPackAddCorpTagLogExample example = new ScrmAmCrowdPackAddCorpTagLogExample();
        Integer rows = null;
        // act
        ScrmAmCrowdPackAddCorpTagLogExample result = example.limit(rows);
        // assert
        assertNull(result.getRows());
    }
}
