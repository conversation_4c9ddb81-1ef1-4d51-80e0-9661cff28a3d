package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationWxTouchTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.OfficialWxHandler;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

import java.util.ArrayList;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CrowdFriendTouchActionTest {

    @InjectMocks
    private CrowdFriendTouchAction crowdFriendTouchAction;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock(lenient = true)
    private ExecuteManagementService executeManagementService;

    @Mock(lenient = true)
    private WeChatTokenAcl weChatTokenAcl;

    @Mock(lenient = true)
    private CorpAppConfigRepository appConfigRepository;

    @Mock(lenient = true)
    private ProductManagementService productManagementService;

    @Mock(lenient = true)
    private OfficialWxHandler officialWxHandler;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ExecuteManagementDTO executeManagementDTO;

    private ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO;

    private ScrmProcessOrchestrationNodeDTO currentProcessingNode;

    private ScrmAmProcessOrchestrationExecuteLogDO existedExecuteLogDO;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Before
    public void initialize() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        // Set the id to avoid NullPointerException
        processOrchestrationDTO.setId(1L);
        // Set the appId to avoid NullPointerException
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        // Ensure nodeId is set to avoid NullPointerException
        nodeDTO.setNodeId(1L);
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(Collections.singletonList(nodeDTO));
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        executeManagementDTO = new ExecuteManagementDTO(processOrchestrationDTO, 1, "String", Collections.singletonList("String"), 1);
        scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        existedExecuteLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
    }

    private ScrmProcessOrchestrationDTO createProcessOrchestrationDTOWithNodeMediumAndNode() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setId(1L);
        nodeMediumDTO.addProcessOrchestrationNodeDTO(nodeDTO);
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        return processOrchestrationDTO;
    }

    @Test
    public void testCheckStatusInvokeLogDOSIsEmpty() throws Throwable {
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(Collections.emptyList());
        when(executeManagementService.tryStartStatusCheck(anyLong())).thenReturn(true);
        crowdFriendTouchAction.checkStatus(processOrchestrationDTO);
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        verifyNoMoreInteractions(wxInvokeLogDOMapper);
    }

    @Test
    public void testCheckStatusTokenResultIsNull() throws Throwable {
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(Collections.singletonList(new ScrmAmProcessOrchestrationWxInvokeLogDO()));
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(null);
        when(executeManagementService.tryStartStatusCheck(anyLong())).thenReturn(true);
        crowdFriendTouchAction.checkStatus(processOrchestrationDTO);
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        verify(appConfigRepository, times(1)).getCorpIdByAppId(anyString());
        verify(weChatTokenAcl, times(1)).getTokenByCorpId(anyString());
        verifyNoMoreInteractions(wxInvokeLogDOMapper, appConfigRepository, weChatTokenAcl);
    }

    @Test
    public void testCheckStatusTokenResultIsNotSuccess() throws Throwable {
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(Collections.singletonList(new ScrmAmProcessOrchestrationWxInvokeLogDO()));
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(null);
        when(executeManagementService.tryStartStatusCheck(anyLong())).thenReturn(true);
        crowdFriendTouchAction.checkStatus(processOrchestrationDTO);
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        verify(appConfigRepository, times(1)).getCorpIdByAppId(anyString());
        verify(weChatTokenAcl, times(1)).getTokenByCorpId(anyString());
        verifyNoMoreInteractions(wxInvokeLogDOMapper, appConfigRepository, weChatTokenAcl);
    }

    @Test
    public void testCheckStatusInvokeLogDOTypeIsNotOfficial() throws Throwable {
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(Collections.singletonList(new ScrmAmProcessOrchestrationWxInvokeLogDO()));
        when(appConfigRepository.getCorpIdByAppId(anyString())).thenReturn("corpId");
        when(weChatTokenAcl.getTokenByCorpId(anyString())).thenReturn(null);
        when(executeManagementService.tryStartStatusCheck(anyLong())).thenReturn(true);
        crowdFriendTouchAction.checkStatus(processOrchestrationDTO);
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        verify(appConfigRepository, times(1)).getCorpIdByAppId(anyString());
        verify(weChatTokenAcl, times(1)).getTokenByCorpId(anyString());
        verifyNoMoreInteractions(wxInvokeLogDOMapper, appConfigRepository, weChatTokenAcl);
    }

    @Test(expected = Exception.class)
    public void testExecuteAfterNodeDealLogicWithException() throws Throwable {
        List<String> executorIds = Arrays.asList("executor1", "executor2");
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createProcessOrchestrationDTOWithNodeMediumAndNode();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO(processOrchestrationDTO, 0, "", executorIds, 0);
        when(executeManagementService.getExecuteMediumManagementDTO(any())).thenReturn(executeManagementDTO);
        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenThrow(new RuntimeException());
        crowdFriendTouchAction.executeAfterNodeDealLogic(processOrchestrationDTO, executorIds, executeManagementDTO, null);
    }

    /**
     * 测试checkStatusV2方法，当invokeLogDOS为空时，不执行任何操作
     */
    @Test
    public void testCheckStatusV2WithEmptyInvokeLogDOS() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(new ArrayList<>());
        crowdFriendTouchAction.checkStatusV2(processOrchestrationDTO);
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        verifyNoMoreInteractions(officialWxHandler);
    }

    /**
     * 测试checkStatusV2方法，当invokeLogDOS不为空，且类型为MESSAGE时，执行officialWxHandler.checkOfficialWxStatusV2
     */
    @Test
    public void testCheckStatusV2WithMessageType() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> invokeLogDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeLogDO logDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        logDO.setType(ScrmProcessOrchestrationWxTouchTypeEnum.MESSAGE.getCode());
        invokeLogDOS.add(logDO);
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(invokeLogDOS);
        crowdFriendTouchAction.checkStatusV2(processOrchestrationDTO);
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        verify(officialWxHandler, times(1)).checkOfficialWxStatusV2(eq(processOrchestrationDTO), anyLong(), anyString(), any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
    }

    /**
     * 测试checkStatusV2方法，当invokeLogDOS不为空，且类型为GROUP_MESSAGE时，执行officialWxHandler.checkOfficialWxStatusV2
     */
    @Test
    public void testCheckStatusV2WithGroupMessageType() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        List<ScrmAmProcessOrchestrationWxInvokeLogDO> invokeLogDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeLogDO logDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        logDO.setType(ScrmProcessOrchestrationWxTouchTypeEnum.GROUP_MESSAGE.getCode());
        invokeLogDOS.add(logDO);
        when(wxInvokeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class))).thenReturn(invokeLogDOS);
        crowdFriendTouchAction.checkStatusV2(processOrchestrationDTO);
        verify(wxInvokeLogDOMapper, times(1)).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeLogDOExample.class));
        verify(officialWxHandler, times(1)).checkOfficialWxStatusV2(eq(processOrchestrationDTO), anyLong(), anyString(), any(ScrmAmProcessOrchestrationWxInvokeLogDO.class));
    }

}
