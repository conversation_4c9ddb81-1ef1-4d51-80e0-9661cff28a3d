package com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool;

import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductPoolUpdateProductsDTO;
import com.sankuai.medicalcosmetology.product.selectify.api.service.CommonSelectPoolItemService;
import com.sankuai.medicalcosmetology.product.selectify.api.service.CommonSelectPoolService;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductItemsDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductItemsDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductTagsDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationProductTagMapDOMapper;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;

import java.util.Collections;

import static org.mockito.Mockito.*;

public class ProductManagementService_UpdateProductTest {

    @Spy
    @InjectMocks
    private ProductManagementService productManagementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductItemsDOMapper productItemsDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductTagsDOMapper productTagsDOMapper;

    @Mock(lenient = true)
    private ExtScrmAmProcessOrchestrationProductTagMapDOMapper extScrmAmProcessOrchestrationProductTagMapDOMapper;

    @Mock(lenient = true)
    private CommonSelectPoolService commonSelectPoolService;

    @Mock(lenient = true)
    private CommonSelectPoolItemService commonSelectPoolItemService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testUpdateProductWithNoTags() throws Throwable {
        String appId = "testApp";
        ProductPoolUpdateProductsDTO product = new ProductPoolUpdateProductsDTO();
        product.setProductId(1L);
        product.setType(1);
        product.setTags("");
        ScrmAmProcessOrchestrationProductItemsDO itemsDO = new ScrmAmProcessOrchestrationProductItemsDO();
        itemsDO.setId(1L);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(itemsDO));
        productManagementService.updateProduct(appId, product);
        verify(productItemsDOMapper).selectByExample(any());
        verify(productTagsDOMapper, never()).selectByExample(any());
        verify(productTagsDOMapper, never()).batchInsert(anyList());
        verify(extScrmAmProcessOrchestrationProductTagMapDOMapper, never()).deleteByExample(any());
        verify(extScrmAmProcessOrchestrationProductTagMapDOMapper, never()).batchInsert(anyList());
    }

    @Test
    public void testUpdateProductWithNonExistentProduct() throws Throwable {
        String appId = "testApp";
        ProductPoolUpdateProductsDTO product = new ProductPoolUpdateProductsDTO();
        product.setProductId(1L);
        product.setType(1);
        product.setTags("tag1,tag2");
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        productManagementService.updateProduct(appId, product);
        verify(productItemsDOMapper).selectByExample(any());
        verify(productTagsDOMapper, never()).selectByExample(any());
        verify(productTagsDOMapper, never()).batchInsert(anyList());
        verify(extScrmAmProcessOrchestrationProductTagMapDOMapper, never()).deleteByExample(any());
        verify(extScrmAmProcessOrchestrationProductTagMapDOMapper, never()).batchInsert(anyList());
    }

    @Test
    public void testUpdateProductWithNullProduct() throws Throwable {
        String appId = "testApp";
        ProductPoolUpdateProductsDTO product = null;
        productManagementService.updateProduct(appId, product);
        verifyNoInteractions(productItemsDOMapper, productTagsDOMapper, extScrmAmProcessOrchestrationProductTagMapDOMapper);
    }

    @Test
    public void testUpdateProductWithNullProductType() throws Throwable {
        String appId = "testApp";
        ProductPoolUpdateProductsDTO product = new ProductPoolUpdateProductsDTO();
        product.setProductId(1L);
        product.setType(null);
        product.setTags("tag1,tag2");
        productManagementService.updateProduct(appId, product);
        verifyNoInteractions(productItemsDOMapper, productTagsDOMapper, extScrmAmProcessOrchestrationProductTagMapDOMapper);
    }

    @Test
    public void testUpdateProductWithEmptyTags() throws Throwable {
        String appId = "testApp";
        ProductPoolUpdateProductsDTO product = new ProductPoolUpdateProductsDTO();
        product.setProductId(1L);
        product.setType(1);
        product.setTags("");
        ScrmAmProcessOrchestrationProductItemsDO itemsDO = new ScrmAmProcessOrchestrationProductItemsDO();
        itemsDO.setId(1L);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(itemsDO));
        productManagementService.updateProduct(appId, product);
        verify(productItemsDOMapper).selectByExample(any());
        verifyNoMoreInteractions(productTagsDOMapper, extScrmAmProcessOrchestrationProductTagMapDOMapper, commonSelectPoolService, commonSelectPoolItemService);
    }

    @Test(expected = Exception.class)
    public void testUpdateProductWithDatabaseException() throws Throwable {
        String appId = "testApp";
        ProductPoolUpdateProductsDTO product = new ProductPoolUpdateProductsDTO();
        product.setProductId(1L);
        product.setType(1);
        product.setTags("tag1,tag2");
        ScrmAmProcessOrchestrationProductItemsDO itemsDO = new ScrmAmProcessOrchestrationProductItemsDO();
        itemsDO.setId(1L);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(itemsDO));
        when(productTagsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(productTagsDOMapper.batchInsert(anyList())).thenReturn(0);
        productManagementService.updateProduct(appId, product);
    }
    @Test
    public void testUpdateProductSuccess() throws Throwable {
        String appId = "testApp";
        ProductPoolUpdateProductsDTO product = new ProductPoolUpdateProductsDTO();
        product.setProductId(1L);
        product.setType(1);
        product.setTags("tag1,tag2");
        ScrmAmProcessOrchestrationProductItemsDO itemsDO = new ScrmAmProcessOrchestrationProductItemsDO();
        itemsDO.setId(1L);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(itemsDO));
        when(productTagsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(productTagsDOMapper.batchInsert(anyList())).thenReturn(2);
        productManagementService.updateProduct(appId, product);
        verify(productManagementService, times(1)).updateProduct(anyString(), any());
    }
}
