package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import java.util.Properties;
import org.mockito.MockedStatic;

@ExtendWith(MockitoExtension.class)
class GroupRetailAiEngineFlowLimitConsumerTest {

    @Mock
    private IConsumerProcessor mockConsumer;

    @InjectMocks
    private GroupRetailAiEngineFlowLimitConsumer consumer;

    /**
     * Tests destroy() when consumer is null - should do nothing
     */
    @Test
    public void testDestroyWhenConsumerIsNull() throws Throwable {
        // arrange
        // create new instance without mock injection
        consumer = new GroupRetailAiEngineFlowLimitConsumer();
        // act
        consumer.destroy();
        // assert
        verifyNoInteractions(mockConsumer);
    }

    /**
     * Tests destroy() when consumer is not null - should call close()
     */
    @Test
    public void testDestroyWhenConsumerIsNotNull() throws Throwable {
        // arrange - mock is already injected via @InjectMocks
        // act
        consumer.destroy();
        // assert
        verify(mockConsumer).close();
        verifyNoMoreInteractions(mockConsumer);
    }

    /**
     * Tests destroy() when consumer close() throws exception - should propagate exception
     */
    @Test
    public void testDestroyWhenConsumerCloseThrowsException() throws Throwable {
        // arrange
        Exception expectedException = new Exception("Close failed");
        doThrow(expectedException).when(mockConsumer).close();
        // act & assert
        Exception thrown = assertThrows(Exception.class, () -> {
            consumer.destroy();
        });
        // assert
        verify(mockConsumer).close();
        verifyNoMoreInteractions(mockConsumer);
    }

    @Test
    public void testAfterPropertiesSetSuccess() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockProcessor = mock(IConsumerProcessor.class);
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenReturn(mockProcessor);
            consumer.afterPropertiesSet();
            mockedMafkaClient.verify(() -> {
                Properties props = new Properties();
                props.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
                props.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
                props.setProperty(ConsumerConstants.SubscribeGroup, "scrm.group.retail.ai.engine.flow.limit.consumer");
                MafkaClient.buildConsumerFactory(props, "scrm.group.retail.ai.engine.flow.limit");
            });
            verify(mockProcessor).recvMessageWithParallel(eq(String.class), any());
        }
    }

    @Test
    public void testAfterPropertiesSetFactoryCreationFailure() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenThrow(new RuntimeException("Factory creation failed"));
            Exception exception = assertThrows(RuntimeException.class, () -> {
                consumer.afterPropertiesSet();
            });
            assertEquals("Factory creation failed", exception.getMessage());
        }
    }

    @Test
    public void testAfterPropertiesSetReceiverSetupFailure() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockProcessor = mock(IConsumerProcessor.class);
            doThrow(new RuntimeException("Receiver setup failed")).when(mockProcessor).recvMessageWithParallel(any(), any());
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenReturn(mockProcessor);
            Exception exception = assertThrows(RuntimeException.class, () -> {
                consumer.afterPropertiesSet();
            });
            assertEquals("Receiver setup failed", exception.getMessage());
        }
    }

    @Test
    public void testAfterPropertiesSetPropertiesCorrectness() throws Throwable {
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            IConsumerProcessor mockProcessor = mock(IConsumerProcessor.class);
            mockedMafkaClient.when(() -> MafkaClient.buildConsumerFactory(any(Properties.class), anyString())).thenAnswer(invocation -> {
                Properties props = invocation.getArgument(0);
                assertEquals("daozong", props.getProperty(ConsumerConstants.MafkaBGNamespace));
                assertEquals("com.sankuai.medicalcosmetology.scrm.core", props.getProperty(ConsumerConstants.MafkaClientAppkey));
                assertEquals("scrm.group.retail.ai.engine.flow.limit.consumer", props.getProperty(ConsumerConstants.SubscribeGroup));
                return mockProcessor;
            });
            consumer.afterPropertiesSet();
            verify(mockProcessor).recvMessageWithParallel(eq(String.class), any());
        }
    }
}
