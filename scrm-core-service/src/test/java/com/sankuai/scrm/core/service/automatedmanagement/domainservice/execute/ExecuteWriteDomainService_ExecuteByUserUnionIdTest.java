package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutePlanDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecutePlanDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationExecutePlanDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import com.sankuai.scrm.core.service.user.dal.mapper.ext.ExtScrmUserTagMapper;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ExecuteWriteDomainService_ExecuteByUserUnionIdTest {

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock(lenient = true)
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock(lenient = true)
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock(lenient = true)
    private ExtScrmUserTagMapper userTagDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock
    private ExtScrmAmProcessOrchestrationExecutePlanDOMapper executePlanDOMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    private CompletableFuture<ScrmProcessOrchestrationDTO> mockFuture() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        return CompletableFuture.completedFuture(processOrchestrationDTO);
    }

    @Test
    public void testExecuteByUserUnionIdWithEmptyUserUnionId() throws Throwable {
        String userUnionId = "";
        boolean result = executeWriteDomainService.executeByUserUnionId(userUnionId, "yimei");
        assertTrue(result);
        verify(executeLogDOMapper, never()).selectByExample(any());
    }

    @Test
    public void testExecuteByUserUnionIdWithNoExecutionLogs() throws Throwable {
        String userUnionId = "testUser";
        when(executeLogDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        boolean result = executeWriteDomainService.executeByUserUnionId(userUnionId, "yimei");
        assertTrue(result);
        verify(executeLogDOMapper).selectByExample(any());
        verify(crowdPackReadDomainService, never()).queryUserByUnionId(anyString(), anyString());
    }

    @Test
    public void testExecuteByUserUnionIdWithNoCrowdPackDetailInfo() throws Throwable {
        String userUnionId = "testUser";
        List<ScrmAmProcessOrchestrationExecuteLogDO> logs = new ArrayList<>();
        logs.add(new ScrmAmProcessOrchestrationExecuteLogDO());
        when(executeLogDOMapper.selectByExample(any())).thenReturn(logs);
        when(crowdPackReadDomainService.queryUserByUnionId(userUnionId, "yimei")).thenReturn(null);
        boolean result = executeWriteDomainService.executeByUserUnionId(userUnionId, "yimei");
        assertTrue(result);
        verify(executeLogDOMapper).selectByExample(any());
        verify(crowdPackReadDomainService).queryUserByUnionId(userUnionId, "yimei");
    }

    /**
     * Test updating existing execution plans to SKIPPED status
     */
    @Test
    public void testCheckAndCreateProcessOrchestrationTask_UpdateExistingPlans() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationInfoDO> infoDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setValidVersion("1.0");
        // Today
        infoDO.setCronComment(DateUtil.formatYMdHms(new Date()));
        // VALID status
        infoDO.setStatus((byte) 1);
        infoDO.setProcessOrchestrationType((byte) 4);
        // yesterday
        infoDO.setBeginTime(new Date(System.currentTimeMillis() - 86400000));
        // tomorrow
        infoDO.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        infoDOS.add(infoDO);
        List<ScrmAmProcessOrchestrationExecutePlanDO> existingPlans = new ArrayList<>();
        ScrmAmProcessOrchestrationExecutePlanDO planDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        planDO.setId(1L);
        // PREPARING status
        planDO.setStatus((byte) 1);
        existingPlans.add(planDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any())).thenReturn(infoDOS);
        when(executePlanDOMapper.selectByExample(any())).thenReturn(existingPlans);
        when(executePlanDOMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        // act
        executeWriteDomainService.checkAndCreateProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, times(1)).updateByPrimaryKeySelective(any());
    }

    /**
     * Test creating new execution plan with basic fields
     */
    @Test
    public void testCheckAndCreateProcessOrchestrationTask_CreateNewPlan() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationInfoDO> infoDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setValidVersion("1.0");
        // TIMED_PROCESS_ORCHESTRATION
        infoDO.setProcessOrchestrationType((byte) 4);
        // Set cronComment to today's date
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.HOUR_OF_DAY, cal.get(Calendar.HOUR_OF_DAY));
        cal.set(Calendar.MINUTE, cal.get(Calendar.MINUTE) + 1);
        infoDO.setCronComment(DateUtil.formatYMdHms(cal.getTime()));
        // VALID status
        infoDO.setStatus((byte) 1);
        // yesterday
        infoDO.setBeginTime(new Date(System.currentTimeMillis() - 86400000));
        // tomorrow
        infoDO.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        infoDOS.add(infoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any())).thenReturn(infoDOS);
        when(executePlanDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        // act
        executeWriteDomainService.checkAndCreateProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, times(1)).batchInsert(any());
    }

    /**
     * Test handling different process orchestration types
     */
    @Test
    public void testCheckAndCreateProcessOrchestrationTask_DifferentTypes() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationInfoDO> infoDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setValidVersion("1.0");
        // PERIODIC_TIMED_TASK
        infoDO.setProcessOrchestrationType((byte) 5);
        // Set up cron to execute in the next minute
        Calendar cal = Calendar.getInstance();
        int minute = cal.get(Calendar.MINUTE);
        int hour = cal.get(Calendar.HOUR_OF_DAY);
        String cronExpression = String.format("0 %d %d * * ?", (minute + 1) % 60, hour);
        infoDO.setCron(cronExpression);
        // Set cronComment to today
        infoDO.setCronComment(DateUtil.formatYMdHms(cal.getTime()));
        // VALID status
        infoDO.setStatus((byte) 1);
        // yesterday
        infoDO.setBeginTime(new Date(System.currentTimeMillis() - 86400000));
        // tomorrow
        infoDO.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        infoDOS.add(infoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any())).thenReturn(infoDOS);
        when(executePlanDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(executePlanDOMapper.batchInsert(any())).thenReturn(1);
        // act
        executeWriteDomainService.checkAndCreateProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, times(1)).batchInsert(any());
    }

    /**
     * Test date validation logic - task should not be created for past dates
     */
    @Test
    public void testCheckAndCreateProcessOrchestrationTask_DateValidation() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationInfoDO> infoDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setId(1L);
        infoDO.setValidVersion("1.0");
        infoDO.setProcessOrchestrationType((byte) 4);
        // Past date
        infoDO.setCronComment("2000-01-01 10:00:00");
        // VALID status
        infoDO.setStatus((byte) 1);
        // yesterday
        infoDO.setBeginTime(new Date(System.currentTimeMillis() - 86400000));
        // tomorrow
        infoDO.setEndTime(new Date(System.currentTimeMillis() + 86400000));
        infoDOS.add(infoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any())).thenReturn(infoDOS);
        when(executePlanDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        // act
        executeWriteDomainService.checkAndCreateProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, never()).batchInsert(any());
    }

    /**
     * Test batch insert of valid execution plans
     */
    @Test
    public void testCheckAndCreateProcessOrchestrationTask_BatchInsert() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationInfoDO> infoDOS = new ArrayList<>();
        Calendar cal = Calendar.getInstance();
        cal.set(Calendar.MINUTE, cal.get(Calendar.MINUTE) + 1);
        for (int i = 0; i < 3; i++) {
            ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
            infoDO.setId((long) i);
            infoDO.setValidVersion("1.0");
            infoDO.setProcessOrchestrationType((byte) 4);
            infoDO.setCronComment(DateUtil.formatYMdHms(cal.getTime()));
            // VALID status
            infoDO.setStatus((byte) 1);
            // yesterday
            infoDO.setBeginTime(new Date(System.currentTimeMillis() - 86400000));
            // tomorrow
            infoDO.setEndTime(new Date(System.currentTimeMillis() + 86400000));
            infoDOS.add(infoDO);
        }
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any())).thenReturn(infoDOS);
        when(executePlanDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        // act
        executeWriteDomainService.checkAndCreateProcessOrchestrationTask();
        // assert
        verify(executePlanDOMapper, times(1)).batchInsert(any());
    }
}
