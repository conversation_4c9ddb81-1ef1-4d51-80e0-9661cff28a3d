package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.jupiter.api.Assertions.assertThrows;
import static org.junit.jupiter.api.Assertions.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationInfoDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.RefinementOperationExecuteMessageProducer;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.meituan.mdp.boot.starter.mdpcache.core.Cache;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration.ScrmProcessOrchestrationExecutePlanConverter;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackAndProcessMapDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutePlanDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackAndProcessMapDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecutePlanDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackAndProcessMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationExecutePlanDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackUpdateLockService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import java.util.*;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeUnit;
import java.util.concurrent.TimeoutException;
import java.util.concurrent.atomic.AtomicInteger;
import org.mockito.ArgumentCaptor;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackDetailInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.DdlResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.utils.ConditionUtils;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.infrastructure.dal.babymapper.ContactUserDoMapper;
import com.sankuai.scrm.core.service.infrastructure.dal.entity.ContactUserDo;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserTag;
import com.sankuai.scrm.core.service.user.dal.example.ScrmUserTagExample;
import com.sankuai.scrm.core.service.user.dal.mapper.ext.ExtScrmUserTagMapper;
import com.sankuai.scrm.core.service.util.ThreadPoolUtils;
import java.lang.reflect.Field;
import java.util.concurrent.*;
import org.mockito.*;
import org.springframework.test.util.ReflectionTestUtils;
import static org.mockito.ArgumentMatchers.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmCrowdPackTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecutePlanPackStatusEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackBaseInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackBaseInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationInfoDOMapper;
import java.lang.reflect.Method;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.google.common.collect.Lists;
import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmSupportedFilterFieldOperatorTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackUpdateStrategyDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackUpdateStrategyDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackUpdateStrategyDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.config.ConfigDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.AmLionConfigDTO;
import com.sankuai.scrm.core.service.tag.dal.entity.ext.ExtExternalContactTag;
import com.sankuai.scrm.core.service.tag.dal.example.ExternalContactTagExample;
import com.sankuai.scrm.core.service.tag.dal.mapper.ext.ExtExternalContactTagMapper;
import com.sankuai.scrm.core.service.user.dal.entity.ext.ExtScrmUserTag;
import com.sankuai.scrm.core.service.user.enums.ScrmUserTagEnum;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.mockito.MockedStatic;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.group.domain.GroupMemberDomainService;
import com.sankuai.scrm.core.service.util.IdConvertUtils;
import org.apache.commons.lang.StringUtils;
import org.mockito.Spy;

@ExtendWith(MockitoExtension.class)
class ExecuteWriteDomainServiceUpdateExecuteLogSenderByIdsTest {

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock
    private RefinementOperationExecuteMessageProducer refinementOperationExecuteMessageProducer;

    @Mock
    private ExtScrmAmProcessOrchestrationExecutePlanDOMapper executePlanDOMapper;

    @Mock
    private ScrmAmCrowdPackAndProcessMapDOMapper crowdPackAndProcessMapDOMapper;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock
    private CrowdPackUpdateLockService crowdPackUpdateLockService;

    @Mock
    private ScrmProcessOrchestrationExecutePlanConverter scrmProcessOrchestrationExecutePlanConverter;

    @Mock
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock
    private ExtScrmUserTagMapper userTagDOMapper;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private ContactUserDoMapper contactUserDoMapper;

    @Mock
    private ConditionUtils conditionUtils;

    private final Long testCrowdPackId = 123L;

    private final String testAppId = "testApp";

    private final String testUnionId = "union123";

    private final Long testUserId = 456L;

    private final String testCorpId = "testCorpId";

    @Mock
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock
    private ScrmAmCrowdPackBaseInfoDOMapper scrmAmCrowdPackBaseInfoDOMapper;

    @Mock
    private ConfigDomainService configDomainService;

    @Mock
    private ScrmAmCrowdPackUpdateStrategyDOMapper updateStrategyDOMapper;

    @Mock
    private ExtExternalContactTagMapper externalContactTagMapper;

    private final Long crowdPackId = 123L;

    private final String appId = "test_app_id";

    private final String validPackVersion = "1.0";

    @Mock
    private IdConvertUtils idConvertUtils;

    @Mock
    private ContactUserDomain contactUserDomain;

    @Mock
    private GroupMemberDomainService groupMemberDomainService;

    private static final String TEST_APP_ID = "test_app_id";

    private static final String TEST_CORP_ID = "test_corp_id";

    private static final String TEST_UNION_ID = "test_union_id";

    private static final String TEST_PACK_VERSION = "test_pack_version";

    private static final Long TEST_USER_ID = 123456L;

    private static final Long TEST_PACK_ID = 789L;

    private static final String TEST_EXTERNAL_USER_ID = "test_external_user_id";

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test updating execute log sender with valid inputs
     */
    @Test
    void testUpdateExecuteLogSenderByIds_ValidInputs() throws Throwable {
        // arrange
        String sender = "newSender";
        ArrayList executeLogIds = new ArrayList();
        executeLogIds.add(1L);
        executeLogIds.add(2L);
        executeLogIds.add(3L);
        // act
        executeWriteDomainService.updateExecuteLogSenderByIds(sender, executeLogIds);
        // assert
        verify(executeLogDOMapper, times(executeLogIds.size())).updateByPrimaryKeySelective(any());
    }

    /**
     * Test updating execute log sender with empty list of IDs
     */
    @Test
    void testUpdateExecuteLogSenderByIds_EmptyList() throws Throwable {
        // arrange
        String sender = "newSender";
        ArrayList executeLogIds = new ArrayList();
        // act
        executeWriteDomainService.updateExecuteLogSenderByIds(sender, executeLogIds);
        // assert
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * Test updating execute log sender with null list of IDs
     */
    @Test
    void testUpdateExecuteLogSenderByIds_NullList() throws Throwable {
        // arrange
        String sender = "newSender";
        ArrayList executeLogIds = null;
        // act
        executeWriteDomainService.updateExecuteLogSenderByIds(sender, executeLogIds);
        // assert
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * Test updating execute log sender with blank sender
     */
    @Test
    void testUpdateExecuteLogSenderByIds_BlankSender() throws Throwable {
        // arrange
        String sender = "  ";
        ArrayList executeLogIds = new ArrayList();
        executeLogIds.add(1L);
        executeLogIds.add(2L);
        executeLogIds.add(3L);
        // act
        executeWriteDomainService.updateExecuteLogSenderByIds(sender, executeLogIds);
        // assert
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * Test updating execute log sender with null sender
     */
    @Test
    void testUpdateExecuteLogSenderByIds_NullSender() throws Throwable {
        // arrange
        String sender = null;
        ArrayList executeLogIds = new ArrayList();
        executeLogIds.add(1L);
        executeLogIds.add(2L);
        executeLogIds.add(3L);
        // act
        executeWriteDomainService.updateExecuteLogSenderByIds(sender, executeLogIds);
        // assert
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * Test case when both parameters are null
     */
    @Test
    public void testUpdateExecuteLogStatusByIds_NullParameters() throws Throwable {
        // arrange - no setup needed for null parameters
        // act
        executeWriteDomainService.updateExecuteLogStatusByIds(null, null);
        // assert
        verifyNoInteractions(executeLogDOMapper);
    }

    /**
     * Test case when status is null but IDs list is not empty
     */
    @Test
    public void testUpdateExecuteLogStatusByIds_NullStatusWithNonEmptyIds() throws Throwable {
        // arrange
        List<Long> ids = Arrays.asList(1L, 2L, 3L);
        // act
        executeWriteDomainService.updateExecuteLogStatusByIds(null, ids);
        // assert
        verifyNoInteractions(executeLogDOMapper);
    }

    /**
     * Test case when status is valid but IDs list is empty
     */
    @Test
    public void testUpdateExecuteLogStatusByIds_ValidStatusWithEmptyIds() throws Throwable {
        // arrange
        ScrmProcessOrchestrationExecuteStatusTypeEnum status = ScrmProcessOrchestrationExecuteStatusTypeEnum.SUCCESS;
        // act
        executeWriteDomainService.updateExecuteLogStatusByIds(status, Collections.emptyList());
        // assert
        verifyNoInteractions(executeLogDOMapper);
    }

    /**
     * Test case when updating single log ID
     */
    @Test
    public void testUpdateExecuteLogStatusByIds_SingleLogId() throws Throwable {
        // arrange
        ScrmProcessOrchestrationExecuteStatusTypeEnum status = ScrmProcessOrchestrationExecuteStatusTypeEnum.SUCCESS;
        Long logId = 1L;
        when(executeLogDOMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        // act
        executeWriteDomainService.updateExecuteLogStatusByIds(status, Collections.singletonList(logId));
        // assert
        verify(executeLogDOMapper).updateByPrimaryKeySelective(argThat(arg -> {
            ScrmAmProcessOrchestrationExecuteLogDO logDO = (ScrmAmProcessOrchestrationExecuteLogDO) arg;
            return logDO.getId().equals(logId) && logDO.getStatus() == status.getValue().byteValue() && logDO.getUpdateTime() != null;
        }));
    }

    /**
     * Test case when updating multiple log IDs
     */
    @Test
    public void testUpdateExecuteLogStatusByIds_MultipleLogIds() throws Throwable {
        // arrange
        ScrmProcessOrchestrationExecuteStatusTypeEnum status = ScrmProcessOrchestrationExecuteStatusTypeEnum.FAILED;
        List<Long> logIds = Arrays.asList(1L, 2L, 3L);
        when(executeLogDOMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        // act
        executeWriteDomainService.updateExecuteLogStatusByIds(status, logIds);
        // assert
        verify(executeLogDOMapper, times(logIds.size())).updateByPrimaryKeySelective(any());
        // Verify the content of each call
        verify(executeLogDOMapper, times(logIds.size())).updateByPrimaryKeySelective(argThat(arg -> {
            ScrmAmProcessOrchestrationExecuteLogDO logDO = (ScrmAmProcessOrchestrationExecuteLogDO) arg;
            return logIds.contains(logDO.getId()) && logDO.getStatus() == status.getValue().byteValue() && logDO.getUpdateTime() != null;
        }));
    }

    /**
     * Test case when database update fails (verifies no exception is thrown)
     */
    @Test
    public void testUpdateExecuteLogStatusByIds_DatabaseUpdateFails() throws Throwable {
        // arrange
        ScrmProcessOrchestrationExecuteStatusTypeEnum status = ScrmProcessOrchestrationExecuteStatusTypeEnum.SUCCESS;
        Long logId = 1L;
        when(executeLogDOMapper.updateByPrimaryKeySelective(any())).thenReturn(0);
        // act
        executeWriteDomainService.updateExecuteLogStatusByIds(status, Collections.singletonList(logId));
        // assert
        verify(executeLogDOMapper).updateByPrimaryKeySelective(argThat(arg -> {
            ScrmAmProcessOrchestrationExecuteLogDO logDO = (ScrmAmProcessOrchestrationExecuteLogDO) arg;
            return logDO.getId().equals(logId) && logDO.getStatus() == status.getValue().byteValue() && logDO.getUpdateTime() != null;
        }));
    }

    /**
     * Test single batch of logs (<=100) with unique process orchestration IDs
     */
    @Test
    public void testExecuteUnFinishedExternalCallExecuteLogSingleBatch() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationExecuteLogDO> logs = new ArrayList<>();
        for (int i = 1; i <= 50; i++) {
            ScrmAmProcessOrchestrationExecuteLogDO log = new ScrmAmProcessOrchestrationExecuteLogDO();
            log.setId((long) i);
            // Each log has unique processOrchestrationId
            log.setProcessOrchestrationId(100L + i);
            log.setProcessOrchestrationVersion("v1");
            log.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
            log.setAppId("app1");
            logs.add(log);
        }
        when(executeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecuteLogDOExample.class))).thenReturn(logs);
        // act
        boolean result = executeWriteDomainService.executeUnFinishedExternalCallExecuteLog();
        // assert
        assertTrue(result);
        verify(executeLogDOMapper).selectByExample(any(ScrmAmProcessOrchestrationExecuteLogDOExample.class));
        // Verify called once for each unique processOrchestrationId (50 times)
        verify(refinementOperationExecuteMessageProducer, times(50)).sendExecutionTaskExecuteMessage(eq(new ArrayList<>()), eq("app1"), anyLong(), eq("v1"));
    }

    /**
     * Test single batch with duplicate process orchestration IDs (should only call once per unique ID)
     */
    @Test
    public void testExecuteUnFinishedExternalCallExecuteLogSingleBatchWithDuplicates() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationExecuteLogDO> logs = new ArrayList<>();
        for (int i = 1; i <= 50; i++) {
            ScrmAmProcessOrchestrationExecuteLogDO log = new ScrmAmProcessOrchestrationExecuteLogDO();
            log.setId((long) i);
            // Only 5 unique IDs
            log.setProcessOrchestrationId(100L + (i % 5));
            log.setProcessOrchestrationVersion("v1");
            log.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
            log.setAppId("app1");
            logs.add(log);
        }
        when(executeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecuteLogDOExample.class))).thenReturn(logs);
        // act
        boolean result = executeWriteDomainService.executeUnFinishedExternalCallExecuteLog();
        // assert
        assertTrue(result);
        verify(executeLogDOMapper).selectByExample(any(ScrmAmProcessOrchestrationExecuteLogDOExample.class));
        // Should only be called 5 times (once per unique processOrchestrationId)
        verify(refinementOperationExecuteMessageProducer, times(5)).sendExecutionTaskExecuteMessage(eq(new ArrayList<>()), eq("app1"), anyLong(), eq("v1"));
    }

    /**
     * Test multiple batches of logs (>100) with unique process orchestration IDs
     */
    @Test
    public void testExecuteUnFinishedExternalCallExecuteLogMultipleBatches() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationExecuteLogDO> firstBatch = new ArrayList<>();
        List<ScrmAmProcessOrchestrationExecuteLogDO> secondBatch = new ArrayList<>();
        // First batch (100 logs with unique IDs)
        for (int i = 1; i <= 100; i++) {
            ScrmAmProcessOrchestrationExecuteLogDO log = new ScrmAmProcessOrchestrationExecuteLogDO();
            log.setId((long) i);
            log.setProcessOrchestrationId(100L + i);
            log.setProcessOrchestrationVersion("v1");
            log.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
            log.setAppId("app1");
            firstBatch.add(log);
        }
        // Second batch (50 logs with unique IDs)
        for (int i = 101; i <= 150; i++) {
            ScrmAmProcessOrchestrationExecuteLogDO log = new ScrmAmProcessOrchestrationExecuteLogDO();
            log.setId((long) i);
            log.setProcessOrchestrationId(100L + i);
            log.setProcessOrchestrationVersion("v1");
            log.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
            log.setAppId("app1");
            secondBatch.add(log);
        }
        when(executeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecuteLogDOExample.class))).thenReturn(firstBatch).thenReturn(secondBatch);
        // act
        boolean result = executeWriteDomainService.executeUnFinishedExternalCallExecuteLog();
        // assert
        assertTrue(result);
        verify(executeLogDOMapper, times(2)).selectByExample(any(ScrmAmProcessOrchestrationExecuteLogDOExample.class));
        // Verify called once for each unique processOrchestrationId (150 times)
        verify(refinementOperationExecuteMessageProducer, times(150)).sendExecutionTaskExecuteMessage(eq(new ArrayList<>()), eq("app1"), anyLong(), eq("v1"));
    }

    /**
     * Test empty filter set case
     */
    @Test
    public void testExecuteUnFinishedExternalCallExecuteLogEmptyFilterSet() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationExecuteLogDO> logs = new ArrayList<>();
        ScrmAmProcessOrchestrationExecuteLogDO log = new ScrmAmProcessOrchestrationExecuteLogDO();
        log.setId(1L);
        log.setProcessOrchestrationId(100L);
        log.setProcessOrchestrationVersion("v1");
        log.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.WAIT_FOR_SEND.getValue().byteValue());
        log.setAppId("app1");
        logs.add(log);
        when(executeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecuteLogDOExample.class))).thenReturn(logs);
        // act
        boolean result = executeWriteDomainService.executeUnFinishedExternalCallExecuteLog();
        // assert
        assertTrue(result);
        verify(executeLogDOMapper).selectByExample(any(ScrmAmProcessOrchestrationExecuteLogDOExample.class));
        verify(refinementOperationExecuteMessageProducer).sendExecutionTaskExecuteMessage(eq(new ArrayList<>()), eq("app1"), eq(100L), eq("v1"));
    }

    /**
     * Test exception scenario
     */
    @Test
    public void testExecuteUnFinishedExternalCallExecuteLogException() throws Throwable {
        // arrange
        when(executeLogDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecuteLogDOExample.class))).thenThrow(new RuntimeException("Database error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            executeWriteDomainService.executeUnFinishedExternalCallExecuteLog();
        });
    }

    @Test
    public void testGetPreparedExecuteScrmProcessOrchestrationDTONoPlansFound() throws Throwable {
        // arrange
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.emptyList());
        // act
        ScrmProcessOrchestrationDTO result = executeWriteDomainService.getPreparedExecuteScrmProcessOrchestrationDTO();
        // assert
        assertNull(result);
        verify(executePlanDOMapper, atLeastOnce()).selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class));
    }

    @Test
    public void testGetPreparedExecuteScrmProcessOrchestrationDTOPlanFoundButLocked() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationExecutePlanDO planDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        planDO.setId(1L);
        planDO.setProcessOrchestrationId(100L);
        planDO.setProcessOrchestrationVersion("v1");
        planDO.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.PREPARING.getValue().byteValue());
        planDO.setTaskStartTime(new Date());
        ScrmAmCrowdPackAndProcessMapDO mapDO = new ScrmAmCrowdPackAndProcessMapDO();
        mapDO.setPackId(200L);
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.singletonList(planDO));
        when(crowdPackAndProcessMapDOMapper.selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class))).thenReturn(Collections.singletonList(mapDO));
        when(crowdPackUpdateLockService.getProducerValue(200L)).thenReturn(1L);
        // act
        ScrmProcessOrchestrationDTO result = executeWriteDomainService.getPreparedExecuteScrmProcessOrchestrationDTO();
        // assert
        assertNull(result);
        // The method is called 5 times due to the retry mechanism
        verify(executePlanDOMapper, times(5)).updateByPrimaryKeySelective(any(ScrmAmProcessOrchestrationExecutePlanDO.class));
        verify(crowdPackUpdateLockService, times(5)).getProducerValue(200L);
    }

    @Test
    public void testGetPreparedExecuteScrmProcessOrchestrationDTOPlanFoundAndUpdated() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationExecutePlanDO planDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        planDO.setId(1L);
        planDO.setProcessOrchestrationId(100L);
        planDO.setProcessOrchestrationVersion("v1");
        planDO.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.PREPARING.getValue().byteValue());
        planDO.setTaskStartTime(new Date());
        ScrmProcessOrchestrationDTO expectedDTO = new ScrmProcessOrchestrationDTO();
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.singletonList(planDO));
        when(crowdPackAndProcessMapDOMapper.selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class))).thenReturn(Collections.emptyList());
        CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(expectedDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(100L)).thenReturn(future);
        when(executePlanDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // act
        ScrmProcessOrchestrationDTO result = executeWriteDomainService.getPreparedExecuteScrmProcessOrchestrationDTO();
        // assert
        assertNotNull(result);
        assertEquals(expectedDTO, result);
        // Verify the status was updated to STARTED
        ArgumentCaptor<ScrmAmProcessOrchestrationExecutePlanDO> planCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationExecutePlanDO.class);
        verify(executePlanDOMapper).updateByExampleSelective(planCaptor.capture(), any());
        assertEquals(ScrmProcessOrchestrationExecuteStatusTypeEnum.STARTED.getValue().byteValue(), planCaptor.getValue().getStatus());
    }

    @Test
    public void testGetPreparedExecuteScrmProcessOrchestrationDTOVersionMismatch() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationExecutePlanDO planDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        planDO.setId(1L);
        planDO.setProcessOrchestrationId(100L);
        planDO.setProcessOrchestrationVersion("v1");
        planDO.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.PREPARING.getValue().byteValue());
        planDO.setTaskStartTime(new Date());
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.singletonList(planDO));
        when(crowdPackAndProcessMapDOMapper.selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class))).thenReturn(Collections.emptyList());
        CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(new ScrmProcessOrchestrationDTO());
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(100L)).thenReturn(future);
        when(executePlanDOMapper.updateByExampleSelective(any(), any())).thenReturn(0);
        // act
        ScrmProcessOrchestrationDTO result = executeWriteDomainService.getPreparedExecuteScrmProcessOrchestrationDTO();
        // assert
        assertNull(result);
        verify(executePlanDOMapper, atLeast(2)).selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class));
    }

    @Test
    public void testGetPreparedExecuteScrmProcessOrchestrationDTOTimeoutException() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationExecutePlanDO planDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        planDO.setId(1L);
        planDO.setProcessOrchestrationId(100L);
        planDO.setProcessOrchestrationVersion("v1");
        planDO.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.PREPARING.getValue().byteValue());
        planDO.setTaskStartTime(new Date());
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.singletonList(planDO));
        when(crowdPackAndProcessMapDOMapper.selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class))).thenReturn(Collections.emptyList());
        CompletableFuture<ScrmProcessOrchestrationDTO> future = new CompletableFuture<>();
        future.completeExceptionally(new TimeoutException("Test timeout"));
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(100L)).thenReturn(future);
        // act & assert
        assertThrows(ExecutionException.class, () -> {
            executeWriteDomainService.getPreparedExecuteScrmProcessOrchestrationDTO();
        });
        // Verify the cause of the ExecutionException is a TimeoutException
        try {
            executeWriteDomainService.getPreparedExecuteScrmProcessOrchestrationDTO();
        } catch (ExecutionException e) {
            assertTrue(e.getCause() instanceof TimeoutException);
        } catch (Exception e) {
            fail("Expected ExecutionException with TimeoutException cause");
        }
    }

    @Test
    public void testGetPreparedExecuteScrmProcessOrchestrationDTOExecutionException() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationExecutePlanDO planDO = new ScrmAmProcessOrchestrationExecutePlanDO();
        planDO.setId(1L);
        planDO.setProcessOrchestrationId(100L);
        planDO.setProcessOrchestrationVersion("v1");
        planDO.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.PREPARING.getValue().byteValue());
        planDO.setTaskStartTime(new Date());
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.singletonList(planDO));
        when(crowdPackAndProcessMapDOMapper.selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class))).thenReturn(Collections.emptyList());
        CompletableFuture<ScrmProcessOrchestrationDTO> future = new CompletableFuture<>();
        future.completeExceptionally(new ExecutionException(new RuntimeException("Test execution error")));
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(100L)).thenReturn(future);
        // act & assert
        assertThrows(ExecutionException.class, () -> {
            executeWriteDomainService.getPreparedExecuteScrmProcessOrchestrationDTO();
        });
    }

    @Test
    public void testGetPreparedExecuteScrmProcessOrchestrationDTOMultiplePlansFound() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationExecutePlanDO planDO1 = new ScrmAmProcessOrchestrationExecutePlanDO();
        planDO1.setId(1L);
        planDO1.setProcessOrchestrationId(100L);
        planDO1.setProcessOrchestrationVersion("v1");
        planDO1.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.PREPARING.getValue().byteValue());
        planDO1.setTaskStartTime(new Date());
        ScrmAmProcessOrchestrationExecutePlanDO planDO2 = new ScrmAmProcessOrchestrationExecutePlanDO();
        planDO2.setId(2L);
        planDO2.setProcessOrchestrationId(200L);
        planDO2.setProcessOrchestrationVersion("v2");
        planDO2.setStatus(ScrmProcessOrchestrationExecuteStatusTypeEnum.PREPARING.getValue().byteValue());
        planDO2.setTaskStartTime(new Date());
        List<ScrmAmProcessOrchestrationExecutePlanDO> plans = Arrays.asList(planDO1, planDO2);
        ScrmProcessOrchestrationDTO expectedDTO = new ScrmProcessOrchestrationDTO();
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(plans);
        when(crowdPackAndProcessMapDOMapper.selectByExample(any(ScrmAmCrowdPackAndProcessMapDOExample.class))).thenReturn(Collections.emptyList());
        CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(expectedDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(100L)).thenReturn(future);
        when(executePlanDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // act
        ScrmProcessOrchestrationDTO result = executeWriteDomainService.getPreparedExecuteScrmProcessOrchestrationDTO();
        // assert
        assertNotNull(result);
        assertEquals(expectedDTO, result);
        // Verify only the first plan was processed
        ArgumentCaptor<ScrmAmProcessOrchestrationExecutePlanDO> planCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationExecutePlanDO.class);
        verify(executePlanDOMapper).updateByExampleSelective(planCaptor.capture(), any());
        assertEquals(1L, planCaptor.getValue().getId());
    }

    @Test
    public void testGetPreparedExecuteScrmProcessOrchestrationDTOAllRetriesFail() throws Throwable {
        // arrange
        // First return empty list to trigger retry, then return a plan that will be locked
        // retryCount += 3 (now 3)
        // retryCount += 3 (now 3)
        // retryCount += 3 (now 6, exceeds limit of 5)
        // retryCount += 3 (now 6, exceeds limit of 5)
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.emptyList()).thenReturn(Collections.emptyList());
        // act
        ScrmProcessOrchestrationDTO result = executeWriteDomainService.getPreparedExecuteScrmProcessOrchestrationDTO();
        // assert
        assertNull(result);
        verify(executePlanDOMapper, times(2)).selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class));
    }

    @Test
    public void testUpdateCrowdPackManualSubTaskEmptyUserUnionIds() throws Throwable {
        // arrange
        List<String> emptyList = Collections.emptyList();
        // act
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(emptyList, testCrowdPackId, testAppId);
        // assert
        assertTrue(result);
        verifyNoInteractions(crowdPackWriteDomainService, executeManagementService, crowdPackReadDomainService);
    }

    @Test
    public void testUpdateCrowdPackManualSubTaskDeletedCrowdPack() throws Throwable {
        // arrange
        List<String> userUnionIds = Collections.singletonList(testUnionId);
        when(crowdPackWriteDomainService.isDeletedTempPack(testCrowdPackId)).thenReturn(true);
        // act
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, testCrowdPackId, testAppId);
        // assert
        assertFalse(result);
        verify(crowdPackWriteDomainService).isDeletedTempPack(testCrowdPackId);
        verifyNoMoreInteractions(crowdPackWriteDomainService);
        verifyNoInteractions(executeManagementService, crowdPackReadDomainService);
    }

    @Test
    public void testUpdateCrowdPackManualSubTaskSingleUserValid() throws Throwable {
        // arrange
        List<String> userUnionIds = Collections.singletonList(testUnionId);
        ScrmCrowdPackDTO packDTO = new ScrmCrowdPackDTO();
        packDTO.setId(testCrowdPackId);
        packDTO.setAppId(testAppId);
        packDTO.setValidPackVersion("v1");
        ScrmCrowdPackUpdateStrategyInfoDTO strategyInfoDTO = new ScrmCrowdPackUpdateStrategyInfoDTO();
        packDTO.setScrmCrowdPackUpdateStrategyInfoDTO(strategyInfoDTO);
        ScrmUserTag userTag = new ScrmUserTag();
        userTag.setUserId(testUserId);
        userTag.setAppId(testAppId);
        userTag.setUnionId(testUnionId);
        userTag.setStatus(1);
        CorpAppConfig config = new CorpAppConfig();
        config.setAppId(testAppId);
        config.setCorpId(testCorpId);
        ContactUserDo contactUserDo = new ContactUserDo();
        contactUserDo.setExternalUserId("externalId123");
        // Mock behavior
        when(crowdPackWriteDomainService.isDeletedTempPack(testCrowdPackId)).thenReturn(false);
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(testCrowdPackId, testAppId)).thenReturn(packDTO);
        when(userTagDOMapper.selectByExample(any(ScrmUserTagExample.class))).thenReturn(Collections.singletonList(userTag));
        when(appConfigRepository.getAllConfigs()).thenReturn(Collections.singletonList(config));
        when(appConfigRepository.getCorpIdByAppId(testAppId)).thenReturn(testCorpId);
        when(contactUserDoMapper.selectByExample(any())).thenReturn(Collections.singletonList(contactUserDo));
        when(crowdPackUpdateLockService.consumerIncrementValue(eq(testCrowdPackId), anyInt())).thenReturn(1L);
        when(crowdPackUpdateLockService.getProducerValue(testCrowdPackId)).thenReturn(1L);
        // Mock the condition utils to return true for valid condition match
        when(conditionUtils.isCrowdPackConditionMatch(eq(packDTO), eq(testUnionId), eq(strategyInfoDTO), anyList())).thenReturn(true);
        // Mock updateCrowdPackDetailInfo to return success for all calls
        when(crowdPackWriteDomainService.updateCrowdPackDetailInfo(any(ScrmAmCrowdPackDetailInfoDO.class), anyBoolean())).thenReturn(new DdlResultDTO(true, "success", 1L));
        // act
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, testCrowdPackId, testAppId);
        // assert
        assertTrue(result);
        verify(executeManagementService).subTaskRunBegin(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), testCrowdPackId);
        verify(executeManagementService).taskRunFinished(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), testCrowdPackId);
        // Verify updateCrowdPackDetailInfo is called with the correct parameters for the main crowd pack
        verify(crowdPackWriteDomainService).updateCrowdPackDetailInfo(argThat(arg -> arg.getPackId().equals(testCrowdPackId)), eq(true));
        verify(crowdPackUpdateLockService).deleteConsumerValue(testCrowdPackId);
        verify(crowdPackUpdateLockService).deleteProducerValue(testCrowdPackId);
    }

    @Test
    public void testUpdateCrowdPackManualSubTaskUserNoTags() throws Throwable {
        // arrange
        List<String> userUnionIds = Collections.singletonList(testUnionId);
        ScrmCrowdPackDTO packDTO = new ScrmCrowdPackDTO();
        packDTO.setId(testCrowdPackId);
        packDTO.setAppId(testAppId);
        when(crowdPackWriteDomainService.isDeletedTempPack(testCrowdPackId)).thenReturn(false);
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(testCrowdPackId, testAppId)).thenReturn(packDTO);
        when(userTagDOMapper.selectByExample(any(ScrmUserTagExample.class))).thenReturn(Collections.emptyList());
        when(crowdPackUpdateLockService.consumerIncrementValue(eq(testCrowdPackId), anyInt())).thenReturn(1L);
        when(crowdPackUpdateLockService.getProducerValue(testCrowdPackId)).thenReturn(1L);
        // act
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, testCrowdPackId, testAppId);
        // assert
        assertTrue(result);
        verify(crowdPackWriteDomainService, never()).updateCrowdPackDetailInfo(argThat(arg -> arg.getPackId().equals(testCrowdPackId)), anyBoolean());
        verify(crowdPackUpdateLockService).deleteConsumerValue(testCrowdPackId);
        verify(crowdPackUpdateLockService).deleteProducerValue(testCrowdPackId);
    }

    @Test
    public void testUpdateCrowdPackManualSubTaskUserNotMatchConditions() throws Throwable {
        // arrange
        List<String> userUnionIds = Collections.singletonList(testUnionId);
        ScrmCrowdPackDTO packDTO = new ScrmCrowdPackDTO();
        packDTO.setId(testCrowdPackId);
        packDTO.setAppId(testAppId);
        packDTO.setValidPackVersion("v1");
        ScrmCrowdPackUpdateStrategyInfoDTO strategyInfoDTO = new ScrmCrowdPackUpdateStrategyInfoDTO();
        packDTO.setScrmCrowdPackUpdateStrategyInfoDTO(strategyInfoDTO);
        ScrmUserTag userTag = new ScrmUserTag();
        userTag.setUserId(testUserId);
        // Different app ID so condition check will fail
        userTag.setAppId("otherApp");
        userTag.setUnionId(testUnionId);
        userTag.setStatus(1);
        CorpAppConfig config = new CorpAppConfig();
        config.setAppId(testAppId);
        config.setCorpId(testCorpId);
        when(crowdPackWriteDomainService.isDeletedTempPack(testCrowdPackId)).thenReturn(false);
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(testCrowdPackId, testAppId)).thenReturn(packDTO);
        when(userTagDOMapper.selectByExample(any(ScrmUserTagExample.class))).thenReturn(Collections.singletonList(userTag));
        when(appConfigRepository.getAllConfigs()).thenReturn(Collections.singletonList(config));
        when(crowdPackUpdateLockService.consumerIncrementValue(eq(testCrowdPackId), anyInt())).thenReturn(1L);
        when(crowdPackUpdateLockService.getProducerValue(testCrowdPackId)).thenReturn(1L);
        // Mock the condition utils to return false for invalid condition match
        when(conditionUtils.isCrowdPackConditionMatch(eq(packDTO), eq(testUnionId), eq(strategyInfoDTO), anyList())).thenReturn(false);
        // Mock updateCrowdPackDetailInfo to return success for all calls
        when(crowdPackWriteDomainService.updateCrowdPackDetailInfo(any(ScrmAmCrowdPackDetailInfoDO.class), anyBoolean())).thenReturn(new DdlResultDTO(true, "success", 1L));
        // act
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, testCrowdPackId, testAppId);
        // assert
        assertTrue(result);
        verify(crowdPackWriteDomainService).updateCrowdPackDetailInfo(argThat(arg -> arg.getPackId().equals(testCrowdPackId)), eq(false));
        verify(crowdPackUpdateLockService).deleteConsumerValue(testCrowdPackId);
        verify(crowdPackUpdateLockService).deleteProducerValue(testCrowdPackId);
    }

    @Test
    public void testUpdateCrowdPackManualSubTaskMultipleUsers() throws Throwable {
        // arrange
        List<String> userUnionIds = Arrays.asList("union1", "union2", "union3");
        ScrmCrowdPackDTO packDTO = new ScrmCrowdPackDTO();
        packDTO.setId(testCrowdPackId);
        packDTO.setAppId(testAppId);
        packDTO.setValidPackVersion("v1");
        ScrmCrowdPackUpdateStrategyInfoDTO strategyInfoDTO = new ScrmCrowdPackUpdateStrategyInfoDTO();
        packDTO.setScrmCrowdPackUpdateStrategyInfoDTO(strategyInfoDTO);
        ScrmUserTag userTag = new ScrmUserTag();
        userTag.setUserId(testUserId);
        userTag.setAppId(testAppId);
        userTag.setUnionId("union1");
        userTag.setStatus(1);
        CorpAppConfig config = new CorpAppConfig();
        config.setAppId(testAppId);
        config.setCorpId(testCorpId);
        ContactUserDo contactUserDo = new ContactUserDo();
        contactUserDo.setExternalUserId("externalId123");
        when(crowdPackWriteDomainService.isDeletedTempPack(testCrowdPackId)).thenReturn(false);
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(testCrowdPackId, testAppId)).thenReturn(packDTO);
        when(userTagDOMapper.selectByExample(any(ScrmUserTagExample.class))).thenReturn(Collections.singletonList(userTag));
        when(appConfigRepository.getAllConfigs()).thenReturn(Collections.singletonList(config));
        when(appConfigRepository.getCorpIdByAppId(testAppId)).thenReturn(testCorpId);
        when(contactUserDoMapper.selectByExample(any())).thenReturn(Collections.singletonList(contactUserDo));
        when(crowdPackUpdateLockService.consumerIncrementValue(eq(testCrowdPackId), eq(3))).thenReturn(3L);
        when(crowdPackUpdateLockService.getProducerValue(testCrowdPackId)).thenReturn(3L);
        // Mock the condition utils to return true for valid condition match
        when(conditionUtils.isCrowdPackConditionMatch(eq(packDTO), anyString(), eq(strategyInfoDTO), anyList())).thenReturn(true);
        // Mock updateCrowdPackDetailInfo to return success for all calls
        when(crowdPackWriteDomainService.updateCrowdPackDetailInfo(any(ScrmAmCrowdPackDetailInfoDO.class), anyBoolean())).thenReturn(new DdlResultDTO(true, "success", 1L));
        // act
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, testCrowdPackId, testAppId);
        // assert
        assertTrue(result);
        verify(crowdPackWriteDomainService, atLeast(3)).updateCrowdPackDetailInfo(argThat(arg -> arg.getPackId().equals(testCrowdPackId)), eq(true));
        verify(crowdPackUpdateLockService).deleteConsumerValue(testCrowdPackId);
        verify(crowdPackUpdateLockService).deleteProducerValue(testCrowdPackId);
    }

    @Test
    public void testUpdateCrowdPackManualSubTaskRedisCounterNotReached() throws Throwable {
        // arrange
        List<String> userUnionIds = Collections.singletonList(testUnionId);
        ScrmCrowdPackDTO packDTO = new ScrmCrowdPackDTO();
        packDTO.setId(testCrowdPackId);
        packDTO.setAppId(testAppId);
        ScrmCrowdPackUpdateStrategyInfoDTO strategyInfoDTO = new ScrmCrowdPackUpdateStrategyInfoDTO();
        packDTO.setScrmCrowdPackUpdateStrategyInfoDTO(strategyInfoDTO);
        ScrmUserTag userTag = new ScrmUserTag();
        userTag.setUserId(testUserId);
        userTag.setAppId(testAppId);
        userTag.setUnionId(testUnionId);
        userTag.setStatus(1);
        CorpAppConfig config = new CorpAppConfig();
        config.setAppId(testAppId);
        config.setCorpId(testCorpId);
        when(crowdPackWriteDomainService.isDeletedTempPack(testCrowdPackId)).thenReturn(false);
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(testCrowdPackId, testAppId)).thenReturn(packDTO);
        when(userTagDOMapper.selectByExample(any(ScrmUserTagExample.class))).thenReturn(Collections.singletonList(userTag));
        when(appConfigRepository.getAllConfigs()).thenReturn(Collections.singletonList(config));
        when(appConfigRepository.getCorpIdByAppId(testAppId)).thenReturn(testCorpId);
        when(crowdPackUpdateLockService.consumerIncrementValue(eq(testCrowdPackId), anyInt())).thenReturn(1L);
        // Total is 2 but we only processed 1
        when(crowdPackUpdateLockService.getProducerValue(testCrowdPackId)).thenReturn(2L);
        // Mock the condition utils to return true for valid condition match
        when(conditionUtils.isCrowdPackConditionMatch(eq(packDTO), eq(testUnionId), eq(strategyInfoDTO), anyList())).thenReturn(true);
        // Mock updateCrowdPackDetailInfo to return success for all calls
        when(crowdPackWriteDomainService.updateCrowdPackDetailInfo(any(ScrmAmCrowdPackDetailInfoDO.class), anyBoolean())).thenReturn(new DdlResultDTO(true, "success", 1L));
        // act
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, testCrowdPackId, testAppId);
        // assert
        assertTrue(result);
        verify(crowdPackUpdateLockService, never()).deleteConsumerValue(testCrowdPackId);
        verify(crowdPackUpdateLockService, never()).deleteProducerValue(testCrowdPackId);
    }

    @Test
    public void testUpdateCrowdPackManualSubTaskRedisCounterReached() throws Throwable {
        // arrange
        List<String> userUnionIds = Collections.singletonList(testUnionId);
        ScrmCrowdPackDTO packDTO = new ScrmCrowdPackDTO();
        packDTO.setId(testCrowdPackId);
        packDTO.setAppId(testAppId);
        ScrmCrowdPackUpdateStrategyInfoDTO strategyInfoDTO = new ScrmCrowdPackUpdateStrategyInfoDTO();
        packDTO.setScrmCrowdPackUpdateStrategyInfoDTO(strategyInfoDTO);
        ScrmUserTag userTag = new ScrmUserTag();
        userTag.setUserId(testUserId);
        userTag.setAppId(testAppId);
        userTag.setUnionId(testUnionId);
        userTag.setStatus(1);
        CorpAppConfig config = new CorpAppConfig();
        config.setAppId(testAppId);
        config.setCorpId(testCorpId);
        when(crowdPackWriteDomainService.isDeletedTempPack(testCrowdPackId)).thenReturn(false);
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(testCrowdPackId, testAppId)).thenReturn(packDTO);
        when(userTagDOMapper.selectByExample(any(ScrmUserTagExample.class))).thenReturn(Collections.singletonList(userTag));
        when(appConfigRepository.getAllConfigs()).thenReturn(Collections.singletonList(config));
        when(appConfigRepository.getCorpIdByAppId(testAppId)).thenReturn(testCorpId);
        when(crowdPackUpdateLockService.consumerIncrementValue(eq(testCrowdPackId), anyInt())).thenReturn(1L);
        when(crowdPackUpdateLockService.getProducerValue(testCrowdPackId)).thenReturn(1L);
        // Mock the condition utils to return true for valid condition match
        when(conditionUtils.isCrowdPackConditionMatch(eq(packDTO), eq(testUnionId), eq(strategyInfoDTO), anyList())).thenReturn(true);
        // Mock updateCrowdPackDetailInfo to return success for all calls
        when(crowdPackWriteDomainService.updateCrowdPackDetailInfo(any(ScrmAmCrowdPackDetailInfoDO.class), anyBoolean())).thenReturn(new DdlResultDTO(true, "success", 1L));
        // act
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, testCrowdPackId, testAppId);
        // assert
        assertTrue(result);
        verify(crowdPackUpdateLockService).deleteConsumerValue(testCrowdPackId);
        verify(crowdPackUpdateLockService).deleteProducerValue(testCrowdPackId);
    }

    @Test
    public void testUpdateCrowdPackManualSubTaskWithGroupList() throws Throwable {
        // arrange
        List<String> userUnionIds = Collections.singletonList(testUnionId);
        ScrmCrowdPackDTO packDTO = new ScrmCrowdPackDTO();
        packDTO.setId(testCrowdPackId);
        packDTO.setAppId(testAppId);
        packDTO.setValidPackVersion("v1");
        ScrmCrowdPackUpdateStrategyInfoDTO strategyInfoDTO = new ScrmCrowdPackUpdateStrategyInfoDTO();
        packDTO.setScrmCrowdPackUpdateStrategyInfoDTO(strategyInfoDTO);
        ScrmUserTag userTag = new ScrmUserTag();
        userTag.setUserId(testUserId);
        userTag.setAppId(testAppId);
        userTag.setUnionId(testUnionId);
        userTag.setStatus(1);
        CorpAppConfig config = new CorpAppConfig();
        config.setAppId(testAppId);
        config.setCorpId(testCorpId);
        MemberInfoEntity memberInfo = new MemberInfoEntity();
        memberInfo.setGroupMemberId("groupMemberId123");
        when(crowdPackWriteDomainService.isDeletedTempPack(testCrowdPackId)).thenReturn(false);
        when(crowdPackReadDomainService.queryCrowdPackDetailInfo(testCrowdPackId, testAppId)).thenReturn(packDTO);
        when(userTagDOMapper.selectByExample(any(ScrmUserTagExample.class))).thenReturn(Collections.singletonList(userTag));
        when(appConfigRepository.getAllConfigs()).thenReturn(Collections.singletonList(config));
        when(appConfigRepository.getCorpIdByAppId(testAppId)).thenReturn(testCorpId);
        // No contact user
        when(contactUserDoMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(executeManagementService.getUsersGroupList(testUnionId, testAppId)).thenReturn(Collections.singletonList(memberInfo));
        when(crowdPackUpdateLockService.consumerIncrementValue(eq(testCrowdPackId), anyInt())).thenReturn(1L);
        when(crowdPackUpdateLockService.getProducerValue(testCrowdPackId)).thenReturn(1L);
        // Mock the condition utils to return true for valid condition match
        when(conditionUtils.isCrowdPackConditionMatch(eq(packDTO), eq(testUnionId), eq(strategyInfoDTO), anyList())).thenReturn(true);
        // Mock updateCrowdPackDetailInfo to return success for all calls
        when(crowdPackWriteDomainService.updateCrowdPackDetailInfo(any(ScrmAmCrowdPackDetailInfoDO.class), anyBoolean())).thenReturn(new DdlResultDTO(true, "success", 1L));
        // act
        boolean result = executeWriteDomainService.updateCrowdPackManualSubTask(userUnionIds, testCrowdPackId, testAppId);
        // assert
        assertTrue(result);
        // We can't verify exact number of calls because the method is called in both updateAllUserInfos and the main flow
        verify(executeManagementService, atLeastOnce()).getUsersGroupList(testUnionId, testAppId);
        verify(crowdPackWriteDomainService).updateCrowdPackDetailInfo(argThat(arg -> arg.getPackId().equals(testCrowdPackId) && arg.getWxexternaluserid().equals("groupMemberId123")), eq(true));
    }

    private void invokePrivateUpdateCrowdPackExecuteTwoHoursLater() throws Exception {
        Method method = ExecuteWriteDomainService.class.getDeclaredMethod("updateCrowdPackExecuteTwoHoursLater");
        method.setAccessible(true);
        method.invoke(executeWriteDomainService);
    }

    @Test
    public void testUpdateCrowdPackExecuteTwoHoursLaterNoPlansFound() throws Throwable {
        // arrange
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.emptyList());
        // act
        invokePrivateUpdateCrowdPackExecuteTwoHoursLater();
        // assert
        verify(executePlanDOMapper).selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class));
        verifyNoMoreInteractions(scrmAmProcessOrchestrationInfoDOMapper, crowdPackAndProcessMapDOMapper, crowdPackUpdateLockService, refinementOperationExecuteMessageProducer);
    }

    @Test
    public void testUpdateCrowdPackExecuteTwoHoursLaterNoProcessInfo() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationExecutePlanDO plan = new ScrmAmProcessOrchestrationExecutePlanDO();
        plan.setProcessOrchestrationId(1L);
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.singletonList(plan));
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class)))
                .thenReturn(Collections.emptyList());
        // act
        invokePrivateUpdateCrowdPackExecuteTwoHoursLater();
        // assert
        verify(scrmAmProcessOrchestrationInfoDOMapper).selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class));
        verifyNoInteractions(crowdPackAndProcessMapDOMapper, crowdPackUpdateLockService, refinementOperationExecuteMessageProducer);
    }

    @Test
    public void testUpdateCrowdPackExecuteTwoHoursLaterNoCrowdPackMapping() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationExecutePlanDO plan = new ScrmAmProcessOrchestrationExecutePlanDO();
        plan.setProcessOrchestrationId(1L);
        ScrmAmProcessOrchestrationInfoDO info = new ScrmAmProcessOrchestrationInfoDO();
        info.setValidVersion("1.0");
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.singletonList(plan));
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class)))
                .thenReturn(Arrays.asList(info));
        when(crowdPackAndProcessMapDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        invokePrivateUpdateCrowdPackExecuteTwoHoursLater();
        // assert
        verify(crowdPackAndProcessMapDOMapper).selectByExample(any());
        verifyNoInteractions(scrmAmCrowdPackBaseInfoDOMapper, crowdPackUpdateLockService, refinementOperationExecuteMessageProducer);
    }

    @Test
    public void testUpdateCrowdPackExecuteTwoHoursLaterNonTempPack() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationExecutePlanDO plan = new ScrmAmProcessOrchestrationExecutePlanDO();
        plan.setProcessOrchestrationId(1L);
        ScrmAmProcessOrchestrationInfoDO info = new ScrmAmProcessOrchestrationInfoDO();
        info.setValidVersion("1.0");
        ScrmAmCrowdPackAndProcessMapDO map = new ScrmAmCrowdPackAndProcessMapDO();
        map.setPackId(100L);
        ScrmAmCrowdPackBaseInfoDO packInfo = new ScrmAmCrowdPackBaseInfoDO();
        // Non-temp pack
        packInfo.setType((byte) 2);
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.singletonList(plan));
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class)))
                .thenReturn(Arrays.asList(info));
        when(crowdPackAndProcessMapDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(map));
        when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(100L)).thenReturn(packInfo);
        // act
        invokePrivateUpdateCrowdPackExecuteTwoHoursLater();
        // assert
        verify(scrmAmCrowdPackBaseInfoDOMapper).selectByPrimaryKey(100L);
        verifyNoInteractions(crowdPackUpdateLockService, refinementOperationExecuteMessageProducer);
    }

    @Test
    public void testUpdateCrowdPackExecuteTwoHoursLaterTempPackSuccess() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationExecutePlanDO plan = new ScrmAmProcessOrchestrationExecutePlanDO();
        plan.setProcessOrchestrationId(1L);
        ScrmAmProcessOrchestrationInfoDO info = new ScrmAmProcessOrchestrationInfoDO();
        info.setValidVersion("1.0");
        info.setAppId("app1");
        ScrmAmCrowdPackAndProcessMapDO map = new ScrmAmCrowdPackAndProcessMapDO();
        map.setPackId(100L);
        ScrmAmCrowdPackBaseInfoDO packInfo = new ScrmAmCrowdPackBaseInfoDO();
        // Temp pack (ScrmCrowdPackTypeEnum.TEMP_PACK.getValue())
        packInfo.setType((byte) 1);
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.singletonList(plan));
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class)))
                .thenReturn(Arrays.asList(info));
        when(crowdPackAndProcessMapDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(map));
        when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(100L)).thenReturn(packInfo);
        when(crowdPackUpdateLockService.tryProducerLock(100L, 60 * 60 * 2)).thenReturn(true);
        // act
        invokePrivateUpdateCrowdPackExecuteTwoHoursLater();
        // assert
        verify(crowdPackUpdateLockService).tryProducerLock(100L, 60 * 60 * 2);
        verify(refinementOperationExecuteMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(new ArrayList<>(), "app1", 100L);
        verify(executePlanDOMapper).updateByPrimaryKey(plan);
        assertEquals(ScrmProcessOrchestrationExecutePlanPackStatusEnum.UPDATE_MESSAGE_SENT.getCode().byteValue(), plan.getPackStatus());
    }

    @Test
    public void testUpdateCrowdPackExecuteTwoHoursLaterTempPackLockFailed() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationExecutePlanDO plan = new ScrmAmProcessOrchestrationExecutePlanDO();
        plan.setProcessOrchestrationId(1L);
        ScrmAmProcessOrchestrationInfoDO info = new ScrmAmProcessOrchestrationInfoDO();
        info.setValidVersion("1.0");
        info.setAppId("app1");
        ScrmAmCrowdPackAndProcessMapDO map = new ScrmAmCrowdPackAndProcessMapDO();
        map.setPackId(100L);
        ScrmAmCrowdPackBaseInfoDO packInfo = new ScrmAmCrowdPackBaseInfoDO();
        // Temp pack
        packInfo.setType((byte) 1);
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.singletonList(plan));
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class)))
                .thenReturn(Arrays.asList(info));
        when(crowdPackAndProcessMapDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(map));
        when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(100L)).thenReturn(packInfo);
        when(crowdPackUpdateLockService.tryProducerLock(100L, 60 * 60 * 2)).thenReturn(false);
        // act
        invokePrivateUpdateCrowdPackExecuteTwoHoursLater();
        // assert
        verify(crowdPackUpdateLockService).tryProducerLock(100L, 60 * 60 * 2);
        // Based on the error, the message is still sent even when lock fails
        verify(refinementOperationExecuteMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(new ArrayList<>(), "app1", 100L);
        verify(executePlanDOMapper).updateByPrimaryKey(plan);
        assertEquals(ScrmProcessOrchestrationExecutePlanPackStatusEnum.UPDATE_MESSAGE_SENT.getCode().byteValue(), plan.getPackStatus());
    }

    @Test
    public void testUpdateCrowdPackExecuteTwoHoursLaterMultiplePlans() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationExecutePlanDO> plans = new ArrayList<>();
        // Plan 1 - temp pack
        ScrmAmProcessOrchestrationExecutePlanDO plan1 = new ScrmAmProcessOrchestrationExecutePlanDO();
        plan1.setProcessOrchestrationId(1L);
        plans.add(plan1);
        // Plan 2 - non-temp pack
        ScrmAmProcessOrchestrationExecutePlanDO plan2 = new ScrmAmProcessOrchestrationExecutePlanDO();
        plan2.setProcessOrchestrationId(2L);
        plans.add(plan2);
        ScrmAmProcessOrchestrationInfoDO info1 = new ScrmAmProcessOrchestrationInfoDO();
        info1.setValidVersion("1.0");
        info1.setAppId("app1");
        ScrmAmProcessOrchestrationInfoDO info2 = new ScrmAmProcessOrchestrationInfoDO();
        info2.setValidVersion("2.0");
        info2.setAppId("app2");
        ScrmAmCrowdPackAndProcessMapDO map1 = new ScrmAmCrowdPackAndProcessMapDO();
        map1.setPackId(100L);
        ScrmAmCrowdPackAndProcessMapDO map2 = new ScrmAmCrowdPackAndProcessMapDO();
        map2.setPackId(200L);
        ScrmAmCrowdPackBaseInfoDO packInfo1 = new ScrmAmCrowdPackBaseInfoDO();
        // Temp pack
        packInfo1.setType((byte) 1);
        ScrmAmCrowdPackBaseInfoDO packInfo2 = new ScrmAmCrowdPackBaseInfoDO();
        // Non-temp pack
        packInfo2.setType((byte) 2);
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(plans);
        // Setup process orchestration info mocks
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class)))
                .thenReturn(Arrays.asList(info1))  // 第一次调用返回 info1
                .thenReturn(Arrays.asList(info2)); // 第二次调用返回 info2
        // Setup crowd pack mapping mocks - use consecutive calls pattern
        // First call returns map1
        // First call returns map1
        // Second call returns map2
        // Second call returns map2
        when(crowdPackAndProcessMapDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(map1)).thenReturn(Collections.singletonList(map2));
        // Setup crowd pack base info mocks
        when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(100L)).thenReturn(packInfo1);
        when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(200L)).thenReturn(packInfo2);
        when(crowdPackUpdateLockService.tryProducerLock(100L, 60 * 60 * 2)).thenReturn(true);
        // act
        invokePrivateUpdateCrowdPackExecuteTwoHoursLater();
        // assert
        verify(crowdPackUpdateLockService).tryProducerLock(100L, 60 * 60 * 2);
        verify(refinementOperationExecuteMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(new ArrayList<>(), "app1", 100L);
        // Verify plan1 was updated
        ArgumentCaptor<ScrmAmProcessOrchestrationExecutePlanDO> planCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationExecutePlanDO.class);
        verify(executePlanDOMapper).updateByPrimaryKey(planCaptor.capture());
        ScrmAmProcessOrchestrationExecutePlanDO capturedPlan = planCaptor.getValue();
        assertEquals(1L, capturedPlan.getProcessOrchestrationId());
        assertEquals(ScrmProcessOrchestrationExecutePlanPackStatusEnum.UPDATE_MESSAGE_SENT.getCode().byteValue(), capturedPlan.getPackStatus());
        // Verify both pack infos were checked
        verify(scrmAmProcessOrchestrationInfoDOMapper, times(2)).selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class));
        verify(scrmAmCrowdPackBaseInfoDOMapper).selectByPrimaryKey(100L);
        verify(scrmAmCrowdPackBaseInfoDOMapper).selectByPrimaryKey(200L);
    }

    @Test
    public void testUpdateCrowdPackExecuteTwoHoursLaterNullCrowdPackInfo() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationExecutePlanDO plan = new ScrmAmProcessOrchestrationExecutePlanDO();
        plan.setProcessOrchestrationId(1L);
        ScrmAmProcessOrchestrationInfoDO info = new ScrmAmProcessOrchestrationInfoDO();
        info.setValidVersion("1.0");
        ScrmAmCrowdPackAndProcessMapDO map = new ScrmAmCrowdPackAndProcessMapDO();
        map.setPackId(100L);
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.singletonList(plan));
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any(ScrmAmProcessOrchestrationInfoDOExample.class)))
                .thenReturn(Arrays.asList(info));
        when(crowdPackAndProcessMapDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(map));
        when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(100L)).thenReturn(null);
        // act
        invokePrivateUpdateCrowdPackExecuteTwoHoursLater();
        // assert
        verify(scrmAmCrowdPackBaseInfoDOMapper).selectByPrimaryKey(100L);
        verifyNoInteractions(crowdPackUpdateLockService, refinementOperationExecuteMessageProducer);
    }

    @Test
    public void testUpdateCrowdPackByPackId_DeletedTempPack() throws Throwable {
        when(crowdPackWriteDomainService.isDeletedTempPack(crowdPackId)).thenReturn(true);
        executeWriteDomainService.updateCrowdPackByPackId(crowdPackId, appId);
        verify(crowdPackWriteDomainService).isDeletedTempPack(crowdPackId);
        verifyNoMoreInteractions(executeManagementService, scrmAmCrowdPackBaseInfoDOMapper, updateStrategyDOMapper);
    }

    @Test
    public void testUpdateCrowdPackByPackId_TaskAlreadyRunning() throws Throwable {
        AmLionConfigDTO amLionConfigDTO = new AmLionConfigDTO();
        amLionConfigDTO.setMaxCrowdOfEachCorpSize(10000);
        when(configDomainService.getAmLionConfigDTO()).thenReturn(amLionConfigDTO);
        when(crowdPackWriteDomainService.isDeletedTempPack(crowdPackId)).thenReturn(false);
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), crowdPackId)).thenReturn(false);
        executeWriteDomainService.updateCrowdPackByPackId(crowdPackId, appId);
        verify(crowdPackWriteDomainService).isDeletedTempPack(crowdPackId);
        verify(executeManagementService).taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), crowdPackId);
        verifyNoMoreInteractions(scrmAmCrowdPackBaseInfoDOMapper, updateStrategyDOMapper);
    }

    @Test
    public void testUpdateCrowdPackByPackId_EmptyStrategyList() throws Throwable {
        AmLionConfigDTO amLionConfigDTO = new AmLionConfigDTO();
        amLionConfigDTO.setMaxCrowdOfEachCorpSize(10000);
        when(configDomainService.getAmLionConfigDTO()).thenReturn(amLionConfigDTO);
        when(crowdPackWriteDomainService.isDeletedTempPack(crowdPackId)).thenReturn(false);
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), crowdPackId)).thenReturn(true);
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setValidPackVersion(validPackVersion);
        when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(crowdPackId)).thenReturn(baseInfoDO);
        when(updateStrategyDOMapper.selectByExample(any(ScrmAmCrowdPackUpdateStrategyDOExample.class))).thenReturn(Collections.emptyList());
        executeWriteDomainService.updateCrowdPackByPackId(crowdPackId, appId);
        verify(crowdPackWriteDomainService).isDeletedTempPack(crowdPackId);
        verify(executeManagementService).taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), crowdPackId);
        verify(scrmAmCrowdPackBaseInfoDOMapper).selectByPrimaryKey(crowdPackId);
        verify(updateStrategyDOMapper).selectByExample(any(ScrmAmCrowdPackUpdateStrategyDOExample.class));
        verify(executeManagementService).taskRunFinished(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), crowdPackId);
    }

    @Test
    public void testUpdateCrowdPackByPackId_NullBaseInfo() throws Throwable {
        AmLionConfigDTO amLionConfigDTO = new AmLionConfigDTO();
        amLionConfigDTO.setMaxCrowdOfEachCorpSize(10000);
        when(configDomainService.getAmLionConfigDTO()).thenReturn(amLionConfigDTO);
        when(crowdPackWriteDomainService.isDeletedTempPack(crowdPackId)).thenReturn(false);
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), crowdPackId)).thenReturn(true);
        when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(crowdPackId)).thenReturn(null);
        assertThrows(NullPointerException.class, () -> executeWriteDomainService.updateCrowdPackByPackId(crowdPackId, appId));
    }

    private List<ContactUser> createContactUsers() {
        ContactUser contactUser = new ContactUser();
        contactUser.setExternalUserId(TEST_EXTERNAL_USER_ID);
        return Lists.newArrayList(contactUser);
    }

    private List<MemberInfoEntity> createMemberInfoEntities() {
        MemberInfoEntity memberInfoEntity = new MemberInfoEntity();
        memberInfoEntity.setGroupMemberId(TEST_EXTERNAL_USER_ID);
        return Lists.newArrayList(memberInfoEntity);
    }

    @Test
    public void testInsertCrowdWhenBothUserIdAndUnionIdAreNull() throws Throwable {
        // arrange
        Long userId = null;
        String unionId = null;
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(executeWriteDomainService, "insertCrowd", userId, unionId, TEST_PACK_ID, TEST_PACK_VERSION, TEST_APP_ID);
        // assert
        assertFalse(result);
        verifyNoInteractions(crowdPackWriteDomainService, contactUserDomain, groupMemberDomainService, appConfigRepository);
    }

    @Test
    public void testInsertCrowdWhenUserIdIsNullButUnionIdIsProvided() throws Throwable {
        // arrange
        Long userId = null;
        String unionId = TEST_UNION_ID;
        List<ContactUser> contactUsers = createContactUsers();
        when(appConfigRepository.getCorpIdByAppId(TEST_APP_ID)).thenReturn(TEST_CORP_ID);
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(TEST_CORP_ID, unionId)).thenReturn(contactUsers);
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(TEST_CORP_ID, unionId)).thenReturn(Collections.emptyList());
        when(crowdPackWriteDomainService.insertCrowdPackIfExist(TEST_PACK_ID, unionId, TEST_PACK_VERSION, TEST_APP_ID)).thenReturn(false);
        when(crowdPackWriteDomainService.insertCrowdPackDetailInfo(eq(TEST_PACK_ID), eq(unionId), eq(TEST_EXTERNAL_USER_ID), eq(TEST_APP_ID), eq(TEST_PACK_VERSION))).thenReturn(true);
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(executeWriteDomainService, "insertCrowd", userId, unionId, TEST_PACK_ID, TEST_PACK_VERSION, TEST_APP_ID);
        // assert
        assertTrue(result);
        verify(crowdPackWriteDomainService).insertCrowdPackDetailInfo(eq(TEST_PACK_ID), eq(unionId), eq(TEST_EXTERNAL_USER_ID), eq(TEST_APP_ID), eq(TEST_PACK_VERSION));
    }

    @Test
    public void testInsertCrowdWhenUnionIdIsNullAndUserIdIsProvidedConversionSuccessful() throws Throwable {
        // arrange
        Long userId = TEST_USER_ID;
        String unionId = null;
        when(idConvertUtils.convertMtUserIdToUnionId(userId, TEST_APP_ID)).thenReturn(TEST_UNION_ID);
        when(appConfigRepository.getCorpIdByAppId(TEST_APP_ID)).thenReturn(TEST_CORP_ID);
        List<ContactUser> contactUsers = createContactUsers();
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(TEST_CORP_ID, TEST_UNION_ID)).thenReturn(contactUsers);
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(TEST_CORP_ID, TEST_UNION_ID)).thenReturn(Collections.emptyList());
        when(crowdPackWriteDomainService.insertCrowdPackIfExist(TEST_PACK_ID, TEST_UNION_ID, TEST_PACK_VERSION, TEST_APP_ID)).thenReturn(false);
        when(crowdPackWriteDomainService.insertCrowdPackDetailInfo(eq(TEST_PACK_ID), eq(TEST_UNION_ID), eq(TEST_EXTERNAL_USER_ID), eq(TEST_APP_ID), eq(TEST_PACK_VERSION))).thenReturn(true);
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(executeWriteDomainService, "insertCrowd", userId, unionId, TEST_PACK_ID, TEST_PACK_VERSION, TEST_APP_ID);
        // assert
        assertTrue(result);
        verify(idConvertUtils).convertMtUserIdToUnionId(userId, TEST_APP_ID);
        verify(crowdPackWriteDomainService).insertCrowdPackDetailInfo(eq(TEST_PACK_ID), eq(TEST_UNION_ID), eq(TEST_EXTERNAL_USER_ID), eq(TEST_APP_ID), eq(TEST_PACK_VERSION));
    }

    @Test
    public void testInsertCrowdWhenUnionIdIsNullAndUserIdIsProvidedButConversionFails() throws Throwable {
        // arrange
        Long userId = TEST_USER_ID;
        String unionId = null;
        when(idConvertUtils.convertMtUserIdToUnionId(userId, TEST_APP_ID)).thenReturn(null);
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(executeWriteDomainService, "insertCrowd", userId, unionId, TEST_PACK_ID, TEST_PACK_VERSION, TEST_APP_ID);
        // assert
        assertFalse(result);
        verify(idConvertUtils).convertMtUserIdToUnionId(userId, TEST_APP_ID);
        verifyNoInteractions(crowdPackWriteDomainService, contactUserDomain, groupMemberDomainService, appConfigRepository);
    }

    @Test
    public void testInsertCrowdWhenUserAlreadyExistsInCrowdPack() throws Throwable {
        // arrange
        Long userId = TEST_USER_ID;
        String unionId = TEST_UNION_ID;
        when(crowdPackWriteDomainService.insertCrowdPackIfExist(TEST_PACK_ID, unionId, TEST_PACK_VERSION, TEST_APP_ID)).thenReturn(true);
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(executeWriteDomainService, "insertCrowd", userId, unionId, TEST_PACK_ID, TEST_PACK_VERSION, TEST_APP_ID);
        // assert
        assertTrue(result);
        verify(crowdPackWriteDomainService).insertCrowdPackIfExist(TEST_PACK_ID, unionId, TEST_PACK_VERSION, TEST_APP_ID);
        verify(crowdPackWriteDomainService, never()).insertCrowdPackDetailInfo(anyLong(), anyString(), anyString(), anyString(), anyString());
        verifyNoInteractions(contactUserDomain, groupMemberDomainService, appConfigRepository);
    }

    @Test
    public void testInsertCrowdWhenUserIsNotFoundInSystem() throws Throwable {
        // arrange
        Long userId = TEST_USER_ID;
        String unionId = TEST_UNION_ID;
        when(crowdPackWriteDomainService.insertCrowdPackIfExist(TEST_PACK_ID, unionId, TEST_PACK_VERSION, TEST_APP_ID)).thenReturn(false);
        when(appConfigRepository.getCorpIdByAppId(TEST_APP_ID)).thenReturn(TEST_CORP_ID);
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(TEST_CORP_ID, unionId)).thenReturn(Collections.emptyList());
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(TEST_CORP_ID, unionId)).thenReturn(Collections.emptyList());
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(executeWriteDomainService, "insertCrowd", userId, unionId, TEST_PACK_ID, TEST_PACK_VERSION, TEST_APP_ID);
        // assert
        assertFalse(result);
        verify(crowdPackWriteDomainService).insertCrowdPackIfExist(TEST_PACK_ID, unionId, TEST_PACK_VERSION, TEST_APP_ID);
        verify(contactUserDomain).getContactUsersByCorpIdAndUnionId(TEST_CORP_ID, unionId);
        verify(groupMemberDomainService).getMemberInfoEntitiesByCorpIdAndUnionId(TEST_CORP_ID, unionId);
        verify(crowdPackWriteDomainService, never()).insertCrowdPackDetailInfo(anyLong(), anyString(), anyString(), anyString(), anyString());
    }

    @Test
    public void testInsertCrowdWhenUserIsFoundInContactUsers() throws Throwable {
        // arrange
        Long userId = TEST_USER_ID;
        String unionId = TEST_UNION_ID;
        List<ContactUser> contactUsers = createContactUsers();
        when(crowdPackWriteDomainService.insertCrowdPackIfExist(TEST_PACK_ID, unionId, TEST_PACK_VERSION, TEST_APP_ID)).thenReturn(false);
        when(appConfigRepository.getCorpIdByAppId(TEST_APP_ID)).thenReturn(TEST_CORP_ID);
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(TEST_CORP_ID, unionId)).thenReturn(contactUsers);
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(TEST_CORP_ID, unionId)).thenReturn(Collections.emptyList());
        when(crowdPackWriteDomainService.insertCrowdPackDetailInfo(eq(TEST_PACK_ID), eq(unionId), eq(TEST_EXTERNAL_USER_ID), eq(TEST_APP_ID), eq(TEST_PACK_VERSION))).thenReturn(true);
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(executeWriteDomainService, "insertCrowd", userId, unionId, TEST_PACK_ID, TEST_PACK_VERSION, TEST_APP_ID);
        // assert
        assertTrue(result);
        verify(crowdPackWriteDomainService).insertCrowdPackIfExist(TEST_PACK_ID, unionId, TEST_PACK_VERSION, TEST_APP_ID);
        verify(contactUserDomain).getContactUsersByCorpIdAndUnionId(TEST_CORP_ID, unionId);
        verify(groupMemberDomainService).getMemberInfoEntitiesByCorpIdAndUnionId(TEST_CORP_ID, unionId);
        verify(crowdPackWriteDomainService).insertCrowdPackDetailInfo(eq(TEST_PACK_ID), eq(unionId), eq(TEST_EXTERNAL_USER_ID), eq(TEST_APP_ID), eq(TEST_PACK_VERSION));
    }

    @Test
    public void testInsertCrowdWhenUserIsFoundInMemberInfo() throws Throwable {
        // arrange
        Long userId = TEST_USER_ID;
        String unionId = TEST_UNION_ID;
        List<MemberInfoEntity> memberInfos = createMemberInfoEntities();
        when(crowdPackWriteDomainService.insertCrowdPackIfExist(TEST_PACK_ID, unionId, TEST_PACK_VERSION, TEST_APP_ID)).thenReturn(false);
        when(appConfigRepository.getCorpIdByAppId(TEST_APP_ID)).thenReturn(TEST_CORP_ID);
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(TEST_CORP_ID, unionId)).thenReturn(Collections.emptyList());
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(TEST_CORP_ID, unionId)).thenReturn(memberInfos);
        when(crowdPackWriteDomainService.insertCrowdPackDetailInfo(eq(TEST_PACK_ID), eq(unionId), eq(TEST_EXTERNAL_USER_ID), eq(TEST_APP_ID), eq(TEST_PACK_VERSION))).thenReturn(true);
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(executeWriteDomainService, "insertCrowd", userId, unionId, TEST_PACK_ID, TEST_PACK_VERSION, TEST_APP_ID);
        // assert
        assertTrue(result);
        verify(crowdPackWriteDomainService).insertCrowdPackIfExist(TEST_PACK_ID, unionId, TEST_PACK_VERSION, TEST_APP_ID);
        verify(contactUserDomain).getContactUsersByCorpIdAndUnionId(TEST_CORP_ID, unionId);
        verify(groupMemberDomainService).getMemberInfoEntitiesByCorpIdAndUnionId(TEST_CORP_ID, unionId);
        verify(crowdPackWriteDomainService).insertCrowdPackDetailInfo(eq(TEST_PACK_ID), eq(unionId), eq(TEST_EXTERNAL_USER_ID), eq(TEST_APP_ID), eq(TEST_PACK_VERSION));
    }

    @Test
    public void testInsertCrowdWhenUserIsFoundInBothContactUsersAndMemberInfo() throws Throwable {
        // arrange
        Long userId = TEST_USER_ID;
        String unionId = TEST_UNION_ID;
        List<ContactUser> contactUsers = createContactUsers();
        List<MemberInfoEntity> memberInfos = createMemberInfoEntities();
        when(crowdPackWriteDomainService.insertCrowdPackIfExist(TEST_PACK_ID, unionId, TEST_PACK_VERSION, TEST_APP_ID)).thenReturn(false);
        when(appConfigRepository.getCorpIdByAppId(TEST_APP_ID)).thenReturn(TEST_CORP_ID);
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(TEST_CORP_ID, unionId)).thenReturn(contactUsers);
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(TEST_CORP_ID, unionId)).thenReturn(memberInfos);
        when(crowdPackWriteDomainService.insertCrowdPackDetailInfo(eq(TEST_PACK_ID), eq(unionId), eq(TEST_EXTERNAL_USER_ID), eq(TEST_APP_ID), eq(TEST_PACK_VERSION))).thenReturn(true);
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(executeWriteDomainService, "insertCrowd", userId, unionId, TEST_PACK_ID, TEST_PACK_VERSION, TEST_APP_ID);
        // assert
        assertTrue(result);
        verify(crowdPackWriteDomainService).insertCrowdPackIfExist(TEST_PACK_ID, unionId, TEST_PACK_VERSION, TEST_APP_ID);
        verify(contactUserDomain).getContactUsersByCorpIdAndUnionId(TEST_CORP_ID, unionId);
        verify(groupMemberDomainService).getMemberInfoEntitiesByCorpIdAndUnionId(TEST_CORP_ID, unionId);
        verify(crowdPackWriteDomainService).insertCrowdPackDetailInfo(eq(TEST_PACK_ID), eq(unionId), eq(TEST_EXTERNAL_USER_ID), eq(TEST_APP_ID), eq(TEST_PACK_VERSION));
    }

    @Test
    public void testInsertCrowdWhenInsertionIntoCrowdPackFails() throws Throwable {
        // arrange
        Long userId = TEST_USER_ID;
        String unionId = TEST_UNION_ID;
        List<ContactUser> contactUsers = createContactUsers();
        when(crowdPackWriteDomainService.insertCrowdPackIfExist(TEST_PACK_ID, unionId, TEST_PACK_VERSION, TEST_APP_ID)).thenReturn(false);
        when(appConfigRepository.getCorpIdByAppId(TEST_APP_ID)).thenReturn(TEST_CORP_ID);
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(TEST_CORP_ID, unionId)).thenReturn(contactUsers);
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(TEST_CORP_ID, unionId)).thenReturn(Collections.emptyList());
        when(crowdPackWriteDomainService.insertCrowdPackDetailInfo(eq(TEST_PACK_ID), eq(unionId), eq(TEST_EXTERNAL_USER_ID), eq(TEST_APP_ID), eq(TEST_PACK_VERSION))).thenReturn(false);
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(executeWriteDomainService, "insertCrowd", userId, unionId, TEST_PACK_ID, TEST_PACK_VERSION, TEST_APP_ID);
        // assert
        assertFalse(result);
        verify(crowdPackWriteDomainService).insertCrowdPackIfExist(TEST_PACK_ID, unionId, TEST_PACK_VERSION, TEST_APP_ID);
        verify(contactUserDomain).getContactUsersByCorpIdAndUnionId(TEST_CORP_ID, unionId);
        verify(groupMemberDomainService).getMemberInfoEntitiesByCorpIdAndUnionId(TEST_CORP_ID, unionId);
        verify(crowdPackWriteDomainService).insertCrowdPackDetailInfo(eq(TEST_PACK_ID), eq(unionId), eq(TEST_EXTERNAL_USER_ID), eq(TEST_APP_ID), eq(TEST_PACK_VERSION));
    }

    @Test
    public void testInsertCrowdWhenExceptionOccurs() throws Throwable {
        // arrange
        Long userId = TEST_USER_ID;
        String unionId = TEST_UNION_ID;
        when(crowdPackWriteDomainService.insertCrowdPackIfExist(TEST_PACK_ID, unionId, TEST_PACK_VERSION, TEST_APP_ID)).thenThrow(new RuntimeException("Test exception"));
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(executeWriteDomainService, "insertCrowd", userId, unionId, TEST_PACK_ID, TEST_PACK_VERSION, TEST_APP_ID);
        // assert
        assertFalse(result);
        verify(crowdPackWriteDomainService).insertCrowdPackIfExist(TEST_PACK_ID, unionId, TEST_PACK_VERSION, TEST_APP_ID);
        verifyNoInteractions(contactUserDomain, groupMemberDomainService);
        verifyNoMoreInteractions(crowdPackWriteDomainService);
    }

    @Test
    public void testInsertCrowdWhenPackIdIsNull() throws Throwable {
        // arrange
        Long userId = TEST_USER_ID;
        String unionId = TEST_UNION_ID;
        Long packId = null;
        // We need to mock the behavior for null packId
        when(crowdPackWriteDomainService.insertCrowdPackIfExist(null, unionId, TEST_PACK_VERSION, TEST_APP_ID)).thenReturn(false);
        when(appConfigRepository.getCorpIdByAppId(TEST_APP_ID)).thenReturn(TEST_CORP_ID);
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(TEST_CORP_ID, unionId)).thenReturn(Collections.emptyList());
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(TEST_CORP_ID, unionId)).thenReturn(Collections.emptyList());
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(executeWriteDomainService, "insertCrowd", userId, unionId, packId, TEST_PACK_VERSION, TEST_APP_ID);
        // assert
        assertFalse(result);
        verify(crowdPackWriteDomainService).insertCrowdPackIfExist(null, unionId, TEST_PACK_VERSION, TEST_APP_ID);
        verify(appConfigRepository).getCorpIdByAppId(TEST_APP_ID);
        verify(contactUserDomain).getContactUsersByCorpIdAndUnionId(TEST_CORP_ID, unionId);
        verify(groupMemberDomainService).getMemberInfoEntitiesByCorpIdAndUnionId(TEST_CORP_ID, unionId);
        verifyNoMoreInteractions(crowdPackWriteDomainService);
    }

    @Test
    public void testInsertCrowdWhenPackVersionIsNull() throws Throwable {
        // arrange
        Long userId = TEST_USER_ID;
        String unionId = TEST_UNION_ID;
        String packVersion = null;
        // We need to mock the behavior for null packVersion
        when(crowdPackWriteDomainService.insertCrowdPackIfExist(TEST_PACK_ID, unionId, null, TEST_APP_ID)).thenReturn(false);
        when(appConfigRepository.getCorpIdByAppId(TEST_APP_ID)).thenReturn(TEST_CORP_ID);
        when(contactUserDomain.getContactUsersByCorpIdAndUnionId(TEST_CORP_ID, unionId)).thenReturn(Collections.emptyList());
        when(groupMemberDomainService.getMemberInfoEntitiesByCorpIdAndUnionId(TEST_CORP_ID, unionId)).thenReturn(Collections.emptyList());
        // act
        boolean result = (boolean) ReflectionTestUtils.invokeMethod(executeWriteDomainService, "insertCrowd", userId, unionId, TEST_PACK_ID, packVersion, TEST_APP_ID);
        // assert
        assertFalse(result);
        verify(crowdPackWriteDomainService).insertCrowdPackIfExist(TEST_PACK_ID, unionId, null, TEST_APP_ID);
        verify(appConfigRepository).getCorpIdByAppId(TEST_APP_ID);
        verify(contactUserDomain).getContactUsersByCorpIdAndUnionId(TEST_CORP_ID, unionId);
        verify(groupMemberDomainService).getMemberInfoEntitiesByCorpIdAndUnionId(TEST_CORP_ID, unionId);
        verifyNoMoreInteractions(crowdPackWriteDomainService);
    }
}
