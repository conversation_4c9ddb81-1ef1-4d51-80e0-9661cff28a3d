package com.sankuai.scrm.core.service.automatedmanagement.utils;

import com.sankuai.dz.srcm.automatedmanagement.dto.config.ScrmOperatorDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Method;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertFalse;

@ExtendWith(MockitoExtension.class)
class ConditionUtilsCheckIsFriendTest {

    @Mock
    private ExecuteManagementService executeManagementService;

    @InjectMocks
    private ConditionUtils conditionUtils;

    private boolean invokeCheckIsFriend(long filterFieldId, List<String> conditionParam, ScrmAmProcessOrchestrationExecuteLogDO executeLogDO, ScrmOperatorDTO operatorDTO, boolean tempResult) throws Exception {
        Method method = ConditionUtils.class.getDeclaredMethod("checkIsFriend", long.class, List.class, ScrmAmProcessOrchestrationExecuteLogDO.class, ScrmOperatorDTO.class, boolean.class);
        method.setAccessible(true);
        return (boolean) method.invoke(conditionUtils, filterFieldId, conditionParam, executeLogDO, operatorDTO, tempResult);
    }

    /**
     * Test when executorId doesn't exist in friend set should return false
     */
    @Test
    public void testCheckIsFriendWhenExecutorIsNotFriend() throws Throwable {
        // arrange
        long filterFieldId = 1L;
        List<String> conditionParam = Collections.emptyList();
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setExecutorId("staff123");
        executeLogDO.setTargetUnionId("union123");
        executeLogDO.setAppId("app123");
        ScrmOperatorDTO operatorDTO = new ScrmOperatorDTO();
        boolean tempResult = false;
        // act
        boolean result = invokeCheckIsFriend(filterFieldId, conditionParam, executeLogDO, operatorDTO, tempResult);
        // assert
        assertFalse(result);
    }

    /**
     * Test when executeLogDO is null should return false (handled gracefully)
     */
    @Test
    public void testCheckIsFriendWhenExecuteLogDOIsNull() throws Throwable {
        // arrange
        long filterFieldId = 1L;
        List<String> conditionParam = Collections.emptyList();
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = null;
        ScrmOperatorDTO operatorDTO = new ScrmOperatorDTO();
        boolean tempResult = false;
        // act
        boolean result = invokeCheckIsFriend(filterFieldId, conditionParam, executeLogDO, operatorDTO, tempResult);
        // assert
        assertFalse(result);
    }

    /**
     * Test when executorId is null should return false
     */
    @Test
    public void testCheckIsFriendWhenExecutorIdIsNull() throws Throwable {
        // arrange
        long filterFieldId = 1L;
        List<String> conditionParam = Collections.emptyList();
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setExecutorId(null);
        executeLogDO.setTargetUnionId("union123");
        executeLogDO.setAppId("app123");
        ScrmOperatorDTO operatorDTO = new ScrmOperatorDTO();
        boolean tempResult = false;
        // act
        boolean result = invokeCheckIsFriend(filterFieldId, conditionParam, executeLogDO, operatorDTO, tempResult);
        // assert
        assertFalse(result);
    }

    /**
     * Test when targetUnionId is null should return false
     */
    @Test
    public void testCheckIsFriendWhenTargetUnionIdIsNull() throws Throwable {
        // arrange
        long filterFieldId = 1L;
        List<String> conditionParam = Collections.emptyList();
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setExecutorId("staff123");
        executeLogDO.setTargetUnionId(null);
        executeLogDO.setAppId("app123");
        ScrmOperatorDTO operatorDTO = new ScrmOperatorDTO();
        boolean tempResult = false;
        // act
        boolean result = invokeCheckIsFriend(filterFieldId, conditionParam, executeLogDO, operatorDTO, tempResult);
        // assert
        assertFalse(result);
    }

    /**
     * Test when appId is null should return false
     */
    @Test
    public void testCheckIsFriendWhenAppIdIsNull() throws Throwable {
        // arrange
        long filterFieldId = 1L;
        List<String> conditionParam = Collections.emptyList();
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setExecutorId("staff123");
        executeLogDO.setTargetUnionId("union123");
        executeLogDO.setAppId(null);
        ScrmOperatorDTO operatorDTO = new ScrmOperatorDTO();
        boolean tempResult = false;
        // act
        boolean result = invokeCheckIsFriend(filterFieldId, conditionParam, executeLogDO, operatorDTO, tempResult);
        // assert
        assertFalse(result);
    }
}
