package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationContentSupplyTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.*;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.*;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.*;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.*;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.*;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.*;
import com.sankuai.scrm.core.service.message.push.dto.*;
import com.sankuai.scrm.core.service.message.push.enums.*;
import com.sankuai.scrm.core.service.message.push.request.*;
import com.sankuai.scrm.core.service.message.push.response.*;
import com.sankuai.scrm.core.service.message.push.service.*;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.math.BigDecimal;
import java.util.*;
import java.util.concurrent.atomic.AtomicInteger;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class DeepSeaWxHandlerDealRealTimeDeepSeaWxMessageV2Test {

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private MsgUnifiedPushService msgUnifiedPushService;

    @Mock
    private InformationGatheringService informationGatheringService;

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private ProductInfoService productInfoService;

    @InjectMocks
    private DeepSeaWxHandler deepSeaWxHandler;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private List<String> executorIds;

    private InvokeDetailKeyObject keyObject;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS;

    private ExecuteManagementDTO executeManagementDTO;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setAppId("test-app");
        processOrchestrationDTO.setValidVersion("1.0");
        executorIds = Arrays.asList("executor1");
        keyObject = new InvokeDetailKeyObject("test", (byte) 1, (byte) 1, 1L);
        totalInvokeDetailDOS = new ArrayList<>();
        executeManagementDTO = new ExecuteManagementDTO();
        executeManagementDTO.setPoiId("123");
        ScrmProcessOrchestrationNodeMediumDTO mediumDTO = mock(ScrmProcessOrchestrationNodeMediumDTO.class);
        processOrchestrationDTO.setNodeMediumDTO(mediumDTO);
    }

    @Test
    public void testDealRealTimeDeepSeaWxMessageV2MessageNotReadyToSend() throws Throwable {
        // Arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setId(1L);
        // Important: Set this to avoid NPE
        detail.setProcessOrchestrationNodeId(1L);
        // DEEP_SEA_SEND_FAILED status
        detail.setStatus((byte) 3);
        totalInvokeDetailDOS.add(detail);
        // Act
        deepSeaWxHandler.dealRealTimeDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, keyObject, totalInvokeDetailDOS, executeManagementDTO);
        // Assert
        verify(msgUnifiedPushService, never()).saveMsgPushTask(any());
        verify(wxInvokeLogDOMapper, never()).insertSelective(any());
    }

    @Test
    public void testDealRealTimeDeepSeaWxMessageV2WithEmptyDetails() throws Throwable {
        // Arrange
        totalInvokeDetailDOS = Collections.emptyList();
        // Act
        deepSeaWxHandler.dealRealTimeDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, keyObject, totalInvokeDetailDOS, executeManagementDTO);
        // Assert
        verifyNoInteractions(wxInvokeLogDOMapper, wxInvokeDetailDOMapper, executeLogDOMapper, msgUnifiedPushService);
    }

    @Test
    public void testDealRealTimeDeepSeaWxMessageV2WithNullDetails() throws Throwable {
        // Arrange
        totalInvokeDetailDOS = null;
        // Act
        deepSeaWxHandler.dealRealTimeDeepSeaWxMessageV2(processOrchestrationDTO, executorIds, keyObject, totalInvokeDetailDOS, executeManagementDTO);
        // Assert
        verifyNoInteractions(wxInvokeLogDOMapper, wxInvokeDetailDOMapper, executeLogDOMapper, msgUnifiedPushService);
    }
}
