package com.sankuai.scrm.core.service.infrastructure.acl.ds;

import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.AssistantInfo;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.service.fe.corp.wx.thrift.CorpWxService;
import com.sankuai.service.fe.corp.wx.thrift.GetCorpAssistantRequest;
import com.sankuai.service.fe.corp.wx.thrift.GetCorpAssistantResponse;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DsAssistantAclGetAssistantMobileByUserId1Test {

    @InjectMocks
    @Spy
    private DsAssistantAcl dsAssistantAcl;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private CorpWxService.Iface corpWxService;

    @Test
    public void testGetAssistantMobileByUserIdWhenAppIdOrAssistantIsNull() throws Throwable {
        assertNull(dsAssistantAcl.getAssistantMobileByUserId(null, "appId"));
        assertNull(dsAssistantAcl.getAssistantMobileByUserId("assistant", null));
    }

    @Test
    public void testGetAssistantMobileByUserIdWhenGetAssistantByAccountReturnsNull() throws Throwable {
        // Mock the dependencies to return null
        when(appConfigRepository.getConfigByAppId(anyString())).thenReturn(null);
        assertNull(dsAssistantAcl.getAssistantMobileByUserId("assistant", "appId"));
    }

    @Test
    public void testGetAssistantMobileByUserIdWhenGetAssistantByAccountReturnsNonNull() throws Throwable {
        // Mock CorpAppConfig
        CorpAppConfig mockCorpAppConfig = mock(CorpAppConfig.class);
        // Mock GetCorpAssistantResponse
        GetCorpAssistantResponse mockResponse = mock(GetCorpAssistantResponse.class);
        // Create AssistantInfo with mobile number
        AssistantInfo assistantInfo = new AssistantInfo();
        assistantInfo.setMobile("**********");
        assistantInfo.setAccountId("assistant");
        // Use doReturn for spy
        doReturn(assistantInfo).when(dsAssistantAcl).getAssistantByAccount("appId", "assistant");
        // Test the method
        String result = dsAssistantAcl.getAssistantMobileByUserId("assistant", "appId");
        assertEquals("**********", result);
    }
}
