package com.sankuai.scrm.core.service.couponIntegration.service.impl;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.couponIntegration.dto.CouponList;
import com.sankuai.dz.srcm.couponIntegration.dto.GetCouponListQuery;
import com.sankuai.dz.srcm.couponIntegration.dto.PageResult;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.StrategistCouponInfo;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.StrategistCouponInfoExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.StrategistCouponInfoMapper;
import com.sankuai.scrm.core.service.couponIntegration.utils.CouponIntegrationUtil;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Arrays;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class CouponIntegrationImplGetCouponListTest {

    @InjectMocks
    private CouponIntegrationImpl couponIntegration;

    @Mock
    private StrategistCouponInfoMapper strategistCouponInfoMapper;

    @Mock
    private CouponIntegrationUtil util;

    private GetCouponListQuery query;

    private List<StrategistCouponInfo> strategistCouponInfos;

    @Before
    public void setUp() {
        query = new GetCouponListQuery();
        query.setPageNo(1);
        query.setPageSize(10);
        strategistCouponInfos = Arrays.asList(new StrategistCouponInfo(), new StrategistCouponInfo());
        // Correctly mock the selectByExample method to return the expected list
        when(strategistCouponInfoMapper.selectByExample(any())).thenReturn(strategistCouponInfos);
        // Mock the util to return a valid example
        StrategistCouponInfoExample example = new StrategistCouponInfoExample();
        // Ensure the mock setup aligns with the actual method behavior
        when(util.getStrategistCouponInfoExample(any(GetCouponListQuery.class))).thenReturn(example);
        // Correctly mock the countByExample method to return the expected total count
        when(strategistCouponInfoMapper.countByExample(any())).thenReturn((long) strategistCouponInfos.size());
        // Mock the transferToCouponList method to return a list of CouponList objects
        when(util.transferToCouponList(strategistCouponInfos)).thenReturn(Arrays.asList(new CouponList(), new CouponList()));
    }

    /**
     * 测试正常场景
     */
    @Test
    public void testGetCouponListNormal() throws Throwable {
        RemoteResponse<PageResult> response = couponIntegration.getCouponList(query);
        assertEquals(strategistCouponInfos.size(), response.getData().getTotalCount().intValue());
        assertEquals(strategistCouponInfos.size(), response.getData().getCouponList().size());
    }

    /**
     * 测试边界场景
     */
    @Test
    public void testGetCouponListBoundary() throws Throwable {
        RemoteResponse<PageResult> response = couponIntegration.getCouponList(query);
        assertEquals(strategistCouponInfos.size(), response.getData().getTotalCount().intValue());
        assertEquals(strategistCouponInfos.size(), response.getData().getCouponList().size());
    }

    /**
     * 测试异常场景
     */
    @Test(expected = NullPointerException.class)
    public void testGetCouponListException() throws Throwable {
        couponIntegration.getCouponList(null);
    }
}
