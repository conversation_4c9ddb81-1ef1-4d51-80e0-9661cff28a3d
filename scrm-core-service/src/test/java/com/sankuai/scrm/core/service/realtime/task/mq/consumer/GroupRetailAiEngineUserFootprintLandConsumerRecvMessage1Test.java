package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.aigc.service.dal.entity.ScrmGroupRetailUserFootPrintRecordDO;
import com.sankuai.scrm.core.service.aigc.service.dal.example.ScrmGroupRetailUserFootPrintRecordDOExample;
import com.sankuai.scrm.core.service.aigc.service.dal.mapper.ScrmGroupRetailUserFootPrintRecordDOMapper;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineDispatchMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineMqMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.dto.ScrmDzBizDataUserTrackDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineDispatchProducer;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.apache.commons.lang3.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GroupRetailAiEngineUserFootprintLandConsumerRecvMessage1Test {

    @Mock
    private RealTimeTaskConsumerConfig consumerConfig;

    @Mock
    private GroupRetailAiEngineDispatchProducer dispatchProducer;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private ScrmGroupRetailUserFootPrintRecordDOMapper userFootPrintRecordDOMapper;

    @InjectMocks
    private GroupRetailAiEngineUserFootprintLandConsumer consumer;

    private MafkaMessage<String> message;

    private MessagetContext context;

    @BeforeEach
    void setUp() {
        message = new MafkaMessage<>("topic", 0, 0L, "key", "");
        context = new MessagetContext();
    }

    private ConsumeStatus invokeRecvMessage(MafkaMessage<String> message, MessagetContext context) throws Exception {
        Method method = GroupRetailAiEngineUserFootprintLandConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        method.setAccessible(true);
        return (ConsumeStatus) method.invoke(consumer, message, context);
    }

    /**
     * Test when message body is null
     */
    @Test
    public void testRecvMessage_NullMessageBody() throws Throwable {
        // arrange
        message = new MafkaMessage<>("topic", 0, 0L, "key", null);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test when message body cannot be parsed to DTO
     */
    @Test
    public void testRecvMessage_InvalidJsonMessage() throws Throwable {
        // arrange
        message = new MafkaMessage<>("topic", 0, 0L, "key", "invalid json");
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test when trackDTO is null
     */
    @Test
    public void testRecvMessage_NullTrackDTO() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO dto = new GroupRetailAiEngineMqMessageDTO();
        message = new MafkaMessage<>("topic", 0, 0L, "key", JsonUtils.toStr(dto));
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test order action type with valid appId
     */
    @Test
    public void testRecvMessage_OrderActionWithValidAppId() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("ord"));
        trackDTO.setTypelist(Collections.singletonList("product"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setItemidlist(Collections.singletonList(456L));
        GroupRetailAiEngineMqMessageDTO mqMessageDTO = new GroupRetailAiEngineMqMessageDTO();
        mqMessageDTO.setUserTrackDTO(trackDTO);
        mqMessageDTO.setValidAppid("testAppId");
        message = new MafkaMessage<>("topic", 0, 0L, "key", JsonUtils.toStr(mqMessageDTO));
        // ORDER code
        when(consumerConfig.getValidActionTypes()).thenReturn(Arrays.asList(9));
        when(userFootPrintRecordDOMapper.countByExample(any(ScrmGroupRetailUserFootPrintRecordDOExample.class))).thenReturn(0L);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper).insert(any(ScrmGroupRetailUserFootPrintRecordDO.class));
        verify(dispatchProducer).sendDelayMessage(any(GroupRetailAiEngineDispatchMessageDTO.class), anyLong());
    }

    /**
     * Test pay action type with empty validAppId but config provides one
     */
    @Test
    public void testRecvMessage_PayActionWithConfigAppId() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("pay"));
        trackDTO.setTypelist(Collections.singletonList("poi"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setShopidlist(Collections.singletonList(789L));
        GroupRetailAiEngineMqMessageDTO mqMessageDTO = new GroupRetailAiEngineMqMessageDTO();
        mqMessageDTO.setUserTrackDTO(trackDTO);
        mqMessageDTO.setValidAppid("");
        message = new MafkaMessage<>("topic", 0, 0L, "key", JsonUtils.toStr(mqMessageDTO));
        // PAY code
        when(consumerConfig.getValidActionTypes()).thenReturn(Arrays.asList(10));
        when(consumerConfig.getValidAppId(anyLong(), any())).thenReturn("configAppId");
        when(userFootPrintRecordDOMapper.countByExample(any(ScrmGroupRetailUserFootPrintRecordDOExample.class))).thenReturn(0L);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper).insert(any(ScrmGroupRetailUserFootPrintRecordDO.class));
        verify(dispatchProducer).sendDelayMessage(any(GroupRetailAiEngineDispatchMessageDTO.class), anyLong());
    }

    /**
     * Test view action with insertAllTrackSwitch enabled and user in whitelist
     */
    @Test
    public void testRecvMessage_ViewActionWithInsertAllSwitch() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("view"));
        trackDTO.setTypelist(Collections.singletonList("poi"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setShopidlist(Collections.singletonList(789L));
        GroupRetailAiEngineMqMessageDTO mqMessageDTO = new GroupRetailAiEngineMqMessageDTO();
        mqMessageDTO.setUserTrackDTO(trackDTO);
        mqMessageDTO.setValidAppid("testAppId");
        message = new MafkaMessage<>("topic", 0, 0L, "key", JsonUtils.toStr(mqMessageDTO));
        when(consumerConfig.getValidActionTypes()).thenReturn(Collections.emptyList());
        when(consumerConfig.getInsertAllTrackSwitch()).thenReturn(true);
        when(consumerConfig.isInWhitelistPure(123L)).thenReturn(true);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper).insert(any(ScrmGroupRetailUserFootPrintRecordDO.class));
    }

    /**
     * Test click product action with insertAllTrackSwitch enabled
     */
    @Test
    public void testRecvMessage_ClickProductActionWithInsertAllSwitch() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("click"));
        trackDTO.setTypelist(Collections.singletonList("product"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setItemidlist(Collections.singletonList(456L));
        GroupRetailAiEngineMqMessageDTO mqMessageDTO = new GroupRetailAiEngineMqMessageDTO();
        mqMessageDTO.setUserTrackDTO(trackDTO);
        mqMessageDTO.setValidAppid("testAppId");
        message = new MafkaMessage<>("topic", 0, 0L, "key", JsonUtils.toStr(mqMessageDTO));
        when(consumerConfig.getValidActionTypes()).thenReturn(Collections.emptyList());
        when(consumerConfig.getInsertAllTrackSwitch()).thenReturn(true);
        when(consumerConfig.isInWhitelistPure(123L)).thenReturn(true);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper).insert(any(ScrmGroupRetailUserFootPrintRecordDO.class));
    }

    /**
     * Test add to cart action with insertAllTrackSwitch enabled
     */
    @Test
    public void testRecvMessage_AddToCartActionWithInsertAllSwitch() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("act"));
        trackDTO.setTypelist(Collections.singletonList("product"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setItemidlist(Collections.singletonList(456L));
        GroupRetailAiEngineMqMessageDTO mqMessageDTO = new GroupRetailAiEngineMqMessageDTO();
        mqMessageDTO.setUserTrackDTO(trackDTO);
        mqMessageDTO.setValidAppid("testAppId");
        message = new MafkaMessage<>("topic", 0, 0L, "key", JsonUtils.toStr(mqMessageDTO));
        when(consumerConfig.getValidActionTypes()).thenReturn(Collections.emptyList());
        when(consumerConfig.getInsertAllTrackSwitch()).thenReturn(true);
        when(consumerConfig.isInWhitelistPure(123L)).thenReturn(true);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper).insert(any(ScrmGroupRetailUserFootPrintRecordDO.class));
    }

    /**
     * Test when no valid appId is available
     */
    @Test
    public void testRecvMessage_NoValidAppId() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("ord"));
        trackDTO.setTypelist(Collections.singletonList("product"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setItemidlist(Collections.singletonList(456L));
        GroupRetailAiEngineMqMessageDTO mqMessageDTO = new GroupRetailAiEngineMqMessageDTO();
        mqMessageDTO.setUserTrackDTO(trackDTO);
        mqMessageDTO.setValidAppid("");
        message = new MafkaMessage<>("topic", 0, 0L, "key", JsonUtils.toStr(mqMessageDTO));
        // ORDER code
        when(consumerConfig.getValidAppId(anyLong(), any())).thenReturn("");
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper, never()).insert(any());
        verify(dispatchProducer, never()).sendDelayMessage(any(), anyLong());
    }

    /**
     * Test when action type is not in valid types and insertAllSwitch is disabled
     */
    @Test
    public void testRecvMessage_InvalidActionTypeWithoutSwitch() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("view"));
        trackDTO.setTypelist(Collections.singletonList("poi"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setShopidlist(Collections.singletonList(789L));
        GroupRetailAiEngineMqMessageDTO mqMessageDTO = new GroupRetailAiEngineMqMessageDTO();
        mqMessageDTO.setUserTrackDTO(trackDTO);
        mqMessageDTO.setValidAppid("testAppId");
        message = new MafkaMessage<>("topic", 0, 0L, "key", JsonUtils.toStr(mqMessageDTO));
        // Only ORDER and PAY
        when(consumerConfig.getValidActionTypes()).thenReturn(Arrays.asList(9, 10));
        when(consumerConfig.getInsertAllTrackSwitch()).thenReturn(false);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper, never()).insert(any());
    }

    /**
     * Test when record already exists for PAY action
     */
    @Test
    public void testRecvMessage_PayActionWithExistingRecord() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("pay"));
        trackDTO.setTypelist(Collections.singletonList("poi"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setShopidlist(Collections.singletonList(789L));
        GroupRetailAiEngineMqMessageDTO mqMessageDTO = new GroupRetailAiEngineMqMessageDTO();
        mqMessageDTO.setUserTrackDTO(trackDTO);
        mqMessageDTO.setValidAppid("testAppId");
        message = new MafkaMessage<>("topic", 0, 0L, "key", JsonUtils.toStr(mqMessageDTO));
        // PAY code
        when(consumerConfig.getValidActionTypes()).thenReturn(Arrays.asList(10));
        when(userFootPrintRecordDOMapper.countByExample(any(ScrmGroupRetailUserFootPrintRecordDOExample.class))).thenReturn(1L);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper, never()).insert(any());
        verify(dispatchProducer).sendDelayMessage(any(GroupRetailAiEngineDispatchMessageDTO.class), anyLong());
    }

    /**
     * Test exception handling
     */
    @Test
    public void testRecvMessage_ExceptionHandling() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("ord"));
        trackDTO.setTypelist(Collections.singletonList("product"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setItemidlist(Collections.singletonList(456L));
        GroupRetailAiEngineMqMessageDTO mqMessageDTO = new GroupRetailAiEngineMqMessageDTO();
        mqMessageDTO.setUserTrackDTO(trackDTO);
        mqMessageDTO.setValidAppid("testAppId");
        message = new MafkaMessage<>("topic", 0, 0L, "key", JsonUtils.toStr(mqMessageDTO));
        // ORDER code
        when(consumerConfig.getValidActionTypes()).thenReturn(Arrays.asList(9));
        when(userFootPrintRecordDOMapper.countByExample(any(ScrmGroupRetailUserFootPrintRecordDOExample.class))).thenThrow(new RuntimeException("DB error"));
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test view shop action with insertAllTrackSwitch enabled
     */
    @Test
    public void testRecvMessage_ViewShopActionWithInsertAllSwitch() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("view"));
        trackDTO.setTypelist(Collections.singletonList("poi"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setShopidlist(Collections.singletonList(789L));
        GroupRetailAiEngineMqMessageDTO mqMessageDTO = new GroupRetailAiEngineMqMessageDTO();
        mqMessageDTO.setUserTrackDTO(trackDTO);
        mqMessageDTO.setValidAppid("testAppId");
        message = new MafkaMessage<>("topic", 0, 0L, "key", JsonUtils.toStr(mqMessageDTO));
        when(consumerConfig.getValidActionTypes()).thenReturn(Collections.emptyList());
        when(consumerConfig.getInsertAllTrackSwitch()).thenReturn(true);
        when(consumerConfig.isInWhitelistPure(123L)).thenReturn(true);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper).insert(any(ScrmGroupRetailUserFootPrintRecordDO.class));
    }

    /**
     * Test click shop action with insertAllTrackSwitch enabled
     */
    @Test
    public void testRecvMessage_ClickShopActionWithInsertAllSwitch() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("click"));
        trackDTO.setTypelist(Collections.singletonList("poi"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setShopidlist(Collections.singletonList(789L));
        GroupRetailAiEngineMqMessageDTO mqMessageDTO = new GroupRetailAiEngineMqMessageDTO();
        mqMessageDTO.setUserTrackDTO(trackDTO);
        mqMessageDTO.setValidAppid("testAppId");
        message = new MafkaMessage<>("topic", 0, 0L, "key", JsonUtils.toStr(mqMessageDTO));
        when(consumerConfig.getValidActionTypes()).thenReturn(Collections.emptyList());
        when(consumerConfig.getInsertAllTrackSwitch()).thenReturn(true);
        when(consumerConfig.isInWhitelistPure(123L)).thenReturn(true);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper).insert(any(ScrmGroupRetailUserFootPrintRecordDO.class));
    }
}
