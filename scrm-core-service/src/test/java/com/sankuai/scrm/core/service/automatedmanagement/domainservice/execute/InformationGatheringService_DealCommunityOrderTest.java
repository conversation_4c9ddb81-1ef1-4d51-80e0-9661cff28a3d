package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.dianping.pay.order.service.query.dto.UnifiedOrderWithId;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationActionTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationContentSupplyTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationContentTypeEnum;
import com.sankuai.scrm.core.service.aigc.service.SupplyMarketingTextService;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationBranchExeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationDistributorCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.*;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.order.UnifiedOrderAclService;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.technician.trade.api.order.enums.OrderDistributionTypeEnum;
import com.sankuai.technician.trade.api.order.message.OrderOperateNotify;
import org.assertj.core.util.Lists;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class InformationGatheringService_DealCommunityOrderTest {

    String processOrchestrationMocks = "{\n" + "  \"id\" : 192,\n" + "  \"name\" : \"自动推品+全部商品+团购+群\",\n"
            + "  \"processOrchestrationType\" : 4,\n" + "  \"cron\" : \"\",\n"
            + "  \"beginTime\" : 1725702000000,\n" + "  \"endTime\" : 1725874800000,\n" + "  \"status\" : 1,\n"
            + "  \"validVersion\" : \"1725788339717\",\n" + "  \"updateTime\" : 1725788339000,\n"
            + "  \"creatorId\" : \"wangxuefei05\",\n" + "  \"lastUpdaterId\" : \"wangxuefei05\",\n"
            + "  \"participationRestrict\" : true,\n" + "  \"participationRestrictionsCycle\" : 0,\n"
            + "  \"participationRestrictionsTimes\" : 0,\n" + "  \"appId\" : \"yimei\",\n"
            + "  \"previewPic\" : \"\",\n" + "  \"cronComment\" : \"2024-09-08 17:40:00\",\n"
            + "  \"executorType\" : 1,\n" + "  \"executorList\" : [ {\n" + "    \"executorId\" : \"WangXueFei\",\n"
            + "    \"executorName\" : \"王雪飞\",\n" + "    \"executorType\" : 2\n" + "  } ],\n"
            + "  \"crowdPackType\" : 3,\n" + "  \"crowdPackIdList\" : [ ],\n"
            + "  \"groupIdList\" : [ \"wrb_61EQAACdwDGbhFsGznPHZMX3i3_A\" ],\n" + "  \"groupInfoList\" : [ {\n"
            + "    \"groupName\" : \"军师测试25\",\n" + "    \"owner\" : \"WangXueFei\",\n"
            + "    \"robotList\" : null,\n" + "    \"groupId\" : \"wrb_61EQAACdwDGbhFsGznPHZMX3i3_A\",\n"
            + "    \"createDate\" : 1725788266000,\n" + "    \"memberCount\" : 3\n" + "  } ],\n"
            + "  \"crowdPackUpdateStrategyInfoDTO\" : null,\n" + "  \"goalDTO\" : {\n" + "    \"id\" : 554,\n"
            + "    \"checkTime\" : \"3\",\n" + "    \"checkTimeUnit\" : \"2\",\n" + "    \"status\" : 1,\n"
            + "    \"processOrchestrationId\" : 192,\n"
            + "    \"processOrchestrationVersion\" : \"1725788339717\",\n" + "    \"goalType\" : 1,\n"
            + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003 ],\n"
            + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n"
            + "  \"negativeGoalDTO\" : {\n" + "    \"id\" : 553,\n" + "    \"checkTime\" : \"3\",\n"
            + "    \"checkTimeUnit\" : \"2\",\n" + "    \"status\" : 1,\n"
            + "    \"processOrchestrationId\" : 192,\n"
            + "    \"processOrchestrationVersion\" : \"1725788339717\",\n" + "    \"goalType\" : 2,\n"
            + "    \"careNegativeResult\" : true,\n" + "    \"positiveResultHighlightList\" : [ 90002, 90003 ],\n"
            + "    \"negativeResultHighlightList\" : null,\n" + "    \"goalConditionList\" : [ ]\n" + "  },\n"
            + "  \"nodeMediumDTO\" : {\n" + "    \"processOrchestrationNodeDTOList\" : [ {\n"
            + "      \"nodeId\" : 0,\n" + "      \"preNodeId\" : -1,\n" + "      \"nodeType\" : 4,\n"
            + "      \"id\" : 750,\n" + "      \"processOrchestrationId\" : 192,\n"
            + "      \"processOrchestrationVersion\" : \"1725788339717\",\n"
            + "      \"childrenNodes\" : [ 1725778802191 ]\n" + "    }, {\n" + "      \"nodeId\" : 1725778802191,\n"
            + "      \"preNodeId\" : 0,\n" + "      \"nodeType\" : 2,\n" + "      \"id\" : 751,\n"
            + "      \"processOrchestrationId\" : 192,\n"
            + "      \"processOrchestrationVersion\" : \"1725788339717\",\n" + "      \"childrenNodes\" : [ ]\n"
            + "    } ],\n" + "    \"conditionMap\" : { },\n" + "    \"actionMap\" : {\n"
            + "      \"1725778802191\" : {\n" + "        \"id\" : 343,\n" + "        \"actionId\" : 1,\n"
            + "        \"processOrchestrationId\" : 192,\n"
            + "        \"processOrchestrationVersion\" : \"1725788339717\",\n"
            + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"actionType\" : 7,\n"
            + "        \"actionSubType\" : 5,\n" + "        \"updateTime\" : 1725788339000,\n"
            + "        \"contentType\" : 1,\n" + "        \"contentList\" : null\n" + "      }\n" + "    },\n"
            + "    \"actionContentMap\" : {\n" + "      \"1725778802191-1\" : [ {\n" + "        \"id\" : 342,\n"
            + "        \"actionId\" : 1,\n" + "        \"contentId\" : 1,\n"
            + "        \"processOrchestrationId\" : 192,\n"
            + "        \"processOrchestrationVersion\" : \"1725788339717\",\n"
            + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"content\" : \"\",\n"
            + "        \"updateTime\" : 1725788339000,\n" + "        \"contentType\" : 1,\n"
            + "        \"attachmentDTOList\" : null\n" + "      } ]\n" + "    },\n"
            + "    \"actionAttachmentMap\" : {\n" + "      \"1725778802191-1-1\" : [ {\n"
            + "        \"id\" : 422,\n" + "        \"contentId\" : 1,\n" + "        \"actionId\" : 1,\n"
            + "        \"attachmentTypeId\" : 7,\n"
            + "        \"attachmentContent\" : \"{\\\"supplyType\\\":2,\\\"productType\\\":1,\\\"shelfName\\\":\\\"\\\",\\\"jumpPageType\\\":\\\"\\\",\\\"jumpUrl\\\":\\\"\\\",\\\"productId\\\":\\\"738067238,753099031,427691503\\\",\\\"supplyScope\\\":\\\"\\\",\\\"marketingCopy\\\":\\\"手动推品+团购\\\",\\\"marketingCopySource\\\":2,\\\"hotTagList\\\":\\\"\\\",\\\"topproductids\\\":\\\"\\\",\\\"headpicUrl\\\":\\\"\\\"}\",\n"
            + "        \"processOrchestrationId\" : 192,\n"
            + "        \"processOrchestrationVersion\" : \"1725788339717\",\n"
            + "        \"processOrchestrationNodeId\" : 1725778802191,\n" + "        \"updateTime\" : null,\n"
            + "        \"attachmentSupplyDetailDTO\" : {\n" + "          \"supplyType\" : 2,\n"
            + "          \"productId\" : \"738067238,753099031,427691503\",\n" + "          \"productType\" : 1,\n"
            + "          \"marketingCopy\" : \"手动推品+团购\",\n" + "          \"marketingCopySource\" : 2,\n"
            + "          \"supplyScope\" : null,\n" + "          \"hotTagList\" : \"\",\n"
            + "          \"shelfName\" : \"\",\n" + "          \"jumpPageType\" : null,\n"
            + "          \"jumpUrl\" : \"\",\n" + "          \"topproductids\" : \"\",\n"
            + "          \"headpicUrl\" : \"\"\n" + "        },\n"
            + "        \"attachmentContentDetailDTO\" : null\n" + "      } ]\n" + "    }\n" + "  },\n"
            + "  \"executePlanDTO\" : null\n" + "}";

    @InjectMocks
    private InformationGatheringService informationGatheringService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationDistributorCodeDOMapper distributorCodeDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper invokeDetailDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteGoalDetailDOMapper executeGoalDetailDOMapper;

    @Mock(lenient = true)
    private UnifiedOrderAclService unifiedOrderAclService;

    @Mock(lenient = true)
    private SupplyMarketingTextService supplyMarketingTextService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationRelatedOrderInfoDOMapper relatedOrderInfoDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationBranchExeLogDOMapper branchExeLogDOMapper;

    @Mock(lenient = true)
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;


    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testDealCommunityOrderWithNullOperateNotify() throws Throwable {
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(null);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void testDealCommunityOrderWithNonCommunityChannel() throws Throwable {
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void testDealCommunityOrderWithNonMatchingOperateType() throws Throwable {
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        operateNotify.setOperateType(2);
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void testDealCommunityOrderWithEmptyExtInfo() throws Throwable {
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        operateNotify.setOperateType(1);
        operateNotify.setExtInfo(new HashMap<>());
        when(relatedOrderInfoDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(relatedOrderInfoDOMapper.insert(any())).thenReturn(1);
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void testDealCommunityOrderWithEmptyDistributorCode() throws Throwable {
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        operateNotify.setOperateType(1);
        Map<String, String> extInfo = new HashMap<>();
        extInfo.put("distributorId", "");
        operateNotify.setExtInfo(extInfo);
        when(relatedOrderInfoDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(relatedOrderInfoDOMapper.insert(any())).thenReturn(1);
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    @Test
    public void testDealCommunityOrderWithNoMatchingDistributorCode() throws Throwable {
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        operateNotify.setOperateType(1);
        Map<String, String> extInfo = new HashMap<>();
        extInfo.put("distributorId", "10000");
        operateNotify.setExtInfo(extInfo);
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(relatedOrderInfoDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(relatedOrderInfoDOMapper.insert(any())).thenReturn(1);
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(distributorCodeDOMapper, atLeastOnce()).selectByExample(any());
    }

    @Test
    public void testDealCommunityOrderWithNoMatchingExecuteLog() throws Throwable {
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        operateNotify.setOperateType(1);
        Map<String, String> extInfo = new HashMap<>();
        extInfo.put("distributorId", "10000");
        operateNotify.setExtInfo(extInfo);
        operateNotify.setOrderId("testOrderId");
        ScrmAmProcessOrchestrationDistributorCodeDO distributorCodeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        distributorCodeDO.setProcessOrchestrationId(1L);
        distributorCodeDO.setProcessOrchestrationNodeId(2L);
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Arrays.asList(distributorCodeDO));
        UnifiedOrderWithId unifiedOrderWithId = new UnifiedOrderWithId();
        unifiedOrderWithId.setUnifiedOrderId("testOrderId");
        when(unifiedOrderAclService.getOrderInfo(anyString())).thenReturn(unifiedOrderWithId);
        when(executeLogDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(relatedOrderInfoDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(relatedOrderInfoDOMapper.insert(any())).thenReturn(1);
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(distributorCodeDOMapper, atLeastOnce()).selectByExample(any());
        verify(unifiedOrderAclService, times(1)).getOrderInfo(anyString());
        verify(executeLogDOMapper, atLeastOnce()).selectByExample(any());
    }

    @Test
    public void testDealCommunityOrderSuccessful() throws Throwable {
        OrderOperateNotify operateNotify = new OrderOperateNotify();
        operateNotify.setChannel(OrderDistributionTypeEnum.COMMUNITY.getCode());
        operateNotify.setOperateType(1);
        Map<String, String> extInfo = new HashMap<>();
        extInfo.put("distributorId", "10000");
        operateNotify.setExtInfo(extInfo);
        operateNotify.setOrderId("testOrderId");
        ScrmAmProcessOrchestrationDistributorCodeDO distributorCodeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        distributorCodeDO.setProcessOrchestrationId(1L);
        distributorCodeDO.setProcessOrchestrationNodeId(2L);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(3L);
        executeLogDO.setTargetUnionId("targetUnionId");
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO.setId(1L);
        invokeDetailDO.setProcessOrchestrationId(1L);
        invokeDetailDO.setExecuteLogId(3L);
        invokeDetailDO.setProcessOrchestrationNodeId(1L);
        invokeDetailDO.setProcessOrchestrationVersion("1.0");
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> invokeDetailDOS = Arrays.asList(invokeDetailDO);
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Arrays.asList(distributorCodeDO));
        com.dianping.pay.order.service.query.dto.UnifiedOrderWithId orderWithId = new com.dianping.pay.order.service.query.dto.UnifiedOrderWithId();
        orderWithId.setLongOrderId(1L);
        orderWithId.setUnifiedOrderId("orderId");
        orderWithId.setOrderId(1);
        orderWithId.setMtUserId(1L);

        ScrmAmProcessOrchestrationBranchExeLogDO branchExeLogDO = new ScrmAmProcessOrchestrationBranchExeLogDO();
        branchExeLogDO.setNodeId(1L);
        when(branchExeLogDOMapper.selectByExample(any())).thenReturn(Lists.newArrayList(branchExeLogDO));

        when(branchExeLogDOMapper.updateByPrimaryKey(any())).thenReturn(1);

        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setNodeId(1L);
        CompletableFuture<List<ScrmProcessOrchestrationNodeDTO>> superSplitBranchNodeFuture = CompletableFuture.completedFuture(Lists.newArrayList(nodeDTO));
        when(processOrchestrationReadDomainService.querySuperConditionAndSplitBranchNodeFuture(anyLong(), anyString(), anyLong())).thenReturn(superSplitBranchNodeFuture);
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JsonUtils.toObject(processOrchestrationMocks,
                                                                                 ScrmProcessOrchestrationDTO.class);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(anyLong())).thenReturn(CompletableFuture.completedFuture(processOrchestrationDTO));
        when(unifiedOrderAclService.getOrderInfo(anyString())).thenReturn(orderWithId);
        when(executeLogDOMapper.selectByExample(any())).thenReturn(Arrays.asList(executeLogDO));
        when(invokeDetailDOMapper.selectByExample(any())).thenReturn(invokeDetailDOS);
        when(executeGoalDetailDOMapper.batchInsert(any())).thenReturn(1);
        when(relatedOrderInfoDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(relatedOrderInfoDOMapper.insert(any())).thenReturn(1);
        doNothing().when(supplyMarketingTextService).batchRecycleUserTag(any(), any());
        ConsumeStatus result = informationGatheringService.dealCommunityOrder(operateNotify);
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * 测试当 actionDTO 为 null 时，方法不执行任何操作
     */
    @Test
    public void testDealNoInvokeDetailLogRequestWithNullActionDTO() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO =  JsonUtils.toObject(processOrchestrationMocks,
                ScrmProcessOrchestrationDTO.class);
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();

        boolean result = informationGatheringService.dealNoInvokeDetailLogRequest(processOrchestrationDTO, nodeDTO, executeLogDO);

        // 断言方法返回 false，因为没有执行任何操作
        assertFalse("Expected dealNoInvokeDetailLogRequest to return false", result);
    }

    /**
     * 测试当 actionDTO 不为 null 且 actionType 为 COUPON_DISTRIBUTION 时，方法执行并返回 true
     */
    @Test
    public void testDealNoInvokeDetailLogRequestWithCouponDistribution() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO =  JsonUtils.toObject(processOrchestrationMocks,
                ScrmProcessOrchestrationDTO.class);
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setNodeId(1111L);
        nodeDTO.setProcessOrchestrationId(processOrchestrationDTO.getId());
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setActionType(ScrmProcessOrchestrationActionTypeEnum.COUPON_DISTRIBUTION.getValue().byteValue());

        processOrchestrationDTO.getNodeMediumDTO().addActionDTO(nodeDTO.getNodeId(), actionDTO);

        boolean result = informationGatheringService.dealNoInvokeDetailLogRequest(processOrchestrationDTO, nodeDTO, executeLogDO);

        // 断言方法返回 true，因为执行了操作
        assertTrue("Expected dealNoInvokeDetailLogRequest to return true", result);
    }

    /**
     * 测试当 actionDTO 不为 null 且 actionType 不是 COUPON_DISTRIBUTION 且 contentType 是 SUPPLY 且 supplyType 是 COUPON_PROMOTION 时，方法执行并返回 true
     */
    @Test
    public void testDealNoInvokeDetailLogRequestWithSupplyAndCouponPromotion() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO =  JsonUtils.toObject(processOrchestrationMocks,
                ScrmProcessOrchestrationDTO.class);
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setNodeId(1111L);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setId(1L);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setActionType((byte) 2); // 非 COUPON_DISTRIBUTION 类型
        actionDTO.setContentType(ScrmProcessOrchestrationContentTypeEnum.SUPPLY.getValue());
        ScrmProcessOrchestrationActionContentDTO actionContentDTO = new ScrmProcessOrchestrationActionContentDTO();
        actionContentDTO.setId(1111L);

        nodeDTO.setProcessOrchestrationId(processOrchestrationDTO.getId());
        processOrchestrationDTO.getNodeMediumDTO().addActionDTO(nodeDTO.getNodeId(), actionDTO);
        processOrchestrationDTO.getNodeMediumDTO().addActionContentDTOList(actionDTO, Collections.singletonList(actionContentDTO));
        processOrchestrationDTO.getNodeMediumDTO().addActionAttachmentDTOList(actionContentDTO,Collections.singletonList(new ScrmProcessOrchestrationActionAttachmentDTO() {{
            setAttachmentSupplyDetailDTO(new ScrmProcessOrchestrationAttachmentSupplyDetailDTO() {{
                setSupplyType(ScrmProcessOrchestrationContentSupplyTypeEnum.COUPON_PROMOTION.getValue());
            }});
        }}));

        boolean result = informationGatheringService.dealNoInvokeDetailLogRequest(processOrchestrationDTO, nodeDTO, executeLogDO);

        // 断言方法返回 true，因为执行了操作
        assertTrue("Expected dealNoInvokeDetailLogRequest to return true", result);
    }

    /**
     * 测试当 actionDTO 不为 null 且 actionType 不是 COUPON_DISTRIBUTION 且 contentType 不是 SUPPLY 时，方法执行并返回 false
     */
    @Test
    public void testDealNoInvokeDetailLogRequestWithNonSupplyContentType() {
        ScrmProcessOrchestrationDTO processOrchestrationDTO =  JsonUtils.toObject(processOrchestrationMocks,
                ScrmProcessOrchestrationDTO.class);
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setNodeId(1111L);
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setActionType((byte) 2); // 非 COUPON_DISTRIBUTION 类型
        actionDTO.setContentType(99); // 非 SUPPLY 类型

        processOrchestrationDTO.getNodeMediumDTO().addActionDTO(nodeDTO.getNodeId(), actionDTO);

        boolean result = informationGatheringService.dealNoInvokeDetailLogRequest(processOrchestrationDTO, nodeDTO, executeLogDO);

        // 断言方法返回 false，因为没有执行任何操作
        assertFalse("Expected dealNoInvokeDetailLogRequest to return false", result);
    }
}
