package com.sankuai.scrm.core.service.activity.miniprogram.crane;

import com.google.common.collect.Lists;
import com.google.common.collect.Maps;
import com.sankuai.scrm.core.service.activity.miniprogram.bo.LotteryActivityBO;
import com.sankuai.scrm.core.service.activity.miniprogram.bo.LotteryDetailBO;
import com.sankuai.scrm.core.service.activity.miniprogram.domain.LotteryDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.order.dal.entity.OrderInfo;
import com.sankuai.scrm.core.service.order.domain.OrderDomainService;
import org.apache.commons.lang3.time.DateUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.Date;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class LotteryActivityJobTest {

    @Mock
    private LotteryDomainService lotteryDomainService;

    @Mock
    private OrderDomainService orderDomainService;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Spy
    @InjectMocks
    private LotteryActivityJob lotteryActivityJob;

    private LotteryActivityBO lotteryActivityBO;

    private OrderInfo orderInfo;

    @Before
    public void setUp() {
        Date now = new Date();
        LotteryDetailBO lotteryDetailBO = new LotteryDetailBO();
        lotteryDetailBO.setLotteryNo("001");
        lotteryDetailBO.setLotteryTime(now);
        lotteryDetailBO.setWinningMtUserIdList(Lists.newArrayList());
        lotteryDetailBO.setAwardCount(1);
        lotteryActivityBO = new LotteryActivityBO();
        lotteryActivityBO.setActivityId(1);
        lotteryActivityBO.setStartTime(DateUtils.addHours(now, -1));
        lotteryActivityBO.setEndTime(DateUtils.addHours(now, 1));
        lotteryActivityBO.setDetailList(Lists.newArrayList(lotteryDetailBO));
        lotteryActivityBO.setSkuIdList(Lists.newArrayList("sku1"));
        orderInfo = new OrderInfo();
        orderInfo.setMtUserId(123456L);
        orderInfo.setHasRefund(false);
        orderInfo.setMobileNo("mobileNo");
    }

    /**
     * 测试doActivityLottery方法，当没有有效的活动时
     */
    @Test
    public void testDoActivityLotteryNoValidActivity() {
        // arrange
        when(lotteryDomainService.queryLotteryActivityList()).thenReturn(Lists.newArrayList(lotteryActivityBO));
        when(lotteryDomainService.queryAwardStatisticList(anyInt())).thenReturn(Lists.newArrayList());
        when(lotteryDomainService.queryAwardRecord(anyInt())).thenReturn(Lists.newArrayList());
        when(lotteryDomainService.insertAwardStatistic(any())).thenReturn(true);
        when(lotteryDomainService.batchInsertAwardRecord(anyList())).thenReturn(true);
        when(orderDomainService.queryOrderList(anyList(), any(), any())).thenReturn(Lists.newArrayList(orderInfo));
        when(orderDomainService.queryOrderList(anyList())).thenReturn(Lists.newArrayList(orderInfo));
        when(mtUserCenterAclService.getThirdUserInfo(anyList(), anyString())).thenReturn(Maps.newHashMap());
        // act
        lotteryActivityJob.doActivityLottery();

        // assert
        verify(lotteryActivityJob, times(1)).doActivityLottery();
    }

}
