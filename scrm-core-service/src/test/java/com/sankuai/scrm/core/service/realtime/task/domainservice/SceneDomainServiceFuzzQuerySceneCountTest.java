package com.sankuai.scrm.core.service.realtime.task.domainservice;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.realtime.task.dto.RealtimePerceptionMessage;
import com.sankuai.dz.srcm.realtime.task.request.SceneAddRequest;
import com.sankuai.dz.srcm.realtime.task.request.SceneDeleteRequest;
import com.sankuai.dz.srcm.realtime.task.request.SceneQueryRequest;
import com.sankuai.dz.srcm.realtime.task.response.SceneAddResultVO;
import com.sankuai.dz.srcm.realtime.task.response.SceneDeleteResultVO;
import com.sankuai.dz.srcm.realtime.task.response.SceneQueryResultVO;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmRealtimeSceneDO;
import com.sankuai.scrm.core.service.realtime.task.dal.example.ScrmRealtimeSceneDOExample;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmRealtimeSceneDOMapper;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmRealtimeSceneUserRecordDOMapper;
import com.sankuai.scrm.core.service.realtime.task.domainservice.SceneDomainService;
import java.util.ArrayList;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class SceneDomainServiceFuzzQuerySceneCountTest {

    @InjectMocks
    private SceneDomainService sceneDomainService;

    @Mock
    private ScrmRealtimeSceneDOMapper scrmRealtimeSceneDOMapper;

    @Mock
    private ScrmRealtimeSceneUserRecordDOMapper scrmRealtimeSceneUserRecordDOMapper;

    @Test
    public void testFuzzQuerySceneCountPageSizeIsZero() throws Throwable {
        SceneQueryRequest request = new SceneQueryRequest();
        request.setPageSize(0);
        Long result = sceneDomainService.fuzzQuerySceneCount(request);
        assertEquals(Long.valueOf(0), result);
    }

    @Test
    public void testFuzzQuerySceneCountPageNumIsNull() throws Throwable {
        SceneQueryRequest request = new SceneQueryRequest();
        request.setPageSize(10);
        Long result = sceneDomainService.fuzzQuerySceneCount(request);
        assertEquals(Long.valueOf(0), result);
    }

    @Test
    public void testFuzzQuerySceneCountPageNumIsZero() throws Throwable {
        SceneQueryRequest request = new SceneQueryRequest();
        request.setPageSize(10);
        request.setPageNum(0);
        Long result = sceneDomainService.fuzzQuerySceneCount(request);
        assertEquals(Long.valueOf(0), result);
    }

    @Test
    public void testFuzzQuerySceneCountAppIdIsEmpty() throws Throwable {
        SceneQueryRequest request = new SceneQueryRequest();
        request.setPageSize(10);
        request.setPageNum(1);
        request.setAppId("");
        Long result = sceneDomainService.fuzzQuerySceneCount(request);
        assertEquals(Long.valueOf(0), result);
    }

    @Test
    public void testFuzzQuerySceneCountValidAppId() throws Throwable {
        SceneQueryRequest request = new SceneQueryRequest();
        request.setPageSize(10);
        request.setPageNum(1);
        request.setAppId("validAppId");
        when(scrmRealtimeSceneDOMapper.countByExample(any(ScrmRealtimeSceneDOExample.class))).thenReturn(10L);
        Long result = sceneDomainService.fuzzQuerySceneCount(request);
        assertEquals(Long.valueOf(10), result);
    }

    @Test
    public void testPageQuerySceneListPageSizeIsNull() throws Throwable {
        SceneQueryRequest request = new SceneQueryRequest();
        request.setPageSize(null);
        List<SceneQueryResultVO> result = sceneDomainService.pageQuerySceneList(request);
        assertNull(result);
    }

    @Test
    public void testPageQuerySceneListPageSizeIsZero() throws Throwable {
        SceneQueryRequest request = new SceneQueryRequest();
        request.setPageSize(0);
        List<SceneQueryResultVO> result = sceneDomainService.pageQuerySceneList(request);
        assertNull(result);
    }

    @Test
    public void testPageQuerySceneListPageNumIsNull() throws Throwable {
        SceneQueryRequest request = new SceneQueryRequest();
        request.setPageNum(null);
        List<SceneQueryResultVO> result = sceneDomainService.pageQuerySceneList(request);
        assertNull(result);
    }

    @Test
    public void testPageQuerySceneListPageNumIsZero() throws Throwable {
        SceneQueryRequest request = new SceneQueryRequest();
        request.setPageNum(0);
        List<SceneQueryResultVO> result = sceneDomainService.pageQuerySceneList(request);
        assertNull(result);
    }

    @Test
    public void testPageQuerySceneListAppIdIsNull() throws Throwable {
        SceneQueryRequest request = new SceneQueryRequest();
        request.setAppId(null);
        List<SceneQueryResultVO> result = sceneDomainService.pageQuerySceneList(request);
        assertNull(result);
    }

    @Test
    public void testPageQuerySceneListResultIsEmpty() throws Throwable {
        SceneQueryRequest request = new SceneQueryRequest();
        request.setAppId("appId");
        request.setPageSize(10);
        request.setPageNum(1);
        when(scrmRealtimeSceneDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        List<SceneQueryResultVO> result = sceneDomainService.pageQuerySceneList(request);
        assertTrue(result.isEmpty());
    }
    @Ignore
    @Test
    public void testPageQuerySceneListResultIsNotEmpty() throws Throwable {
        SceneQueryRequest request = new SceneQueryRequest();
        request.setAppId("appId");
        request.setPageSize(10);
        request.setPageNum(1);
        List<ScrmRealtimeSceneDO> scrmRealtimeSceneDOList = new ArrayList<>();
        // Mocking ScrmRealtimeSceneDO object
        ScrmRealtimeSceneDO sceneDO = mock(ScrmRealtimeSceneDO.class);
        // Assuming sceneId is 123
        when(sceneDO.getSceneid()).thenReturn(123);
        scrmRealtimeSceneDOList.add(sceneDO);
        when(scrmRealtimeSceneDOMapper.selectByExample(any())).thenReturn(scrmRealtimeSceneDOList);
        // Mock the behavior of scrmRealtimeSceneUserRecordDOMapper to return a valid Long value
        // Assuming 42 is a valid count
        when(scrmRealtimeSceneUserRecordDOMapper.countByExample(any())).thenReturn(42L);
        List<SceneQueryResultVO> result = sceneDomainService.pageQuerySceneList(request);
        assertFalse(result.isEmpty());
        // Additional assertions can be added here to verify the properties of the SceneQueryResultVO objects
    }
}
