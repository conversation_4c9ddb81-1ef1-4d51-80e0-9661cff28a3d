package com.sankuai.scrm.core.service.dashboard.crane;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmCouponOrderAmountDetail;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmCouponOrderAmountDetailExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmCouponOrderAmountDetailMapper;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import com.sankuai.scrm.core.service.dashboard.dal.dto.EsNarrowlyTransactionDataSnapshot;
import com.sankuai.scrm.core.service.dashboard.dal.enums.TransactionGtvQueryFieldEnum;
import com.sankuai.scrm.core.service.dashboard.domain.DashBoardESWriteDomainService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import java.lang.reflect.Method;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import static org.mockito.ArgumentMatchers.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationInformationGatheringEnum;
import com.sankuai.dz.srcm.couponIntegration.enums.StatusEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteGoalDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationRelatedOrderInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteGoalDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationRelatedOrderInfoDOMapper;
import com.sankuai.scrm.core.service.dashboard.constants.DashBoardESIndexConstant;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class TransactionDataTaskProcessCorpAppConfigNarrowlyTest {

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Mock
    private DashBoardESWriteDomainService dashBoardESWriteDomainService;

    @Mock
    private ScrmCouponOrderAmountDetailMapper scrmCouponOrderAmountDetailMapper;

    @Mock
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordsMapper;

    @InjectMocks
    private TransactionDataTask transactionDataTask;

    private final LocalDate testDate = LocalDate.of(2023, 1, 1);

    private final String testCorpId = "testCorpId";

    private final String testAppId = "testAppId";

    private static final String NARROW_TRADE_INDEX = "corpwx_narrowly_trade_data_dashboard_order_statistics_snapshot_new";

    @Mock
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecuteGoalDetailDOMapper scrmAmProcessOrchestrationExecuteGoalDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationRelatedOrderInfoDOMapper scrmAmProcessOrchestrationRelatedOrderInfoDOMapper;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    private void invokeProcessCorpAppConfigNarrowly(CorpAppConfig config, LocalDate date) throws Exception {
        Method method = TransactionDataTask.class.getDeclaredMethod("processCorpAppConfigNarrowly", CorpAppConfig.class, LocalDate.class);
        method.setAccessible(true);
        method.invoke(transactionDataTask, config, date);
    }

    /**
     * Tests processing when no coupon records are found
     */
    @Test
    public void testProcessCorpAppConfigNarrowlyNoRecords() throws Throwable {
        // arrange
        CorpAppConfig config = new CorpAppConfig();
        config.setCorpId(testCorpId);
        config.setAppId(testAppId);
        when(scrmSceneCouponRecordsMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(Collections.emptyList());
        // act
        invokeProcessCorpAppConfigNarrowly(config, testDate);
        // assert
        verify(scrmSceneCouponRecordsMapper).selectByExample(any(ScrmSceneCouponRecordsExample.class));
        verify(scrmCouponOrderAmountDetailMapper, never()).selectByExample(any(ScrmCouponOrderAmountDetailExample.class));
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(anyString(), argThat(snapshot -> {
            EsNarrowlyTransactionDataSnapshot esSnapshot = (EsNarrowlyTransactionDataSnapshot) snapshot;
            return "0".equals(esSnapshot.getGtv()) && "0".equals(esSnapshot.getActualGtv()) && esSnapshot.getTransactionOrderCount() == 0 && esSnapshot.getNarrowUserCount() == 0;
        }), anyString());
    }

    /**
     * Tests processing with single coupon record and order amount details
     */
    @Test
    public void testProcessCorpAppConfigNarrowlyWithSingleRecord() throws Throwable {
        // arrange
        CorpAppConfig config = new CorpAppConfig();
        config.setCorpId(testCorpId);
        config.setAppId(testAppId);
        ScrmSceneCouponRecords record = new ScrmSceneCouponRecords();
        record.setCoupongroupid("group1");
        record.setUnifiedcouponid("coupon1");
        record.setOrderid("order1");
        record.setUserid(1001L);
        ScrmCouponOrderAmountDetail detail = new ScrmCouponOrderAmountDetail();
        detail.setOrderAmount(new BigDecimal("100.50"));
        detail.setActualPayAmount(new BigDecimal("90.50"));
        when(scrmSceneCouponRecordsMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(Collections.singletonList(record));
        when(scrmCouponOrderAmountDetailMapper.selectByExample(any(ScrmCouponOrderAmountDetailExample.class))).thenReturn(Collections.singletonList(detail));
        // act
        invokeProcessCorpAppConfigNarrowly(config, testDate);
        // assert
        verify(scrmSceneCouponRecordsMapper).selectByExample(any(ScrmSceneCouponRecordsExample.class));
        verify(scrmCouponOrderAmountDetailMapper).selectByExample(any(ScrmCouponOrderAmountDetailExample.class));
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(anyString(), argThat(snapshot -> {
            EsNarrowlyTransactionDataSnapshot esSnapshot = (EsNarrowlyTransactionDataSnapshot) snapshot;
            return "100.50".equals(esSnapshot.getGtv()) && "90.50".equals(esSnapshot.getActualGtv()) && esSnapshot.getTransactionOrderCount() == 1 && esSnapshot.getNarrowUserCount() == 1;
        }), anyString());
    }

    /**
     * Tests processing with multiple coupon records and order amount details
     */
    @Test
    public void testProcessCorpAppConfigNarrowlyWithMultipleRecords() throws Throwable {
        // arrange
        CorpAppConfig config = new CorpAppConfig();
        config.setCorpId(testCorpId);
        config.setAppId(testAppId);
        ScrmSceneCouponRecords record1 = new ScrmSceneCouponRecords();
        record1.setCoupongroupid("group1");
        record1.setUnifiedcouponid("coupon1");
        record1.setOrderid("order1");
        record1.setUserid(1001L);
        ScrmSceneCouponRecords record2 = new ScrmSceneCouponRecords();
        record2.setCoupongroupid("group2");
        record2.setUnifiedcouponid("coupon2");
        record2.setOrderid("order2");
        record2.setUserid(1002L);
        ScrmCouponOrderAmountDetail detail1 = new ScrmCouponOrderAmountDetail();
        detail1.setOrderAmount(new BigDecimal("100.50"));
        detail1.setActualPayAmount(new BigDecimal("90.50"));
        ScrmCouponOrderAmountDetail detail2 = new ScrmCouponOrderAmountDetail();
        detail2.setOrderAmount(new BigDecimal("200.75"));
        detail2.setActualPayAmount(new BigDecimal("180.75"));
        when(scrmSceneCouponRecordsMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(Arrays.asList(record1, record2));
        when(scrmCouponOrderAmountDetailMapper.selectByExample(any(ScrmCouponOrderAmountDetailExample.class))).thenReturn(Arrays.asList(detail1, detail2));
        // act
        invokeProcessCorpAppConfigNarrowly(config, testDate);
        // assert
        verify(scrmSceneCouponRecordsMapper).selectByExample(any(ScrmSceneCouponRecordsExample.class));
        verify(scrmCouponOrderAmountDetailMapper).selectByExample(any(ScrmCouponOrderAmountDetailExample.class));
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(anyString(), argThat(snapshot -> {
            EsNarrowlyTransactionDataSnapshot esSnapshot = (EsNarrowlyTransactionDataSnapshot) snapshot;
            return "301.25".equals(esSnapshot.getGtv()) && "271.25".equals(esSnapshot.getActualGtv()) && esSnapshot.getTransactionOrderCount() == 2 && esSnapshot.getNarrowUserCount() == 2;
        }), anyString());
    }

    /**
     * Tests processing with duplicate user IDs in records
     */
    @Test
    public void testProcessCorpAppConfigNarrowlyWithDuplicateUsers() throws Throwable {
        // arrange
        CorpAppConfig config = new CorpAppConfig();
        config.setCorpId(testCorpId);
        config.setAppId(testAppId);
        ScrmSceneCouponRecords record1 = new ScrmSceneCouponRecords();
        record1.setCoupongroupid("group1");
        record1.setUnifiedcouponid("coupon1");
        record1.setOrderid("order1");
        record1.setUserid(1001L);
        ScrmSceneCouponRecords record2 = new ScrmSceneCouponRecords();
        record2.setCoupongroupid("group2");
        record2.setUnifiedcouponid("coupon2");
        record2.setOrderid("order2");
        // Same user
        record2.setUserid(1001L);
        ScrmCouponOrderAmountDetail detail1 = new ScrmCouponOrderAmountDetail();
        detail1.setOrderAmount(new BigDecimal("100.50"));
        detail1.setActualPayAmount(new BigDecimal("90.50"));
        ScrmCouponOrderAmountDetail detail2 = new ScrmCouponOrderAmountDetail();
        detail2.setOrderAmount(new BigDecimal("200.75"));
        detail2.setActualPayAmount(new BigDecimal("180.75"));
        when(scrmSceneCouponRecordsMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(Arrays.asList(record1, record2));
        when(scrmCouponOrderAmountDetailMapper.selectByExample(any(ScrmCouponOrderAmountDetailExample.class))).thenReturn(Arrays.asList(detail1, detail2));
        // act
        invokeProcessCorpAppConfigNarrowly(config, testDate);
        // assert
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(anyString(), argThat(snapshot -> {
            EsNarrowlyTransactionDataSnapshot esSnapshot = (EsNarrowlyTransactionDataSnapshot) snapshot;
            // Should count distinct users
            return esSnapshot.getNarrowUserCount() == 1;
        }), anyString());
    }

    /**
     * Tests processing when database query throws exception
     */
    @Test
    public void testProcessCorpAppConfigNarrowlyWithDatabaseException() throws Throwable {
        // arrange
        CorpAppConfig config = new CorpAppConfig();
        config.setCorpId(testCorpId);
        config.setAppId(testAppId);
        when(scrmSceneCouponRecordsMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenThrow(new RuntimeException("Database error"));
        // act
        Exception exception = assertThrows(Exception.class, () -> {
            invokeProcessCorpAppConfigNarrowly(config, testDate);
        });
        // assert
        assertTrue(exception.getCause() instanceof RuntimeException);
        assertEquals("Database error", exception.getCause().getMessage());
        verify(dashBoardESWriteDomainService, never()).updateESDocByIdSelective(any(), any(), any());
    }

    /**
     * Tests processing with zero amount values in order details
     */
    @Test
    public void testProcessCorpAppConfigNarrowlyWithZeroAmounts() throws Throwable {
        // arrange
        CorpAppConfig config = new CorpAppConfig();
        config.setCorpId(testCorpId);
        config.setAppId(testAppId);
        ScrmSceneCouponRecords record = new ScrmSceneCouponRecords();
        record.setCoupongroupid("group1");
        record.setUnifiedcouponid("coupon1");
        record.setOrderid("order1");
        record.setUserid(1001L);
        ScrmCouponOrderAmountDetail detail = new ScrmCouponOrderAmountDetail();
        detail.setOrderAmount(BigDecimal.ZERO);
        detail.setActualPayAmount(BigDecimal.ZERO);
        when(scrmSceneCouponRecordsMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(Collections.singletonList(record));
        when(scrmCouponOrderAmountDetailMapper.selectByExample(any(ScrmCouponOrderAmountDetailExample.class))).thenReturn(Collections.singletonList(detail));
        // act
        invokeProcessCorpAppConfigNarrowly(config, testDate);
        // assert
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(anyString(), argThat(snapshot -> {
            EsNarrowlyTransactionDataSnapshot esSnapshot = (EsNarrowlyTransactionDataSnapshot) snapshot;
            return "0".equals(esSnapshot.getGtv()) && "0".equals(esSnapshot.getActualGtv()) && esSnapshot.getTransactionOrderCount() == 1 && esSnapshot.getNarrowUserCount() == 1;
        }), anyString());
    }

    @Test
    public void testProcessCorpAppConfigNarrowlyNoProcessInfo() throws Throwable {
        // arrange
        CorpAppConfig config = new CorpAppConfig();
        config.setAppId("testApp");
        config.setCorpId("testCorp");
        LocalDate date = LocalDate.now();
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(scrmSceneCouponRecordsMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        invokeProcessCorpAppConfigNarrowly(config, date);
        // assert
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(eq(NARROW_TRADE_INDEX), argThat(snapshot -> {
            EsNarrowlyTransactionDataSnapshot esSnapshot = (EsNarrowlyTransactionDataSnapshot) snapshot;
            return esSnapshot.getNarrowUserCount() == 0L && esSnapshot.getTransactionOrderCount() == 0L && "0".equals(esSnapshot.getGtv()) && "0".equals(esSnapshot.getActualGtv());
        }), anyString());
    }

    @Test
    public void testProcessCorpAppConfigNarrowlyWithProcessInfoNoGoalDetails() throws Throwable {
        // arrange
        CorpAppConfig config = new CorpAppConfig();
        config.setAppId("testApp");
        config.setCorpId("testCorp");
        LocalDate date = LocalDate.now();
        ScrmAmProcessOrchestrationInfoDO processInfo = new ScrmAmProcessOrchestrationInfoDO();
        processInfo.setId(1L);
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any())).thenReturn(Arrays.asList(processInfo));
        when(scrmAmProcessOrchestrationExecuteGoalDetailDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(scrmSceneCouponRecordsMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        invokeProcessCorpAppConfigNarrowly(config, date);
        // assert
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(eq(NARROW_TRADE_INDEX), argThat(snapshot -> {
            EsNarrowlyTransactionDataSnapshot esSnapshot = (EsNarrowlyTransactionDataSnapshot) snapshot;
            return esSnapshot.getNarrowUserCount() == 0L && esSnapshot.getTransactionOrderCount() == 0L && "0".equals(esSnapshot.getGtv()) && "0".equals(esSnapshot.getActualGtv());
        }), anyString());
    }

    @Test
    public void testProcessCorpAppConfigNarrowlyWithCompleteData() throws Throwable {
        // arrange
        CorpAppConfig config = new CorpAppConfig();
        config.setAppId("testApp");
        config.setCorpId("testCorp");
        LocalDate date = LocalDate.now();
        // Process orchestration data
        ScrmAmProcessOrchestrationInfoDO processInfo = new ScrmAmProcessOrchestrationInfoDO();
        processInfo.setId(1L);
        ScrmAmProcessOrchestrationExecuteGoalDetailDO goalDetail = new ScrmAmProcessOrchestrationExecuteGoalDetailDO();
        goalDetail.setExecuteLogId(100L);
        goalDetail.setHitHighLightTagId(ProcessOrchestrationInformationGatheringEnum.MESSAGE_TO_ORDER.getValue());
        ScrmAmProcessOrchestrationRelatedOrderInfoDO orderInfo = new ScrmAmProcessOrchestrationRelatedOrderInfoDO();
        orderInfo.setTargetMtUserId(1000L);
        orderInfo.setUniOrderId("order1");
        orderInfo.setOrderTotalAmount(new BigDecimal("100.50"));
        // Coupon data
        ScrmSceneCouponRecords couponRecord = new ScrmSceneCouponRecords();
        couponRecord.setUserid(2000L);
        couponRecord.setOrderid("order2");
        couponRecord.setCoupongroupid("group1");
        couponRecord.setUnifiedcouponid("coupon1");
        couponRecord.setStatisticstatus(StatusEnum.COUNTING.getCode());
        ScrmCouponOrderAmountDetail amountDetail = new ScrmCouponOrderAmountDetail();
        amountDetail.setOrderAmount(new BigDecimal("200.75"));
        amountDetail.setActualPayAmount(new BigDecimal("150.25"));
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any())).thenReturn(Arrays.asList(processInfo));
        when(scrmAmProcessOrchestrationExecuteGoalDetailDOMapper.selectByExample(any())).thenReturn(Arrays.asList(goalDetail));
        when(scrmAmProcessOrchestrationRelatedOrderInfoDOMapper.selectByExample(any())).thenReturn(Arrays.asList(orderInfo));
        when(scrmSceneCouponRecordsMapper.selectByExample(any())).thenReturn(Arrays.asList(couponRecord));
        when(scrmCouponOrderAmountDetailMapper.selectByExample(any())).thenReturn(Arrays.asList(amountDetail));
        // act
        invokeProcessCorpAppConfigNarrowly(config, date);
        // assert
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(eq(NARROW_TRADE_INDEX), argThat(snapshot -> {
            EsNarrowlyTransactionDataSnapshot esSnapshot = (EsNarrowlyTransactionDataSnapshot) snapshot;
            return esSnapshot.getNarrowUserCount() == 3L && esSnapshot.getTransactionOrderCount() == 3L && "401.75".equals(esSnapshot.getGtv()) && "351.25".equals(esSnapshot.getActualGtv());
        }), anyString());
    }

    @Test
    public void testProcessCorpAppConfigNarrowlyWithOnlyCouponData() throws Throwable {
        // arrange
        CorpAppConfig config = new CorpAppConfig();
        config.setAppId("testApp");
        config.setCorpId("testCorp");
        LocalDate date = LocalDate.now();
        // Coupon data
        ScrmSceneCouponRecords couponRecord = new ScrmSceneCouponRecords();
        couponRecord.setUserid(2000L);
        couponRecord.setOrderid("order2");
        couponRecord.setCoupongroupid("group1");
        couponRecord.setUnifiedcouponid("coupon1");
        couponRecord.setStatisticstatus(StatusEnum.COUNTING.getCode());
        ScrmCouponOrderAmountDetail amountDetail = new ScrmCouponOrderAmountDetail();
        amountDetail.setOrderAmount(new BigDecimal("200.75"));
        amountDetail.setActualPayAmount(new BigDecimal("150.25"));
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(scrmSceneCouponRecordsMapper.selectByExample(any())).thenReturn(Arrays.asList(couponRecord));
        when(scrmCouponOrderAmountDetailMapper.selectByExample(any())).thenReturn(Arrays.asList(amountDetail));
        // act
        invokeProcessCorpAppConfigNarrowly(config, date);
        // assert
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(eq(NARROW_TRADE_INDEX), argThat(snapshot -> {
            EsNarrowlyTransactionDataSnapshot esSnapshot = (EsNarrowlyTransactionDataSnapshot) snapshot;
            return esSnapshot.getNarrowUserCount() == 1L && esSnapshot.getTransactionOrderCount() == 1L && "200.75".equals(esSnapshot.getGtv()) && "150.25".equals(esSnapshot.getActualGtv());
        }), anyString());
    }

    @Test
    public void testProcessCorpAppConfigNarrowlyWithOnlyProcessData() throws Throwable {
        // arrange
        CorpAppConfig config = new CorpAppConfig();
        config.setAppId("testApp");
        config.setCorpId("testCorp");
        LocalDate date = LocalDate.now();
        // Process orchestration data
        ScrmAmProcessOrchestrationInfoDO processInfo = new ScrmAmProcessOrchestrationInfoDO();
        processInfo.setId(1L);
        ScrmAmProcessOrchestrationExecuteGoalDetailDO goalDetail = new ScrmAmProcessOrchestrationExecuteGoalDetailDO();
        goalDetail.setExecuteLogId(100L);
        goalDetail.setHitHighLightTagId(ProcessOrchestrationInformationGatheringEnum.MESSAGE_TO_ORDER.getValue());
        ScrmAmProcessOrchestrationRelatedOrderInfoDO orderInfo = new ScrmAmProcessOrchestrationRelatedOrderInfoDO();
        orderInfo.setTargetMtUserId(1000L);
        orderInfo.setUniOrderId("order1");
        orderInfo.setOrderTotalAmount(new BigDecimal("100.50"));
        when(scrmAmProcessOrchestrationInfoDOMapper.selectByExample(any())).thenReturn(Arrays.asList(processInfo));
        when(scrmAmProcessOrchestrationExecuteGoalDetailDOMapper.selectByExample(any())).thenReturn(Arrays.asList(goalDetail));
        when(scrmAmProcessOrchestrationRelatedOrderInfoDOMapper.selectByExample(any())).thenReturn(Arrays.asList(orderInfo));
        when(scrmSceneCouponRecordsMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        invokeProcessCorpAppConfigNarrowly(config, date);
        // assert
        verify(dashBoardESWriteDomainService).updateESDocByIdSelective(eq(NARROW_TRADE_INDEX), argThat(snapshot -> {
            EsNarrowlyTransactionDataSnapshot esSnapshot = (EsNarrowlyTransactionDataSnapshot) snapshot;
            return esSnapshot.getNarrowUserCount() == 2L && esSnapshot.getTransactionOrderCount() == 2L && "201.00".equals(esSnapshot.getGtv()) && "201.00".equals(esSnapshot.getActualGtv());
        }), anyString());
    }
}
