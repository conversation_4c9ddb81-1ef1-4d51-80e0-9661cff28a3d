package com.sankuai.scrm.core.service.dashboard.crane;

import com.sankuai.scrm.core.service.chat.dal.babymapper.OperatorHelperGroupChatLogMapper;
import com.sankuai.scrm.core.service.chat.dal.example.OperatorHelperGroupChatLogExample;
import com.sankuai.scrm.core.service.dashboard.dal.dto.EsActiveDataSnapshot;
import com.sankuai.scrm.core.service.external.contact.dal.babymapper.ContactUserMapper;
import com.sankuai.scrm.core.service.group.dal.babymapper.MemberInfoEntityMapper;
import com.sankuai.scrm.core.service.group.dal.babymapper.OperatorHelperPrivateChatMapper;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.group.dal.example.MemberInfoEntityExample;
import com.sankuai.scrm.core.service.session.dao.babymapper.ScrmSessionMsgRecordEntityMapper;
import com.sankuai.scrm.core.service.session.dao.entity.ScrmSessionMsgRecordEntity;
import com.sankuai.scrm.core.service.session.dao.example.ScrmSessionMsgRecordEntityExample;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ActiveDataTaskSetSenderCount1Test {

    @InjectMocks
    private ActiveDataTask activeDataTask;

    @Mock
    private ScrmSessionMsgRecordEntityMapper sessionMsgRecordEntityMapper;

    @Mock
    private MemberInfoEntityMapper memberInfoEntityMapper;

    @Mock
    private OperatorHelperGroupChatLogMapper operatorHelperGroupChatLogMapper;

    @Mock
    private ContactUserMapper contactUserMapper;

    @Mock
    private OperatorHelperPrivateChatMapper operatorHelperPrivateChatMapper;

    private EsActiveDataSnapshot esActiveDataSnapshot;

    private String corpId;

    private Date startOfYesterday;

    private Date endOfYesterday;

    @Before
    public void setUp() {
        esActiveDataSnapshot = new EsActiveDataSnapshot();
        corpId = "testCorpId";
        startOfYesterday = new Date(System.currentTimeMillis() - 24 * 60 * 60 * 1000);
        endOfYesterday = new Date(System.currentTimeMillis() - 1000);
    }

    private void invokeSetSenderCount(EsActiveDataSnapshot esActiveDataSnapshot, String corpId, Date startOfYesterday, Date endOfYesterday) throws Exception {
        Method method = ActiveDataTask.class.getDeclaredMethod("setSenderCount", EsActiveDataSnapshot.class, String.class, Date.class, Date.class);
        method.setAccessible(true);
        method.invoke(activeDataTask, esActiveDataSnapshot, corpId, startOfYesterday, endOfYesterday);
    }

    @Test
    public void testSetSenderCountBothSendersAreEmpty() throws Throwable {
        when(sessionMsgRecordEntityMapper.selectByExample(any(ScrmSessionMsgRecordEntityExample.class))).thenReturn(Collections.emptyList());
        when(operatorHelperGroupChatLogMapper.selectByExample(any(OperatorHelperGroupChatLogExample.class))).thenReturn(Collections.emptyList());
//        when(operatorHelperPrivateChatMapper.selectByExample(any(OperatorHelperPrivateChatExample.class))).thenReturn(Collections.emptyList());
        invokeSetSenderCount(esActiveDataSnapshot, corpId, startOfYesterday, endOfYesterday);
        assertEquals(0, esActiveDataSnapshot.getInGroupSenderCount());
        assertEquals(0, esActiveDataSnapshot.getPrivateSenderCount());
        assertEquals(0, esActiveDataSnapshot.getSenderCount());
    }

    @Test
    public void testSetSenderCountGroupSenderIsNotEmpty() throws Throwable {
        ScrmSessionMsgRecordEntity sessionEntity = new ScrmSessionMsgRecordEntity();
        sessionEntity.setSender("sender1");
        sessionEntity.setSessionType(1);
        sessionEntity.setSenderType(2);
        when(sessionMsgRecordEntityMapper.selectByExample(any(ScrmSessionMsgRecordEntityExample.class))).thenReturn(Arrays.asList(sessionEntity));
        MemberInfoEntity memberEntity = new MemberInfoEntity();
        memberEntity.setUnionId("unionId1");
        memberEntity.setGroupMemberId("sender1");
        memberEntity.setDeleted(false);
        when(memberInfoEntityMapper.selectByExample(any(MemberInfoEntityExample.class))).thenReturn(Arrays.asList(memberEntity));
        when(operatorHelperGroupChatLogMapper.selectByExample(any(OperatorHelperGroupChatLogExample.class))).thenReturn(Collections.emptyList());
//        when(operatorHelperPrivateChatMapper.selectByExample(any(OperatorHelperPrivateChatExample.class))).thenReturn(Collections.emptyList());
        invokeSetSenderCount(esActiveDataSnapshot, corpId, startOfYesterday, endOfYesterday);
        assertEquals(1, esActiveDataSnapshot.getInGroupSenderCount());
        assertEquals(0, esActiveDataSnapshot.getPrivateSenderCount());
        assertEquals(1, esActiveDataSnapshot.getSenderCount());
    }

    @Test
    public void testSetSenderCountBothSendersAreNotEmpty() throws Throwable {
        ScrmSessionMsgRecordEntity sessionEntity = new ScrmSessionMsgRecordEntity();
        sessionEntity.setSender("sender1");
        sessionEntity.setSessionType(1);
        sessionEntity.setSenderType(2);
        sessionEntity.setCorpId(corpId);
        when(sessionMsgRecordEntityMapper.selectByExample(any(ScrmSessionMsgRecordEntityExample.class))).thenReturn(Collections.singletonList(sessionEntity));
        MemberInfoEntity memberEntity = new MemberInfoEntity();
        memberEntity.setUnionId("unionId1");
        memberEntity.setGroupMemberId("sender1");
        memberEntity.setDeleted(false);
        memberEntity.setCorpId(corpId);
        when(memberInfoEntityMapper.selectByExample(any(MemberInfoEntityExample.class))).thenReturn(Collections.singletonList(memberEntity));
        when(operatorHelperGroupChatLogMapper.selectByExample(any(OperatorHelperGroupChatLogExample.class))).thenReturn(Collections.emptyList());
//        when(operatorHelperPrivateChatMapper.selectByExample(any(OperatorHelperPrivateChatExample.class))).thenReturn(Collections.emptyList());
        invokeSetSenderCount(esActiveDataSnapshot, corpId, startOfYesterday, endOfYesterday);
        assertEquals(1, esActiveDataSnapshot.getInGroupSenderCount());
        assertEquals(0, esActiveDataSnapshot.getPrivateSenderCount());
        assertEquals(1, esActiveDataSnapshot.getSenderCount());
    }
}
