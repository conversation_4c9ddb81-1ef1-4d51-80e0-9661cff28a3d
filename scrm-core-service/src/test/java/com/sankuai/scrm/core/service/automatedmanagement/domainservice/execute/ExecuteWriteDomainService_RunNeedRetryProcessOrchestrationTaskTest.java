package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecutePlanDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecutePlanDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationExecutePlanDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationExecuteStatusTypeEnum;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.concurrent.CompletableFuture;
import static org.mockito.Mockito.*;

public class ExecuteWriteDomainService_RunNeedRetryProcessOrchestrationTaskTest {

    @Mock(lenient = true)
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock(lenient = true)
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock(lenient = true)
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock(lenient = true)
    private ExecuteManagementService executeManagementService;

    @Mock(lenient = true)
    private ExtScrmAmProcessOrchestrationExecutePlanDOMapper executePlanDOMapper;

    @Spy
    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * Test runNeedRetryProcessOrchestrationTask when no retry is needed.
     */
    @Test
    public void testRunNeedRetryProcessOrchestrationTask_NoRetryNeeded() throws Throwable {
        // Setup
        when(executePlanDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecutePlanDOExample.class))).thenReturn(Collections.emptyList());
        // Execute
        executeWriteDomainService.runNeedRetryProcessOrchestrationTask();
        // Verify
        verify(executeManagementService, never()).getExecuteMediumManagementDTO(any());
        verify(executeManagementService, never()).taskRunFinished(any(), any());
    }
}
