package com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration.ScrmProcessOrchestrationConverter;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmConfigurationChangeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackAndProcessMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationExecutePlanDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.DdlResultDTO;
import com.sankuai.scrm.core.service.pchat.utils.DateUtil;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;

import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class ProcessOrchestrationWriteDomainServiceCreateProcessOrchestrationTest {

    @InjectMocks
    private ProcessOrchestrationWriteDomainService processOrchestrationWriteDomainService;

    @Mock(lenient = true)
    private ExtScrmAmProcessOrchestrationExecutePlanDOMapper extScrmAmProcessOrchestrationExecutePlanDOMapper;

    @Mock(lenient = true)
    private ScrmAmConfigurationChangeLogDOMapper configurationChangeLogDOMapper;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationConverter scrmProcessOrchestrationConverter;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock(lenient = true)
    private ScrmAmCrowdPackAndProcessMapDOMapper scrmAmCrowdPackAndProcessMapDOMapper;

    private SimpleDateFormat dateFormat = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    /**
     * Test case where beginTime is after current time
     */
    @Test
    public void testCreateProcessOrchestration_BeginTimeAfterNow() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setCrowdPackType(0);
        dto.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        Calendar future = Calendar.getInstance();
        future.add(Calendar.DAY_OF_MONTH, 1);
        infoDO.setBeginTime(future.getTime());
        infoDO.setId(1L);
        infoDO.setProcessOrchestrationType(dto.getProcessOrchestrationType());
        Calendar endFuture = Calendar.getInstance();
        endFuture.add(Calendar.DAY_OF_MONTH, 2);
        infoDO.setEndTime(endFuture.getTime());
        infoDO.setCronComment(DateUtil.formatYMdHms(endFuture.getTime()));
        when(scrmProcessOrchestrationConverter.convertToDO(any(ScrmProcessOrchestrationDTO.class))).thenReturn(infoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.insertSelective(any(ScrmAmProcessOrchestrationInfoDO.class))).thenReturn(1);
        when(scrmAmCrowdPackAndProcessMapDOMapper.batchInsert(any())).thenReturn(1);
        // act
        DdlResultDTO result = processOrchestrationWriteDomainService.createProcessOrchestration(dto);
        // assert
        assertTrue(result.isSuccess());
        verify(extScrmAmProcessOrchestrationExecutePlanDOMapper, never()).insert(any());
    }

    /**
     * Test case where endTime is before yesterday
     */
    @Test
    public void testCreateProcessOrchestration_EndTimeBeforeYesterday() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setCrowdPackType(0);
        dto.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        Calendar past = Calendar.getInstance();
        past.add(Calendar.DAY_OF_MONTH, -2);
        infoDO.setEndTime(past.getTime());
        infoDO.setId(1L);
        infoDO.setProcessOrchestrationType(dto.getProcessOrchestrationType());
        when(scrmProcessOrchestrationConverter.convertToDO(any(ScrmProcessOrchestrationDTO.class))).thenReturn(infoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.insertSelective(any(ScrmAmProcessOrchestrationInfoDO.class))).thenReturn(1);
        when(scrmAmCrowdPackAndProcessMapDOMapper.batchInsert(any())).thenReturn(1);
        // act
        DdlResultDTO result = processOrchestrationWriteDomainService.createProcessOrchestration(dto);
        // assert
        assertTrue(false == result.isSuccess());
        verify(extScrmAmProcessOrchestrationExecutePlanDOMapper, never()).insert(any());
    }

    /**
     * Test case where cronComment is blank
     */
    @Test
    public void testCreateProcessOrchestration_BlankCronComment() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setCrowdPackType(0);
        dto.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.TIMED_PROCESS_ORCHESTRATION.getValue().byteValue());
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setBeginTime(new Date());
        Calendar tomorrow = Calendar.getInstance();
        tomorrow.add(Calendar.DAY_OF_MONTH, 1);
        infoDO.setEndTime(tomorrow.getTime());
        infoDO.setCronComment("");
        infoDO.setId(1L);
        infoDO.setProcessOrchestrationType(dto.getProcessOrchestrationType());
        when(scrmProcessOrchestrationConverter.convertToDO(any(ScrmProcessOrchestrationDTO.class))).thenReturn(infoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.insertSelective(any(ScrmAmProcessOrchestrationInfoDO.class))).thenReturn(1);
        when(scrmAmCrowdPackAndProcessMapDOMapper.batchInsert(any())).thenReturn(1);
        // act
        DdlResultDTO result = processOrchestrationWriteDomainService.createProcessOrchestration(dto);
        // assert
        assertTrue(false == result.isSuccess());
        verify(extScrmAmProcessOrchestrationExecutePlanDOMapper, never()).insert(any());
    }
}
