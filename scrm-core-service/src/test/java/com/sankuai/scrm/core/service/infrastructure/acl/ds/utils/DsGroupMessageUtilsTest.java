package com.sankuai.scrm.core.service.infrastructure.acl.ds.utils;

import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsAssistantAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsCorpGroupAclService;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.AssistantInfo;
import com.sankuai.scrm.core.service.infrastructure.acl.haima.HaimaAclService;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.service.fe.corp.wx.thrift.open.OpenGroup;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.*;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class DsGroupMessageUtilsTest {

    @InjectMocks
    private DsGroupMessageUtils dsGroupMessageUtils;

    @Mock(lenient = true)
    private RedisStoreClient babyRedisClient;

    @Mock(lenient = true)
    private HaimaAclService haimaAclService;

    @Mock(lenient = true)
    private DsAssistantAcl dsAssistantAcl;

    @Mock(lenient = true)
    private DsCorpGroupAclService dsCorpGroupAclService;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    private CorpAppConfig createCorpAppConfig() {
        CorpAppConfig corpAppConfig = new CorpAppConfig();
        corpAppConfig.setCorpId("corp1");
        corpAppConfig.setOrgId(1L);
        corpAppConfig.setAppId("app1");
        corpAppConfig.setBizId(1);
        corpAppConfig.setBusinessCode("code1");
        return corpAppConfig;
    }

    private AssistantInfo createAssistantInfo() {
        AssistantInfo assistantInfo = new AssistantInfo();
        assistantInfo.setUserId("user1");
        assistantInfo.setAssistantId(1L);
        return assistantInfo;
    }

    @Test
    public void testGetAssistantOfChatIdWithNullCorpAppConfig() throws Throwable {
        int msgNum = 1;
        List<String> limitedExecutors = new ArrayList<>();
        List<String> chatIds = Arrays.asList("chat1", "chat2");
        CorpAppConfig corpAppConfig = null;
        Map<String, AssistantInfo> result = dsGroupMessageUtils.getAssistantOfChatId(msgNum, limitedExecutors, chatIds, corpAppConfig);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetAssistantOfChatIdWithEmptyChatIds() throws Throwable {
        int msgNum = 1;
        List<String> limitedExecutors = new ArrayList<>();
        List<String> chatIds = new ArrayList<>();
        CorpAppConfig corpAppConfig = createCorpAppConfig();
        Map<String, AssistantInfo> result = dsGroupMessageUtils.getAssistantOfChatId(msgNum, limitedExecutors, chatIds, corpAppConfig);
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    @Test
    public void testGetAssistantOfChatIdWithEmptyAssistantList() throws Throwable {
        int msgNum = 1;
        List<String> limitedExecutors = new ArrayList<>();
        List<String> chatIds = Arrays.asList("chat1", "chat2");
        CorpAppConfig corpAppConfig = createCorpAppConfig();
        when(dsAssistantAcl.getAssistantList(anyString(), anyLong())).thenReturn(new ArrayList<>());
        Map<String, AssistantInfo> result = dsGroupMessageUtils.getAssistantOfChatId(msgNum, limitedExecutors, chatIds, corpAppConfig);
        assertNull(result);
    }

    @Test
    public void testGetAssistantOfChatIdWithExceptionInGetAssistantList() throws Throwable {
        int msgNum = 1;
        List<String> limitedExecutors = new ArrayList<>();
        List<String> chatIds = Arrays.asList("chat1", "chat2");
        CorpAppConfig corpAppConfig = createCorpAppConfig();
        when(dsAssistantAcl.getAssistantList(anyString(), anyLong())).thenThrow(new RuntimeException("Test exception"));
        Map<String, AssistantInfo> result = dsGroupMessageUtils.getAssistantOfChatId(msgNum, limitedExecutors, chatIds, corpAppConfig);
        assertNull(result);
    }

    @Test
    public void testGetAssistantOfChatIdWithEmptyAssistantGroupMap() throws Throwable {
        int msgNum = 1;
        List<String> limitedExecutors = new ArrayList<>();
        List<String> chatIds = Arrays.asList("chat1", "chat2");
        CorpAppConfig corpAppConfig = createCorpAppConfig();
        AssistantInfo assistantInfo = createAssistantInfo();
        List<AssistantInfo> assistantList = Arrays.asList(assistantInfo);
        when(dsAssistantAcl.getAssistantList(anyString(), anyLong())).thenReturn(assistantList);
        when(haimaAclService.getConfigListByParams(anyString(), anyMap(), any())).thenReturn(new ArrayList<>());
        when(dsCorpGroupAclService.getGroupListByAssistant(anyLong(), anyString(), anyString())).thenReturn(new ArrayList<>());
        Map<String, AssistantInfo> result = dsGroupMessageUtils.getAssistantOfChatId(msgNum, limitedExecutors, chatIds, corpAppConfig);
        assertNull(result);
    }
}
