package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.automatedmanagement.dto.activitypage.ActivityPageCreatePagesDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.activitypage.ActivityPageUpdatePagesDTO;
import com.sankuai.dz.srcm.automatedmanagement.request.activitypage.CreateActivityPagesRequest;
import com.sankuai.dz.srcm.automatedmanagement.request.activitypage.DeleteActivityPagesRequest;
import com.sankuai.dz.srcm.automatedmanagement.request.activitypage.PagingQueryActivityPagetRequest;
import com.sankuai.dz.srcm.automatedmanagement.request.activitypage.PreviewActivityPageByIdRequest;
import com.sankuai.dz.srcm.automatedmanagement.request.activitypage.QueryActivityPageShelfProductRequest;
import com.sankuai.dz.srcm.automatedmanagement.request.activitypage.UpdateActivityPagesRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.activitypage.CreateActivityPageResultVO;
import com.sankuai.dz.srcm.automatedmanagement.response.activitypage.DeleteActivityPageResultVO;
import com.sankuai.dz.srcm.automatedmanagement.response.activitypage.PagingQueryActivityPagetResultVO;
import com.sankuai.dz.srcm.automatedmanagement.response.activitypage.ShelfProductInfoResultVO;
import com.sankuai.dz.srcm.automatedmanagement.response.activitypage.UpdateActivityPageResultVO;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.activitypage.ActivityPageManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import java.util.Arrays;
import java.util.Collections;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.MockitoJUnitRunner;

public class ScrmAutoManagementActivityPageServiceImplTest {

    @InjectMocks
    private ScrmAutoManagementActivityPageServiceImpl service;

    @Mock(lenient = true)
    private ProductManagementService productManagementService;

    @Mock(lenient = true)
    private ActivityPageManagementService activityPageManagementService;

    @InjectMocks
    private ScrmAutoManagementActivityPageServiceImpl scrmAutoManagementActivityPageService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试预览活动页，请求参数为空
     */
    @Test
    public void testPreviewAutomatedManagementActivityPage_RequestIsNull() {
        // arrange
        PreviewActivityPageByIdRequest request = null;
        // act
        RemoteResponse<ShelfProductInfoResultVO> response = service.previewAutomatedManagementActivityPage(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("请求参数不能为空", response.getMsg());
    }

    /**
     * 测试预览活动页，appId为空
     */
    @Test
    public void testPreviewAutomatedManagementActivityPage_AppIdIsNull() {
        // arrange
        PreviewActivityPageByIdRequest request = new PreviewActivityPageByIdRequest();
        request.setActivityPageId(1L);
        // act
        RemoteResponse<ShelfProductInfoResultVO> response = service.previewAutomatedManagementActivityPage(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("appId不能为空", response.getMsg());
    }

    /**
     * 测试预览活动页，活动页id为空
     */
    @Test
    public void testPreviewAutomatedManagementActivityPage_ActivityPageIdIsNull() {
        // arrange
        PreviewActivityPageByIdRequest request = new PreviewActivityPageByIdRequest();
        request.setAppId("testAppId");
        // act
        RemoteResponse<ShelfProductInfoResultVO> response = service.previewAutomatedManagementActivityPage(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("活动页id不能为空", response.getMsg());
    }

    /**
     * 测试预览活动页，正常情况
     */
    @Test
    public void testPreviewAutomatedManagementActivityPage_Success() {
        // arrange
        PreviewActivityPageByIdRequest request = new PreviewActivityPageByIdRequest();
        request.setAppId("testAppId");
        request.setActivityPageId(1L);
        ShelfProductInfoResultVO expectedResponse = new ShelfProductInfoResultVO();
        when(productManagementService.queryPreviewShelfProductInfo(anyLong(), anyString())).thenReturn(expectedResponse);
        // act
        RemoteResponse<ShelfProductInfoResultVO> response = service.previewAutomatedManagementActivityPage(request);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(expectedResponse, response.getData());
    }

    /**
     * 测试预览活动页，服务异常
     */
    @Test
    public void testPreviewAutomatedManagementActivityPage_Exception() {
        // arrange
        PreviewActivityPageByIdRequest request = new PreviewActivityPageByIdRequest();
        request.setAppId("testAppId");
        request.setActivityPageId(1L);
        when(productManagementService.queryPreviewShelfProductInfo(anyLong(), anyString())).thenThrow(new RuntimeException("服务异常"));
        // act
        RemoteResponse<ShelfProductInfoResultVO> response = service.previewAutomatedManagementActivityPage(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("服务异常", response.getMsg());
    }

    /**
     * 测试请求参数为空
     */
    @Test
    public void testCreateAutomatedManagementActivityPage_RequestIsNull() {
        // arrange
        CreateActivityPagesRequest request = null;
        // act
        RemoteResponse<CreateActivityPageResultVO> response = scrmAutoManagementActivityPageService.createAutomatedManagementActivityPage(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("请求参数不能为空", response.getMsg());
    }

    /**
     * 测试新建页数据为空
     */
    @Test
    public void testCreateAutomatedManagementActivityPage_CreatePagesDTOIsNull() {
        // arrange
        CreateActivityPagesRequest request = new CreateActivityPagesRequest();
        request.setAppId("appId");
        request.setCreatePagesDTO(null);
        // act
        RemoteResponse<CreateActivityPageResultVO> response = scrmAutoManagementActivityPageService.createAutomatedManagementActivityPage(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("新建页数据不能为空", response.getMsg());
    }

    /**
     * 测试appId为空
     */
    @Test
    public void testCreateAutomatedManagementActivityPage_AppIdIsNull() {
        // arrange
        CreateActivityPagesRequest request = new CreateActivityPagesRequest();
        ActivityPageCreatePagesDTO createPagesDTO = new ActivityPageCreatePagesDTO();
        request.setCreatePagesDTO(createPagesDTO);
        // act
        RemoteResponse<CreateActivityPageResultVO> response = scrmAutoManagementActivityPageService.createAutomatedManagementActivityPage(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("appId不能为空", response.getMsg());
    }

    /**
     * 测试活动页标题超过20个字符
     */
    @Test
    public void testCreateAutomatedManagementActivityPage_ActivityPageTitleOverLength() {
        // arrange
        CreateActivityPagesRequest request = new CreateActivityPagesRequest();
        request.setAppId("appId");
        ActivityPageCreatePagesDTO createPagesDTO = new ActivityPageCreatePagesDTO();
        createPagesDTO.setActivityPageTitle("超过20个字符的活动页标题超过20个字符的活动页标题");
        request.setCreatePagesDTO(createPagesDTO);
        // act
        RemoteResponse<CreateActivityPageResultVO> response = scrmAutoManagementActivityPageService.createAutomatedManagementActivityPage(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("活动页标题不能超过20个字符", response.getMsg());
    }

    /**
     * 测试小程序标题超过30个字符
     */
    @Test
    public void testCreateAutomatedManagementActivityPage_ProgTitleOverLength() {
        // arrange
        CreateActivityPagesRequest request = new CreateActivityPagesRequest();
        request.setAppId("appId");
        ActivityPageCreatePagesDTO createPagesDTO = new ActivityPageCreatePagesDTO();
        createPagesDTO.setProgTitle("超过30个字符的小程序标题超过30个字符的小程序标题超过30个字符的小程序标题");
        request.setCreatePagesDTO(createPagesDTO);
        // act
        RemoteResponse<CreateActivityPageResultVO> response = scrmAutoManagementActivityPageService.createAutomatedManagementActivityPage(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("小程序标题不能超过30个字符", response.getMsg());
    }

    /**
     * 测试创建活动页成功
     */
    @Test
    public void testCreateAutomatedManagementActivityPage_Success() throws Exception {
        // arrange
        CreateActivityPagesRequest request = new CreateActivityPagesRequest();
        request.setAppId("appId");
        ActivityPageCreatePagesDTO createPagesDTO = new ActivityPageCreatePagesDTO();
        createPagesDTO.setActivityPageTitle("有效标题");
        createPagesDTO.setProgTitle("有效小程序标题");
        request.setCreatePagesDTO(createPagesDTO);
        when(activityPageManagementService.createActivityPage(anyString(), any(ActivityPageCreatePagesDTO.class))).thenReturn(1L);
        // act
        RemoteResponse<CreateActivityPageResultVO> response = scrmAutoManagementActivityPageService.createAutomatedManagementActivityPage(request);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertEquals(Long.valueOf(1), response.getData().getActivityPageId());
        assertTrue(response.getData().isSuccess());
    }

    /**
     * 测试创建活动页时发生异常
     */
    @Test
    public void testCreateAutomatedManagementActivityPage_Exception() throws Exception {
        // arrange
        CreateActivityPagesRequest request = new CreateActivityPagesRequest();
        request.setAppId("appId");
        ActivityPageCreatePagesDTO createPagesDTO = new ActivityPageCreatePagesDTO();
        createPagesDTO.setActivityPageTitle("有效标题");
        createPagesDTO.setProgTitle("有效小程序标题");
        request.setCreatePagesDTO(createPagesDTO);
        when(activityPageManagementService.createActivityPage(anyString(), any(ActivityPageCreatePagesDTO.class))).thenThrow(new RuntimeException("数据库异常"));
        // act
        RemoteResponse<CreateActivityPageResultVO> response = scrmAutoManagementActivityPageService.createAutomatedManagementActivityPage(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("数据库异常", response.getMsg());
    }

    /**
     * 测试请求参数为空
     */
    @Test
    public void testUpdateAutomatedManagementActivityPagesRequestIsNull() {
        // arrange
        UpdateActivityPagesRequest request = null;
        // act
        RemoteResponse<UpdateActivityPageResultVO> response = service.updateAutomatedManagementActivityPages(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("请求参数不能为空", response.getMsg());
    }

    /**
     * 测试更新页数据为空
     */
    @Test
    public void testUpdateAutomatedManagementActivityPagesUpdatePagesDTOSIsEmpty() {
        // arrange
        UpdateActivityPagesRequest request = new UpdateActivityPagesRequest();
        request.setAppId("appId");
        request.setUpdatePagesDTOS(Collections.emptyList());
        // act
        RemoteResponse<UpdateActivityPageResultVO> response = service.updateAutomatedManagementActivityPages(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("更新页数据不能为空", response.getMsg());
    }

    /**
     * 测试appId为空
     */
    @Test
    public void testUpdateAutomatedManagementActivityPagesAppIdIsNull() {
        // arrange
        UpdateActivityPagesRequest request = new UpdateActivityPagesRequest();
        request.setUpdatePagesDTOS(Arrays.asList(new ActivityPageUpdatePagesDTO()));
        // act
        RemoteResponse<UpdateActivityPageResultVO> response = service.updateAutomatedManagementActivityPages(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("appId不能为空", response.getMsg());
    }

    /**
     * 测试更新活动页成功
     */
    @Test
    public void testUpdateAutomatedManagementActivityPagesSuccess() throws Exception {
        // arrange
        UpdateActivityPagesRequest request = new UpdateActivityPagesRequest();
        request.setAppId("appId");
        ActivityPageUpdatePagesDTO dto = new ActivityPageUpdatePagesDTO();
        request.setUpdatePagesDTOS(Arrays.asList(dto));
        // act
        RemoteResponse<UpdateActivityPageResultVO> response = service.updateAutomatedManagementActivityPages(request);
        // assert
        assertTrue(response.isSuccess());
        assertNotNull(response.getData());
        assertTrue(response.getData().isSuccess());
        verify(activityPageManagementService, times(1)).updateActivityPage(eq("appId"), any(ActivityPageUpdatePagesDTO.class));
    }

    /**
     * 测试更新活动页时发生异常
     */
    @Test
    public void testUpdateAutomatedManagementActivityPagesThrowsException() throws Exception {
        // arrange
        UpdateActivityPagesRequest request = new UpdateActivityPagesRequest();
        request.setAppId("appId");
        ActivityPageUpdatePagesDTO dto = new ActivityPageUpdatePagesDTO();
        request.setUpdatePagesDTOS(Arrays.asList(dto));
        doThrow(new RuntimeException("数据库异常")).when(activityPageManagementService).updateActivityPage(eq("appId"), any(ActivityPageUpdatePagesDTO.class));
        // act
        RemoteResponse<UpdateActivityPageResultVO> response = service.updateAutomatedManagementActivityPages(request);
        // assert
        assertFalse(response.isSuccess());
        assertEquals("数据库异常", response.getMsg());
        verify(activityPageManagementService, times(1)).updateActivityPage(eq("appId"), any(ActivityPageUpdatePagesDTO.class));
    }

    /**
     * 测试请求参数为空
     */
    @Test
    public void testDeleteAutomatedManagementActivityPages_RequestIsNull() {
        // arrange
        DeleteActivityPagesRequest request = null;
        // act
        RemoteResponse<DeleteActivityPageResultVO> response = service.deleteAutomatedManagementActivityPages(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("请求参数不能为空", response.getMsg());
    }

    /**
     * 测试活动页id列表为空
     */
    @Test
    public void testDeleteAutomatedManagementActivityPages_ActivityPageIdsIsEmpty() {
        // arrange
        DeleteActivityPagesRequest request = new DeleteActivityPagesRequest();
        request.setAppId("appId");
        request.setActivityPageIds(Collections.emptyList());
        // act
        RemoteResponse<DeleteActivityPageResultVO> response = service.deleteAutomatedManagementActivityPages(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("活动页id不能为空", response.getMsg());
    }

    /**
     * 测试appId为空
     */
    @Test
    public void testDeleteAutomatedManagementActivityPages_AppIdIsNull() {
        // arrange
        DeleteActivityPagesRequest request = new DeleteActivityPagesRequest();
        request.setActivityPageIds(Arrays.asList(1L, 2L));
        // act
        RemoteResponse<DeleteActivityPageResultVO> response = service.deleteAutomatedManagementActivityPages(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("appId不能为空", response.getMsg());
    }

    /**
     * 测试批量删除活动页成功
     */
    @Test
    public void testDeleteAutomatedManagementActivityPages_Success() throws Exception {
        // arrange
        DeleteActivityPagesRequest request = new DeleteActivityPagesRequest();
        request.setAppId("appId");
        request.setActivityPageIds(Arrays.asList(1L, 2L));
        doNothing().when(activityPageManagementService).batchDeleteActivityPages(anyString(), anyList());
        // act
        RemoteResponse<DeleteActivityPageResultVO> response = service.deleteAutomatedManagementActivityPages(request);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertTrue(response.getData().isSuccess());
    }

    /**
     * 测试批量删除活动页时发生异常
     */
    @Test
    public void testDeleteAutomatedManagementActivityPages_Exception() throws Exception {
        // arrange
        DeleteActivityPagesRequest request = new DeleteActivityPagesRequest();
        request.setAppId("appId");
        request.setActivityPageIds(Arrays.asList(1L, 2L));
        doThrow(new RuntimeException("数据库异常")).when(activityPageManagementService).batchDeleteActivityPages(anyString(), anyList());
        // act
        RemoteResponse<DeleteActivityPageResultVO> response = service.deleteAutomatedManagementActivityPages(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("数据库异常", response.getMsg());
    }

    /**
     * 测试请求参数为空
     */
    @Test
    public void testQueryAutomatedManagementActivityPageRequestIsNull() {
        PageRemoteResponse<PagingQueryActivityPagetResultVO> response = scrmAutoManagementActivityPageService.queryAutomatedManagementActivityPage(null);
        assertNotNull(response);
        assertEquals("请求参数错误", response.getMsg());
    }

    /**
     * 测试pageSize参数无效
     */
    @Test
    public void testQueryAutomatedManagementActivityPageInvalidPageSize() {
        PagingQueryActivityPagetRequest request = new PagingQueryActivityPagetRequest();
        request.setPageSize(0);
        PageRemoteResponse<PagingQueryActivityPagetResultVO> response = scrmAutoManagementActivityPageService.queryAutomatedManagementActivityPage(request);
        assertNotNull(response);
        assertEquals("请求参数错误", response.getMsg());
    }

    /**
     * 测试pageNum参数无效
     */
    @Test
    public void testQueryAutomatedManagementActivityPageInvalidPageNum() {
        PagingQueryActivityPagetRequest request = new PagingQueryActivityPagetRequest();
        request.setPageNum(0);
        PageRemoteResponse<PagingQueryActivityPagetResultVO> response = scrmAutoManagementActivityPageService.queryAutomatedManagementActivityPage(request);
        assertNotNull(response);
        assertEquals("请求参数错误", response.getMsg());
    }

    /**
     * 测试appId为空
     */
    @Test
    public void testQueryAutomatedManagementActivityPageAppIdIsNull() {
        PagingQueryActivityPagetRequest request = new PagingQueryActivityPagetRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        PageRemoteResponse<PagingQueryActivityPagetResultVO> response = scrmAutoManagementActivityPageService.queryAutomatedManagementActivityPage(request);
        assertNotNull(response);
        assertEquals("appId不能为空", response.getMsg());
    }

    /**
     * 测试查询结果为空
     */
    @Test
    public void testQueryAutomatedManagementActivityPageResultIsEmpty() throws Exception {
        PagingQueryActivityPagetRequest request = new PagingQueryActivityPagetRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        request.setAppId("testAppId");
        when(activityPageManagementService.pagingQueryActivityPage(any(PagingQueryActivityPagetRequest.class))).thenReturn(Collections.emptyList());
        PageRemoteResponse<PagingQueryActivityPagetResultVO> response = scrmAutoManagementActivityPageService.queryAutomatedManagementActivityPage(request);
        assertNotNull(response);
        assertTrue(response.getData().isEmpty());
    }

    /**
     * 测试查询成功
     */
    @Test
    public void testQueryAutomatedManagementActivityPageSuccess() throws Exception {
        PagingQueryActivityPagetRequest request = new PagingQueryActivityPagetRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        request.setAppId("testAppId");
        PagingQueryActivityPagetResultVO resultVO = new PagingQueryActivityPagetResultVO();
        resultVO.setActivityPageTitle("Test Title");
        when(activityPageManagementService.pagingQueryActivityPage(any(PagingQueryActivityPagetRequest.class))).thenReturn(Arrays.asList(resultVO));
        when(activityPageManagementService.fuzzQueryActivityPageCount(any(PagingQueryActivityPagetRequest.class))).thenReturn(1L);
        PageRemoteResponse<PagingQueryActivityPagetResultVO> response = scrmAutoManagementActivityPageService.queryAutomatedManagementActivityPage(request);
        assertNotNull(response);
        assertFalse(response.getData().isEmpty());
        assertEquals("Test Title", response.getData().get(0).getActivityPageTitle());
    }

    /**
     * 测试查询异常
     */
    @Test
    public void testQueryAutomatedManagementActivityPageException() throws Exception {
        PagingQueryActivityPagetRequest request = new PagingQueryActivityPagetRequest();
        request.setPageNum(1);
        request.setPageSize(10);
        request.setAppId("testAppId");
        when(activityPageManagementService.pagingQueryActivityPage(any(PagingQueryActivityPagetRequest.class))).thenThrow(new RuntimeException("Database error"));
        PageRemoteResponse<PagingQueryActivityPagetResultVO> response = scrmAutoManagementActivityPageService.queryAutomatedManagementActivityPage(request);
        assertNotNull(response);
        assertEquals("Database error", response.getMsg());
    }

    /**
     * 测试请求参数为空
     */
    @Test
    public void testQueryActivityPageShelfProductRequestIsNull() {
        // arrange
        QueryActivityPageShelfProductRequest request = null;
        // act
        RemoteResponse<ShelfProductInfoResultVO> response = scrmAutoManagementActivityPageService.queryActivityPageShelfProduct(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("请求参数不能为空", response.getMsg());
    }

    /**
     * 测试appId为空
     */
    @Test
    public void testQueryActivityPageShelfProductAppIdIsNull() {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setAppId(null);
        // act
        RemoteResponse<ShelfProductInfoResultVO> response = scrmAutoManagementActivityPageService.queryActivityPageShelfProduct(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("appId不能为空", response.getMsg());
    }

    /**
     * 测试查询成功
     */
    @Test
    public void testQueryActivityPageShelfProductSuccess() throws Exception {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setAppId("testAppId");
        ShelfProductInfoResultVO expectedResult = new ShelfProductInfoResultVO();
        when(productManagementService.queryShelfProductInfoByRequest(any(QueryActivityPageShelfProductRequest.class))).thenReturn(expectedResult);
        // act
        RemoteResponse<ShelfProductInfoResultVO> response = scrmAutoManagementActivityPageService.queryActivityPageShelfProduct(request);
        // assert
        assertNotNull(response);
        assertTrue(response.isSuccess());
        assertEquals(expectedResult, response.getData());
    }

    /**
     * 测试查询异常
     */
    @Test
    public void testQueryActivityPageShelfProductException() throws Exception {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setAppId("testAppId");
        when(productManagementService.queryShelfProductInfoByRequest(any(QueryActivityPageShelfProductRequest.class))).thenThrow(new RuntimeException("查询异常"));
        // act
        RemoteResponse<ShelfProductInfoResultVO> response = scrmAutoManagementActivityPageService.queryActivityPageShelfProduct(request);
        // assert
        assertNotNull(response);
        assertFalse(response.isSuccess());
        assertEquals("查询异常", response.getMsg());
    }
}
