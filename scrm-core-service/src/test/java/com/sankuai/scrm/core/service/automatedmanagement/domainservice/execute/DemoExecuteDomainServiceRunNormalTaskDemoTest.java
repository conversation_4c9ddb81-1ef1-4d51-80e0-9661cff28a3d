package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.infrastructure.dal.babymapper.ContactUserDoMapper;
import com.sankuai.scrm.core.service.infrastructure.dal.entity.ContactUserDo;
import com.sankuai.scrm.core.service.infrastructure.dal.example.ContactUserDoExample;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DemoExecuteDomainServiceRunNormalTaskDemoTest {

    @InjectMocks
    private DemoExecuteDomainService demoExecuteDomainService;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private ContactUserDoMapper contactUserDoMapper;

    private MockedStatic<Lion> lionMock;

    private MockedStatic<Environment> environmentMock;

    @Before
    public void setUp() {
        lionMock = mockStatic(Lion.class);
        environmentMock = mockStatic(Environment.class);
        environmentMock.when(Environment::getAppName).thenReturn("test-app");
    }

    @After
    public void tearDown() {
        lionMock.close();
        environmentMock.close();
    }

    /**
     * Test case: Process orchestration ID not in white list
     * Expected: Return failure result with illegal argument code
     */
    @Test
    public void testRunNormalTaskDemo_NotInWhiteList() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String wxUnionId = "test_union_id";
        lionMock.when(() -> Lion.getList(eq("test-app"), eq("refinement.operation.demo.white.list"), eq(Long.class))).thenReturn(Arrays.asList(2L, 3L));
        // act
        StepExecuteResultDTO result = demoExecuteDomainService.runNormalTaskDemo(processOrchestrationId, wxUnionId);
        // assert
        assertFalse(result.isSuccess());
        assertEquals((Integer) ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getCode(), result.getCode());
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getDesc(), result.getMsg());
    }

    /**
     * Test case: Normal execution with contact user found
     * Expected: Return success result after complete execution
     */
    @Test
    public void testRunNormalTaskDemo_WithContactUser() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String wxUnionId = "test_union_id";
        // mock white list
        lionMock.when(() -> Lion.getList(eq("test-app"), eq("refinement.operation.demo.white.list"), eq(Long.class))).thenReturn(Arrays.asList(1L, 2L));
        // mock process orchestration
        ScrmProcessOrchestrationDTO orchestrationDTO = new ScrmProcessOrchestrationDTO();
        orchestrationDTO.setId(processOrchestrationId);
        // setup node medium DTO
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        List<ScrmProcessOrchestrationNodeDTO> nodeDTOList = new ArrayList<>();
        ScrmProcessOrchestrationNodeDTO rootNode = new ScrmProcessOrchestrationNodeDTO();
        rootNode.setNodeId(0L);
        rootNode.setChildrenNodes(Arrays.asList(1L));
        nodeDTOList.add(rootNode);
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(nodeDTOList);
        orchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(orchestrationDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(processOrchestrationId)).thenReturn(future);
        // mock contact user
        ContactUserDo contactUserDo = new ContactUserDo();
        contactUserDo.setExternalUserId("ext_user_id");
        when(contactUserDoMapper.selectByExample(any(ContactUserDoExample.class))).thenReturn(Arrays.asList(contactUserDo));
        // mock execute management
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        when(executeManagementService.getExecuteMediumManagementDTO(any())).thenReturn(executeManagementDTO);
        // mock step execution
        StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
        stepResult.setSuccess(true);
        when(executeWriteDomainService.processNodes(any(), any(), any(), any(), any(), any())).thenReturn(stepResult);
        // act
        StepExecuteResultDTO result = demoExecuteDomainService.runNormalTaskDemo(processOrchestrationId, wxUnionId);
        // assert
        assertTrue(result.isSuccess());
        verify(executeWriteDomainService).thirdWxGroupTouchStep(eq(orchestrationDTO), eq(executeManagementDTO), eq(stepResult));
        verify(executeWriteDomainService).noticeStep(eq(orchestrationDTO), eq(executeManagementDTO));
        verify(executeWriteDomainService).updateNodeExecuteLog(eq(orchestrationDTO), eq(executeManagementDTO));
    }

    /**
     * Test case: Contact user not found but exists in group
     * Expected: Return success result with group member ID
     */
    @Test
    public void testRunNormalTaskDemo_WithGroupMember() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String wxUnionId = "test_union_id";
        // mock white list
        lionMock.when(() -> Lion.getList(eq("test-app"), eq("refinement.operation.demo.white.list"), eq(Long.class))).thenReturn(Arrays.asList(1L, 2L));
        // mock process orchestration
        ScrmProcessOrchestrationDTO orchestrationDTO = new ScrmProcessOrchestrationDTO();
        orchestrationDTO.setId(processOrchestrationId);
        // setup node medium DTO
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        List<ScrmProcessOrchestrationNodeDTO> nodeDTOList = new ArrayList<>();
        ScrmProcessOrchestrationNodeDTO rootNode = new ScrmProcessOrchestrationNodeDTO();
        rootNode.setNodeId(0L);
        rootNode.setChildrenNodes(Arrays.asList(1L));
        nodeDTOList.add(rootNode);
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(nodeDTOList);
        orchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        CompletableFuture<ScrmProcessOrchestrationDTO> future = CompletableFuture.completedFuture(orchestrationDTO);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(processOrchestrationId)).thenReturn(future);
        // mock empty contact user
        when(contactUserDoMapper.selectByExample(any(ContactUserDoExample.class))).thenReturn(new ArrayList<>());
        // mock group member
        MemberInfoEntity memberInfo = new MemberInfoEntity();
        memberInfo.setGroupMemberId("group_member_id");
        when(executeManagementService.getUsersGroupList(eq(wxUnionId), eq("hanchengfuwu"))).thenReturn(Arrays.asList(memberInfo));
        // mock execute management
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        when(executeManagementService.getExecuteMediumManagementDTO(any())).thenReturn(executeManagementDTO);
        // mock step execution
        StepExecuteResultDTO stepResult = new StepExecuteResultDTO();
        stepResult.setSuccess(true);
        when(executeWriteDomainService.processNodes(any(), any(), any(), any(), any(), any())).thenReturn(stepResult);
        // act
        StepExecuteResultDTO result = demoExecuteDomainService.runNormalTaskDemo(processOrchestrationId, wxUnionId);
        // assert
        assertTrue(result.isSuccess());
        verify(executeManagementService).getUsersGroupList(eq(wxUnionId), eq("hanchengfuwu"));
    }
}
