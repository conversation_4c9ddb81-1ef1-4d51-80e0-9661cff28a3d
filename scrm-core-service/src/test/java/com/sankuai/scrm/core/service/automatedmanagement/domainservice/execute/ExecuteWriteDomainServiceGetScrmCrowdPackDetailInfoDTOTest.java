package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.meituan.mdp.boot.starter.mdpcache.core.Cache;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackReadDomainService;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserTag;
import com.sankuai.scrm.core.service.user.dal.mapper.ext.ExtScrmUserTagMapper;
import java.util.Collections;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.test.util.ReflectionTestUtils;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackUpdateStrategyDO;
import com.sankuai.scrm.core.service.user.enums.ScrmUserTagEnum;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashSet;
import java.util.List;
import java.util.Set;
import org.mockito.junit.*;
import static org.mockito.ArgumentMatchers.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackUpdateStrategyDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackBaseInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackUpdateStrategyDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.config.ConfigDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackUpdateLockService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.AmLionConfigDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.RefinementOperationExecuteMessageProducer;
import com.sankuai.scrm.core.service.tag.dal.entity.ext.ExtExternalContactTag;
import com.sankuai.scrm.core.service.tag.dal.example.ExternalContactTagExample;
import com.sankuai.scrm.core.service.tag.dal.mapper.ext.ExtExternalContactTagMapper;
import com.sankuai.scrm.core.service.user.dal.entity.ext.ExtScrmUserTag;
import com.sankuai.scrm.core.service.user.dal.example.ScrmUserTagExample;
import java.util.*;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

@ExtendWith(MockitoExtension.class)
class ExecuteWriteDomainServiceGetScrmCrowdPackDetailInfoDTOTest {

    @Mock
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private Cache localCache;

    @Mock
    private ExtScrmUserTagMapper userTagDOMapper;

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private final String TEST_APP_ID = "testApp";

    private final String TEST_UNION_ID = "testUnionId";

    @Mock
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock
    private ConfigDomainService configDomainService;

    @Mock
    private ScrmAmCrowdPackBaseInfoDOMapper scrmAmCrowdPackBaseInfoDOMapper;

    @Mock
    private ScrmAmCrowdPackUpdateStrategyDOMapper updateStrategyDOMapper;

    @Mock
    private ExtExternalContactTagMapper externalContactTagMapper;

    @Mock
    private CrowdPackUpdateLockService crowdPackUpdateLockService;

    @Mock
    private RefinementOperationExecuteMessageProducer refinementOperationExecuteMessageProducer;

    private final Long testPackId = 123L;

    private final String testAppId = "testApp";

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId(TEST_APP_ID);
        ReflectionTestUtils.setField(executeWriteDomainService, "localCache", localCache);
        ReflectionTestUtils.setField(executeWriteDomainService, "userTagDOMapper", userTagDOMapper);
    }

    /**
     * Test when user exists in crowd pack - should return user DTO
     */
    @Test
    public void testGetScrmCrowdPackDetailInfoDTO_UserExists() throws Throwable {
        // arrange
        ScrmCrowdPackDetailInfoDTO expectedDto = new ScrmCrowdPackDetailInfoDTO();
        when(crowdPackReadDomainService.queryUserByUnionId(TEST_UNION_ID, TEST_APP_ID)).thenReturn(expectedDto);
        // act
        ScrmCrowdPackDetailInfoDTO result = executeWriteDomainService.getScrmCrowdPackDetailInfoDTO(processOrchestrationDTO, TEST_UNION_ID);
        // assert
        assertEquals(expectedDto, result);
        verify(crowdPackReadDomainService).queryUserByUnionId(TEST_UNION_ID, TEST_APP_ID);
        verifyNoMoreInteractions(crowdPackReadDomainService);
        verifyNoInteractions(executeManagementService);
    }


    private ScrmAmCrowdPackUpdateStrategyDO invokeGetFilterFieldId(List<ScrmAmCrowdPackUpdateStrategyDO> values, Set<Long> needFilterFieldIds) throws Exception {
        Method method = ExecuteWriteDomainService.class.getDeclaredMethod("getFilterFieldId", List.class, Set.class);
        method.setAccessible(true);
        return (ScrmAmCrowdPackUpdateStrategyDO) method.invoke(executeWriteDomainService, values, needFilterFieldIds);
    }

    @Test
    void testGetFilterFieldId_EmptyList_ReturnsNull() throws Throwable {
        // arrange
        Set<Long> needFilterFieldIds = new HashSet<>(Arrays.asList(10001L, 10002L));
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Collections.emptyList(), needFilterFieldIds);
        // assert
        assertNull(result);
    }

    @Test
    void testGetFilterFieldId_AllItemsFiltered_ReturnsNull() throws Throwable {
        // arrange
        ScrmAmCrowdPackUpdateStrategyDO item1 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item1.getFilterFieldId()).thenReturn(10001L);
        ScrmAmCrowdPackUpdateStrategyDO item2 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item2.getFilterFieldId()).thenReturn(10002L);
        Set<Long> needFilterFieldIds = new HashSet<>(Arrays.asList(10001L, 10002L));
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Arrays.asList(item1, item2), needFilterFieldIds);
        // assert
        assertNull(result);
        // Verify that getFilterFieldId was called on both items
        verify(item1, atLeastOnce()).getFilterFieldId();
        verify(item2, atLeastOnce()).getFilterFieldId();
    }

    @Test
    void testGetFilterFieldId_ContainsCorpTag_ReturnsCorpTagItem() throws Throwable {
        // arrange
        ScrmAmCrowdPackUpdateStrategyDO item1 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item1.getFilterFieldId()).thenReturn(10001L);
        ScrmAmCrowdPackUpdateStrategyDO corpTagItem = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(corpTagItem.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        Set<Long> needFilterFieldIds = new HashSet<>(Collections.singletonList(10001L));
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Arrays.asList(item1, corpTagItem), needFilterFieldIds);
        // assert
        assertNotNull(result);
        assertEquals(ScrmUserTagEnum.CORP_TAG.getTagId(), result.getFilterFieldId());
        // Verify that getFilterFieldId was called on both items
        verify(item1, atLeastOnce()).getFilterFieldId();
        verify(corpTagItem, atLeastOnce()).getFilterFieldId();
    }

    @Test
    void testGetFilterFieldId_NoCorpTagButOtherItems_ReturnsFirstValidItem() throws Throwable {
        // arrange
        ScrmAmCrowdPackUpdateStrategyDO item1 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item1.getFilterFieldId()).thenReturn(10001L);
        ScrmAmCrowdPackUpdateStrategyDO item2 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item2.getFilterFieldId()).thenReturn(30001L);
        Set<Long> needFilterFieldIds = new HashSet<>(Collections.singletonList(10001L));
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Arrays.asList(item1, item2), needFilterFieldIds);
        // assert
        assertNotNull(result);
        assertEquals(30001L, result.getFilterFieldId());
        // Verify that getFilterFieldId was called on both items
        verify(item1, atLeastOnce()).getFilterFieldId();
        verify(item2, atLeastOnce()).getFilterFieldId();
    }

    @Test
    void testGetFilterFieldId_MultipleItemsWithCorpTag_ReturnsCorpTagItem() throws Throwable {
        // arrange
        ScrmAmCrowdPackUpdateStrategyDO item1 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item1.getFilterFieldId()).thenReturn(10001L);
        ScrmAmCrowdPackUpdateStrategyDO corpTagItem = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(corpTagItem.getFilterFieldId()).thenReturn(ScrmUserTagEnum.CORP_TAG.getTagId());
        ScrmAmCrowdPackUpdateStrategyDO item2 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item2.getFilterFieldId()).thenReturn(30001L);
        Set<Long> needFilterFieldIds = new HashSet<>(Collections.singletonList(10001L));
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Arrays.asList(item1, corpTagItem, item2), needFilterFieldIds);
        // assert
        assertNotNull(result);
        assertEquals(ScrmUserTagEnum.CORP_TAG.getTagId(), result.getFilterFieldId());
        // Verify that getFilterFieldId was called on all items
        verify(item1, atLeastOnce()).getFilterFieldId();
        verify(corpTagItem, atLeastOnce()).getFilterFieldId();
        verify(item2, atLeastOnce()).getFilterFieldId();
    }

    @Test
    void testGetFilterFieldId_EmptyFilterSet_ReturnsFirstItem() throws Throwable {
        // arrange
        ScrmAmCrowdPackUpdateStrategyDO item1 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item1.getFilterFieldId()).thenReturn(10001L);
        ScrmAmCrowdPackUpdateStrategyDO item2 = mock(ScrmAmCrowdPackUpdateStrategyDO.class);
        when(item2.getFilterFieldId()).thenReturn(30001L);
        // act
        ScrmAmCrowdPackUpdateStrategyDO result = invokeGetFilterFieldId(Arrays.asList(item1, item2), Collections.emptySet());
        // assert
        assertNotNull(result);
        assertEquals(10001L, result.getFilterFieldId());
        // Verify that getFilterFieldId was called on both items
        verify(item1, atLeastOnce()).getFilterFieldId();
        verify(item2, atLeastOnce()).getFilterFieldId();
    }

    @Test
    public void testUpdateCrowdPackByPackIdWhenPackIsDeleted() throws Throwable {
        // arrange
        AmLionConfigDTO amLionConfigDTO = new AmLionConfigDTO();
        amLionConfigDTO.setMaxCrowdOfEachCorpSize(1000);
        when(crowdPackWriteDomainService.isDeletedTempPack(testPackId)).thenReturn(true);
        // act
        executeWriteDomainService.updateCrowdPackByPackId(testPackId, testAppId);
        // assert
        verify(crowdPackWriteDomainService).isDeletedTempPack(testPackId);
        verifyNoMoreInteractions(executeManagementService, scrmAmCrowdPackBaseInfoDOMapper, configDomainService);
    }

    @Test
    public void testUpdateCrowdPackByPackIdWhenTaskNotFirstTime() throws Throwable {
        // arrange
        AmLionConfigDTO amLionConfigDTO = new AmLionConfigDTO();
        amLionConfigDTO.setMaxCrowdOfEachCorpSize(1000);
        when(configDomainService.getAmLionConfigDTO()).thenReturn(amLionConfigDTO);
        when(crowdPackWriteDomainService.isDeletedTempPack(testPackId)).thenReturn(false);
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), testPackId)).thenReturn(false);
        // act
        executeWriteDomainService.updateCrowdPackByPackId(testPackId, testAppId);
        // assert
        verify(executeManagementService).taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), testPackId);
        verifyNoMoreInteractions(scrmAmCrowdPackBaseInfoDOMapper, updateStrategyDOMapper);
    }

    @Test
    public void testUpdateCrowdPackByPackIdWhenNoStrategies() throws Throwable {
        // arrange
        AmLionConfigDTO amLionConfigDTO = new AmLionConfigDTO();
        amLionConfigDTO.setMaxCrowdOfEachCorpSize(1000);
        when(configDomainService.getAmLionConfigDTO()).thenReturn(amLionConfigDTO);
        ScrmAmCrowdPackBaseInfoDO packInfo = new ScrmAmCrowdPackBaseInfoDO();
        packInfo.setValidPackVersion("v1");
        when(crowdPackWriteDomainService.isDeletedTempPack(testPackId)).thenReturn(false);
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), testPackId)).thenReturn(true);
        when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(testPackId)).thenReturn(packInfo);
        when(updateStrategyDOMapper.selectByExample(any(ScrmAmCrowdPackUpdateStrategyDOExample.class))).thenReturn(Collections.emptyList());
        // act
        executeWriteDomainService.updateCrowdPackByPackId(testPackId, testAppId);
        // assert
        verify(updateStrategyDOMapper).selectByExample(any(ScrmAmCrowdPackUpdateStrategyDOExample.class));
        verifyNoMoreInteractions(externalContactTagMapper, userTagDOMapper);
    }

    @Test
    public void testUpdateCrowdPackByPackIdWithCorpTagStrategy() throws Throwable {
        // arrange
        AmLionConfigDTO amLionConfigDTO = new AmLionConfigDTO();
        amLionConfigDTO.setMaxCrowdOfEachCorpSize(1000);
        when(configDomainService.getAmLionConfigDTO()).thenReturn(amLionConfigDTO);
        ScrmAmCrowdPackBaseInfoDO packInfo = new ScrmAmCrowdPackBaseInfoDO();
        packInfo.setValidPackVersion("v1");
        ScrmAmCrowdPackUpdateStrategyDO strategy = new ScrmAmCrowdPackUpdateStrategyDO();
        strategy.setFilterFieldId(ScrmUserTagEnum.CORP_TAG.getTagId());
        strategy.setParam("[\"tag1,tag2\"]");
        strategy.setGroupId(1);
        // CORP_TAG分支虽然不走setFilterCriteria，但为保险也加上
        strategy.setOperatorId(1L);
        when(crowdPackWriteDomainService.isDeletedTempPack(testPackId)).thenReturn(false);
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), testPackId)).thenReturn(true);
        when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(testPackId)).thenReturn(packInfo);
        when(updateStrategyDOMapper.selectByExample(any(ScrmAmCrowdPackUpdateStrategyDOExample.class))).thenReturn(Collections.singletonList(strategy));
        ExtExternalContactTag contactTag = new ExtExternalContactTag();
        contactTag.setUnionId("union1");
        contactTag.setId(1L);
        when(externalContactTagMapper.selectUnionIdByExample(any(ExternalContactTagExample.class))).thenReturn(Collections.singletonList(contactTag));
        doNothing().when(refinementOperationExecuteMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(anyList(), anyString(), anyLong());
        when(crowdPackUpdateLockService.getProducerValue(testPackId)).thenReturn(0L);
        when(crowdPackUpdateLockService.deleteProducerValue(testPackId)).thenReturn(true);
        // act
        executeWriteDomainService.updateCrowdPackByPackId(testPackId, testAppId);
        // assert
        verify(refinementOperationExecuteMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(anyList(), eq(testAppId), eq(testPackId));
        verify(executeManagementService).taskRunFinished(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), testPackId);
    }

    @Test
    public void testUpdateCrowdPackByPackIdWithRegularStrategy() throws Throwable {
        // arrange
        AmLionConfigDTO amLionConfigDTO = new AmLionConfigDTO();
        amLionConfigDTO.setMaxCrowdOfEachCorpSize(1000);
        when(configDomainService.getAmLionConfigDTO()).thenReturn(amLionConfigDTO);
        ScrmAmCrowdPackBaseInfoDO packInfo = new ScrmAmCrowdPackBaseInfoDO();
        packInfo.setValidPackVersion("v1");
        ScrmAmCrowdPackUpdateStrategyDO strategy = new ScrmAmCrowdPackUpdateStrategyDO();
        strategy.setFilterFieldId(ScrmUserTagEnum.IS_FRIEND.getTagId());
        strategy.setParam("[\"1\"]");
        strategy.setGroupId(1);
        // 必须设置
        strategy.setOperatorId(1L);
        when(crowdPackWriteDomainService.isDeletedTempPack(testPackId)).thenReturn(false);
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), testPackId)).thenReturn(true);
        when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(testPackId)).thenReturn(packInfo);
        when(updateStrategyDOMapper.selectByExample(any(ScrmAmCrowdPackUpdateStrategyDOExample.class))).thenReturn(Collections.singletonList(strategy));
        ExtScrmUserTag userTag = new ExtScrmUserTag();
        userTag.setUnionId("union1");
        userTag.setId(1L);
        when(userTagDOMapper.selectUserUnionIdByExample(any(ScrmUserTagExample.class))).thenReturn(Collections.singletonList(userTag));
        doNothing().when(refinementOperationExecuteMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(anyList(), anyString(), anyLong());
        when(crowdPackUpdateLockService.getProducerValue(testPackId)).thenReturn(0L);
        when(crowdPackUpdateLockService.deleteProducerValue(testPackId)).thenReturn(true);
        // act
        executeWriteDomainService.updateCrowdPackByPackId(testPackId, testAppId);
        // assert
        verify(refinementOperationExecuteMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(anyList(), eq(testAppId), eq(testPackId));
        verify(executeManagementService).taskRunFinished(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), testPackId);
    }

    @Test
    public void testUpdateCrowdPackByPackIdWithEmptyUserList() throws Throwable {
        // arrange
        AmLionConfigDTO amLionConfigDTO = new AmLionConfigDTO();
        amLionConfigDTO.setMaxCrowdOfEachCorpSize(1000);
        when(configDomainService.getAmLionConfigDTO()).thenReturn(amLionConfigDTO);
        ScrmAmCrowdPackBaseInfoDO packInfo = new ScrmAmCrowdPackBaseInfoDO();
        packInfo.setValidPackVersion("v1");
        ScrmAmCrowdPackUpdateStrategyDO strategy = new ScrmAmCrowdPackUpdateStrategyDO();
        strategy.setFilterFieldId(ScrmUserTagEnum.IS_FRIEND.getTagId());
        strategy.setParam("[\"1\"]");
        strategy.setGroupId(1);
        // 必须设置
        strategy.setOperatorId(1L);
        when(crowdPackWriteDomainService.isDeletedTempPack(testPackId)).thenReturn(false);
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), testPackId)).thenReturn(true);
        when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(testPackId)).thenReturn(packInfo);
        when(updateStrategyDOMapper.selectByExample(any(ScrmAmCrowdPackUpdateStrategyDOExample.class))).thenReturn(Collections.singletonList(strategy));
        when(userTagDOMapper.selectUserUnionIdByExample(any(ScrmUserTagExample.class))).thenReturn(Collections.emptyList());
        when(crowdPackUpdateLockService.getProducerValue(testPackId)).thenReturn(0L);
        when(crowdPackUpdateLockService.deleteProducerValue(testPackId)).thenReturn(true);
        // act
        executeWriteDomainService.updateCrowdPackByPackId(testPackId, testAppId);
        // assert
        verify(refinementOperationExecuteMessageProducer, never()).sendCrowdPackUpdateTaskExecuteMessage(anyList(), anyString(), anyLong());
        verify(executeManagementService).taskRunFinished(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), testPackId);
    }

    @Test
    public void testUpdateCrowdPackByPackIdWithMultipleBatches() throws Throwable {
        // arrange
        AmLionConfigDTO amLionConfigDTO = new AmLionConfigDTO();
        amLionConfigDTO.setMaxCrowdOfEachCorpSize(1000);
        when(configDomainService.getAmLionConfigDTO()).thenReturn(amLionConfigDTO);
        ScrmAmCrowdPackBaseInfoDO packInfo = new ScrmAmCrowdPackBaseInfoDO();
        packInfo.setValidPackVersion("v1");
        ScrmAmCrowdPackUpdateStrategyDO strategy = new ScrmAmCrowdPackUpdateStrategyDO();
        strategy.setFilterFieldId(ScrmUserTagEnum.IS_FRIEND.getTagId());
        strategy.setParam("[\"1\"]");
        strategy.setGroupId(1);
        // 必须设置
        strategy.setOperatorId(1L);
        when(crowdPackWriteDomainService.isDeletedTempPack(testPackId)).thenReturn(false);
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), testPackId)).thenReturn(true);
        when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(testPackId)).thenReturn(packInfo);
        when(updateStrategyDOMapper.selectByExample(any(ScrmAmCrowdPackUpdateStrategyDOExample.class))).thenReturn(Collections.singletonList(strategy));
        // 第一批满2000，第二批空
        List<ExtScrmUserTag> firstBatch = new ArrayList<>();
        for (int i = 0; i < 2000; i++) {
            ExtScrmUserTag tag = new ExtScrmUserTag();
            tag.setUnionId("union" + i);
            tag.setId((long) i);
            firstBatch.add(tag);
        }
        when(userTagDOMapper.selectUserUnionIdByExample(any(ScrmUserTagExample.class))).thenReturn(firstBatch).thenReturn(Collections.emptyList());
        doNothing().when(refinementOperationExecuteMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(anyList(), anyString(), anyLong());
        when(crowdPackUpdateLockService.getProducerValue(testPackId)).thenReturn(0L);
        when(crowdPackUpdateLockService.deleteProducerValue(testPackId)).thenReturn(true);
        // act
        executeWriteDomainService.updateCrowdPackByPackId(testPackId, testAppId);
        // assert
        verify(refinementOperationExecuteMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(anyList(), eq(testAppId), eq(testPackId));
        verify(executeManagementService).taskRunFinished(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), testPackId);
    }

    @Test
    public void testUpdateCrowdPackByPackIdWithNonZeroLockValue() throws Throwable {
        // arrange
        AmLionConfigDTO amLionConfigDTO = new AmLionConfigDTO();
        amLionConfigDTO.setMaxCrowdOfEachCorpSize(1000);
        when(configDomainService.getAmLionConfigDTO()).thenReturn(amLionConfigDTO);
        ScrmAmCrowdPackBaseInfoDO packInfo = new ScrmAmCrowdPackBaseInfoDO();
        packInfo.setValidPackVersion("v1");
        ScrmAmCrowdPackUpdateStrategyDO strategy = new ScrmAmCrowdPackUpdateStrategyDO();
        strategy.setFilterFieldId(ScrmUserTagEnum.IS_FRIEND.getTagId());
        strategy.setParam("[\"1\"]");
        strategy.setGroupId(1);
        // 必须设置
        strategy.setOperatorId(1L);
        when(crowdPackWriteDomainService.isDeletedTempPack(testPackId)).thenReturn(false);
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), testPackId)).thenReturn(true);
        when(scrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(testPackId)).thenReturn(packInfo);
        when(updateStrategyDOMapper.selectByExample(any(ScrmAmCrowdPackUpdateStrategyDOExample.class))).thenReturn(Collections.singletonList(strategy));
        when(userTagDOMapper.selectUserUnionIdByExample(any(ScrmUserTagExample.class))).thenReturn(Collections.emptyList());
        when(crowdPackUpdateLockService.getProducerValue(testPackId)).thenReturn(1L);
        // act
        executeWriteDomainService.updateCrowdPackByPackId(testPackId, testAppId);
        // assert
        verify(crowdPackUpdateLockService, never()).deleteProducerValue(testPackId);
        verify(executeManagementService).taskRunFinished(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), testPackId);
    }
}
