package com.sankuai.scrm.core.service.realtime.task.dal.example;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class ScrmRealtimeSceneUserRecordDOExampleCreateCriteriaTest {

    private ScrmRealtimeSceneUserRecordDOExample scrmRealtimeSceneUserRecordDOExample;

    @Before
    public void setUp() {
        scrmRealtimeSceneUserRecordDOExample = new ScrmRealtimeSceneUserRecordDOExample();
    }

    /**
     * 测试oredCriteria列表为空的情况
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsEmpty() {
        // arrange
        ScrmRealtimeSceneUserRecordDOExample.Criteria criteria = scrmRealtimeSceneUserRecordDOExample.createCriteria();
        // act
        // 无需执行任何操作
        // assert
        assertNotNull(criteria);
        assertEquals(1, scrmRealtimeSceneUserRecordDOExample.getOredCriteria().size());
    }

    /**
     * 测试oredCriteria列表不为空的情况
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsNotEmpty() {
        // arrange
        ScrmRealtimeSceneUserRecordDOExample.Criteria criteria1 = scrmRealtimeSceneUserRecordDOExample.createCriteria();
        ScrmRealtimeSceneUserRecordDOExample.Criteria criteria2 = scrmRealtimeSceneUserRecordDOExample.createCriteria();
        // act
        // 无需执行任何操作
        // assert
        assertNotNull(criteria1);
        assertNotNull(criteria2);
        assertEquals(1, scrmRealtimeSceneUserRecordDOExample.getOredCriteria().size());
    }

    /**
     * 测试or方法，应该返回一个新的Criteria对象，并将其添加到oredCriteria列表中
     */
    @Test
    public void testOr() {
        // arrange
        int originalSize = scrmRealtimeSceneUserRecordDOExample.getOredCriteria().size();
        // act
        ScrmRealtimeSceneUserRecordDOExample.Criteria criteria = scrmRealtimeSceneUserRecordDOExample.or();
        // assert
        assertNotNull(criteria);
        assertEquals(originalSize + 1, scrmRealtimeSceneUserRecordDOExample.getOredCriteria().size());
        assertTrue(scrmRealtimeSceneUserRecordDOExample.getOredCriteria().contains(criteria));
    }
}
