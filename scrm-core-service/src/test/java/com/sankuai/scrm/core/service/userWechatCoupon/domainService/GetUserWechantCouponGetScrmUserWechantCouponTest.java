package com.sankuai.scrm.core.service.userWechatCoupon.domainService;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.coupon.dal.entity.CouponInfo;
import com.sankuai.scrm.core.service.flow.dal.entity.ScrmFlowMaterialRelationLog;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxContactUserDetail;
import com.sankuai.scrm.core.service.userWechatCoupon.dal.entity.ScrmUserWechatCoupon;
import com.sankuai.scrm.core.service.userWechatCoupon.dal.example.ScrmUserWechatCouponExample;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class GetUserWechantCouponGetScrmUserWechantCouponTest {

    private GetUserWechantCoupon getUserWechantCoupon = new GetUserWechantCoupon();

    private GetUserWechantCoupon service;

    private Method method;

    private ScrmUserWechatCoupon invokeGetScrmUserWechantCoupon(WxContactUserDetail userDetail, Long couponId, CouponInfo couponInfo, ScrmFlowMaterialRelationLog relationLog, long mtUserId) throws Exception {
        Method method = GetUserWechantCoupon.class.getDeclaredMethod("getScrmUserWechantCoupon", WxContactUserDetail.class, Long.class, CouponInfo.class, ScrmFlowMaterialRelationLog.class, long.class);
        method.setAccessible(true);
        return (ScrmUserWechatCoupon) method.invoke(getUserWechantCoupon, userDetail, couponId, couponInfo, relationLog, mtUserId);
    }

    @BeforeEach
    void setUp() throws Exception {
        service = new GetUserWechantCoupon();
        method = GetUserWechantCoupon.class.getDeclaredMethod("getScrmUserWechantCouponExample", WxContactUserDetail.class, Long.class, CouponInfo.class, ScrmFlowMaterialRelationLog.class, long.class);
        method.setAccessible(true);
    }

    /**
     * Test normal case with all valid parameters
     */
    @Test
    public void testGetScrmUserWechantCouponNormalCase() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("testUnionId");
        Long couponId = 12345L;
        CouponInfo couponInfo = CouponInfo.builder().couponCode("TESTCODE123").build();
        ScrmFlowMaterialRelationLog relationLog = new ScrmFlowMaterialRelationLog();
        relationLog.setCategoryId(678L);
        relationLog.setPoiId(91011L);
        relationLog.setMtCityId(1);
        relationLog.setDpCityId(2);
        long mtUserId = 54321L;
        // act
        ScrmUserWechatCoupon result = invokeGetScrmUserWechantCoupon(userDetail, couponId, couponInfo, relationLog, mtUserId);
        // assert
        assertNotNull(result);
        assertEquals("12345", result.getCouponid());
        assertEquals("TESTCODE123", result.getCouponcode());
        assertEquals("678", result.getCategoryids());
        assertEquals(Long.valueOf(91011L), result.getPoiid());
        assertEquals(Integer.valueOf(1), result.getMtcityid());
        assertEquals(Integer.valueOf(2), result.getDpcityid());
        assertEquals(Long.valueOf(54321L), result.getMtuserid());
        assertEquals("testUnionId", result.getUnionid());
        assertEquals(Byte.valueOf((byte) 0), result.getUsed());
    }

    /**
     * Test with null userDetail which should throw NPE
     */
    @Test
    public void testGetScrmUserWechantCouponNullUserDetail() throws Throwable {
        // arrange
        Long couponId = 12345L;
        CouponInfo couponInfo = CouponInfo.builder().couponCode("TESTCODE123").build();
        ScrmFlowMaterialRelationLog relationLog = new ScrmFlowMaterialRelationLog();
        relationLog.setCategoryId(678L);
        relationLog.setPoiId(91011L);
        relationLog.setMtCityId(1);
        relationLog.setDpCityId(2);
        long mtUserId = 54321L;
        // act & assert
        InvocationTargetException ex = assertThrows(InvocationTargetException.class, () -> {
            invokeGetScrmUserWechantCoupon(null, couponId, couponInfo, relationLog, mtUserId);
        });
        assertTrue(ex.getCause() instanceof NullPointerException);
    }

    /**
     * Test with null couponId which should handle it gracefully
     */
    @Test
    public void testGetScrmUserWechantCouponNullCouponId() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("testUnionId");
        CouponInfo couponInfo = CouponInfo.builder().couponCode("TESTCODE123").build();
        ScrmFlowMaterialRelationLog relationLog = new ScrmFlowMaterialRelationLog();
        relationLog.setCategoryId(678L);
        relationLog.setPoiId(91011L);
        relationLog.setMtCityId(1);
        relationLog.setDpCityId(2);
        long mtUserId = 54321L;
        // act
        ScrmUserWechatCoupon result = invokeGetScrmUserWechantCoupon(userDetail, null, couponInfo, relationLog, mtUserId);
        // assert
        assertNotNull(result);
        // String.valueOf(null) returns "null"
        assertEquals("null", result.getCouponid());
        assertEquals("TESTCODE123", result.getCouponcode());
    }

    /**
     * Test with null couponInfo which should throw NPE
     */
    @Test
    public void testGetScrmUserWechantCouponNullCouponInfo() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("testUnionId");
        Long couponId = 12345L;
        ScrmFlowMaterialRelationLog relationLog = new ScrmFlowMaterialRelationLog();
        relationLog.setCategoryId(678L);
        relationLog.setPoiId(91011L);
        relationLog.setMtCityId(1);
        relationLog.setDpCityId(2);
        long mtUserId = 54321L;
        // act & assert
        InvocationTargetException ex = assertThrows(InvocationTargetException.class, () -> {
            invokeGetScrmUserWechantCoupon(userDetail, couponId, null, relationLog, mtUserId);
        });
        assertTrue(ex.getCause() instanceof NullPointerException);
    }

    /**
     * Test with null relationLog which should throw NPE
     */
    @Test
    public void testGetScrmUserWechantCouponNullRelationLog() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("testUnionId");
        Long couponId = 12345L;
        CouponInfo couponInfo = CouponInfo.builder().couponCode("TESTCODE123").build();
        long mtUserId = 54321L;
        // act & assert
        InvocationTargetException ex = assertThrows(InvocationTargetException.class, () -> {
            invokeGetScrmUserWechantCoupon(userDetail, couponId, couponInfo, null, mtUserId);
        });
        assertTrue(ex.getCause() instanceof NullPointerException);
    }

    /**
     * Test with empty unionId in userDetail
     */
    @Test
    public void testGetScrmUserWechantCouponEmptyUnionId() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("");
        Long couponId = 12345L;
        CouponInfo couponInfo = CouponInfo.builder().couponCode("TESTCODE123").build();
        ScrmFlowMaterialRelationLog relationLog = new ScrmFlowMaterialRelationLog();
        relationLog.setCategoryId(678L);
        relationLog.setPoiId(91011L);
        relationLog.setMtCityId(1);
        relationLog.setDpCityId(2);
        long mtUserId = 54321L;
        // act
        ScrmUserWechatCoupon result = invokeGetScrmUserWechantCoupon(userDetail, couponId, couponInfo, relationLog, mtUserId);
        // assert
        assertNotNull(result);
        assertEquals("", result.getUnionid());
    }

    /**
     * Test with max long values for numeric fields
     */
    @Test
    public void testGetScrmUserWechantCouponMaxValues() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("testUnionId");
        Long couponId = Long.MAX_VALUE;
        CouponInfo couponInfo = CouponInfo.builder().couponCode("TESTCODE123").build();
        ScrmFlowMaterialRelationLog relationLog = new ScrmFlowMaterialRelationLog();
        relationLog.setCategoryId(Long.MAX_VALUE);
        relationLog.setPoiId(Long.MAX_VALUE);
        relationLog.setMtCityId(Integer.MAX_VALUE);
        relationLog.setDpCityId(Integer.MAX_VALUE);
        long mtUserId = Long.MAX_VALUE;
        // act
        ScrmUserWechatCoupon result = invokeGetScrmUserWechantCoupon(userDetail, couponId, couponInfo, relationLog, mtUserId);
        // assert
        assertNotNull(result);
        assertEquals(String.valueOf(Long.MAX_VALUE), result.getCouponid());
        assertEquals(String.valueOf(Long.MAX_VALUE), result.getCategoryids());
        assertEquals(Long.valueOf(Long.MAX_VALUE), result.getPoiid());
        assertEquals(Integer.valueOf(Integer.MAX_VALUE), result.getMtcityid());
        assertEquals(Integer.valueOf(Integer.MAX_VALUE), result.getDpcityid());
        assertEquals(Long.valueOf(Long.MAX_VALUE), result.getMtuserid());
    }

    /**
     * Test normal case with all valid parameters
     */
    @Test
    public void testGetScrmUserWechantCouponExampleNormalCase() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("union123");
        Long couponId = 1001L;
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setCouponCode("CODE123");
        ScrmFlowMaterialRelationLog relationLog = new ScrmFlowMaterialRelationLog();
        relationLog.setCategoryId(2001L);
        relationLog.setPoiId(3001L);
        relationLog.setMtCityId(4001);
        relationLog.setDpCityId(5001);
        long mtUserId = 6001L;
        // act
        ScrmUserWechatCouponExample example = (ScrmUserWechatCouponExample) method.invoke(service, userDetail, couponId, couponInfo, relationLog, mtUserId);
        // assert
        assertNotNull(example);
        assertEquals(1, example.getOredCriteria().size());
        assertEquals("1001", example.getOredCriteria().get(0).getAllCriteria().get(0).getValue());
        assertEquals("CODE123", example.getOredCriteria().get(0).getAllCriteria().get(1).getValue());
        assertEquals("2001", example.getOredCriteria().get(0).getAllCriteria().get(2).getValue());
        assertEquals(3001L, example.getOredCriteria().get(0).getAllCriteria().get(3).getValue());
        assertEquals(4001, example.getOredCriteria().get(0).getAllCriteria().get(4).getValue());
        assertEquals(5001, example.getOredCriteria().get(0).getAllCriteria().get(5).getValue());
        assertEquals(6001L, example.getOredCriteria().get(0).getAllCriteria().get(6).getValue());
        assertEquals("union123", example.getOredCriteria().get(0).getAllCriteria().get(7).getValue());
    }

    /**
     * Test case when userDetail is null
     */
    @Test
    public void testGetScrmUserWechantCouponExampleNullUserDetail() throws Throwable {
        // arrange
        Long couponId = 1001L;
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setCouponCode("CODE123");
        ScrmFlowMaterialRelationLog relationLog = new ScrmFlowMaterialRelationLog();
        relationLog.setCategoryId(2001L);
        relationLog.setPoiId(3001L);
        relationLog.setMtCityId(4001);
        relationLog.setDpCityId(5001);
        long mtUserId = 6001L;
        // act & assert
        assertThrows(InvocationTargetException.class, () -> {
            method.invoke(service, null, couponId, couponInfo, relationLog, mtUserId);
        });
    }

    /**
     * Test case when couponInfo is null
     */
    @Test
    public void testGetScrmUserWechantCouponExampleNullCouponInfo() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("union123");
        Long couponId = 1001L;
        ScrmFlowMaterialRelationLog relationLog = new ScrmFlowMaterialRelationLog();
        relationLog.setCategoryId(2001L);
        relationLog.setPoiId(3001L);
        relationLog.setMtCityId(4001);
        relationLog.setDpCityId(5001);
        long mtUserId = 6001L;
        // act & assert
        assertThrows(InvocationTargetException.class, () -> {
            method.invoke(service, userDetail, couponId, null, relationLog, mtUserId);
        });
    }

    /**
     * Test case when relationLog is null
     */
    @Test
    public void testGetScrmUserWechantCouponExampleNullRelationLog() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("union123");
        Long couponId = 1001L;
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setCouponCode("CODE123");
        long mtUserId = 6001L;
        // act & assert
        assertThrows(InvocationTargetException.class, () -> {
            method.invoke(service, userDetail, couponId, couponInfo, null, mtUserId);
        });
    }

    /**
     * Test case when couponId is null
     */
    @Test
    public void testGetScrmUserWechantCouponExampleNullCouponId() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("union123");
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setCouponCode("CODE123");
        ScrmFlowMaterialRelationLog relationLog = new ScrmFlowMaterialRelationLog();
        relationLog.setCategoryId(2001L);
        relationLog.setPoiId(3001L);
        relationLog.setMtCityId(4001);
        relationLog.setDpCityId(5001);
        long mtUserId = 6001L;
        // act
        ScrmUserWechatCouponExample example = (ScrmUserWechatCouponExample) method.invoke(service, userDetail, null, couponInfo, relationLog, mtUserId);
        // assert
        assertNotNull(example);
        assertEquals("null", example.getOredCriteria().get(0).getAllCriteria().get(0).getValue());
    }

    /**
     * Test case when userDetail has null unionId
     */
    @Test
    public void testGetScrmUserWechantCouponExampleNullUnionId() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId(null);
        Long couponId = 1001L;
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setCouponCode("CODE123");
        ScrmFlowMaterialRelationLog relationLog = new ScrmFlowMaterialRelationLog();
        relationLog.setCategoryId(2001L);
        relationLog.setPoiId(3001L);
        relationLog.setMtCityId(4001);
        relationLog.setDpCityId(5001);
        long mtUserId = 6001L;
        // act & assert
        Exception exception = assertThrows(InvocationTargetException.class, () -> {
            method.invoke(service, userDetail, couponId, couponInfo, relationLog, mtUserId);
        });
        Throwable cause = exception.getCause();
        assertTrue(cause instanceof RuntimeException);
        assertEquals("Value for unionid cannot be null", cause.getMessage());
    }

    /**
     * Test case with zero and negative values for IDs
     */
    @Test
    public void testGetScrmUserWechantCouponExampleZeroAndNegativeIds() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("union123");
        Long couponId = -1001L;
        CouponInfo couponInfo = new CouponInfo();
        couponInfo.setCouponCode("CODE123");
        ScrmFlowMaterialRelationLog relationLog = new ScrmFlowMaterialRelationLog();
        relationLog.setCategoryId(0L);
        relationLog.setPoiId(-3001L);
        relationLog.setMtCityId(0);
        relationLog.setDpCityId(-5001);
        long mtUserId = 0L;
        // act
        ScrmUserWechatCouponExample example = (ScrmUserWechatCouponExample) method.invoke(service, userDetail, couponId, couponInfo, relationLog, mtUserId);
        // assert
        assertNotNull(example);
        assertEquals("-1001", example.getOredCriteria().get(0).getAllCriteria().get(0).getValue());
        assertEquals("0", example.getOredCriteria().get(0).getAllCriteria().get(2).getValue());
        assertEquals(-3001L, example.getOredCriteria().get(0).getAllCriteria().get(3).getValue());
        assertEquals(0, example.getOredCriteria().get(0).getAllCriteria().get(4).getValue());
        assertEquals(-5001, example.getOredCriteria().get(0).getAllCriteria().get(5).getValue());
        assertEquals(0L, example.getOredCriteria().get(0).getAllCriteria().get(6).getValue());
    }
}
