package com.sankuai.scrm.core.service.infrastructure.acl.ds;

import com.sankuai.service.fe.corp.wx.thrift.CorpWxService;
import com.sankuai.service.fe.corp.wx.thrift.InitGroupNotifactionRequest;
import com.sankuai.service.fe.corp.wx.thrift.InitGroupNotifactionResponse;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.Assert.*;
import org.junit.Before;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class DsCorpGroupAclServiceTest {

    @InjectMocks
    private DsCorpGroupAclService dsCorpGroupAclService;

    @Mock
    private CorpWxService.Iface corpWxService;

    public DsCorpGroupAclServiceTest() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 setGroupNotifyMsgV2 方法，正常情况
     */
    @Test
    public void testSetGroupNotifyMsgV2Normal() throws Throwable {
        // arrange
        String chatId = "testChatId";
        String notification = "testNotification";
        InitGroupNotifactionResponse expectedResponse = new InitGroupNotifactionResponse();
        when(corpWxService.initGroupNotifaction(any(InitGroupNotifactionRequest.class))).thenReturn(expectedResponse);
        // act
        InitGroupNotifactionResponse actualResponse = dsCorpGroupAclService.setGroupNotifyMsgV2(chatId, notification);
        // assert
        assertEquals(expectedResponse, actualResponse);
        verify(corpWxService, times(1)).initGroupNotifaction(any(InitGroupNotifactionRequest.class));
    }

    /**
     * 测试 setGroupNotifyMsgV2 方法，异常情况
     */
    @Test
    public void testSetGroupNotifyMsgV2Exception() throws Throwable {
        // arrange
        String chatId = "testChatId";
        String notification = "testNotification";
        when(corpWxService.initGroupNotifaction(any(InitGroupNotifactionRequest.class))).thenThrow(new RuntimeException());
        // act
        InitGroupNotifactionResponse actualResponse = dsCorpGroupAclService.setGroupNotifyMsgV2(chatId, notification);
        // assert
        assertNull(actualResponse);
        verify(corpWxService, times(1)).initGroupNotifaction(any(InitGroupNotifactionRequest.class));
    }
}
