package com.sankuai.scrm.core.service.realtime.task.dal.example;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import java.util.ArrayList;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.Mockito;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.junit.jupiter.api.DisplayName;

@ExtendWith(MockitoExtension.class)
public class ScrmAIAgentTestGroupRecordDOExampleClearTest {

    private ScrmAIAgentTestGroupRecordDOExample example;

    @BeforeEach
    public void setUp() {
        example = new ScrmAIAgentTestGroupRecordDOExample();
    }

    /**
     * 测试clear方法在初始状态下的行为
     */
    @Test
    public void testClearWhenInitialState() throws Throwable {
        // arrange - 初始状态已由setUp方法创建
        // act
        example.clear();
        // assert
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertTrue(example.getOredCriteria().isEmpty());
        assertNull(example.getOffset());
        assertNull(example.getRows());
    }

    /**
     * 测试clear方法在所有字段都有值的情况下的行为
     */
    @Test
    public void testClearWhenAllFieldsSet() throws Throwable {
        // arrange
        example.setOrderByClause("test_order");
        example.setDistinct(true);
        // 添加一个Criteria
        example.or();
        example.setOffset(10);
        example.setRows(20);
        // act
        example.clear();
        // assert
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertTrue(example.getOredCriteria().isEmpty());
        assertNull(example.getOffset());
        assertNull(example.getRows());
    }

    /**
     * 测试clear方法在部分字段有值的情况下的行为
     */
    @Test
    public void testClearWhenPartialFieldsSet() throws Throwable {
        // arrange
        example.setOrderByClause("test_order");
        example.setDistinct(true);
        // 不设置offset和rows
        // act
        example.clear();
        // assert
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertTrue(example.getOredCriteria().isEmpty());
        assertNull(example.getOffset());
        assertNull(example.getRows());
    }

    /**
     * 测试clear方法在多次调用后的行为
     */
    @Test
    public void testClearWhenCalledMultipleTimes() throws Throwable {
        // arrange
        example.setOrderByClause("test_order");
        example.setDistinct(true);
        example.or();
        // act
        example.clear();
        // 第二次调用
        example.clear();
        // assert
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertTrue(example.getOredCriteria().isEmpty());
        assertNull(example.getOffset());
        assertNull(example.getRows());
    }

    /**
     * 测试clear方法后可以重新设置值
     */
    @Test
    public void testClearThenResetValues() throws Throwable {
        // arrange
        example.setOrderByClause("test_order");
        example.setDistinct(true);
        example.or();
        // act
        example.clear();
        // 重新设置值
        example.setOrderByClause("new_order");
        example.setDistinct(false);
        example.or();
        example.setOffset(5);
        example.setRows(10);
        // assert
        assertEquals("new_order", example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertFalse(example.getOredCriteria().isEmpty());
        assertEquals(5, example.getOffset());
        assertEquals(10, example.getRows());
    }

    /**
     * 演示如何使用Mockito框架（虽然这个简单类不需要Mock）
     */
    @Test
    public void testClearWithMockitoDemonstration() throws Throwable {
        // arrange - 创建spy对象而不是mock对象
        ScrmAIAgentTestGroupRecordDOExample spyExample = Mockito.spy(new ScrmAIAgentTestGroupRecordDOExample());
        // 设置一些初始状态
        spyExample.setOrderByClause("test_order");
        spyExample.setDistinct(true);
        spyExample.or();
        // act
        spyExample.clear();
        // assert - 验证方法被调用且状态被重置
        verify(spyExample).clear();
        assertNull(spyExample.getOrderByClause());
        assertFalse(spyExample.isDistinct());
        assertTrue(spyExample.getOredCriteria().isEmpty());
    }

    @Test
    void testLimitWithPositiveOffsetAndRows() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int offset = 10;
        int rows = 20;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(offset, result.getOffset(), "offset应被正确设置为10");
        assertEquals(rows, result.getRows(), "rows应被正确设置为20");
    }

    @Test
    void testLimitWithZeroOffset() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int offset = 0;
        int rows = 20;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(offset, result.getOffset(), "offset为0应被正确设置");
        assertEquals(rows, result.getRows(), "rows应被正确设置为20");
    }

    @Test
    void testLimitWithZeroRows() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int offset = 10;
        int rows = 0;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(offset, result.getOffset(), "offset应被正确设置为10");
        assertEquals(rows, result.getRows(), "rows为0应被正确设置");
    }

    @Test
    void testLimitWithNullOffset() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        Integer offset = null;
        int rows = 20;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertNull(result.getOffset(), "offset为null应被正确设置");
        assertEquals(rows, result.getRows(), "rows应被正确设置为20");
    }

    @Test
    void testLimitWithNullRows() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int offset = 10;
        Integer rows = null;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(offset, result.getOffset(), "offset应被正确设置为10");
        assertNull(result.getRows(), "rows为null应被正确设置");
    }

    @Test
    void testLimitWithNegativeOffset() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int offset = -5;
        int rows = 20;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(offset, result.getOffset(), "负值offset应被正确设置");
        assertEquals(rows, result.getRows(), "rows应被正确设置为20");
    }

    @Test
    void testLimitWithNegativeRows() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int offset = 10;
        int rows = -5;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(offset, result.getOffset(), "offset应被正确设置为10");
        assertEquals(rows, result.getRows(), "负值rows应被正确设置");
    }

    @Test
    void testLimitMethodChaining() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(10, 20).limit(30, 40);
        // assert
        assertSame(example, result, "应返回当前对象实例");
        assertEquals(30, result.getOffset(), "第二次调用的offset应覆盖第一次的值");
        assertEquals(40, result.getRows(), "第二次调用的rows应覆盖第一次的值");
    }

    @Test
    public void testCreateCriteriaWhenOredCriteriaIsEmpty() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria result = example.createCriteria();
        // assert
        assertNotNull(result);
        assertEquals(1, example.getOredCriteria().size());
        assertSame(result, example.getOredCriteria().get(0));
    }

    @Test
    public void testCreateCriteriaWhenOredCriteriaIsNotEmpty() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        ScrmAIAgentTestGroupRecordDOExample.Criteria existingCriteria = new ScrmAIAgentTestGroupRecordDOExample.Criteria();
        example.getOredCriteria().add(existingCriteria);
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria result = example.createCriteria();
        // assert
        assertNotNull(result);
        assertEquals(1, example.getOredCriteria().size());
        assertSame(existingCriteria, example.getOredCriteria().get(0));
        assertNotSame(result, existingCriteria);
    }

    @Test
    public void testCreateCriteriaCallsCreateCriteriaInternal() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = spy(new ScrmAIAgentTestGroupRecordDOExample());
        // act
        example.createCriteria();
        // assert
        verify(example, times(1)).createCriteriaInternal();
    }

    @Test
    public void testCreateCriteriaReturnsCorrectType() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria result = example.createCriteria();
        // assert
        assertTrue(result instanceof ScrmAIAgentTestGroupRecordDOExample.Criteria);
    }

    @Test
    public void testCreateCriteriaMultipleCalls() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria first = example.createCriteria();
        ScrmAIAgentTestGroupRecordDOExample.Criteria second = example.createCriteria();
        // assert
        assertEquals(1, example.getOredCriteria().size());
        assertSame(first, example.getOredCriteria().get(0));
        assertNotSame(first, second);
    }

    @Test
    void testCreateCriteriaInternal_ReturnsNonNullCriteria() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertNotNull(result, "Created Criteria should not be null");
        assertInstanceOf(ScrmAIAgentTestGroupRecordDOExample.Criteria.class, result, "Result should be instance of Criteria");
    }

    @Test
    void testCreateCriteriaInternal_ReturnsNewInstanceEachTime() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria firstInstance = example.createCriteriaInternal();
        ScrmAIAgentTestGroupRecordDOExample.Criteria secondInstance = example.createCriteriaInternal();
        // assert
        assertNotSame(firstInstance, secondInstance, "Each call should return a new instance");
    }

    @Test
    void testCreateCriteriaInternal_ReturnsCriteriaWithEmptyList() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertNotNull(result.getCriteria(), "Criteria list should not be null");
        assertTrue(result.getCriteria().isEmpty(), "Criteria list should be empty initially");
    }

    @Test
    void testCreateCriteriaInternal_AfterOtherOperations() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        example.setDistinct(true);
        example.setOrderByClause("test");
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertNotNull(result, "Should still return valid Criteria after other operations");
    }

    @Test
    void testCreateCriteriaInternal_ReturnsProperType() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = spy(new ScrmAIAgentTestGroupRecordDOExample());
        // act
        ScrmAIAgentTestGroupRecordDOExample.Criteria result = example.createCriteriaInternal();
        // assert
        verify(example, times(1)).createCriteriaInternal();
        assertNotNull(result, "Result should not be null");
        assertEquals(0, result.getCriteria().size(), "New Criteria should have empty criteria list");
    }

    @Test
    public void testPageNormalCase() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int page = 2;
        int pageSize = 10;
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.page(page, pageSize);
        // assert
        assertEquals(page * pageSize, example.getOffset());
        assertEquals(pageSize, example.getRows());
        assertSame(example, result);
    }

    @Test
    public void testPageFirstPageCase() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int page = 0;
        int pageSize = 10;
        // act
        example.page(page, pageSize);
        // assert
        assertEquals(0, example.getOffset());
        assertEquals(pageSize, example.getRows());
    }

    @Test
    public void testPageLargeNumberCase() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int page = Integer.MAX_VALUE / 100;
        int pageSize = 100;
        // act
        example.page(page, pageSize);
        // assert
        assertEquals((Integer.MAX_VALUE / 100) * 100, example.getOffset());
        assertEquals(pageSize, example.getRows());
    }

    @Test
    public void testPageNullPageCase() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        Integer page = null;
        int pageSize = 10;
        // act & assert
        assertThrows(NullPointerException.class, () -> example.page(page, pageSize));
    }

    @Test
    public void testPageNullPageSizeCase() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int page = 1;
        Integer pageSize = null;
        // act & assert
        assertThrows(NullPointerException.class, () -> example.page(page, pageSize));
    }

    @Test
    public void testPageZeroPageSizeCase() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int page = 1;
        int pageSize = 0;
        // act
        example.page(page, pageSize);
        // assert
        assertEquals(0, example.getOffset());
        assertEquals(0, example.getRows());
    }

    @Test
    public void testPageNegativePageCase() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        int page = -1;
        int pageSize = 10;
        // act
        example.page(page, pageSize);
        // assert
        assertEquals(-10, example.getOffset());
        assertEquals(10, example.getRows());
    }

    @Test
    public void testPageMethodChaining() throws Throwable {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.page(1, 10).page(2, 20);
        // assert
        assertEquals(40, example.getOffset());
        assertEquals(20, example.getRows());
        assertSame(example, result);
    }

    @Test
    @DisplayName("Should set rows with positive number and return this")
    void testLimitWithPositiveNumber() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(10);
        // assert
        assertEquals(10, result.getRows(), "rows值应被设置为10");
        assertSame(example, result, "应返回同一个实例");
    }

    @Test
    @DisplayName("Should set rows with zero and return this")
    void testLimitWithZero() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(0);
        // assert
        assertEquals(0, result.getRows(), "rows值应被设置为0");
        assertSame(example, result, "应返回同一个实例");
    }

    @Test
    @DisplayName("Should set rows with MAX_VALUE and return this")
    void testLimitWithMaxValue() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(Integer.MAX_VALUE);
        // assert
        assertEquals(Integer.MAX_VALUE, result.getRows(), "rows值应被设置为Integer.MAX_VALUE");
        assertSame(example, result, "应返回同一个实例");
    }

    @Test
    @DisplayName("Should set rows with null and return this")
    void testLimitWithNull() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(null);
        // assert
        assertNull(result.getRows(), "rows值应被设置为null");
        assertSame(example, result, "应返回同一个实例");
    }

    @Test
    @DisplayName("Should set rows with negative number and return this")
    void testLimitWithNegativeNumber() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = new ScrmAIAgentTestGroupRecordDOExample();
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(-1);
        // assert
        assertEquals(-1, result.getRows(), "rows值应被设置为-1");
        assertSame(example, result, "应返回同一个实例");
    }

    @Test
    @DisplayName("Should demonstrate Mockito usage (though not needed for this simple case)")
    void testLimitWithMockitoDemonstration() {
        // arrange
        ScrmAIAgentTestGroupRecordDOExample example = spy(new ScrmAIAgentTestGroupRecordDOExample());
        // act
        ScrmAIAgentTestGroupRecordDOExample result = example.limit(5);
        // assert
        assertEquals(5, result.getRows());
        // 验证方法被调用
        verify(example).limit(5);
    }
}
