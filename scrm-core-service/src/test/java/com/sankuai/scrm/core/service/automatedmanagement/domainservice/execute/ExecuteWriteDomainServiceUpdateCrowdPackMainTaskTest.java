package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.google.common.hash.BloomFilter;
import com.google.common.hash.Funnels;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.config.ConfigDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.AmLionConfigDTO;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.UpdateAllCrowdPackMessageProducer;
import com.sankuai.scrm.core.service.user.dal.entity.ext.ExtScrmUserTag;
import com.sankuai.scrm.core.service.user.dal.example.ScrmUserTagExample;
import com.sankuai.scrm.core.service.user.dal.mapper.ext.ExtScrmUserTagMapper;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class ExecuteWriteDomainServiceUpdateCrowdPackMainTaskTest {

    @Mock
    private ConfigDomainService configDomainService;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private UpdateAllCrowdPackMessageProducer updateAllCrowdPackMessageProducer;

    @Mock
    private ExtScrmUserTagMapper userTagDOMapper;

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    private AmLionConfigDTO amLionConfigDTO;

    @BeforeEach
    public void setUp() {
        amLionConfigDTO = new AmLionConfigDTO();
        amLionConfigDTO.setMaxCrowdOfEachCorpSize(10000);
        when(configDomainService.getAmLionConfigDTO()).thenReturn(amLionConfigDTO);
    }

    /**
     * 测试任务不能运行时（taskRunFirstTime 返回 false），应直接返回并调用 taskRunFinished
     */
    @Test
    public void testUpdateCrowdPackMainTask_TaskCannotRun() throws Throwable {
        // arrange
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L)).thenReturn(false);
        // act
        executeWriteDomainService.updateCrowdPackMainTask();
        // assert
        verify(executeManagementService).taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L);
        verify(userTagDOMapper, never()).selectUserUnionIdByExample(any());
        verify(updateAllCrowdPackMessageProducer, never()).sendCrowdPackUpdateTaskExecuteMessage(anyList());
        // taskRunFinished is always called in finally, so it should be called once
        verify(executeManagementService).taskRunFinished(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L);
    }

    /**
     * 测试首批查询无用户 unionId 时，抛出 IndexOutOfBoundsException
     */
    @Test
    public void testUpdateCrowdPackMainTask_NoUserUnionIdsFound() throws Throwable {
        // arrange
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L)).thenReturn(true);
        when(userTagDOMapper.selectUserUnionIdByExample(any())).thenReturn(Collections.emptyList());
        // act & assert
        assertThrows(IndexOutOfBoundsException.class, () -> executeWriteDomainService.updateCrowdPackMainTask());
        verify(executeManagementService).taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L);
        verify(userTagDOMapper).selectUserUnionIdByExample(any());
        verify(updateAllCrowdPackMessageProducer, never()).sendCrowdPackUpdateTaskExecuteMessage(anyList());
        verify(executeManagementService).taskRunFinished(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L);
    }

    /**
     * 测试只有一批用户 unionId（小于 2000）时的正常流程
     */
    @Test
    public void testUpdateCrowdPackMainTask_SingleBatch() throws Throwable {
        // arrange
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L)).thenReturn(true);
        List<ExtScrmUserTag> userTags = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            ExtScrmUserTag tag = new ExtScrmUserTag();
            tag.setId((long) i);
            tag.setUnionId("unionId" + i);
            userTags.add(tag);
        }
        when(userTagDOMapper.selectUserUnionIdByExample(any())).thenReturn(userTags);
        // act
        executeWriteDomainService.updateCrowdPackMainTask();
        // assert
        verify(executeManagementService).taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L);
        ArgumentCaptor<ScrmUserTagExample> exampleCaptor = ArgumentCaptor.forClass(ScrmUserTagExample.class);
        verify(userTagDOMapper).selectUserUnionIdByExample(exampleCaptor.capture());
        ScrmUserTagExample capturedExample = exampleCaptor.getValue();
        assertEquals(0, capturedExample.getOffset());
        assertEquals(2000, capturedExample.getRows());
        ArgumentCaptor<List<String>> unionIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(updateAllCrowdPackMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(unionIdsCaptor.capture());
        List<String> capturedUnionIds = unionIdsCaptor.getValue();
        assertEquals(100, capturedUnionIds.size());
        assertTrue(capturedUnionIds.contains("unionId1"));
        assertTrue(capturedUnionIds.contains("unionId100"));
        verify(executeManagementService).taskRunFinished(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L);
    }

    /**
     * 测试多批次 unionId（首批 2000，第二批 500），循环终止于第二批
     */
    @Test
    public void testUpdateCrowdPackMainTask_MultipleBatches() throws Throwable {
        // arrange
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L)).thenReturn(true);
        // First batch - full 2000 records
        List<ExtScrmUserTag> firstBatch = new ArrayList<>();
        for (int i = 1; i <= 2000; i++) {
            ExtScrmUserTag tag = new ExtScrmUserTag();
            tag.setId((long) i);
            tag.setUnionId("unionId" + i);
            firstBatch.add(tag);
        }
        // Second batch - partial records
        List<ExtScrmUserTag> secondBatch = new ArrayList<>();
        for (int i = 2001; i <= 2500; i++) {
            ExtScrmUserTag tag = new ExtScrmUserTag();
            tag.setId((long) i);
            tag.setUnionId("unionId" + i);
            secondBatch.add(tag);
        }
        // Only two batches: after second batch, loopFlag will be false and break
        when(userTagDOMapper.selectUserUnionIdByExample(any())).thenReturn(firstBatch).thenReturn(secondBatch);
        // act
        executeWriteDomainService.updateCrowdPackMainTask();
        // assert
        verify(executeManagementService).taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L);
        verify(userTagDOMapper, times(2)).selectUserUnionIdByExample(any());
        ArgumentCaptor<List<String>> unionIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(updateAllCrowdPackMessageProducer, times(2)).sendCrowdPackUpdateTaskExecuteMessage(unionIdsCaptor.capture());
        List<List<String>> capturedUnionIdsList = unionIdsCaptor.getAllValues();
        assertEquals(2, capturedUnionIdsList.size());
        // First batch should have 2000 union IDs
        assertEquals(2000, capturedUnionIdsList.get(0).size());
        // Second batch should have 500 union IDs
        assertEquals(500, capturedUnionIdsList.get(1).size());
        verify(executeManagementService).taskRunFinished(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L);
    }

    /**
     * 测试 BloomFilter 过滤掉所有 unionId（全部已处理）
     */
    @Test
    public void testUpdateCrowdPackMainTask_AllUnionIdsFiltered() throws Throwable {
        // arrange
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L)).thenReturn(true);
        List<ExtScrmUserTag> userTags = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            ExtScrmUserTag tag = new ExtScrmUserTag();
            tag.setId((long) i);
            tag.setUnionId("unionId" + i);
            userTags.add(tag);
        }
        when(userTagDOMapper.selectUserUnionIdByExample(any())).thenReturn(userTags);
        // act
        executeWriteDomainService.updateCrowdPackMainTask();
        // assert
        verify(executeManagementService).taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L);
        verify(userTagDOMapper).selectUserUnionIdByExample(any());
        // Producer is called with a non-empty list, but the filter logic is not mockable, so we just check the call
        verify(updateAllCrowdPackMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(anyList());
        verify(executeManagementService).taskRunFinished(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L);
    }

    /**
     * 测试处理过程中抛出异常时，异常会被抛出，finally 依然会调用 taskRunFinished
     */
    @Test
    public void testUpdateCrowdPackMainTask_ExceptionDuringProcessing() throws Throwable {
        // arrange
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L)).thenReturn(true);
        when(userTagDOMapper.selectUserUnionIdByExample(any())).thenThrow(new RuntimeException("Database error"));
        // act & assert
        RuntimeException ex = assertThrows(RuntimeException.class, () -> executeWriteDomainService.updateCrowdPackMainTask());
        assertEquals("Database error", ex.getMessage());
        verify(executeManagementService).taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L);
        verify(executeManagementService).taskRunFinished(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L);
    }

    /**
     * 测试第二批为空时，循环终止
     */
    @Test
    public void testUpdateCrowdPackMainTask_EmptySecondBatch() throws Throwable {
        // arrange
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L)).thenReturn(true);
        // First batch with data
        List<ExtScrmUserTag> firstBatch = new ArrayList<>();
        for (int i = 1; i <= 100; i++) {
            ExtScrmUserTag tag = new ExtScrmUserTag();
            tag.setId((long) i);
            tag.setUnionId("unionId" + i);
            firstBatch.add(tag);
        }
        // Second batch empty, to break the loop
        List<ExtScrmUserTag> secondBatch = Collections.emptyList();
        // Only one call will be made, because after the first batch, userUnionIds.size() < 2000, so loopFlag is false and loop breaks
        when(userTagDOMapper.selectUserUnionIdByExample(any())).thenReturn(firstBatch);
        // act
        executeWriteDomainService.updateCrowdPackMainTask();
        // assert
        verify(executeManagementService).taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L);
        // Only called once: first for data, then loopFlag is false and loop breaks
        verify(userTagDOMapper, times(1)).selectUserUnionIdByExample(any());
        ArgumentCaptor<List<String>> unionIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(updateAllCrowdPackMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(unionIdsCaptor.capture());
        List<String> capturedUnionIds = unionIdsCaptor.getValue();
        assertEquals(100, capturedUnionIds.size());
        verify(executeManagementService).taskRunFinished(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L);
    }

    /**
     * 测试首批正好 2000 条，第二批为空时，抛出 IndexOutOfBoundsException
     */
    @Test
    public void testUpdateCrowdPackMainTask_ExactlyMaxBatchSize() throws Throwable {
        // arrange
        when(executeManagementService.taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L)).thenReturn(true);
        // First batch - exactly 2000 records
        List<ExtScrmUserTag> firstBatch = new ArrayList<>();
        for (int i = 1; i <= 2000; i++) {
            ExtScrmUserTag tag = new ExtScrmUserTag();
            tag.setId((long) i);
            tag.setUnionId("unionId" + i);
            firstBatch.add(tag);
        }
        // Second batch - empty, to break the loop
        List<ExtScrmUserTag> secondBatch = Collections.emptyList();
        // After first batch, userUnionIds.size() == 2000, so loopFlag is true, so second call is made and returns empty, then originMaxId = userUnionIds.get(userUnionIds.size() - 1).getId() throws IndexOutOfBoundsException
        when(userTagDOMapper.selectUserUnionIdByExample(any())).thenReturn(firstBatch).thenReturn(secondBatch);
        // act & assert
        assertThrows(IndexOutOfBoundsException.class, () -> executeWriteDomainService.updateCrowdPackMainTask());
        verify(executeManagementService).taskRunFirstTime(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L);
        verify(userTagDOMapper, times(2)).selectUserUnionIdByExample(any());
        ArgumentCaptor<List<String>> unionIdsCaptor = ArgumentCaptor.forClass(List.class);
        verify(updateAllCrowdPackMessageProducer).sendCrowdPackUpdateTaskExecuteMessage(unionIdsCaptor.capture());
        List<String> capturedUnionIds = unionIdsCaptor.getValue();
        assertEquals(2000, capturedUnionIds.size());
        verify(executeManagementService).taskRunFinished(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue(), 0L);
    }
}
