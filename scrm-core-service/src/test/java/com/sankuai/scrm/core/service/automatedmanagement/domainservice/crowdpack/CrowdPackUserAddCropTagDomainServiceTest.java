package com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONException;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.dz.srcm.automatedmanagement.request.CrowdPackUserAddCorpTagRequest;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackAddCorpTagLog;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackDetailInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackAddCorpTagLogMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackDetailInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.mq.entity.ScrmPackUserBatchAddCorpTagMessage;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.ScrmPackUserBatchAddCorpTagProducer;
import org.junit.*;
import org.junit.runner.RunWith.*;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CrowdPackUserAddCropTagDomainServiceTest {

    @InjectMocks
    private CrowdPackUserAddCropTagDomainService crowdPackUserAddCropTagDomainService;

    @Mock(lenient = true)
    private ScrmAmCrowdPackAddCorpTagLogMapper addCorpTagLogMapper;

    @Mock(lenient = true)
    private ExtScrmAmCrowdPackDetailInfoDOMapper detailInfoDOMapper;

    @Mock(lenient = true)
    private ScrmPackUserBatchAddCorpTagProducer scrmPackUserBatchAddCorpTagProducer;

    private ScrmAmCrowdPackAddCorpTagLog crowdPackAddCorpTagLog;

    public CrowdPackUserAddCropTagDomainServiceTest() {
        MockitoAnnotations.initMocks(this);
    }

    @Before
    public void setUp() {
        crowdPackAddCorpTagLog = new ScrmAmCrowdPackAddCorpTagLog();
    }

    private CrowdPackUserAddCorpTagRequest createRequest() {
        CrowdPackUserAddCorpTagRequest request = new CrowdPackUserAddCorpTagRequest();
        request.setPackId(1L);
        request.setTagId("tagId");
        request.setTagGroupId("tagGroupId");
        request.setAppId("appId");
        request.setMisId("misId");
        return request;
    }

    private ScrmCrowdPackDTO createScrmCrowdPackDTO() {
        ScrmCrowdPackDTO scrmCrowdPackDTO = new ScrmCrowdPackDTO();
        scrmCrowdPackDTO.setCrowdCount(10L);
        return scrmCrowdPackDTO;
    }

    private ScrmAmCrowdPackDetailInfoDO createDetailInfoDO() {
        ScrmAmCrowdPackDetailInfoDO detailInfoDO = new ScrmAmCrowdPackDetailInfoDO();
        detailInfoDO.setId(1L);
        detailInfoDO.setPackId(1L);
        return detailInfoDO;
    }

    @Test(expected = JSONException.class)
    public void testHandleCrowdPackUserBatchAddCorpTagMessageMessageParsingFailed() throws Throwable {
        String message = "invalid message";
        crowdPackUserAddCropTagDomainService.handleCrowdPackUserBatchAddCorpTagMessage(message);
    }

    @Test
    public void testHandleCrowdPackUserBatchAddCorpTagMessageNormal() throws Throwable {
        // Prepare test data
        String message = "{\"packId\":1,\"packAddTagLogId\":1,\"currentPackDetailId\":1}";
        // Mock addCorpTagLogMapper behavior
        ScrmAmCrowdPackAddCorpTagLog mockLog = new ScrmAmCrowdPackAddCorpTagLog();
        mockLog.setId(1L);
        mockLog.setPackId(1L);
        mockLog.setAppId("testAppId");
        mockLog.setCorpTagId("testTagId");
        when(addCorpTagLogMapper.selectByPrimaryKey(1L)).thenReturn(mockLog);
        // Call the method under test
        crowdPackUserAddCropTagDomainService.handleCrowdPackUserBatchAddCorpTagMessage(message);
        // Verify the mapper was called
        verify(addCorpTagLogMapper, times(1)).selectByPrimaryKey(1L);
    }

    @Test
    public void testHandleCrowdPackUserBatchAddCorpTagMessageMessageIsNull() throws Throwable {
        String message = null;
        crowdPackUserAddCropTagDomainService.handleCrowdPackUserBatchAddCorpTagMessage(message);
        verify(addCorpTagLogMapper, never()).selectByPrimaryKey(anyLong());
    }

    @Test
    public void testHandleCrowdPackUserBatchAddCorpTagMessageFieldIsNull() throws Throwable {
        String message = "{\"packId\":null,\"packAddTagLogId\":null,\"currentPackDetailId\":null}";
        crowdPackUserAddCropTagDomainService.handleCrowdPackUserBatchAddCorpTagMessage(message);
        verify(addCorpTagLogMapper, never()).selectByPrimaryKey(anyLong());
    }

    @Test
    public void testInsertPackUserBatchAddCorpTagLogAndSendMsg_Normal() throws Throwable {
        // Prepare test data
        CrowdPackUserAddCorpTagRequest request = createRequest();
        ScrmCrowdPackDTO scrmCrowdPackDTO = createScrmCrowdPackDTO();
        ScrmAmCrowdPackDetailInfoDO detailInfoDO = createDetailInfoDO();
        // Configure mocks
        when(detailInfoDOMapper.getPackDetailInfoByPackId(any())).thenReturn(java.util.Collections.singletonList(10L));
        // Capture the log object and set its ID
        ArgumentCaptor<ScrmAmCrowdPackAddCorpTagLog> logCaptor = ArgumentCaptor.forClass(ScrmAmCrowdPackAddCorpTagLog.class);
        when(addCorpTagLogMapper.insert(logCaptor.capture())).thenAnswer(invocation -> {
            ScrmAmCrowdPackAddCorpTagLog log = logCaptor.getValue();
            log.setId(1L);
            return 1;
        });
        when(scrmPackUserBatchAddCorpTagProducer.sendAsyncDelayMessage(any(), anyLong())).thenReturn(true);
        // Execute
        boolean result = crowdPackUserAddCropTagDomainService.insertPackUserBatchAddCorpTagLogAndSendMsg(request, scrmCrowdPackDTO);
        // Verify
        assertTrue(result);
        verify(scrmPackUserBatchAddCorpTagProducer).sendAsyncDelayMessage(any(), anyLong());
    }

    @Test
    public void testInsertPackUserBatchAddCorpTagLogAndSendMsg_SendPackUserBatchAddCorpTagMessageThrowException() throws Throwable {
        // Prepare test data
        CrowdPackUserAddCorpTagRequest request = createRequest();
        ScrmCrowdPackDTO scrmCrowdPackDTO = createScrmCrowdPackDTO();
        ScrmAmCrowdPackDetailInfoDO detailInfoDO = createDetailInfoDO();
        // Configure mocks
        when(detailInfoDOMapper.getPackDetailInfoByPackId(any())).thenReturn(java.util.Collections.singletonList(10L));
        // Capture the log object and set its ID
        ArgumentCaptor<ScrmAmCrowdPackAddCorpTagLog> logCaptor = ArgumentCaptor.forClass(ScrmAmCrowdPackAddCorpTagLog.class);
        when(addCorpTagLogMapper.insert(logCaptor.capture())).thenAnswer(invocation -> {
            ScrmAmCrowdPackAddCorpTagLog log = logCaptor.getValue();
            log.setId(1L);
            return 1;
        });
        when(scrmPackUserBatchAddCorpTagProducer.sendAsyncDelayMessage(any(), anyLong())).thenReturn(false);
        // Execute
        boolean result = crowdPackUserAddCropTagDomainService.insertPackUserBatchAddCorpTagLogAndSendMsg(request, scrmCrowdPackDTO);
        // Verify
        assertTrue(result);
        verify(scrmPackUserBatchAddCorpTagProducer).sendAsyncDelayMessage(any(), anyLong());
    }

    @Test
    public void testInsertPackUserBatchAddCorpTagLogAndSendMsg_GetFirstPackDetailInfoByPackIdReturnNull() throws Throwable {
        CrowdPackUserAddCorpTagRequest request = createRequest();
        ScrmCrowdPackDTO scrmCrowdPackDTO = createScrmCrowdPackDTO();
        when(detailInfoDOMapper.selectByExample(any())).thenReturn(null);
        boolean result = crowdPackUserAddCropTagDomainService.insertPackUserBatchAddCorpTagLogAndSendMsg(request, scrmCrowdPackDTO);
        assertFalse(result);
    }

    @Test
    public void testInsertPackUserBatchAddCorpTagLogAndSendMsg_InsertCrowdPackAddCorpTagLogReturnFalse() throws Throwable {
        CrowdPackUserAddCorpTagRequest request = createRequest();
        ScrmCrowdPackDTO scrmCrowdPackDTO = createScrmCrowdPackDTO();
        ScrmAmCrowdPackDetailInfoDO detailInfoDO = createDetailInfoDO();
        when(detailInfoDOMapper.selectByExample(any())).thenReturn(java.util.Collections.singletonList(detailInfoDO));
        when(addCorpTagLogMapper.insert(any())).thenReturn(0);
        boolean result = crowdPackUserAddCropTagDomainService.insertPackUserBatchAddCorpTagLogAndSendMsg(request, scrmCrowdPackDTO);
        assertFalse(result);
    }

    /**
     * 测试插入日志成功的情况
     */
    @Test
    public void testInsertCrowdPackAddCorpTagLogSuccess() {
        // arrange
        when(addCorpTagLogMapper.insert(crowdPackAddCorpTagLog)).thenReturn(1);
        // act
        boolean result = crowdPackUserAddCropTagDomainService.insertCrowdPackAddCorpTagLog(crowdPackAddCorpTagLog);
        // assert
        assertTrue(result);
        verify(addCorpTagLogMapper, times(1)).insert(crowdPackAddCorpTagLog);
    }

    /**
     * 测试插入日志失败的情况
     */
    @Test
    public void testInsertCrowdPackAddCorpTagLogFailure() {
        // arrange
        when(addCorpTagLogMapper.insert(crowdPackAddCorpTagLog)).thenReturn(0);
        // act
        boolean result = crowdPackUserAddCropTagDomainService.insertCrowdPackAddCorpTagLog(crowdPackAddCorpTagLog);
        // assert
        assertFalse(result);
        verify(addCorpTagLogMapper, times(1)).insert(crowdPackAddCorpTagLog);
    }
}
