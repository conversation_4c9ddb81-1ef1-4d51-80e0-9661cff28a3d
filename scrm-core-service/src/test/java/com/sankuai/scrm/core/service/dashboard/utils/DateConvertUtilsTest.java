package com.sankuai.scrm.core.service.dashboard.utils;

import com.meituan.service.mobile.mtthrift.util.json.JacksonUtils;
import com.sankuai.dz.srcm.flowV2.dto.EntryConfigDetailDTO;
import com.sankuai.dz.srcm.flowV2.request.EntryEditRequest;
import org.junit.Test;
import org.junit.Assert;
import java.time.LocalDate;
import java.time.ZoneId;
import java.util.Date;

public class DateConvertUtilsTest {

    /**
     * 测试将LocalDate转换为Date，正常情况
     */
    @Test
    public void testConvertLocalDateToDateNormal() {
        // arrange
        LocalDate localDate = LocalDate.of(2023, 4, 1);
        Date expected = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

        // act
        Date result = DateConvertUtils.convertLocalDateToDate(localDate);

        // assert
        Assert.assertEquals("转换后的Date对象与预期不符", expected, result);
    }

    /**
     * 测试将LocalDate转换为Date，使用系统默认时区的边界情况（时区变更日）
     */
    @Test
    public void testConvertLocalDateToDateBoundary() {
        // arrange
        // 假设在时区变更的日期进行测试，这里以美国夏令时开始的日期为例（例如：2023年3月12日）
        LocalDate localDate = LocalDate.of(2023, 3, 12);
        Date expected = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());

        // act
        Date result = DateConvertUtils.convertLocalDateToDate(localDate);

        // assert
        Assert.assertEquals("在时区变更日转换后的Date对象与预期不符", expected, result);
    }

    /**
     * 测试将LocalDate转换为Date，输入null
     */
    @Test(expected = NullPointerException.class)
    public void testConvertLocalDateToDateWithNull() {
        // arrange
        LocalDate localDate = null;

        // act
        DateConvertUtils.convertLocalDateToDate(localDate);

        // assert
        // 期望抛出NullPointerException
    }

    /**
     * 测试 getEndOfDay 方法，输入为当前日期
     */
    @Test
    public void testGetEndOfDayWithCurrentDate() {
        // arrange
        Date currentDate = new Date();
        LocalDate localDate = currentDate.toInstant().atZone(ZoneId.systemDefault()).toLocalDate();
        Date expected = Date.from(localDate.atTime(23, 59, 59, 999999999).atZone(ZoneId.systemDefault()).toInstant());

        // act
        Date result = DateConvertUtils.getEndOfDay(currentDate);

        // assert
        Assert.assertEquals("The end of day time does not match", expected, result);
    }

    /**
     * 测试 getEndOfDay 方法，输入为特定日期（例如：2000年1月1日）
     */
    @Test
    public void testGetEndOfDayWithSpecificDate() {
        // arrange
        LocalDate localDate = LocalDate.of(2000, 1, 1);
        Date specificDate = Date.from(localDate.atStartOfDay(ZoneId.systemDefault()).toInstant());
        Date expected = Date.from(localDate.atTime(23, 59, 59, 999999999).atZone(ZoneId.systemDefault()).toInstant());

        // act
        Date result = DateConvertUtils.getEndOfDay(specificDate);

        // assert
        Assert.assertEquals("The end of day time for 2000-01-01 does not match", expected, result);
    }

    /**
     * 测试 getEndOfDay 方法，输入为null
     */
    @Test(expected = NullPointerException.class)
    public void testGetEndOfDayWithNullInput() {
        // arrange
        Date nullDate = null;

        // act
        DateConvertUtils.getEndOfDay(nullDate);

        // assert is handled by the expected exception
    }
}
