package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationWxInvokeJobExecuteStatusTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationNodeTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeLogDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGetMomentSendResultAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGetMomentTaskResultAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxGetMomentSendResultRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGetMomentSendResultResponse;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGetMomentTaskResultResponse;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.dto.CustomerListDTO;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Collections;
import java.util.Date;
// 确保使用 java.util.List
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;
import java.util.stream.Collectors;
import org.assertj.core.internal.Arrays;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.stubbing.Answer;

@ExtendWith(MockitoExtension.class)
class StaffMomentTouchActionGetConditionNodeListFromProcessOrchestrationTest {

    @InjectMocks
    private StaffMomentTouchAction staffMomentTouchAction;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private WxGetMomentSendResultAcl wxGetMomentSendResultAcl;

    @Mock
    private WxGetMomentTaskResultAcl wxGetMomentTaskResultAcl;

    @Mock
    private InformationGatheringService informationGatheringService;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        processOrchestrationDTO.setAppId("testAppId");
    }

    /**
     * Test case for normal scenario where the method collects condition nodes until it reaches a root node.
     */
    @Test
    public void testGetConditionNodeListFromProcessOrchestrationNormalScenario() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmProcessOrchestrationNodeDTO node1 = new ScrmProcessOrchestrationNodeDTO();
        node1.setId(1L);
        node1.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_CONDITION.getValue());
        node1.setPreNodeId(2L);
        ScrmProcessOrchestrationNodeDTO node2 = new ScrmProcessOrchestrationNodeDTO();
        node2.setId(2L);
        node2.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ACTION.getValue());
        node2.setPreNodeId(3L);
        ScrmProcessOrchestrationNodeDTO node3 = new ScrmProcessOrchestrationNodeDTO();
        node3.setId(3L);
        node3.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ROOT.getValue());
        node3.setPreNodeId(null);
        java.util.List<ScrmProcessOrchestrationNodeDTO> nodeList = Lists.newArrayList(node1, node2, node3);
        // act
        java.util.List<ScrmProcessOrchestrationNodeDTO> result = action.getConditionNodeListFromProcessOrchestration(nodeList, node1);
        // assert
        assertEquals(1, result.size());
        assertEquals(node1, result.get(0));
    }

    /**
     * Test case for boundary scenario where the currentProcessingNode has a null id.
     */
    @Test
    public void testGetConditionNodeListFromProcessOrchestrationNullId() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmProcessOrchestrationNodeDTO node1 = new ScrmProcessOrchestrationNodeDTO();
        node1.setId(null);
        java.util.List<ScrmProcessOrchestrationNodeDTO> nodeList = Lists.newArrayList(node1);
        // act
        java.util.List<ScrmProcessOrchestrationNodeDTO> result = action.getConditionNodeListFromProcessOrchestration(nodeList, node1);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for exception scenario where the processOrchestrationNodeDTOList is null.
     */
    @Test
    public void testGetConditionNodeListFromProcessOrchestrationNullList() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmProcessOrchestrationNodeDTO node1 = new ScrmProcessOrchestrationNodeDTO();
        node1.setId(1L);
        // act & assert
        assertThrows(NullPointerException.class, () -> {
            action.getConditionNodeListFromProcessOrchestration(null, node1);
        });
    }

    /**
     * Test case for edge scenario where the currentProcessingNode is already a root node.
     */
    @Test
    public void testGetConditionNodeListFromProcessOrchestrationRootNode() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmProcessOrchestrationNodeDTO node1 = new ScrmProcessOrchestrationNodeDTO();
        node1.setId(1L);
        node1.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ROOT.getValue());
        java.util.List<ScrmProcessOrchestrationNodeDTO> nodeList = Lists.newArrayList(node1);
        // act
        java.util.List<ScrmProcessOrchestrationNodeDTO> result = action.getConditionNodeListFromProcessOrchestration(nodeList, node1);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for edge scenario where the currentProcessingNode is the only node in the list and it's not a condition node.
     */
    @Test
    public void testGetConditionNodeListFromProcessOrchestrationSingleNodeNotCondition() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmProcessOrchestrationNodeDTO node1 = new ScrmProcessOrchestrationNodeDTO();
        node1.setId(1L);
        node1.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ACTION.getValue());
        java.util.List<ScrmProcessOrchestrationNodeDTO> nodeList = Lists.newArrayList(node1);
        // act
        java.util.List<ScrmProcessOrchestrationNodeDTO> result = action.getConditionNodeListFromProcessOrchestration(nodeList, node1);
        // assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for edge scenario where the currentProcessingNode is a condition node and the next node is a root node.
     */
    @Test
    public void testGetConditionNodeListFromProcessOrchestrationConditionNodeNextIsRoot() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmProcessOrchestrationNodeDTO node1 = new ScrmProcessOrchestrationNodeDTO();
        node1.setId(1L);
        node1.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_CONDITION.getValue());
        node1.setPreNodeId(2L);
        ScrmProcessOrchestrationNodeDTO node2 = new ScrmProcessOrchestrationNodeDTO();
        node2.setId(2L);
        node2.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_ROOT.getValue());
        node2.setPreNodeId(null);
        java.util.List<ScrmProcessOrchestrationNodeDTO> nodeList = Lists.newArrayList(node1, node2);
        // act
        java.util.List<ScrmProcessOrchestrationNodeDTO> result = action.getConditionNodeListFromProcessOrchestration(nodeList, node1);
        // assert
        assertEquals(1, result.size());
        assertEquals(node1, result.get(0));
    }

    /**
     * Test case for edge scenario where the currentProcessingNode is a condition node and the next node does not exist in the map.
     */
    @Test
    public void testGetConditionNodeListFromProcessOrchestrationConditionNodeNextNodeNotInMap() throws Throwable {
        // arrange
        StaffMomentTouchAction action = new StaffMomentTouchAction();
        ScrmProcessOrchestrationNodeDTO node1 = new ScrmProcessOrchestrationNodeDTO();
        node1.setId(1L);
        node1.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_CONDITION.getValue());
        // PreNodeId points to a non-existent node
        node1.setPreNodeId(2L);
        java.util.List<ScrmProcessOrchestrationNodeDTO> nodeList = Lists.newArrayList(node1);
        // act
        java.util.List<ScrmProcessOrchestrationNodeDTO> result = action.getConditionNodeListFromProcessOrchestration(nodeList, node1);
        // assert
        assertEquals(1, result.size());
        assertEquals(node1, result.get(0));
    }

    /**
     * Test case when tryStartStatusCheck returns false
     */
    @Test
    public void testCheckStatus_WhenTryStartStatusCheckReturnsFalse() throws Throwable {
        when(executeManagementService.tryStartStatusCheck(anyLong())).thenReturn(false);
        staffMomentTouchAction.checkStatus(processOrchestrationDTO);
        verify(executeManagementService).tryStartStatusCheck(1L);
        verifyNoMoreInteractions(executeManagementService, wxInvokeLogDOMapper);
    }

    /**
     * Test case when no invoke logs are found
     */
    @Test
    public void testCheckStatus_WhenNoInvokeLogsFound() throws Throwable {
        when(executeManagementService.tryStartStatusCheck(anyLong())).thenReturn(true);
        when(wxInvokeLogDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        staffMomentTouchAction.checkStatus(processOrchestrationDTO);
        verify(executeManagementService).tryStartStatusCheck(1L);
        verify(wxInvokeLogDOMapper).selectByExample(any());
        verifyNoMoreInteractions(executeManagementService, wxInvokeLogDOMapper);
    }
}
