package com.sankuai.scrm.core.service.realtime.task.mq.producer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineABTestRecordMessageDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Field;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

@ExtendWith(MockitoExtension.class)
class GroupRetailAiEngineABTestRecordMessageProducerTest {

    @Mock
    private IProducerProcessor producer;

    @InjectMocks
    private GroupRetailAiEngineABTestRecordMessageProducer messageProducer;

    private GroupRetailAiEngineABTestRecordMessageDTO testMessage;

    @BeforeEach
    void setUp() throws Exception {
        testMessage = new GroupRetailAiEngineABTestRecordMessageDTO();
        testMessage.setMtUserId(12345L);
        testMessage.setAppId("testApp");
        testMessage.setTestVersion("v1.0");
        // Use reflection to set the private static producer field
        Field producerField = GroupRetailAiEngineABTestRecordMessageProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, producer);
    }

    /**
     * 测试发送消息成功场景
     */
    @Test
    void testSendMessageSuccess() throws Throwable {
        // arrange
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(producer.sendMessage(anyString())).thenReturn(successResult);
        // act
        messageProducer.sendMessage(testMessage);
        // assert
        verify(producer, times(1)).sendMessage(anyString());
    }

    /**
     * 测试输入参数为null的场景
     */
    @Test
    void testSendMessageNullInput() throws Throwable {
        // act
        messageProducer.sendMessage(null);
        // assert
        verify(producer, never()).sendMessage(anyString());
    }

    /**
     * 测试序列化失败抛出异常的场景
     */
    @Test
    void testSendMessageSerializationFailure() throws Throwable {
        // arrange
        when(producer.sendMessage(anyString())).thenThrow(new RuntimeException("Serialization failed"));
        // act
        messageProducer.sendMessage(testMessage);
        // assert
        verify(producer, times(3)).sendMessage(anyString());
    }

    /**
     * 测试重试后最终成功的场景
     */
    @Test
    void testSendMessageRetryThenSuccess() throws Throwable {
        // arrange
        ProducerResult failureResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(producer.sendMessage(anyString())).thenThrow(new RuntimeException("First attempt failed")).thenReturn(failureResult).thenReturn(successResult);
        // act
        messageProducer.sendMessage(testMessage);
        // assert
        verify(producer, times(3)).sendMessage(anyString());
    }

    /**
     * 测试所有重试都失败的场景
     */
    @Test
    void testSendMessageAllRetriesFailed() throws Throwable {
        // arrange
        ProducerResult failureResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        when(producer.sendMessage(anyString())).thenReturn(failureResult);
        // act
        messageProducer.sendMessage(testMessage);
        // assert
        verify(producer, times(3)).sendMessage(anyString());
    }

    /**
     * 测试Producer返回非OK状态的场景
     */
    @Test
    void testSendMessageNonOkStatus() throws Throwable {
        // arrange
        ProducerResult unsupportedResult = new ProducerResult(ProducerStatus.SEND_UNSUPPORTED);
        when(producer.sendMessage(anyString())).thenReturn(unsupportedResult);
        // act
        messageProducer.sendMessage(testMessage);
        // assert
        verify(producer, times(3)).sendMessage(anyString());
    }
}
