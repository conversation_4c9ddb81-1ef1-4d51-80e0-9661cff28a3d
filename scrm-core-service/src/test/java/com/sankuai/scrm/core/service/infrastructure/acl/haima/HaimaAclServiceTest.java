package com.sankuai.scrm.core.service.infrastructure.acl.haima;

import com.dianping.appkit.constants.BizGroup;
import com.dianping.haima.client.HaimaClient;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import com.dianping.haima.entity.haima.HaimaConfig;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.mockito.ArgumentMatchers.any;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class HaimaAclServiceTest {

    @InjectMocks
    private HaimaAclService haimaAclService;

    @Mock(lenient = true)
    private HaimaClient haimaClient;

    private String key;

    private Map<String, String> params;

    private Class<HaimaConfig> clazz;

    @Before
    public void setUp() {
        key = "testKey";
        params = new HashMap<>();
        params.put("key1", "value1");
        clazz = HaimaConfig.class;
    }

    @Test
    public void testGetConfigListByParamsKeyIsNull() throws Throwable {
        key = null;
        List<HaimaConfig> result = haimaAclService.getConfigListByParams(key, params, clazz);
        assertNull(result);
    }

    @Test
    public void testGetConfigListByParamsParamsIsNull() throws Throwable {
        params = null;
        List<HaimaConfig> result = haimaAclService.getConfigListByParams(key, params, clazz);
        assertNull(result);
    }

    @Test
    public void testGetConfigListByParamsParamsIsEmpty() throws Throwable {
        params.clear();
        List<HaimaConfig> result = haimaAclService.getConfigListByParams(key, params, clazz);
        assertNull(result);
    }

    @Test
    public void testGetConfigListByParamsGetConfigListReturnsNull() throws Throwable {
        when(haimaClient.query(any(HaimaRequest.class))).thenReturn(null);
        List<HaimaConfig> result = haimaAclService.getConfigListByParams(key, params, clazz);
        assertNull(result);
    }

    @Test
    public void testGetConfigListByParamsGetConfigListReturnsEmptyList() throws Throwable {
        HaimaResponse haimaResponse = new HaimaResponse(true, new ArrayList<>(), "success");
        when(haimaClient.query(any(HaimaRequest.class))).thenReturn(haimaResponse);
        List<HaimaConfig> result = haimaAclService.getConfigListByParams(key, params, clazz);
        assertNull(result);
    }

    @Test
    public void testGetConfigListByParamsGetConfigListReturnsNonEmptyList() throws Throwable {
        List<HaimaConfig> haimaConfigList = new ArrayList<>();
        HaimaConfig haimaConfig = new HaimaConfig();
        // Ensure the HaimaConfig object has a non-empty list of contents
        haimaConfig.setContents(java.util.Collections.singletonList(new com.dianping.haima.entity.haima.HaimaContent()));
        haimaConfigList.add(haimaConfig);
        HaimaResponse haimaResponse = new HaimaResponse(true, haimaConfigList, "success");
        when(haimaClient.query(any(HaimaRequest.class))).thenReturn(haimaResponse);
        List<HaimaConfig> result = haimaAclService.getConfigListByParams(key, params, clazz);
        assertNotNull(result);
        assertFalse(result.isEmpty());
    }
}
