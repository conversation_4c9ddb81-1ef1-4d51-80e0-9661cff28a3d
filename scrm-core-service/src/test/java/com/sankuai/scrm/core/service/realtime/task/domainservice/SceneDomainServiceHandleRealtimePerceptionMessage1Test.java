package com.sankuai.scrm.core.service.realtime.task.domainservice;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.meituan.service.mobile.group.geo.bean.CityInfo;
import com.meituan.service.mobile.group.geo.service.CityService;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ExecuteResultDTO;
import com.sankuai.dz.srcm.automatedmanagement.service.ScrmRefinementOperationBackEndService;
import com.sankuai.dz.srcm.realtime.task.dto.ExtendFieldsDTO;
import com.sankuai.dz.srcm.realtime.task.dto.RealtimePerceptionMessage;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmSceneProcessPriorityDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.service.impl.ScrmRefinementOperationBackEndServiceImpl;
import com.sankuai.scrm.core.service.external.contact.dal.babymapper.ContactUserMapper;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.config.CorpIdCategoryIdConfig;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmRealtimeSceneDO;
import com.sankuai.scrm.core.service.realtime.task.dal.entity.ScrmRealtimeSceneUserRecordDO;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmRealtimeSceneDOMapper;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmRealtimeSceneUserRecordDOMapper;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class SceneDomainServiceHandleRealtimePerceptionMessage1Test {

    @InjectMocks
    private SceneDomainService sceneDomainService;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    @Spy
    private ScrmRefinementOperationBackEndServiceImpl scrmRefinementOperationBackEndService;

    @Mock
    private ContactUserMapper contactUserMapper;

    @Mock
    private ScrmRealtimeSceneDOMapper scrmRealtimeSceneDOMapper;

    @Mock
    private ScrmRealtimeSceneUserRecordDOMapper scrmRealtimeSceneUserRecordDOMapper;

    @Mock
    private ScrmAmSceneProcessPriorityDOMapper sceneProcessPriorityDOMapper;

    @Mock
    private CityService cityService;

    private static final String TEST_APP_ID = "wx_zongfa";

    private static final String TEST_UNION_ID = "testUnionId";

    private static final String TEST_CITY_ID = "1";

    private static final String TEST_CORP_ID = "ww975965cae5f5138f";

    @Before
    public void setUp() {
        // Setup CityInfo with proper adCode
        CityInfo cityInfo = new CityInfo();
        cityInfo.setAdCode(TEST_CITY_ID);
        // Setup CorpAppConfig with proper corpId and appId
        CorpAppConfig corpAppConfig = new CorpAppConfig();
        corpAppConfig.setCorpId(TEST_CORP_ID);
        corpAppConfig.setAppId(TEST_APP_ID);
        when(corpAppConfigRepository.getConfigByAppId(TEST_APP_ID)).thenReturn(corpAppConfig);
        // Setup ContactUser with proper status and matching corpId
        List<ContactUser> contactUsers = new ArrayList<>();
        ContactUser contactUser = new ContactUser();
        contactUser.setCorpId(TEST_CORP_ID);
        contactUser.setUnionId(TEST_UNION_ID);
        contactUser.setStatus(1);
        contactUsers.add(contactUser);
        when(contactUserMapper.selectByExample(any())).thenReturn(contactUsers);
        // Setup UnionId
        when(mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(anyLong(), anyString())).thenReturn(TEST_UNION_ID);
        // Setup ExecuteResultDTO
        ExecuteResultDTO executeResultDTO = new ExecuteResultDTO();
        executeResultDTO.setNeedExecute(true);
        executeResultDTO.setSuccess(true);
        when(scrmRefinementOperationBackEndService.executeRealTimeTask(any(), any(),any())).thenReturn(executeResultDTO);
    }

    private RealtimePerceptionMessage createTestMessage() {
        RealtimePerceptionMessage message = new RealtimePerceptionMessage();
        message.setSceneId(1);
        message.setUserId(1L);
        ExtendFieldsDTO extendFields = new ExtendFieldsDTO();
        extendFields.setMt_poi_city_id(TEST_CITY_ID);
        extendFields.setSensor_mt_poi_id("test_poi_id");
        message.setExtendFields(extendFields);
        return message;
    }

    private ScrmRealtimeSceneDO createTestSceneDO() {
        return ScrmRealtimeSceneDO.builder().id(1L).appid(TEST_APP_ID).sceneid(1).isDeleted("0").build();
    }

    /**
     * Test successful execution of handleRealtimePerceptionMessage with valid inputs
     * Verifies that executeRealTimeTask is called when all conditions are met
     */
    @Test
    public void testHandleRealtimePerceptionMessage_ValidExecution() throws Throwable {
        // arrange
        RealtimePerceptionMessage message = createTestMessage();
        ScrmRealtimeSceneDO sceneDO = createTestSceneDO();
        List<ScrmRealtimeSceneDO> sceneDOs = new ArrayList<>();
        sceneDOs.add(sceneDO);
        when(scrmRealtimeSceneDOMapper.selectByExample(any())).thenReturn(sceneDOs);
        // Mock existing record to ensure update path
        List<ScrmRealtimeSceneUserRecordDO> existingRecords = new ArrayList<>();
        ScrmRealtimeSceneUserRecordDO existingRecord = ScrmRealtimeSceneUserRecordDO.builder().visitcount(1L).ifexecuted(0).build();
        existingRecords.add(existingRecord);
        when(scrmRealtimeSceneUserRecordDOMapper.selectByExample(any())).thenReturn(existingRecords);
        // act
        sceneDomainService.handleRealtimePerceptionMessage(message);
        // assert
        verify(scrmRefinementOperationBackEndService).executeRealTimeTask(any(), any(),any());
    }

    /**
     * Test updating an existing record in handleRealtimePerceptionMessage
     */
    @Test
    public void testHandleRealtimePerceptionMessage_UpdateExistingRecord() throws Throwable {
        // arrange
        RealtimePerceptionMessage message = createTestMessage();
        ScrmRealtimeSceneDO sceneDO = createTestSceneDO();
        List<ScrmRealtimeSceneDO> sceneDOs = new ArrayList<>();
        sceneDOs.add(sceneDO);
        when(scrmRealtimeSceneDOMapper.selectByExample(any())).thenReturn(sceneDOs);
        ScrmRealtimeSceneUserRecordDO existingRecord = ScrmRealtimeSceneUserRecordDO.builder().visitcount(1L).ifexecuted(0).build();
        List<ScrmRealtimeSceneUserRecordDO> existingRecords = new ArrayList<>();
        existingRecords.add(existingRecord);
        when(scrmRealtimeSceneUserRecordDOMapper.selectByExample(any())).thenReturn(existingRecords);
        // act
        sceneDomainService.handleRealtimePerceptionMessage(message);
        // assert
        verify(scrmRealtimeSceneUserRecordDOMapper).updateByExampleSelective(any(), any());
    }

    /**
     * Test inserting a new record in handleRealtimePerceptionMessage
     */
    @Test
    public void testHandleRealtimePerceptionMessage_InsertNewRecord() throws Throwable {
        // arrange
        RealtimePerceptionMessage message = createTestMessage();
        ScrmRealtimeSceneDO sceneDO = createTestSceneDO();
        List<ScrmRealtimeSceneDO> sceneDOs = new ArrayList<>();
        sceneDOs.add(sceneDO);
        when(scrmRealtimeSceneDOMapper.selectByExample(any())).thenReturn(sceneDOs);
        when(scrmRealtimeSceneUserRecordDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        // act
        sceneDomainService.handleRealtimePerceptionMessage(message);
        // assert
        verify(scrmRealtimeSceneUserRecordDOMapper).insertSelective(any());
    }
}
