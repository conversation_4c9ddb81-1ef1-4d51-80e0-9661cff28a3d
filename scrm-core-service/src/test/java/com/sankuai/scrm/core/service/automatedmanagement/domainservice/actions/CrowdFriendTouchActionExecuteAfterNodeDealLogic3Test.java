package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationExecutePlanDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationContentTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessExecutionCheckPointDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.DeepSeaWxHandler;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.OfficialWxHandler;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.message.push.constant.MsgPushConstant;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.mockito.junit.jupiter.MockitoSettings;
import org.mockito.quality.Strictness;

@ExtendWith(MockitoExtension.class)
@MockitoSettings(strictness = Strictness.LENIENT)
public class CrowdFriendTouchActionExecuteAfterNodeDealLogic3Test {

    @InjectMocks
    private CrowdFriendTouchAction crowdFriendTouchAction;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessExecutionCheckPointDOMapper processExecutionCheckPointDOMapper;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private WeChatTokenAcl weChatTokenAcl;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private OfficialWxHandler officialWxHandler;

    @Mock
    private DeepSeaWxHandler deepSeaWxHandler;

    private MockedStatic<Lion> lionMock;

    private MockedStatic<Environment> environmentMock;

    @BeforeEach
    void setUp() {
        lionMock = mockStatic(Lion.class);
        environmentMock = mockStatic(Environment.class);
        environmentMock.when(Environment::getAppName).thenReturn("test-app");
        lionMock.when(() -> Lion.getBoolean(any(), eq(MsgPushConstant.PUSH_INTEGRATION_SWITCH_LION_KEY), eq(false))).thenReturn(false);
    }

    @AfterEach
    void tearDown() {
        if (lionMock != null) {
            lionMock.close();
        }
        if (environmentMock != null) {
            environmentMock.close();
        }
    }

    private ScrmProcessOrchestrationDTO createBasicDTO() {
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setId(1L);
        dto.setValidVersion("1.0");
        dto.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue().byteValue());
        dto.setAppId("test_app_id");
        return dto;
    }

    private ScrmAmProcessOrchestrationWxInvokeDetailDO createInvokeDetailDO(Long id, String type, Long nodeId) {
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setId(id);
        detail.setType(type);
        detail.setProcessOrchestrationNodeId(nodeId);
        detail.setStatus((byte) 1);
        detail.setProcessOrchestrationId(1L);
        detail.setProcessOrchestrationVersion("1.0");
        detail.setContentType(ScrmProcessOrchestrationContentTypeEnum.NON_SUPPLY.getValue().byteValue());
        return detail;
    }

    /**
     * Test empty executor list scenario
     */
    @Test
    public void testExecuteAfterNodeDealLogic_EmptyExecutorIds() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO dto = createBasicDTO();
        List<String> executorIds = Collections.emptyList();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        // act
        crowdFriendTouchAction.executeAfterNodeDealLogic(dto, executorIds, executeManagementDTO, stepExecuteResultDTO);
        // assert
        verify(wxInvokeDetailDOMapper, never()).selectByExample(any());
    }

    /**
     * Test old logic path with no invoke details
     */
    @Test
    public void testExecuteAfterNodeDealLogic_OldLogic_NoInvokeDetails() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO dto = createBasicDTO();
        List<String> executorIds = Collections.singletonList("executor1");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        crowdFriendTouchAction.executeAfterNodeDealLogic(dto, executorIds, executeManagementDTO, stepExecuteResultDTO);
        // assert
        verify(wxInvokeDetailDOMapper).selectByExample(any());
        verify(officialWxHandler, never()).dealOfficialWxMessage(any(), any(), any(), any());
    }

    /**
     * Test new logic path with AI scene
     */
    @Test
    public void testExecuteAfterNodeDealLogic_NewLogic_AiScene() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO dto = createBasicDTO();
        dto.setAiScene(true);
        IntelligentFollowResultDTO aiSceneContent = new IntelligentFollowResultDTO();
        aiSceneContent.setInvokeDetailLogId(123L);
        dto.setAiSceneContent(aiSceneContent);
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(1L);
        dto.setExecutePlanDTO(executePlanDTO);
        List<String> executorIds = Collections.singletonList("executor1");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        lionMock.when(() -> Lion.getBoolean(any(), eq(MsgPushConstant.PUSH_INTEGRATION_SWITCH_LION_KEY), eq(false))).thenReturn(true);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> mockDetails = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = createInvokeDetailDO(123L, "MESSAGE", 1L);
        mockDetails.add(detail);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(mockDetails).thenReturn(Collections.emptyList());
        // act
        crowdFriendTouchAction.executeAfterNodeDealLogic(dto, executorIds, executeManagementDTO, stepExecuteResultDTO);
        // assert
        verify(wxInvokeDetailDOMapper, times(2)).selectByExample(any());
        verify(deepSeaWxHandler).dealRealTimeDeepSeaWxMessageV2(eq(dto), eq(executorIds), any(), eq(mockDetails), eq(executeManagementDTO));
    }

    /**
     * Test new logic path with pagination
     */
    @Test
    public void testExecuteAfterNodeDealLogic_NewLogic_WithPagination() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO dto = createBasicDTO();
        List<String> executorIds = Collections.singletonList("executor1");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(1L);
        dto.setExecutePlanDTO(executePlanDTO);
        lionMock.when(() -> Lion.getBoolean(any(), eq(MsgPushConstant.PUSH_INTEGRATION_SWITCH_LION_KEY), eq(false))).thenReturn(true);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> firstPage = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = createInvokeDetailDO(1L, "MESSAGE", 1L);
        detail.setContentType(ScrmProcessOrchestrationContentTypeEnum.NON_SUPPLY.getValue().byteValue());
        firstPage.add(detail);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(firstPage).thenReturn(Collections.emptyList());
        // act
        crowdFriendTouchAction.executeAfterNodeDealLogic(dto, executorIds, executeManagementDTO, stepExecuteResultDTO);
        // assert
        verify(wxInvokeDetailDOMapper, times(2)).selectByExample(any());
        verify(deepSeaWxHandler).dealRealTimeDeepSeaWxMessageV2(eq(dto), eq(executorIds), any(), eq(firstPage), eq(executeManagementDTO));
    }
}
