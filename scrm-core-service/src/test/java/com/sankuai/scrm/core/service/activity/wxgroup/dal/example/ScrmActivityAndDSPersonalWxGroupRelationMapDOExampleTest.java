package com.sankuai.scrm.core.service.activity.wxgroup.dal.example;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
@DisplayName("ScrmActivityAndDSPersonalWxGroupRelationMapDOExample Limit Method Tests")
public class ScrmActivityAndDSPersonalWxGroupRelationMapDOExampleTest {

    private ScrmActivityAndDSPersonalWxGroupRelationMapDOExample scrmActivityAndDSPersonalWxGroupRelationMapDOExample;

    @Spy
    private ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example;

    @BeforeEach
    public void setUp() {
        scrmActivityAndDSPersonalWxGroupRelationMapDOExample = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
    }

    /**
     * 测试oredCriteria列表为空的情况
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsEmpty() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.Criteria criteria = scrmActivityAndDSPersonalWxGroupRelationMapDOExample.createCriteria();
        // act
        // 无需执行任何操作
        // assert
        assertNotNull(criteria);
        assertEquals(1, scrmActivityAndDSPersonalWxGroupRelationMapDOExample.getOredCriteria().size());
    }

    /**
     * 测试oredCriteria列表不为空的情况
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsNotEmpty() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.Criteria criteria1 = scrmActivityAndDSPersonalWxGroupRelationMapDOExample.createCriteria();
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.Criteria criteria2 = scrmActivityAndDSPersonalWxGroupRelationMapDOExample.createCriteria();
        // act
        // 无需执行任何操作
        // assert
        assertNotNull(criteria1);
        assertNotNull(criteria2);
        assertEquals(1, scrmActivityAndDSPersonalWxGroupRelationMapDOExample.getOredCriteria().size());
    }

    /**
     * Test limit method with normal positive values
     */
    @Test
    @DisplayName("Should set offset and rows with positive values")
    public void testLimitWithPositiveValues() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        Integer offset = 10;
        Integer rows = 20;
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample result = example.limit(offset, rows);
        // assert
        assertAll(() -> assertEquals(offset, result.getOffset(), "Offset should be set to 10"), () -> assertEquals(rows, result.getRows(), "Rows should be set to 20"), () -> assertSame(example, result, "Should return the same instance for chaining"));
    }

    /**
     * Test limit method with zero values
     */
    @Test
    @DisplayName("Should accept zero values for offset and rows")
    public void testLimitWithZeroValues() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        Integer offset = 0;
        Integer rows = 0;
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample result = example.limit(offset, rows);
        // assert
        assertAll(() -> assertEquals(offset, result.getOffset(), "Offset should be set to 0"), () -> assertEquals(rows, result.getRows(), "Rows should be set to 0"), () -> assertSame(example, result, "Should return the same instance for chaining"));
    }

    /**
     * Test limit method with null values
     */
    @Test
    @DisplayName("Should accept null values for offset and rows")
    public void testLimitWithNullValues() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        Integer offset = null;
        Integer rows = null;
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample result = example.limit(offset, rows);
        // assert
        assertAll(() -> assertNull(result.getOffset(), "Offset should be null"), () -> assertNull(result.getRows(), "Rows should be null"), () -> assertSame(example, result, "Should return the same instance for chaining"));
    }

    /**
     * Test limit method with mixed null and non-null values
     */
    @Test
    @DisplayName("Should handle mixed null and non-null values")
    public void testLimitWithMixedNullValues() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        Integer offset = 10;
        Integer rows = null;
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample result = example.limit(offset, rows);
        // assert
        assertAll(() -> assertEquals(offset, result.getOffset(), "Offset should be set to 10"), () -> assertNull(result.getRows(), "Rows should be null"), () -> assertSame(example, result, "Should return the same instance for chaining"));
    }

    /**
     * Test limit method chaining capability
     */
    @Test
    @DisplayName("Should support method chaining with multiple limit calls")
    public void testLimitMethodChaining() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        Integer firstOffset = 10;
        Integer firstRows = 20;
        Integer secondOffset = 30;
        Integer secondRows = 40;
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample result = example.limit(firstOffset, firstRows).limit(secondOffset, secondRows);
        // assert
        assertAll(() -> assertEquals(secondOffset, result.getOffset(), "Offset should have the last set value"), () -> assertEquals(secondRows, result.getRows(), "Rows should have the last set value"), () -> assertSame(example, result, "Should return the same instance for chaining"));
    }

    /**
     * Test limit method with maximum integer values
     */
    @Test
    @DisplayName("Should handle maximum integer values")
    public void testLimitWithMaxValues() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        Integer offset = Integer.MAX_VALUE;
        Integer rows = Integer.MAX_VALUE;
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample result = example.limit(offset, rows);
        // assert
        assertAll(() -> assertEquals(offset, result.getOffset(), "Offset should accept maximum integer value"), () -> assertEquals(rows, result.getRows(), "Rows should accept maximum integer value"), () -> assertSame(example, result, "Should return the same instance for chaining"));
    }

    /**
     * Test page method with normal positive integers
     */
    @Test
    public void testPageWithNormalPositiveIntegers() throws Throwable {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample result = example.page(2, 10);
        // assert
        assertEquals(20, result.getOffset());
        assertEquals(10, result.getRows());
        assertSame(example, result);
    }

    /**
     * Test page method with zero page
     */
    @Test
    public void testPageWithZeroPage() throws Throwable {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample result = example.page(0, 10);
        // assert
        assertEquals(0, result.getOffset());
        assertEquals(10, result.getRows());
        assertSame(example, result);
    }

    /**
     * Test page method with zero pageSize
     */
    @Test
    public void testPageWithZeroPageSize() throws Throwable {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample result = example.page(2, 0);
        // assert
        assertEquals(0, result.getOffset());
        assertEquals(0, result.getRows());
        assertSame(example, result);
    }

    /**
     * Test page method with null page
     */
    @Test
    public void testPageWithNullPage() throws Throwable {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        // act & assert
        assertThrows(NullPointerException.class, () -> example.page(null, 10));
    }

    /**
     * Test page method with null pageSize
     */
    @Test
    public void testPageWithNullPageSize() throws Throwable {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        // act & assert
        assertThrows(NullPointerException.class, () -> example.page(2, null));
    }

    /**
     * Test page method with large numbers
     */
    @Test
    public void testPageWithLargeNumbers() throws Throwable {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample result = example.page(1000, 1000);
        // assert
        assertEquals(1000000, result.getOffset());
        assertEquals(1000, result.getRows());
        assertSame(example, result);
    }

    /**
     * Test createCriteriaInternal method
     * Scenario: Create a new Criteria object
     * Expected: Should return a new non-null Criteria instance
     */
    @Test
    @DisplayName("createCriteriaInternal should create new Criteria instance")
    public void testCreateCriteriaInternal_ShouldCreateNewCriteria() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertNotNull(result, "Created Criteria should not be null");
        assertTrue(result instanceof ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.Criteria, "Result should be instance of Criteria");
    }

    /**
     * Test createCriteriaInternal method
     * Scenario: Create multiple Criteria objects
     * Expected: Should return different instances for each call
     */
    @Test
    @DisplayName("createCriteriaInternal should create unique instances")
    public void testCreateCriteriaInternal_ShouldCreateUniqueInstances() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.Criteria result1 = example.createCriteriaInternal();
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.Criteria result2 = example.createCriteriaInternal();
        // assert
        assertNotNull(result1, "First created Criteria should not be null");
        assertNotNull(result2, "Second created Criteria should not be null");
        assertNotSame(result1, result2, "Multiple calls should create different Criteria instances");
    }

    /**
     * Test createCriteriaInternal method
     * Scenario: Verify Criteria initialization
     * Expected: Should create Criteria with empty criteria list
     */
    @Test
    @DisplayName("createCriteriaInternal should initialize Criteria properly")
    public void testCreateCriteriaInternal_ShouldInitializeCriteriaProperly() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertNotNull(result.getCriteria(), "Criteria list should be initialized");
        assertTrue(result.getCriteria().isEmpty(), "Criteria list should be empty initially");
    }

    /**
     * Test scenario: Clear method with default values
     * Expected: All fields should be reset to their default values
     */
    @Test
    @DisplayName("Should clear example with default values")
    public void testClearWithDefaultValues() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        // act
        example.clear();
        // assert
        assertAll(() -> assertTrue(example.getOredCriteria().isEmpty(), "oredCriteria should be empty"), () -> assertNull(example.getOrderByClause(), "orderByClause should be null"), () -> assertFalse(example.isDistinct(), "distinct should be false"), () -> assertNull(example.getRows(), "rows should be null"), () -> assertNull(example.getOffset(), "offset should be null"));
    }

    /**
     * Test scenario: Clear method with populated values
     * Expected: All fields should be reset to their default values regardless of previous state
     */
    @Test
    @DisplayName("Should clear example with populated values")
    public void testClearWithPopulatedValues() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        example.setOrderByClause("id DESC");
        example.setDistinct(true);
        example.setRows(10);
        example.setOffset(0);
        example.createCriteria();
        // act
        example.clear();
        // assert
        assertAll(() -> assertTrue(example.getOredCriteria().isEmpty(), "oredCriteria should be empty"), () -> assertNull(example.getOrderByClause(), "orderByClause should be null"), () -> assertFalse(example.isDistinct(), "distinct should be false"), () -> assertNull(example.getRows(), "rows should be null"), () -> assertNull(example.getOffset(), "offset should be null"));
    }

    /**
     * Test scenario: Clear method with multiple criteria
     * Expected: All criteria should be cleared and fields reset to default values
     */
    @Test
    @DisplayName("Should clear example with multiple criteria")
    public void testClearWithMultipleCriteria() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        example.createCriteria();
        example.or();
        example.or();
        assertEquals(3, example.getOredCriteria().size(), "Should have 3 criteria before clearing");
        // act
        example.clear();
        // assert
        assertAll(() -> assertTrue(example.getOredCriteria().isEmpty(), "oredCriteria should be empty"), () -> assertNull(example.getOrderByClause(), "orderByClause should be null"), () -> assertFalse(example.isDistinct(), "distinct should be false"), () -> assertNull(example.getRows(), "rows should be null"), () -> assertNull(example.getOffset(), "offset should be null"));
    }

    /**
     * Test scenario: Clear method called multiple times
     * Expected: Should maintain cleared state after multiple clear calls
     */
    @Test
    @DisplayName("Should maintain cleared state after multiple clear calls")
    public void testClearMultipleTimes() {
        // arrange
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample example = new ScrmActivityAndDSPersonalWxGroupRelationMapDOExample();
        example.setOrderByClause("id DESC");
        example.setDistinct(true);
        example.createCriteria();
        // act
        example.clear();
        // Second clear call
        example.clear();
        // assert
        assertAll(() -> assertTrue(example.getOredCriteria().isEmpty(), "oredCriteria should be empty"), () -> assertNull(example.getOrderByClause(), "orderByClause should be null"), () -> assertFalse(example.isDistinct(), "distinct should be false"), () -> assertNull(example.getRows(), "rows should be null"), () -> assertNull(example.getOffset(), "offset should be null"));
    }

    /**
     * Test normal execution of or() method
     * Verifies:
     * 1. createCriteriaInternal is called
     * 2. Returns non-null Criteria
     * 3. Increases oredCriteria size by 1
     */
    @Test
    public void testOrNormalExecution() {
        // arrange
        int initialSize = example.getOredCriteria().size();
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.Criteria result = example.or();
        // assert
        verify(example).createCriteriaInternal();
        assertNotNull(result);
        assertEquals(initialSize + 1, example.getOredCriteria().size());
    }

    /**
     * Test that returned Criteria is the same as what was added to list
     * Verifies the Criteria object is properly added to oredCriteria list
     */
    @Test
    public void testOrReturnedCriteriaIsInList() {
        // arrange
        doCallRealMethod().when(example).or();
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.Criteria result = example.or();
        // assert
        verify(example).createCriteriaInternal();
        assertTrue(example.getOredCriteria().contains(result));
        assertEquals(result, example.getOredCriteria().get(example.getOredCriteria().size() - 1));
    }

    /**
     * Test multiple calls to or() method
     * Verifies:
     * 1. Each call creates new Criteria
     * 2. Each Criteria is added to list
     * 3. createCriteriaInternal is called for each or() invocation
     */
    @Test
    public void testOrMultipleCalls() {
        // arrange
        int initialSize = example.getOredCriteria().size();
        doCallRealMethod().when(example).or();
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.Criteria result1 = example.or();
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.Criteria result2 = example.or();
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.Criteria result3 = example.or();
        // assert
        verify(example, times(3)).createCriteriaInternal();
        assertNotNull(result1);
        assertNotNull(result2);
        assertNotNull(result3);
        assertNotSame(result1, result2);
        assertNotSame(result2, result3);
        assertEquals(initialSize + 3, example.getOredCriteria().size());
        assertTrue(example.getOredCriteria().contains(result1));
        assertTrue(example.getOredCriteria().contains(result2));
        assertTrue(example.getOredCriteria().contains(result3));
    }

    /**
     * Test the order of Criteria objects in oredCriteria list
     * Verifies the order of addition is maintained
     */
    @Test
    public void testOrCriteriaOrder() {
        // arrange
        doCallRealMethod().when(example).or();
        // act
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.Criteria result1 = example.or();
        ScrmActivityAndDSPersonalWxGroupRelationMapDOExample.Criteria result2 = example.or();
        // assert
        verify(example, times(2)).createCriteriaInternal();
        assertEquals(result1, example.getOredCriteria().get(example.getOredCriteria().size() - 2));
        assertEquals(result2, example.getOredCriteria().get(example.getOredCriteria().size() - 1));
    }
}
