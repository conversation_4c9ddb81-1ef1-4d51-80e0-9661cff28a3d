package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationConditionDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationNodeTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationBranchExeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationBranchExeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.utils.ConditionUtils;
import com.sankuai.scrm.core.service.user.dal.mapper.ext.ExtScrmUserTagMapper;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import org.junit.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * Tests for the dealConditionBranch method in ExecuteWriteDomainService.
 */
public class ExecuteWriteDomainServiceDealConditionBranchTest {

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock
    private ConditionUtils conditionUtils;

    @Mock
    private ExtScrmUserTagMapper userTagDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationBranchExeLogDOMapper branchExeLogDOMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test that an IllegalArgumentException is thrown when crowdPackType is 3.
     */
    @Test(expected = IllegalArgumentException.class)
    public void testDealConditionBranchCrowdPackType3() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setCrowdPackType(3);
        executeWriteDomainService.dealConditionBranch(processOrchestrationDTO, new ExecuteManagementDTO(), new HashMap<>(), new ArrayList<>(), new ScrmCrowdPackDetailInfoDTO(), new HashMap<>(), new ScrmAmProcessOrchestrationExecuteLogDO());
    }

    /**
     * Test that the method throws a RuntimeException when no conditions match and there is no default condition node.
     */
    @Test(expected = RuntimeException.class)
    public void testDealConditionBranchNoMatchingConditions() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setCrowdPackType(1);
        List<Long> processingNodes = Arrays.asList(1L);
        Map<Long, ScrmProcessOrchestrationNodeDTO> nodeDTOMap = new HashMap<>();
        ScrmProcessOrchestrationNodeDTO node = new ScrmProcessOrchestrationNodeDTO();
        node.setNodeId(1L);
        node.setChildrenNodes(Arrays.asList(2L));
        node.setNodeType(ScrmProcessOrchestrationNodeTypeEnum.PROCESS_ORCHESTRATION_CONDITION.getValue());
        nodeDTOMap.put(1L, node);
        Map<Long, List<ScrmProcessOrchestrationConditionDetailDTO>> conditionMap = new HashMap<>();
        List<ScrmProcessOrchestrationConditionDetailDTO> conditionDetails = new ArrayList<>();
        ScrmProcessOrchestrationConditionDetailDTO conditionDetail = new ScrmProcessOrchestrationConditionDetailDTO();
        conditionDetail.setDefaultCondition(false);
        conditionDetail.setProcessOrchestrationNodeId(1L);
        conditionDetails.add(conditionDetail);
        conditionMap.put(1L, conditionDetails);
        when(userTagDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(conditionUtils.isConditionMatch(any(), any(), any(), any(), any())).thenReturn(false);
        executeWriteDomainService.dealConditionBranch(processOrchestrationDTO, new ExecuteManagementDTO(), nodeDTOMap, processingNodes, new ScrmCrowdPackDetailInfoDTO(), conditionMap, new ScrmAmProcessOrchestrationExecuteLogDO());
    }
}
