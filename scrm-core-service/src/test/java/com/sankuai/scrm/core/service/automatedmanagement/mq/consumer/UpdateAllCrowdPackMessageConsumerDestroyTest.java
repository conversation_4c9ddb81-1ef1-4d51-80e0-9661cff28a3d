package com.sankuai.scrm.core.service.automatedmanagement.mq.consumer;

import static org.mockito.Mockito.*;
import com.meituan.mafka.client.consumer.IConsumerProcessor;
import java.lang.reflect.Field;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.dz.srcm.automatedmanagement.dto.mq.RefinementOperationExecuteMessage;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.exception.PersonaCrowdPackException;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.util.Collections;
import java.util.HashSet;
import java.util.concurrent.ExecutionException;
import java.util.concurrent.TimeoutException;

@ExtendWith(MockitoExtension.class)
class UpdateAllCrowdPackMessageConsumerDestroyTest {

    @Mock
    private ExecuteWriteDomainService executeDomainService;

    @Mock
    private ExecuteWriteDomainService executeWriteDomainService;

    @InjectMocks
    private UpdateAllCrowdPackMessageConsumer consumer;

    /**
     * Tests destroy() when consumer is null - should do nothing
     */
    @Test
    public void testDestroyWhenConsumerIsNull() throws Throwable {
        // arrange
        UpdateAllCrowdPackMessageConsumer consumer = new UpdateAllCrowdPackMessageConsumer();
        // Set the static consumer field to null using reflection
        Field consumerField = UpdateAllCrowdPackMessageConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(null, null);
        // act
        consumer.destroy();
        // assert
        // No exception should be thrown
        // We can't verify a null object, but we can verify the field is still null
        Object fieldValue = consumerField.get(null);
        assert fieldValue == null : "Consumer field should remain null";
    }

    /**
     * Tests destroy() when consumer is not null - should call close()
     */
    @Test
    public void testDestroyWhenConsumerIsNotNull() throws Throwable {
        // arrange
        UpdateAllCrowdPackMessageConsumer consumer = new UpdateAllCrowdPackMessageConsumer();
        IConsumerProcessor mockConsumer = mock(IConsumerProcessor.class);
        // Set the static consumer field using reflection
        Field consumerField = UpdateAllCrowdPackMessageConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(null, mockConsumer);
        // act
        consumer.destroy();
        // assert
        verify(mockConsumer, times(1)).close();
        // Reset the static field to avoid affecting other tests
        consumerField.set(null, null);
    }

    /**
     * Tests destroy() when consumer close throws exception - should propagate exception
     */
    @Test
    public void testDestroyWhenConsumerCloseThrowsException() throws Throwable {
        // arrange
        UpdateAllCrowdPackMessageConsumer consumer = new UpdateAllCrowdPackMessageConsumer();
        IConsumerProcessor mockConsumer = mock(IConsumerProcessor.class);
        Exception expectedException = new Exception("Test exception");
        doThrow(expectedException).when(mockConsumer).close();
        // Set the static consumer field using reflection
        Field consumerField = UpdateAllCrowdPackMessageConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(null, mockConsumer);
        // act & assert
        Exception actualException = org.junit.jupiter.api.Assertions.assertThrows(Exception.class, () -> consumer.destroy());
        org.junit.jupiter.api.Assertions.assertEquals(expectedException, actualException);
        verify(mockConsumer, times(1)).close();
        // Reset the static field to avoid affecting other tests
        consumerField.set(null, null);
    }

    @Test
    public void testRecvMessageEmptyBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "");
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = consumer.recvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verifyNoInteractions(executeDomainService, executeWriteDomainService);
    }

    @Test
    public void testRecvMessageInvalidJson() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "invalid json");
        MessagetContext context = new MessagetContext();
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject("invalid json", RefinementOperationExecuteMessage.class)).thenReturn(null);
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verifyNoInteractions(executeDomainService, executeWriteDomainService);
        }
    }

    @Test
    public void testRecvMessagePeriodicCrowdPackUpdateExecutionException() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setTaskType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue());
        msg.setUserUnionIds(Collections.singletonList("union1"));
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject("valid json", RefinementOperationExecuteMessage.class)).thenReturn(msg);
            doThrow(new ExecutionException("test", new RuntimeException())).when(executeDomainService).updateCrowdPackSubTask(Collections.singletonList("union1"));
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.RECONSUME_LATER, result);
            verify(executeDomainService).updateCrowdPackSubTask(Collections.singletonList("union1"));
        }
    }

    @Test
    public void testRecvMessagePeriodicCrowdPackUpdateInterruptedException() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setTaskType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue());
        msg.setUserUnionIds(Collections.singletonList("union1"));
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject("valid json", RefinementOperationExecuteMessage.class)).thenReturn(msg);
            doThrow(new InterruptedException("test")).when(executeDomainService).updateCrowdPackSubTask(Collections.singletonList("union1"));
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.RECONSUME_LATER, result);
            verify(executeDomainService).updateCrowdPackSubTask(Collections.singletonList("union1"));
        }
    }

    @Test
    public void testRecvMessagePeriodicCrowdPackUpdateTimeoutException() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setTaskType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue());
        msg.setUserUnionIds(Collections.singletonList("union1"));
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject("valid json", RefinementOperationExecuteMessage.class)).thenReturn(msg);
            doThrow(new TimeoutException("test")).when(executeDomainService).updateCrowdPackSubTask(Collections.singletonList("union1"));
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.RECONSUME_LATER, result);
            verify(executeDomainService).updateCrowdPackSubTask(Collections.singletonList("union1"));
        }
    }

    @Test
    public void testRecvMessagePersonaCrowdPackUpdatePersonaException() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setTaskType(ScrmProcessOrchestrationTaskTypeEnum.PERSONA_CROWD_PACK_UPDATE.getValue());
        msg.setPersonaId(1);
        msg.setCrowdPackId(1L);
        msg.setAppId("app1");
        msg.setCrowdPackVersion("v1");
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject("valid json", RefinementOperationExecuteMessage.class)).thenReturn(msg);
            doThrow(new PersonaCrowdPackException("test")).when(executeDomainService).updatePersonaCrowdPackSubTask(1, 1L, "app1", "v1");
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(executeDomainService).updatePersonaCrowdPackSubTask(1, 1L, "app1", "v1");
        }
    }

    @Test
    public void testRecvMessagePersonaCrowdPackUpdateGeneralException() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setTaskType(ScrmProcessOrchestrationTaskTypeEnum.PERSONA_CROWD_PACK_UPDATE.getValue());
        msg.setPersonaId(1);
        msg.setCrowdPackId(1L);
        msg.setAppId("app1");
        msg.setCrowdPackVersion("v1");
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject("valid json", RefinementOperationExecuteMessage.class)).thenReturn(msg);
            doThrow(new RuntimeException("test")).when(executeDomainService).updatePersonaCrowdPackSubTask(1, 1L, "app1", "v1");
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.RECONSUME_LATER, result);
            verify(executeDomainService).updatePersonaCrowdPackSubTask(1, 1L, "app1", "v1");
        }
    }

    @Test
    public void testRecvMessagePersonaCrowdPackUpdateDbPersonaException() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setTaskType(ScrmProcessOrchestrationTaskTypeEnum.PERSONA_CROWD_PACK_UPDATE_DB.getValue());
        msg.setAppId("app1");
        msg.setMtUserIds(new HashSet<>(Collections.singletonList(1L)));
        msg.setCrowdPackId(1L);
        msg.setCrowdPackVersion("v1");
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject("valid json", RefinementOperationExecuteMessage.class)).thenReturn(msg);
            doThrow(new PersonaCrowdPackException("test")).when(executeWriteDomainService).concurrentUpdatePersonaCrowdPackAsync("app1", msg.getMtUserIds(), 1L, "v1");
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(executeWriteDomainService).concurrentUpdatePersonaCrowdPackAsync("app1", msg.getMtUserIds(), 1L, "v1");
        }
    }

    @Test
    public void testRecvMessagePersonaCrowdPackUpdateDbGeneralException() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setTaskType(ScrmProcessOrchestrationTaskTypeEnum.PERSONA_CROWD_PACK_UPDATE_DB.getValue());
        msg.setAppId("app1");
        msg.setMtUserIds(new HashSet<>(Collections.singletonList(1L)));
        msg.setCrowdPackId(1L);
        msg.setCrowdPackVersion("v1");
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject("valid json", RefinementOperationExecuteMessage.class)).thenReturn(msg);
            doThrow(new RuntimeException("test")).when(executeWriteDomainService).concurrentUpdatePersonaCrowdPackAsync("app1", msg.getMtUserIds(), 1L, "v1");
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.RECONSUME_LATER, result);
            verify(executeWriteDomainService).concurrentUpdatePersonaCrowdPackAsync("app1", msg.getMtUserIds(), 1L, "v1");
        }
    }

    @Test
    public void testRecvMessagePeriodicCrowdPackUpdateSuccess() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setTaskType(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue());
        msg.setUserUnionIds(Collections.singletonList("union1"));
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject("valid json", RefinementOperationExecuteMessage.class)).thenReturn(msg);
            when(executeDomainService.updateCrowdPackSubTask(Collections.singletonList("union1"))).thenReturn(true);
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(executeDomainService).updateCrowdPackSubTask(Collections.singletonList("union1"));
        }
    }

    @Test
    public void testRecvMessagePersonaCrowdPackUpdateSuccess() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setTaskType(ScrmProcessOrchestrationTaskTypeEnum.PERSONA_CROWD_PACK_UPDATE.getValue());
        msg.setPersonaId(1);
        msg.setCrowdPackId(1L);
        msg.setAppId("app1");
        msg.setCrowdPackVersion("v1");
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject("valid json", RefinementOperationExecuteMessage.class)).thenReturn(msg);
            // No need to mock void methods with when/thenReturn
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(executeDomainService).updatePersonaCrowdPackSubTask(1, 1L, "app1", "v1");
        }
    }

    @Test
    public void testRecvMessagePersonaCrowdPackUpdateDbSuccess() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        msg.setTaskType(ScrmProcessOrchestrationTaskTypeEnum.PERSONA_CROWD_PACK_UPDATE_DB.getValue());
        msg.setAppId("app1");
        msg.setMtUserIds(new HashSet<>(Collections.singletonList(1L)));
        msg.setCrowdPackId(1L);
        msg.setCrowdPackVersion("v1");
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject("valid json", RefinementOperationExecuteMessage.class)).thenReturn(msg);
            // No need to mock void methods with when/thenReturn
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verify(executeWriteDomainService).concurrentUpdatePersonaCrowdPackAsync("app1", msg.getMtUserIds(), 1L, "v1");
        }
    }

    @Test
    public void testRecvMessageUnknownTaskType() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 0, 0L, "key", "valid json");
        MessagetContext context = new MessagetContext();
        RefinementOperationExecuteMessage msg = new RefinementOperationExecuteMessage();
        // Unknown task type
        msg.setTaskType(999);
        try (MockedStatic<JsonUtils> jsonUtilsMock = mockStatic(JsonUtils.class)) {
            jsonUtilsMock.when(() -> JsonUtils.toObject("valid json", RefinementOperationExecuteMessage.class)).thenReturn(msg);
            // act
            ConsumeStatus result = consumer.recvMessage(message, context);
            // assert
            assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
            verifyNoInteractions(executeDomainService, executeWriteDomainService);
        }
    }
}