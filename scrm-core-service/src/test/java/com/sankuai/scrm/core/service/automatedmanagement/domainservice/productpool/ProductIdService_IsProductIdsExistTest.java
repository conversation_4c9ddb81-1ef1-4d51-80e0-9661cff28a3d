package com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool;

import com.sankuai.mpproduct.idservice.api.enums.BizProductIdType;
import com.sankuai.mpproduct.idservice.api.request.BizProductIdConvertRequest;
import com.sankuai.mpproduct.idservice.api.response.BizProductIdConvertResponse;
import com.sankuai.mpproduct.idservice.api.service.IdService;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class ProductIdService_IsProductIdsExistTest {

    @InjectMocks
    private ProductIdService productIdService;

    @Mock(lenient = true)
    private IdService idService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    @Test
    public void testIsProductIdsExistWithEmptyProductIds() throws Throwable {
        Map<Long, Boolean> result = productIdService.isProductIdsExist(null, BizProductIdType.DEAL_ID);
        assertNull(result);
    }

    @Test
    public void testIsProductIdsExistWithNullBizProductIdType() throws Throwable {
        Map<Long, Boolean> result = productIdService.isProductIdsExist(Arrays.asList(1L, 2L), null);
        assertNull(result);
    }

    @Test
    public void testIsProductIdsExistWithNullConvertResult() throws Throwable {
        when(idService.convertBizProductIdsToProductIds(any(BizProductIdConvertRequest.class))).thenReturn(null);
        Map<Long, Boolean> result = productIdService.isProductIdsExist(Arrays.asList(1L, 2L), BizProductIdType.DEAL_ID);
        assertNull(result);
    }

    @Test
    public void testIsProductIdsExistWithEmptyConvertResult() throws Throwable {
        BizProductIdConvertResponse response = new BizProductIdConvertResponse();
        response.setBizProductIdConvertResult(new HashMap<>());
        when(idService.convertBizProductIdsToProductIds(any(BizProductIdConvertRequest.class))).thenReturn(response);
        Map<Long, Boolean> result = productIdService.isProductIdsExist(Arrays.asList(1L, 2L), BizProductIdType.DEAL_ID);
        assertNull(result);
    }

    @Test
    public void testIsProductIdsExistWithNotEmptyConvertResult() throws Throwable {
        Map<Long, Long> biz2Plat = new HashMap<>();
        biz2Plat.put(1L, 10L);
        biz2Plat.put(2L, 20L);
        BizProductIdConvertResponse response = new BizProductIdConvertResponse();
        response.setBizProductIdConvertResult(biz2Plat);
        when(idService.convertBizProductIdsToProductIds(any(BizProductIdConvertRequest.class))).thenReturn(response);
        Map<Long, Boolean> result = productIdService.isProductIdsExist(Arrays.asList(1L, 2L), BizProductIdType.DEAL_ID);
        assertNotNull(result);
        assertTrue(result.get(1L));
        assertTrue(result.get(2L));
    }
}
