package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAMRealtimeSceneAndProcessMapDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAMRealtimeSceneAndProcessMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class DemoExecuteDomainServiceRunRealTimeTaskDemoTest {

    @InjectMocks
    private DemoExecuteDomainService demoExecuteDomainService;

    @Mock
    private ScrmAMRealtimeSceneAndProcessMapDOMapper scrmAMRealtimeSceneAndProcessMapDOMapper;

    @Mock
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private ExecuteWriteDomainService executeWriteDomainService;

    private static final String APP_NAME = "test-app";

    private static final String DEMO_WHITE_LIST = "refinement.operation.demo.white.list";

    private static String originalAppName;

    @Before
    public void setUp() throws Exception {
        Field appNameField = Environment.class.getDeclaredField("appName");
        appNameField.setAccessible(true);
        originalAppName = (String) appNameField.get(null);
        appNameField.set(null, APP_NAME);
    }

    @After
    public void tearDown() throws Exception {
        Field appNameField = Environment.class.getDeclaredField("appName");
        appNameField.setAccessible(true);
        appNameField.set(null, originalAppName);
    }

    /**
     * Test when processOrchestrationId is not in white list
     */
    @Test
    public void testRunRealTimeTaskDemo_NotInWhiteList() throws Throwable {
        // arrange
        Long sceneId = 1L;
        String wxUnionId = "test-union-id";
        Long processOrchestrationId = 100L;
        ScrmAMRealtimeSceneAndProcessMapDO mapDO = new ScrmAMRealtimeSceneAndProcessMapDO();
        mapDO.setProcessOrchestrationId(processOrchestrationId);
        List<ScrmAMRealtimeSceneAndProcessMapDO> mapDOList = Arrays.asList(mapDO);
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any())).thenReturn(mapDOList);
        try (MockedStatic<Lion> lionMock = Mockito.mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getList(eq(APP_NAME), eq(DEMO_WHITE_LIST), eq(Long.class))).thenReturn(new ArrayList<>());
            // act
            StepExecuteResultDTO result = demoExecuteDomainService.runRealTimeTaskDemo(sceneId, wxUnionId);
            // assert
            assertNotNull(result);
            assertFalse(result.isSuccess());
            assertEquals(Integer.valueOf(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getCode()), result.getCode());
            assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT.getDesc(), result.getMsg());
            assertEquals(processOrchestrationId, result.getProcessOrchestrationId());
        }
    }

    /**
     * Test successful execution path
     */
    @Test
    public void testRunRealTimeTaskDemo_Success() throws Throwable {
        // arrange
        Long sceneId = 1L;
        String wxUnionId = "test-union-id";
        Long processOrchestrationId = 100L;
        String version = "v1.0";
        ScrmAMRealtimeSceneAndProcessMapDO mapDO = new ScrmAMRealtimeSceneAndProcessMapDO();
        mapDO.setProcessOrchestrationId(processOrchestrationId);
        List<ScrmAMRealtimeSceneAndProcessMapDO> mapDOList = Arrays.asList(mapDO);
        ScrmProcessOrchestrationDTO orchestrationDTO = new ScrmProcessOrchestrationDTO();
        orchestrationDTO.setId(processOrchestrationId);
        orchestrationDTO.setValidVersion(version);
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        ScrmProcessOrchestrationNodeDTO rootNode = new ScrmProcessOrchestrationNodeDTO();
        rootNode.setNodeId(0L);
        List<ScrmProcessOrchestrationNodeDTO> nodeList = Arrays.asList(rootNode);
        nodeMediumDTO.setProcessOrchestrationNodeDTOList(nodeList);
        orchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        StepExecuteResultDTO expectedResult = new StepExecuteResultDTO();
        expectedResult.setSuccess(true);
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any())).thenReturn(mapDOList);
        when(processOrchestrationReadDomainService.queryProcessOrchestrationDetailFuture(processOrchestrationId)).thenReturn(CompletableFuture.completedFuture(orchestrationDTO));
        when(executeManagementService.getExecuteMediumManagementDTO(orchestrationDTO)).thenReturn(executeManagementDTO);
        when(executeWriteDomainService.executeRealTimeTask(eq(orchestrationDTO), eq(wxUnionId), any(), eq(rootNode), isNull(), eq(executeManagementDTO))).thenReturn(expectedResult);
        try (MockedStatic<Lion> lionMock = Mockito.mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getList(eq(APP_NAME), eq(DEMO_WHITE_LIST), eq(Long.class))).thenReturn(Arrays.asList(processOrchestrationId));
            // act
            StepExecuteResultDTO result = demoExecuteDomainService.runRealTimeTaskDemo(sceneId, wxUnionId);
            // assert
            assertNotNull(result);
            assertTrue(result.isSuccess());
            verify(executeWriteDomainService).executeRealTimeTask(eq(orchestrationDTO), eq(wxUnionId), any(), eq(rootNode), isNull(), eq(executeManagementDTO));
            verify(executeWriteDomainService).secondStaffTaskAssignStep(eq(orchestrationDTO), eq(rootNode), eq(executeManagementDTO), isNull());
            verify(executeWriteDomainService).thirdWxGroupTouchStep(eq(orchestrationDTO), eq(executeManagementDTO), eq(expectedResult));
            verify(executeWriteDomainService).updateNodeExecuteLog(eq(orchestrationDTO), eq(executeManagementDTO));
        }
    }

    /**
     * Test when query scene throws exception
     */
    @Test(expected = RuntimeException.class)
    public void testRunRealTimeTaskDemo_QuerySceneException() throws Throwable {
        // arrange
        Long sceneId = 1L;
        String wxUnionId = "test-union-id";
        when(scrmAMRealtimeSceneAndProcessMapDOMapper.selectByExample(any())).thenThrow(new RuntimeException("DB error"));
        // act
        demoExecuteDomainService.runRealTimeTaskDemo(sceneId, wxUnionId);
    }
}
