package com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool;

import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmAmProcessOrchestrationProductTagsTagTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.response.activitypage.ShelfProductInfoResultVO;
import com.sankuai.dz.srcm.automatedmanagement.response.productpool.QueryAllTagsVO;
import com.sankuai.general.product.query.center.client.service.DealGroupQueryService;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductItemsDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductTagsDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationActivSceneCodeDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationProductTagsDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationActivSceneCodeDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductActivityPageDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductItemsDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductTagsDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationProductTagMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.activitypage.ActivityPageManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.QueryProductInfoDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.QueryTagNamesByProductIdsAndTypesDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.QueryVisibleTagNamesDTO;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ProductManagementService_QueryActivityPagesByIdTest {

    @InjectMocks
    private ProductManagementService productManagementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductActivityPageDOMapper activityPageDOMapper;

    private List<Long> pageIds;

    private List<ScrmAmProcessOrchestrationProductActivityPageDO> expectedActivityPages;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductTagsDOMapper productTagsDOMapper;

    private String appId = "appId";

    private String tagName = "tagName";

    private List<ScrmAmProcessOrchestrationProductTagsDO> tagsDOList = new ArrayList<>(Arrays.asList(createTagDO(1L, "tag1"), createTagDO(2L, "tag2")));

    @Mock(lenient = true)
    private ActivityPageManagementService activityPageManagementService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationProductItemsDOMapper productItemsDOMapper;

    @Mock(lenient = true)
    private ExtScrmAmProcessOrchestrationProductTagMapDOMapper extScrmAmProcessOrchestrationProductTagMapDOMapper;

    @Mock(lenient = true)
    private ProductInfoService productInfoService;

    @Mock(lenient = true)
    private DealGroupQueryService queryCenterDealGroupQueryService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationActivSceneCodeDOMapper activSceneCodeDOMapper;

    @Before
    public void setUp() {
        pageIds = Arrays.asList(1L, 2L, 3L);
        expectedActivityPages = Arrays.asList(new ScrmAmProcessOrchestrationProductActivityPageDO(), new ScrmAmProcessOrchestrationProductActivityPageDO(), new ScrmAmProcessOrchestrationProductActivityPageDO());
    }

    private ScrmAmProcessOrchestrationProductTagsDO createTagDO(Long id, String name) {
        ScrmAmProcessOrchestrationProductTagsDO tagDO = new ScrmAmProcessOrchestrationProductTagsDO();
        tagDO.setId(id);
        tagDO.setTagName(name);
        tagDO.setAppId(appId);
        tagDO.setTagType(ScrmAmProcessOrchestrationProductTagsTagTypeEnum.VISIBLE.getCode());
        return tagDO;
    }

    private void mockProductItemsMapper() {
        List<ScrmAmProcessOrchestrationProductItemsDO> mockProductItems = new ArrayList<>();
        ScrmAmProcessOrchestrationProductItemsDO mockItem = new ScrmAmProcessOrchestrationProductItemsDO();
        // Mocked product type code
        int productTypeCode = 1;
        mockItem.setProductType(productTypeCode);
        // Ensure productId is not null
        mockItem.setProductId(1L);
        mockProductItems.add(mockItem);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(mockProductItems);
    }

    private void mockProductItemsDOMapper(List<ScrmAmProcessOrchestrationProductItemsDO> mockProductItemsDOS) {
        when(productItemsDOMapper.selectByExample(any())).thenReturn(mockProductItemsDOS);
    }

    private ScrmAmProcessOrchestrationProductItemsDO createProductItemsDO(Long productId, Integer productType) {
        ScrmAmProcessOrchestrationProductItemsDO mockProductItemsDO = new ScrmAmProcessOrchestrationProductItemsDO();
        mockProductItemsDO.setProductId(productId);
        mockProductItemsDO.setProductType(productType);
        return mockProductItemsDO;
    }

    private ScrmProcessOrchestrationActionAttachmentDTO createActionAttachmentDTO() {
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = new ScrmProcessOrchestrationActionAttachmentDTO();
        actionAttachmentDTO.setProcessOrchestrationId(1L);
        actionAttachmentDTO.setProcessOrchestrationVersion("1.0");
        // Set processOrchestrationNodeId to avoid RuntimeException
        actionAttachmentDTO.setProcessOrchestrationNodeId(1L);
        return actionAttachmentDTO;
    }

    private ScrmProcessOrchestrationAttachmentSupplyDetailDTO createSupplyDetailDTO(int supplyType, int supplyScope) {
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        supplyDetailDTO.setSupplyType(supplyType);
        // Initialize supplyScope to avoid NullPointerException
        supplyDetailDTO.setSupplyScope(supplyScope);
        return supplyDetailDTO;
    }

    private ScrmAmProcessOrchestrationProductActivityPageDO createPageInfo() {
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = new ScrmAmProcessOrchestrationProductActivityPageDO();
        return pageInfo;
    }


    /**
     * 测试queryActivityPagesById方法，正常场景
     */
    @Test
    public void testQueryActivityPagesByIdNormal() {
        when(activityPageDOMapper.selectByExample(any())).thenReturn(expectedActivityPages);
        List<ScrmAmProcessOrchestrationProductActivityPageDO> actualActivityPages = productManagementService.queryActivityPagesById(pageIds);
        assertEquals(expectedActivityPages, actualActivityPages);
        verify(activityPageDOMapper, times(1)).selectByExample(any());
    }

    /**
     * 测试queryActivityPagesById方法，边界场景
     */
    @Test
    public void testQueryActivityPagesByIdBoundary() {
        when(activityPageDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        List<ScrmAmProcessOrchestrationProductActivityPageDO> actualActivityPages = productManagementService.queryActivityPagesById(Collections.emptyList());
        assertEquals(Collections.emptyList(), actualActivityPages);
        verify(activityPageDOMapper, times(1)).selectByExample(any());
    }

    /**
     * 测试queryActivityPagesById方法，异常场景
     */
    @Test
    public void testQueryActivityPagesByIdException() {
        when(activityPageDOMapper.selectByExample(any())).thenThrow(new RuntimeException());
        try {
            productManagementService.queryActivityPagesById(pageIds);
        } catch (RuntimeException e) {
            assertEquals(RuntimeException.class, e.getClass());
        }
        verify(activityPageDOMapper, times(1)).selectByExample(any());
    }

    @Test(expected = Exception.class)
    public void testFuzzyQueryTagsInfoWhenAppIdOrTagNameIsNull() throws Throwable {
        productManagementService.fuzzyQueryTagsInfo(null, null);
    }

    @Test
    public void testFuzzyQueryTagsInfoWhenTagsDOListIsEmpty() throws Throwable {
        when(productTagsDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductTagsDOExample.class))).thenReturn(Collections.emptyList());
        List<QueryAllTagsVO> result = productManagementService.fuzzyQueryTagsInfo(appId, tagName);
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testFuzzyQueryTagsInfoWhenTagNameIsNull() throws Throwable {
        when(productTagsDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductTagsDOExample.class))).thenReturn(tagsDOList);
        List<QueryAllTagsVO> result = productManagementService.fuzzyQueryTagsInfo(appId, null);
        assertEquals(tagsDOList.size(), result.size());
    }

    @Test
    public void testFuzzyQueryTagsInfoWhenTagsDOListDoesNotContainTagName() throws Throwable {
        when(productTagsDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductTagsDOExample.class))).thenReturn(tagsDOList);
        List<QueryAllTagsVO> result = productManagementService.fuzzyQueryTagsInfo(appId, "nonexistent");
        assertEquals(0, result.size());
    }

    @Test
    public void testFuzzyQueryTagsInfoWhenTagsDOListContainsTagName() throws Throwable {
        ScrmAmProcessOrchestrationProductTagsDO matchingTag = createTagDO(3L, "matching" + tagName);
        List<ScrmAmProcessOrchestrationProductTagsDO> extendedList = new ArrayList<>(tagsDOList);
        extendedList.add(matchingTag);
        when(productTagsDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductTagsDOExample.class))).thenReturn(extendedList);
        List<QueryAllTagsVO> result = productManagementService.fuzzyQueryTagsInfo(appId, tagName);
        assertEquals(1, result.size());
        assertEquals(matchingTag.getId(), result.get(0).getTagId());
        assertEquals(matchingTag.getTagName(), result.get(0).getTagName());
    }

    @Test
    public void testQueryPreviewShelfProductInfoNormal() throws Throwable {
        Long activityPageId = 1L;
        String appId = "appId";
        List<Long> productIds = new ArrayList<>();
        productIds.add(1L);
        when(activityPageManagementService.getProductIdsByActivityPageId(appId, activityPageId)).thenReturn(productIds);
        mockProductItemsMapper();
        ShelfProductInfoResultVO result = productManagementService.queryPreviewShelfProductInfo(activityPageId, appId);
        assertNotNull(result);
        assertEquals(0, result.getShelfProductInfoDTOS().size());
    }

    @Test
    public void testQueryPreviewShelfProductInfoProductTypeError() throws Throwable {
        Long activityPageId = 1L;
        String appId = "appId";
        List<Long> productIds = new ArrayList<>();
        productIds.add(1L);
        when(activityPageManagementService.getProductIdsByActivityPageId(appId, activityPageId)).thenReturn(productIds);
        mockProductItemsMapper();
        ShelfProductInfoResultVO result = productManagementService.queryPreviewShelfProductInfo(activityPageId, appId);
        assertNotNull(result);
        assertEquals(0, result.getShelfProductInfoDTOS().size());
    }

    @Test
    public void testQueryPreviewShelfProductInfoActivityPageNotExist() throws Throwable {
        Long activityPageId = 1L;
        String appId = "appId";
        when(activityPageManagementService.getProductIdsByActivityPageId(appId, activityPageId)).thenReturn(new ArrayList<>());
        ShelfProductInfoResultVO result = productManagementService.queryPreviewShelfProductInfo(activityPageId, appId);
        assertNotNull(result);
    }

    @Test
    public void testQueryPreviewShelfProductInfoProductNotExist() throws Throwable {
        Long activityPageId = 1L;
        String appId = "appId";
        when(activityPageManagementService.getProductIdsByActivityPageId(appId, activityPageId)).thenReturn(new ArrayList<>());
        ShelfProductInfoResultVO result = productManagementService.queryPreviewShelfProductInfo(activityPageId, appId);
        assertNotNull(result);
    }

    @Test
    public void testGetTagNamesByProductIdsAndTypesAppIdIsNull() throws Throwable {
        List<QueryTagNamesByProductIdsAndTypesDTO> products = Arrays.asList(new QueryTagNamesByProductIdsAndTypesDTO(1, 1L));
        Map<Integer, Map<Long, List<String>>> result = productManagementService.getTagNamesByProductIdsAndTypes(null, products);
        assertNull(result);
    }

    @Test
    public void testGetTagNamesByProductIdsAndTypesProductsIsNull() throws Throwable {
        String appId = "appId";
        Map<Integer, Map<Long, List<String>>> result = productManagementService.getTagNamesByProductIdsAndTypes(appId, null);
        assertNull(result);
    }

    @Test
    public void testGetTagNamesByProductIdsAndTypesAppIdAndProductsAreNull() throws Throwable {
        Map<Integer, Map<Long, List<String>>> result = productManagementService.getTagNamesByProductIdsAndTypes(null, null);
        assertNull(result);
    }

    @Test
    public void testGetTagNamesByProductIdsAndTypesType2IdIsEmpty() throws Throwable {
        String appId = "appId";
        List<QueryTagNamesByProductIdsAndTypesDTO> products = Collections.emptyList();
        Map<Integer, Map<Long, List<String>>> result = productManagementService.getTagNamesByProductIdsAndTypes(appId, products);
        assertNull(result);
    }

    @Test
    public void testGetTagNamesByProductIdsAndTypesQueryVisibleTagNamesDTOListIsEmpty() throws Throwable {
        String appId = "appId";
        List<QueryTagNamesByProductIdsAndTypesDTO> products = Arrays.asList(new QueryTagNamesByProductIdsAndTypesDTO(1, 1L));
        when(extScrmAmProcessOrchestrationProductTagMapDOMapper.getTagNamesByAppIdAndProductTypeAndProductIds(anyString(), anyInt(), anyInt(), anyList())).thenReturn(Collections.emptyList());
        Map<Integer, Map<Long, List<String>>> result = productManagementService.getTagNamesByProductIdsAndTypes(appId, products);
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertTrue(result.containsKey(1));
        assertTrue(result.get(1).isEmpty());
    }

    @Test
    public void testGetTagNamesByProductIdsAndTypesNormal() throws Throwable {
        String appId = "appId";
        List<QueryTagNamesByProductIdsAndTypesDTO> products = Arrays.asList(new QueryTagNamesByProductIdsAndTypesDTO(1, 1L));
        when(extScrmAmProcessOrchestrationProductTagMapDOMapper.getTagNamesByAppIdAndProductTypeAndProductIds(anyString(), anyInt(), anyInt(), anyList())).thenReturn(Arrays.asList(new QueryVisibleTagNamesDTO(1L, 1, "Tag1")));
        Map<Integer, Map<Long, List<String>>> result = productManagementService.getTagNamesByProductIdsAndTypes(appId, products);
        assertNotNull(result);
        assertFalse(result.isEmpty());
        assertTrue(result.containsKey(1));
        assertFalse(result.get(1).isEmpty());
        assertEquals(Arrays.asList("Tag1"), result.get(1).get(1L));
    }

    /**
     * Test getProductTypeByProductId method when appId or productId is null.
     */
    @Test
    public void testGetProductTypeByProductId_NullInput() throws Throwable {
        assertNull(productManagementService.getProductTypeByProductId(null, 1L));
        assertNull(productManagementService.getProductTypeByProductId("appId", null));
    }

    /**
     * Test getProductTypeByProductId method when there is no corresponding record in the database.
     */
    @Test
    public void testGetProductTypeByProductId_NoRecord() throws Throwable {
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        assertNull(productManagementService.getProductTypeByProductId("appId", 1L));
    }

    /**
     * Test getProductTypeByProductId method when there is a corresponding record in the database.
     */
    @Test
    public void testGetProductTypeByProductId_RecordExists() throws Throwable {
        ScrmAmProcessOrchestrationProductItemsDO productItemsDO = new ScrmAmProcessOrchestrationProductItemsDO();
        productItemsDO.setProductType(1);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(productItemsDO));
        assertEquals(Integer.valueOf(1), productManagementService.getProductTypeByProductId("appId", 1L));
    }

    /**
     * Test getProductsByAppId method when appId is null, it should return null.
     */
    @Test
    public void testGetProductsByAppIdWhenAppIdIsNull() throws Throwable {
        // Arrange
        String appId = null;
        // Act
        List<ScrmAmProcessOrchestrationProductItemsDO> result = productManagementService.pageProductsByAppId(appId, 0, 100);
        // Assert
        assertNull(result);
    }

    /**
     * Test getProductsByAppId method when appId is not null, but there are no corresponding records in the database, it should return an empty list.
     */
    @Test
    public void testGetProductsByAppIdWhenNoRecordInDB() throws Throwable {
        // Arrange
        String appId = "testAppId";
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // Act
        List<ScrmAmProcessOrchestrationProductItemsDO> result = productManagementService.pageProductsByAppId(appId, 0, 100);
        // Assert
        assertTrue(result.isEmpty());
    }

    /**
     * Test getProductsByAppId method when appId is not null and there are corresponding records in the database, it should return the queried records.
     */
    @Test
    public void testGetProductsByAppIdWhenRecordsInDB() throws Throwable {
        // Arrange
        String appId = "testAppId";
        ScrmAmProcessOrchestrationProductItemsDO productItemsDO = new ScrmAmProcessOrchestrationProductItemsDO();
        productItemsDO.setAppId(appId);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(productItemsDO));
        // Act
        List<ScrmAmProcessOrchestrationProductItemsDO> result = productManagementService.pageProductsByAppId(appId, 0, 100);
        // Assert
        assertTrue(result.size() == 1 && result.get(0).getAppId().equals(appId));
    }

    /**
     * Test case when appId or mtProductId is null.
     */
    @Test
    public void testGetDpProductIdByMtProductIdAndProductType_NullInput() throws Throwable {
        assertNull(productManagementService.getDpProductIdByMtProductIdAndProductType(null, 1L, 1));
        assertNull(productManagementService.getDpProductIdByMtProductIdAndProductType("appId", null, 1));
    }

    /**
     * Test case when the query result is empty.
     */
    @Test
    public void testGetDpProductIdByMtProductIdAndProductType_EmptyResult() throws Throwable {
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        assertNull(productManagementService.getDpProductIdByMtProductIdAndProductType("appId", 1L, 1));
    }

    /**
     * Test case when the query result is not empty.
     */
    @Test
    public void testGetDpProductIdByMtProductIdAndProductType_NotEmptyResult() throws Throwable {
        ScrmAmProcessOrchestrationProductItemsDO mockDO = new ScrmAmProcessOrchestrationProductItemsDO();
        mockDO.setDpProductId(2L);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(mockDO));
        assertEquals(Long.valueOf(2L), productManagementService.getDpProductIdByMtProductIdAndProductType("appId", 1L, 1));
    }

    /**
     * Test fuzzyQueryTags method when both appId and tagName are not null but there is no matching tagName in the database, should return an empty list
     */
    @Test
    public void testFuzzyQueryTagsNoMatch() throws Throwable {
        String appId = "testAppId";
        String tagName = "testTagName";
        when(productTagsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        List<String> result = productManagementService.fuzzyQueryTags(appId, tagName);
        assertEquals(Collections.emptyList(), result);
    }

    /**
     * Test fuzzyQueryTags method when both appId and tagName are not null and there is a matching tagName in the database, should return a non-empty list
     */
    @Test
    public void testFuzzyQueryTagsMatch() throws Throwable {
        String appId = "testAppId";
        String tagName = "testTagName";
        List<ScrmAmProcessOrchestrationProductTagsDO> tagsDOList = Collections.singletonList(new ScrmAmProcessOrchestrationProductTagsDO());
        when(productTagsDOMapper.selectByExample(any())).thenReturn(tagsDOList);
        List<String> result = productManagementService.fuzzyQueryTags(appId, tagName);
        assertEquals(1, result.size());
    }

    /**
     * Test fuzzyQueryTags method when either appId or tagName is null, should return an empty list
     */
    @Test
    public void testFuzzyQueryTagsEmpty() throws Throwable {
        when(productTagsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // Adjusted to avoid null values that cause exception
        List<String> result = productManagementService.fuzzyQueryTags("", "");
        assertEquals(Collections.emptyList(), result);
    }

    @Test
    public void testQueryProductInfoByProductIdsEmpty() throws Throwable {
        List<Long> productIds = new ArrayList<>();
        String appId = "testAppId";
        List<QueryProductInfoDTO> result = productManagementService.queryProductInfoByProductIds(productIds, appId);
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryProductInfoByProductIdsGeneralException() throws Throwable {
        List<Long> productIds = Arrays.asList(1L);
        String appId = "testAppId";
        mockProductItemsDOMapper(Collections.singletonList(createProductItemsDO(1L, 1)));
        List<QueryProductInfoDTO> result = productManagementService.queryProductInfoByProductIds(productIds, appId);
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryProductInfoByProductIdsGeneralEmpty() throws Throwable {
        List<Long> productIds = Arrays.asList(1L);
        String appId = "testAppId";
        mockProductItemsDOMapper(Collections.singletonList(createProductItemsDO(1L, 1)));
        List<QueryProductInfoDTO> result = productManagementService.queryProductInfoByProductIds(productIds, appId);
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryProductInfoByProductIdsBulkOrderException() throws Throwable {
        List<Long> productIds = Arrays.asList(1L);
        String appId = "testAppId";
        mockProductItemsDOMapper(Collections.singletonList(createProductItemsDO(1L, 2)));
        List<QueryProductInfoDTO> result = productManagementService.queryProductInfoByProductIds(productIds, appId);
        assertEquals(0, result.size());
    }

    @Test
    public void testQueryProductInfoByProductIdsBulkOrderEmpty() throws Throwable {
        List<Long> productIds = Arrays.asList(1L);
        String appId = "testAppId";
        mockProductItemsDOMapper(Collections.singletonList(createProductItemsDO(1L, 2)));
        List<QueryProductInfoDTO> resultList = productManagementService.queryProductInfoByProductIds(productIds, appId);
        assertEquals(0, resultList.size());
    }

    @Test
    public void testQueryProductInfoByProductIdAndAppId_NoProductFound() throws Throwable {
        Long productId = 1L;
        String appId = "testAppId";
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        ProductInfoDTO result = productManagementService.queryProductInfoByProductIdAndAppId(productId, appId);
        assertNull(result);
    }

    @Test
    public void testQueryProductInfoByProductIdAndAppId_NullProductId() throws Throwable {
        Long productId = null;
        String appId = "testAppId";
        ProductInfoDTO result = productManagementService.queryProductInfoByProductIdAndAppId(productId, appId);
        assertNull(result);
    }

    @Test
    public void testQueryProductInfoByProductIdAndAppId_ZeroProductId() throws Throwable {
        Long productId = 0L;
        String appId = "testAppId";
        ProductInfoDTO result = productManagementService.queryProductInfoByProductIdAndAppId(productId, appId);
        assertNull(result);
    }

    @Test
    public void testQueryProductInfoByProductIdAndAppId_NegativeProductId() throws Throwable {
        Long productId = -1L;
        String appId = "testAppId";
        ProductInfoDTO result = productManagementService.queryProductInfoByProductIdAndAppId(productId, appId);
        assertNull(result);
    }

    @Test
    public void testQueryProductInfoByProductIdAndAppId_NullAppId() throws Throwable {
        Long productId = 1L;
        String appId = null;
        ProductInfoDTO result = productManagementService.queryProductInfoByProductIdAndAppId(productId, appId);
        assertNull(result);
    }

    @Test
    public void testQueryProductInfoByProductIdAndAppId_EmptyAppId() throws Throwable {
        Long productId = 1L;
        String appId = "";
        ProductInfoDTO result = productManagementService.queryProductInfoByProductIdAndAppId(productId, appId);
        assertNull(result);
    }

    @Test
    public void testGetActivSceneCodeDOSceneCodeDONotEmpty() throws Throwable {
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = createActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = createSupplyDetailDTO(1, 1);
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = createPageInfo();
        String communityDistributorCode = "communityDistributorCode";
        String appId = "appId";
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        when(activSceneCodeDOMapper.selectByExample(any(ScrmAmProcessOrchestrationActivSceneCodeDOExample.class))).thenReturn(Collections.singletonList(sceneCodeDO));
        ScrmAmProcessOrchestrationActivSceneCodeDO result = productManagementService.getActivSceneCodeDO(actionAttachmentDTO, supplyDetailDTO, pageInfo, communityDistributorCode, appId);
        assertEquals(sceneCodeDO, result);
    }

    @Test
    public void testGetActivSceneCodeDOSceneCodeDOEmptySupplyTypeIsOne() throws Throwable {
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = createActionAttachmentDTO();
        // Initialize supplyScope to avoid NullPointerException
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = createSupplyDetailDTO(1, 1);
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = createPageInfo();
        String communityDistributorCode = "communityDistributorCode";
        String appId = "appId";
        // Mock the productTagsDOMapper to return a non-null list
        when(productTagsDOMapper.selectByExample(any(ScrmAmProcessOrchestrationProductTagsDOExample.class))).thenReturn(Collections.emptyList());
        ScrmAmProcessOrchestrationActivSceneCodeDO result = productManagementService.getActivSceneCodeDO(actionAttachmentDTO, supplyDetailDTO, pageInfo, communityDistributorCode, appId);
        assertNotNull(result);
    }

    @Test
    public void testGetActivSceneCodeDOSceneCodeDOEmptySupplyTypeIsTwo() throws Throwable {
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = createActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = createSupplyDetailDTO(2, 1);
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = createPageInfo();
        String communityDistributorCode = "communityDistributorCode";
        String appId = "appId";
        ScrmAmProcessOrchestrationActivSceneCodeDO result = productManagementService.getActivSceneCodeDO(actionAttachmentDTO, supplyDetailDTO, pageInfo, communityDistributorCode, appId);
        assertNotNull(result);
    }

    @Test
    public void testGetActivSceneCodeDOSceneCodeDOEmptySupplyTypeNotOneAndTwo() throws Throwable {
        ScrmProcessOrchestrationActionAttachmentDTO actionAttachmentDTO = createActionAttachmentDTO();
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO supplyDetailDTO = createSupplyDetailDTO(3, 1);
        ScrmAmProcessOrchestrationProductActivityPageDO pageInfo = createPageInfo();
        String communityDistributorCode = "communityDistributorCode";
        String appId = "appId";
        ScrmAmProcessOrchestrationActivSceneCodeDO result = productManagementService.getActivSceneCodeDO(actionAttachmentDTO, supplyDetailDTO, pageInfo, communityDistributorCode, appId);
        assertNotNull(result);
    }
}
