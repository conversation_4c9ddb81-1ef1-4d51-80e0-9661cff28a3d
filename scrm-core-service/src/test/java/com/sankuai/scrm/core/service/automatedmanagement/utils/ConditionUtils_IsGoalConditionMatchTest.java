package com.sankuai.scrm.core.service.automatedmanagement.utils;

import com.sankuai.scrm.core.service.automatedmanagement.domainservice.config.ConfigDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.utils.dto.ConditionUtilComputeResult;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserTag;
import com.sankuai.scrm.core.service.user.enums.ScrmUserTagEnum;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.config.ScrmFilterFieldConfigDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.config.ScrmOperatorDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationGoalDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationGoalConditionDetailDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.utils.dto.ConditionUtilComputeResultHitTag;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.config.ScrmFilterFieldDTO;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ConditionUtils_IsGoalConditionMatchTest {

    @InjectMocks
    private ConditionUtils conditionUtils;

    @Mock(lenient = true)
    private ConfigDomainService configDomainService;

    @Mock(lenient = true)
    private ExecuteManagementService executeManagementService;

    private ScrmAmProcessOrchestrationExecuteLogDO executeLogDO;

    private ScrmProcessOrchestrationGoalDTO goalDTO;

    private List<ScrmUserTag> userTagDOS;

    @Before
    public void setUp() {
        executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setAppId("testAppId");
        goalDTO = new ScrmProcessOrchestrationGoalDTO();
        userTagDOS = new ArrayList<>();
    }

    private void setupFilterFieldConfig(Map<Long, ScrmFilterFieldConfigDTO> map, Long id, String operatorName, String fieldType) {
        ScrmFilterFieldConfigDTO filterFieldConfigDTO = new ScrmFilterFieldConfigDTO();
        Map<Long, ScrmOperatorDTO> operatorDTOMap = new HashMap<>();
        ScrmOperatorDTO operatorDTO = new ScrmOperatorDTO();
        operatorDTO.setOperatorName(operatorName);
        operatorDTO.setFieldType(fieldType);
        operatorDTOMap.put(1L, operatorDTO);
        filterFieldConfigDTO.setOperatorDTOMap(operatorDTOMap);
        filterFieldConfigDTO.setFilterField(new ScrmFilterFieldDTO());
        map.put(id, filterFieldConfigDTO);
    }

    private ScrmUserTag createUserTag(Long tagId, String tagValue) {
        ScrmUserTag userTag = new ScrmUserTag();
        userTag.setTagId(tagId);
        userTag.setAppId(executeLogDO.getAppId());
        userTag.setTagValue(tagValue);
        return userTag;
    }

    @Test
    public void testIsGoalConditionMatchGoalDtoIsNull() throws Throwable {
        ConditionUtilComputeResult result = conditionUtils.isGoalConditionMatch(executeLogDO, null, userTagDOS);
        assertTrue(result.isPassed());
    }

    @Test
    public void testIsGoalConditionMatchGoalConditionListIsEmpty() throws Throwable {
        goalDTO.setGoalConditionList(new ArrayList<>());
        ConditionUtilComputeResult result = conditionUtils.isGoalConditionMatch(executeLogDO, goalDTO, userTagDOS);
        assertTrue(result.isPassed());
    }

    @Test
    public void testIsGoalConditionMatchFilterFieldConfigNotContainsFilterFieldId() throws Throwable {
        ScrmProcessOrchestrationGoalConditionDetailDTO conditionDetailDTO = new ScrmProcessOrchestrationGoalConditionDetailDTO();
        conditionDetailDTO.setFilterFieldId(1L);
        goalDTO.setGoalConditionList(Arrays.asList(conditionDetailDTO));
        Map<Long, ScrmFilterFieldConfigDTO> filterFieldConfigDTOMap = new HashMap<>();
        when(configDomainService.getFilterFieldConfigsMapByAppId(executeLogDO.getAppId())).thenReturn(filterFieldConfigDTOMap);
        ConditionUtilComputeResult result = conditionUtils.isGoalConditionMatch(executeLogDO, goalDTO, userTagDOS);
        assertFalse(result.isPassed());
    }

    @Test
    public void testIsGoalConditionMatchOperatorDtoIsNull() throws Throwable {
        ScrmProcessOrchestrationGoalConditionDetailDTO conditionDetailDTO = new ScrmProcessOrchestrationGoalConditionDetailDTO();
        conditionDetailDTO.setFilterFieldId(1L);
        conditionDetailDTO.setOperatorId(1L);
        goalDTO.setGoalConditionList(Arrays.asList(conditionDetailDTO));
        Map<Long, ScrmFilterFieldConfigDTO> filterFieldConfigDTOMap = new HashMap<>();
        ScrmFilterFieldConfigDTO filterFieldConfigDTO = new ScrmFilterFieldConfigDTO();
        filterFieldConfigDTO.setOperatorDTOMap(new HashMap<>());
        filterFieldConfigDTOMap.put(1L, filterFieldConfigDTO);
        when(configDomainService.getFilterFieldConfigsMapByAppId(executeLogDO.getAppId())).thenReturn(filterFieldConfigDTOMap);
        ConditionUtilComputeResult result = conditionUtils.isGoalConditionMatch(executeLogDO, goalDTO, userTagDOS);
        assertFalse(result.isPassed());
    }

    @Test
    public void testIsGoalConditionMatchUserTagDoIsNull() throws Throwable {
        ScrmProcessOrchestrationGoalConditionDetailDTO conditionDetailDTO = new ScrmProcessOrchestrationGoalConditionDetailDTO();
        conditionDetailDTO.setFilterFieldId(1L);
        conditionDetailDTO.setOperatorId(1L);
        goalDTO.setGoalConditionList(Arrays.asList(conditionDetailDTO));
        Map<Long, ScrmFilterFieldConfigDTO> filterFieldConfigDTOMap = new HashMap<>();
        ScrmFilterFieldConfigDTO filterFieldConfigDTO = new ScrmFilterFieldConfigDTO();
        Map<Long, ScrmOperatorDTO> operatorDTOMap = new HashMap<>();
        operatorDTOMap.put(1L, new ScrmOperatorDTO());
        filterFieldConfigDTO.setOperatorDTOMap(operatorDTOMap);
        filterFieldConfigDTOMap.put(1L, filterFieldConfigDTO);
        when(configDomainService.getFilterFieldConfigsMapByAppId(executeLogDO.getAppId())).thenReturn(filterFieldConfigDTOMap);
        ConditionUtilComputeResult result = conditionUtils.isGoalConditionMatch(executeLogDO, goalDTO, userTagDOS);
        assertFalse(result.isPassed());
    }

    @Test
    public void testIsGoalConditionMatchSpecialCheckLogic() throws Throwable {
        ScrmProcessOrchestrationGoalConditionDetailDTO conditionDetailDTO = new ScrmProcessOrchestrationGoalConditionDetailDTO();
        conditionDetailDTO.setFilterFieldId(ScrmUserTagEnum.IS_FRIEND.getTagId());
        conditionDetailDTO.setOperatorId(1L);
        conditionDetailDTO.setParam(Arrays.asList("true"));
        goalDTO.setGoalConditionList(Arrays.asList(conditionDetailDTO));
        Map<Long, ScrmFilterFieldConfigDTO> filterFieldConfigDTOMap = new HashMap<>();
        setupFilterFieldConfig(filterFieldConfigDTOMap, ScrmUserTagEnum.IS_FRIEND.getTagId(), "equal", "java.lang.Boolean");
        when(configDomainService.getFilterFieldConfigsMapByAppId(executeLogDO.getAppId())).thenReturn(filterFieldConfigDTOMap);
        ScrmUserTag userTagDO = createUserTag(ScrmUserTagEnum.IS_FRIEND.getTagId(), "true");
        userTagDOS.add(userTagDO);
        ConditionUtilComputeResult result = conditionUtils.isGoalConditionMatch(executeLogDO, goalDTO, userTagDOS);
        assertTrue(result.isPassed());
    }
}
