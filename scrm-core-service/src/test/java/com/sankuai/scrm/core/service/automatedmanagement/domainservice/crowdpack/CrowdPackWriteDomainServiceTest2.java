package com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack;

import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackDetailInfoDOMapper;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class CrowdPackWriteDomainServiceTest2 {

    @InjectMocks
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock
    private ExtScrmAmCrowdPackDetailInfoDOMapper detailInfoDOMapper;

    @Test
    public void testDeleteCrowdPackDetailInfoByPackIdAndVersion() {
        // arrange
        Long packId = 1L;
        // act
        crowdPackWriteDomainService.concurrencyDeleteCrowdPackDetailInfoByPackId(packId);
        // assert
        verify(detailInfoDOMapper, times(1)).deleteByExample(any());
    }
}
