package com.sankuai.scrm.core.service.external.contact.domain;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.meituan.image.client.utils.Md5Utils;
import com.sankuai.inf.kms.pangolin.api.service.IEncryptService;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ScrmMobileAddFriendTaskDO;
import com.sankuai.scrm.core.service.external.contact.dal.example.ScrmMobileAddFriendTaskDOExample;
import com.sankuai.scrm.core.service.external.contact.dal.mapper.ext.ExtScrmMobileAddFriendTaskDOMapper;
import java.security.GeneralSecurityException;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.apache.commons.collections.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MobileAddFriendRealTimeDomainServiceQueryMobileAddFriendRealTimeTaskByIdTest {

    @Mock
    private ExtScrmMobileAddFriendTaskDOMapper mobileAddFriendTaskDOMapper;

    @Mock
    private IEncryptService phoneEncryptService;

    @InjectMocks
    private MobileAddFriendRealTimeDomainService mobileAddFriendRealTimeDomainService;

    private ScrmMobileAddFriendTaskDO testTask;

    private final Long testId = 1L;

    private final String encryptedPhone = "encrypted123";

    private final String decryptedPhone = "77143689225";

    @BeforeEach
    void setUp() {
        testTask = new ScrmMobileAddFriendTaskDO();
        testTask.setId(testId);
        testTask.setAddNumber(encryptedPhone);
    }

    /**
     * 测试正常情况：成功查询到任务并解密电话号码
     */
    @Test
    void testQueryMobileAddFriendRealTimeTaskById_Success() throws Throwable {
        // arrange
        List<ScrmMobileAddFriendTaskDO> taskList = Collections.singletonList(testTask);
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(taskList);
        when(phoneEncryptService.decryptUTF8String(encryptedPhone)).thenReturn(decryptedPhone);
        // act
        ScrmMobileAddFriendTaskDO result = mobileAddFriendRealTimeDomainService.queryMobileAddFriendRealTimeTaskById(testId);
        // assert
        assertNotNull(result);
        assertEquals(testId, result.getId());
        assertEquals(decryptedPhone, result.getAddNumber());
        verify(mobileAddFriendTaskDOMapper).selectByExample(any(ScrmMobileAddFriendTaskDOExample.class));
        verify(phoneEncryptService).decryptUTF8String(encryptedPhone);
    }

    /**
     * 测试任务不存在的情况：查询返回空列表
     */
    @Test
    void testQueryMobileAddFriendRealTimeTaskById_TaskNotFound() throws Throwable {
        // arrange
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(Collections.emptyList());
        // act
        ScrmMobileAddFriendTaskDO result = mobileAddFriendRealTimeDomainService.queryMobileAddFriendRealTimeTaskById(testId);
        // assert
        assertNull(result);
        verify(mobileAddFriendTaskDOMapper).selectByExample(any(ScrmMobileAddFriendTaskDOExample.class));
        verify(phoneEncryptService, never()).decryptUTF8String(anyString());
    }

    /**
     * 测试解密失败的情况：抛出GeneralSecurityException异常
     */
    @Test
    void testQueryMobileAddFriendRealTimeTaskById_DecryptFailed() throws Throwable {
        // arrange
        List<ScrmMobileAddFriendTaskDO> taskList = Collections.singletonList(testTask);
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(taskList);
        when(phoneEncryptService.decryptUTF8String(encryptedPhone)).thenThrow(new GeneralSecurityException("Decrypt failed"));
        // act
        ScrmMobileAddFriendTaskDO result = mobileAddFriendRealTimeDomainService.queryMobileAddFriendRealTimeTaskById(testId);
        // assert
        assertNull(result);
        verify(mobileAddFriendTaskDOMapper).selectByExample(any(ScrmMobileAddFriendTaskDOExample.class));
        verify(phoneEncryptService).decryptUTF8String(encryptedPhone);
    }

    /**
     * 测试查询返回多条记录的情况：虽然业务上id应该是唯一的
     */
    @Test
    void testQueryMobileAddFriendRealTimeTaskById_MultipleTasksFound() throws Throwable {
        // arrange
        ScrmMobileAddFriendTaskDO anotherTask = new ScrmMobileAddFriendTaskDO();
        anotherTask.setId(2L);
        List<ScrmMobileAddFriendTaskDO> taskList = new ArrayList<>();
        taskList.add(testTask);
        taskList.add(anotherTask);
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(taskList);
        when(phoneEncryptService.decryptUTF8String(encryptedPhone)).thenReturn(decryptedPhone);
        // act
        ScrmMobileAddFriendTaskDO result = mobileAddFriendRealTimeDomainService.queryMobileAddFriendRealTimeTaskById(testId);
        // assert
        assertNotNull(result);
        assertEquals(testId, result.getId());
        assertEquals(decryptedPhone, result.getAddNumber());
        verify(mobileAddFriendTaskDOMapper).selectByExample(any(ScrmMobileAddFriendTaskDOExample.class));
        verify(phoneEncryptService).decryptUTF8String(encryptedPhone);
    }

    /**
     * 测试id为null的情况：预期会抛出RuntimeException
     */
    @Test
    void testQueryMobileAddFriendRealTimeTaskById_NullId() throws Throwable {
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            mobileAddFriendRealTimeDomainService.queryMobileAddFriendRealTimeTaskById(null);
        });
        verify(mobileAddFriendTaskDOMapper, never()).selectByExample(any());
        verify(phoneEncryptService, never()).decryptUTF8String(anyString());
    }

    /**
     * 测试id为负数的情况
     */
    @Test
    void testQueryMobileAddFriendRealTimeTaskById_NegativeId() throws Throwable {
        // arrange
        when(mobileAddFriendTaskDOMapper.selectByExample(any(ScrmMobileAddFriendTaskDOExample.class))).thenReturn(Collections.emptyList());
        // act
        ScrmMobileAddFriendTaskDO result = mobileAddFriendRealTimeDomainService.queryMobileAddFriendRealTimeTaskById(-1L);
        // assert
        assertNull(result);
        verify(mobileAddFriendTaskDOMapper).selectByExample(any(ScrmMobileAddFriendTaskDOExample.class));
        verify(phoneEncryptService, never()).decryptUTF8String(anyString());
    }
}
