package com.sankuai.scrm.core.service.automatedmanagement.service.impl;

import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.automatedmanagement.request.coupon.attribution.ScrmCouponAttributionRequest;
import com.sankuai.dz.srcm.automatedmanagement.response.coupon.attribution.ScrmCouponAttributionResponse;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.ScrmSceneCouponRecords;
import com.sankuai.scrm.core.service.couponIntegration.dal.example.ScrmSceneCouponRecordsExample;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.ScrmSceneCouponRecordsMapper;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmCouponAttributionServiceImplTest {

    @Mock
    private ScrmSceneCouponRecordsMapper scrmSceneCouponRecordDOMapper;

    @InjectMocks
    private ScrmCouponAttributionServiceImpl scrmCouponAttributionService;

    private ScrmCouponAttributionRequest request;
    private ScrmSceneCouponRecords scrmSceneCouponRecordDO;

    @Before
    public void setUp() {
        request = new ScrmCouponAttributionRequest();
        scrmSceneCouponRecordDO = new ScrmSceneCouponRecords();
    }

    /**
     * 测试请求参数为null的情况
     */
    @Test
    public void testQueryCouponAttributionRequestIsNull() {
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttribution(null);
        assertFalse("应当返回失败", response.isSuccess());
    }

    /**
     * 测试请求中couponId为空的情况
     */
    @Test
    public void testQueryCouponAttributionCouponIdIsEmpty() {
        request.setCouponId("");
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttribution(request);
        assertFalse("应当返回失败", response.isSuccess());
    }

    /**
     * 测试请求中MtUserID为null的情况
     */
    @Test
    public void testQueryCouponAttributionMtUserIDIsNull() {
        request.setCouponId("123");
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttribution(request);
        assertFalse("应当返回失败", response.isSuccess());
    }

    /**
     * 测试数据库中没有找到对应记录的情况
     */
    @Test
    public void testQueryCouponAttributionRecordNotFound() {
        request.setCouponId("123");
        request.setMtUserID(456L);
        when(scrmSceneCouponRecordDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttribution(request);
        assertTrue("应当返回成功", response.isSuccess());
        assertFalse("CommunityChannel应为false", response.getData().isCommunityChannel());
    }

    /**
     * 测试用户ID不匹配的情况
     */
    @Test
    public void testQueryCouponAttributionUserIdNotMatch() {
        request.setCouponId("123");
        request.setMtUserID(456L);
        scrmSceneCouponRecordDO.setUserid(789L);
        when(scrmSceneCouponRecordDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(scrmSceneCouponRecordDO));
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttribution(request);
        assertFalse("应当返回失败", response.isSuccess());
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testQueryCouponAttributionSuccess() {
        request.setCouponId("123");
        request.setMtUserID(456L);
        scrmSceneCouponRecordDO.setUserid(456L);
        scrmSceneCouponRecordDO.setDistributorcode("distributorCode");
        when(scrmSceneCouponRecordDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(scrmSceneCouponRecordDO));
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttribution(request);
        assertTrue("应当返回成功", response.isSuccess());
        assertTrue("CommunityChannel应为true", response.getData().isCommunityChannel());
        assertEquals("DistributorCode应该匹配", "distributorCode", response.getData().getDistributorCode());
    }

    /**
     * 测试 queryCouponAttributionOnlyByUserId 方法，当请求参数中的 MtUserID 为 null 时
     */
    @Test
    public void testQueryCouponAttributionOnlyByUserIdWithNullUserId() {
        // arrange
        ScrmCouponAttributionRequest request = new ScrmCouponAttributionRequest();

        // act
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttributionOnlyByUserId(request);

        // assert
        assertNotNull(response);
        assertEquals("参数错误", response.getMsg());
    }
    /**
     * 测试 queryCouponAttributionOnlyByUserId 方法，当数据库中找到对应的记录时
     */
    @Test
    public void testQueryCouponAttributionOnlyByUserIdWithRecordFound() {
        // arrange
        ScrmCouponAttributionRequest request = new ScrmCouponAttributionRequest();
        request.setMtUserID(123L);
        List<ScrmSceneCouponRecords> recordList = new ArrayList<>();
        recordList.add(new ScrmSceneCouponRecords());
        when(scrmSceneCouponRecordDOMapper.selectByExample(any(ScrmSceneCouponRecordsExample.class))).thenReturn(recordList);

        // act
        RemoteResponse<ScrmCouponAttributionResponse> response = scrmCouponAttributionService.queryCouponAttributionOnlyByUserId(request);

        // assert
        assertNotNull(response);
        assertTrue(response.getData().isCommunityChannel());
    }

}
