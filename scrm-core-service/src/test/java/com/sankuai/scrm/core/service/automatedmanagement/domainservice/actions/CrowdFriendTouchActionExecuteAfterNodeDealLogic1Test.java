package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationExecutePlanDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationWxTouchTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessExecutionCheckPointDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.DeepSeaWxHandler;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.OfficialWxHandler;
import com.sankuai.scrm.core.service.message.push.constant.MsgPushConstant;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.anyBoolean;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class CrowdFriendTouchActionExecuteAfterNodeDealLogic1Test {

    @InjectMocks
    private CrowdFriendTouchAction crowdFriendTouchAction;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessExecutionCheckPointDOMapper processExecutionCheckPointDOMapper;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private OfficialWxHandler officialWxHandler;

    @Mock
    private DeepSeaWxHandler deepSeaWxHandler;

    private MockedStatic<Lion> lionMock;

    private MockedStatic<Environment> environmentMock;

    @BeforeEach
    void setUp() {
        lionMock = mockStatic(Lion.class);
        environmentMock = mockStatic(Environment.class);
        environmentMock.when(Environment::getAppName).thenReturn("test-app");
    }

    @AfterEach
    void tearDown() {
        lionMock.close();
        environmentMock.close();
    }

    private ScrmProcessOrchestrationDTO createProcessOrchestrationDTO() {
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        dto.setId(12345L);
        dto.setValidVersion("v1");
        dto.setAppId("test-app-id");
        return dto;
    }

    /**
     * Test case: Empty executorIds list should return early
     */
    @Test
    public void testExecuteAfterNodeDealLogic_EmptyExecutorIds() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createProcessOrchestrationDTO();
        List<String> executorIds = Collections.emptyList();
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        // act
        crowdFriendTouchAction.executeAfterNodeDealLogic(processOrchestrationDTO, executorIds, executeManagementDTO, stepExecuteResultDTO);
        // assert
        verify(wxInvokeDetailDOMapper, never()).selectByExample(any());
    }

    /**
     * Test case: No invoke details found should continue to next executor
     */
    @Test
    public void testExecuteAfterNodeDealLogic_NoInvokeDetails() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createProcessOrchestrationDTO();
        List<String> executorIds = Collections.singletonList("executor1");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(Collections.emptyList());
        // act
        crowdFriendTouchAction.executeAfterNodeDealLogic(processOrchestrationDTO, executorIds, executeManagementDTO, stepExecuteResultDTO);
        // assert
        verify(wxInvokeDetailDOMapper).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(deepSeaWxHandler, never()).dealRealTimeDeepSeaWxMessageV2(any(), any(), any(), any(), any());
        verify(officialWxHandler, never()).dealOfficialWxMessageV2(any(), any(), any(), any());
    }

    /**
     * Test case: Deep sea message type handling with old logic
     */
    @Test
    public void testExecuteAfterNodeDealLogic_DeepSeaMessageTypeWithOldLogic() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createProcessOrchestrationDTO();
        processOrchestrationDTO.setProcessOrchestrationType((byte) 1);
        List<String> executorIds = Collections.singletonList("executor1");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> invokeDetails = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setProcessOrchestrationNodeId(1L);
        detail.setType("deepSeaMessage");
        invokeDetails.add(detail);
        lionMock.when(() -> Lion.getBoolean(anyString(), eq(MsgPushConstant.PUSH_INTEGRATION_SWITCH_LION_KEY), anyBoolean())).thenReturn(false);
        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(invokeDetails);
        // act
        crowdFriendTouchAction.executeAfterNodeDealLogic(processOrchestrationDTO, executorIds, executeManagementDTO, stepExecuteResultDTO);
        // assert
        verify(deepSeaWxHandler).dealDeepSeaWxMessage(eq(processOrchestrationDTO), anyString(), any(), eq(stepExecuteResultDTO));
    }

    /**
     * Test case: Group message type handling with old logic
     */
    @Test
    public void testExecuteAfterNodeDealLogic_GroupMessageTypeWithOldLogic() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = createProcessOrchestrationDTO();
        processOrchestrationDTO.setProcessOrchestrationType((byte) 1);
        List<String> executorIds = Collections.singletonList("executor1");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> invokeDetails = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setProcessOrchestrationNodeId(1L);
        detail.setType("groupMessage");
        invokeDetails.add(detail);
        lionMock.when(() -> Lion.getBoolean(anyString(), eq(MsgPushConstant.PUSH_INTEGRATION_SWITCH_LION_KEY), anyBoolean())).thenReturn(false);
        when(wxInvokeDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class))).thenReturn(invokeDetails);
        // act
        crowdFriendTouchAction.executeAfterNodeDealLogic(processOrchestrationDTO, executorIds, executeManagementDTO, stepExecuteResultDTO);
        // assert
        verify(officialWxHandler).dealOfficialWxGroupMessage(eq(processOrchestrationDTO), anyString(), any(), eq(stepExecuteResultDTO));
    }

    /**
     * 测试executeAfterNodeDealLogic方法，当开关打开且查询结果非空，处理实时任务时
     */
    @Test
    public void testExecuteAfterNodeDealLogicWithSwitchOnAndRealTimeTask() {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setProcessOrchestrationType(ScrmProcessOrchestrationTaskTypeEnum.REAL_TIME_PROCESS_ORCHESTRATION.getValue().byteValue());
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("123");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(1L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);
        List<String> executorIds = Arrays.asList("executor1", "executor2");
        ExecuteManagementDTO executeManagementDTO = new ExecuteManagementDTO();
        StepExecuteResultDTO stepExecuteResultDTO = new StepExecuteResultDTO();
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO.setType(ScrmProcessOrchestrationWxTouchTypeEnum.MESSAGE.getCode());
        invokeDetailDO.setId(2L);

        when(Lion.getBoolean(Environment.getAppName(), "push.integration.switch", false)).thenReturn(true);
        when(wxInvokeDetailDOMapper.selectByExample(any()))
                .thenReturn(Collections.singletonList(invokeDetailDO))
                .thenReturn(new ArrayList<>());

        // act
        crowdFriendTouchAction.executeAfterNodeDealLogic(processOrchestrationDTO, executorIds, executeManagementDTO, stepExecuteResultDTO);

        // assert
        verify(wxInvokeDetailDOMapper, atLeastOnce()).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(deepSeaWxHandler, atLeastOnce()).dealRealTimeDeepSeaWxMessageV2(eq(processOrchestrationDTO), eq(executorIds), any(), any(), any());
    }
}
