package com.sankuai.scrm.core.service.dashboard.domain;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.sankuai.dz.srcm.dashboard.service.dto.TableHeader;
import com.sankuai.scrm.core.service.dashboard.dal.dto.EsUserDataSnapshot;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import java.util.Arrays;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.function.ToDoubleFunction;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class UtilsAuthenticationTest {

    @InjectMocks
    private Utils utils;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    private Map<String, List<String>> whiteList;

    private static final String LION_APP_NAME = "baby-customer-service";

    private static final String LION_CORP_NAME_APP_KEY = "baby-customer-service.crm.business.info.list";

    @Before
    public void setUp() {
        whiteList = new HashMap<>();
        whiteList.put("corp1", Arrays.asList("mis1", "mis2"));
        whiteList.put("corp2", Arrays.asList("mis3"));
    }

    private Map<String, Object> createBusinessInfo() {
        Map<String, Object> businessInfo = new HashMap<>();
        businessInfo.put("appId", "123");
        businessInfo.put("businessName", "test");
        return businessInfo;
    }

    @Test
    public void testAuthenticationMisInWhiteListAndAppIdNotNull() throws Throwable {
        String mis = "mis1";
        String appId = "appId";
        when(corpAppConfigRepository.getCorpIdByAppId(appId)).thenReturn("corp1");
        List<String> result = utils.authentication(mis, appId);
        assertEquals(1, result.size());
        assertEquals("corp1", result.get(0));
    }

    @Test
    public void testAuthenticationMisInWhiteListAndAppIdNull() throws Throwable {
        String mis = "mis1";
        String appId = "appId";
        when(corpAppConfigRepository.getCorpIdByAppId(appId)).thenReturn(null);
        List<String> result = utils.authentication(mis, appId);
        assertEquals(0, result.size());
    }

    @Test
    public void testAuthenticationMisNotInWhiteListAndAppIdNotNull() throws Throwable {
        String mis = "mis4";
        String appId = "appId";
        when(corpAppConfigRepository.getCorpIdByAppId(appId)).thenReturn("corp3");
        List<String> result = utils.authentication(mis, appId);
        assertEquals(1, result.size());
        assertEquals("corp3", result.get(0));
    }

    @Test
    public void testAuthenticationMisNotInWhiteListAndAppIdNull() throws Throwable {
        String mis = "mis4";
        String appId = "appId";
        when(corpAppConfigRepository.getCorpIdByAppId(appId)).thenReturn(null);
        List<String> result = utils.authentication(mis, appId);
        assertEquals(0, result.size());
    }

    @Test
    public void testAverageListIsNull() throws Throwable {
        Double result = Utils.average(null, (EsUserDataSnapshot esUserDataSnapshot) -> 1.0);
        assertEquals(0.0, result, 0.0);
    }

    @Test
    public void testAverageMapperIsNull() throws Throwable {
        EsUserDataSnapshot esUserDataSnapshot = new EsUserDataSnapshot();
        Double result = Utils.average(Arrays.asList(esUserDataSnapshot), null);
        assertEquals(0.0, result, 0.0);
    }

    @Test
    public void testAverageList() throws Throwable {
        Double result = Utils.average(Arrays.asList(), (EsUserDataSnapshot esUserDataSnapshot) -> 1.0);
        assertEquals(0.0, result, 0.0);
    }

    @Test
    public void testAverageElementIsNull() throws Throwable {
        Double result = Utils.average(Arrays.asList((EsUserDataSnapshot) null), (EsUserDataSnapshot esUserDataSnapshot) -> 1.0);
        assertEquals(0.0, result, 0.0);
    }

    @Test
    public void testAverageElementValueLessThanEpsilon() throws Throwable {
        EsUserDataSnapshot esUserDataSnapshot = new EsUserDataSnapshot();
        // Set a value less than epsilon
        esUserDataSnapshot.setWeeklyUserRetentionRate(0.000001);
        Double result = Utils.average(Arrays.asList(esUserDataSnapshot), EsUserDataSnapshot::getWeeklyUserRetentionRate);
        // Adjust the assertion to expect the result to be epsilon
        assertEquals(1.0E-6, result, 0.0);
    }

    @Test
    public void testAverageElementValueGreaterThanEpsilon() throws Throwable {
        EsUserDataSnapshot esUserDataSnapshot = new EsUserDataSnapshot();
        // Set a value greater than epsilon
        esUserDataSnapshot.setWeeklyUserRetentionRate(0.00001);
        Double result = Utils.average(Arrays.asList(esUserDataSnapshot), EsUserDataSnapshot::getWeeklyUserRetentionRate);
        // Adjust the assertion to expect the result to be the actual value
        assertEquals(0.00001, result, 0.0);
    }

    // Test when corpId is null
    @Test
    public void testGetCorpNameCorpIdIsNull() throws Throwable {
        String result = utils.getCorpName(null);
        assertEquals("", result);
    }

    // Test when getAppIdByCorpId returns null
    @Test
    public void testGetCorpNameAppIdByCorpIdIsNull() throws Throwable {
        when(corpAppConfigRepository.getAppIdByCorpId("123")).thenReturn(null);
        String result = utils.getCorpName("123");
        assertEquals("", result);
    }

    // Test when business info list is empty
    @Test
    public void testGetCorpNameBusinessInfoIsEmpty() throws Throwable {
        try (MockedStatic<Lion> mocked = mockStatic(Lion.class)) {
            mocked.when(() -> Lion.getList(LION_APP_NAME, LION_CORP_NAME_APP_KEY, Map.class)).thenReturn(Arrays.asList());
            when(corpAppConfigRepository.getAppIdByCorpId("123")).thenReturn("123");
            String result = utils.getCorpName("123");
            assertEquals("", result);
        }
    }

    // Test when there is no matched appId
    @Test
    public void testGetCorpNameNoMatchedAppId() throws Throwable {
        Map<String, Object> businessInfo = createBusinessInfo();
        try (MockedStatic<Lion> mocked = mockStatic(Lion.class)) {
            mocked.when(() -> Lion.getList(LION_APP_NAME, LION_CORP_NAME_APP_KEY, Map.class)).thenReturn(Arrays.asList(businessInfo));
            when(corpAppConfigRepository.getAppIdByCorpId("123")).thenReturn("456");
            String result = utils.getCorpName("123");
            assertEquals("", result);
        }
    }

    // Test when there is a matched appId
    @Test
    public void testGetCorpNameMatchedAppId() throws Throwable {
        Map<String, Object> businessInfo = createBusinessInfo();
        try (MockedStatic<Lion> mocked = mockStatic(Lion.class)) {
            mocked.when(() -> Lion.getList(LION_APP_NAME, LION_CORP_NAME_APP_KEY, Map.class)).thenReturn(Arrays.asList(businessInfo));
            when(corpAppConfigRepository.getAppIdByCorpId("123")).thenReturn("123");
            String result = utils.getCorpName("123");
            assertEquals("test", result);
        }
    }
}
