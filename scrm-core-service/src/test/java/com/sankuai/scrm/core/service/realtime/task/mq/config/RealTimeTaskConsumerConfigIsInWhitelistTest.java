package com.sankuai.scrm.core.service.realtime.task.mq.config;

import com.sankuai.scrm.core.service.realtime.task.dto.RealTimeTaskConsumerConfigDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
public class RealTimeTaskConsumerConfigIsInWhitelistTest {

    private RealTimeTaskConsumerConfig config;

    private RealTimeTaskConsumerConfigDTO configDTO;

    @BeforeEach
    void setUp() {
        config = new RealTimeTaskConsumerConfig();
        configDTO = Mockito.mock(RealTimeTaskConsumerConfigDTO.class);
        // 使用反射设置configDTO字段
        try {
            Field field = RealTimeTaskConsumerConfig.class.getDeclaredField("configDTO");
            field.setAccessible(true);
            field.set(config, configDTO);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
    }

    /**
     * 测试用户在白名单中的情况
     */
    @Test
    void testIsInWhitelistWhenUserInWhitelist() throws Throwable {
        // arrange
        Set<Long> whiteList = new HashSet<>();
        whiteList.add(123L);
        when(configDTO.getWhiteListSet()).thenReturn(whiteList);
        // act
        boolean result = config.isInWhitelist(123L);
        // assert
        assertTrue(result);
    }

    /**
     * 测试传入null用户ID的情况
     * 根据实际实现，可能返回false而不是抛出异常
     */
    @Test
    void testIsInWhitelistWhenUserIdIsNull() throws Throwable {
        // arrange
        // act
        boolean result = config.isInWhitelist(new ArrayList<>());
        // assert
        assertFalse(result, "Expected false when userId is null");
    }

    /**
     * 测试白名单集合为null的情况
     */
    @Test
    void testIsInWhitelistWhenWhitelistIsNull() throws Throwable {
        // arrange
        when(configDTO.getWhiteListSet()).thenReturn(null);
        // act & assert
        assertThrows(NullPointerException.class, () -> config.isInWhitelist(123L));
    }

    /**
     * 测试configDTO为null的情况
     */
    @Test
    void testIsInWhitelistWhenConfigDTOIsNull() throws Throwable {
        // arrange
        try {
            Field field = RealTimeTaskConsumerConfig.class.getDeclaredField("configDTO");
            field.setAccessible(true);
            field.set(config, null);
        } catch (Exception e) {
            throw new RuntimeException(e);
        }
        // act & assert
        assertThrows(NullPointerException.class, () -> config.isInWhitelist(123L));
    }

    @Test
    void isInWhitelist_shouldReturnFalse_whenUserIdListIsNull() {
        // Arrange
        configDTO.getWhiteListSet().add(1L); // 白名单内容对此测试例的null输入不产生影响

        // Act
        boolean result = config.isInWhitelist((List<Long>) null);

        // Assert
        assertThat(result).isFalse();
        // 或者使用 JUnit 5 原生断言: assertFalse(result);
    }

    @Test
    void isInWhitelist_shouldReturnFalse_whenUserIdListIsEmpty() {
        // Arrange
        configDTO.getWhiteListSet().add(1L); // 白名单内容对此测试例的空列表输入不产生影响

        // Act
        boolean result = config.isInWhitelist(Collections.emptyList());

        // Assert
        assertThat(result).isFalse();
    }
}
