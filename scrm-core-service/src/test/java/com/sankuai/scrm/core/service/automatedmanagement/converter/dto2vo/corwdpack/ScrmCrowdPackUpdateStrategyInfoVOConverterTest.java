package com.sankuai.scrm.core.service.automatedmanagement.converter.dto2vo.corwdpack;

import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmCrowdPackUpdateStrategyDetailVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmCrowdPackUpdateStrategyInfoVO;
import com.sankuai.dz.srcm.automatedmanagement.vo.ScrmProcessOrchestrationExecutorVO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationExecutorDTO;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.ArrayList;
import java.util.List;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ScrmCrowdPackUpdateStrategyInfoVOConverterTest {

    @InjectMocks
    private ScrmCrowdPackUpdateStrategyInfoVOConverter scrmCrowdPackUpdateStrategyInfoVOConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationExecutorVOConverter scrmProcessOrchestrationExecutorVOConverter;

    private ScrmCrowdPackUpdateStrategyInfoVO createResource() {
        ScrmCrowdPackUpdateStrategyInfoVO resource = new ScrmCrowdPackUpdateStrategyInfoVO();
        List<ScrmCrowdPackUpdateStrategyDetailVO> crowdPackUpdateStrategyVOs = new ArrayList<>();
        ScrmCrowdPackUpdateStrategyDetailVO strategyDetailVO = new ScrmCrowdPackUpdateStrategyDetailVO();
        strategyDetailVO.setFilterFieldId(1L);
        strategyDetailVO.setOperatorId(2L);
        strategyDetailVO.setParam(new ArrayList<>());
        strategyDetailVO.setGroupId(3);
        strategyDetailVO.setStrategyVersion("1.0");
        crowdPackUpdateStrategyVOs.add(strategyDetailVO);
        resource.setCrowdPackUpdateStrategy(crowdPackUpdateStrategyVOs);
        List<ScrmProcessOrchestrationExecutorVO> crowdPackUpdateStrategyStaffVOs = new ArrayList<>();
        ScrmProcessOrchestrationExecutorVO executorVO = new ScrmProcessOrchestrationExecutorVO();
        executorVO.setExecutorId("executorId");
        executorVO.setExecutorName("executorName");
        executorVO.setExecutorType(1);
        crowdPackUpdateStrategyStaffVOs.add(executorVO);
        resource.setCrowdPackUpdateStrategyStaff(crowdPackUpdateStrategyStaffVOs);
        return resource;
    }

    @Test
    public void testConvertToDTOWhenResourceIsNull() throws Throwable {
        ScrmCrowdPackUpdateStrategyInfoVO resource = null;
        ScrmCrowdPackUpdateStrategyInfoDTO result = scrmCrowdPackUpdateStrategyInfoVOConverter.convertToDTO(resource);
        assertNull(result);
    }

    @Test
    public void testConvertToDTOWhenCrowdPackUpdateStrategyIsEmpty() throws Throwable {
        ScrmCrowdPackUpdateStrategyInfoVO resource = createResource();
        resource.getCrowdPackUpdateStrategy().clear();
        ScrmCrowdPackUpdateStrategyInfoDTO result = scrmCrowdPackUpdateStrategyInfoVOConverter.convertToDTO(resource);
        assertNotNull(result);
        assertTrue(result.getCrowdPackUpdateStrategy().isEmpty());
    }

    @Test
    public void testConvertToDTOWhenCrowdPackUpdateStrategyStaffIsEmpty() throws Throwable {
        ScrmCrowdPackUpdateStrategyInfoVO resource = createResource();
        resource.getCrowdPackUpdateStrategyStaff().clear();
        ScrmCrowdPackUpdateStrategyInfoDTO result = scrmCrowdPackUpdateStrategyInfoVOConverter.convertToDTO(resource);
        assertNotNull(result);
        assertTrue(result.getCrowdPackUpdateStrategyStaff().isEmpty());
    }

    @Test
    public void testConvertToDTOWhenCrowdPackUpdateStrategyAndCrowdPackUpdateStrategyStaffAreNotEmpty() throws Throwable {
        ScrmCrowdPackUpdateStrategyInfoVO resource = createResource();
        // Ensure the mock setup is correctly applied
        List<ScrmProcessOrchestrationExecutorDTO> mockStaffList = new ArrayList<>();
        ScrmProcessOrchestrationExecutorDTO executorDTO = new ScrmProcessOrchestrationExecutorDTO();
        executorDTO.setExecutorId("executorId");
        executorDTO.setExecutorName("executorName");
        executorDTO.setExecutorType(1);
        mockStaffList.add(executorDTO);
        when(scrmProcessOrchestrationExecutorVOConverter.convertToDTOsSafety(anyList())).thenReturn(mockStaffList);
        ScrmCrowdPackUpdateStrategyInfoDTO result = scrmCrowdPackUpdateStrategyInfoVOConverter.convertToDTO(resource);
        assertNotNull(result);
        assertFalse(result.getCrowdPackUpdateStrategy().isEmpty());
        assertFalse(result.getCrowdPackUpdateStrategyStaff().isEmpty());
        ScrmCrowdPackUpdateStrategyDetailDTO strategyDetailDTO = result.getCrowdPackUpdateStrategy().get(0);
        assertEquals(1L, strategyDetailDTO.getFilterFieldId().longValue());
        assertEquals(2L, strategyDetailDTO.getOperatorId().longValue());
        assertTrue(strategyDetailDTO.getParam().isEmpty());
        assertEquals(3, strategyDetailDTO.getGroupId().intValue());
        assertEquals("1.0", strategyDetailDTO.getStrategyVersion());
        ScrmProcessOrchestrationExecutorDTO executorDTOResult = result.getCrowdPackUpdateStrategyStaff().get(0);
        assertEquals("executorId", executorDTOResult.getExecutorId());
        assertEquals("executorName", executorDTOResult.getExecutorName());
        assertEquals(1, executorDTOResult.getExecutorType().intValue());
    }
}
