package com.sankuai.scrm.core.service.realtime.task.mq.producer;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.producer.AsyncDelayProducerResult;
import com.meituan.mafka.client.producer.IDelayFutureCallback;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ScrmIntelligentFollowResultDelayMsgProducerTest {

    private ScrmIntelligentFollowResultDelayMsgProducer producer;

    private IProducerProcessor mockProducer;

    private IProducerProcessor getProducer() throws Exception {
        Field field = ScrmIntelligentFollowResultDelayMsgProducer.class.getDeclaredField("producer");
        field.setAccessible(true);
        return (IProducerProcessor) field.get(null);
    }

    private void setProducer(IProducerProcessor producer) throws Exception {
        Field field = ScrmIntelligentFollowResultDelayMsgProducer.class.getDeclaredField("producer");
        field.setAccessible(true);
        field.set(null, producer);
    }

    @BeforeEach
    void setUp() throws Exception {
        mockProducer = mock(IProducerProcessor.class);
        producer = new ScrmIntelligentFollowResultDelayMsgProducer();
        // 使用反射设置producer字段
        Field producerField = ScrmIntelligentFollowResultDelayMsgProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, mockProducer);
    }

    /**
     * 测试正常初始化场景 - 属性配置正确且MafkaClient成功创建生产者
     */
    @Test
    public void testAfterPropertiesSetNormalCase() throws Throwable {
        // arrange
        ScrmIntelligentFollowResultDelayMsgProducer producer = new ScrmIntelligentFollowResultDelayMsgProducer();
        IProducerProcessor mockProducer = mock(IProducerProcessor.class);
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            mockedMafkaClient.when(() -> MafkaClient.buildDelayProduceFactory(any(Properties.class), anyString())).thenReturn(mockProducer);
            // act
            producer.afterPropertiesSet();
            // assert
            mockedMafkaClient.verify(() -> MafkaClient.buildDelayProduceFactory(any(Properties.class), eq("scrm_intelligent_follow_result_delay_msg")));
            assertNotNull(getProducer());
        }
    }

    /**
     * 测试静态字段重置场景 - 确保测试间不互相影响
     */
    @Test
    public void testAfterPropertiesSetStaticFieldReset() throws Throwable {
        // arrange
        ScrmIntelligentFollowResultDelayMsgProducer producer = new ScrmIntelligentFollowResultDelayMsgProducer();
        IProducerProcessor originalProducer = getProducer();
        IProducerProcessor mockProducer = mock(IProducerProcessor.class);
        try (MockedStatic<MafkaClient> mockedMafkaClient = mockStatic(MafkaClient.class)) {
            mockedMafkaClient.when(() -> MafkaClient.buildDelayProduceFactory(any(Properties.class), anyString())).thenReturn(mockProducer);
            // act
            producer.afterPropertiesSet();
            // assert
            assertNotSame(originalProducer, getProducer());
            // reset static field
            setProducer(originalProducer);
        }
    }

    /**
     * 测试输入参数为null时返回false
     */
    @Test
    void testSendAsyncDelayMessageWhenInputIsNull() throws Throwable {
        // act
        boolean result = producer.sendAsyncDelayMessage(null, 1000L);
        // assert
        assertFalse(result);
        verify(mockProducer, never()).sendAsyncDelayMessage(any(), anyLong(), any());
    }

    /**
     * 测试发送消息成功时返回true并触发onSuccess回调
     */
    @Test
    void testSendAsyncDelayMessageWhenSendSuccess() throws Throwable {
        // arrange
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        AsyncDelayProducerResult mockResult = new AsyncDelayProducerResult(ProducerStatus.SEND_OK);
        when(mockProducer.sendAsyncDelayMessage(anyString(), anyLong(), any())).thenReturn(mockResult);
        // act
        boolean result = producer.sendAsyncDelayMessage(dto, 1000L);
        // assert
        assertTrue(result);
        // verify(mockProducer).sendAsyncDelayMessage(eq(JsonUtils.toStr(dto)), eq(1000L), any());
    }

    /**
     * 测试发送消息失败时返回true但会记录错误日志
     */
    @Test
    void testSendAsyncDelayMessageWhenSendFailure() throws Throwable {
        // arrange
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        AsyncDelayProducerResult mockResult = new AsyncDelayProducerResult(ProducerStatus.SEND_FAILURE, "error message");
        when(mockProducer.sendAsyncDelayMessage(anyString(), anyLong(), any())).thenReturn(mockResult);
        // act
        boolean result = producer.sendAsyncDelayMessage(dto, 1000L);
        // assert
        assertTrue(result);
    }

    /**
     * 测试发送过程中抛出异常时返回false
     */
    @Test
    void testSendAsyncDelayMessageWhenExceptionThrown() throws Throwable {
        // arrange
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        when(mockProducer.sendAsyncDelayMessage(anyString(), anyLong(), any())).thenThrow(new RuntimeException("test exception"));
        // act
        boolean result = producer.sendAsyncDelayMessage(dto, 1000L);
        // assert
        assertFalse(result);
    }

    /**
     * 测试回调函数onFailure被正确调用
     */
    @Test
    void testSendAsyncDelayMessageCallbackOnFailure() throws Throwable {
        // arrange
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        AsyncDelayProducerResult mockResult = new AsyncDelayProducerResult(ProducerStatus.SEND_FAILURE, "error message");
        // 捕获回调函数
        ArgumentCaptor<IDelayFutureCallback> callbackCaptor = ArgumentCaptor.forClass(IDelayFutureCallback.class);
        when(mockProducer.sendAsyncDelayMessage(anyString(), anyLong(), callbackCaptor.capture())).thenReturn(mockResult);
        // act
        boolean result = producer.sendAsyncDelayMessage(dto, 1000L);
        // assert
        assertTrue(result);
        // 验证回调函数
        IDelayFutureCallback callback = callbackCaptor.getValue();
        assertNotNull(callback);
        callback.onFailure(mockResult);
    }

    /**
     * 测试回调函数onSuccess被正确调用
     */
    @Test
    void testSendAsyncDelayMessageCallbackOnSuccess() throws Throwable {
        // arrange
        IntelligentFollowResultDTO dto = new IntelligentFollowResultDTO();
        AsyncDelayProducerResult mockResult = new AsyncDelayProducerResult(ProducerStatus.SEND_OK);
        // 捕获回调函数
        ArgumentCaptor<IDelayFutureCallback> callbackCaptor = ArgumentCaptor.forClass(IDelayFutureCallback.class);
        when(mockProducer.sendAsyncDelayMessage(anyString(), anyLong(), callbackCaptor.capture())).thenReturn(mockResult);
        // act
        boolean result = producer.sendAsyncDelayMessage(dto, 1000L);
        // assert
        assertTrue(result);
        // 验证回调函数
        IDelayFutureCallback callback = callbackCaptor.getValue();
        assertNotNull(callback);
        callback.onSuccess(mockResult);
    }
}
