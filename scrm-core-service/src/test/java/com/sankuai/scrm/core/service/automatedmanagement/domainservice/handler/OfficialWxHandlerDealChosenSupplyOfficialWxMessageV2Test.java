package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.function.InvokeDetailColumnGetter;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.PrivateSendStrategy;
import com.sankuai.scrm.core.service.message.push.request.MsgPushRequest;
import com.sankuai.scrm.core.service.message.push.response.MsgPushResponse;
import com.sankuai.scrm.core.service.message.push.service.MsgUnifiedPushService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.MockitoJUnitRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class OfficialWxHandlerDealChosenSupplyOfficialWxMessageV2Test {

    @Spy
    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private MsgUnifiedPushService msgUnifiedPushService;

    @Mock
    private PrivateSendStrategy privateSendStrategy;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private List<String> executorId;

    private InvokeDetailKeyObject keyObject;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS;

    @Mock
    private ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO;

    @Before
    public void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        processOrchestrationDTO.setAppId("testAppId");
        executorId = Lists.newArrayList("executorId");
        keyObject = new InvokeDetailKeyObject("key", (byte) 1, (byte) 1, 1L);
        totalInvokeDetailDOS = new ArrayList<>();
        ReflectionTestUtils.setField(officialWxHandler, "privateSendStrategy", privateSendStrategy);
        when(privateSendStrategy.getColumnGetter()).thenReturn(new InvokeDetailColumnGetter() {

            @Override
            public String get(ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO) {
                return detailDO.getTargetId();
            }
        });
        // Mock stopAndLogRequest to allow message sending
        doReturn(false).when(officialWxHandler).stopAndLogRequest(anyString(), anyString(), anyString());
    }

    /**
     * Test case for empty invocation details.
     */
    @Test
    public void testDealChosenSupplyOfficialWxMessageV2EmptyDetails() throws Throwable {
        // arrange
        totalInvokeDetailDOS = Collections.emptyList();
        // act
        officialWxHandler.dealChosenSupplyOfficialWxMessageV2(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        // assert
        verify(msgUnifiedPushService, never()).saveMsgPushTask(any());
    }

    /**
     * Test case for no action content.
     */
    @Test
    public void testDealChosenSupplyOfficialWxMessageV2NoActionContent() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setTargetId("targetId");
        detail.setProcessOrchestrationNodeId(1L);
        totalInvokeDetailDOS.add(detail);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        when(nodeMediumDTO.getActionDTO(anyLong())).thenReturn(actionDTO);
        when(nodeMediumDTO.getActionContentDTOList(any())).thenReturn(Collections.emptyList());
        // act
        officialWxHandler.dealChosenSupplyOfficialWxMessageV2(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        // assert
        verify(msgUnifiedPushService, never()).saveMsgPushTask(any());
    }

    /**
     * Test case for no attachments.
     */
    @Test
    public void testDealChosenSupplyOfficialWxMessageV2NoAttachments() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setTargetId("targetId");
        detail.setProcessOrchestrationNodeId(1L);
        totalInvokeDetailDOS.add(detail);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        when(nodeMediumDTO.getActionDTO(anyLong())).thenReturn(actionDTO);
        // act
        officialWxHandler.dealChosenSupplyOfficialWxMessageV2(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        // assert
        verify(msgUnifiedPushService, never()).saveMsgPushTask(any());
    }

    /**
     * Test case for successful message push.
     */
    @Test
    public void testDealChosenSupplyOfficialWxMessageV2SuccessfulPush() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setTargetId("targetId");
        detail.setProcessOrchestrationNodeId(1L);
        totalInvokeDetailDOS.add(detail);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        when(nodeMediumDTO.getActionDTO(anyLong())).thenReturn(actionDTO);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("Test Content");
        contentDTOS.add(contentDTO);
        when(nodeMediumDTO.getActionContentDTOList(any())).thenReturn(contentDTOS);
        List<ScrmProcessOrchestrationActionAttachmentDTO> attachments = new ArrayList<>();
        ScrmProcessOrchestrationActionAttachmentDTO attachment = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachments.add(attachment);
        when(nodeMediumDTO.getActionAttachmentDTOList(any())).thenReturn(attachments);
        MsgPushResponse<Long> response = new MsgPushResponse<>();
        response.setCode(0);
        response.setData(1L);
        when(msgUnifiedPushService.saveMsgPushTask(any(MsgPushRequest.class))).thenReturn(response);
        // act
        officialWxHandler.dealChosenSupplyOfficialWxMessageV2(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
        // assert
        verify(msgUnifiedPushService, times(1)).saveMsgPushTask(any(MsgPushRequest.class));
    }

    /**
     * Test case for failed message push.
     */
    @Test
    public void testDealChosenSupplyOfficialWxMessageV2FailedPush() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setTargetId("targetId");
        detail.setProcessOrchestrationNodeId(1L);
        totalInvokeDetailDOS.add(detail);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        when(nodeMediumDTO.getActionDTO(anyLong())).thenReturn(actionDTO);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("Test Content");
        contentDTOS.add(contentDTO);
        when(nodeMediumDTO.getActionContentDTOList(any())).thenReturn(contentDTOS);
        List<ScrmProcessOrchestrationActionAttachmentDTO> attachments = new ArrayList<>();
        ScrmProcessOrchestrationActionAttachmentDTO attachment = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachments.add(attachment);
        when(nodeMediumDTO.getActionAttachmentDTOList(any())).thenReturn(attachments);
        MsgPushResponse<Long> response = new MsgPushResponse<>();
        response.setCode(1);
        when(msgUnifiedPushService.saveMsgPushTask(any(MsgPushRequest.class))).thenReturn(response);
        // act
        officialWxHandler.dealChosenSupplyOfficialWxMessageV2(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS,null );
        // assert
        verify(msgUnifiedPushService, times(1)).saveMsgPushTask(any(MsgPushRequest.class));
    }

    /**
     * Test case for exception handling.
     */
    @Test(expected = RuntimeException.class)
    public void testDealChosenSupplyOfficialWxMessageV2ExceptionHandling() throws Throwable {
        // arrange
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setTargetId("targetId");
        detail.setProcessOrchestrationNodeId(1L);
        totalInvokeDetailDOS.add(detail);
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        when(nodeMediumDTO.getActionDTO(anyLong())).thenReturn(actionDTO);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("Test Content");
        contentDTOS.add(contentDTO);
        when(nodeMediumDTO.getActionContentDTOList(any())).thenReturn(contentDTOS);
        List<ScrmProcessOrchestrationActionAttachmentDTO> attachments = new ArrayList<>();
        ScrmProcessOrchestrationActionAttachmentDTO attachment = new ScrmProcessOrchestrationActionAttachmentDTO();
        attachments.add(attachment);
        when(nodeMediumDTO.getActionAttachmentDTOList(any())).thenReturn(attachments);
        when(msgUnifiedPushService.saveMsgPushTask(any(MsgPushRequest.class))).thenThrow(new RuntimeException("Test Exception"));
        // act
        officialWxHandler.dealChosenSupplyOfficialWxMessageV2(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS,null );
    }

    /**
     * Test case for null process orchestration DTO.
     */
    @Test(expected = NullPointerException.class)
    public void testDealChosenSupplyOfficialWxMessageV2NullProcessOrchestrationDTO() throws Throwable {
        // arrange
        processOrchestrationDTO = null;
        ScrmAmProcessOrchestrationWxInvokeDetailDO detail = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detail.setTargetId("targetId");
        detail.setProcessOrchestrationNodeId(1L);
        totalInvokeDetailDOS.add(detail);
        // act
        officialWxHandler.dealChosenSupplyOfficialWxMessageV2(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, null);
    }
}
