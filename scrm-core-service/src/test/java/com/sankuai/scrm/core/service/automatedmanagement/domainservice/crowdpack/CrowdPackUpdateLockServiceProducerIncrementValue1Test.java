package com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CrowdPackUpdateLockServiceProducerIncrementValue1Test {

    @Mock
    private RedisStoreClient redisClient;

    @InjectMocks
    private CrowdPackUpdateLockService crowdPackUpdateLockService;

    // 定义与原始类相同的常量
    private static final String SCRM_CROWD_PACK_UPDATE_LOCK_CATEGORY = "crowd_pack_update_category";

    private static final String PRODUCER_NUM_LOCK = "producer";

    private static final Long TEST_PACK_ID = 12345L;

    private static final StoreKey EXPECTED_KEY = new StoreKey(SCRM_CROWD_PACK_UPDATE_LOCK_CATEGORY, TEST_PACK_ID, PRODUCER_NUM_LOCK);

    /**
     * 测试正常增加正整数的情况
     */
    @Test
    public void testProducerIncrementValuePositiveIncrement() throws Throwable {
        // arrange
        int increment = 5;
        long expectedResult = 10L;
        when(redisClient.incrBy(EXPECTED_KEY, increment)).thenReturn(expectedResult);
        when(redisClient.expire(EXPECTED_KEY, 60 * 60 * 2)).thenReturn(true);
        // act
        long result = crowdPackUpdateLockService.producerIncrementValue(TEST_PACK_ID, increment);
        // assert
        assertEquals(expectedResult, result);
        verify(redisClient).incrBy(EXPECTED_KEY, increment);
        verify(redisClient).expire(EXPECTED_KEY, 60 * 60 * 2);
    }

    /**
     * 测试增加0值的情况
     */
    @Test
    public void testProducerIncrementValueZeroIncrement() throws Throwable {
        // arrange
        int increment = 0;
        long expectedResult = 5L;
        when(redisClient.incrBy(EXPECTED_KEY, increment)).thenReturn(expectedResult);
        when(redisClient.expire(EXPECTED_KEY, 60 * 60 * 2)).thenReturn(true);
        // act
        long result = crowdPackUpdateLockService.producerIncrementValue(TEST_PACK_ID, increment);
        // assert
        assertEquals(expectedResult, result);
        verify(redisClient).incrBy(EXPECTED_KEY, increment);
        verify(redisClient).expire(EXPECTED_KEY, 60 * 60 * 2);
    }

    /**
     * 测试增加负值(减少)的情况
     */
    @Test
    public void testProducerIncrementValueNegativeIncrement() throws Throwable {
        // arrange
        int increment = -3;
        long expectedResult = 2L;
        when(redisClient.incrBy(EXPECTED_KEY, increment)).thenReturn(expectedResult);
        when(redisClient.expire(EXPECTED_KEY, 60 * 60 * 2)).thenReturn(true);
        // act
        long result = crowdPackUpdateLockService.producerIncrementValue(TEST_PACK_ID, increment);
        // assert
        assertEquals(expectedResult, result);
        verify(redisClient).incrBy(EXPECTED_KEY, increment);
        verify(redisClient).expire(EXPECTED_KEY, 60 * 60 * 2);
    }

    /**
     * 测试packId为null的情况
     */
    @Test
    public void testProducerIncrementValueNullPackId() throws Throwable {
        // arrange
        Long packId = null;
        int increment = 5;
        // act
        assertDoesNotThrow(() -> {
            crowdPackUpdateLockService.producerIncrementValue(packId, increment);
        });
        // assert
        // Since the method doesn't throw NPE, we just verify it completes normally
    }

    /**
     * 测试Redis incrBy操作异常的情况
     */
    @Test
    public void testProducerIncrementValueRedisIncrByException() throws Throwable {
        // arrange
        int increment = 5;
        when(redisClient.incrBy(EXPECTED_KEY, increment)).thenThrow(new RuntimeException("Redis error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            crowdPackUpdateLockService.producerIncrementValue(TEST_PACK_ID, increment);
        });
        verify(redisClient).incrBy(EXPECTED_KEY, increment);
        verify(redisClient, never()).expire(any(), anyInt());
    }

    /**
     * 测试Redis expire操作异常的情况
     */
    @Test
    public void testProducerIncrementValueRedisExpireException() throws Throwable {
        // arrange
        int increment = 5;
        long expectedResult = 10L;
        when(redisClient.incrBy(EXPECTED_KEY, increment)).thenReturn(expectedResult);
        when(redisClient.expire(EXPECTED_KEY, 60 * 60 * 2)).thenThrow(new RuntimeException("Expire error"));
        // act & assert
        assertThrows(RuntimeException.class, () -> {
            crowdPackUpdateLockService.producerIncrementValue(TEST_PACK_ID, increment);
        });
        verify(redisClient).incrBy(EXPECTED_KEY, increment);
        verify(redisClient).expire(EXPECTED_KEY, 60 * 60 * 2);
    }

    /**
     * 测试increment为Integer.MAX_VALUE的情况
     */
    @Test
    public void testProducerIncrementValueMaxIncrement() throws Throwable {
        // arrange
        int increment = Integer.MAX_VALUE;
        long expectedResult = Long.MAX_VALUE;
        when(redisClient.incrBy(EXPECTED_KEY, increment)).thenReturn(expectedResult);
        when(redisClient.expire(EXPECTED_KEY, 60 * 60 * 2)).thenReturn(true);
        // act
        long result = crowdPackUpdateLockService.producerIncrementValue(TEST_PACK_ID, increment);
        // assert
        assertEquals(expectedResult, result);
        verify(redisClient).incrBy(EXPECTED_KEY, increment);
        verify(redisClient).expire(EXPECTED_KEY, 60 * 60 * 2);
    }

    /**
     * 测试increment为Integer.MIN_VALUE的情况
     */
    @Test
    public void testProducerIncrementValueMinIncrement() throws Throwable {
        // arrange
        int increment = Integer.MIN_VALUE;
        long expectedResult = Long.MIN_VALUE;
        when(redisClient.incrBy(EXPECTED_KEY, increment)).thenReturn(expectedResult);
        when(redisClient.expire(EXPECTED_KEY, 60 * 60 * 2)).thenReturn(true);
        // act
        long result = crowdPackUpdateLockService.producerIncrementValue(TEST_PACK_ID, increment);
        // assert
        assertEquals(expectedResult, result);
        verify(redisClient).incrBy(EXPECTED_KEY, increment);
        verify(redisClient).expire(EXPECTED_KEY, 60 * 60 * 2);
    }
}
