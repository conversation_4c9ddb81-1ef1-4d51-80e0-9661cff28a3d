package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.fasterxml.jackson.core.type.TypeReference;
import com.meituan.mdp.boot.starter.mdpcache.core.Cache;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.utils.ConditionUtils;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.group.domain.GroupMemberDomainService;
import com.sankuai.scrm.core.service.infrastructure.dal.babymapper.ContactUserDoMapper;
import com.sankuai.scrm.core.service.infrastructure.dal.entity.ContactUserDo;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserTag;
import com.sankuai.scrm.core.service.user.dal.example.ScrmUserTagExample;
import com.sankuai.scrm.core.service.user.dal.mapper.ext.ExtScrmUserTagMapper;
import com.sankuai.scrm.core.service.util.ThreadPoolUtils;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.concurrent.CompletableFuture;
import java.util.concurrent.ExecutionException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

/**
 * Test cases for ExecuteWriteDomainService.updateCrowdPackSubTask method
 */
public class ExecuteWriteDomainServiceUpdateCrowdPackSubTaskTest {

    @Mock
    private Cache localCache;

    @Mock
    private ExecuteManagementService executeManagementService;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private ExtScrmUserTagMapper userTagDOMapper;

    @Mock
    private GroupMemberDomainService groupMemberDomainService;

    @Mock
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock
    private ContactUserDoMapper contactUserDoMapper;

    @Mock
    private ConditionUtils conditionUtils;

    @InjectMocks
    private ExecuteWriteDomainService service;

    @BeforeEach
    public void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test updateCrowdPackSubTask with empty userUnionIds list
     */
    @Test
    public void testUpdateCrowdPackSubTaskWithEmptyUserUnionIds() throws Throwable {
        // arrange
        List<String> userUnionIds = new ArrayList<>();
        // act
        boolean result = service.updateCrowdPackSubTask(userUnionIds);
        // assert
        assertTrue(result, "The result should be true when userUnionIds is empty.");
        verify(executeManagementService).subTaskRunBegin(any(), any());
        verify(executeManagementService).taskRunFinished(any(), any());
    }





    /**
     * Test updateCrowdPackSubTask with exception during execution
     */
    @Test
    public void testUpdateCrowdPackSubTaskWithException() throws Throwable {
        // arrange
        List<String> userUnionIds = new ArrayList<>();
        userUnionIds.add("testUnionId1");
        // Mock user tags to avoid NPE
        List<ScrmUserTag> userTags = new ArrayList<>();
        ScrmUserTag tag = new ScrmUserTag();
        tag.setUserId(1L);
        tag.setAppId("testAppId");
        userTags.add(tag);
        when(userTagDOMapper.selectByExample(any())).thenReturn(userTags);
        when(localCache.get(anyString(), any(TypeReference.class))).thenThrow(new RuntimeException("Test exception"));
        when(crowdPackReadDomainService.getValidScrmAmCrowdPackBaseInfoDOS()).thenThrow(new RuntimeException("Test exception"));
        // act & assert
        RuntimeException thrown = assertThrows(RuntimeException.class, () -> service.updateCrowdPackSubTask(userUnionIds), "Should throw RuntimeException when there is an exception during execution.");
        assertEquals("Test exception", thrown.getMessage());
        verify(executeManagementService).subTaskRunBegin(any(), any());
        verify(executeManagementService).taskRunFinished(any(), any());
    }
}
