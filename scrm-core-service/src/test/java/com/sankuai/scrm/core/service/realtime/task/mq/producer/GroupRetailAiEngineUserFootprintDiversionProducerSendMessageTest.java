package com.sankuai.scrm.core.service.realtime.task.mq.producer;

import static org.junit.jupiter.api.Assertions.assertDoesNotThrow;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineMqMessageDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Field;
import java.lang.reflect.Modifier;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.slf4j.Logger;

@ExtendWith(MockitoExtension.class)
public class GroupRetailAiEngineUserFootprintDiversionProducerSendMessageTest {

    @Mock
    private IProducerProcessor mockProducer;

    private GroupRetailAiEngineUserFootprintDiversionProducer producerUnderTest;

    private IProducerProcessor originalProducer;

    private MockedStatic<JsonUtils> mockedJsonUtils;

    @BeforeEach
    public void setUp() throws Exception {
        producerUnderTest = new GroupRetailAiEngineUserFootprintDiversionProducer();
        // 获取并修改producer字段
        Field producerField = GroupRetailAiEngineUserFootprintDiversionProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        // 移除final修饰符
        Field modifiersField = Field.class.getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.setInt(producerField, producerField.getModifiers() & ~Modifier.FINAL);
        // 保存并替换原始producer
        originalProducer = (IProducerProcessor) producerField.get(null);
        producerField.set(null, mockProducer);
        // 初始化JsonUtils的mock
        mockedJsonUtils = Mockito.mockStatic(JsonUtils.class);
    }

    @AfterEach
    public void tearDown() throws Exception {
        // 恢复原始producer
        Field producerField = GroupRetailAiEngineUserFootprintDiversionProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        // 再次移除final修饰符
        Field modifiersField = Field.class.getDeclaredField("modifiers");
        modifiersField.setAccessible(true);
        modifiersField.setInt(producerField, producerField.getModifiers() & ~Modifier.FINAL);
        producerField.set(null, originalProducer);
        // 关闭JsonUtils的mock
        mockedJsonUtils.close();
    }

    /**
     * 测试输入为null时方法直接返回，不进行任何处理
     */
    @Test
    public void testSendMessageWithNullInput() throws Throwable {
        // act & assert
        assertDoesNotThrow(() -> producerUnderTest.sendMessage(null));
        // verify
        verifyNoInteractions(mockProducer);
        mockedJsonUtils.verifyNoInteractions();
    }

    /**
     * 测试消息第一次发送就成功的情况
     */
    @Test
    public void testSendMessageSuccessAtFirstTime() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO message = new GroupRetailAiEngineMqMessageDTO();
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(mockProducer.sendMessage(anyString())).thenReturn(successResult);
        mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("test message");
        // act
        producerUnderTest.sendMessage(message);
        // assert
        verify(mockProducer, times(1)).sendMessage(anyString());
        mockedJsonUtils.verify(() -> JsonUtils.toStr(eq(message)));
        verifyNoMoreInteractions(mockProducer);
    }

    /**
     * 测试消息发送失败后重试成功的情况
     */
    @Test
    public void testSendMessageSuccessAfterRetry() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO message = new GroupRetailAiEngineMqMessageDTO();
        ProducerResult failResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        ProducerResult successResult = new ProducerResult(ProducerStatus.SEND_OK);
        when(mockProducer.sendMessage(anyString())).thenReturn(failResult).thenReturn(successResult);
        mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("test message");
        // act
        producerUnderTest.sendMessage(message);
        // assert
        verify(mockProducer, times(2)).sendMessage(anyString());
        mockedJsonUtils.verify(() -> JsonUtils.toStr(eq(message)), times(2));
    }

    /**
     * 测试重试多次后仍然失败的情况
     */
    @Test
    public void testSendMessageFailAfterAllRetries() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO message = new GroupRetailAiEngineMqMessageDTO();
        ProducerResult failResult = new ProducerResult(ProducerStatus.SEND_FAILURE);
        when(mockProducer.sendMessage(anyString())).thenReturn(failResult);
        mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("test message");
        // act
        producerUnderTest.sendMessage(message);
        // assert
        verify(mockProducer, times(3)).sendMessage(anyString());
        mockedJsonUtils.verify(() -> JsonUtils.toStr(eq(message)), times(3));
    }

    /**
     * 测试发送过程中抛出异常的情况
     */
    @Test
    public void testSendMessageWithException() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO message = new GroupRetailAiEngineMqMessageDTO();
        when(mockProducer.sendMessage(anyString())).thenThrow(new RuntimeException("test exception"));
        mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("test message");
        // act
        producerUnderTest.sendMessage(message);
        // assert
        verify(mockProducer, times(3)).sendMessage(anyString());
        mockedJsonUtils.verify(() -> JsonUtils.toStr(eq(message)), times(3));
    }

    /**
     * 测试序列化失败的情况
     */
    @Test
    public void testSendMessageWithSerializationError() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO message = new GroupRetailAiEngineMqMessageDTO();
        mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenThrow(new RuntimeException("serialization error"));
        // act
        producerUnderTest.sendMessage(message);
        // assert
        verifyNoInteractions(mockProducer);
        mockedJsonUtils.verify(() -> JsonUtils.toStr(eq(message)), times(3));
    }

    /**
     * 测试日志记录行为（通过验证方法调用次数间接验证）
     */
    @Test
    public void testSendMessageLoggingBehavior() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO message = new GroupRetailAiEngineMqMessageDTO();
        when(mockProducer.sendMessage(anyString())).thenThrow(new RuntimeException("test exception"));
        mockedJsonUtils.when(() -> JsonUtils.toStr(any())).thenReturn("test message");
        // act
        producerUnderTest.sendMessage(message);
        // assert - 通过验证重试次数间接验证日志行为
        verify(mockProducer, times(3)).sendMessage(anyString());
    }
}
