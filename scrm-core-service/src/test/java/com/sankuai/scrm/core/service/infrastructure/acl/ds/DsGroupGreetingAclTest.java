package com.sankuai.scrm.core.service.infrastructure.acl.ds;

import com.sankuai.ds.deepsea.common.model.BaseTResponse;
import com.sankuai.ds.deepsea.common.model.Paged;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import com.sankuai.service.fe.corp.wx.crm.greeting.GroupGreetingTService;
import com.sankuai.service.fe.corp.wx.crm.greeting.dto.GroupGreetingDto;
import org.apache.thrift.TException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import java.util.Arrays;
import java.util.List;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class DsGroupGreetingAclTest {

    @InjectMocks
    private DsGroupGreetingAcl dsGroupGreetingAcl;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private GroupGreetingTService groupGreetingTService;

    private String corpId;

    private String greetingPrefix;

    private CorpAppConfig config;

    private BaseTResponse<Paged<GroupGreetingDto>> response;

    private Paged<GroupGreetingDto> data;

    @Before
    public void setUp() {
        setUpCommonMocks();
    }

    private void setUpCommonMocks() {
        corpId = "testCorpId";
        greetingPrefix = "testGreetingPrefix";
        config = new CorpAppConfig();
        config.setBusinessCode("testBusinessCode");
        response = new BaseTResponse<>();
        response.setCode(200);
        data = new Paged<>();
        response.setData(data);
        when(appConfigRepository.getConfigByCorpId(anyString())).thenReturn(config);
        try {
            when(groupGreetingTService.listGreeting(anyString(), any(), any())).thenReturn(response);
        } catch (TException e) {
            fail("Unexpected TException");
        }
    }

    @Test(expected = IllegalArgumentException.class)
    public void testQueryGroupGreetingListCorpIdIsNull() throws Throwable {
        dsGroupGreetingAcl.queryGroupGreetingList(null, greetingPrefix);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testQueryGroupGreetingListConfigIsNull() throws Throwable {
        when(appConfigRepository.getConfigByCorpId(anyString())).thenReturn(null);
        dsGroupGreetingAcl.queryGroupGreetingList(corpId, greetingPrefix);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testQueryGroupGreetingListBusinessCodeIsNull() throws Throwable {
        config.setBusinessCode(null);
        when(appConfigRepository.getConfigByCorpId(anyString())).thenReturn(config);
        dsGroupGreetingAcl.queryGroupGreetingList(corpId, greetingPrefix);
    }

    @Test(expected = RuntimeException.class)
    public void testQueryGroupGreetingListResponseIsNull() throws Throwable {
        try {
            when(groupGreetingTService.listGreeting(anyString(), any(), any())).thenReturn(null);
        } catch (TException e) {
            fail("Unexpected TException");
        }
        dsGroupGreetingAcl.queryGroupGreetingList(corpId, greetingPrefix);
    }

    @Test(expected = RuntimeException.class)
    public void testQueryGroupGreetingListResponseCodeIsNotSuccess() throws Throwable {
        response.setCode(500);
        dsGroupGreetingAcl.queryGroupGreetingList(corpId, greetingPrefix);
    }

    @Test(expected = RuntimeException.class)
    public void testQueryGroupGreetingListResponseDataIsNull() throws Throwable {
        response.setData(null);
        dsGroupGreetingAcl.queryGroupGreetingList(corpId, greetingPrefix);
    }

    @Test(expected = RuntimeException.class)
    public void testQueryGroupGreetingListExceptionOccurred() throws Throwable {
        try {
            when(groupGreetingTService.listGreeting(anyString(), any(), any())).thenThrow(new RuntimeException());
        } catch (TException e) {
            fail("Unexpected TException");
        }
        dsGroupGreetingAcl.queryGroupGreetingList(corpId, greetingPrefix);
    }
}
