package com.sankuai.scrm.core.service.infrastructure.acl.ds.utils;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsAssistantAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsCorpGroupAclService;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.entity.AssistantInfo;
import com.sankuai.scrm.core.service.infrastructure.acl.haima.HaimaAclService;
import com.sankuai.scrm.core.service.infrastructure.dal.babymapper.ContactUserDoMapper;
import com.sankuai.scrm.core.service.infrastructure.vo.CorpAppConfig;
import java.util.*;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class DsGroupMessageUtils_GetPureValidAssistantOfUserIdTest {

    @InjectMocks
    private DsGroupMessageUtils dsGroupMessageUtils;

    @Mock(lenient = true)
    private DsAssistantAcl dsAssistantAcl;

    @Mock(lenient = true)
    private DsCorpGroupAclService dsCorpGroupAclService;

    @Mock(lenient = true)
    private HaimaAclService haimaAclService;

    @Mock(lenient = true)
    private ContactUserDoMapper contactUserDoMapper;

    private int msgNum = 10;

    private List<String> limitedExecutors = Arrays.asList("executor1", "executor2");

    private List<String> userIds = Arrays.asList("user1", "user2");

    private CorpAppConfig corpAppConfig = new CorpAppConfig();

    private void setUpCommonMocks() {
        corpAppConfig.setCorpId("corpId");
        corpAppConfig.setOrgId(1L);
    }

    @Test
    public void testGetPureValidAssistantOfUserId_CorpAppConfigIsNull() throws Throwable {
        Set<String> limitedExecutors = new HashSet<>();
        List<AssistantInfo> result = dsGroupMessageUtils.getPureValidAssistantOfUserId(limitedExecutors, null);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetPureValidAssistantOfUserId_AssistantListIsEmpty() throws Throwable {
        Set<String> limitedExecutors = new HashSet<>();
        CorpAppConfig corpAppConfig = new CorpAppConfig();
        corpAppConfig.setCorpId("corpId");
        corpAppConfig.setOrgId(1L);
        when(dsAssistantAcl.getAssistantList(corpAppConfig.getCorpId(), corpAppConfig.getOrgId())).thenReturn(new ArrayList<>());
        List<AssistantInfo> result = dsGroupMessageUtils.getPureValidAssistantOfUserId(limitedExecutors, corpAppConfig);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetPureValidAssistantOfUserId_NoAssistantInLimitedExecutors() throws Throwable {
        Set<String> limitedExecutors = new HashSet<>();
        CorpAppConfig corpAppConfig = new CorpAppConfig();
        corpAppConfig.setCorpId("corpId");
        corpAppConfig.setOrgId(1L);
        List<AssistantInfo> assistantList = new ArrayList<>();
        AssistantInfo assistantInfo3 = new AssistantInfo();
        assistantInfo3.setUserId("userId3");
        assistantList.add(assistantInfo3);
        when(dsAssistantAcl.getAssistantList(corpAppConfig.getCorpId(), corpAppConfig.getOrgId())).thenReturn(assistantList);
        List<AssistantInfo> result = dsGroupMessageUtils.getPureValidAssistantOfUserId(limitedExecutors, corpAppConfig);
        assertEquals(0, result.size());
    }

    @Test
    public void testGetPureValidAssistantOfUserId_HasAssistantInLimitedExecutors() throws Throwable {
        Set<String> limitedExecutors = new HashSet<>();
        limitedExecutors.add("userId1");
        limitedExecutors.add("userId2");
        CorpAppConfig corpAppConfig = new CorpAppConfig();
        corpAppConfig.setCorpId("corpId");
        corpAppConfig.setOrgId(1L);
        List<AssistantInfo> assistantList = new ArrayList<>();
        AssistantInfo assistantInfo1 = new AssistantInfo();
        assistantInfo1.setUserId("userId1");
        AssistantInfo assistantInfo2 = new AssistantInfo();
        assistantInfo2.setUserId("userId2");
        assistantList.add(assistantInfo1);
        assistantList.add(assistantInfo2);
        when(dsAssistantAcl.getAssistantList(corpAppConfig.getCorpId(), corpAppConfig.getOrgId())).thenReturn(assistantList);
        List<AssistantInfo> result = dsGroupMessageUtils.getPureValidAssistantOfUserId(limitedExecutors, corpAppConfig);
        assertEquals(2, result.size());
    }

    @Test
    public void testGetAssistantOfUserIdWhenCorpAppConfigIsNull() throws Throwable {
        Map<String, AssistantInfo> result = dsGroupMessageUtils.getAssistantOfUserId(msgNum, limitedExecutors, userIds, null);
        assertEquals(new HashMap<>(), result);
    }

    @Test
    public void testGetAssistantOfUserIdWhenUserIdsIsEmpty() throws Throwable {
        setUpCommonMocks();
        Map<String, AssistantInfo> result = dsGroupMessageUtils.getAssistantOfUserId(msgNum, limitedExecutors, new ArrayList<>(), corpAppConfig);
        assertEquals(new HashMap<>(), result);
    }

    @Test
    public void testGetAssistantOfUserIdWhenGetAssistantCountListForSendReturnsNull() throws Throwable {
        setUpCommonMocks();
        when(dsAssistantAcl.getAssistantList(anyString(), anyLong())).thenReturn(null);
        Map<String, AssistantInfo> result = dsGroupMessageUtils.getAssistantOfUserId(msgNum, limitedExecutors, userIds, corpAppConfig);
        assertNull(result);
    }

    @Test
    public void testGetAssistantOfUserIdWhenGetAssistantSendLimitConfigReturnsEmptyMap() throws Throwable {
        setUpCommonMocks();
        when(dsAssistantAcl.getAssistantList(anyString(), anyLong())).thenReturn(Arrays.asList(new AssistantInfo()));
        Map<String, AssistantInfo> result = dsGroupMessageUtils.getAssistantOfUserId(msgNum, limitedExecutors, userIds, corpAppConfig);
        assertNull(result);
    }

    @Test
    public void testGetAssistantOfUserIdWhenGetAssistantUserMapReturnsNull() throws Throwable {
        setUpCommonMocks();
        when(dsAssistantAcl.getAssistantList(anyString(), anyLong())).thenReturn(Arrays.asList(new AssistantInfo()));
        Map<String, AssistantInfo> result = dsGroupMessageUtils.getAssistantOfUserId(msgNum, limitedExecutors, userIds, corpAppConfig);
        assertNull(result);
    }

    @Test
    public void testGetAssistantOfUserIdWhenCheckTargetHasAssistantThrowsException() throws Throwable {
        setUpCommonMocks();
        when(dsAssistantAcl.getAssistantList(anyString(), anyLong())).thenReturn(Arrays.asList(new AssistantInfo()));
        Map<String, AssistantInfo> result = dsGroupMessageUtils.getAssistantOfUserId(msgNum, limitedExecutors, userIds, corpAppConfig);
        assertNull(result);
    }

    @Test
    public void testGetAssistantOfUserIdWhenBalanceAssistantGroupReturnsNull() throws Throwable {
        setUpCommonMocks();
        when(dsAssistantAcl.getAssistantList(anyString(), anyLong())).thenReturn(Arrays.asList(new AssistantInfo()));
        Map<String, AssistantInfo> result = dsGroupMessageUtils.getAssistantOfUserId(msgNum, limitedExecutors, userIds, corpAppConfig);
        assertNull(result);
    }

    @Test
    public void testGetAssistantOfUserIdNormal() throws Throwable {
        setUpCommonMocks();
        when(dsAssistantAcl.getAssistantList(anyString(), anyLong())).thenReturn(Arrays.asList(new AssistantInfo()));
        Map<String, AssistantInfo> result = dsGroupMessageUtils.getAssistantOfUserId(msgNum, limitedExecutors, userIds, corpAppConfig);
        assertNull(result);
    }
}
