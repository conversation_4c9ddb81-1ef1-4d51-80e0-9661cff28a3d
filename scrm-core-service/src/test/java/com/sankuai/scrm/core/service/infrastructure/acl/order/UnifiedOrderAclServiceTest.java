package com.sankuai.scrm.core.service.infrastructure.acl.order;

import com.sankuai.general.order.querycenter.api.dto.UniOrderDTO;
import com.sankuai.general.order.querycenter.api.request.OrderQueryRequest;
import com.sankuai.general.order.querycenter.api.response.OrderResponse;
import com.sankuai.general.order.querycenter.api.service.OrderQueryService;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.junit.jupiter.api.Test;

import java.util.HashMap;
import java.util.Map;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class UnifiedOrderAclServiceTest {

    @InjectMocks
    private UnifiedOrderAclService unifiedOrderAclService;

    @Mock
    private OrderQueryService orderQueryService;



    /**
     * Test method for {@link UnifiedOrderAclService#isPrivateLiveOrder(String)} when orderId is null.
     */
    @Test
    public void testIsPrivateLiveOrderOrderIdIsNull() throws Throwable {
        // arrange
        String orderId = null;
        // act
        Boolean result = unifiedOrderAclService.isPrivateLiveOrder(orderId);
        // assert
        assertFalse(result);
    }

    /**
     * Test method for {@link UnifiedOrderAclService#isPrivateLiveOrder(String)} when OrderResponse is null.
     */
    @Test
    public void testIsPrivateLiveOrderOrderResponseIsNull() throws Throwable {
        // arrange
        String orderId = "testOrderId";
        when(orderQueryService.query(any(OrderQueryRequest.class), any())).thenReturn(null);
        // act
        Boolean result = unifiedOrderAclService.isPrivateLiveOrder(orderId);
        // assert
        assertFalse(result);
    }

    /**
     * Test method for {@link UnifiedOrderAclService#isPrivateLiveOrder(String)} when response data is null.
     */
    @Test
    public void testIsPrivateLiveOrderDataIsNull() throws Throwable {
        // arrange
        String orderId = "testOrderId";
        OrderResponse response = new OrderResponse();
        response.setData(null);
        when(orderQueryService.query(any(OrderQueryRequest.class), any())).thenReturn(response);
        // act
        Boolean result = unifiedOrderAclService.isPrivateLiveOrder(orderId);
        // assert
        assertFalse(result);
    }

    /**
     * Test method for {@link UnifiedOrderAclService#isPrivateLiveOrder(String)} when orderExtraMap is null.
     */
    @Test
    public void testIsPrivateLiveOrderOrderExtraMapIsNull() throws Throwable {
        // arrange
        String orderId = "testOrderId";
        UniOrderDTO uniOrderDTO = new UniOrderDTO();
        OrderResponse response = new OrderResponse();
        response.setData(uniOrderDTO);
        when(orderQueryService.query(any(OrderQueryRequest.class), any())).thenReturn(response);
        // act
        Boolean result = unifiedOrderAclService.isPrivateLiveOrder(orderId);
        // assert
        assertFalse(result);
    }

    /**
     * Test method for {@link UnifiedOrderAclService#isPrivateLiveOrder(String)} when distributionType is null.
     */
    @Test
    public void testIsPrivateLiveOrderDistributionTypeIsNull() throws Throwable {
        // arrange
        String orderId = "testOrderId";
        Map<String, String> orderExtraMap = new HashMap<>();
        orderExtraMap.put("DISTRIBUTION_BASIC_INFO", "{\"distributionType\":null}");
        UniOrderDTO uniOrderDTO = new UniOrderDTO();
        uniOrderDTO.setOrderExtraMap(orderExtraMap);
        OrderResponse response = new OrderResponse();
        response.setData(uniOrderDTO);
        when(orderQueryService.query(any(OrderQueryRequest.class), any())).thenReturn(response);
        // act
        Boolean result = unifiedOrderAclService.isPrivateLiveOrder(orderId);
        // assert
        assertFalse(result);
    }

    /**
     * Test method for {@link UnifiedOrderAclService#isPrivateLiveOrder(String)} when distributionType is not in privateLiveSet.
     */
    @Test
    public void testIsPrivateLiveOrderPrivateLiveSetNotContainsDistributionType() throws Throwable {
        // arrange
        String orderId = "testOrderId";
        Map<String, String> orderExtraMap = new HashMap<>();
        orderExtraMap.put("DISTRIBUTION_BASIC_INFO", "{\"distributionType\":\"OTHER\"}");
        UniOrderDTO uniOrderDTO = new UniOrderDTO();
        uniOrderDTO.setOrderExtraMap(orderExtraMap);
        OrderResponse response = new OrderResponse();
        response.setData(uniOrderDTO);
        when(orderQueryService.query(any(OrderQueryRequest.class), any())).thenReturn(response);
        // act
        Boolean result = unifiedOrderAclService.isPrivateLiveOrder(orderId);
        // assert
        assertFalse(result);
    }
}
