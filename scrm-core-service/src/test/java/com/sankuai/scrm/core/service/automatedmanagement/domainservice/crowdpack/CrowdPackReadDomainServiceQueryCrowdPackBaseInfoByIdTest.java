package com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmCrowdPackTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.crowdpack.ScrmCrowdPackBaseInfoConverter;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackBaseInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ext.ScrmAmCrowdPackDetailCountDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmCrowdPackBaseInfoDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackBaseInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackDetailInfoDOMapper;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CrowdPackReadDomainServiceQueryCrowdPackBaseInfoByIdTest {

    @InjectMocks
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock
    private ExtScrmAmCrowdPackBaseInfoDOMapper extScrmAmCrowdPackBaseInfoDOMapper;

    private ScrmAmCrowdPackBaseInfoDO scrmAmCrowdPackBaseInfoDO;

    @Mock
    private ExtScrmAmCrowdPackDetailInfoDOMapper extScrmAmCrowdPackDetailInfoDOMapper;

    @Mock
    private ScrmCrowdPackBaseInfoConverter packBaseInfoConverter;

    @Before
    public void setUp() {
        scrmAmCrowdPackBaseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        scrmAmCrowdPackBaseInfoDO.setId(1L);
    }

    private ScrmAmCrowdPackBaseInfoDO createScrmAmCrowdPackBaseInfoDO() {
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setId(1L);
        baseInfoDO.setType((byte) ScrmCrowdPackTypeEnum.PERSONA_PACK.getValue().byteValue());
        return baseInfoDO;
    }

    /**
     * 测试 packId 为 null 的情况
     */
    @Test
    public void testQueryCrowdPackBaseInfoByIdPackIdIsNull() {
        // arrange
        Long packId = null;
        // act
        ScrmAmCrowdPackBaseInfoDO result = crowdPackReadDomainService.queryCrowdPackBaseInfoById(packId);
        // assert
        assertNull(result);
    }

    /**
     * 测试 packId 不为 null，且数据库中存在对应的 ScrmAmCrowdPackBaseInfoDO 对象的情况
     */
    @Test
    public void testQueryCrowdPackBaseInfoByIdPackIdExists() {
        // arrange
        Long packId = 1L;
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(packId)).thenReturn(scrmAmCrowdPackBaseInfoDO);
        // act
        ScrmAmCrowdPackBaseInfoDO result = crowdPackReadDomainService.queryCrowdPackBaseInfoById(packId);
        // assert
        assertEquals(scrmAmCrowdPackBaseInfoDO, result);
    }

    /**
     * 测试 packId 不为 null，但数据库中不存在对应的 ScrmAmCrowdPackBaseInfoDO 对象的情况
     */
    @Test
    public void testQueryCrowdPackBaseInfoByIdPackIdNotExists() {
        // arrange
        Long packId = 1L;
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(packId)).thenReturn(null);
        // act
        ScrmAmCrowdPackBaseInfoDO result = crowdPackReadDomainService.queryCrowdPackBaseInfoById(packId);
        // assert
        assertNull(result);
    }

    /**
     * 测试 personaPackExists 方法，当 appId 为空时
     */
    @Test
    public void testIsPersonaPackExistsAppIdIsNull() throws Throwable {
        // arrange
        String appId = null;
        Integer personaId = 1;
        // act
        boolean result = crowdPackReadDomainService.isPersonaPackExists(appId, personaId);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 personaPackExists 方法，当 personaId 为 null 时
     */
    @Test
    public void testIsPersonaPackExistsPersonaIdIsNull() throws Throwable {
        // arrange
        String appId = "testAppId";
        Integer personaId = null;
        // act
        boolean result = crowdPackReadDomainService.isPersonaPackExists(appId, personaId);
        // assert
        assertFalse(result);
    }

    /**
     * 测试 personaPackExists 方法，当 appId 和 personaId 都不为空，且数据库中存在对应的记录时
     */
    @Test
    public void testIsPersonaPackExistsRecordExists() throws Throwable {
        // arrange
        String appId = "testAppId";
        Integer personaId = 1;
        when(extScrmAmCrowdPackBaseInfoDOMapper.countByExample(any(ScrmAmCrowdPackBaseInfoDOExample.class))).thenReturn(1L);
        // act
        boolean result = crowdPackReadDomainService.isPersonaPackExists(appId, personaId);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 personaPackExists 方法，当 appId 和 personaId 都不为空，但数据库中不存在对应的记录时
     */
    @Test
    public void testIsPersonaPackExistsRecordNotExists() throws Throwable {
        // arrange
        String appId = "testAppId";
        Integer personaId = 1;
        when(extScrmAmCrowdPackBaseInfoDOMapper.countByExample(any(ScrmAmCrowdPackBaseInfoDOExample.class))).thenReturn(0L);
        // act
        boolean result = crowdPackReadDomainService.isPersonaPackExists(appId, personaId);
        // assert
        assertFalse(result);
    }

    /**
     * Test case to cover when dto.getType() is not null
     */
    @Test
    public void testQueryCrowdPackList_WithType() throws Throwable {
        // arrange
        ScrmCrowdPackDTO dto = new ScrmCrowdPackDTO();
        dto.setAppId("testApp");
        // Corrected to cast Integer to Byte
        dto.setType((byte) ScrmCrowdPackTypeEnum.TIME_PACK.getValue().intValue());
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setAppId("testApp");
        List<ScrmAmCrowdPackBaseInfoDO> mockDOList = Collections.singletonList(baseInfoDO);
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByExampleOrderByUpdateTimeDesc(any())).thenReturn(mockDOList);
        ScrmCrowdPackDTO mockDTO = new ScrmCrowdPackDTO();
        mockDTO.setId(1L);
        mockDTO.setAppId("testApp");
        when(packBaseInfoConverter.convertToDTOsSafety(mockDOList)).thenReturn(Collections.singletonList(mockDTO));
        ScrmAmCrowdPackDetailCountDO countDO = new ScrmAmCrowdPackDetailCountDO();
        countDO.setPackId(1L);
        countDO.setCountNum(10L);
        when(extScrmAmCrowdPackDetailInfoDOMapper.batchCountByExample(any())).thenReturn(Collections.singletonList(countDO));
        // act
        List<ScrmCrowdPackDTO> result = crowdPackReadDomainService.queryCrowdPackList(dto, 1, 10);
        // assert
        assertEquals(1, result.size());
        assertEquals(10L, result.get(0).getCrowdCount());
        verify(extScrmAmCrowdPackBaseInfoDOMapper).selectByExampleOrderByUpdateTimeDesc(any());
    }

    /**
     * Test case to cover when dto.getName() is not blank
     */
    @Test
    public void testQueryCrowdPackList_WithName() throws Throwable {
        // arrange
        ScrmCrowdPackDTO dto = new ScrmCrowdPackDTO();
        dto.setAppId("testApp");
        dto.setName("testName");
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setAppId("testApp");
        List<ScrmAmCrowdPackBaseInfoDO> mockDOList = Collections.singletonList(baseInfoDO);
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByExampleOrderByUpdateTimeDesc(any())).thenReturn(mockDOList);
        when(packBaseInfoConverter.convertToDTOsSafety(any())).thenReturn(Collections.emptyList());
        // act
        List<ScrmCrowdPackDTO> result = crowdPackReadDomainService.queryCrowdPackList(dto, 1, 10);
        // assert
        verify(extScrmAmCrowdPackBaseInfoDOMapper).selectByExampleOrderByUpdateTimeDesc(any());
    }

    /**
     * Test case to cover when dto.getCreatorId() is not blank
     */
    @Test
    public void testQueryCrowdPackList_WithCreatorId() throws Throwable {
        // arrange
        ScrmCrowdPackDTO dto = new ScrmCrowdPackDTO();
        dto.setAppId("testApp");
        dto.setCreatorId("creator1,creator2");
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setAppId("testApp");
        List<ScrmAmCrowdPackBaseInfoDO> mockDOList = Collections.singletonList(baseInfoDO);
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByExampleOrderByUpdateTimeDesc(any())).thenReturn(mockDOList);
        when(packBaseInfoConverter.convertToDTOsSafety(any())).thenReturn(Collections.emptyList());
        // act
        List<ScrmCrowdPackDTO> result = crowdPackReadDomainService.queryCrowdPackList(dto, 1, 10);
        // assert
        verify(extScrmAmCrowdPackBaseInfoDOMapper).selectByExampleOrderByUpdateTimeDesc(any());
    }

    /**
     * Test case to cover when dto.getLastUpdaterId() is not blank
     */
    @Test
    public void testQueryCrowdPackList_WithLastUpdaterId() throws Throwable {
        // arrange
        ScrmCrowdPackDTO dto = new ScrmCrowdPackDTO();
        dto.setAppId("testApp");
        dto.setLastUpdaterId("updater1,updater2");
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setAppId("testApp");
        List<ScrmAmCrowdPackBaseInfoDO> mockDOList = Collections.singletonList(baseInfoDO);
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByExampleOrderByUpdateTimeDesc(any())).thenReturn(mockDOList);
        when(packBaseInfoConverter.convertToDTOsSafety(any())).thenReturn(Collections.emptyList());
        // act
        List<ScrmCrowdPackDTO> result = crowdPackReadDomainService.queryCrowdPackList(dto, 1, 10);
        // assert
        verify(extScrmAmCrowdPackBaseInfoDOMapper).selectByExampleOrderByUpdateTimeDesc(any());
    }

    /**
     * Test case to cover when crowdPackCountMap contains scrmCrowdPackDTO's id
     */
    @Test
    public void testQueryCrowdPackList_WithCrowdCount() throws Throwable {
        // arrange
        ScrmCrowdPackDTO dto = new ScrmCrowdPackDTO();
        dto.setAppId("testApp");
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setAppId("testApp");
        List<ScrmAmCrowdPackBaseInfoDO> mockDOList = Collections.singletonList(baseInfoDO);
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByExampleOrderByUpdateTimeDesc(any())).thenReturn(mockDOList);
        ScrmCrowdPackDTO mockDTO = new ScrmCrowdPackDTO();
        mockDTO.setId(1L);
        mockDTO.setAppId("testApp");
        when(packBaseInfoConverter.convertToDTOsSafety(mockDOList)).thenReturn(Collections.singletonList(mockDTO));
        ScrmAmCrowdPackDetailCountDO countDO = new ScrmAmCrowdPackDetailCountDO();
        countDO.setPackId(1L);
        countDO.setCountNum(10L);
        when(extScrmAmCrowdPackDetailInfoDOMapper.batchCountByExample(any())).thenReturn(Collections.singletonList(countDO));
        // act
        List<ScrmCrowdPackDTO> result = crowdPackReadDomainService.queryCrowdPackList(dto, 1, 10);
        // assert
        assertEquals(1, result.size());
        assertEquals(10L, result.get(0).getCrowdCount());
        verify(extScrmAmCrowdPackDetailInfoDOMapper).batchCountByExample(any());
    }

    @Test
    public void testPagePersonaPackInfoNormal() throws Throwable {
        // arrange
        long minId = 1L;
        int limit = 10;
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = createScrmAmCrowdPackBaseInfoDO();
        List<ScrmAmCrowdPackBaseInfoDO> expected = Collections.singletonList(baseInfoDO);
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByExample(any(ScrmAmCrowdPackBaseInfoDOExample.class))).thenReturn(expected);
        // act
        List<ScrmAmCrowdPackBaseInfoDO> result = crowdPackReadDomainService.pagePersonaPackInfo(minId, limit);
        // assert
        assertEquals(expected, result);
        verify(extScrmAmCrowdPackBaseInfoDOMapper, times(1)).selectByExample(any(ScrmAmCrowdPackBaseInfoDOExample.class));
    }

    @Test
    public void testPagePersonaPackInfoBoundary() throws Throwable {
        // arrange
        long minId = 1L;
        int limit = 10;
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = createScrmAmCrowdPackBaseInfoDO();
        List<ScrmAmCrowdPackBaseInfoDO> expected = Collections.singletonList(baseInfoDO);
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByExample(any(ScrmAmCrowdPackBaseInfoDOExample.class))).thenReturn(expected);
        // act
        List<ScrmAmCrowdPackBaseInfoDO> result = crowdPackReadDomainService.pagePersonaPackInfo(minId, limit);
        // assert
        assertEquals(expected, result);
        verify(extScrmAmCrowdPackBaseInfoDOMapper, times(1)).selectByExample(any(ScrmAmCrowdPackBaseInfoDOExample.class));
    }

    @Test(expected = RuntimeException.class)
    public void testPagePersonaPackInfoException() throws Throwable {
        // arrange
        long minId = 1L;
        int limit = 10;
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByExample(any(ScrmAmCrowdPackBaseInfoDOExample.class))).thenThrow(new RuntimeException());
        // act
        crowdPackReadDomainService.pagePersonaPackInfo(minId, limit);
        // assert
        verify(extScrmAmCrowdPackBaseInfoDOMapper, times(1)).selectByExample(any(ScrmAmCrowdPackBaseInfoDOExample.class));
    }
}
