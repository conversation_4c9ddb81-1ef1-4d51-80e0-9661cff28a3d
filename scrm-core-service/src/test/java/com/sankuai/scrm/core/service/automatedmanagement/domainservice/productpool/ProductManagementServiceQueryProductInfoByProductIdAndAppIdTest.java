package com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool;

import com.dianping.pigeon.remoting.invoker.concurrent.FutureFactory;
import com.sankuai.dz.srcm.automatedmanagement.dto.productpool.ProductInfoDTO;
import com.sankuai.dztheme.deal.DealProductService;
import com.sankuai.dztheme.deal.dto.DealProductDTO;
import com.sankuai.dztheme.deal.res.DealProductResult;
import com.sankuai.dztheme.generalproduct.GeneralProductService;
import com.sankuai.dztheme.generalproduct.res.GeneralProductDTO;
import com.sankuai.dztheme.generalproduct.res.GeneralProductResult;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductItemsDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationProductItemsDOMapper;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;
import com.dianping.deal.common.enums.ClientTypeEnum;
import com.dianping.deal.common.enums.Platform;
import com.dianping.general.unified.search.api.productshopsearch.response.GeneralProductShopSearchResponse;
import com.dianping.product.shelf.common.dto.NavTag;
import com.dianping.product.shelf.common.dto.Response;
import com.dianping.product.shelf.common.dto.ShelfComponent;
import com.dianping.product.shelf.common.dto.ShelfDTO;
import com.dianping.product.shelf.common.dto.ShelfNavComponent;
import com.dianping.product.shelf.common.request.ShelfRequest;
import com.dianping.product.shelf.query.api.GeneralShelfQueryService;
import com.sankuai.dz.srcm.automatedmanagement.request.activitypage.QueryActivityPageShelfProductRequest;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import java.lang.reflect.Method;
import org.mockito.ArgumentCaptor;
import static org.mockito.Mockito.when;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationActivSceneCodeDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationActivSceneCodeDOMapper;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;

import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.Future;
import java.util.concurrent.TimeUnit;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@ExtendWith(MockitoExtension.class)
class ProductManagementServiceQueryProductInfoByProductIdAndAppIdTest {

    @Mock
    private ScrmAmProcessOrchestrationProductItemsDOMapper productItemsDOMapper;

    @Mock
    private DealProductService dealProductService;

    @Mock
    private GeneralProductService generalProductService;

    @InjectMocks
    private ProductManagementService productManagementService;

    @Mock
    private Future<DealProductResult> dealProductResultFuture;

    @Mock
    private Future<GeneralProductResult> generalProductResultFuture;

    private MockedStatic<FutureFactory> futureFactoryMockedStatic;

    @Mock
    private GeneralShelfQueryService generalShelfQueryService;

    @Mock
    private ScrmAmProcessOrchestrationActivSceneCodeDOMapper activSceneCodeDOMapper;

    @BeforeEach
    void setUp() {
        futureFactoryMockedStatic = mockStatic(FutureFactory.class);
    }

    @AfterEach
    void tearDown() {
        if (futureFactoryMockedStatic != null) {
            futureFactoryMockedStatic.close();
        }
    }

    /**
     * Test when productIds list is empty
     */
    @Test
    public void testQueryProductInfoByProductIdAndAppIdEmptyProductIds() throws Throwable {
        // arrange
        List<Long> productIds = Collections.emptyList();
        String appId = "testApp";
        // act
        List<ProductInfoDTO> result = productManagementService.queryProductInfoByProductIdAndAppId(productIds, appId);
        // assert
        assertNull(result);
    }

    /**
     * Test when appId is empty
     */
    @Test
    public void testQueryProductInfoByProductIdAndAppIdEmptyAppId() throws Throwable {
        // arrange
        List<Long> productIds = Arrays.asList(1L, 2L);
        String appId = "";
        // act
        List<ProductInfoDTO> result = productManagementService.queryProductInfoByProductIdAndAppId(productIds, appId);
        // assert
        assertNull(result);
    }

    /**
     * Test with valid inputs but no matching products
     */
    @Test
    public void testQueryProductInfoByProductIdAndAppIdNoMatchingProducts() throws Throwable {
        // arrange
        List<Long> productIds = Arrays.asList(1L, 2L);
        String appId = "testApp";
        when(productItemsDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        List<ProductInfoDTO> result = productManagementService.queryProductInfoByProductIdAndAppId(productIds, appId);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test with only deal products (productType = 1)
     */
    @Test
    public void testQueryProductInfoByProductIdAndAppIdOnlyDealProducts() throws Throwable {
        // arrange
        List<Long> productIds = Arrays.asList(1L, 2L);
        String appId = "testApp";
        List<ScrmAmProcessOrchestrationProductItemsDO> items = new ArrayList<>();
        ScrmAmProcessOrchestrationProductItemsDO item1 = new ScrmAmProcessOrchestrationProductItemsDO();
        item1.setProductId(1L);
        item1.setProductType(1);
        ScrmAmProcessOrchestrationProductItemsDO item2 = new ScrmAmProcessOrchestrationProductItemsDO();
        item2.setProductId(2L);
        item2.setProductType(1);
        items.add(item1);
        items.add(item2);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(items);
        // Mock deal product service response
        DealProductResult dealProductResult = new DealProductResult();
        List<DealProductDTO> dealProducts = new ArrayList<>();
        for (Long id : Arrays.asList(1L, 2L)) {
            DealProductDTO dto = new DealProductDTO();
            dto.setProductId(id.intValue());
            dto.setName("Deal Product " + id);
            dto.setHeadPic("http://example.com/deal/" + id);
            dto.setBasePrice(new BigDecimal("100.00"));
            dealProducts.add(dto);
        }
        dealProductResult.setDeals(dealProducts);
        when(dealProductService.query(any())).thenReturn(null);
        when(dealProductResultFuture.get(5, TimeUnit.SECONDS)).thenReturn(dealProductResult);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(DealProductResult.class)).thenReturn(dealProductResultFuture);
        // act
        List<ProductInfoDTO> result = productManagementService.queryProductInfoByProductIdAndAppId(productIds, appId);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(1, result.get(0).getProductType());
        assertEquals(1, result.get(1).getProductType());
    }

    /**
     * Test with only general products (productType = 2)
     */
    @Test
    public void testQueryProductInfoByProductIdAndAppIdOnlyGeneralProducts() throws Throwable {
        // arrange
        List<Long> productIds = Arrays.asList(3L, 4L);
        String appId = "testApp";
        List<ScrmAmProcessOrchestrationProductItemsDO> items = new ArrayList<>();
        ScrmAmProcessOrchestrationProductItemsDO item1 = new ScrmAmProcessOrchestrationProductItemsDO();
        item1.setProductId(3L);
        item1.setProductType(2);
        ScrmAmProcessOrchestrationProductItemsDO item2 = new ScrmAmProcessOrchestrationProductItemsDO();
        item2.setProductId(4L);
        item2.setProductType(2);
        items.add(item1);
        items.add(item2);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(items);
        // Mock general product service response
        GeneralProductResult generalProductResult = new GeneralProductResult();
        List<GeneralProductDTO> generalProducts = new ArrayList<>();
        for (Long id : Arrays.asList(3L, 4L)) {
            GeneralProductDTO dto = new GeneralProductDTO();
            dto.setProductId(id.intValue());
            dto.setName("General Product " + id);
            dto.setHeadPic("http://example.com/general/" + id);
            dto.setOriginalSalePrice(new BigDecimal("200.00"));
            generalProducts.add(dto);
        }
        generalProductResult.setProducts(generalProducts);
        when(generalProductService.query(any())).thenReturn(null);
        when(generalProductResultFuture.get(5000L, TimeUnit.MILLISECONDS)).thenReturn(generalProductResult);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(generalProductResultFuture);
        // act
        List<ProductInfoDTO> result = productManagementService.queryProductInfoByProductIdAndAppId(productIds, appId);
        // assert
        assertNotNull(result);
        assertEquals(2, result.size());
        assertEquals(2, result.get(0).getProductType());
        assertEquals(2, result.get(1).getProductType());
    }

    /**
     * Test with mixed product types
     */
    @Test
    public void testQueryProductInfoByProductIdAndAppIdMixedProductTypes() throws Throwable {
        // arrange
        List<Long> productIds = Arrays.asList(1L, 2L, 3L);
        String appId = "testApp";
        List<ScrmAmProcessOrchestrationProductItemsDO> items = new ArrayList<>();
        ScrmAmProcessOrchestrationProductItemsDO item1 = new ScrmAmProcessOrchestrationProductItemsDO();
        item1.setProductId(1L);
        item1.setProductType(1);
        ScrmAmProcessOrchestrationProductItemsDO item2 = new ScrmAmProcessOrchestrationProductItemsDO();
        item2.setProductId(2L);
        item2.setProductType(2);
        ScrmAmProcessOrchestrationProductItemsDO item3 = new ScrmAmProcessOrchestrationProductItemsDO();
        item3.setProductId(3L);
        item3.setProductType(1);
        items.add(item1);
        items.add(item2);
        items.add(item3);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(items);
        // Mock deal product service response
        DealProductResult dealProductResult = new DealProductResult();
        List<DealProductDTO> dealProducts = new ArrayList<>();
        for (Long id : Arrays.asList(1L, 3L)) {
            DealProductDTO dto = new DealProductDTO();
            dto.setProductId(id.intValue());
            dto.setName("Deal Product " + id);
            dto.setHeadPic("http://example.com/deal/" + id);
            dto.setBasePrice(new BigDecimal("100.00"));
            dealProducts.add(dto);
        }
        dealProductResult.setDeals(dealProducts);
        // Mock general product service response
        GeneralProductResult generalProductResult = new GeneralProductResult();
        List<GeneralProductDTO> generalProducts = new ArrayList<>();
        GeneralProductDTO generalDto = new GeneralProductDTO();
        generalDto.setProductId(2);
        generalDto.setName("General Product 2");
        generalDto.setHeadPic("http://example.com/general/2");
        generalDto.setOriginalSalePrice(new BigDecimal("200.00"));
        generalProducts.add(generalDto);
        generalProductResult.setProducts(generalProducts);
        when(dealProductService.query(any())).thenReturn(null);
        when(generalProductService.query(any())).thenReturn(null);
        when(dealProductResultFuture.get(5, TimeUnit.SECONDS)).thenReturn(dealProductResult);
        when(generalProductResultFuture.get(5000L, TimeUnit.MILLISECONDS)).thenReturn(generalProductResult);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(DealProductResult.class)).thenReturn(dealProductResultFuture);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(GeneralProductResult.class)).thenReturn(generalProductResultFuture);
        // act
        List<ProductInfoDTO> result = productManagementService.queryProductInfoByProductIdAndAppId(productIds, appId);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(1, result.get(0).getProductType());
        assertEquals(2, result.get(1).getProductType());
        assertEquals(1, result.get(2).getProductType());
    }

    /**
     * Test with some products not found in database
     */
    @Test
    public void testQueryProductInfoByProductIdAndAppIdSomeProductsNotFound() throws Throwable {
        // arrange
        List<Long> productIds = Arrays.asList(1L, 2L, 3L);
        String appId = "testApp";
        List<ScrmAmProcessOrchestrationProductItemsDO> items = new ArrayList<>();
        ScrmAmProcessOrchestrationProductItemsDO item1 = new ScrmAmProcessOrchestrationProductItemsDO();
        item1.setProductId(1L);
        item1.setProductType(1);
        items.add(item1);
        when(productItemsDOMapper.selectByExample(any())).thenReturn(items);
        // Mock deal product service response
        DealProductResult dealProductResult = new DealProductResult();
        List<DealProductDTO> dealProducts = new ArrayList<>();
        DealProductDTO dto = new DealProductDTO();
        dto.setProductId(1);
        dto.setName("Deal Product 1");
        dto.setHeadPic("http://example.com/deal/1");
        dto.setBasePrice(new BigDecimal("100.00"));
        dealProducts.add(dto);
        dealProductResult.setDeals(dealProducts);
        when(dealProductService.query(any())).thenReturn(null);
        when(dealProductResultFuture.get(5, TimeUnit.SECONDS)).thenReturn(dealProductResult);
        futureFactoryMockedStatic.when(() -> FutureFactory.getFuture(DealProductResult.class)).thenReturn(dealProductResultFuture);
        // act
        List<ProductInfoDTO> result = productManagementService.queryProductInfoByProductIdAndAppId(productIds, appId);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(1L, result.get(0).getProductId());
        assertEquals(1, result.get(0).getProductType());
    }

    private Long invokeGetShelfNavRes(QueryActivityPageShelfProductRequest request, ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO, long mtPoiId, long dpPoiId) throws Exception {
        Method method = ProductManagementService.class.getDeclaredMethod("getShelfNavRes", QueryActivityPageShelfProductRequest.class, ScrmAmProcessOrchestrationActivSceneCodeDO.class, long.class, long.class);
        method.setAccessible(true);
        return (Long) method.invoke(productManagementService, request, sceneCodeDO, mtPoiId, dpPoiId);
    }

    @Test
    public void testGetShelfNavRes_NullResponse() throws Throwable {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setCityId(1);
        request.setUserId(123L);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        when(generalShelfQueryService.queryShelfNav(any(ShelfRequest.class))).thenReturn(null);
        // act
        Long result = invokeGetShelfNavRes(request, sceneCodeDO, 456L, 789L);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetShelfNavRes_UnsuccessfulResponse() throws Throwable {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setCityId(1);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        Response<ShelfDTO> response = new Response<>();
        response.setSuccess(false);
        when(generalShelfQueryService.queryShelfNav(any(ShelfRequest.class))).thenReturn(response);
        // act
        Long result = invokeGetShelfNavRes(request, sceneCodeDO, 456L, 789L);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetShelfNavRes_NullContent() throws Throwable {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setCityId(1);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        Response<ShelfDTO> response = new Response<>();
        response.setSuccess(true);
        response.setContent(null);
        when(generalShelfQueryService.queryShelfNav(any(ShelfRequest.class))).thenReturn(response);
        // act
        Long result = invokeGetShelfNavRes(request, sceneCodeDO, 456L, 789L);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetShelfNavRes_EmptyShelfComponents() throws Throwable {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setCityId(1);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        ShelfDTO shelfDTO = new ShelfDTO();
        shelfDTO.setShelfComponentList(Collections.emptyList());
        Response<ShelfDTO> response = new Response<>();
        response.setSuccess(true);
        response.setContent(shelfDTO);
        when(generalShelfQueryService.queryShelfNav(any(ShelfRequest.class))).thenReturn(response);
        // act
        Long result = invokeGetShelfNavRes(request, sceneCodeDO, 456L, 789L);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetShelfNavRes_NoMatchingShelfComponent() throws Throwable {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setCityId(1);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        ShelfDTO shelfDTO = new ShelfDTO();
        ShelfComponent shelfComponent = new ShelfComponent();
        shelfComponent.setName("预订");
        shelfDTO.setShelfComponentList(Arrays.asList(shelfComponent));
        Response<ShelfDTO> response = new Response<>();
        response.setSuccess(true);
        response.setContent(shelfDTO);
        when(generalShelfQueryService.queryShelfNav(any(ShelfRequest.class))).thenReturn(response);
        // act
        Long result = invokeGetShelfNavRes(request, sceneCodeDO, 456L, 789L);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetShelfNavRes_NullNavComponent() throws Throwable {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setCityId(1);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        ShelfDTO shelfDTO = new ShelfDTO();
        ShelfComponent shelfComponent = new ShelfComponent();
        shelfComponent.setName("其他");
        shelfComponent.setShelfNavComponent(null);
        shelfDTO.setShelfComponentList(Arrays.asList(shelfComponent));
        Response<ShelfDTO> response = new Response<>();
        response.setSuccess(true);
        response.setContent(shelfDTO);
        when(generalShelfQueryService.queryShelfNav(any(ShelfRequest.class))).thenReturn(response);
        // act
        Long result = invokeGetShelfNavRes(request, sceneCodeDO, 456L, 789L);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetShelfNavRes_EmptyNavTags() throws Throwable {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setCityId(1);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        ShelfDTO shelfDTO = new ShelfDTO();
        ShelfComponent shelfComponent = new ShelfComponent();
        shelfComponent.setName("其他");
        ShelfNavComponent shelfNavComponent = new ShelfNavComponent();
        shelfNavComponent.setShelfNavTagList(Collections.emptyList());
        shelfComponent.setShelfNavComponent(shelfNavComponent);
        shelfDTO.setShelfComponentList(Arrays.asList(shelfComponent));
        Response<ShelfDTO> response = new Response<>();
        response.setSuccess(true);
        response.setContent(shelfDTO);
        when(generalShelfQueryService.queryShelfNav(any(ShelfRequest.class))).thenReturn(response);
        // act
        Long result = invokeGetShelfNavRes(request, sceneCodeDO, 456L, 789L);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetShelfNavRes_FoundAllNavTag() throws Throwable {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setCityId(1);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        ShelfDTO shelfDTO = new ShelfDTO();
        ShelfComponent shelfComponent = new ShelfComponent();
        shelfComponent.setName("其他");
        ShelfNavComponent shelfNavComponent = new ShelfNavComponent();
        NavTag navTag = new NavTag();
        navTag.setName("全部");
        navTag.setId(111L);
        shelfNavComponent.setShelfNavTagList(Arrays.asList(navTag));
        shelfComponent.setShelfNavComponent(shelfNavComponent);
        shelfDTO.setShelfComponentList(Arrays.asList(shelfComponent));
        Response<ShelfDTO> response = new Response<>();
        response.setContent(shelfDTO);
        response.setSuccess(true);
        ArgumentCaptor<ShelfRequest> requestCaptor = ArgumentCaptor.forClass(ShelfRequest.class);
        when(generalShelfQueryService.queryShelfNav(requestCaptor.capture())).thenReturn(response);
        // act
        Long result = invokeGetShelfNavRes(request, sceneCodeDO, 456L, 789L);
        // assert
        assertEquals(111L, result);
        ShelfRequest capturedRequest = requestCaptor.getValue();
        assertEquals(Platform.MT.getSource(), capturedRequest.getPlatform());
        assertEquals(ClientTypeEnum.mt_weApp.getType(), capturedRequest.getClientType());
        assertTrue(capturedRequest.isUseCache());
    }

    @Test
    public void testGetShelfNavRes_FoundRecommendNavTag() throws Throwable {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setCityId(1);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        ShelfDTO shelfDTO = new ShelfDTO();
        ShelfComponent shelfComponent = new ShelfComponent();
        shelfComponent.setName("其他");
        ShelfNavComponent shelfNavComponent = new ShelfNavComponent();
        NavTag navTag1 = new NavTag();
        navTag1.setName("其他");
        NavTag navTag2 = new NavTag();
        navTag2.setName("推荐");
        navTag2.setId(222L);
        shelfNavComponent.setShelfNavTagList(Arrays.asList(navTag1, navTag2));
        shelfComponent.setShelfNavComponent(shelfNavComponent);
        shelfDTO.setShelfComponentList(Arrays.asList(shelfComponent));
        Response<ShelfDTO> response = new Response<>();
        response.setContent(shelfDTO);
        response.setSuccess(true);
        when(generalShelfQueryService.queryShelfNav(any(ShelfRequest.class))).thenReturn(response);
        // act
        Long result = invokeGetShelfNavRes(request, sceneCodeDO, 456L, 789L);
        // assert
        assertEquals(222L, result);
    }

    @Test
    public void testGetShelfNavRes_NavTagWithChildren() throws Throwable {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setCityId(1);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        ShelfDTO shelfDTO = new ShelfDTO();
        ShelfComponent shelfComponent = new ShelfComponent();
        shelfComponent.setName("其他");
        ShelfNavComponent shelfNavComponent = new ShelfNavComponent();
        NavTag childTag = new NavTag();
        childTag.setId(333L);
        NavTag navTag = new NavTag();
        navTag.setName("全部");
        navTag.setId(111L);
        navTag.setChildNavTag(Arrays.asList(childTag));
        shelfNavComponent.setShelfNavTagList(Arrays.asList(navTag));
        shelfComponent.setShelfNavComponent(shelfNavComponent);
        shelfDTO.setShelfComponentList(Arrays.asList(shelfComponent));
        Response<ShelfDTO> response = new Response<>();
        response.setContent(shelfDTO);
        response.setSuccess(true);
        when(generalShelfQueryService.queryShelfNav(any(ShelfRequest.class))).thenReturn(response);
        // act
        Long result = invokeGetShelfNavRes(request, sceneCodeDO, 456L, 789L);
        // assert
        assertEquals(333L, result);
    }

    @Test
    public void testGetShelfNavRes_NoMatchingNavTag() throws Throwable {
        // arrange
        QueryActivityPageShelfProductRequest request = new QueryActivityPageShelfProductRequest();
        request.setCityId(1);
        ScrmAmProcessOrchestrationActivSceneCodeDO sceneCodeDO = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        ShelfDTO shelfDTO = new ShelfDTO();
        ShelfComponent shelfComponent = new ShelfComponent();
        shelfComponent.setName("其他");
        ShelfNavComponent shelfNavComponent = new ShelfNavComponent();
        NavTag navTag = new NavTag();
        navTag.setName("其他");
        shelfNavComponent.setShelfNavTagList(Arrays.asList(navTag));
        shelfComponent.setShelfNavComponent(shelfNavComponent);
        shelfDTO.setShelfComponentList(Arrays.asList(shelfComponent));
        Response<ShelfDTO> response = new Response<>();
        response.setContent(shelfDTO);
        response.setSuccess(true);
        when(generalShelfQueryService.queryShelfNav(any(ShelfRequest.class))).thenReturn(response);
        // act
        Long result = invokeGetShelfNavRes(request, sceneCodeDO, 456L, 789L);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetSceneCodeDOByScenecodeNullSceneCode() throws Throwable {
        // arrange - no setup needed for null input
        // act
        ScrmAmProcessOrchestrationActivSceneCodeDO result = productManagementService.getSceneCodeDOByScenecode(null);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetSceneCodeDOByScenecodeEmptySceneCode() throws Throwable {
        // arrange - no setup needed for empty input
        // act
        ScrmAmProcessOrchestrationActivSceneCodeDO result = productManagementService.getSceneCodeDOByScenecode("");
        // assert
        assertNull(result);
    }

    @Test
    public void testGetSceneCodeDOByScenecodeNoMatchingRecord() throws Throwable {
        // arrange
        String sceneCode = "valid_scene_code";
        when(activSceneCodeDOMapper.selectByExample(any(ScrmAmProcessOrchestrationActivSceneCodeDOExample.class))).thenReturn(Collections.emptyList());
        // act
        ScrmAmProcessOrchestrationActivSceneCodeDO result = productManagementService.getSceneCodeDOByScenecode(sceneCode);
        // assert
        assertNull(result);
    }

    @Test
    public void testGetSceneCodeDOByScenecodeOneMatchingRecord() throws Throwable {
        // arrange
        String sceneCode = "valid_scene_code";
        ScrmAmProcessOrchestrationActivSceneCodeDO expected = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        expected.setSceneCode(sceneCode);
        when(activSceneCodeDOMapper.selectByExample(any(ScrmAmProcessOrchestrationActivSceneCodeDOExample.class))).thenReturn(Collections.singletonList(expected));
        // act
        ScrmAmProcessOrchestrationActivSceneCodeDO result = productManagementService.getSceneCodeDOByScenecode(sceneCode);
        // assert
        assertNotNull(result);
        assertEquals(sceneCode, result.getSceneCode());
    }

    @Test
    public void testGetSceneCodeDOByScenecodeMultipleMatchingRecords() throws Throwable {
        // arrange
        String sceneCode = "valid_scene_code";
        ScrmAmProcessOrchestrationActivSceneCodeDO expectedFirst = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        expectedFirst.setId(2L);
        expectedFirst.setSceneCode(sceneCode);
        ScrmAmProcessOrchestrationActivSceneCodeDO expectedSecond = new ScrmAmProcessOrchestrationActivSceneCodeDO();
        expectedSecond.setId(1L);
        expectedSecond.setSceneCode(sceneCode);
        List<ScrmAmProcessOrchestrationActivSceneCodeDO> records = Arrays.asList(expectedFirst, expectedSecond);
        when(activSceneCodeDOMapper.selectByExample(any(ScrmAmProcessOrchestrationActivSceneCodeDOExample.class))).thenReturn(records);
        // act
        ScrmAmProcessOrchestrationActivSceneCodeDO result = productManagementService.getSceneCodeDOByScenecode(sceneCode);
        // assert
        assertNotNull(result);
        // should return the one with higher id
        assertEquals(2L, result.getId());
        assertEquals(sceneCode, result.getSceneCode());
    }
}
