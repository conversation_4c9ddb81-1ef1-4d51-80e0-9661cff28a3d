package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.aigc.service.dal.mapper.ScrmGroupRetailUserFootPrintRecordDOMapper;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineMqMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.dto.ScrmDzBizDataUserTrackDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.realtime.task.mq.producer.GroupRetailAiEngineDispatchProducer;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.Collections;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

class GroupRetailAiEngineUserFootprintLandConsumerRecvMessageTest {

    @Mock(lenient = true)
    private RealTimeTaskConsumerConfig consumerConfig;

    @Mock(lenient = true)
    private GroupRetailAiEngineDispatchProducer dispatchProducer;

    @Mock(lenient = true)
    private ScrmGroupRetailUserFootPrintRecordDOMapper userFootPrintRecordDOMapper;

    @Mock(lenient = true)
    private RedisStoreClient redisClient;

    @InjectMocks
    private GroupRetailAiEngineUserFootprintLandConsumer consumer;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(consumerConfig.getTestAppId()).thenReturn("testApp");
        when(consumerConfig.getValidActionTypes()).thenReturn(Arrays.asList(9, 10));
        when(consumerConfig.getDispatchDelayTime()).thenReturn(1000L);
        when(redisClient.setnx(any(), any(), anyInt())).thenReturn(true);
    }

    private ConsumeStatus invokeRecvMessage(MafkaMessage<String> message, MessagetContext context) throws Exception {
        Method method = GroupRetailAiEngineUserFootprintLandConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        method.setAccessible(true);
        return (ConsumeStatus) method.invoke(consumer, message, context);
    }

    /**
     * Test when message body is null
     */
    @Test
    public void testRecvMessageNullMessageBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = new MafkaMessage<>("topic", 1, 1L, "key", null);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test when user track data is null
     */
    @Test
    public void testRecvMessageNullUserTrack() throws Throwable {
        // arrange
        GroupRetailAiEngineMqMessageDTO dto = new GroupRetailAiEngineMqMessageDTO();
        String json = JsonUtils.toStr(dto);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 1, 1L, "key", json);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test order action type processing
     */
    @Test
    public void testRecvMessageOrderActionType() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("ord"));
        trackDTO.setTypelist(Collections.singletonList("type"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setItemidlist(Collections.singletonList(456L));
        GroupRetailAiEngineMqMessageDTO mqDto = new GroupRetailAiEngineMqMessageDTO();
        mqDto.setUserTrackDTO(trackDTO);
        String json = JsonUtils.toStr(mqDto);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 1, 1L, "key", json);
        MessagetContext context = new MessagetContext();
        when(userFootPrintRecordDOMapper.countByExample(any())).thenReturn(0L);
        when(consumerConfig.getValidAppId( any(),eq(null))).thenReturn("TEST_APP_ID");
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper).insert(any());
        // verify(dispatchProducer).sendDelayMessage(any(), anyLong());
    }

    /**
     * Test pay action type processing
     */
    @Test
    public void testRecvMessagePayActionType() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("pay"));
        trackDTO.setTypelist(Collections.singletonList("type"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setShopidlist(Collections.singletonList(789L));
        GroupRetailAiEngineMqMessageDTO mqDto = new GroupRetailAiEngineMqMessageDTO();
        mqDto.setUserTrackDTO(trackDTO);
        String json = JsonUtils.toStr(mqDto);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 1, 1L, "key", json);
        MessagetContext context = new MessagetContext();
        when(userFootPrintRecordDOMapper.countByExample(any())).thenReturn(0L);
        when(consumerConfig.getValidAppId( any(),eq(null))).thenReturn("TEST_APP_ID");
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper).insert(any());
        // verify(dispatchProducer).sendDelayMessage(any(), anyLong());
    }

    /**
     * Test when action type is not in valid list
     */
    @Test
    public void testRecvMessageInvalidActionType() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("invalid"));
        trackDTO.setTypelist(Collections.singletonList("type"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        GroupRetailAiEngineMqMessageDTO mqDto = new GroupRetailAiEngineMqMessageDTO();
        mqDto.setUserTrackDTO(trackDTO);
        String json = JsonUtils.toStr(mqDto);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 1, 1L, "key", json);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper, never()).insert(any());
    }

    /**
     * Test when userId is null
     */
    @Test
    public void testRecvMessageNullUserId() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("pay"));
        trackDTO.setTypelist(Collections.singletonList("type"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.emptyList());
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setShopidlist(Collections.singletonList(789L));
        GroupRetailAiEngineMqMessageDTO mqDto = new GroupRetailAiEngineMqMessageDTO();
        mqDto.setUserTrackDTO(trackDTO);
        String json = JsonUtils.toStr(mqDto);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 1, 1L, "key", json);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(dispatchProducer, never()).sendDelayMessage(any(), anyLong());
    }

    /**
     * Test when appId is blank
     */
    @Test
    public void testRecvMessageBlankAppId() throws Throwable {
        // arrange
        when(consumerConfig.getTestAppId()).thenReturn("");
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("pay"));
        trackDTO.setTypelist(Collections.singletonList("type"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setShopidlist(Collections.singletonList(789L));
        GroupRetailAiEngineMqMessageDTO mqDto = new GroupRetailAiEngineMqMessageDTO();
        mqDto.setUserTrackDTO(trackDTO);
        String json = JsonUtils.toStr(mqDto);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 1, 1L, "key", json);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(dispatchProducer, never()).sendDelayMessage(any(), anyLong());
    }

    /**
     * Test when JSON parsing fails
     */
    @Test
    public void testRecvMessageJsonParseError() throws Throwable {
        // arrange
        String invalidJson = "{invalid json}";
        MafkaMessage<String> message = new MafkaMessage<>("topic", 1, 1L, "key", invalidJson);
        MessagetContext context = new MessagetContext();
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test when database insert fails
     */
    @Test
    public void testRecvMessageDatabaseInsertError() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("pay"));
        trackDTO.setTypelist(Collections.singletonList("type"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setShopidlist(Collections.singletonList(789L));
        GroupRetailAiEngineMqMessageDTO mqDto = new GroupRetailAiEngineMqMessageDTO();
        mqDto.setUserTrackDTO(trackDTO);
        String json = JsonUtils.toStr(mqDto);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 1, 1L, "key", json);
        MessagetContext context = new MessagetContext();
        when(userFootPrintRecordDOMapper.countByExample(any())).thenReturn(0L);
        doThrow(new RuntimeException("DB error")).when(userFootPrintRecordDOMapper).insert(any());
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test when record already exists (count > 0)
     */
    @Test
    public void testRecvMessageRecordExists() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("pay"));
        trackDTO.setTypelist(Collections.singletonList("type"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setShopidlist(Collections.singletonList(789L));
        GroupRetailAiEngineMqMessageDTO mqDto = new GroupRetailAiEngineMqMessageDTO();
        mqDto.setUserTrackDTO(trackDTO);
        String json = JsonUtils.toStr(mqDto);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 1, 1L, "key", json);
        MessagetContext context = new MessagetContext();
        when(userFootPrintRecordDOMapper.countByExample(any())).thenReturn(1L);
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(userFootPrintRecordDOMapper, never()).insert(any());
    }

    /**
     * Test when producer send fails
     */
    @Test
    public void testRecvMessageProducerSendError() throws Throwable {
        // arrange
        ScrmDzBizDataUserTrackDTO trackDTO = new ScrmDzBizDataUserTrackDTO();
        trackDTO.setActionlist(Collections.singletonList("pay"));
        trackDTO.setTypelist(Collections.singletonList("type"));
        trackDTO.setPlatformlist(Collections.singletonList("mt"));
        trackDTO.setUserid(Collections.singletonList(123L));
        trackDTO.setTimestamplist(Collections.singletonList(System.currentTimeMillis()));
        trackDTO.setShopidlist(Collections.singletonList(789L));
        GroupRetailAiEngineMqMessageDTO mqDto = new GroupRetailAiEngineMqMessageDTO();
        mqDto.setUserTrackDTO(trackDTO);
        String json = JsonUtils.toStr(mqDto);
        MafkaMessage<String> message = new MafkaMessage<>("topic", 1, 1L, "key", json);
        MessagetContext context = new MessagetContext();
        when(userFootPrintRecordDOMapper.countByExample(any())).thenReturn(0L);
        doThrow(new RuntimeException("Producer error")).when(dispatchProducer).sendDelayMessage(any(), anyLong());
        // act
        ConsumeStatus result = invokeRecvMessage(message, context);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }
}
