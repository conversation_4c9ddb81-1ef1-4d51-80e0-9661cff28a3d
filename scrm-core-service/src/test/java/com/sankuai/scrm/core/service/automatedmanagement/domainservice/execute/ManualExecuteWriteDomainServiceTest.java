package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.alibaba.fastjson.JSON;
import com.dianping.education.lab.base.api.EduDaxiangSendService;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationActionTypeEnum;
import com.sankuai.scrm.core.service.BaseMockTest;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.executelog.ScrmProcessOrchestrationNodeExecuteLogConverter;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration.ScrmProcessOrchestrationExecutePlanConverter;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationExecutePlanDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.CrowdFriendTouchAction;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.StaffMomentTouchAction;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.StaffTaskAssignAction;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.config.ConfigDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack.CrowdPackWriteDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.mq.producer.RefinementOperationExecuteMessageProducer;
import com.sankuai.scrm.core.service.automatedmanagement.utils.ConditionUtils;
import com.sankuai.scrm.core.service.infrastructure.dal.babymapper.ContactUserDoMapper;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.member.dao.mapper.DepartmentMemberInfoMapper;
import com.sankuai.scrm.core.service.user.dal.mapper.ext.ExtScrmUserTagMapper;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;

import java.io.BufferedReader;
import java.io.InputStream;
import java.io.InputStreamReader;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

import static org.junit.Assert.assertNotNull;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

/**
 * <AUTHOR> wangyonghao02
 * @version : 0.1
 * @since : 2024/9/11
 */
public class ManualExecuteWriteDomainServiceTest extends BaseMockTest {

    @InjectMocks
    private ExecuteWriteDomainService executeWriteDomainService;

    @Mock(lenient = true)
    private ScrmAmCrowdPackAndProcessMapDOMapper crowdPackAndProcessMapDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationNodeExecuteLogDOMapper nodeExecuteLogDOMapper;

    @Mock(lenient = true)
    private ExtScrmAmProcessOrchestrationExecutePlanDOMapper executePlanDOMapper;

    @Mock(lenient = true)
    private DepartmentMemberInfoMapper departmentMemberInfoMapper;

    @Mock(lenient = true)
    private ExtScrmUserTagMapper userTagDOMapper;

    @Mock(lenient = true)
    private ContactUserDoMapper contactUserDoMapper;

    @Mock(lenient = true)
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @Mock(lenient = true)
    private CrowdPackWriteDomainService crowdPackWriteDomainService;

    @Mock(lenient = true)
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    @Mock(lenient = true)
    private ConfigDomainService configDomainService;

    @Mock(lenient = true)
    private ExecuteManagementService executeManagementService;

    @Mock(lenient = true)
    private RefinementOperationExecuteMessageProducer refinementOperationExecuteMessageProducer;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationExecutePlanConverter scrmProcessOrchestrationExecutePlanConverter;

    @Mock(lenient = true)
    private ScrmProcessOrchestrationNodeExecuteLogConverter scrmProcessOrchestrationNodeExecuteLogConverter;

    @Mock(lenient = true)
    private StaffTaskAssignAction staffTaskAssignAction;

    @Mock(lenient = true)
    private CrowdFriendTouchAction crowdFriendTouchAction;

    @Mock(lenient = true)
    private StaffMomentTouchAction staffMomentTouchAction;

    @Mock(lenient = true)
    private ConditionUtils conditionUtils;

    @Mock(lenient = true)
    private EduDaxiangSendService eduDaxiangSendService;

    @Mock(lenient = true)
    private CorpAppConfigRepository appConfigRepository;

    @Mock(lenient = true)
    private InformationGatheringService informationGatheringService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationBranchExeLogDOMapper branchExeLogDOMapper;

    @Before
    public void setUp() {
        /*ReflectionTestUtils.setField(executeWriteDomainService, "executePlanDOMapper", executePlanDOMapper);
        ReflectionTestUtils.setField(executeWriteDomainService, "executeLogDOMapper", executeLogDOMapper);
        ReflectionTestUtils.setField(executeWriteDomainService, "nodeExecuteLogDOMapper", nodeExecuteLogDOMapper);
        ReflectionTestUtils.setField(executeWriteDomainService, "executePlanDOMapper", executePlanDOMapper);
        ReflectionTestUtils.setField(executeWriteDomainService, "crowdPackAndProcessMapDOMapper", crowdPackAndProcessMapDOMapper);
        ReflectionTestUtils.setField(executeWriteDomainService, "scrmAmProcessOrchestrationInfoDOMapper", scrmAmProcessOrchestrationInfoDOMapper);
        ReflectionTestUtils.setField(executeWriteDomainService, "processOrchestrationReadDomainService", processOrchestrationReadDomainService);
        ReflectionTestUtils.setField(executeWriteDomainService, "executeManagementService", executeManagementService);
        ReflectionTestUtils.setField(executeWriteDomainService, "conditionUtils", conditionUtils);
        ReflectionTestUtils.setField(executeWriteDomainService, "eduDaxiangSendService", eduDaxiangSendService);
        ReflectionTestUtils.setField(executeWriteDomainService, "appConfigRepository", appConfigRepository);
        ReflectionTestUtils.setField(executeWriteDomainService, "informationGatheringService", informationGatheringService);
        ReflectionTestUtils.setField(executeWriteDomainService, "scrmProcessOrchestrationExecutePlanConverter", scrmProcessOrchestrationExecutePlanConverter);
        ReflectionTestUtils.setField(executeWriteDomainService, "scrmProcessOrchestrationNodeExecuteLogConverter", scrmProcessOrchestrationNodeExecuteLogConverter);
        ReflectionTestUtils.setField(executeWriteDomainService, "staffTaskAssignAction", staffTaskAssignAction);
        ReflectionTestUtils.setField(executeWriteDomainService, "crowdFriendTouchAction", crowdFriendTouchAction);
        ReflectionTestUtils.setField(executeWriteDomainService, "staffMomentTouchAction", staffMomentTouchAction);
        ReflectionTestUtils.setField(executeWriteDomainService, "refinementOperationExecuteMessageProducer", refinementOperationExecuteMessageProducer);
        ReflectionTestUtils.setField(executeWriteDomainService, "crowdPackWriteDomainService", crowdPackWriteDomainService);
        ReflectionTestUtils.setField(executeWriteDomainService, "crowdPackReadDomainService", crowdPackReadDomainService);
        ReflectionTestUtils.setField(executeWriteDomainService, "configDomainService", configDomainService);
        ReflectionTestUtils.setField(executeWriteDomainService, "departmentMemberInfoMapper", departmentMemberInfoMapper);
        ReflectionTestUtils.setField(executeWriteDomainService, "userTagDOMapper", userTagDOMapper);
        ReflectionTestUtils.setField(executeWriteDomainService, "contactUserDoMapper", contactUserDoMapper);*/
    }

    @Test
    public void testDealProcessOrchestrationDTO() {
        ClassLoader classLoader = getClass().getClassLoader();
        InputStream nonSupplyProcessOrchestrationDTOInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/NonSupplyProcessOrchestrationDTO");
        String nonSupplyProcessOrchestrationDTOInputStreamContent = new BufferedReader(new InputStreamReader(nonSupplyProcessOrchestrationDTOInputStream))
                .lines().collect(Collectors.joining("\n"));
        ScrmProcessOrchestrationDTO nonSupplyProcessOrchestrationDTO = JsonUtils.toObject(nonSupplyProcessOrchestrationDTOInputStreamContent, ScrmProcessOrchestrationDTO.class);

        InputStream executeManagementDTOInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/NonSupplyProcessOrchestrationExecuteManagementDTO");
        String executeManagementDTOInputStreamContent = new BufferedReader(new InputStreamReader(executeManagementDTOInputStream))
                .lines().collect(Collectors.joining("\n"));
        ExecuteManagementDTO executeManagementDTO = JsonUtils.toObject(executeManagementDTOInputStreamContent, ExecuteManagementDTO.class);

        InputStream relatedCrowdPacksInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/RelatedCrowdPacks");
        String relatedCrowdPacksInputStreamContent = new BufferedReader(new InputStreamReader(relatedCrowdPacksInputStream))
                .lines().collect(Collectors.joining("\n"));
        List<ScrmCrowdPackDTO> relatedCrowdPacks = JsonUtils.toList(relatedCrowdPacksInputStreamContent, ScrmCrowdPackDTO.class);

        InputStream crowdPackDetailInfoDTOListInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/ScrmCrowdPackDetailInfoDTOList");
        String crowdPackDetailInfoDTOListInputStreamContent = new BufferedReader(new InputStreamReader(crowdPackDetailInfoDTOListInputStream))
                .lines().collect(Collectors.joining("\n"));
        List<ScrmCrowdPackDetailInfoDTO> crowdPackDetailInfoDTOList = JsonUtils.toList(crowdPackDetailInfoDTOListInputStreamContent, ScrmCrowdPackDetailInfoDTO.class);

        when(executeManagementService.getExecuteMediumManagementDTO(any())).thenReturn(executeManagementDTO);
        when(executeManagementService.isTaskRunning(any(), any())).thenReturn(false);
        ExecuteWriteDomainService executeWriteDomainServiceSpy = spy(executeWriteDomainService);
        when(crowdFriendTouchAction.dealCrowdGroupTouchAction(any(), any(), any(), any(), any())).thenReturn(null);
        when(staffMomentTouchAction.dealStaffMomentAction(any(), any(), any(), any(), any())).thenReturn(null);
        when(crowdFriendTouchAction.dealWxGroupTouch(any(), any(), any(), any(), any())).thenReturn(null);
        doNothing().when(executeWriteDomainServiceSpy).firstDealStep(any(), any(), any(), any(), any(), any());
        doNothing().when(executeWriteDomainServiceSpy).secondStaffTaskAssignStep(any(), any(), any(), any());
        doNothing().when(executeWriteDomainServiceSpy).thirdWxGroupTouchStep(any(), any(), any());
        doNothing().when(executeWriteDomainServiceSpy).noticeStep(any(), any());
        doNothing().when(executeWriteDomainServiceSpy).updateNodeExecuteLog(any(), any());

        executeWriteDomainServiceSpy.dealProcessOrchestrationDTO(nonSupplyProcessOrchestrationDTO,
                nonSupplyProcessOrchestrationDTO.getNodeMediumDTO().getProcessOrchestrationNodeDTOList().stream().collect(Collectors.toMap(ScrmProcessOrchestrationNodeDTO::getNodeId, Function.identity())),
                relatedCrowdPacks);
        verify(executeManagementService,times(1)).getExecuteMediumManagementDTO(any());
    }


    @Test
    public void testFirstDealStepRelatedCrowdPacks() {
        ClassLoader classLoader = getClass().getClassLoader();
        InputStream nonSupplyProcessOrchestrationDTOInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/NonSupplyProcessOrchestrationDTO");
        String nonSupplyProcessOrchestrationDTOInputStreamContent = new BufferedReader(new InputStreamReader(nonSupplyProcessOrchestrationDTOInputStream))
                .lines().collect(Collectors.joining("\n"));
        ScrmProcessOrchestrationDTO nonSupplyProcessOrchestrationDTO = JsonUtils.toObject(nonSupplyProcessOrchestrationDTOInputStreamContent, ScrmProcessOrchestrationDTO.class);

        InputStream executeManagementDTOInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/NonSupplyProcessOrchestrationExecuteManagementDTO");
        String executeManagementDTOInputStreamContent = new BufferedReader(new InputStreamReader(executeManagementDTOInputStream))
                .lines().collect(Collectors.joining("\n"));
        ExecuteManagementDTO executeManagementDTO = JsonUtils.toObject(executeManagementDTOInputStreamContent, ExecuteManagementDTO.class);

        InputStream relatedCrowdPacksInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/RelatedCrowdPacks");
        String relatedCrowdPacksInputStreamContent = new BufferedReader(new InputStreamReader(relatedCrowdPacksInputStream))
                .lines().collect(Collectors.joining("\n"));
        List<ScrmCrowdPackDTO> relatedCrowdPacks = JsonUtils.toList(relatedCrowdPacksInputStreamContent, ScrmCrowdPackDTO.class);

        InputStream crowdPackDetailInfoDTOListInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/ScrmCrowdPackDetailInfoDTOList");
        String crowdPackDetailInfoDTOListInputStreamContent = new BufferedReader(new InputStreamReader(crowdPackDetailInfoDTOListInputStream))
                .lines().collect(Collectors.joining("\n"));
        List<ScrmCrowdPackDetailInfoDTO> crowdPackDetailInfoDTOList = JsonUtils.toList(crowdPackDetailInfoDTOListInputStreamContent, ScrmCrowdPackDetailInfoDTO.class);

        Map<Long, ScrmProcessOrchestrationNodeDTO> nodeDTOMap = nonSupplyProcessOrchestrationDTO.getNodeMediumDTO().getProcessOrchestrationNodeDTOList().stream().collect(Collectors.toMap(ScrmProcessOrchestrationNodeDTO::getNodeId, Function.identity()));
        // ExecuteWriteDomainService executeWriteDomainServiceSpy = spy(executeWriteDomainService);


        when(crowdPackReadDomainService.queryUserListInCrowdPack(any(), any(), any(), any(), any())).thenReturn(crowdPackDetailInfoDTOList);
        doNothing().when(executeManagementService).subTaskRunBegin(any(), any());
        // when(executeWriteDomainServiceSpy.processNodes(nonSupplyProcessOrchestrationDTO,executeManagementDTO, nodeDTOMap, nodeDTOMap.get(0).getChildrenNodes(), crowdPackDetailInfoDTOList.get(0), null)).thenReturn(null);
        when(crowdFriendTouchAction.dealCrowdGroupTouchAction(any(), any(), any(), any(), any())).thenReturn(null);
        when(staffMomentTouchAction.dealStaffMomentAction(any(), any(), any(), any(), any())).thenReturn(null);
        when(crowdFriendTouchAction.dealWxGroupTouch(any(), any(), any(), any(), any())).thenReturn(null);
        doNothing().when(executeManagementService).taskRunFinished(any(), any());

        executeWriteDomainService.firstDealStep(nonSupplyProcessOrchestrationDTO,
                nodeDTOMap,
                relatedCrowdPacks,
                nodeDTOMap.get(0L),
                nonSupplyProcessOrchestrationDTO,
                executeManagementDTO);

        assertNotNull(nonSupplyProcessOrchestrationDTO);
    }

    @Test
    public void testFirstDealStepAllCrows() {
        ClassLoader classLoader = getClass().getClassLoader();
        InputStream nonSupplyProcessOrchestrationDTOInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/NonSupplyProcessOrchestrationDTO");
        String nonSupplyProcessOrchestrationDTOInputStreamContent = new BufferedReader(new InputStreamReader(nonSupplyProcessOrchestrationDTOInputStream))
                .lines().collect(Collectors.joining("\n"));
        ScrmProcessOrchestrationDTO nonSupplyProcessOrchestrationDTO = JsonUtils.toObjectSafe(nonSupplyProcessOrchestrationDTOInputStreamContent, ScrmProcessOrchestrationDTO.class);

        InputStream executeManagementDTOInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/NonSupplyProcessOrchestrationExecuteManagementDTO");
        String executeManagementDTOInputStreamContent = new BufferedReader(new InputStreamReader(executeManagementDTOInputStream))
                .lines().collect(Collectors.joining("\n"));
        ExecuteManagementDTO executeManagementDTO = JsonUtils.toObjectSafe(executeManagementDTOInputStreamContent, ExecuteManagementDTO.class);

        InputStream relatedCrowdPacksInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/RelatedCrowdPacks");
        String relatedCrowdPacksInputStreamContent = new BufferedReader(new InputStreamReader(relatedCrowdPacksInputStream))
                .lines().collect(Collectors.joining("\n"));
        List<ScrmCrowdPackDTO> relatedCrowdPacks = JsonUtils.toListSafe(relatedCrowdPacksInputStreamContent, ScrmCrowdPackDTO.class);

        InputStream crowdPackDetailInfoDTOListInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/ScrmCrowdPackDetailInfoDTOList");
        String crowdPackDetailInfoDTOListInputStreamContent = new BufferedReader(new InputStreamReader(crowdPackDetailInfoDTOListInputStream))
                .lines().collect(Collectors.joining("\n"));
        List<ScrmCrowdPackDetailInfoDTO> crowdPackDetailInfoDTOList = JsonUtils.toListSafe(crowdPackDetailInfoDTOListInputStreamContent, ScrmCrowdPackDetailInfoDTO.class);

        Map<Long, ScrmProcessOrchestrationNodeDTO> nodeDTOMap = nonSupplyProcessOrchestrationDTO.getNodeMediumDTO().getProcessOrchestrationNodeDTOList().stream().collect(Collectors.toMap(ScrmProcessOrchestrationNodeDTO::getNodeId, Function.identity()));
        // ExecuteWriteDomainService executeWriteDomainServiceSpy = spy(executeWriteDomainService);

        nonSupplyProcessOrchestrationDTO.setCrowdPackType(0);
        when(crowdPackReadDomainService.queryUserListInCrowdPack(any(), any(), any(), any(), any())).thenReturn(crowdPackDetailInfoDTOList);
        doNothing().when(executeManagementService).subTaskRunBegin(any(), any());
        // when(executeWriteDomainServiceSpy.processNodes(nonSupplyProcessOrchestrationDTO,executeManagementDTO, nodeDTOMap, nodeDTOMap.get(0).getChildrenNodes(), crowdPackDetailInfoDTOList.get(0), null)).thenReturn(null);
        when(crowdFriendTouchAction.dealCrowdGroupTouchAction(any(), any(), any(), any(), any())).thenReturn(null);
        when(staffMomentTouchAction.dealStaffMomentAction(any(), any(), any(), any(), any())).thenReturn(null);
        when(crowdFriendTouchAction.dealWxGroupTouch(any(), any(), any(), any(), any())).thenReturn(null);
        doNothing().when(executeManagementService).taskRunFinished(any(), any());


        executeWriteDomainService.firstDealStep(nonSupplyProcessOrchestrationDTO,
                nodeDTOMap,
                relatedCrowdPacks,
                nodeDTOMap.get(0L),
                nonSupplyProcessOrchestrationDTO,
                executeManagementDTO);

        assertNotNull(nonSupplyProcessOrchestrationDTO);
    }

    @Test
    public void testFirstDealStepGroupCrowds() {
        ClassLoader classLoader = getClass().getClassLoader();
        InputStream nonSupplyProcessOrchestrationDTOInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/NonSupplyProcessOrchestrationDTO");
        String nonSupplyProcessOrchestrationDTOInputStreamContent = new BufferedReader(new InputStreamReader(nonSupplyProcessOrchestrationDTOInputStream))
                .lines().collect(Collectors.joining("\n"));
        ScrmProcessOrchestrationDTO nonSupplyProcessOrchestrationDTO = JsonUtils.toObjectSafe(nonSupplyProcessOrchestrationDTOInputStreamContent, ScrmProcessOrchestrationDTO.class);

        InputStream executeManagementDTOInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/NonSupplyProcessOrchestrationExecuteManagementDTO");
        String executeManagementDTOInputStreamContent = new BufferedReader(new InputStreamReader(executeManagementDTOInputStream))
                .lines().collect(Collectors.joining("\n"));
        ExecuteManagementDTO executeManagementDTO = JsonUtils.toObjectSafe(executeManagementDTOInputStreamContent, ExecuteManagementDTO.class);

        InputStream relatedCrowdPacksInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/RelatedCrowdPacks");
        String relatedCrowdPacksInputStreamContent = new BufferedReader(new InputStreamReader(relatedCrowdPacksInputStream))
                .lines().collect(Collectors.joining("\n"));
        List<ScrmCrowdPackDTO> relatedCrowdPacks = JsonUtils.toListSafe(relatedCrowdPacksInputStreamContent, ScrmCrowdPackDTO.class);

        InputStream crowdPackDetailInfoDTOListInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/ScrmCrowdPackDetailInfoDTOList");
        String crowdPackDetailInfoDTOListInputStreamContent = new BufferedReader(new InputStreamReader(crowdPackDetailInfoDTOListInputStream))
                .lines().collect(Collectors.joining("\n"));
        List<ScrmCrowdPackDetailInfoDTO> crowdPackDetailInfoDTOList = JsonUtils.toListSafe(crowdPackDetailInfoDTOListInputStreamContent, ScrmCrowdPackDetailInfoDTO.class);

        Map<Long, ScrmProcessOrchestrationNodeDTO> nodeDTOMap = nonSupplyProcessOrchestrationDTO.getNodeMediumDTO().getProcessOrchestrationNodeDTOList().stream().collect(Collectors.toMap(ScrmProcessOrchestrationNodeDTO::getNodeId, Function.identity()));
        // ExecuteWriteDomainService executeWriteDomainServiceSpy = spy(executeWriteDomainService);


        nonSupplyProcessOrchestrationDTO.setCrowdPackType(3);
        when(crowdPackReadDomainService.queryUserListByGroupId(any(),any())).thenReturn(crowdPackDetailInfoDTOList);
        doNothing().when(executeManagementService).subTaskRunBegin(any(), any());
        // when(executeWriteDomainServiceSpy.processNodes(nonSupplyProcessOrchestrationDTO,executeManagementDTO, nodeDTOMap, nodeDTOMap.get(0).getChildrenNodes(), crowdPackDetailInfoDTOList.get(0), null)).thenReturn(null);
        when(crowdFriendTouchAction.dealCrowdGroupTouchAction(any(), any(), any(), any(), any())).thenReturn(null);
        when(staffMomentTouchAction.dealStaffMomentAction(any(), any(), any(), any(), any())).thenReturn(null);
        when(crowdFriendTouchAction.dealWxGroupTouch(any(), any(), any(), any(), any())).thenReturn(null);
        doNothing().when(executeManagementService).taskRunFinished(any(), any());

        executeWriteDomainService.firstDealStep(nonSupplyProcessOrchestrationDTO,
                nodeDTOMap,
                new ArrayList<>(),
                nodeDTOMap.get(0L),
                nonSupplyProcessOrchestrationDTO,
                executeManagementDTO);
        assertNotNull(nonSupplyProcessOrchestrationDTO);
    }

    @Test
    public void testProcessNodes1() {
        ClassLoader classLoader = getClass().getClassLoader();
        InputStream nonSupplyProcessOrchestrationDTOInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/NonSupplyProcessOrchestrationDTO");
        String nonSupplyProcessOrchestrationDTOInputStreamContent = new BufferedReader(new InputStreamReader(nonSupplyProcessOrchestrationDTOInputStream))
                .lines().collect(Collectors.joining("\n"));
        ScrmProcessOrchestrationDTO nonSupplyProcessOrchestrationDTO = JsonUtils.toObjectSafe(nonSupplyProcessOrchestrationDTOInputStreamContent, ScrmProcessOrchestrationDTO.class);

        InputStream executeManagementDTOInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/NonSupplyProcessOrchestrationExecuteManagementDTO");
        String executeManagementDTOInputStreamContent = new BufferedReader(new InputStreamReader(executeManagementDTOInputStream))
                .lines().collect(Collectors.joining("\n"));
        ExecuteManagementDTO executeManagementDTO = JsonUtils.toObjectSafe(executeManagementDTOInputStreamContent, ExecuteManagementDTO.class);

        InputStream relatedCrowdPacksInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/RelatedCrowdPacks");
        String relatedCrowdPacksInputStreamContent = new BufferedReader(new InputStreamReader(relatedCrowdPacksInputStream))
                .lines().collect(Collectors.joining("\n"));
        List<ScrmCrowdPackDTO> relatedCrowdPacks = JsonUtils.toListSafe(relatedCrowdPacksInputStreamContent, ScrmCrowdPackDTO.class);

        InputStream crowdPackDetailInfoDTOListInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/ScrmCrowdPackDetailInfoDTOList");
        String crowdPackDetailInfoDTOListInputStreamContent = new BufferedReader(new InputStreamReader(crowdPackDetailInfoDTOListInputStream))
                .lines().collect(Collectors.joining("\n"));
        List<ScrmCrowdPackDetailInfoDTO> crowdPackDetailInfoDTOList = JsonUtils.toListSafe(crowdPackDetailInfoDTOListInputStreamContent, ScrmCrowdPackDetailInfoDTO.class);

        Map<Long, ScrmProcessOrchestrationNodeDTO> nodeDTOMap = nonSupplyProcessOrchestrationDTO.getNodeMediumDTO().getProcessOrchestrationNodeDTOList().stream().collect(Collectors.toMap(ScrmProcessOrchestrationNodeDTO::getNodeId, Function.identity()));
        ExecuteWriteDomainService executeWriteDomainServiceSpy = spy(executeWriteDomainService);
        ScrmProcessOrchestrationActionDTO actionDetailDTO = nonSupplyProcessOrchestrationDTO.getNodeMediumDTO().getActionMap()
                .get(nodeDTOMap.get(0L).getChildrenNodes().get(0));

        when(crowdFriendTouchAction.dealCrowdGroupTouchAction(any(), any(), any(), any(), any())).thenReturn(null);
        when(staffMomentTouchAction.dealStaffMomentAction(any(), any(), any(), any(), any())).thenReturn(null);
        when(crowdFriendTouchAction.dealWxGroupTouch(any(), any(), any(), any(), any())).thenReturn(null);

        actionDetailDTO.setActionType(ScrmProcessOrchestrationActionTypeEnum.CROWD_GROUP_TOUCH.getValue().byteValue());
        executeWriteDomainServiceSpy.processNodes(nonSupplyProcessOrchestrationDTO,executeManagementDTO, nodeDTOMap, nodeDTOMap.get(0L).getChildrenNodes(), crowdPackDetailInfoDTOList.get(0), null);
        assertNotNull(nonSupplyProcessOrchestrationDTO);
    }

    @Test
    public void testProcessNodes2() {
        ClassLoader classLoader = getClass().getClassLoader();
        InputStream nonSupplyProcessOrchestrationDTOInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/NonSupplyProcessOrchestrationDTO");
        String nonSupplyProcessOrchestrationDTOInputStreamContent = new BufferedReader(new InputStreamReader(nonSupplyProcessOrchestrationDTOInputStream))
                .lines().collect(Collectors.joining("\n"));
        ScrmProcessOrchestrationDTO nonSupplyProcessOrchestrationDTO = JsonUtils.toObject(nonSupplyProcessOrchestrationDTOInputStreamContent, ScrmProcessOrchestrationDTO.class);

        InputStream executeManagementDTOInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/NonSupplyProcessOrchestrationExecuteManagementDTO");
        String executeManagementDTOInputStreamContent = new BufferedReader(new InputStreamReader(executeManagementDTOInputStream))
                .lines().collect(Collectors.joining("\n"));
        ExecuteManagementDTO executeManagementDTO = JsonUtils.toObject(executeManagementDTOInputStreamContent, ExecuteManagementDTO.class);

        InputStream relatedCrowdPacksInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/RelatedCrowdPacks");
        String relatedCrowdPacksInputStreamContent = new BufferedReader(new InputStreamReader(relatedCrowdPacksInputStream))
                .lines().collect(Collectors.joining("\n"));
        List<ScrmCrowdPackDTO> relatedCrowdPacks = JsonUtils.toList(relatedCrowdPacksInputStreamContent, ScrmCrowdPackDTO.class);

        InputStream crowdPackDetailInfoDTOListInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/ScrmCrowdPackDetailInfoDTOList");
        String crowdPackDetailInfoDTOListInputStreamContent = new BufferedReader(new InputStreamReader(crowdPackDetailInfoDTOListInputStream))
                .lines().collect(Collectors.joining("\n"));
        List<ScrmCrowdPackDetailInfoDTO> crowdPackDetailInfoDTOList = JsonUtils.toList(crowdPackDetailInfoDTOListInputStreamContent, ScrmCrowdPackDetailInfoDTO.class);

        Map<Long, ScrmProcessOrchestrationNodeDTO> nodeDTOMap = nonSupplyProcessOrchestrationDTO.getNodeMediumDTO().getProcessOrchestrationNodeDTOList().stream().collect(Collectors.toMap(ScrmProcessOrchestrationNodeDTO::getNodeId, Function.identity()));
        ExecuteWriteDomainService executeWriteDomainServiceSpy = spy(executeWriteDomainService);
        ScrmProcessOrchestrationActionDTO actionDetailDTO = nonSupplyProcessOrchestrationDTO.getNodeMediumDTO().getActionMap()
                .get(nodeDTOMap.get(0L).getChildrenNodes().get(0));

        when(crowdFriendTouchAction.dealCrowdGroupTouchAction(any(), any(), any(), any(), any())).thenReturn(null);
        when(staffMomentTouchAction.dealStaffMomentAction(any(), any(), any(), any(), any())).thenReturn(null);
        when(crowdFriendTouchAction.dealWxGroupTouch(any(), any(), any(), any(), any())).thenReturn(null);

        actionDetailDTO.setActionType(ScrmProcessOrchestrationActionTypeEnum.STAFF_FRIEND_CIRCLE_TOUCH.getValue().byteValue());
        executeWriteDomainServiceSpy.processNodes(nonSupplyProcessOrchestrationDTO,executeManagementDTO, nodeDTOMap, nodeDTOMap.get(0L).getChildrenNodes(), crowdPackDetailInfoDTOList.get(0), null);
        assertNotNull(nonSupplyProcessOrchestrationDTO);
    }

    @Test
    public void testProcessNodes3() {
        ClassLoader classLoader = getClass().getClassLoader();
        InputStream nonSupplyProcessOrchestrationDTOInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/NonSupplyProcessOrchestrationDTO");
        String nonSupplyProcessOrchestrationDTOInputStreamContent = new BufferedReader(new InputStreamReader(nonSupplyProcessOrchestrationDTOInputStream))
                .lines().collect(Collectors.joining("\n"));
        ScrmProcessOrchestrationDTO nonSupplyProcessOrchestrationDTO = JsonUtils.toObject(nonSupplyProcessOrchestrationDTOInputStreamContent, ScrmProcessOrchestrationDTO.class);

        InputStream executeManagementDTOInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/NonSupplyProcessOrchestrationExecuteManagementDTO");
        String executeManagementDTOInputStreamContent = new BufferedReader(new InputStreamReader(executeManagementDTOInputStream))
                .lines().collect(Collectors.joining("\n"));
        ExecuteManagementDTO executeManagementDTO = JsonUtils.toObject(executeManagementDTOInputStreamContent, ExecuteManagementDTO.class);

        InputStream relatedCrowdPacksInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/RelatedCrowdPacks");
        String relatedCrowdPacksInputStreamContent = new BufferedReader(new InputStreamReader(relatedCrowdPacksInputStream))
                .lines().collect(Collectors.joining("\n"));
        List<ScrmCrowdPackDTO> relatedCrowdPacks = JsonUtils.toList(relatedCrowdPacksInputStreamContent, ScrmCrowdPackDTO.class);

        InputStream crowdPackDetailInfoDTOListInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/ScrmCrowdPackDetailInfoDTOList");
        String crowdPackDetailInfoDTOListInputStreamContent = new BufferedReader(new InputStreamReader(crowdPackDetailInfoDTOListInputStream))
                .lines().collect(Collectors.joining("\n"));
        List<ScrmCrowdPackDetailInfoDTO> crowdPackDetailInfoDTOList = JsonUtils.toList(crowdPackDetailInfoDTOListInputStreamContent, ScrmCrowdPackDetailInfoDTO.class);

        Map<Long, ScrmProcessOrchestrationNodeDTO> nodeDTOMap = nonSupplyProcessOrchestrationDTO.getNodeMediumDTO().getProcessOrchestrationNodeDTOList().stream().collect(Collectors.toMap(ScrmProcessOrchestrationNodeDTO::getNodeId, Function.identity()));
        ExecuteWriteDomainService executeWriteDomainServiceSpy = spy(executeWriteDomainService);
        ScrmProcessOrchestrationActionDTO actionDetailDTO = nonSupplyProcessOrchestrationDTO.getNodeMediumDTO().getActionMap()
                .get(nodeDTOMap.get(0L).getChildrenNodes().get(0));

        when(crowdFriendTouchAction.dealCrowdGroupTouchAction(any(), any(), any(), any(), any())).thenReturn(null);
        when(staffMomentTouchAction.dealStaffMomentAction(any(), any(), any(), any(), any())).thenReturn(null);
        when(crowdFriendTouchAction.dealWxGroupTouch(any(), any(), any(), any(), any())).thenReturn(null);

        actionDetailDTO.setActionType(ScrmProcessOrchestrationActionTypeEnum.CROWD_GROUP_TOUCH_IN_GROUP.getValue().byteValue());
        executeWriteDomainServiceSpy.processNodes(nonSupplyProcessOrchestrationDTO,executeManagementDTO, nodeDTOMap, nodeDTOMap.get(0L).getChildrenNodes(), crowdPackDetailInfoDTOList.get(0), null);
        assertNotNull(nonSupplyProcessOrchestrationDTO);
    }

    /**
     * 测试分流分支执行
     */
    @Test
    public void testProcessNodesSplitBranch() {
        ClassLoader classLoader = getClass().getClassLoader();
        InputStream nonSupplyProcessOrchestrationDTOInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/TestSplitAndDelayBranchProcessOrchestrationDTO");
        String nonSupplyProcessOrchestrationDTOInputStreamContent = new BufferedReader(new InputStreamReader(nonSupplyProcessOrchestrationDTOInputStream))
                .lines().collect(Collectors.joining("\n"));
        ScrmProcessOrchestrationDTO processOrchestrationDTO = JSON.parseObject(nonSupplyProcessOrchestrationDTOInputStreamContent, ScrmProcessOrchestrationDTO.class);

        InputStream executeManagementDTOInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/TestSplitAndDelayBranchExecuteManagementDTO");
        String executeManagementDTOInputStreamContent = new BufferedReader(new InputStreamReader(executeManagementDTOInputStream))
                .lines().collect(Collectors.joining("\n"));
        ExecuteManagementDTO executeManagementDTO = JSON.parseObject(executeManagementDTOInputStreamContent, ExecuteManagementDTO.class);

        InputStream crowdPackDetailInfoDTOListInputStream = classLoader.getResourceAsStream("test/files/automatedmanagement/domainservice/handler/TestSplitAndDelayBranchCrowdPackDetailInfoDTO");
        String crowdPackDetailInfoDTOListInputStreamContent = new BufferedReader(new InputStreamReader(crowdPackDetailInfoDTOListInputStream))
                .lines().collect(Collectors.joining("\n"));
        ScrmCrowdPackDetailInfoDTO crowdPackDetailInfoDTO = JSON.parseObject(crowdPackDetailInfoDTOListInputStreamContent, ScrmCrowdPackDetailInfoDTO.class);

        Map<Long, ScrmProcessOrchestrationNodeDTO> nodeDTOMap = processOrchestrationDTO.getNodeMediumDTO().getProcessOrchestrationNodeDTOList().stream().collect(Collectors.toMap(ScrmProcessOrchestrationNodeDTO::getNodeId, Function.identity()));
        ExecuteWriteDomainService executeWriteDomainServiceSpy = spy(executeWriteDomainService);

        when(branchExeLogDOMapper.insertSelective(any())).thenReturn(1);
        when(branchExeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);

        executeWriteDomainServiceSpy.processNodes(processOrchestrationDTO,executeManagementDTO, nodeDTOMap, nodeDTOMap.get(0L).getChildrenNodes(), crowdPackDetailInfoDTO, null);
        assertNotNull(processOrchestrationDTO);
    }

}