package com.sankuai.scrm.core.service.infrastructure.mq.consumer;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyBoolean;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.PersonalWxGroupInfoDomainService;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.PersonalWxGroupMemberInfoDomainService;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.dto.UpdateResultDTO;
import com.sankuai.scrm.core.service.infrastructure.mq.bo.corpwxgroupchange.OpenGroupChangeEvent;
import com.sankuai.scrm.core.service.util.JsonUtils;
import java.lang.reflect.Method;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class CorpWxGroupChangeConsumerRecvMessageTest {

    @Mock
    private PersonalWxGroupInfoDomainService personalWxGroupInfoDomainService;

    @Mock
    private PersonalWxGroupMemberInfoDomainService personalWxGroupMemberInfoDomainService;

    @Mock
    private MafkaMessage<String> message;

    @Mock
    private MessagetContext messagetContext;

    private Method recvMessageMethod;

    @InjectMocks
    private CorpWxGroupChangeConsumer consumerUnderTest;

    @BeforeEach
    public void setUp() throws Exception {
        recvMessageMethod = CorpWxGroupChangeConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        recvMessageMethod.setAccessible(true);
    }

    /**
     * Test normal case where message is successfully processed
     */
    @Test
    public void testRecvMessage_NormalCase() throws Throwable {
        // arrange
        OpenGroupChangeEvent event = new OpenGroupChangeEvent();
        when(message.getBody()).thenReturn(JsonUtils.toStr(event));
        UpdateResultDTO updateResultDTO = new UpdateResultDTO();
        updateResultDTO.setSuccess(true);
        when(personalWxGroupInfoDomainService.updateWxGroup(any())).thenReturn(updateResultDTO);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumerUnderTest, message, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(personalWxGroupInfoDomainService).updateWxGroup(any());
        verify(personalWxGroupMemberInfoDomainService).updateWxGroupMember(any(), anyBoolean());
    }

    /**
     * Test case where message body cannot be parsed
     */
    @Test
    public void testRecvMessage_ParseError() throws Throwable {
        // arrange
        when(message.getBody()).thenReturn("invalid json");
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumerUnderTest, message, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(personalWxGroupInfoDomainService, never()).updateWxGroup(any());
        verify(personalWxGroupMemberInfoDomainService, never()).updateWxGroupMember(any(), anyBoolean());
    }

    /**
     * Test case where dealEvent throws an exception
     */
    @Test
    public void testRecvMessage_DealEventException() throws Throwable {
        // arrange
        OpenGroupChangeEvent event = new OpenGroupChangeEvent();
        when(message.getBody()).thenReturn(JsonUtils.toStr(event));
        when(personalWxGroupInfoDomainService.updateWxGroup(any())).thenThrow(new RuntimeException("Test exception"));
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumerUnderTest, message, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(personalWxGroupInfoDomainService).updateWxGroup(any());
        verify(personalWxGroupMemberInfoDomainService, never()).updateWxGroupMember(any(), anyBoolean());
    }

    /**
     * Test case where updateWxGroup returns unsuccessful result
     */
    @Test
    public void testRecvMessage_UpdateWxGroupUnsuccessful() throws Throwable {
        // arrange
        OpenGroupChangeEvent event = new OpenGroupChangeEvent();
        when(message.getBody()).thenReturn(JsonUtils.toStr(event));
        UpdateResultDTO updateResultDTO = new UpdateResultDTO();
        updateResultDTO.setSuccess(false);
        when(personalWxGroupInfoDomainService.updateWxGroup(any())).thenReturn(updateResultDTO);
        // act
        ConsumeStatus result = (ConsumeStatus) recvMessageMethod.invoke(consumerUnderTest, message, messagetContext);
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(personalWxGroupInfoDomainService).updateWxGroup(any());
        verify(personalWxGroupMemberInfoDomainService, never()).updateWxGroupMember(any(), anyBoolean());
    }
}
