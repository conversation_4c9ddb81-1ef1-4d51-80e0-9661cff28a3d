package com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.processorchestration.ScrmProcessOrchestrationConverter;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmConfigurationChangeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackAndProcessMapDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmProcessOrchestrationExecutePlanDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.DdlResultDTO;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.scrm.core.service.util.model.CronModel;
import java.text.SimpleDateFormat;
import java.util.Calendar;
import java.util.Date;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;
import org.springframework.beans.factory.annotation.Autowired;

class ProcessOrchestrationWriteDomainServiceCreateProcessOrchestration1Test {

    @Mock
    private ScrmProcessOrchestrationConverter scrmProcessOrchestrationConverter;

    @Mock
    private ScrmAmProcessOrchestrationInfoDOMapper scrmAmProcessOrchestrationInfoDOMapper;

    @Mock
    private ScrmAmCrowdPackAndProcessMapDOMapper scrmAmCrowdPackAndProcessMapDOMapper;

    @Mock
    private ExtScrmAmProcessOrchestrationExecutePlanDOMapper extScrmAmProcessOrchestrationExecutePlanDOMapper;

    @Mock
    private ScrmAmConfigurationChangeLogDOMapper configurationChangeLogDOMapper;

    @InjectMocks
    @Autowired
    private ProcessOrchestrationWriteDomainService processOrchestrationWriteDomainService;

    private SimpleDateFormat sdf = new SimpleDateFormat("yyyy-MM-dd HH:mm:ss");

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }



    /**
     * Test when the end time is after the current date, expecting a success result.
     */
    @Test
    void testCreateProcessOrchestration_EndTimeAfterCurrent() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO dto = new ScrmProcessOrchestrationDTO();
        Calendar future = Calendar.getInstance();
        future.add(Calendar.DAY_OF_MONTH, 1);
        dto.setEndTime(future.getTime());
        // TIMED_PROCESS_ORCHESTRATION
        dto.setProcessOrchestrationType((byte) 4);
        dto.setCrowdPackType(0);
        Calendar nextRun = Calendar.getInstance();
        nextRun.add(Calendar.HOUR, 1);
        dto.setCronComment(sdf.format(nextRun.getTime()));
        ScrmAmProcessOrchestrationInfoDO infoDO = new ScrmAmProcessOrchestrationInfoDO();
        infoDO.setProcessOrchestrationType(dto.getProcessOrchestrationType());
        infoDO.setEndTime(dto.getEndTime());
        infoDO.setBeginTime(new Date());
        infoDO.setValidVersion("1.0");
        infoDO.setStatus((byte) 1);
        infoDO.setId(1L);
        infoDO.setCronComment(dto.getCronComment());
        infoDO.setCron("0 0 12 * * ?");
        when(scrmProcessOrchestrationConverter.convertToDO(any(ScrmProcessOrchestrationDTO.class))).thenReturn(infoDO);
        when(scrmAmProcessOrchestrationInfoDOMapper.insertSelective(any(ScrmAmProcessOrchestrationInfoDO.class))).thenReturn(1);
        when(scrmAmCrowdPackAndProcessMapDOMapper.batchInsert(any())).thenReturn(1);
        when(configurationChangeLogDOMapper.insert(any())).thenReturn(1);
        when(extScrmAmProcessOrchestrationExecutePlanDOMapper.insert(any())).thenReturn(1);
        // act
        DdlResultDTO result = processOrchestrationWriteDomainService.createProcessOrchestration(dto);
        // assert
        assertTrue(result.isSuccess());
        assertEquals("编排插入成功", result.getMsg());
        verify(extScrmAmProcessOrchestrationExecutePlanDOMapper, times(1)).insert(any());
    }
}
