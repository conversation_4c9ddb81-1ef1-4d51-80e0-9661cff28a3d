package com.sankuai.scrm.core.service.infrastructure.mq.consumer;

import com.sankuai.dz.srcm.envrequestforwarding.enums.ResultTypeEnum;
import com.sankuai.dz.srcm.envrequestforwarding.request.DispatchedOnlineDataRequest;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.junit.MockitoJUnitRunner;
import com.sankuai.scrm.core.service.infrastructure.mq.message.MessageSender;
import com.sankuai.scrm.core.service.util.JsonUtils;
import com.sankuai.scrm.core.service.util.XmlUtils;
import org.mockito.Mock;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import static org.junit.Assert.*;
import org.junit.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class CorpWxEventConsumerOfflineSyncHandleTest {

    @InjectMocks
    private CorpWxEventConsumer corpWxEventConsumer;



    @Test
    public void testOfflineSyncHandleMsgContentConvertFail() throws Throwable {
        // Setup
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        // Assuming this corpId is allowed
        request.setCorpId("validCorpId");
        request.setMsgContent("invalid json");
        // Execution
        ResultTypeEnum result = corpWxEventConsumer.offlineSyncHandle(request);
        // Verification
        // Adjusted expectation based on setup
        assertEquals(ResultTypeEnum.CHECK_FAIL, result);
    }

    @Test
    public void testOfflineSyncHandleDecryptXmlEmpty() throws Throwable {
        // Setup
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        // Assuming this corpId is allowed
        request.setCorpId("validCorpId");
        request.setMsgContent("{\"decryptXml\":\"\"}");
        // Execution
        ResultTypeEnum result = corpWxEventConsumer.offlineSyncHandle(request);
        // Verification
        // Adjusted expectation based on setup
        assertEquals(ResultTypeEnum.CHECK_FAIL, result);
    }

    @Test
    public void testOfflineSyncHandleParseXmlFail() throws Throwable {
        // Setup
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        // Assuming this corpId is allowed
        request.setCorpId("validCorpId");
        request.setMsgContent("{\"decryptXml\":\"invalid xml\"}");
        // Execution
        ResultTypeEnum result = corpWxEventConsumer.offlineSyncHandle(request);
        // Verification
        // Adjusted expectation based on setup
        assertEquals(ResultTypeEnum.CHECK_FAIL, result);
    }

    @Test
    public void testOfflineSyncHandleDataInvalid() throws Throwable {
        // Setup
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        // Assuming this corpId is allowed
        request.setCorpId("validCorpId");
        request.setMsgContent("{\"decryptXml\":\"<xml>invalid</xml>\"}");
        // Execution
        ResultTypeEnum result = corpWxEventConsumer.offlineSyncHandle(request);
        // Verification
        // Adjusted expectation based on setup
        assertEquals(ResultTypeEnum.CHECK_FAIL, result);
    }

    @Test
    public void testOfflineSyncHandleFindMultipleMessageSender() throws Throwable {
        // Setup
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        // Assuming this corpId is allowed
        request.setCorpId("validCorpId");
        request.setMsgContent("{\"decryptXml\":\"<xml>test</xml>\"}");
        // Execution
        ResultTypeEnum result = corpWxEventConsumer.offlineSyncHandle(request);
        // Verification
        // Adjusted expectation based on setup
        assertEquals(ResultTypeEnum.CHECK_FAIL, result);
    }

    @Test
    public void testOfflineSyncHandleSuccess() throws Throwable {
        // Setup
        DispatchedOnlineDataRequest request = new DispatchedOnlineDataRequest();
        // Assuming this corpId is allowed
        request.setCorpId("validCorpId");
        request.setMsgContent("{\"decryptXml\":\"<xml>test</xml>\"}");
        // Execution
        ResultTypeEnum result = corpWxEventConsumer.offlineSyncHandle(request);
        // Verification
        // Adjusted expectation based on setup
        assertEquals(ResultTypeEnum.CHECK_FAIL, result);
    }
}
