package com.sankuai.scrm.core.service.automatedmanagement.utils;

import com.sankuai.dz.srcm.automatedmanagement.dto.config.ScrmFilterFieldConfigDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.config.ScrmOperatorDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationConditionDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationExecutorDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmSupportedFilterFieldOperatorTypeEnum;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.config.ConfigDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.tag.dal.entity.ExternalContactTag;
import com.sankuai.scrm.core.service.tag.domain.ExternalContactTagDomainService;
import com.sankuai.scrm.core.service.user.dal.entity.ScrmUserTag;
import com.sankuai.scrm.core.service.user.enums.ScrmUserTagEnum;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.*;

import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class ConditionUtilsIsCrowdPackConditionMatchTest {

    @InjectMocks
    private ConditionUtils conditionUtils;

    @Mock
    private ConfigDomainService configDomainService;

    @Mock
    private OperateUtilsV2 operateUtils;

    @Mock
    private ExternalContactTagDomainService externalContactTagDomainService;

    @Mock
    private ExecuteManagementService executeManagementService;

    private ScrmUserTag createUserTag(String appId, Long tagId) {
        ScrmUserTag tag = new ScrmUserTag();
        tag.setAppId(appId);
        tag.setTagId(tagId);
        return tag;
    }

    private ScrmFilterFieldConfigDTO createFilterFieldConfig() {
        ScrmFilterFieldConfigDTO config = new ScrmFilterFieldConfigDTO();
        ScrmOperatorDTO operatorDTO = new ScrmOperatorDTO();
        operatorDTO.setOperatorName("equal");
        operatorDTO.setFieldType("java.lang.String");
        Map<Long, ScrmOperatorDTO> operatorMap = new HashMap<>();
        operatorMap.put(1L, operatorDTO);
        config.setOperatorDTOMap(operatorMap);
        return config;
    }

    private ExternalContactTag createExternalContactTag(String tagId) {
        ExternalContactTag tag = new ExternalContactTag();
        tag.setTagId(tagId);
        return tag;
    }

    /**
     * Test case for empty userTagDOMap
     */
    @Test
    public void testIsCrowdPackConditionMatch_EmptyUserTagDOMap() throws Throwable {
        // arrange
        ScrmCrowdPackDTO packDTO = new ScrmCrowdPackDTO();
        packDTO.setAppId("testApp");
        ScrmCrowdPackUpdateStrategyInfoDTO strategyInfoDTO = new ScrmCrowdPackUpdateStrategyInfoDTO();
        List<ScrmProcessOrchestrationExecutorDTO> staffList = new ArrayList<>();
        ScrmProcessOrchestrationExecutorDTO executorDTO = new ScrmProcessOrchestrationExecutorDTO();
        executorDTO.setExecutorId("testStaffId");
        // Department type
        executorDTO.setExecutorType(1);
        staffList.add(executorDTO);
        strategyInfoDTO.setCrowdPackUpdateStrategyStaff(staffList);
        packDTO.setScrmCrowdPackUpdateStrategyInfoDTO(strategyInfoDTO);
        List<ScrmUserTag> userTagDOS = new ArrayList<>();
        // Mock the dependencies
        when(configDomainService.getFilterFieldConfigsMapByAppId(anyString())).thenReturn(new HashMap<>());
        // Mock executeManagementService to return empty lists for both contact users and department users
        when(executeManagementService.getContactUserByExternalUnionId(anyString(), anyString())).thenReturn(Collections.emptyList());
        when(executeManagementService.queryStaffSetByDepartmentId(anyString(), any(ScrmProcessOrchestrationExecutorDTO.class))).thenReturn(Collections.emptySet());
        when(executeManagementService.getUsersGroupList(anyString(), anyString())).thenReturn(Collections.emptyList());
        // act
        boolean result = conditionUtils.isCrowdPackConditionMatch(packDTO, "testUnionId", strategyInfoDTO, userTagDOS);
        // assert
        assertFalse(result);
        // verify
        verify(executeManagementService).getContactUserByExternalUnionId(anyString(), anyString());
        verify(executeManagementService).queryStaffSetByDepartmentId(anyString(), any(ScrmProcessOrchestrationExecutorDTO.class));
    }

    // Additional test cases can be added here following the same pattern
    /**
     * Test case for CORP_TAG tag type
     */
    @Test
    public void testIsCrowdPackConditionMatch_CorpTag() throws Throwable {
        // arrange
        ScrmCrowdPackDTO packDTO = new ScrmCrowdPackDTO();
        packDTO.setAppId("testApp");
        ScrmCrowdPackUpdateStrategyInfoDTO strategyInfoDTO = new ScrmCrowdPackUpdateStrategyInfoDTO();
        strategyInfoDTO.setCrowdPackUpdateStrategyStaff(new ArrayList<>());
        packDTO.setScrmCrowdPackUpdateStrategyInfoDTO(strategyInfoDTO);
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyList = new ArrayList<>();
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO = new ScrmCrowdPackUpdateStrategyDetailDTO();
        detailDTO.setGroupId(1);
        detailDTO.setFilterFieldId(ScrmUserTagEnum.CORP_TAG.getTagId());
        detailDTO.setParam(Arrays.asList("tag1"));
        strategyList.add(detailDTO);
        strategyInfoDTO.setCrowdPackUpdateStrategy(strategyList);
        List<ScrmUserTag> userTagDOS = new ArrayList<>();
        ScrmUserTag userTag = new ScrmUserTag();
        userTag.setAppId("testApp");
        userTag.setTagId(ScrmUserTagEnum.CORP_TAG.getTagId());
        userTagDOS.add(userTag);
        Map<Long, ScrmFilterFieldConfigDTO> configMap = new HashMap<>();
        ScrmFilterFieldConfigDTO configDTO = new ScrmFilterFieldConfigDTO();
        Map<Long, ScrmOperatorDTO> operatorMap = new HashMap<>();
        ScrmOperatorDTO operatorDTO = new ScrmOperatorDTO();
        operatorDTO.setOperatorName(ScrmSupportedFilterFieldOperatorTypeEnum.EQUAL.getCode());
        operatorMap.put(1L, operatorDTO);
        configDTO.setOperatorDTOMap(operatorMap);
        configMap.put(ScrmUserTagEnum.CORP_TAG.getTagId(), configDTO);
        when(configDomainService.getFilterFieldConfigsMapByAppId(anyString())).thenReturn(configMap);
        // act
        boolean result = conditionUtils.isCrowdPackConditionMatch(packDTO, "testUnionId", strategyInfoDTO, userTagDOS);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for null userTagDO and not corp tag
     */
    @Test
    public void testIsCrowdPackConditionMatch_NullUserTagAndNotCorpTag() throws Throwable {
        // arrange
        ScrmCrowdPackDTO packDTO = new ScrmCrowdPackDTO();
        packDTO.setAppId("testApp");
        ScrmCrowdPackUpdateStrategyInfoDTO strategyInfoDTO = new ScrmCrowdPackUpdateStrategyInfoDTO();
        strategyInfoDTO.setCrowdPackUpdateStrategyStaff(new ArrayList<>());
        packDTO.setScrmCrowdPackUpdateStrategyInfoDTO(strategyInfoDTO);
        List<ScrmCrowdPackUpdateStrategyDetailDTO> strategyList = new ArrayList<>();
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO = new ScrmCrowdPackUpdateStrategyDetailDTO();
        detailDTO.setGroupId(1);
        detailDTO.setFilterFieldId(1L);
        strategyList.add(detailDTO);
        strategyInfoDTO.setCrowdPackUpdateStrategy(strategyList);
        List<ScrmUserTag> userTagDOS = new ArrayList<>();
        ScrmUserTag userTag = new ScrmUserTag();
        userTag.setAppId("testApp");
        userTag.setTagId(2L);
        userTagDOS.add(userTag);
        Map<Long, ScrmFilterFieldConfigDTO> configMap = new HashMap<>();
        ScrmFilterFieldConfigDTO configDTO = new ScrmFilterFieldConfigDTO();
        configDTO.setOperatorDTOMap(new HashMap<>());
        configMap.put(1L, configDTO);
        when(configDomainService.getFilterFieldConfigsMapByAppId(anyString())).thenReturn(configMap);
        // act
        boolean result = conditionUtils.isCrowdPackConditionMatch(packDTO, "testUnionId", strategyInfoDTO, userTagDOS);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for skipping when group result is false
     */
    @Test
    public void testIsConditionMatch_SkipWhenGroupResultFalse() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationConditionDetailDTO conditionDetailDTO = new ScrmProcessOrchestrationConditionDetailDTO();
        conditionDetailDTO.setGroupId(1);
        conditionDetailDTO.setFilterFieldId(1L);
        List<ScrmProcessOrchestrationConditionDetailDTO> conditionDetailDTOList = Arrays.asList(conditionDetailDTO);
        List<ScrmUserTag> userTagDOS = Collections.singletonList(createUserTag("testAppId", 1L));
        Map<Long, ScrmFilterFieldConfigDTO> filterFieldConfigDTOMap = new HashMap<>();
        filterFieldConfigDTOMap.put(1L, createFilterFieldConfig());
        when(configDomainService.getFilterFieldConfigsMapByAppId(anyString())).thenReturn(filterFieldConfigDTOMap);
        // act
        boolean result = conditionUtils.isConditionMatch(processOrchestrationDTO, conditionDetailDTOList, userTagDOS, null, null);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for skipping when filter field is STAFF_FRIENDS
     */
    @Test
    public void testIsConditionMatch_SkipWhenStaffFriends() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        ScrmProcessOrchestrationConditionDetailDTO conditionDetailDTO = new ScrmProcessOrchestrationConditionDetailDTO();
        conditionDetailDTO.setGroupId(1);
        conditionDetailDTO.setFilterFieldId(ScrmUserTagEnum.STAFF_FRIENDS.getTagId());
        List<ScrmProcessOrchestrationConditionDetailDTO> conditionDetailDTOList = Arrays.asList(conditionDetailDTO);
        List<ScrmUserTag> userTagDOS = Collections.singletonList(createUserTag("testAppId", ScrmUserTagEnum.STAFF_FRIENDS.getTagId()));
        Map<Long, ScrmFilterFieldConfigDTO> filterFieldConfigDTOMap = new HashMap<>();
        filterFieldConfigDTOMap.put(ScrmUserTagEnum.STAFF_FRIENDS.getTagId(), createFilterFieldConfig());
        when(configDomainService.getFilterFieldConfigsMapByAppId(anyString())).thenReturn(filterFieldConfigDTOMap);
        // act
        boolean result = conditionUtils.isConditionMatch(processOrchestrationDTO, conditionDetailDTOList, userTagDOS, null, null);
        // assert
        assertTrue(result);
    }

    /**
     * Test case for skipping when filter field config not found
     */
    @Test
    public void testIsConditionMatch_SkipWhenFilterFieldConfigNotFound() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("1.0");
        ScrmProcessOrchestrationConditionDetailDTO conditionDetailDTO = new ScrmProcessOrchestrationConditionDetailDTO();
        conditionDetailDTO.setGroupId(1);
        conditionDetailDTO.setFilterFieldId(999L);
        List<ScrmProcessOrchestrationConditionDetailDTO> conditionDetailDTOList = Arrays.asList(conditionDetailDTO);
        List<ScrmUserTag> userTagDOS = Collections.singletonList(createUserTag("testAppId", 999L));
        when(configDomainService.getFilterFieldConfigsMapByAppId(anyString())).thenReturn(new HashMap<>());
        // act
        boolean result = conditionUtils.isConditionMatch(processOrchestrationDTO, conditionDetailDTOList, userTagDOS, null, null);
        // assert
        assertFalse(result);
    }

    /**
     * Test case for setting isCorpTag flag and computing corp tag result
     */
    @Test
    public void testIsConditionMatch_ComputeCorpTag() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testAppId");
        // Create condition detail with operator ID
        ScrmProcessOrchestrationConditionDetailDTO conditionDetailDTO = new ScrmProcessOrchestrationConditionDetailDTO();
        conditionDetailDTO.setGroupId(1);
        conditionDetailDTO.setFilterFieldId(ScrmUserTagEnum.CORP_TAG.getTagId());
        // Set operator ID
        conditionDetailDTO.setOperatorId(1L);
        conditionDetailDTO.setParam(Arrays.asList("tag1"));
        List<ScrmProcessOrchestrationConditionDetailDTO> conditionDetailDTOList = Arrays.asList(conditionDetailDTO);
        // Create user tag with correct app ID and tag ID
        ScrmUserTag userTag = createUserTag("testAppId", ScrmUserTagEnum.CORP_TAG.getTagId());
        List<ScrmUserTag> userTagDOS = Collections.singletonList(userTag);
        // Create filter field config with operator
        ScrmFilterFieldConfigDTO filterFieldConfig = createFilterFieldConfig();
        Map<Long, ScrmFilterFieldConfigDTO> filterFieldConfigDTOMap = new HashMap<>();
        filterFieldConfigDTOMap.put(ScrmUserTagEnum.CORP_TAG.getTagId(), filterFieldConfig);
        when(configDomainService.getFilterFieldConfigsMapByAppId("testAppId")).thenReturn(filterFieldConfigDTOMap);
        // Mock external contact tag service
        ExternalContactTag externalTag = createExternalContactTag("tag1");
        when(externalContactTagDomainService.queryTagsByUnionId("testAppId", "testUnionId", false)).thenReturn(Collections.singletonList(externalTag));
        // Mock operate utils compute method with specific parameters
        when(operateUtils.compute(anyList(), eq(Arrays.asList("tag1")), any())).thenReturn(true);
        // act
        boolean result = conditionUtils.isConditionMatch(processOrchestrationDTO, conditionDetailDTOList, userTagDOS, null, "testUnionId");
        // assert
        verify(externalContactTagDomainService).queryTagsByUnionId("testAppId", "testUnionId", false);
        verify(operateUtils).compute(anyList(), eq(Arrays.asList("tag1")), any());
        assertTrue(result);
    }
}
