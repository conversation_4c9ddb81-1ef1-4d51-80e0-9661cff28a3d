package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.ArgumentMatchers.eq;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import java.util.AbstractMap;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Test for OfficialWxHandler.dealOfficialWxGroupMessageV2()
 */
@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerDealOfficialWxGroupMessageV21Test {

    @Spy
    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    @Mock
    private ExecuteManagementDTO executeManagementDTO;

    private List<String> executorIds;

    private Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> typeExecuteLogDOMapEntry;

    private InvokeDetailKeyObject keyObject;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> invokeDetailDOS;

    @BeforeEach
    void setUp() {
        // Initialize executorIds
        executorIds = new ArrayList<>();
        executorIds.add("executor1");
        // Initialize keyObject with required parameters
        keyObject = new // key
        // contentType (SUPPLY)
        InvokeDetailKeyObject(// contentSubType (will be set in individual tests)
        "testKey", // nodeId
        (byte) 1, (byte) 0, 1L);
        // Initialize invokeDetailDOS with required fields
        invokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setProcessOrchestrationNodeId(1L);
        detailDO.setProcessOrchestrationId(1L);
        detailDO.setProcessOrchestrationVersion("v1");
        detailDO.setExecutorId("executor1");
        detailDO.setType("test");
        invokeDetailDOS.add(detailDO);
        // Create the map entry
        typeExecuteLogDOMapEntry = new AbstractMap.SimpleEntry<>(keyObject, invokeDetailDOS);
    }

    /**
     * Test non-supply type message processing
     */
    @Test
    void testDealOfficialWxGroupMessageV2_NonSupplyType() throws Throwable {
        // arrange
        // NON_SUPPLY
        keyObject.setContentType((byte) 0);
        doNothing().when(officialWxHandler).dealNormalOfficialWxGroupMessageV2(any(), anyList(), any(), anyList());
        // act
        officialWxHandler.dealOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, typeExecuteLogDOMapEntry, executeManagementDTO);
        // assert
        verify(officialWxHandler, times(1)).dealNormalOfficialWxGroupMessageV2(eq(processOrchestrationDTO), eq(executorIds), eq(keyObject), eq(invokeDetailDOS));
    }

    /**
     * Test AI recall supply message processing
     */
    @Test
    void testDealOfficialWxGroupMessageV2_AutomaticProductPromotion() throws Throwable {
        // arrange
        // AUTOMATIC_PRODUCT_PROMOTION
        keyObject.setContentSubType((byte) 1);
        doNothing().when(officialWxHandler).dealAIRecallSupplyOfficialWxGroupMessageV2(any(), anyList(), any(), anyList(), any());
        // act
        officialWxHandler.dealOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, typeExecuteLogDOMapEntry, executeManagementDTO);
        // assert
        verify(officialWxHandler, times(1)).dealAIRecallSupplyOfficialWxGroupMessageV2(eq(processOrchestrationDTO), eq(executorIds), eq(keyObject), eq(invokeDetailDOS), eq(executeManagementDTO));
    }

    /**
     * Test manual product promotion message processing
     */
    @Test
    void testDealOfficialWxGroupMessageV2_ManualProductPromotion() throws Throwable {
        // arrange
        // MANUAL_PRODUCT_PROMOTION
        keyObject.setContentSubType((byte) 2);
        doNothing().when(officialWxHandler).dealChosenSupplyOfficialWxGroupMessageV2(any(), anyList(), any(), anyList(), any());
        // act
        officialWxHandler.dealOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, typeExecuteLogDOMapEntry, executeManagementDTO);
        // assert
        verify(officialWxHandler, times(1)).dealChosenSupplyOfficialWxGroupMessageV2(eq(processOrchestrationDTO), eq(executorIds), eq(keyObject), eq(invokeDetailDOS), eq(executeManagementDTO));
    }

    /**
     * Test customized product promotion message processing
     */
    @Test
    void testDealOfficialWxGroupMessageV2_CustomizedProductPromotion() throws Throwable {
        // arrange
        // CUSTOMIZED_PRODUCT_PROMOTION
        keyObject.setContentSubType((byte) 3);
        doNothing().when(officialWxHandler).dealChosenSupplyOfficialWxGroupMessageV2(any(), anyList(), any(), anyList(), any());
        // act
        officialWxHandler.dealOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, typeExecuteLogDOMapEntry, executeManagementDTO);
        // assert
        verify(officialWxHandler, times(1)).dealChosenSupplyOfficialWxGroupMessageV2(eq(processOrchestrationDTO), eq(executorIds), eq(keyObject), eq(invokeDetailDOS), eq(executeManagementDTO));
    }

    /**
     * Test coupon promotion message processing
     */
    @Test
    void testDealOfficialWxGroupMessageV2_CouponPromotion() throws Throwable {
        // arrange
        // COUPON_PROMOTION
        keyObject.setContentSubType((byte) 4);
        doNothing().when(officialWxHandler).dealCouponSupplyOfficialWxGroupMessageV2(any(), anyList(), any(), anyList(), any());
        // act
        officialWxHandler.dealOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, typeExecuteLogDOMapEntry, executeManagementDTO);
        // assert
        verify(officialWxHandler, times(1)).dealCouponSupplyOfficialWxGroupMessageV2(eq(processOrchestrationDTO), eq(executorIds), eq(keyObject), eq(invokeDetailDOS), eq(executeManagementDTO));
    }

    /**
     * Test processing with empty invoke details
     */
    @Test
    void testDealOfficialWxGroupMessageV2_EmptyInvokeDetails() throws Throwable {
        // arrange
        Map.Entry<InvokeDetailKeyObject, List<ScrmAmProcessOrchestrationWxInvokeDetailDO>> emptyEntry = new AbstractMap.SimpleEntry<>(keyObject, new ArrayList<>());
        // act
        officialWxHandler.dealOfficialWxGroupMessageV2(processOrchestrationDTO, executorIds, emptyEntry, executeManagementDTO);
        // assert - no processing methods should be called with empty list
        verify(officialWxHandler, never()).dealAIRecallSupplyOfficialWxGroupMessageV2(any(), anyList(), any(), anyList(), any());
        verify(officialWxHandler, never()).dealChosenSupplyOfficialWxGroupMessageV2(any(), anyList(), any(), anyList(), any());
        verify(officialWxHandler, never()).dealCouponSupplyOfficialWxGroupMessageV2(any(), anyList(), any(), anyList(), any());
        verify(officialWxHandler, never()).dealNormalOfficialWxGroupMessageV2(any(), anyList(), any(), anyList());
    }

    /**
     * Test processing with empty executors list
     */
    @Test
    void testDealOfficialWxGroupMessageV2_EmptyExecutors() throws Throwable {
        // arrange
        List<String> emptyExecutors = new ArrayList<>();
        // MANUAL_PRODUCT_PROMOTION
        keyObject.setContentSubType((byte) 2);
        doNothing().when(officialWxHandler).dealChosenSupplyOfficialWxGroupMessageV2(any(), anyList(), any(), anyList(), any());
        // act
        officialWxHandler.dealOfficialWxGroupMessageV2(processOrchestrationDTO, emptyExecutors, typeExecuteLogDOMapEntry, executeManagementDTO);
        // assert
        verify(officialWxHandler, times(1)).dealChosenSupplyOfficialWxGroupMessageV2(eq(processOrchestrationDTO), eq(emptyExecutors), eq(keyObject), eq(invokeDetailDOS), eq(executeManagementDTO));
    }
}
