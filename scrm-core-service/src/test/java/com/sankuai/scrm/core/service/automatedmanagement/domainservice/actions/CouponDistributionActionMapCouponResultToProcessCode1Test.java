package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum;
import java.lang.reflect.Method;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;
import static org.junit.jupiter.api.Assertions.*;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.AutoActionContentDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.coupon.dto.CouponRequestContext;
import com.sankuai.scrm.core.service.coupon.dto.CouponSceneEnum;
import java.util.Date;

@ExtendWith(MockitoExtension.class)
class CouponDistributionActionMapCouponResultToProcessCode1Test {

    @InjectMocks
    private CouponDistributionAction couponDistributionAction;

    private Method mapCouponResultToProcessCodeMethod;

    @BeforeEach
    void setUp() throws NoSuchMethodException {
        // Get access to the private method using reflection
        mapCouponResultToProcessCodeMethod = CouponDistributionAction.class.getDeclaredMethod("mapCouponResultToProcessCode", String.class);
        mapCouponResultToProcessCodeMethod.setAccessible(true);
    }

    /**
     * Test when couponErrorCode is null should return SEND_COUPON_FAIL
     */
    @Test
    void testMapCouponResultToProcessCode_NullInput() throws Throwable {
        // arrange
        String couponErrorCode = null;
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SEND_COUPON_FAIL, result);
    }

    /**
     * Test when couponErrorCode is "ALREADY_RECEIVED" should return ALREADY_RECEIVED
     */
    @Test
    void testMapCouponResultToProcessCode_AlreadyReceived() throws Throwable {
        // arrange
        String couponErrorCode = "ALREADY_RECEIVED";
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ALREADY_RECEIVED, result);
    }

    /**
     * Test when couponErrorCode is "INVALID_PARAM" should return ILLEGAL_ARGUMENT
     */
    @Test
    void testMapCouponResultToProcessCode_InvalidParam() throws Throwable {
        // arrange
        String couponErrorCode = "INVALID_PARAM";
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.ILLEGAL_ARGUMENT, result);
    }

    /**
     * Test when couponErrorCode is "USER_NOT_FOUND" should return TARGET_USER_NOT_FIND
     */
    @Test
    void testMapCouponResultToProcessCode_UserNotFound() throws Throwable {
        // arrange
        String couponErrorCode = "USER_NOT_FOUND";
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.TARGET_USER_NOT_FIND, result);
    }

    /**
     * Test when couponErrorCode is "MOCK_ISSUE" should return SUCCESS
     */
    @Test
    void testMapCouponResultToProcessCode_MockIssue() throws Throwable {
        // arrange
        String couponErrorCode = "MOCK_ISSUE";
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SUCCESS, result);
    }

    /**
     * Test when couponErrorCode is "ISSUE_FAILED" should return SEND_COUPON_FAIL
     */
    @Test
    void testMapCouponResultToProcessCode_IssueFailed() throws Throwable {
        // arrange
        String couponErrorCode = "ISSUE_FAILED";
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SEND_COUPON_FAIL, result);
    }

    /**
     * Test when couponErrorCode is "SYSTEM_ERROR" should return SEND_COUPON_FAIL
     */
    @Test
    void testMapCouponResultToProcessCode_SystemError() throws Throwable {
        // arrange
        String couponErrorCode = "SYSTEM_ERROR";
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SEND_COUPON_FAIL, result);
    }

    /**
     * Test when couponErrorCode is unknown should return SEND_COUPON_FAIL
     */
    @Test
    void testMapCouponResultToProcessCode_UnknownErrorCode() throws Throwable {
        // arrange
        String couponErrorCode = "UNKNOWN_ERROR";
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SEND_COUPON_FAIL, result);
    }

    /**
     * Test when couponErrorCode is empty string should return SEND_COUPON_FAIL
     */
    @Test
    void testMapCouponResultToProcessCode_EmptyString() throws Throwable {
        // arrange
        String couponErrorCode = "";
        // act
        ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum result = (ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum) mapCouponResultToProcessCodeMethod.invoke(couponDistributionAction, couponErrorCode);
        // assert
        assertEquals(ProcessOrchestrationRealTimeTaskExecuteResultCodeEnum.SEND_COUPON_FAIL, result);
    }

    private CouponRequestContext invokePrivateMethod(ScrmProcessOrchestrationDTO processDTO, ScrmCrowdPackDetailInfoDTO crowdPackDTO, AutoActionContentDetailDTO contentDTO, ScrmAmProcessOrchestrationExecuteLogDO executeLogDO) throws Exception {
        CouponDistributionAction action = new CouponDistributionAction();
        java.lang.reflect.Method method = CouponDistributionAction.class.getDeclaredMethod("buildCouponRequestContext", ScrmProcessOrchestrationDTO.class, ScrmCrowdPackDetailInfoDTO.class, AutoActionContentDetailDTO.class, ScrmAmProcessOrchestrationExecuteLogDO.class);
        method.setAccessible(true);
        return (CouponRequestContext) method.invoke(action, processDTO, crowdPackDTO, contentDTO, executeLogDO);
    }

    @Test
    void testBuildCouponRequestContextNormalCase() throws Throwable {
        // 准备测试数据
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        processDTO.setId(1L);
        processDTO.setAppId("testApp");
        processDTO.setProcessOrchestrationType((byte) 1);
        processDTO.setAiScene(false);
        ScrmCrowdPackDetailInfoDTO crowdPackDTO = new ScrmCrowdPackDetailInfoDTO();
        crowdPackDTO.setExternalUserWxUnionId("testUnionId");
        AutoActionContentDetailDTO contentDTO = new AutoActionContentDetailDTO();
        contentDTO.setCouponGroupId("testCouponGroup");
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setTargetMtUserId(100L);
        executeLogDO.setProcessOrchestrationNodeId(10L);
        executeLogDO.setTaskStartTime(new Date());
        // 执行测试
        CouponRequestContext result = invokePrivateMethod(processDTO, crowdPackDTO, contentDTO, executeLogDO);
        // 验证结果
        assertNotNull(result);
        assertEquals(100L, result.getUserId());
        assertEquals("testUnionId", result.getUnionId());
        assertEquals("testApp", result.getAppId());
        assertEquals("testCouponGroup", result.getCouponGroupId());
        assertEquals(CouponSceneEnum.PROCESS_ORCHESTRATION, result.getScene());
        assertEquals(1L, result.getProcessOrchestrationId());
        assertEquals(10L, result.getProcessOrchestrationNodeId());
        assertEquals((byte) 1, result.getProcessOrchestrationType());
        assertNotNull(result.getTaskStartTime());
        assertFalse(result.isAiScene());
        assertNull(result.getProductId());
        assertNull(result.getShopId());
    }

    @Test
    void testBuildCouponRequestContextAiSceneWithContent() throws Throwable {
        // 准备测试数据
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        processDTO.setId(1L);
        processDTO.setAppId("testApp");
        processDTO.setProcessOrchestrationType((byte) 1);
        processDTO.setAiScene(true);
        IntelligentFollowResultDTO aiContent = new IntelligentFollowResultDTO();
        aiContent.setProductId(200L);
        aiContent.setShopId(300L);
        processDTO.setAiSceneContent(aiContent);
        ScrmCrowdPackDetailInfoDTO crowdPackDTO = new ScrmCrowdPackDetailInfoDTO();
        crowdPackDTO.setExternalUserWxUnionId("testUnionId");
        AutoActionContentDetailDTO contentDTO = new AutoActionContentDetailDTO();
        contentDTO.setCouponGroupId("testCouponGroup");
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setTargetMtUserId(100L);
        executeLogDO.setProcessOrchestrationNodeId(10L);
        executeLogDO.setTaskStartTime(new Date());
        // 执行测试
        CouponRequestContext result = invokePrivateMethod(processDTO, crowdPackDTO, contentDTO, executeLogDO);
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isAiScene());
        assertEquals(200L, result.getProductId());
        assertEquals(300L, result.getShopId());
    }

    @Test
    void testBuildCouponRequestContextAiSceneWithNullContent() throws Throwable {
        // 准备测试数据
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        processDTO.setId(1L);
        processDTO.setAppId("testApp");
        processDTO.setProcessOrchestrationType((byte) 1);
        processDTO.setAiScene(true);
        processDTO.setAiSceneContent(null);
        ScrmCrowdPackDetailInfoDTO crowdPackDTO = new ScrmCrowdPackDetailInfoDTO();
        crowdPackDTO.setExternalUserWxUnionId("testUnionId");
        AutoActionContentDetailDTO contentDTO = new AutoActionContentDetailDTO();
        contentDTO.setCouponGroupId("testCouponGroup");
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setTargetMtUserId(100L);
        executeLogDO.setProcessOrchestrationNodeId(10L);
        executeLogDO.setTaskStartTime(new Date());
        // 执行测试
        CouponRequestContext result = invokePrivateMethod(processDTO, crowdPackDTO, contentDTO, executeLogDO);
        // 验证结果
        assertNotNull(result);
        assertTrue(result.isAiScene());
        assertNull(result.getProductId());
        assertNull(result.getShopId());
    }

    @Test
    void testBuildCouponRequestContextEmptyUnionId() throws Throwable {
        // 准备测试数据
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        processDTO.setId(1L);
        processDTO.setAppId("testApp");
        processDTO.setProcessOrchestrationType((byte) 1);
        processDTO.setAiScene(false);
        ScrmCrowdPackDetailInfoDTO crowdPackDTO = new ScrmCrowdPackDetailInfoDTO();
        crowdPackDTO.setExternalUserWxUnionId("");
        AutoActionContentDetailDTO contentDTO = new AutoActionContentDetailDTO();
        contentDTO.setCouponGroupId("testCouponGroup");
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setTargetMtUserId(100L);
        executeLogDO.setProcessOrchestrationNodeId(10L);
        executeLogDO.setTaskStartTime(new Date());
        // 执行测试
        CouponRequestContext result = invokePrivateMethod(processDTO, crowdPackDTO, contentDTO, executeLogDO);
        // 验证结果
        assertNotNull(result);
        assertEquals("", result.getUnionId());
    }

    @Test
    void testBuildCouponRequestContextNullProcessDTO() throws Throwable {
        // 准备测试数据
        ScrmCrowdPackDetailInfoDTO crowdPackDTO = new ScrmCrowdPackDetailInfoDTO();
        crowdPackDTO.setExternalUserWxUnionId("testUnionId");
        AutoActionContentDetailDTO contentDTO = new AutoActionContentDetailDTO();
        contentDTO.setCouponGroupId("testCouponGroup");
        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setTargetMtUserId(100L);
        executeLogDO.setProcessOrchestrationNodeId(10L);
        executeLogDO.setTaskStartTime(new Date());
        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            invokePrivateMethod(null, crowdPackDTO, contentDTO, executeLogDO);
        });
    }

    @Test
    void testBuildCouponRequestContextNullExecuteLog() throws Throwable {
        // 准备测试数据
        ScrmProcessOrchestrationDTO processDTO = new ScrmProcessOrchestrationDTO();
        processDTO.setId(1L);
        processDTO.setAppId("testApp");
        processDTO.setProcessOrchestrationType((byte) 1);
        processDTO.setAiScene(false);
        ScrmCrowdPackDetailInfoDTO crowdPackDTO = new ScrmCrowdPackDetailInfoDTO();
        crowdPackDTO.setExternalUserWxUnionId("testUnionId");
        AutoActionContentDetailDTO contentDTO = new AutoActionContentDetailDTO();
        contentDTO.setCouponGroupId("testCouponGroup");
        // 执行测试并验证异常
        assertThrows(Exception.class, () -> {
            invokePrivateMethod(processDTO, crowdPackDTO, contentDTO, null);
        });
    }
}
