package com.sankuai.scrm.core.service.automatedmanagement.mq.producer;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.producer.IProducerProcessor;
import com.meituan.mafka.client.producer.ProducerResult;
import com.meituan.mafka.client.producer.ProducerStatus;
import java.lang.reflect.Field;
import java.util.Properties;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.ArgumentMatcher;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ManualRefreshProductInfoProducerTest {

    private IProducerProcessor producer;

    private ManualRefreshProductInfoProducer manualRefreshProductInfoProducer;

    private IProducerProcessor originalProducer;

    @Mock(lenient = true)
    private MafkaClient mafkaClient;

    @Before
    public void setUp() throws Exception {
        MockitoAnnotations.initMocks(this);
        producer = mock(IProducerProcessor.class);
        manualRefreshProductInfoProducer = new ManualRefreshProductInfoProducer();
        // Store the original producer
        Field producerField = ManualRefreshProductInfoProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        originalProducer = (IProducerProcessor) producerField.get(null);
        // Set the mocked producer
        producerField.set(null, producer);
    }

    @After
    public void tearDown() throws Exception {
        // Restore the original producer
        Field producerField = ManualRefreshProductInfoProducer.class.getDeclaredField("producer");
        producerField.setAccessible(true);
        producerField.set(null, originalProducer);
    }

    @Test
    public void testSendMessageAppIdIsNull() throws Throwable {
        String appId = null;
        manualRefreshProductInfoProducer.sendMessage(appId);
        verify(producer, never()).sendMessage(anyString());
    }

    @Test
    public void testSendMessageSendSuccessOnFirstTry() throws Throwable {
        String appId = "testAppId";
        ProducerResult result = new ProducerResult(ProducerStatus.SEND_OK);
        when(producer.sendMessage(appId)).thenReturn(result);
        manualRefreshProductInfoProducer.sendMessage(appId);
        verify(producer, times(1)).sendMessage(appId);
    }

    @Test
    public void testSendMessageSendFailOnAllTries() throws Throwable {
        String appId = "testAppId";
        ProducerResult result = new ProducerResult(ProducerStatus.SEND_UNSUPPORTED);
        when(producer.sendMessage(appId)).thenReturn(result);
        manualRefreshProductInfoProducer.sendMessage(appId);
        verify(producer, times(3)).sendMessage(appId);
    }

    @Test
    public void testSendMessageSendSuccessOnSecondTry() throws Throwable {
        String appId = "testAppId";
        ProducerResult result1 = new ProducerResult(ProducerStatus.SEND_UNSUPPORTED);
        ProducerResult result2 = new ProducerResult(ProducerStatus.SEND_OK);
        when(producer.sendMessage(appId)).thenReturn(result1, result2);
        manualRefreshProductInfoProducer.sendMessage(appId);
        verify(producer, times(2)).sendMessage(appId);
    }

    @Test
    public void testSendMessageException() throws Throwable {
        String appId = "testAppId";
        when(producer.sendMessage(appId)).thenThrow(new Exception());
        manualRefreshProductInfoProducer.sendMessage(appId);
        verify(producer, times(3)).sendMessage(appId);
    }

    private static class PropertiesMatcher implements ArgumentMatcher<Properties> {

        @Override
        public boolean matches(Properties properties) {
            return properties != null && "daozong".equals(properties.getProperty("ConsumerConstants.MafkaBGNamespace")) && "com.sankuai.medicalcosmetology.scrm.core".equals(properties.getProperty("ConsumerConstants.MafkaClientAppkey"));
        }
    }

    /**
     * Tests afterPropertiesSet method under exceptional conditions.
     */
    @Test(expected = Exception.class)
    public void testAfterPropertiesSetException() throws Throwable {
        try (MockedStatic<MafkaClient> mockedStatic = mockStatic(MafkaClient.class)) {
            // Arrange
            ManualRefreshProductInfoProducer producer = new ManualRefreshProductInfoProducer();
            mockedStatic.when(() -> MafkaClient.buildProduceFactory(argThat(new PropertiesMatcher()), eq("scrm.product.pool.refresh.task"))).thenThrow(new Exception());
            // Act
            producer.afterPropertiesSet();
            // Assert
            mockedStatic.verify(() -> MafkaClient.buildProduceFactory(argThat(new PropertiesMatcher()), eq("scrm.product.pool.refresh.task")));
        }
    }
}
