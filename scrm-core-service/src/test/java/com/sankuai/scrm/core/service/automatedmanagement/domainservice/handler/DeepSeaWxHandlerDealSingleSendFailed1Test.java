package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;
import com.fasterxml.jackson.annotation.JsonProperty;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.message.push.response.MsgPushResponse;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

/**
 * Test cases for DeepSeaWxHandler.dealSingleSendFailed method
 */
public class DeepSeaWxHandlerDealSingleSendFailed1Test {

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @InjectMocks
    private DeepSeaWxHandler deepSeaWxHandler;

    private AutoCloseable closeable;

    /**
     * A concrete class for the request object with proper Jackson annotations
     */
    public static class TestRequest {

        @JsonProperty("field1")
        private String field1;

        @JsonProperty("field2")
        private int field2;

        public TestRequest() {
        }

        public TestRequest(String field1, int field2) {
            this.field1 = field1;
            this.field2 = field2;
        }

        public String getField1() {
            return field1;
        }

        public void setField1(String field1) {
            this.field1 = field1;
        }

        public int getField2() {
            return field2;
        }

        public void setField2(int field2) {
            this.field2 = field2;
        }
    }

    @BeforeEach
    public void setUp() {
        closeable = MockitoAnnotations.openMocks(this);
    }

    @AfterEach
    public void tearDown() throws Exception {
        closeable.close();
    }

    /**
     * Test dealSingleSendFailed when response is null
     */
    @Test
    public void testDealSingleSendFailed_ResponseIsNull() throws Throwable {
        // arrange
        Long executeLogId = 1L;
        TestRequest request = new TestRequest("test", 123);
        MsgPushResponse<Long> response = null;
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        String executorId = "executor1";
        when(wxInvokeLogDOMapper.insertSelective(any())).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // act
        deepSeaWxHandler.dealSingleSendFailed(executeLogId, request, response, wxInvokeLogDO, executorId);
        // assert
        ArgumentCaptor<ScrmAmProcessOrchestrationWxInvokeLogDO> logCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationWxInvokeLogDO.class);
        verify(wxInvokeLogDOMapper).insertSelective(logCaptor.capture());
        assertEquals(StringUtils.EMPTY, logCaptor.getValue().getJobid());
    }

    /**
     * Test dealSingleSendFailed when response has data
     */
    @Test
    public void testDealSingleSendFailed_ResponseHasData() throws Throwable {
        // arrange
        Long executeLogId = 1L;
        TestRequest request = new TestRequest("test", 123);
        MsgPushResponse<Long> response = new MsgPushResponse<>();
        response.setData(12345L);
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        String executorId = "executor1";
        when(wxInvokeLogDOMapper.insertSelective(any())).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // act
        deepSeaWxHandler.dealSingleSendFailed(executeLogId, request, response, wxInvokeLogDO, executorId);
        // assert
        ArgumentCaptor<ScrmAmProcessOrchestrationWxInvokeLogDO> logCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationWxInvokeLogDO.class);
        verify(wxInvokeLogDOMapper).insertSelective(logCaptor.capture());
        assertEquals("12345", logCaptor.getValue().getJobid());
    }

    /**
     * Test dealSingleSendFailed when response has no data
     */
    @Test
    public void testDealSingleSendFailed_ResponseHasNoData() throws Throwable {
        // arrange
        Long executeLogId = 1L;
        TestRequest request = new TestRequest("test", 123);
        MsgPushResponse<Long> response = new MsgPushResponse<>();
        response.setData(null);
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        String executorId = "executor1";
        when(wxInvokeLogDOMapper.insertSelective(any())).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // act
        deepSeaWxHandler.dealSingleSendFailed(executeLogId, request, response, wxInvokeLogDO, executorId);
        // assert
        ArgumentCaptor<ScrmAmProcessOrchestrationWxInvokeLogDO> logCaptor = ArgumentCaptor.forClass(ScrmAmProcessOrchestrationWxInvokeLogDO.class);
        verify(wxInvokeLogDOMapper).insertSelective(logCaptor.capture());
        assertEquals(StringUtils.EMPTY, logCaptor.getValue().getJobid());
    }
}
