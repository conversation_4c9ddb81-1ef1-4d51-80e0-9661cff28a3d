package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationExecutePlanDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteLogDOExample;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import org.apache.commons.lang.StringUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Spy;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class AbstractActionGetScrmAmProcessOrchestrationExecuteLogDO2Test {

    @Spy
    private AbstractAction abstractAction = new AbstractAction() {

        @Override
        protected List<ScrmAmProcessOrchestrationExecuteLogDO> getExecuteLogByExample(ScrmAmProcessOrchestrationExecuteLogDOExample executeLogDOExample) {
            return new ArrayList<>();
        }
    };

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO;

    private ScrmProcessOrchestrationNodeDTO currentProcessingNode;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setAppId("testApp");
        processOrchestrationDTO.setValidVersion("1.0");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(1L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);
        scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        scrmCrowdPackDetailInfoDTO.setExternalUserWxUnionId("wxUnionId");
        scrmCrowdPackDetailInfoDTO.setExternalUserId(100L);
        currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setNodeId(1L);
    }

    /**
     * Test case when existedExecuteLogDO is not null
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_WithExistedLog() {
        // arrange
        ScrmAmProcessOrchestrationExecuteLogDO existedExecuteLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        // assert
        assertSame(existedExecuteLogDO, result);
    }

    /**
     * Test case for participation restrict is true
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_ParticipationRestrictTrue() {
        // arrange
        processOrchestrationDTO.setParticipationRestrict(true);
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertNotNull(result);
        assertEquals(processOrchestrationDTO.getAppId(), result.getAppId());
        assertEquals(processOrchestrationDTO.getId(), result.getProcessOrchestrationId());
    }

    /**
     * Test case for demo scene
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_DemoScene() {
        // arrange
        processOrchestrationDTO.setDemoScene(true);
        List<ScrmAmProcessOrchestrationExecuteLogDO> existingLogs = new ArrayList<>();
        existingLogs.add(new ScrmAmProcessOrchestrationExecuteLogDO());
        doReturn(existingLogs).when(abstractAction).getExecuteLogByExample(any());
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertNotNull(result);
        assertEquals("system", result.getExecutorId());
    }

    /**
     * Test case for AI scene
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_AiScene() {
        // arrange
        processOrchestrationDTO.setAiScene(true);
        List<ScrmAmProcessOrchestrationExecuteLogDO> existingLogs = new ArrayList<>();
        existingLogs.add(new ScrmAmProcessOrchestrationExecuteLogDO());
        doReturn(existingLogs).when(abstractAction).getExecuteLogByExample(any());
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertNotNull(result);
        assertEquals((byte) 3, result.getExecutorIdType());
    }

    /**
     * Test case when participation restrict is false with cycle > 1
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_ParticipationRestrictFalseWithCycle() {
        // arrange
        processOrchestrationDTO.setParticipationRestrict(false);
        processOrchestrationDTO.setParticipationRestrictionsCycle((byte) 2);
        processOrchestrationDTO.setParticipationRestrictionsTimes((byte) 5);
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertNotNull(result);
        assertEquals(processOrchestrationDTO.getValidVersion(), result.getProcessOrchestrationVersion());
    }

    /**
     * Test case when existing logs exceed participation restrictions
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_ExceedRestrictions() {
        // arrange
        processOrchestrationDTO.setParticipationRestrictionsTimes((byte) 1);
        List<ScrmAmProcessOrchestrationExecuteLogDO> existingLogs = new ArrayList<>();
        existingLogs.add(new ScrmAmProcessOrchestrationExecuteLogDO());
        doReturn(existingLogs).when(abstractAction).getExecuteLogByExample(any());
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertNull(result);
    }

    /**
     * Test case with null external user ID
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_NullExternalUserId() {
        // arrange
        scrmCrowdPackDetailInfoDTO.setExternalUserId(null);
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertNotNull(result);
        assertEquals(0L, result.getTargetId());
    }

    /**
     * Test case for verifying member name is empty
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_EmptyMemberName() {
        // arrange
        processOrchestrationDTO.setParticipationRestrict(false);
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertNotNull(result);
        assertEquals(StringUtils.EMPTY, result.getMemberName());
    }

    /**
     * Test case for verifying target ID type
     */
    @Test
    public void testGetScrmAmProcessOrchestrationExecuteLogDO_TargetIdType() {
        // arrange
        processOrchestrationDTO.setParticipationRestrict(false);
        // act
        ScrmAmProcessOrchestrationExecuteLogDO result = abstractAction.getScrmAmProcessOrchestrationExecuteLogDO(processOrchestrationDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, null);
        // assert
        assertNotNull(result);
        assertEquals((byte) 1, result.getTargetIdType());
    }
}
