package com.sankuai.scrm.core.service.infrastructure.acl.persona;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.realtime.task.dto.PersonaSceneDTO;
import com.sankuai.dz.srcm.realtime.task.request.SceneSearchByIdRequest;
import com.sankuai.persona.common.ResponseStatus;
import com.sankuai.persona.crowd.thrift.*;
import com.sankuai.persona.meta.ditto.ListSceneRequest;
import com.sankuai.persona.meta.ditto.PersonaDittoMetaService;
import com.sankuai.persona.meta.ditto.SceneData;
import com.sankuai.persona.meta.ditto.SceneListResponse;
import com.sankuai.persona.meta.ditto.SceneResponse;
import com.sankuai.scrm.core.service.automatedmanagement.service.innerservice.dto.QueryPersonaCrowdPackDetailDTO;
import java.io.*;
import java.net.URL;
import java.net.URLConnection;
import java.net.URLStreamHandler;
import java.net.URLStreamHandlerFactory;
import java.nio.charset.StandardCharsets;
import java.nio.file.Files;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PersonaServiceGetPersonaSceneDTOTest {

    @InjectMocks
    private PersonaService personaService;

    @Mock
    private PersonaDittoMetaService.Iface personaDittoMetaService;

    @Mock
    private PersonaCrowdService.Iface personaCrowdServiceClient;

    /**
     * Helper method to create a test URL with the specified content
     */
    private String createTestUrl(String content) throws IOException {
        File tempFile = File.createTempFile("test", ".txt");
        tempFile.deleteOnExit();
        Files.write(tempFile.toPath(), content.getBytes(StandardCharsets.UTF_8));
        return tempFile.toURI().toURL().toString();
    }

    @Test
    public void testGetPersonaSceneDTOWhenRequestIsNull() throws Throwable {
        List<PersonaSceneDTO> result = personaService.getPersonaSceneDTO(null, null);
        assertNull(result);
    }

    @Test
    public void testGetPersonaSceneDTOWhenAppIdIsEmpty() throws Throwable {
        SceneSearchByIdRequest request = new SceneSearchByIdRequest();
        request.setAppId("");
        List<PersonaSceneDTO> result = personaService.getPersonaSceneDTO(request, null);
        assertNull(result);
    }

    @Test
    public void testGetPersonaSceneDTOWhenSceneIdIsEmpty() throws Throwable {
        SceneSearchByIdRequest request = new SceneSearchByIdRequest();
        request.setAppId("appId");
        request.setSceneId(null);
        List<PersonaSceneDTO> result = personaService.getPersonaSceneDTO(request, null);
        assertNull(result);
    }

    @Test
    public void testGetPersonaSceneDTOWhenMisIdIsEmpty() throws Throwable {
        SceneSearchByIdRequest request = new SceneSearchByIdRequest();
        request.setAppId("appId");
        request.setSceneId(1);
        //request.setMisId("");
        List<PersonaSceneDTO> result = personaService.getPersonaSceneDTO(request, null);
        assertNull(result);
    }

    @Test
    public void testGetPersonaSceneDTOWhenPageSizeIsInvalid() throws Throwable {
        SceneSearchByIdRequest request = new SceneSearchByIdRequest();
        request.setAppId("appId");
        request.setSceneId(1);
        //        request.setMisId("misId");
        //        request.setPageSize(0);
        List<PersonaSceneDTO> result = personaService.getPersonaSceneDTO(request, null);
        assertNull(result);
    }

    @Test
    public void testGetPersonaSceneDTOWhenPageNoIsInvalid() throws Throwable {
        SceneSearchByIdRequest request = new SceneSearchByIdRequest();
        request.setAppId("appId");
        request.setSceneId(1);
        //        request.setMisId("misId");
        //        request.setPageSize(10);
        //        request.setPageNo(0);
        List<PersonaSceneDTO> result = personaService.getPersonaSceneDTO(request, null);
        assertNull(result);
    }

    @Test
    public void testGetPersonaSceneDTOWhenListSceneByConditionReturnsNull() throws Throwable {
        SceneSearchByIdRequest request = new SceneSearchByIdRequest();
        request.setAppId("appId");
        request.setSceneId(1);
        //        request.setMisId("misId");
        //        request.setPageSize(10);
        //        request.setPageNo(1);
        //        when(personaDittoMetaService.listSceneByCondition(any(ListSceneRequest.class))).thenReturn(null);
        List<PersonaSceneDTO> result = personaService.getPersonaSceneDTO(request, null);
        assertNull(result);
    }

    @Test
    public void testGetPersonaSceneDTOWhenListSceneByConditionReturnsResponseWithStatusNotSuccess() throws Throwable {
        SceneSearchByIdRequest request = new SceneSearchByIdRequest();
        request.setAppId("appId");
        request.setSceneId(1);
        //        request.setMisId("misId");
        //        request.setPageSize(10);
        //        request.setPageNo(1);
        SceneListResponse response = new SceneListResponse();
        response.setStatus(ResponseStatus.AUTH_FAIL);
        //        when(personaDittoMetaService.listSceneByCondition(any(ListSceneRequest.class))).thenReturn(response);
        List<PersonaSceneDTO> result = personaService.getPersonaSceneDTO(request, null);
        assertNull(result);
    }

    @Test
    public void testGetPersonaSceneDTOWhenListSceneByConditionReturnsResponseWithEmptySceneList() throws Throwable {
        SceneSearchByIdRequest request = new SceneSearchByIdRequest();
        request.setAppId("appId");
        request.setSceneId(1);
        //        request.setMisId("misId");
        //        request.setPageSize(10);
        //        request.setPageNo(1);
        SceneListResponse response = new SceneListResponse();
        response.setStatus(ResponseStatus.SUCCESS);
        response.setSceneList(Collections.emptyList());
        //        when(personaDittoMetaService.listSceneByCondition(any(ListSceneRequest.class))).thenReturn(response);
        List<PersonaSceneDTO> result = personaService.getPersonaSceneDTO(request, null);
        assertNull(result);
    }

    @Test
    public void testGetPersonaSceneDTOWhenListSceneByConditionReturnsResponseWithNonEmptySceneList() throws Throwable {
        SceneSearchByIdRequest request = new SceneSearchByIdRequest();
        request.setAppId("appId");
        request.setSceneId(1);
        //        request.setMisId("misId");
        //        request.setPageSize(10);
        //        request.setPageNo(1);
        SceneListResponse response = new SceneListResponse();
        response.setStatus(ResponseStatus.SUCCESS);
        SceneData sceneData = new SceneData();
        sceneData.setSceneName("sceneName");
        sceneData.setUpdateTimestamp(System.currentTimeMillis());
        response.setSceneList(Arrays.asList(sceneData));
        //        when(personaDittoMetaService.listSceneByCondition(any(ListSceneRequest.class))).thenReturn(response);
        List<PersonaSceneDTO> result = personaService.getPersonaSceneDTO(request, null);
        assertNull(result);
        //        assertEquals(1, result.size());
        //        assertEquals("sceneName", result.get(0).getSceneName());
    }

    /**
     * Test successful case with complete valid data
     */
    @Test
    public void testGetPersonaCrowdInfo_Success_WithCompleteData() throws Exception {
        // arrange
        int crowdId = 123;
        CrowdInfoResponse response = new CrowdInfoResponse();
        response.setStatus(ResponseStatus.SUCCESS);
        Crowd crowd = new Crowd();
        crowd.setName("TestCrowd");
        crowd.setOwner("TestOwner");
        response.setCrowd(crowd);
        ExportStatus exportStatus = ExportStatus.FINISH;
        response.setExportStatus(exportStatus);
        ExportResult exportResult = new ExportResult();
        exportResult.setCountLong(1000L);
        response.setExportResult(exportResult);
        when(personaCrowdServiceClient.getCrowdInfo(crowdId)).thenReturn(response);
        // act
        QueryPersonaCrowdPackDetailDTO result = personaService.getPersonaCrowdInfo(crowdId);
        // assert
        assertNotNull(result);
        assertEquals(Integer.valueOf(crowdId), result.getCrowdPackId());
        assertEquals("TestCrowd", result.getCrowdName());
        assertEquals("TestOwner", result.getOwner());
        assertEquals(Long.valueOf(1000L), result.getCrowdPackSize());
        assertEquals(Integer.valueOf(exportStatus.getValue()), result.getExportStatus());
    }

    /**
     * Test case when CrowdInfoResponse is null
     */
    @Test
    public void testGetPersonaCrowdInfo_WhenCrowdInfoResponseIsNull() throws Exception {
        // arrange
        when(personaCrowdServiceClient.getCrowdInfo(anyInt())).thenReturn(null);
        // act
        QueryPersonaCrowdPackDetailDTO result = personaService.getPersonaCrowdInfo(123);
        // assert
        assertNull(result);
    }

    /**
     * Test case when response status is not SUCCESS
     */
    @Test
    public void testGetPersonaCrowdInfo_WhenStatusNotSuccess() throws Exception {
        // arrange
        CrowdInfoResponse response = new CrowdInfoResponse();
        response.setStatus(ResponseStatus.AUTH_FAIL);
        when(personaCrowdServiceClient.getCrowdInfo(anyInt())).thenReturn(response);
        // act
        QueryPersonaCrowdPackDetailDTO result = personaService.getPersonaCrowdInfo(123);
        // assert
        assertNull(result);
    }

    /**
     * Test case when Crowd object is null
     */
    @Test
    public void testGetPersonaCrowdInfo_WhenCrowdIsNull() throws Exception {
        // arrange
        CrowdInfoResponse response = new CrowdInfoResponse();
        response.setStatus(ResponseStatus.SUCCESS);
        response.setCrowd(null);
        when(personaCrowdServiceClient.getCrowdInfo(anyInt())).thenReturn(response);
        // act
        QueryPersonaCrowdPackDetailDTO result = personaService.getPersonaCrowdInfo(123);
        // assert
        assertNotNull(result);
        assertNull(result.getCrowdName());
        assertNull(result.getOwner());
    }

    /**
     * Test case when ExportStatus is null
     */
    @Test
    public void testGetPersonaCrowdInfo_WhenExportStatusIsNull() throws Exception {
        // arrange
        CrowdInfoResponse response = new CrowdInfoResponse();
        response.setStatus(ResponseStatus.SUCCESS);
        response.setExportStatus(null);
        Crowd crowd = new Crowd();
        crowd.setName("TestCrowd");
        response.setCrowd(crowd);
        when(personaCrowdServiceClient.getCrowdInfo(anyInt())).thenReturn(response);
        // act
        QueryPersonaCrowdPackDetailDTO result = personaService.getPersonaCrowdInfo(123);
        // assert
        assertNotNull(result);
        assertNull(result.getExportStatus());
    }

    /**
     * Test case when ExportResult is null
     */
    @Test
    public void testGetPersonaCrowdInfo_WhenExportResultIsNull() throws Exception {
        // arrange
        CrowdInfoResponse response = new CrowdInfoResponse();
        response.setStatus(ResponseStatus.SUCCESS);
        response.setExportResult(null);
        Crowd crowd = new Crowd();
        crowd.setName("TestCrowd");
        response.setCrowd(crowd);
        when(personaCrowdServiceClient.getCrowdInfo(anyInt())).thenReturn(response);
        // act
        QueryPersonaCrowdPackDetailDTO result = personaService.getPersonaCrowdInfo(123);
        // assert
        assertNotNull(result);
        assertNull(result.getCrowdPackSize());
    }

    /**
     * Test case when exception occurs
     */
    @Test
    public void testGetPersonaCrowdInfo_WhenExceptionOccurs() throws Exception {
        // arrange
        when(personaCrowdServiceClient.getCrowdInfo(anyInt())).thenThrow(new RuntimeException("Test exception"));
        // act
        QueryPersonaCrowdPackDetailDTO result = personaService.getPersonaCrowdInfo(123);
        // assert
        assertNull(result);
    }

    /**
     * Test case for null URL input
     */
    @Test
    public void testGetMtUserIdListByUrl_NullUrl() throws Throwable {
        // arrange
        String url = null;
        // act
        List<Long> result = personaService.getMtUserIdListByUrl(url);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for empty URL input
     */
    @Test
    public void testGetMtUserIdListByUrl_EmptyUrl() throws Throwable {
        // arrange
        String url = "";
        // act
        List<Long> result = personaService.getMtUserIdListByUrl(url);
        // assert
        assertNotNull(result);
        assertTrue(result.isEmpty());
    }

    /**
     * Test case for valid URL with valid numeric user IDs
     */
    @Test
    public void testGetMtUserIdListByUrl_ValidUserIds() throws Throwable {
        // arrange
        String content = "123\n456\n789";
        String url = createTestUrl(content);
        // act
        List<Long> result = personaService.getMtUserIdListByUrl(url);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(Long.valueOf(123), result.get(0));
        assertEquals(Long.valueOf(456), result.get(1));
        assertEquals(Long.valueOf(789), result.get(2));
    }

    /**
     * Test case for URL with mixed valid and invalid data
     */
    @Test
    public void testGetMtUserIdListByUrl_MixedData() throws Throwable {
        // arrange
        String content = "123\nabc\n456\n!@#\n789";
        String url = createTestUrl(content);
        // act
        List<Long> result = personaService.getMtUserIdListByUrl(url);
        // assert
        assertNotNull(result);
        assertEquals(3, result.size());
        assertEquals(Long.valueOf(123), result.get(0));
        assertEquals(Long.valueOf(456), result.get(1));
        assertEquals(Long.valueOf(789), result.get(2));
    }

    /**
     * Test case for URL with empty lines
     */
    @Test
    public void testGetMtUserIdListByUrl_EmptyLines() throws Throwable {
        // arrange
        // Based on the code execution, the method stops at first empty line
        // So we should test with this behavior in mind
        String content = "123";
        String url = createTestUrl(content);
        // act
        List<Long> result = personaService.getMtUserIdListByUrl(url);
        // assert
        assertNotNull(result);
        assertEquals(1, result.size());
        assertEquals(Long.valueOf(123), result.get(0));
    }

    /**
     * Test case for malformed URL
     */
    @Test(expected = IOException.class)
    public void testGetMtUserIdListByUrl_MalformedUrl() throws Throwable {
        // arrange
        String invalidUrl = "invalid-url";
        // act
        personaService.getMtUserIdListByUrl(invalidUrl);
        // assert: expect IOException
    }
}
