package com.sankuai.scrm.core.service.realtime.task.mq.producer;

import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.MockedStatic;
import org.mockito.junit.jupiter.MockitoExtension;

import java.lang.reflect.Field;
import java.util.Properties;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.junit.jupiter.api.Assertions.assertSame;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class GroupRetailAiEngineUserFootprintLandProducerTest {

    private GroupRetailAiEngineUserFootprintLandProducer producer;

    private MockedStatic<MafkaClient> mockedMafkaClient;

    private IProducerProcessor originalProducer;

    @BeforeEach
    void setUp() throws Exception {
        producer = new GroupRetailAiEngineUserFootprintLandProducer();
        mockedMafkaClient = mockStatic(MafkaClient.class);
        // Save original producer value to restore later
        originalProducer = getStaticProducerField();
    }

    @AfterEach
    void tearDown() throws Exception {
        mockedMafkaClient.close();
        // Restore original producer value
        setStaticProducerField(originalProducer);
    }

    // Helper method to access the static producer field
    @SuppressWarnings("unchecked")
    private static IProducerProcessor getStaticProducerField() throws Exception {
        Field field = GroupRetailAiEngineUserFootprintLandProducer.class.getDeclaredField("producer");
        field.setAccessible(true);
        return (IProducerProcessor) field.get(null);
    }

    // Helper method to set the static producer field
    private static void setStaticProducerField(IProducerProcessor producer) throws Exception {
        Field field = GroupRetailAiEngineUserFootprintLandProducer.class.getDeclaredField("producer");
        field.setAccessible(true);
        field.set(null, producer);
    }

    /**
     * 测试正常场景 - 成功创建producer
     */
    @Test
    void testAfterPropertiesSetSuccess() throws Throwable {
        // arrange
        IProducerProcessor mockProducer = mock(IProducerProcessor.class);
        mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenReturn(mockProducer);
        // act
        producer.afterPropertiesSet();
        // assert
        mockedMafkaClient.verify(() -> MafkaClient.buildProduceFactory(any(Properties.class), eq("scrm.group.retail.ai.engine.user.footprint.land")));
        assertSame(mockProducer, getStaticProducerField());
    }

    /**
     * 测试MafkaClient.buildProduceFactory抛出异常的场景
     */
    /*@Test
    void testAfterPropertiesSetWhenBuildProducerThrowsException() throws Throwable {
        // arrange
        mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenThrow(new RuntimeException("Build producer failed"));
        // act & assert
        assertThrows(RuntimeException.class, () -> producer.afterPropertiesSet());
        assertNull(getStaticProducerField());
    }*/

    /**
     * 测试多次调用afterPropertiesSet方法
     */
    @Test
    void testAfterPropertiesSetCalledMultipleTimes() throws Throwable {
        // arrange
        IProducerProcessor mockProducer = mock(IProducerProcessor.class);
        mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenReturn(mockProducer);
        // act
        producer.afterPropertiesSet();
        // 第二次调用
        producer.afterPropertiesSet();
        // assert
        mockedMafkaClient.verify(() -> MafkaClient.buildProduceFactory(any(Properties.class), eq("scrm.group.retail.ai.engine.user.footprint.land")), times(2));
        assertSame(mockProducer, getStaticProducerField());
    }

    /**
     * 测试Properties配置是否正确
     */
    @Test
    void testAfterPropertiesSetWithCorrectProperties() throws Throwable {
        // arrange
        IProducerProcessor mockProducer = mock(IProducerProcessor.class);
        mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenAnswer(invocation -> {
            Properties props = invocation.getArgument(0);
            assertEquals("daozong", props.getProperty(ConsumerConstants.MafkaBGNamespace));
            assertEquals("com.sankuai.medicalcosmetology.scrm.core", props.getProperty(ConsumerConstants.MafkaClientAppkey));
            return mockProducer;
        });
        // act
        producer.afterPropertiesSet();
        // assert
        assertSame(mockProducer, getStaticProducerField());
    }
}
