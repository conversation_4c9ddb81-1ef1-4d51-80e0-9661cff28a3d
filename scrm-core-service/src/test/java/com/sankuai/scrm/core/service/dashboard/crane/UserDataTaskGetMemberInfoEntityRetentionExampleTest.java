package com.sankuai.scrm.core.service.dashboard.crane;

import static org.junit.Assert.*;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.group.dal.example.MemberInfoEntityExample;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.Date;
import org.junit.*;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

@RunWith(MockitoJUnitRunner.class)
public class UserDataTaskGetMemberInfoEntityRetentionExampleTest {

    private UserDataTask userDataTask = new UserDataTask();

    @Test
    public void testGetMemberInfoEntityRetentionExampleNormal() throws Throwable {
        Date start = new Date(1000L);
        Date end = new Date(2000L);
        String corpId = "testCorpId";
        Method method = UserDataTask.class.getDeclaredMethod("getMemberInfoEntityRetentionExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        MemberInfoEntityExample result = (MemberInfoEntityExample) method.invoke(userDataTask, start, end, corpId);
        assertNotNull(result);
    }

    @Test
    public void testGetMemberInfoEntityRetentionExampleStartNull() throws Throwable {
        Date start = null;
        Date end = new Date(2000L);
        String corpId = "testCorpId";
        Method method = UserDataTask.class.getDeclaredMethod("getMemberInfoEntityRetentionExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        try {
            method.invoke(userDataTask, start, end, corpId);
            fail("Expected an InvocationTargetException wrapping a RuntimeException");
        } catch (InvocationTargetException e) {
            assertTrue(e.getCause() instanceof RuntimeException);
            assertEquals("Between values for enterTime cannot be null", e.getCause().getMessage());
        }
    }

    @Test
    public void testGetMemberInfoEntityRetentionExampleSameDay() throws Throwable {
        Date start = new Date(1000L);
        Date end = new Date(1000L);
        String corpId = "testCorpId";
        Method method = UserDataTask.class.getDeclaredMethod("getMemberInfoEntityRetentionExample", Date.class, Date.class, String.class);
        method.setAccessible(true);
        MemberInfoEntityExample result = (MemberInfoEntityExample) method.invoke(userDataTask, start, end, corpId);
        assertNotNull(result);
    }
}
