package com.sankuai.scrm.core.service.automatedmanagement.mq.consumer;

import com.meituan.mafka.client.consumer.IConsumerProcessor;
import org.mockito.Mockito;
import java.lang.reflect.Field;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

public class ManualRefreshProductInfoConsumer_DestroyTest {

    private ManualRefreshProductInfoConsumer manualRefreshProductInfoConsumer;

    private IConsumerProcessor consumer;

    /**
     * 测试destroy方法，消费者为空的情况
     */
    @Test
    public void testDestroyConsumerIsNull() throws Throwable {
        // arrange
        ManualRefreshProductInfoConsumer manualRefreshProductInfoConsumer = new ManualRefreshProductInfoConsumer();
        // act
        manualRefreshProductInfoConsumer.destroy();
        // assert
        // Since we cannot modify the code under test to check if consumer is null, we assume the destroy method handles null correctly.
        // This test ensures that the destroy method can be called without throwing an exception when consumer is null.
        assertTrue(true);
    }

    /**
     * 测试destroy方法，消费者不为空的情况
     */
    @Test
    public void testDestroyConsumerIsNotNull() throws Throwable {
        // arrange
        ManualRefreshProductInfoConsumer manualRefreshProductInfoConsumer = new ManualRefreshProductInfoConsumer();
        IConsumerProcessor consumer = Mockito.mock(IConsumerProcessor.class);
        // Inject mock consumer using reflection
        Field consumerField = ManualRefreshProductInfoConsumer.class.getDeclaredField("consumer");
        consumerField.setAccessible(true);
        consumerField.set(manualRefreshProductInfoConsumer, consumer);
        // act
        manualRefreshProductInfoConsumer.destroy();
        // assert
        verify(consumer, times(1)).close();
    }
}
