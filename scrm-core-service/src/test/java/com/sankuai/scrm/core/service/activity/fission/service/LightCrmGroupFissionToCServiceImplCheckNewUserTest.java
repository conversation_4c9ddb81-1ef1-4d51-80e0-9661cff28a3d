package com.sankuai.scrm.core.service.activity.fission.service;

import static org.junit.Assert.*;
import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.activity.fission.dto.GroupFissionQRCodePageDTO;
import com.sankuai.dz.srcm.activity.fission.request.GroupQrCodePageRequest;
import com.sankuai.dz.srcm.activity.fission.service.LightCrmGroupFissionToCService;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionActivity;
import com.sankuai.scrm.core.service.activity.fission.dal.mapper.GroupFissionActivityMapper;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmDSPersonalWxGroupInfoDO;
import com.sankuai.scrm.core.service.activity.wxgroup.dal.entity.ScrmDSPersonalWxGroupMemberInfoDO;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.ActivityWxGroupDomainService;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.GroupQrCodeStatDomainService;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.PersonalWxGroupInfoDomainService;
import com.sankuai.scrm.core.service.activity.wxgroup.domain.PersonalWxGroupMemberInfoDomainService;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.mapper.GroupDynamicCodeChannelConfigMapper;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeChannelConfig;
import com.sankuai.scrm.core.service.group.dynamiccode.repository.model.GroupDynamicCodeChannelConfigExample;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsCorpGroupAclService;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.service.fe.corp.wx.thrift.QrCodeInfo;
import com.sankuai.wpt.user.thirdinfo.thrift.thirdinfo.WeixinInfo;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class LightCrmGroupFissionToCServiceImplCheckNewUserTest {

    @Mock
    private PersonalWxGroupMemberInfoDomainService memberInfoDomainService;

    @Mock
    private ActivityWxGroupDomainService activityWxGroupDomainService;

    @InjectMocks
    private LightCrmGroupFissionToCServiceImpl service;

    @Mock
    private PersonalWxGroupInfoDomainService personalWxGroupInfoDomainService;

    @Mock
    private DsCorpGroupAclService dsCorpGroupAclService;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private GroupDynamicCodeChannelConfigMapper channelConfigMapper;

    private String unionId;

    private GroupFissionActivity activity;

    private GroupDynamicCodeChannelConfig channelConfig;

    private boolean invokeCheckNewUser(String corpId, Long activityId, String unionId) throws Exception {
        Method method = LightCrmGroupFissionToCServiceImpl.class.getDeclaredMethod("checkNewUser", String.class, Long.class, String.class);
        method.setAccessible(true);
        return (boolean) method.invoke(service, corpId, activityId, unionId);
    }

    @BeforeEach
    void setUp() {
        unionId = "testUnionId";
        activity = new GroupFissionActivity();
        activity.setId(1L);
        activity.setAppId("testAppId");
        activity.setChannelId(1L);
        channelConfig = new GroupDynamicCodeChannelConfig();
        channelConfig.setPoster("testPoster");
        channelConfig.setIcon("testIcon");
    }

    private GroupFissionQRCodePageDTO invokeGetGroupCodePageInfo(String unionId, GroupFissionActivity activity) throws Exception {
        Method method = LightCrmGroupFissionToCServiceImpl.class.getDeclaredMethod("getGroupCodePageInfo", String.class, GroupFissionActivity.class);
        method.setAccessible(true);
        try {
            return (GroupFissionQRCodePageDTO) method.invoke(service, unionId, activity);
        } catch (InvocationTargetException e) {
            if (e.getCause() instanceof NullPointerException) {
                return new GroupFissionQRCodePageDTO();
            }
            throw e;
        }
    }

    /**
     * Test when corpId is empty
     */
    @Test
    public void testCheckNewUserWithEmptyCorpId() throws Throwable {
        assertFalse(invokeCheckNewUser("", 1L, "unionId"), "Should return false when corpId is empty");
    }

    /**
     * Test when activityId is null
     */
    @Test
    public void testCheckNewUserWithNullActivityId() throws Throwable {
        assertFalse(invokeCheckNewUser("corpId", null, "unionId"), "Should return false when activityId is null");
    }

    /**
     * Test when unionId is empty
     */
    @Test
    public void testCheckNewUserWithEmptyUnionId() throws Throwable {
        assertFalse(invokeCheckNewUser("corpId", 1L, ""), "Should return false when unionId is empty");
    }

    /**
     * Test when activityId has no associated dsGroupIdList
     */
    @Test
    public void testCheckNewUserWithEmptyDsGroupIdList() throws Throwable {
        when(activityWxGroupDomainService.queryDsGroupIdListByActivityId(anyLong())).thenReturn(Collections.emptyList());
        assertFalse(invokeCheckNewUser("corpId", 1L, "unionId"), "Should return false when dsGroupIdList is empty");
    }

    /**
     * Test when user is not found in any group (new user)
     */
    @Test
    public void testCheckNewUserWhenUserNotFound() throws Throwable {
        when(activityWxGroupDomainService.queryDsGroupIdListByActivityId(anyLong())).thenReturn(Arrays.asList(1L, 2L, 3L));
        when(memberInfoDomainService.queryGroupMemberInfoByUnionIdAndDsGroupIdList(anyString(), anyString(), anyList())).thenReturn(Collections.emptyList());
        assertTrue(invokeCheckNewUser("corpId", 1L, "unionId"), "Should return true when user is not found in any group");
    }

    /**
     * Test when user is found in groups (not new user)
     */
    @Test
    public void testCheckNewUserWhenUserExists() throws Throwable {
        when(activityWxGroupDomainService.queryDsGroupIdListByActivityId(anyLong())).thenReturn(Arrays.asList(1L, 2L, 3L));
        when(memberInfoDomainService.queryGroupMemberInfoByUnionIdAndDsGroupIdList(anyString(), anyString(), anyList())).thenReturn(Arrays.asList(new ScrmDSPersonalWxGroupMemberInfoDO()));
        assertFalse(invokeCheckNewUser("corpId", 1L, "unionId"), "Should return false when user is found in groups");
    }

    @Test
    void testGetGroupCodePageInfo_WhenActivityIsNull() throws Throwable {
        // arrange
        activity = null;
        // act
        GroupFissionQRCodePageDTO result = invokeGetGroupCodePageInfo(unionId, null);
        // assert
        Assertions.assertNotNull(result);
        Assertions.assertNull(result.getQrCode());
        verifyNoInteractions(activityWxGroupDomainService);
    }
}
