package com.sankuai.scrm.core.service.couponIntegration.service.impl;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.meituan.beauty.fundamental.light.remote.RemoteResponse;
import com.sankuai.dz.srcm.basic.dto.PageRemoteResponse;
import com.sankuai.dz.srcm.couponIntegration.dto.CouponDataDetailDTO;
import com.sankuai.dz.srcm.couponIntegration.dto.CouponDataSummaryDTO;
import com.sankuai.dz.srcm.couponIntegration.request.AuthenticationedAppIdsQueryRequest;
import com.sankuai.dz.srcm.couponIntegration.request.CouponDataDetailDownloadRequest;
import com.sankuai.dz.srcm.couponIntegration.request.CouponDataDetailQueryRequest;
import com.sankuai.dz.srcm.couponIntegration.request.CouponDataSummaryDownloadRequest;
import com.sankuai.dz.srcm.couponIntegration.request.CouponDataSummaryQueryRequest;
import com.sankuai.scrm.core.service.couponIntegration.domain.CouponDashBoardDomainService;
import com.sankuai.scrm.core.service.couponIntegration.service.impl.CouponDataServiceImpl;
import com.sankuai.scrm.core.service.pchat.service.activity.DownloadExcelDataService;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class CouponDataServiceImplTest {

    @InjectMocks
    private CouponDataServiceImpl couponDataService;

    @Mock
    private CouponDashBoardDomainService couponDashBoardDomainService;

    @Mock
    private DownloadExcelDataService downloadExcelDataService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试正常情况
     */
    @Test
    public void testQueryCouponDataSummaryNormal() throws Throwable {
        // arrange
        CouponDataSummaryQueryRequest request = new CouponDataSummaryQueryRequest();
        request.setAppId("appId");
        List<CouponDataSummaryDTO> couponDataSummaryDTOS = new ArrayList<>();
        when(couponDashBoardDomainService.querySummaryByRequest(request)).thenReturn(couponDataSummaryDTOS);
        // act
        RemoteResponse<List<CouponDataSummaryDTO>> response = couponDataService.queryCouponDataSummary(request);
        // assert
        assertEquals(couponDataSummaryDTOS, response.getData());
        verify(couponDashBoardDomainService, times(1)).querySummaryByRequest(request);
    }

    /**
     * 测试请求参数为空的情况
     */
    @Test
    public void testQueryCouponDataSummaryRequestIsNull() throws Throwable {
        // arrange
        CouponDataSummaryQueryRequest request = null;
        // act
        RemoteResponse<List<CouponDataSummaryDTO>> response = couponDataService.queryCouponDataSummary(request);
        // assert
        assertNotNull(response);
        assertEquals("请求参数不能为空", response.getMsg());
    }

    /**
     * 测试appId为空的情况
     */
    @Test
    public void testQueryCouponDataSummaryAppIdIsNull() throws Throwable {
        // arrange
        CouponDataSummaryQueryRequest request = new CouponDataSummaryQueryRequest();
        request.setAppId(null);
        // act
        RemoteResponse<List<CouponDataSummaryDTO>> response = couponDataService.queryCouponDataSummary(request);
        // assert
        assertNotNull(response);
        assertEquals("appId不能为空", response.getMsg());
    }

    /**
     * 测试couponDashBoardDomainService.querySummaryByRequest(request)方法抛出异常的情况
     */
    @Test
    public void testQueryCouponDataSummaryException() throws Throwable {
        // arrange
        CouponDataSummaryQueryRequest request = new CouponDataSummaryQueryRequest();
        request.setAppId("appId");
        when(couponDashBoardDomainService.querySummaryByRequest(request)).thenThrow(new RuntimeException());
        // act
        RemoteResponse<List<CouponDataSummaryDTO>> response = couponDataService.queryCouponDataSummary(request);
        // assert
        assertNotNull(response);
        assertEquals("系统异常", response.getMsg());
        verify(couponDashBoardDomainService, times(1)).querySummaryByRequest(request);
    }

    @Test
    public void testDownloadCouponDataDetailRequestIsNull() throws Throwable {
        RemoteResponse<String> response = couponDataService.downloadCouponDataDetail(null);
        assertEquals("请求参数不能为空", response.getMsg());
    }

    @Test
    public void testDownloadCouponDataDetailPathAppIdIsEmpty() throws Throwable {
        CouponDataDetailDownloadRequest request = new CouponDataDetailDownloadRequest();
        request.setPathAppId("");
        RemoteResponse<String> response = couponDataService.downloadCouponDataDetail(request);
        assertEquals("pathAppId不能为空", response.getMsg());
    }

    @Test
    public void testDownloadCouponDataDetailCouponDataDetailDTOSIsEmpty() throws Throwable {
        CouponDataDetailDownloadRequest request = new CouponDataDetailDownloadRequest();
        request.setPathAppId("pathAppId");
        when(couponDashBoardDomainService.queryDetailByRequest(request)).thenReturn(new ArrayList<>());
        RemoteResponse<String> response = couponDataService.downloadCouponDataDetail(request);
        assertEquals("没有数据", response.getMsg());
    }

    @Test
    public void testDownloadCouponDataDetailCouponDataDetailDTOSIsNotEmpty() throws Throwable {
        CouponDataDetailDownloadRequest request = new CouponDataDetailDownloadRequest();
        request.setPathAppId("pathAppId");
        List<CouponDataDetailDTO> couponDataDetailDTOS = new ArrayList<>();
        CouponDataDetailDTO dto = new CouponDataDetailDTO();
        // Fix: Set userId to avoid NullPointerException
        dto.setUserId(123L);
        couponDataDetailDTOS.add(dto);
        when(couponDashBoardDomainService.queryDetailByRequest(request)).thenReturn(couponDataDetailDTOS);
        when(downloadExcelDataService.download(anyList(), anyList())).thenReturn("downloadUrl");
        RemoteResponse<String> response = couponDataService.downloadCouponDataDetail(request);
        assertEquals("downloadUrl", response.getData());
    }

    @Test
    public void testDownloadCouponDataSummaryRequestIsNull() throws Throwable {
        CouponDataSummaryDownloadRequest request = null;
        RemoteResponse<String> response = couponDataService.downloadCouponDataSummary(request);
        assertEquals("请求参数不能为空", response.getMsg());
    }

    @Test
    public void testDownloadCouponDataSummaryAppIdIsEmpty() throws Throwable {
        CouponDataSummaryDownloadRequest request = new CouponDataSummaryDownloadRequest();
        request.setAppId("");
        RemoteResponse<String> response = couponDataService.downloadCouponDataSummary(request);
        assertEquals("appId不能为空", response.getMsg());
    }

    @Test
    public void testDownloadCouponDataSummaryListIsEmpty() throws Throwable {
        CouponDataSummaryDownloadRequest request = new CouponDataSummaryDownloadRequest();
        request.setAppId("appId");
        when(couponDashBoardDomainService.querySummaryByRequest(any(CouponDataSummaryDownloadRequest.class))).thenReturn(Collections.emptyList());
        RemoteResponse<String> response = couponDataService.downloadCouponDataSummary(request);
        assertEquals("没有数据", response.getMsg());
    }

    @Test
    public void testDownloadCouponDataSummaryListIsNotEmpty() throws Throwable {
        CouponDataSummaryDownloadRequest request = new CouponDataSummaryDownloadRequest();
        request.setAppId("appId");
        CouponDataSummaryDTO dto = new CouponDataSummaryDTO();
        dto.setCouponSendCount(10L);
        dto.setCouponSendAmount(new BigDecimal("100.0"));
        dto.setCouponUseCount(5L);
        dto.setCouponUseAmount(new BigDecimal("50.0"));
        List<CouponDataSummaryDTO> couponDataSummaryDTOS = Collections.singletonList(dto);
        when(couponDashBoardDomainService.querySummaryByRequest(any(CouponDataSummaryDownloadRequest.class))).thenReturn(couponDataSummaryDTOS);
        RemoteResponse<String> response = couponDataService.downloadCouponDataSummary(request);
        assertEquals("success", response.getMsg());
    }

    /**
     * 测试request为null的情况
     */
    @Test
    public void testQueryAuthenticationedAppIdsRequestIsNull() {
        RemoteResponse<List<String>> response = couponDataService.queryAuthenticationedAppIds(null);
        assertEquals("请求参数错误", response.getMsg());
    }

    /**
     * 测试getAuthenticationedAppIds方法抛出异常的情况
     */
    @Test
    public void testQueryAuthenticationedAppIdsException() {
        AuthenticationedAppIdsQueryRequest request = new AuthenticationedAppIdsQueryRequest();
        request.setAccessToken("accessToken");
        request.setAppId("appId");
        when(couponDashBoardDomainService.getAuthenticationedAppIds(anyString(), anyString())).thenThrow(new RuntimeException());
        RemoteResponse<List<String>> response = couponDataService.queryAuthenticationedAppIds(request);
        assertEquals("系统异常", response.getMsg());
    }

    /**
     * 测试getAuthenticationedAppIds方法返回空列表的情况
     */
    @Test
    public void testQueryAuthenticationedAppIdsEmptyList() {
        AuthenticationedAppIdsQueryRequest request = new AuthenticationedAppIdsQueryRequest();
        request.setAccessToken("accessToken");
        request.setAppId("appId");
        when(couponDashBoardDomainService.getAuthenticationedAppIds(anyString(), anyString())).thenReturn(Collections.emptyList());
        RemoteResponse<List<String>> response = couponDataService.queryAuthenticationedAppIds(request);
        assertEquals(Collections.emptyList(), response.getData());
    }

    /**
     * 测试getAuthenticationedAppIds方法返回非空列表的情况
     */
    @Test
    public void testQueryAuthenticationedAppIdsNonEmptyList() {
        AuthenticationedAppIdsQueryRequest request = new AuthenticationedAppIdsQueryRequest();
        request.setAccessToken("accessToken");
        request.setAppId("appId");
        List<String> authenticationedAppIds = Collections.singletonList("appId");
        when(couponDashBoardDomainService.getAuthenticationedAppIds(anyString(), anyString())).thenReturn(authenticationedAppIds);
        RemoteResponse<List<String>> response = couponDataService.queryAuthenticationedAppIds(request);
        assertEquals(authenticationedAppIds, response.getData());
    }

    @Test
    public void testQueryCouponDataDetailRequestIsNull() throws Throwable {
        PageRemoteResponse<CouponDataDetailDTO> response = couponDataService.queryCouponDataDetail(null);
        assertEquals(401, response.getCode());
        assertEquals("请求参数错误", response.getMsg());
    }

    @Test
    public void testQueryCouponDataDetailPageNumberIsZero() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageNumber(0);
        PageRemoteResponse<CouponDataDetailDTO> response = couponDataService.queryCouponDataDetail(request);
        assertEquals(401, response.getCode());
        assertEquals("请求参数错误", response.getMsg());
    }

    @Test
    public void testQueryCouponDataDetailPageSizeIsNull() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(null);
        PageRemoteResponse<CouponDataDetailDTO> response = couponDataService.queryCouponDataDetail(request);
        assertEquals(401, response.getCode());
        assertEquals("请求参数错误", response.getMsg());
    }

    @Test
    public void testQueryCouponDataDetailPageSizeIsZero() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(0);
        PageRemoteResponse<CouponDataDetailDTO> response = couponDataService.queryCouponDataDetail(request);
        assertEquals(401, response.getCode());
        assertEquals("请求参数错误", response.getMsg());
    }

    @Test
    public void testQueryCouponDataDetailPageNumberIsNull() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageNumber(null);
        PageRemoteResponse<CouponDataDetailDTO> response = couponDataService.queryCouponDataDetail(request);
        assertEquals(401, response.getCode());
        assertEquals("请求参数错误", response.getMsg());
    }

    @Test
    public void testQueryCouponDataDetailPathAppIdIsEmpty() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPathAppId("");
        PageRemoteResponse<CouponDataDetailDTO> response = couponDataService.queryCouponDataDetail(request);
        assertEquals(401, response.getCode());
        assertEquals("请求参数错误", response.getMsg());
    }

    @Test
    public void testQueryCouponDataDetailSuccess() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(10);
        request.setPageNumber(1);
        request.setPathAppId("validAppId");
        when(couponDashBoardDomainService.queryDetailByRequest(any(CouponDataDetailQueryRequest.class))).thenReturn(Collections.emptyList());
        when(couponDashBoardDomainService.countDetailByRequest(any(CouponDataDetailQueryRequest.class))).thenReturn(0L);
        PageRemoteResponse<CouponDataDetailDTO> response = couponDataService.queryCouponDataDetail(request);
        assertEquals(200, response.getCode());
        assertEquals("success", response.getMsg());
    }

    @Test
    public void testQueryCouponDataDetailQueryDetailByRequestThrowsException() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(10);
        request.setPageNumber(1);
        request.setPathAppId("validAppId");
        when(couponDashBoardDomainService.queryDetailByRequest(any(CouponDataDetailQueryRequest.class))).thenThrow(new RuntimeException());
        PageRemoteResponse<CouponDataDetailDTO> response = couponDataService.queryCouponDataDetail(request);
        assertEquals(400, response.getCode());
        assertEquals("系统异常", response.getMsg());
    }

    @Test
    public void testQueryCouponDataDetailCountDetailByRequestThrowsException() throws Throwable {
        CouponDataDetailQueryRequest request = new CouponDataDetailQueryRequest();
        request.setPageSize(10);
        request.setPageNumber(1);
        request.setPathAppId("validAppId");
        when(couponDashBoardDomainService.queryDetailByRequest(any(CouponDataDetailQueryRequest.class))).thenReturn(Collections.emptyList());
        when(couponDashBoardDomainService.countDetailByRequest(any(CouponDataDetailQueryRequest.class))).thenThrow(new RuntimeException());
        PageRemoteResponse<CouponDataDetailDTO> response = couponDataService.queryCouponDataDetail(request);
        assertEquals(400, response.getCode());
        assertEquals("系统异常", response.getMsg());
    }
}