package com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool;

import com.sankuai.mpproduct.idservice.api.enums.BizProductIdType;
import com.sankuai.mpproduct.idservice.api.request.BizProductIdConvertRequest;
import com.sankuai.mpproduct.idservice.api.response.BizProductIdConvertResponse;
import com.sankuai.mpproduct.idservice.api.service.IdService;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.Assertions;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.MockitoAnnotations;

import java.util.Arrays;
import java.util.Collections;
import java.util.HashMap;
import java.util.Map;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

public class ProductIdServiceTest {

    @InjectMocks
    private ProductIdService productIdService;

    @Mock(lenient = true)
    private IdService idService;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试商品ID存在的情况
     */
    @Test
    public void testIsProductIdExistsWhenIdExists() throws Exception {
        BizProductIdConvertResponse response = new BizProductIdConvertResponse();
        Map<Long, Long> resultMap = new HashMap<>();
        resultMap.put(1L, 2L);
        response.setBizProductIdConvertResult(resultMap);
        Mockito.when(idService.convertBizProductIdsToProductIds(any(BizProductIdConvertRequest.class))).thenReturn(response);
        boolean result = productIdService.isProductIdExists(1L, BizProductIdType.DEAL_ID);
        Assertions.assertTrue(result);
    }

    /**
     * 测试商品ID不存在的情况
     */
    @Test
    public void testIsProductIdExistsWhenIdDoesNotExist() throws Exception {
        BizProductIdConvertResponse response = new BizProductIdConvertResponse();
        response.setBizProductIdConvertResult(new HashMap<>());
        when(idService.convertBizProductIdsToProductIds(any(BizProductIdConvertRequest.class))).thenReturn(response);
        boolean result = productIdService.isProductIdExists(1L, BizProductIdType.DEAL_ID);
        Assertions.assertFalse(result);
    }

    /**
     * 测试当服务返回null的情况
     */
    @Test
    public void testIsProductIdExistsWhenServiceReturnsNull() throws Exception {
        when(idService.convertBizProductIdsToProductIds(any(BizProductIdConvertRequest.class))).thenReturn(null);
        boolean result = productIdService.isProductIdExists(1L, BizProductIdType.DEAL_ID);
        Assertions.assertFalse(result);
    }

    /**
     * 测试商品ID列表为空
     */
    @Test
    public void testIsProductIdsExistWithEmptyList() {
        Assertions.assertNull(productIdService.isProductIdsExist(Collections.emptyList(), BizProductIdType.DEAL_ID));
    }

    /**
     * 测试服务返回null
     */
    @Test
    public void testIsProductIdsExistWithServiceReturnsNull() throws Exception {
        when(idService.convertBizProductIdsToProductIds(any())).thenReturn(null);
        Assertions.assertNull(productIdService.isProductIdsExist(Arrays.asList(1L, 2L), BizProductIdType.DEAL_ID));
    }

    /**
     * 测试服务返回空映射
     */
    @Test
    public void testIsProductIdsExistWithServiceReturnsEmptyMap() throws Exception {
        BizProductIdConvertResponse response = new BizProductIdConvertResponse();
        response.setBizProductIdConvertResult(Collections.emptyMap());
        when(idService.convertBizProductIdsToProductIds(any())).thenReturn(response);
        Map<Long, Boolean> result = productIdService.isProductIdsExist(Arrays.asList(1L, 2L), BizProductIdType.DEAL_ID);
        Assertions.assertNull(result);
    }

    /**
     * 测试服务正常返回，所有ID存在
     */
    @Test
    public void testIsProductIdsExistWithAllIdsExist() throws Exception {
        Map<Long, Long> convertResult = new HashMap<>();
        convertResult.put(1L, 10L);
        convertResult.put(2L, 20L);
        BizProductIdConvertResponse response = new BizProductIdConvertResponse();
        response.setBizProductIdConvertResult(convertResult);
        when(idService.convertBizProductIdsToProductIds(any())).thenReturn(response);
        Map<Long, Boolean> result = productIdService.isProductIdsExist(Arrays.asList(1L, 2L), BizProductIdType.DEAL_ID);
        Assertions.assertNotNull(result);
        Assertions.assertFalse(result.isEmpty());
        Assertions.assertTrue(result.get(1L));
        Assertions.assertTrue(result.get(2L));
    }

    /**
     * 测试服务正常返回，部分ID不存在
     */
    @Test
    public void testIsProductIdsExistWithSomeIdsNotExist() throws Exception {
        Map<Long, Long> convertResult = new HashMap<>();
        convertResult.put(1L, null);
        convertResult.put(2L, 20L);
        BizProductIdConvertResponse response = new BizProductIdConvertResponse();
        response.setBizProductIdConvertResult(convertResult);
        when(idService.convertBizProductIdsToProductIds(any())).thenReturn(response);
        Map<Long, Boolean> result = productIdService.isProductIdsExist(Arrays.asList(1L, 2L), BizProductIdType.DEAL_ID);
        Assertions.assertNotNull(result);
        Assertions.assertFalse(result.isEmpty());
        Assertions.assertFalse(result.get(1L));
        Assertions.assertTrue(result.get(2L));
    }

    @Test
    public void testIsProductIdExistsProductIdIsNull() throws Throwable {
        Assertions.assertFalse(productIdService.isProductIdExists(null, BizProductIdType.DEAL_ID));
    }

    @Test
    public void testIsProductIdExistsBizProductIdTypeIsNull() throws Throwable {
        Assertions.assertFalse(productIdService.isProductIdExists(1L, null));
    }

    @Test
    public void testIsProductIdExistsConvertBizProductIdsToProductIdsReturnsNull() throws Throwable {
        when(idService.convertBizProductIdsToProductIds(any(BizProductIdConvertRequest.class))).thenReturn(null);
        Assertions.assertFalse(productIdService.isProductIdExists(1L, BizProductIdType.DEAL_ID));
    }

    @Test
    public void testIsProductIdExistsBizProductIdConvertResultIsNull() throws Throwable {
        BizProductIdConvertResponse response = new BizProductIdConvertResponse();
        when(idService.convertBizProductIdsToProductIds(any(BizProductIdConvertRequest.class))).thenReturn(response);
        Assertions.assertFalse(productIdService.isProductIdExists(1L, BizProductIdType.DEAL_ID));
    }

    @Test
    public void testIsProductIdExistsProductIdNotInBizProductIdConvertResult() throws Throwable {
        BizProductIdConvertResponse response = new BizProductIdConvertResponse();
        Map<Long, Long> bizProductIdConvertResult = new HashMap<>();
        bizProductIdConvertResult.put(2L, 2L);
        response.setBizProductIdConvertResult(bizProductIdConvertResult);
        when(idService.convertBizProductIdsToProductIds(any(BizProductIdConvertRequest.class))).thenReturn(response);
        Assertions.assertFalse(productIdService.isProductIdExists(1L, BizProductIdType.DEAL_ID));
    }

    @Test
    public void testIsProductIdExistsProductIdInBizProductIdConvertResult() throws Throwable {
        BizProductIdConvertResponse response = new BizProductIdConvertResponse();
        Map<Long, Long> bizProductIdConvertResult = new HashMap<>();
        bizProductIdConvertResult.put(1L, 1L);
        response.setBizProductIdConvertResult(bizProductIdConvertResult);
        when(idService.convertBizProductIdsToProductIds(any(BizProductIdConvertRequest.class))).thenReturn(response);
        Assertions.assertTrue(productIdService.isProductIdExists(1L, BizProductIdType.DEAL_ID));
    }
}
