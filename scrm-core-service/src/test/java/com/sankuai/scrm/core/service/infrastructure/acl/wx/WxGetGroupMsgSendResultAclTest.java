package com.sankuai.scrm.core.service.infrastructure.acl.wx;

import com.dianping.baby.http.HttpClientUtil;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.WxGetGroupmsgSendResultRequest;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGetGroupmsgSendResultResponse;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;
import java.io.IOException;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import static org.junit.Assert.*;

@RunWith(MockitoJUnitRunner.class)
public class WxGetGroupMsgSendResultAclTest {

    @InjectMocks
    private WxGetGroupMsgSendResultAcl wxGetGroupMsgSendResultAcl;

    private WxGetGroupmsgSendResultRequest request;

    private String appId;

    private String token;

    @Before
    public void setUp() {
        request = new WxGetGroupmsgSendResultRequest();
        appId = "appId";
        token = "token";
    }

    @Test(expected = Exception.class)
    public void testGetGroupMsgSendResultAppIdIsNull() throws Throwable {
        wxGetGroupMsgSendResultAcl.getGroupMsgSendResult(request, null, token);
    }

    @Test(expected = Exception.class)
    public void testGetGroupMsgSendResultTokenIsNull() throws Throwable {
        wxGetGroupMsgSendResultAcl.getGroupMsgSendResult(request, appId, null);
    }

    @Test
    public void testGetGroupMsgSendResultIOException() throws Throwable {
        try (MockedStatic<HttpClientUtil> mockedStatic = mockStatic(HttpClientUtil.class)) {
            mockedStatic.when(() -> HttpClientUtil.post(anyString(), anyString(), anyMap())).thenThrow(new IOException());
            WxGetGroupmsgSendResultResponse response = wxGetGroupMsgSendResultAcl.getGroupMsgSendResult(request, appId, token);
            assertNull(response);
        }
    }
}
