package com.sankuai.scrm.core.service.external.contact.domain;

import com.sankuai.scrm.core.service.external.contact.dal.babymapper.ContactUserMapper;
import com.sankuai.scrm.core.service.external.contact.dal.entity.ContactUser;
import com.sankuai.scrm.core.service.external.contact.dal.example.ContactUserExample;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;
import org.junit.Before;

@RunWith(MockitoJUnitRunner.class)
public class ContactUserDomainTest {

    @InjectMocks
    private ContactUserDomain contactUserDomain;

    @Mock
    private ContactUserMapper contactUserMapper;

    /**
     * Tests getContactUsersByCorpIdAndUnionIdAndExternalUserIdFromMaster method under normal conditions.
     */
    @Test
    public void testGetContactUsersByCorpIdAndUnionIdAndExternalUserIdFromMaster_Normal() throws Throwable {
        // Arrange
        String corpId = "corpId";
        String unionId = "unionId";
        String externalUserId = "externalUserId";
        List<ContactUser> expected = Collections.singletonList(new ContactUser());
        when(contactUserMapper.selectByExample(any(ContactUserExample.class))).thenReturn(expected);
        // Act
        List<ContactUser> actual = contactUserDomain.getContactUsersByCorpIdAndUnionIdAndExternalUserIdFromMaster(corpId, unionId, externalUserId);
        // Assert
        assertEquals(expected, actual);
    }

    /**
     * Tests getContactUsersByCorpIdAndUnionIdAndExternalUserIdFromMaster method under exception conditions.
     */
    @Test
    public void testGetContactUsersByCorpIdAndUnionIdAndExternalUserIdFromMaster_Exception() throws Throwable {
        // Arrange
        String corpId = "corpId";
        String unionId = "unionId";
        String externalUserId = "externalUserId";
        when(contactUserMapper.selectByExample(any(ContactUserExample.class))).thenReturn(Collections.emptyList());
        // Act
        List<ContactUser> actual = contactUserDomain.getContactUsersByCorpIdAndUnionIdAndExternalUserIdFromMaster(corpId, unionId, externalUserId);
        // Assert
        assertEquals(Collections.emptyList(), actual);
    }
}
