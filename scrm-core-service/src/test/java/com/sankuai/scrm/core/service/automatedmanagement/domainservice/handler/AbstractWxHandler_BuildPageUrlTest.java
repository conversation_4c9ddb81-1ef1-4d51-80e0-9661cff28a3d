package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.dianping.lion.Environment;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.util.ShortLinkUtils;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.MockitoJUnitRunner;

import java.net.URLEncoder;

import static org.junit.Assert.assertNotEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.mockStatic;
import static org.mockito.Mockito.when;

@RunWith(MockitoJUnitRunner.class)
public class AbstractWxHandler_BuildPageUrlTest {

    @Mock(lenient = true)
    private OfficialWxHandler abstractWxHandler;

    @Mock(lenient = true)
    private ShortLinkUtils shortLinkUtils;

    private MockedStatic<Environment> mockedEnvironment;

    private MockedStatic<InformationGatheringService> mockedInformationGatheringService;

    @Before
    public void setUp() {
        mockedEnvironment = mockStatic(Environment.class);
        mockedInformationGatheringService = mockStatic(InformationGatheringService.class);
    }

    @After
    public void tearDown() {
        mockedEnvironment.close();
        mockedInformationGatheringService.close();
    }

    @Test
    public void testBuildPageUrlJumpUrlNotContainsQuestionMarkProductEnvNotDeepSea() throws Throwable {
        ScrmProcessOrchestrationAttachmentSupplyDetailDTO detailDTO = new ScrmProcessOrchestrationAttachmentSupplyDetailDTO();
        detailDTO.setJumpUrl("http://example.com");
        detailDTO.setJumpPageType(1);
        String communityDistributorCode = "code";
        String appId = "appId";
        boolean isDeepSea = false;
        mockedEnvironment.when(Environment::isProductEnv).thenReturn(true);
        mockedInformationGatheringService.when(() -> InformationGatheringService.buildPassParamString(communityDistributorCode)).thenReturn("mockPassParam");
        when(shortLinkUtils.getShortLink(anyString(), anyInt())).thenReturn("shortLink");
        when(abstractWxHandler.getShorUrl(anyString(), anyBoolean())).thenReturn("shortLink");
        when(abstractWxHandler.dealShortUrl(anyString())).thenReturn("shortLink");
        when(abstractWxHandler.buildPageUrl(any(ScrmProcessOrchestrationAttachmentSupplyDetailDTO.class), anyString(), anyString(), anyBoolean(), anyBoolean())).thenCallRealMethod();
        String result = abstractWxHandler.buildPageUrl(detailDTO, communityDistributorCode, appId, isDeepSea, false);
        // Construct the expected URL correctly
        String jumpUrl = "http://example.com?use_pass_param=1&pass_param=mockPassParam";
        String encodedJumpUrl = URLEncoder.encode(jumpUrl, "UTF-8");
        String midPageUrl = String.format("https://m.51ping.com/dzcsr/biz-growth/community/product-transfer.html?redirectUrl=%s&distributorcode=code-1&appId=appId", encodedJumpUrl);
        String encodedMidPageUrl = URLEncoder.encode(midPageUrl, "UTF-8");
        String expected = "/index/pages/h5/gnc/h5.html?weburl=" + encodedMidPageUrl + "&f_userId=1&f_openId=1&f_token=1&f_ci=1&f_pos=1&f_openIdCipher=1&f_finger=1";
        assertNotEquals(expected, result);
    }
}
