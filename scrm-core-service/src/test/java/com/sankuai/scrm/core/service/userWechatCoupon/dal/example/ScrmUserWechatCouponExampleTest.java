package com.sankuai.scrm.core.service.userWechatCoupon.dal.example;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import ai.djl.repository.zoo.Criteria;
import java.util.List;
import org.junit.jupiter.api.DisplayName;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

/**
 * Unit tests for ScrmUserWechatCouponExample's createCriteriaInternal method
 */
@ExtendWith(MockitoExtension.class)
@DisplayName("ScrmUserWechatCouponExample CreateCriteriaInternal Tests")
public class ScrmUserWechatCouponExampleTest {

    @InjectMocks
    private ScrmUserWechatCouponExample example;

    @Mock
    private List<Criteria> mockOredCriteria;

    /**
     * Test case to verify createCriteriaInternal creates a new Criteria instance
     */
    @Test
    @DisplayName("Should create new Criteria instance")
    public void testCreateCriteriaInternal_ShouldCreateNewInstance() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample() {

            @Override
            protected Criteria createCriteriaInternal() {
                return super.createCriteriaInternal();
            }
        };
        // act
        ScrmUserWechatCouponExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertNotNull(result, "Created criteria should not be null");
        assertTrue(result instanceof ScrmUserWechatCouponExample.Criteria, "Result should be instance of Criteria");
    }

    /**
     * Test case to verify each call creates a new unique instance
     */
    @Test
    @DisplayName("Should create unique instances on multiple calls")
    public void testCreateCriteriaInternal_ShouldCreateUniqueInstances() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample() {

            @Override
            protected Criteria createCriteriaInternal() {
                return super.createCriteriaInternal();
            }
        };
        // act
        ScrmUserWechatCouponExample.Criteria result1 = example.createCriteriaInternal();
        ScrmUserWechatCouponExample.Criteria result2 = example.createCriteriaInternal();
        // assert
        assertNotNull(result1, "First created criteria should not be null");
        assertNotNull(result2, "Second created criteria should not be null");
        assertNotSame(result1, result2, "Each call should create a new instance");
    }

    /**
     * Test case to verify created Criteria has empty criteria list
     */
    @Test
    @DisplayName("Should create Criteria with empty criteria list")
    public void testCreateCriteriaInternal_ShouldHaveEmptyCriteriaList() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample() {

            @Override
            protected Criteria createCriteriaInternal() {
                return super.createCriteriaInternal();
            }
        };
        // act
        ScrmUserWechatCouponExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertNotNull(result.getCriteria(), "Criteria list should not be null");
        assertTrue(result.getCriteria().isEmpty(), "Criteria list should be empty");
    }

    /**
     * Test case to verify created Criteria inherits from GeneratedCriteria
     */
    @Test
    @DisplayName("Should create Criteria that extends GeneratedCriteria")
    public void testCreateCriteriaInternal_ShouldExtendGeneratedCriteria() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample() {

            @Override
            protected Criteria createCriteriaInternal() {
                return super.createCriteriaInternal();
            }
        };
        // act
        ScrmUserWechatCouponExample.Criteria result = example.createCriteriaInternal();
        // assert
        assertTrue(result instanceof ScrmUserWechatCouponExample.GeneratedCriteria, "Created Criteria should extend GeneratedCriteria");
    }

    /**
     * Test case to verify or() method creates new criteria and adds it to oredCriteria list
     */
    @Test
    @DisplayName("Test or() creates and adds new criteria")
    public void testOrCreatesAndAddsCriteria() {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int initialSize = example.getOredCriteria().size();
        // act
        ScrmUserWechatCouponExample.Criteria result = example.or();
        // assert
        assertNotNull(result, "Returned Criteria should not be null");
        assertEquals(initialSize + 1, example.getOredCriteria().size(), "OredCriteria list should increase by 1");
        assertSame(result, example.getOredCriteria().get(example.getOredCriteria().size() - 1), "The returned Criteria should be the last element in oredCriteria list");
    }

    /**
     * Test case to verify or() method creates distinct criteria instances
     */
    @Test
    @DisplayName("Test or() creates distinct criteria instances")
    public void testOrCreatesDifferentInstances() {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        // act
        ScrmUserWechatCouponExample.Criteria criteria1 = example.or();
        ScrmUserWechatCouponExample.Criteria criteria2 = example.or();
        // assert
        assertAll(() -> assertNotNull(criteria1, "First criteria should not be null"), () -> assertNotNull(criteria2, "Second criteria should not be null"), () -> assertNotSame(criteria1, criteria2, "Criteria instances should be different"), () -> assertEquals(2, example.getOredCriteria().size(), "Should have two criteria in list"));
    }

    /**
     * Test case to verify or() method preserves the order of criteria
     */
    @Test
    @DisplayName("Test or() preserves criteria order")
    public void testOrPreservesOrder() {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        // act
        ScrmUserWechatCouponExample.Criteria first = example.or();
        ScrmUserWechatCouponExample.Criteria second = example.or();
        ScrmUserWechatCouponExample.Criteria third = example.or();
        // assert
        assertAll(() -> assertSame(first, example.getOredCriteria().get(0), "First criteria should be at index 0"), () -> assertSame(second, example.getOredCriteria().get(1), "Second criteria should be at index 1"), () -> assertSame(third, example.getOredCriteria().get(2), "Third criteria should be at index 2"));
    }

    /**
     * Test case to verify or() method behavior with multiple consecutive calls
     */
    @Test
    @DisplayName("Test or() with multiple consecutive calls")
    public void testOrMultipleConsecutiveCalls() {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int numberOfCalls = 5;
        // act
        for (int i = 0; i < numberOfCalls; i++) {
            example.or();
        }
        // assert
        assertAll(() -> assertEquals(numberOfCalls, example.getOredCriteria().size(), "Should have exactly 5 criteria"), () -> assertTrue(example.getOredCriteria().stream().allMatch(c -> c != null), "All criteria should be non-null"), () -> assertEquals(example.getOredCriteria().size(), example.getOredCriteria().stream().distinct().count(), "All criteria should be unique instances"));
    }

    /**
     * Test case to verify or() method creates valid Criteria object
     */
    @Test
    @DisplayName("Test or() creates valid Criteria object")
    public void testOrCreatesValidCriteria() {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        // act
        ScrmUserWechatCouponExample.Criteria criteria = example.or();
        // assert
        assertAll(() -> assertNotNull(criteria, "Criteria should not be null"), () -> assertTrue(example.getOredCriteria().contains(criteria), "OredCriteria should contain the created criteria"), () -> assertEquals(1, example.getOredCriteria().size(), "Should have exactly one criteria"));
    }

    /**
     * 测试正常情况 - 正数行数限制
     */
    @Test
    @DisplayName("Should set rows and return this when positive rows provided")
    public void testLimitWithPositiveRows() {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int testRows = 10;
        // act
        ScrmUserWechatCouponExample result = example.limit(testRows);
        // assert
        assertEquals(testRows, result.getRows());
        assertSame(example, result);
    }

    /**
     * 测试边界情况 - 零行数限制
     */
    @Test
    @DisplayName("Should set rows to zero when zero provided")
    public void testLimitWithZeroRows() {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        // act
        ScrmUserWechatCouponExample result = example.limit(0);
        // assert
        assertEquals(0, result.getRows());
        assertSame(example, result);
    }

    /**
     * 测试边界情况 - 负数行数限制
     */
    @Test
    @DisplayName("Should set rows when negative value provided")
    public void testLimitWithNegativeRows() {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int testRows = -5;
        // act
        ScrmUserWechatCouponExample result = example.limit(testRows);
        // assert
        assertEquals(testRows, result.getRows());
        assertSame(example, result);
    }

    /**
     * 测试特殊情况 - null行数限制
     */
    @Test
    @DisplayName("Should set rows to null when null provided")
    public void testLimitWithNullRows() {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        // act
        ScrmUserWechatCouponExample result = example.limit(null);
        // assert
        assertNull(result.getRows());
        assertSame(example, result);
    }

    /**
     * 测试方法链式调用
     * 这里展示Mockito的基本用法，虽然对这个简单例子不是必要的
     */
    @Test
    @DisplayName("Should support method chaining")
    public void testLimitMethodChaining() {
        // arrange
        ScrmUserWechatCouponExample example = spy(new ScrmUserWechatCouponExample());
        // act
        ScrmUserWechatCouponExample result = example.limit(5).limit(10);
        // assert
        // 验证limit方法被调用了两次
        verify(example, times(2)).limit(anyInt());
        assertEquals(10, result.getRows());
        assertSame(example, result);
    }

    /**
     * 测试与offset方法的组合使用
     */
    @Test
    @DisplayName("Should work correctly with offset method")
    public void testLimitWithOffset() {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int testOffset = 20;
        int testRows = 10;
        // act
        ScrmUserWechatCouponExample result = example.limit(testOffset, testRows).limit(testRows);
        // assert
        assertEquals(testOffset, result.getOffset());
        assertEquals(testRows, result.getRows());
    }

    /**
     * 测试clear方法 - 重置所有字段到初始状态
     */
    @Test
    public void testClearResetsAllFields() throws Throwable {
        // arrange
        example.setOrderByClause("id desc");
        example.setDistinct(true);
        example.setOffset(10);
        example.setRows(20);
        // 对于void方法使用doNothing
        doNothing().when(mockOredCriteria).clear();
        // act
        example.clear();
        // assert
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertNull(example.getOffset());
        assertNull(example.getRows());
        // 验证oredCriteria.clear()被调用
        verify(mockOredCriteria).clear();
    }

    /**
     * 测试clear方法 - 当字段已经为null时调用clear
     */
    @Test
    public void testClearWhenFieldsAlreadyNull() throws Throwable {
        // arrange
        example.setOrderByClause(null);
        example.setDistinct(false);
        example.setOffset(null);
        example.setRows(null);
        doNothing().when(mockOredCriteria).clear();
        // act
        example.clear();
        // assert
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertNull(example.getOffset());
        assertNull(example.getRows());
        verify(mockOredCriteria).clear();
    }

    /**
     * 测试clear方法 - 多次调用clear方法
     */
    @Test
    public void testClearCalledMultipleTimes() throws Throwable {
        // arrange
        example.setOrderByClause("name asc");
        example.setDistinct(true);
        doNothing().when(mockOredCriteria).clear();
        // act
        example.clear();
        example.clear();
        // assert
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        // 验证clear被调用了两次
        verify(mockOredCriteria, times(2)).clear();
    }

    /**
     * 测试clear方法 - 验证distinct字段被正确重置
     */
    @Test
    public void testClearResetsDistinctField() throws Throwable {
        // arrange
        example.setDistinct(true);
        doNothing().when(mockOredCriteria).clear();
        // act
        example.clear();
        // assert
        assertFalse(example.isDistinct());
        verify(mockOredCriteria).clear();
    }

    /**
     * 测试clear方法 - 验证orderByClause字段被正确重置
     */
    @Test
    public void testClearResetsOrderByClause() throws Throwable {
        // arrange
        example.setOrderByClause("id desc");
        doNothing().when(mockOredCriteria).clear();
        // act
        example.clear();
        // assert
        assertNull(example.getOrderByClause());
        verify(mockOredCriteria).clear();
    }

    /**
     * Test page method with normal positive values
     * Expected: offset = page * pageSize, rows = pageSize
     */
    @Test
    @DisplayName("Should calculate correct offset and rows for positive values")
    public void testPage_WithPositiveValues() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int page = 2;
        int pageSize = 10;
        // act
        ScrmUserWechatCouponExample result = example.page(page, pageSize);
        // assert
        assertEquals(Integer.valueOf(page * pageSize), result.getOffset(), "Offset should be page * pageSize (2 * 10 = 20)");
        assertEquals(Integer.valueOf(pageSize), result.getRows(), "Rows should equal pageSize (10)");
        assertSame(example, result, "Should return the same instance");
    }

    /**
     * Test page method with zero values
     * Expected: offset = 0, rows = 0
     */
    @Test
    @DisplayName("Should handle zero values correctly")
    public void testPage_WithZeroValues() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int page = 0;
        int pageSize = 0;
        // act
        ScrmUserWechatCouponExample result = example.page(page, pageSize);
        // assert
        assertEquals(Integer.valueOf(0), result.getOffset(), "Offset should be 0 for page=0");
        assertEquals(Integer.valueOf(0), result.getRows(), "Rows should be 0 for pageSize=0");
        assertSame(example, result, "Should return the same instance");
    }

    /**
     * Test page method with maximum integer boundary values
     * Expected: offset = page * pageSize, rows = pageSize
     */
    @Test
    @DisplayName("Should handle maximum integer boundary values")
    public void testPage_WithMaximumValues() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int page = Integer.MAX_VALUE / 2;
        int pageSize = 2;
        int expectedOffset = page * pageSize;
        // act
        ScrmUserWechatCouponExample result = example.page(page, pageSize);
        // assert
        assertEquals(Integer.valueOf(expectedOffset), result.getOffset(), "Offset should be page * pageSize");
        assertEquals(Integer.valueOf(pageSize), result.getRows(), "Rows should equal pageSize");
        assertSame(example, result, "Should return the same instance");
    }

    /**
     * Test page method with null page parameter
     * Expected: throws NullPointerException
     */
    @Test
    @DisplayName("Should throw NullPointerException when page is null")
    public void testPage_WithNullPage() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        Integer pageSize = 10;
        // act & assert
        assertThrows(NullPointerException.class, () -> example.page(null, pageSize));
    }

    /**
     * Test page method with null pageSize parameter
     * Expected: throws NullPointerException
     */
    @Test
    @DisplayName("Should throw NullPointerException when pageSize is null")
    public void testPage_WithNullPageSize() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        Integer page = 2;
        // act & assert
        assertThrows(NullPointerException.class, () -> example.page(page, null));
    }

    /**
     * Test page method with both null parameters
     * Expected: throws NullPointerException
     */
    @Test
    @DisplayName("Should throw NullPointerException when both parameters are null")
    public void testPage_WithBothNullParameters() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        // act & assert
        assertThrows(NullPointerException.class, () -> example.page(null, null));
    }

    /**
     * Test page method with negative values
     * Expected: negative offset and rows values
     */
    @Test
    @DisplayName("Should handle negative values")
    public void testPage_WithNegativeValues() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int page = -1;
        int pageSize = -5;
        // act
        ScrmUserWechatCouponExample result = example.page(page, pageSize);
        // assert
        assertEquals(Integer.valueOf(page * pageSize), result.getOffset(), "Offset should be negative");
        assertEquals(Integer.valueOf(pageSize), result.getRows(), "Rows should be negative");
        assertSame(example, result, "Should return the same instance");
    }
}
