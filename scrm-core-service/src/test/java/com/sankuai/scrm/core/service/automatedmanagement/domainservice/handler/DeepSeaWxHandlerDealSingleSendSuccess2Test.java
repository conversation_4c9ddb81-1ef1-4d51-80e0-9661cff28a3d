package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import java.lang.reflect.Method;
import java.util.Date;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

public class DeepSeaWxHandlerDealSingleSendSuccess2Test {

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @InjectMocks
    private DeepSeaWxHandler deepSeaWxHandler;

    private Method dealSingleSendSuccessMethod;

    @BeforeEach
    public void setUp() throws Exception {
        MockitoAnnotations.openMocks(this);
        dealSingleSendSuccessMethod = DeepSeaWxHandler.class.getDeclaredMethod("dealSingleSendSuccess", String.class, Long.class, ScrmAmProcessOrchestrationWxInvokeLogDO.class, String.class);
        dealSingleSendSuccessMethod.setAccessible(true);
    }

    /**
     * Test normal execution of dealSingleSendSuccess method
     */
    @Test
    public void testDealSingleSendSuccess_NormalExecution() throws Throwable {
        // arrange
        String executorId = "executor123";
        Long executeLogId = 1L;
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        String taskIdStr = "task123";
        when(wxInvokeLogDOMapper.insertSelective(any())).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // act
        dealSingleSendSuccessMethod.invoke(deepSeaWxHandler, executorId, executeLogId, wxInvokeLogDO, taskIdStr);
        // assert
        verify(wxInvokeLogDOMapper).insertSelective(any());
        verify(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper).updateByExampleSelective(any(), any());
    }

    /**
     * Test database insertion failure in dealSingleSendSuccess method
     */
    @Test
    public void testDealSingleSendSuccess_InsertionFailure() throws Throwable {
        // arrange
        String executorId = "executor123";
        Long executeLogId = 1L;
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        String taskIdStr = "task123";
        when(wxInvokeLogDOMapper.insertSelective(any())).thenThrow(new RuntimeException("Insertion failed"));
        try {
            // act
            dealSingleSendSuccessMethod.invoke(deepSeaWxHandler, executorId, executeLogId, wxInvokeLogDO, taskIdStr);
        } catch (Exception e) {
            // assert
            verify(wxInvokeLogDOMapper).insertSelective(any());
            verify(wxInvokeDetailDOMapper, never()).updateByExampleSelective(any(), any());
            verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
        }
    }

    /**
     * Test update failure in dealSingleSendSuccess method
     */
    @Test
    public void testDealSingleSendSuccess_UpdateFailure() throws Throwable {
        // arrange
        String executorId = "executor123";
        Long executeLogId = 1L;
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        String taskIdStr = "task123";
        when(wxInvokeLogDOMapper.insertSelective(any())).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenThrow(new RuntimeException("Update failed"));
        try {
            // act
            dealSingleSendSuccessMethod.invoke(deepSeaWxHandler, executorId, executeLogId, wxInvokeLogDO, taskIdStr);
        } catch (Exception e) {
            // assert
            verify(wxInvokeLogDOMapper).insertSelective(any());
            verify(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
            verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
        }
    }

    /**
     * Test no matching wxInvokeDetailDO in dealSingleSendSuccess method
     */
    @Test
    public void testDealSingleSendSuccess_NoMatchingDetail() throws Throwable {
        // arrange
        String executorId = "executor123";
        Long executeLogId = 1L;
        ScrmAmProcessOrchestrationWxInvokeLogDO wxInvokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        String taskIdStr = "task123";
        when(wxInvokeLogDOMapper.insertSelective(any())).thenReturn(1);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(0);
        when(executeLogDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // act
        dealSingleSendSuccessMethod.invoke(deepSeaWxHandler, executorId, executeLogId, wxInvokeLogDO, taskIdStr);
        // assert
        verify(wxInvokeLogDOMapper).insertSelective(any());
        verify(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper).updateByExampleSelective(any(), any());
    }
}
