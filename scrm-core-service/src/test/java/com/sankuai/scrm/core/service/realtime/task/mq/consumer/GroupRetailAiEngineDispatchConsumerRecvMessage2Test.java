package com.sankuai.scrm.core.service.realtime.task.mq.consumer;

import com.dianping.poi.relation.service.api.PoiRelationService;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.meituan.mafka.client.consumer.ConsumeStatus;
import com.meituan.mafka.client.message.MafkaMessage;
import com.meituan.mafka.client.message.MessagetContext;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowActionTrackDTO;
import com.sankuai.dz.srcm.aigc.service.dto.IntelligentFollowResultDTO;
import com.sankuai.dz.srcm.aigc.service.enums.IntelligentFollowActionType;
import com.sankuai.scrm.core.service.aigc.service.dal.entity.ScrmIntelligentFollowTaskLogDO;
import com.sankuai.scrm.core.service.aigc.service.dal.mapper.ScrmGroupRetailUserFootPrintRecordDOMapper;
import com.sankuai.scrm.core.service.aigc.service.dal.mapper.ScrmIntelligentFollowTaskLogDOMapper;
import com.sankuai.scrm.core.service.aigc.service.domainservice.ScrmFridayIntelligentFollowDomainService;
import com.sankuai.scrm.core.service.chat.domain.PrivateChatDomainService;
import com.sankuai.scrm.core.service.data.statistics.dal.babymapper.ContactUserLogDOMapper;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.acl.track.UserTrackAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.track.result.UserTrackResult;
import com.sankuai.scrm.core.service.realtime.task.dal.mapper.ScrmRealtimeSceneUserRecordDOMapper;
import com.sankuai.scrm.core.service.realtime.task.domainservice.ScrmGrowthUserInfoDomainService;
import com.sankuai.scrm.core.service.realtime.task.dto.GroupRetailAiEngineDispatchMessageDTO;
import com.sankuai.scrm.core.service.realtime.task.dto.RealTimeTaskConsumerConfigDTO;
import com.sankuai.scrm.core.service.realtime.task.mq.config.RealTimeTaskConsumerConfig;
import com.sankuai.scrm.core.service.util.JsonUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;

import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

class GroupRetailAiEngineDispatchConsumerRecvMessage2Test {

    @Mock
    private RealTimeTaskConsumerConfig consumerConfig;

    @Mock
    private ScrmGrowthUserInfoDomainService scrmGrowthUserInfoDomainService;

    @Mock
    private ScrmIntelligentFollowTaskLogDOMapper intelligentFollowTaskLogDOMapper;

    @Mock
    private UserTrackAcl userTrackAcl;

    @Mock
    private ScrmFridayIntelligentFollowDomainService fridayIntelligentFollowDomainService;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private PrivateChatDomainService privateChatDomainService;

    @Mock
    private PoiRelationService poiRelationService;

    @Mock
    private ScrmRealtimeSceneUserRecordDOMapper realtimeSceneUserRecordDOMapper;

    @Mock
    private ContactUserLogDOMapper contactUserLogDOMapper;

    @Mock
    private ScrmGroupRetailUserFootPrintRecordDOMapper groupRetailUserFootPrintRecordDOMapper;

    @Mock
    private RealTimeTaskConsumerConfigDTO configDTO;

    @InjectMocks
    private GroupRetailAiEngineDispatchConsumer consumer;

    @Mock(lenient = true)
    private RedisStoreClient redisClient;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
        when(consumerConfig.ifExcludeType(anyString())).thenReturn(false);
        when(consumerConfig.getFootPrintRecordNum()).thenReturn(15);
        when(consumerConfig.getConfigDTO()).thenReturn(configDTO);
        when(configDTO.getDayNum()).thenReturn(7);
    }

    private ConsumeStatus invokeRecvMessage(MafkaMessage<String> message, MessagetContext context) throws Exception {
        Method method = GroupRetailAiEngineDispatchConsumer.class.getDeclaredMethod("recvMessage", MafkaMessage.class, MessagetContext.class);
        method.setAccessible(true);
        return (ConsumeStatus) method.invoke(consumer, message, context);
    }

    /**
     * Test empty message body returns CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessageEmptyBody() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        when(message.getBody()).thenReturn(null);
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test actionType=1 case updates DB map info and returns CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessageActionType1() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        GroupRetailAiEngineDispatchMessageDTO dto = new GroupRetailAiEngineDispatchMessageDTO();
        dto.setActionType(1);
        dto.setUserId(123L);
        dto.setAppId("testApp");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(scrmGrowthUserInfoDomainService).updateDBMapInfoByMtUserId(123L, "testApp");
    }

    /**
     * Test outside effective time returns CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessageOutsideEffectiveTime() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        GroupRetailAiEngineDispatchMessageDTO dto = new GroupRetailAiEngineDispatchMessageDTO();
        dto.setUserId(123L);
        dto.setAppId("testApp");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(consumerConfig.checkInEffectiveTime()).thenReturn(false);
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test not in whitelist returns CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessageNotInWhitelist() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        GroupRetailAiEngineDispatchMessageDTO dto = new GroupRetailAiEngineDispatchMessageDTO();
        dto.setUserId(123L);
        dto.setAppId("testApp");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(123L)).thenReturn(false);
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test existing task log not in whitelist returns CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessageExistingTaskLogNotInWhitelist() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        GroupRetailAiEngineDispatchMessageDTO dto = new GroupRetailAiEngineDispatchMessageDTO();
        dto.setUserId(123L);
        dto.setAppId("testApp");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(123L)).thenReturn(true);
        ScrmIntelligentFollowTaskLogDO logDO = new ScrmIntelligentFollowTaskLogDO();
        logDO.setId(1L);
        logDO.setUpdateTime(new Date());
        List<ScrmIntelligentFollowTaskLogDO> logList = new ArrayList<>();
        logList.add(logDO);
        when(intelligentFollowTaskLogDOMapper.selectByExample(any())).thenReturn(logList);
        when(consumerConfig.isInWhitelistPure(123L)).thenReturn(false);
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test existing task log in whitelist but not expired deletes and continues
     */
    @Test
    public void testRecvMessageExistingTaskLogInWhitelistNotExpired() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        GroupRetailAiEngineDispatchMessageDTO dto = new GroupRetailAiEngineDispatchMessageDTO();
        dto.setUserId(123L);
        dto.setAppId("testApp");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(anyLong(), anyString())).thenReturn(true);
        ScrmIntelligentFollowTaskLogDO logDO = new ScrmIntelligentFollowTaskLogDO();
        logDO.setId(1L);
        Calendar calendar = Calendar.getInstance();
        // Make it expired
        calendar.add(Calendar.MINUTE, -11);
        logDO.setUpdateTime(calendar.getTime());
        List<ScrmIntelligentFollowTaskLogDO> logList = new ArrayList<>();
        logList.add(logDO);
        when(intelligentFollowTaskLogDOMapper.selectByExample(any())).thenReturn(logList);
        when(consumerConfig.isInWhitelistPure(123L)).thenReturn(true);
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        verify(intelligentFollowTaskLogDOMapper).deleteByPrimaryKey(1L);
    }

    /**
     * Test task log insertion failure returns CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessageTaskLogInsertFailure() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        GroupRetailAiEngineDispatchMessageDTO dto = new GroupRetailAiEngineDispatchMessageDTO();
        dto.setUserId(123L);
        dto.setAppId("testApp");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(123L)).thenReturn(true);
        when(intelligentFollowTaskLogDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(intelligentFollowTaskLogDOMapper.insertSelective(any())).thenReturn(0);
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
    }

    /**
     * Test successful processing flow returns CONSUME_SUCCESS
     */
    @Test
    public void testRecvMessageSuccessfulProcessing() throws Throwable {
        // arrange
        MafkaMessage<String> message = mock(MafkaMessage.class);
        GroupRetailAiEngineDispatchMessageDTO dto = new GroupRetailAiEngineDispatchMessageDTO();
        dto.setUserId(123L);
        dto.setAppId("testApp");
        when(message.getBody()).thenReturn(JsonUtils.toStr(dto));
        when(consumerConfig.checkInEffectiveTime()).thenReturn(true);
        when(consumerConfig.isInWhitelist(anyLong(), anyString())).thenReturn(true);
        when(redisClient.setnx(any(), any(), anyInt())).thenReturn(true);
        when(intelligentFollowTaskLogDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(intelligentFollowTaskLogDOMapper.insertSelective(any())).thenReturn(1);
        when(mtUserCenterAclService.getUnionIdByUserIdFromMtUserCenter(anyLong(), anyString())).thenReturn("testUnionId");
        when(contactUserLogDOMapper.selectByUnionId(anyString(), any())).thenReturn(new ArrayList<>());
        when(privateChatDomainService.queryPrivateChatLogForActions(anyString(), anyString(), any())).thenReturn(new ArrayList<>());
        List<UserTrackResult> trackResults = new ArrayList<>();
        UserTrackResult trackResult = new UserTrackResult();
        trackResult.setActionType(IntelligentFollowActionType.VIEW_SHOP.getCode());
        trackResult.setActionContent("123");
        trackResult.setActionTimestamp(System.currentTimeMillis());
        trackResult.setPlatForm("dp");
        trackResults.add(trackResult);
        when(userTrackAcl.queryUserTrack(any())).thenReturn(trackResults);
        when(scrmGrowthUserInfoDomainService.queryDpUserIdByMtUserId(anyLong())).thenReturn(456L);
        when(poiRelationService.queryMtByDpIdL(anyLong())).thenReturn(new ArrayList<Long>() {

            {
                add(789L);
            }
        });
        IntelligentFollowResultDTO resultDTO = new IntelligentFollowResultDTO();
        when(fridayIntelligentFollowDomainService.queryIntelligentFollowResult(any(IntelligentFollowActionTrackDTO.class))).thenReturn(resultDTO);
        // act
        ConsumeStatus result = invokeRecvMessage(message, new MessagetContext());
        // assert
        assertEquals(ConsumeStatus.CONSUME_SUCCESS, result);
        //verify(fridayIntelligentFollowDomainService).queryIntelligentFollowResult(any(IntelligentFollowActionTrackDTO.class));
    }
}
