package com.sankuai.scrm.core.service.realtime.task.mq.producer;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.meituan.mafka.client.MafkaClient;
import com.meituan.mafka.client.consumer.ConsumerConstants;
import com.meituan.mafka.client.producer.IProducerProcessor;
import java.lang.reflect.Field;
import java.util.Properties;
import org.junit.jupiter.api.AfterEach;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Mockito;
import org.mockito.junit.*;

class GroupRetailAiEngineFlowLimitProducerAfterPropertiesSetTest {

    private GroupRetailAiEngineFlowLimitProducer producer;

    private MockedStatic<MafkaClient> mockedMafkaClient;

    @BeforeEach
    void setUp() {
        producer = new GroupRetailAiEngineFlowLimitProducer();
        mockedMafkaClient = mockStatic(MafkaClient.class);
    }

    @AfterEach
    void tearDown() throws Exception {
        // 使用反射重置静态字段
        setStaticProducerField(null);
        mockedMafkaClient.close();
    }

    private void setStaticProducerField(IProducerProcessor value) throws Exception {
        Field field = GroupRetailAiEngineFlowLimitProducer.class.getDeclaredField("producer");
        field.setAccessible(true);
        field.set(null, value);
    }

    private IProducerProcessor getStaticProducerField() throws Exception {
        Field field = GroupRetailAiEngineFlowLimitProducer.class.getDeclaredField("producer");
        field.setAccessible(true);
        return (IProducerProcessor) field.get(null);
    }

    /**
     * 测试正常初始化场景，验证Properties参数设置正确且成功创建生产者
     */
    @Test
    void testAfterPropertiesSetNormalCase() throws Throwable {
        // arrange
        IProducerProcessor mockProducer = mock(IProducerProcessor.class);
        mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenReturn(mockProducer);
        // act
        producer.afterPropertiesSet();
        // assert
        mockedMafkaClient.verify(() -> {
            Properties props = new Properties();
            props.setProperty(ConsumerConstants.MafkaBGNamespace, "daozong");
            props.setProperty(ConsumerConstants.MafkaClientAppkey, "com.sankuai.medicalcosmetology.scrm.core");
            MafkaClient.buildProduceFactory(props, "scrm.group.retail.ai.engine.flow.limit");
        });
        assertSame(mockProducer, getStaticProducerField());
    }

    /**
     * 测试MafkaClient构建失败场景，应抛出异常
     */
    @Test
    void testAfterPropertiesSetWhenBuildFailed() throws Throwable {
        // arrange
        mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenThrow(new RuntimeException("Build failed"));
        // act & assert
        assertThrows(RuntimeException.class, () -> producer.afterPropertiesSet());
        assertNull(getStaticProducerField());
    }

    /**
     * 测试Properties参数是否正确设置
     */
    @Test
    void testAfterPropertiesSetPropertiesSetting() throws Throwable {
        // arrange
        IProducerProcessor mockProducer = mock(IProducerProcessor.class);
        mockedMafkaClient.when(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString())).thenAnswer(invocation -> {
            Properties props = invocation.getArgument(0);
            assertEquals("daozong", props.getProperty(ConsumerConstants.MafkaBGNamespace));
            assertEquals("com.sankuai.medicalcosmetology.scrm.core", props.getProperty(ConsumerConstants.MafkaClientAppkey));
            return mockProducer;
        });
        // act
        producer.afterPropertiesSet();
        // assert
        mockedMafkaClient.verify(() -> MafkaClient.buildProduceFactory(any(Properties.class), anyString()));
    }
}
