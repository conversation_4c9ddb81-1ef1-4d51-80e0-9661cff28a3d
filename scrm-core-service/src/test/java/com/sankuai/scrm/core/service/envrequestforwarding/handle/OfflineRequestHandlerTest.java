package com.sankuai.scrm.core.service.envrequestforwarding.handle;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.alibaba.fastjson.JSONObject;
import com.dianping.lion.client.Lion;
import com.meituan.dorado.rpc.meta.RpcInvocation;
import com.sankuai.dz.srcm.envrequestforwarding.dto.OfflineRequestHandlerResponseDTO;
import com.sankuai.dz.srcm.envrequestforwarding.enums.ResultTypeEnum;
import com.sankuai.scrm.core.service.envrequestforwarding.config.OfflineDataSyncConfig;
import com.sankuai.scrm.core.service.envrequestforwarding.constant.OfflineDataSyncConstant;
import com.sankuai.scrm.core.service.envrequestforwarding.dal.entity.OfflineDataSyncLogsWithBLOBs;
import com.sankuai.scrm.core.service.envrequestforwarding.dal.mapper.OfflineDataSyncLogsMapper;
import com.sankuai.scrm.core.service.infrastructure.util.ScrmHttpUtils;
import java.io.IOException;
import java.lang.reflect.Method;
import java.util.Arrays;
import java.util.HashMap;
import java.util.Map;
import net.sf.cglib.core.internal.Function;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class OfflineRequestHandlerTest {

    @InjectMocks
    private OfflineRequestHandler offlineRequestHandler;

    @Mock
    private OfflineDataSyncLogsMapper offlineDataSyncLogsMapper;

    private String corpId = "testCorpId";

    private RpcInvocation invocation;

    // Assuming a valid service interface for demonstration
    private interface TestServiceInterface {

        String getName();

        void testMethod(String arg);
    }

    @Test
    public void testHandleInvocationIsNull() throws Throwable {
        OfflineRequestHandlerResponseDTO result = offlineRequestHandler.handle(corpId, null);
        assertNotNull(result);
    }

    /**
     * Test case to verify swimLanes header setting and successful HTTP post
     */
    @Test
    public void testHandleWithSwimLanesSuccess() throws Throwable {
        // arrange
        String corpId = "testCorpId";
        RpcInvocation invocation = mock(RpcInvocation.class);
        when(invocation.getServiceInterface()).thenReturn((Class) Object.class);
        when(invocation.getMethod()).thenReturn(Object.class.getMethods()[0]);
        when(invocation.getParameterTypes()).thenReturn(new Class[] {});
        when(invocation.getArguments()).thenReturn(new Object[] {});
        OfflineDataSyncConfig config = new OfflineDataSyncConfig();
        config.setSwimLanes(Arrays.asList("testLane"));
        config.setOfflineRequestHandleUrl("http://test.url");
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getBean(any(), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class))).thenReturn(config);
            OfflineRequestHandlerResponseDTO successResponse = OfflineRequestHandlerResponseDTO.builder().success(1).build();
            try (MockedStatic<ScrmHttpUtils> httpMock = mockStatic(ScrmHttpUtils.class)) {
                httpMock.when(() -> ScrmHttpUtils.postAsJson(any(String.class), argThat(headers -> "testLane".equals(headers.get("x-shepherd-swimlane")) && "testLane".equals(headers.get("swimlane"))), any(Map.class), any())).thenReturn(successResponse);
                // act
                OfflineRequestHandlerResponseDTO result = offlineRequestHandler.handle(corpId, invocation);
                // assert
                assertNotNull(result);
                assertEquals(1, result.getSuccess());
                verify(offlineDataSyncLogsMapper).updateByPrimaryKeySelective(argThat(logs -> logs.getResultType() == ResultTypeEnum.SUCCESS.getCode()));
            }
        }
    }

    /**
     * Test case to verify exception handling during HTTP post
     */
    @Test
    public void testHandleHttpPostException() throws Throwable {
        // arrange
        String corpId = "testCorpId";
        RpcInvocation invocation = mock(RpcInvocation.class);
        when(invocation.getServiceInterface()).thenReturn((Class) Object.class);
        when(invocation.getMethod()).thenReturn(Object.class.getMethods()[0]);
        when(invocation.getParameterTypes()).thenReturn(new Class[] {});
        when(invocation.getArguments()).thenReturn(new Object[] {});
        OfflineDataSyncConfig config = new OfflineDataSyncConfig();
        config.setOfflineRequestHandleUrl("http://test.url");
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getBean(any(), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class))).thenReturn(config);
            try (MockedStatic<ScrmHttpUtils> httpMock = mockStatic(ScrmHttpUtils.class)) {
                httpMock.when(() -> ScrmHttpUtils.postAsJson(any(String.class), any(Map.class), any(Map.class), any())).thenThrow(new IOException("Test exception"));
                // act
                OfflineRequestHandlerResponseDTO result = offlineRequestHandler.handle(corpId, invocation);
                // assert
                verify(offlineDataSyncLogsMapper).updateByPrimaryKeySelective(argThat(logs -> logs.getResultType() == ResultTypeEnum.FAIL.getCode()));
            }
        }
    }

    /**
     * Test case to verify handling of failed response (success != 1)
     */
    @Test
    public void testHandleFailedResponse() throws Throwable {
        // arrange
        String corpId = "testCorpId";
        RpcInvocation invocation = mock(RpcInvocation.class);
        when(invocation.getServiceInterface()).thenReturn((Class) Object.class);
        when(invocation.getMethod()).thenReturn(Object.class.getMethods()[0]);
        when(invocation.getParameterTypes()).thenReturn(new Class[] {});
        when(invocation.getArguments()).thenReturn(new Object[] {});
        OfflineDataSyncConfig config = new OfflineDataSyncConfig();
        config.setOfflineRequestHandleUrl("http://test.url");
        try (MockedStatic<Lion> lionMock = mockStatic(Lion.class)) {
            lionMock.when(() -> Lion.getBean(any(), eq(OfflineDataSyncConstant.CONFIG_KEY), eq(OfflineDataSyncConfig.class))).thenReturn(config);
            OfflineRequestHandlerResponseDTO failedResponse = OfflineRequestHandlerResponseDTO.builder().success(0).build();
            try (MockedStatic<ScrmHttpUtils> httpMock = mockStatic(ScrmHttpUtils.class)) {
                httpMock.when(() -> ScrmHttpUtils.postAsJson(any(String.class), any(Map.class), any(Map.class), any())).thenReturn(failedResponse);
                // act
                OfflineRequestHandlerResponseDTO result = offlineRequestHandler.handle(corpId, invocation);
                // assert
                assertNotNull(result);
                assertEquals(0, result.getSuccess());
                verify(offlineDataSyncLogsMapper).updateByPrimaryKeySelective(argThat(logs -> logs.getResultType() == ResultTypeEnum.FAIL.getCode()));
            }
        }
    }
}
