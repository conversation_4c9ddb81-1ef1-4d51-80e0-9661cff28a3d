package com.sankuai.scrm.core.service.infrastructure.util;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyInt;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.meituan.mdp.boot.starter.mdpcache.core.Cache;
import com.meituan.mdp.boot.starter.mdpcache.core.builder.CaffeineCacheBuilder;
import com.meituan.mdp.boot.starter.mdpcache.core.impl.proxy.RefreshPolicy;
import java.lang.reflect.Field;
import java.util.concurrent.TimeUnit;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedConstruction;
import org.mockito.MockedStatic;
import org.mockito.junit.*;

class VenusUtilInitTest {

    private VenusUtil venusUtil;

    @BeforeEach
    void setUp() {
        venusUtil = new VenusUtil();
    }

    /**
     * Tests normal initialization of the cache with all configurations applied
     */
    @Test
    void testInitNormalInitialization() throws Throwable {
        // arrange
        RefreshPolicy mockRefreshPolicy = mock(RefreshPolicy.class);
        Cache mockCache = mock(Cache.class);
        try (MockedStatic<RefreshPolicy> mockedRefreshPolicy = mockStatic(RefreshPolicy.class);
            MockedConstruction<CaffeineCacheBuilder> mockedBuilder = mockConstruction(CaffeineCacheBuilder.class, (mock, context) -> {
                when(mock.limit(anyInt())).thenReturn(mock);
                when(mock.loader(any())).thenReturn(mock);
                when(mock.refreshPolicy(any(RefreshPolicy.class))).thenReturn(mock);
                when(mock.expireAfterWrite(anyLong(), any(TimeUnit.class))).thenReturn(mock);
                when(mock.cacheNullValue(anyBoolean())).thenReturn(mock);
                when(mock.build()).thenReturn(mockCache);
            })) {
            when(RefreshPolicy.newPolicy(10, TimeUnit.MINUTES)).thenReturn(mockRefreshPolicy);
            // act
            venusUtil.init();
            // assert
            assertEquals(1, mockedBuilder.constructed().size());
            CaffeineCacheBuilder builder = mockedBuilder.constructed().get(0);
            verify(builder).limit(2000);
            verify(builder).loader(any());
            verify(builder).refreshPolicy(mockRefreshPolicy);
            verify(builder).expireAfterWrite(10, TimeUnit.MINUTES);
            verify(builder).limit(5);
            verify(builder).cacheNullValue(false);
            verify(builder).build();
            // Verify localCache field using reflection
            Field localCacheField = VenusUtil.class.getDeclaredField("localCache");
            localCacheField.setAccessible(true);
            assertSame(mockCache, localCacheField.get(venusUtil));
        }
    }

    /**
     * Tests refresh policy creation with correct time values
     */
    @Test
    void testInitRefreshPolicyCreation() throws Throwable {
        // arrange
        RefreshPolicy mockPolicy = mock(RefreshPolicy.class);
        Cache mockCache = mock(Cache.class);
        try (MockedStatic<RefreshPolicy> mockedRefreshPolicy = mockStatic(RefreshPolicy.class);
            MockedConstruction<CaffeineCacheBuilder> mockedBuilder = mockConstruction(CaffeineCacheBuilder.class, (mock, context) -> {
                when(mock.limit(anyInt())).thenReturn(mock);
                when(mock.loader(any())).thenReturn(mock);
                when(mock.refreshPolicy(any(RefreshPolicy.class))).thenReturn(mock);
                when(mock.expireAfterWrite(anyLong(), any(TimeUnit.class))).thenReturn(mock);
                when(mock.cacheNullValue(anyBoolean())).thenReturn(mock);
                when(mock.build()).thenReturn(mockCache);
            })) {
            when(RefreshPolicy.newPolicy(10, TimeUnit.MINUTES)).thenReturn(mockPolicy);
            // act
            venusUtil.init();
            // assert
            mockedRefreshPolicy.verify(() -> RefreshPolicy.newPolicy(10, TimeUnit.MINUTES));
            verify(mockedBuilder.constructed().get(0)).refreshPolicy(mockPolicy);
        }
    }

    /**
     * Tests cache builder behavior when build fails
     */
    @Test
    void testInitCacheBuildFailure() throws Throwable {
        // arrange
        RefreshPolicy mockPolicy = mock(RefreshPolicy.class);
        try (MockedStatic<RefreshPolicy> mockedRefreshPolicy = mockStatic(RefreshPolicy.class);
            MockedConstruction<CaffeineCacheBuilder> mockedBuilder = mockConstruction(CaffeineCacheBuilder.class, (mock, context) -> {
                when(mock.limit(anyInt())).thenReturn(mock);
                when(mock.loader(any())).thenReturn(mock);
                when(mock.refreshPolicy(any(RefreshPolicy.class))).thenReturn(mock);
                when(mock.expireAfterWrite(anyLong(), any(TimeUnit.class))).thenReturn(mock);
                when(mock.cacheNullValue(anyBoolean())).thenReturn(mock);
                when(mock.build()).thenThrow(new RuntimeException("Build failed"));
            })) {
            when(RefreshPolicy.newPolicy(10, TimeUnit.MINUTES)).thenReturn(mockPolicy);
            // act & assert
            assertThrows(RuntimeException.class, () -> venusUtil.init());
            assertEquals(1, mockedBuilder.constructed().size());
        }
    }

    /**
     * Tests that the cache loader is properly set to use getVenusSecret method
     */
    @Test
    void testInitCacheLoaderSetup() throws Throwable {
        // arrange
        RefreshPolicy mockPolicy = mock(RefreshPolicy.class);
        Cache mockCache = mock(Cache.class);
        try (MockedStatic<RefreshPolicy> mockedRefreshPolicy = mockStatic(RefreshPolicy.class);
            MockedConstruction<CaffeineCacheBuilder> mockedBuilder = mockConstruction(CaffeineCacheBuilder.class, (mock, context) -> {
                when(mock.limit(anyInt())).thenReturn(mock);
                when(mock.loader(any())).thenReturn(mock);
                when(mock.refreshPolicy(any(RefreshPolicy.class))).thenReturn(mock);
                when(mock.expireAfterWrite(anyLong(), any(TimeUnit.class))).thenReturn(mock);
                when(mock.cacheNullValue(anyBoolean())).thenReturn(mock);
                when(mock.build()).thenReturn(mockCache);
            })) {
            when(RefreshPolicy.newPolicy(10, TimeUnit.MINUTES)).thenReturn(mockPolicy);
            // act
            venusUtil.init();
            // assert
            CaffeineCacheBuilder builder = mockedBuilder.constructed().get(0);
            verify(builder).loader(any());
            // Verify localCache field using reflection
            Field localCacheField = VenusUtil.class.getDeclaredField("localCache");
            localCacheField.setAccessible(true);
            assertSame(mockCache, localCacheField.get(venusUtil));
        }
    }
}
