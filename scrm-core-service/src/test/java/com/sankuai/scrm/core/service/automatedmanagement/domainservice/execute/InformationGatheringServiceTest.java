package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.meituan.mdp.boot.starter.mdpcache.core.Cache;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeDTO;
import com.sankuai.dz.srcm.automatedmanagement.enums.ProcessOrchestrationInformationGatheringEnum;
import com.sankuai.scrm.core.service.aigc.service.SupplyMarketingTextService;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.*;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationDistributorCodeDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationExecuteGoalDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationBranchExeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationDistributorCodeDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteGoalDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.BizDistributorServiceKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.processorchestration.ProcessOrchestrationReadDomainService;
import com.sankuai.scrm.core.service.automatedmanagement.utils.dto.ConditionUtilComputeResult;
import com.sankuai.scrm.core.service.automatedmanagement.utils.dto.ConditionUtilComputeResultHitTag;
import com.sankuai.scrm.core.service.user.enums.ScrmUserTagEnum;
import org.assertj.core.util.Lists;
import org.junit.After;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Field;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import java.util.concurrent.CompletableFuture;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class InformationGatheringServiceTest {

    @InjectMocks
    private InformationGatheringService informationGatheringService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteGoalDetailDOMapper executeGoalDetailDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationDistributorCodeDOMapper distributorCodeDOMapper;

    @Mock(lenient = true)
    private Cache communityDistributorCache;

    @Mock(lenient = true)
    private SupplyMarketingTextService supplyMarketingTextService;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationBranchExeLogDOMapper branchExeLogDOMapper;

    @Mock(lenient = true)
    private ProcessOrchestrationReadDomainService processOrchestrationReadDomainService;

    private BizDistributorServiceKeyObject keyObject;

    private ScrmAmProcessOrchestrationDistributorCodeDOExample example;

    private ScrmAmProcessOrchestrationDistributorCodeDO distributorCodeDO;

    @Before
    public void setUp() throws Exception {
        keyObject = new BizDistributorServiceKeyObject(1L, "1.0", 1L, "appId");
        example = new ScrmAmProcessOrchestrationDistributorCodeDOExample();
        example.createCriteria().andProcessOrchestrationIdEqualTo(keyObject.getProcessOrchestrationId()).andProcessOrchestrationNodeIdEqualTo(keyObject.getProcessOrchestrationNodeId());
        distributorCodeDO = new ScrmAmProcessOrchestrationDistributorCodeDO();
        distributorCodeDO.setDistributorCode("distributorCode");
        // Inject the mocked communityDistributorCache into the static field
        Field cacheField = InformationGatheringService.class.getDeclaredField("communityDistributorCache");
        cacheField.setAccessible(true);
        cacheField.set(null, communityDistributorCache);
    }

    @After
    public void tearDown() throws Exception {
        // Reset the static field after each test
        Field cacheField = InformationGatheringService.class.getDeclaredField("communityDistributorCache");
        cacheField.setAccessible(true);
        cacheField.set(null, null);
    }

    /**
     * Tests the buildPassParamString method with a valid communityDistributorCode.
     */
    @Test
    public void testBuildPassParamStringNormal() throws Throwable {
        // arrange
        String communityDistributorCode = "testCode";
        // act
        String result = InformationGatheringService.buildPassParamString(communityDistributorCode);
        // assert
        assertNotNull(result);
    }

    /**
     * Tests the buildPassParamString method with a null communityDistributorCode.
     */
    @Test
    public void testBuildPassParamStringNull() throws Throwable {
        // arrange
        String communityDistributorCode = null;
        // act
        String result = InformationGatheringService.buildPassParamString(communityDistributorCode);
        // assert
        assertNotNull(result);
    }

    /**
     * Tests the buildPassParamString method with an empty string communityDistributorCode.
     */
    @Test
    public void testBuildPassParamStringEmpty() throws Throwable {
        // arrange
        String communityDistributorCode = "";
        // act
        String result = InformationGatheringService.buildPassParamString(communityDistributorCode);
        // assert
        assertNotNull(result);
    }

    /**
     * Test case when invokeDetailDOS is null.
     */
    @Test
    public void testLogUserStatusChangeDetailInvokeDetailDOSIsNull() throws Throwable {
        ProcessOrchestrationInformationGatheringEnum informationGatheringEnum = ProcessOrchestrationInformationGatheringEnum.UNKNOWN;
        informationGatheringService.logUserStatusChangeDetail(null, informationGatheringEnum);
        verify(executeGoalDetailDOMapper, times(0)).batchInsert(anyList());
    }

    /**
     * Test case when invokeDetailDOS is not empty, but elements are null.
     * Adjusted to reflect the actual behavior of the method under test.
     */
    @Test(expected = Exception.class)
    public void testLogUserStatusChangeDetailInvokeDetailDOSElementIsNull() throws Throwable {
        ProcessOrchestrationInformationGatheringEnum informationGatheringEnum = ProcessOrchestrationInformationGatheringEnum.UNKNOWN;
        informationGatheringService.logUserStatusChangeDetail(Collections.singletonList(null), informationGatheringEnum);
    }

    /**
     * Test case when invokeDetailDOS is not empty, and elements are not null.
     */
    @Test
    public void testLogUserStatusChangeDetailInvokeDetailDOSElementIsNotNull() throws Throwable {
        ProcessOrchestrationInformationGatheringEnum informationGatheringEnum = ProcessOrchestrationInformationGatheringEnum.MESSAGE_RECEIVED;
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO.setProcessOrchestrationId(1L);
        invokeDetailDO.setProcessOrchestrationVersion("1.0");
        invokeDetailDO.setProcessOrchestrationNodeId(1L);
        invokeDetailDO.setExecuteLogId(1L);

        ScrmAmProcessOrchestrationExecuteLogDO executeLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        executeLogDO.setTargetUnionId("targetUnionId");
        when(executeLogDOMapper.selectByExample(any())).thenReturn(Lists.newArrayList(executeLogDO));
        
        ScrmAmProcessOrchestrationBranchExeLogDO branchExeLogDO = new ScrmAmProcessOrchestrationBranchExeLogDO();
        branchExeLogDO.setNodeId(1L);
        when(branchExeLogDOMapper.selectByExample(any())).thenReturn(Lists.newArrayList(branchExeLogDO));

        when(branchExeLogDOMapper.updateByPrimaryKey(any())).thenReturn(1);

        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setNodeId(1L);
        CompletableFuture<List<ScrmProcessOrchestrationNodeDTO>> superSplitBranchNodeFuture = CompletableFuture.completedFuture(Lists.newArrayList(nodeDTO));
        when(processOrchestrationReadDomainService.querySuperConditionAndSplitBranchNodeFuture(anyLong(), anyString(), anyLong())).thenReturn(superSplitBranchNodeFuture);

        informationGatheringService.logUserStatusChangeDetail(Arrays.asList(invokeDetailDO), informationGatheringEnum);
        verify(executeGoalDetailDOMapper, times(1)).batchInsert(anyList());
    }

    /**
     * Test logUserStatusChangeDetail method when conditionUtilComputeResult is null
     */
    @Test(expected = Exception.class)
    public void testLogUserStatusChangeDetailWhenConditionUtilComputeResultIsNull() throws Throwable {
        // Arrange
        ConditionUtilComputeResult conditionUtilComputeResult = null;
        // Act
        informationGatheringService.logUserStatusChangeDetail(1L, "version", 1L, 1L, (byte) 1, conditionUtilComputeResult);
        // Assert is handled by the expected exception
    }

    /**
     * Test logUserStatusChangeDetail method when hitTagList is empty
     */
    @Test
    public void testLogUserStatusChangeDetailWhenHitTagListIsEmpty() throws Throwable {
        // Arrange
        ConditionUtilComputeResult conditionUtilComputeResult = new ConditionUtilComputeResult();
        conditionUtilComputeResult.setHitTagList(new ArrayList<>());
        // Act
        boolean result = informationGatheringService.logUserStatusChangeDetail(1L, "version", 1L, 1L, (byte) 1, conditionUtilComputeResult);
        // Assert
        assertTrue(result);
        verify(executeGoalDetailDOMapper, never()).insert(any());
        verify(executeGoalDetailDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * Test logUserStatusChangeDetail method when hitTagList is not empty
     */
    @Test
    public void testLogUserStatusChangeDetailWhenHitTagListIsNotEmpty() throws Throwable {
        // Arrange
        ConditionUtilComputeResult conditionUtilComputeResult = new ConditionUtilComputeResult();
        List<ConditionUtilComputeResultHitTag> hitTagList = new ArrayList<>();
        ConditionUtilComputeResultHitTag hitTag = new ConditionUtilComputeResultHitTag();
        hitTag.setHitGroupId(1);
        hitTag.setHitHighLightTagId(1L);
        hitTagList.add(hitTag);
        conditionUtilComputeResult.setHitTagList(hitTagList);
        when(executeGoalDetailDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());
        when(executeGoalDetailDOMapper.insert(any())).thenReturn(1);
        // Act
        boolean result = informationGatheringService.logUserStatusChangeDetail(1L, "version", 1L, 1L, (byte) 1, conditionUtilComputeResult);
        // Assert
        assertTrue(result);
        verify(executeGoalDetailDOMapper, times(1)).insert(any());
    }

    @Test
    public void testQueryCommunityDistributor_CacheHit() throws Throwable {
        when(communityDistributorCache.get(any(), eq(String.class))).thenReturn("cachedDistributorCode");
        String result = informationGatheringService.queryCommunityDistributor(keyObject);
        assertEquals("cachedDistributorCode", result);
    }

    @Test
    public void testQueryCommunityDistributor_CacheMiss() throws Throwable {
        when(communityDistributorCache.get(any(), eq(String.class))).thenReturn(null);
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(distributorCodeDO));
        String result = informationGatheringService.queryCommunityDistributor(keyObject);
        assertEquals("distributorCode", result);
    }

    @Test
    public void testQueryCommunityDistributor_CacheMiss_DBEmpty() throws Throwable {
        when(communityDistributorCache.get(any(), eq(String.class))).thenReturn(null);
        when(distributorCodeDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        String result = informationGatheringService.queryCommunityDistributor(keyObject);
        assertNull(result);
    }

    /*@Test
    public void testLogUserStatusChangeDetail_UpdateSuccess() throws Throwable {
        // Simulate existence of record
        when(executeGoalDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecuteGoalDetailDOExample.class))).thenReturn(Collections.singletonList(new ScrmAmProcessOrchestrationExecuteGoalDetailDO()));
        // Setup mock to simulate successful update
        when(executeGoalDetailDOMapper.updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteGoalDetailDO.class), any(ScrmAmProcessOrchestrationExecuteGoalDetailDOExample.class))).thenReturn(1);
        boolean result = informationGatheringService.logUserStatusChangeDetail(1L, "1.0", 1L, 1L, (byte) 1, 1, 1L, "clue");
        assertTrue(result);
    }*/

    @Test
    public void testLogUserStatusChangeDetail_UpdateFail() throws Throwable {
        // Simulate existence of record
        when(executeGoalDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecuteGoalDetailDOExample.class))).thenReturn(Collections.singletonList(new ScrmAmProcessOrchestrationExecuteGoalDetailDO()));
        // Setup mock to simulate update failure
        when(executeGoalDetailDOMapper.updateByExampleSelective(any(ScrmAmProcessOrchestrationExecuteGoalDetailDO.class), any(ScrmAmProcessOrchestrationExecuteGoalDetailDOExample.class))).thenReturn(0);
        boolean result = informationGatheringService.logUserStatusChangeDetail(1L, "1.0", 1L, 1L, (byte) 1, 1, 1L, "clue");
        assertTrue(!result);
    }

    @Test
    public void testLogUserStatusChangeDetail_InsertSuccess() throws Throwable {
        // Simulate non-existence of record
        when(executeGoalDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecuteGoalDetailDOExample.class))).thenReturn(Collections.emptyList());
        // Setup mock to simulate successful insert
        when(executeGoalDetailDOMapper.insert(any(ScrmAmProcessOrchestrationExecuteGoalDetailDO.class))).thenReturn(1);
        boolean result = informationGatheringService.logUserStatusChangeDetail(1L, "1.0", 1L, 1L, (byte) 1, 1, 1L, "clue");
        assertTrue(result);
    }

    @Test
    public void testLogUserStatusChangeDetail_InsertFail() throws Throwable {
        // Simulate non-existence of record
        when(executeGoalDetailDOMapper.selectByExample(any(ScrmAmProcessOrchestrationExecuteGoalDetailDOExample.class))).thenReturn(Collections.emptyList());
        // Setup mock to simulate insert failure
        when(executeGoalDetailDOMapper.insert(any(ScrmAmProcessOrchestrationExecuteGoalDetailDO.class))).thenReturn(0);
        boolean result = informationGatheringService.logUserStatusChangeDetail(1L, "1.0", 1L, 1L, (byte) 1, 1, 1L, "clue");
        assertTrue(!result);
    }

    /**
     * 测试分支执行日志为空的情况
     */
    @Test
    public void testUpdateBranchExeLog_EmptyBranchExeLogList() {
        // arrange
        when(branchExeLogDOMapper.selectByExample(any())).thenReturn(new ArrayList<>());

        // act
        informationGatheringService.updateBranchExeLog(1L, "v1", 1L, "unionId", ScrmUserTagEnum.MESSAGE_CLICKED, (byte) 1);

        // assert
        verify(branchExeLogDOMapper, times(1)).selectByExample(any());
        verify(branchExeLogDOMapper, never()).updateByPrimaryKey(any(ScrmAmProcessOrchestrationBranchExeLogDO.class));
    }

    /**
     * 测试分支执行日志不为空，但未找到对应的超级节点
     */
    @Test
    public void testUpdateBranchExeLog_NoSuperNodeFound() {
        // arrange
        List<ScrmAmProcessOrchestrationBranchExeLogDO> branchExeLogDOS = new ArrayList<>();
        branchExeLogDOS.add(new ScrmAmProcessOrchestrationBranchExeLogDO());
        when(branchExeLogDOMapper.selectByExample(any())).thenReturn(branchExeLogDOS);
        CompletableFuture<List<ScrmProcessOrchestrationNodeDTO>> future = CompletableFuture.completedFuture(new ArrayList<>());
        when(processOrchestrationReadDomainService.querySuperConditionAndSplitBranchNodeFuture(anyLong(), anyString(), anyLong())).thenReturn(future);

        // act
        informationGatheringService.updateBranchExeLog(1L, "v1", 1L, "unionId", ScrmUserTagEnum.MESSAGE_CLICKED, (byte) 1);

        // assert
        verify(branchExeLogDOMapper, times(1)).selectByExample(any());
        verify(branchExeLogDOMapper, never()).updateByPrimaryKey(any(ScrmAmProcessOrchestrationBranchExeLogDO.class));
    }

    /**
     * 测试分支执行日志不为空，找到对应的超级节点，且目标类型为1，用户标签为MESSAGE_CLICKED
     */
    @Test
    public void testUpdateBranchExeLog_SuperNodeFound_GoalType1_MessageClicked() throws InterruptedException {
        // arrange
        List<ScrmAmProcessOrchestrationBranchExeLogDO> branchExeLogDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationBranchExeLogDO logDO = new ScrmAmProcessOrchestrationBranchExeLogDO();
        logDO.setNodeId(1L);
        branchExeLogDOS.add(logDO);
        when(branchExeLogDOMapper.selectByExample(any())).thenReturn(branchExeLogDOS);

        List<ScrmProcessOrchestrationNodeDTO> nodeDTOS = new ArrayList<>();
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setNodeId(1L);
        nodeDTOS.add(nodeDTO);
        CompletableFuture<List<ScrmProcessOrchestrationNodeDTO>> future = CompletableFuture.completedFuture(nodeDTOS);
        when(processOrchestrationReadDomainService.querySuperConditionAndSplitBranchNodeFuture(anyLong(), anyString(), anyLong())).thenReturn(future);
        when(branchExeLogDOMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        // act
        informationGatheringService.updateBranchExeLog(1L, "v1", 1L, "unionId", ScrmUserTagEnum.MESSAGE_CLICKED, (byte) 1);

        // assert
        Thread.sleep(100); // 等待异步执行完成
        verify(branchExeLogDOMapper, times(1)).selectByExample(any());
        verify(branchExeLogDOMapper, times(1)).updateByPrimaryKeySelective(any(ScrmAmProcessOrchestrationBranchExeLogDO.class));
    }

    /**
     * 测试分支执行日志不为空，找到对应的超级节点，且目标类型为2，用户标签为IS_FRIEND
     */
    @Test
    public void testUpdateBranchExeLog_SuperNodeFound_GoalType2_IsFriend() throws InterruptedException {
        // arrange
        List<ScrmAmProcessOrchestrationBranchExeLogDO> branchExeLogDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationBranchExeLogDO logDO = new ScrmAmProcessOrchestrationBranchExeLogDO();
        logDO.setNodeId(1L);
        branchExeLogDOS.add(logDO);
        when(branchExeLogDOMapper.selectByExample(any())).thenReturn(branchExeLogDOS);

        List<ScrmProcessOrchestrationNodeDTO> nodeDTOS = new ArrayList<>();
        ScrmProcessOrchestrationNodeDTO nodeDTO = new ScrmProcessOrchestrationNodeDTO();
        nodeDTO.setNodeId(1L);
        nodeDTOS.add(nodeDTO);
        CompletableFuture<List<ScrmProcessOrchestrationNodeDTO>> future = CompletableFuture.completedFuture(nodeDTOS);
        when(processOrchestrationReadDomainService.querySuperConditionAndSplitBranchNodeFuture(anyLong(), anyString(), anyLong())).thenReturn(future);
        when(branchExeLogDOMapper.updateByPrimaryKeySelective(any())).thenReturn(1);
        // act
        informationGatheringService.updateBranchExeLog(1L, "v1", 1L, "unionId", ScrmUserTagEnum.IS_FRIEND, (byte) 2);

        // assert
        Thread.sleep(100); // 等待异步执行完成
        verify(branchExeLogDOMapper, times(1)).selectByExample(any());
        verify(branchExeLogDOMapper, times(1)).updateByPrimaryKeySelective(any(ScrmAmProcessOrchestrationBranchExeLogDO.class));
    }
}
