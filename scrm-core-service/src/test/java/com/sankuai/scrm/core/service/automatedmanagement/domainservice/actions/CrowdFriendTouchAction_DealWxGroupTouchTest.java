package com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions;

import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDetailInfoDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.*;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationCheckStatusEnum;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationCheckStepEnum;
import com.sankuai.dz.srcm.group.dto.GroupListDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessExecutionCheckPointDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationExecuteLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessExecutionCheckPointDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessExecutionCheckPointDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.dto.ExecuteManagementDTO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.springframework.test.util.ReflectionTestUtils;

import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Date;
import java.util.List;
import java.util.Optional;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

public class CrowdFriendTouchAction_DealWxGroupTouchTest {

    @InjectMocks
    private CrowdFriendTouchAction action;

    @Mock(lenient = true)
    private ExecuteManagementService executeManagementService;

    @Mock(lenient = true)
    private ExecuteManagementDTO mediumManagementDTO;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogMapper;

    @Mock(lenient = true)
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock(lenient = true)
    private ScrmAmProcessExecutionCheckPointDOMapper processExecutionCheckPointDOMapper;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    private void setupNodeMediumDTO(ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO, Long nodeId, Integer actionSubType) {
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setActionSubType(actionSubType);
        actionDTO.setProcessOrchestrationNodeId(nodeId);
        actionDTO.setContentType(1);
        nodeMediumDTO.getActionMap().put(nodeId, actionDTO);
    }

    @Test
    public void testDealWxGroupTouch_BlankTempGroupId() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        scrmCrowdPackDetailInfoDTO.setTempGroupId("");
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        ScrmAmProcessOrchestrationExecuteLogDO existedExecuteLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        StepExecuteResultDTO result = action.dealWxGroupTouch(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        assertFalse(result.isSuccess());
    }

    @Test
    public void testDealWxGroupTouch_EmptyStaffListExecutorType1() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setExecutorType(1);
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        scrmCrowdPackDetailInfoDTO.setTempGroupId("tempGroupId");
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setNodeId(1L);
        ScrmAmProcessOrchestrationExecuteLogDO existedExecuteLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        existedExecuteLogDO.setGroupId("tempGroupId");
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        setupNodeMediumDTO(nodeMediumDTO, currentProcessingNode.getNodeId(), 5);
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        processOrchestrationDTO.setGroupInfoList(new ArrayList<>());
        GroupListDTO groupListDTO = new GroupListDTO();
        groupListDTO.setGroupId("tempGroupId");
        groupListDTO.setGroupName("groupName");
        groupListDTO.setOwner("groupOwner");
        processOrchestrationDTO.getGroupInfoList().add(groupListDTO);
        processOrchestrationDTO.setExecutePlanDTO(new ScrmProcessOrchestrationExecutePlanDTO());
        processOrchestrationDTO.getExecutePlanDTO().setTaskStartTime(new Date());
        when(executeManagementService.getStaffListByGroupId(anyList())).thenReturn(new ArrayList<>());
        when(executeLogMapper.updateByPrimaryKey(any())).thenReturn(1);
        when(wxInvokeDetailDOMapper.insert(any())).thenReturn(1);
        StepExecuteResultDTO result = action.dealWxGroupTouch(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        assertTrue(result.isSuccess());
    }

    @Test
    public void testDealWxGroupTouch_ValidStaffListNoLessCountExecutor() throws Throwable {
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setExecutorType(1);
        ScrmCrowdPackDetailInfoDTO scrmCrowdPackDetailInfoDTO = new ScrmCrowdPackDetailInfoDTO();
        scrmCrowdPackDetailInfoDTO.setTempGroupId("tempGroupId");
        ScrmProcessOrchestrationNodeDTO currentProcessingNode = new ScrmProcessOrchestrationNodeDTO();
        currentProcessingNode.setNodeId(1L);
        ScrmAmProcessOrchestrationExecuteLogDO existedExecuteLogDO = new ScrmAmProcessOrchestrationExecuteLogDO();
        existedExecuteLogDO.setGroupId("tempGroupId");
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = new ScrmProcessOrchestrationNodeMediumDTO();
        setupNodeMediumDTO(nodeMediumDTO, currentProcessingNode.getNodeId(), 5);
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        List<MemberInfoEntity> staffList = new ArrayList<>();
        MemberInfoEntity member = new MemberInfoEntity();
        member.setGroupMemberId("memberId");
        staffList.add(member);
        processOrchestrationDTO.setGroupInfoList(new ArrayList<>());
        GroupListDTO groupListDTO = new GroupListDTO();
        groupListDTO.setGroupId("tempGroupId");
        groupListDTO.setGroupName("groupName");
        groupListDTO.setOwner("groupOwner");
        processOrchestrationDTO.getGroupInfoList().add(groupListDTO);
        processOrchestrationDTO.setExecutePlanDTO(new ScrmProcessOrchestrationExecutePlanDTO());
        processOrchestrationDTO.getExecutePlanDTO().setTaskStartTime(new Date());
        when(executeManagementService.getStaffListByGroupId(anyList())).thenReturn(staffList);
        when(mediumManagementDTO.getLessCountExecutor(anyList())).thenReturn(null);
        when(executeLogMapper.updateByPrimaryKey(any())).thenReturn(1);
        when(wxInvokeDetailDOMapper.insert(any())).thenReturn(1);
        StepExecuteResultDTO result = action.dealWxGroupTouch(processOrchestrationDTO, mediumManagementDTO, scrmCrowdPackDetailInfoDTO, currentProcessingNode, existedExecuteLogDO);
        assertTrue(result.isSuccess());
    }

    @Test
    public void testUpdateExecuteThirdCheckSuccess() throws Exception {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(100L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);
        Long maxId = 200L;

        // 使用反射调用私有方法

        ReflectionTestUtils.invokeMethod(action, "updateExecuteThirdCheck",
                processOrchestrationDTO, maxId);
        verify(processExecutionCheckPointDOMapper, times(1)).updateByExampleSelective(any(),any());
    }

    @Test
    public void testQueryThirdCheckMaxIdSuccess() throws Exception {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setValidVersion("v1");
        ScrmProcessOrchestrationExecutePlanDTO executePlanDTO = new ScrmProcessOrchestrationExecutePlanDTO();
        executePlanDTO.setId(100L);
        processOrchestrationDTO.setExecutePlanDTO(executePlanDTO);

        // 使用反射调用私有方法

        Long num = ReflectionTestUtils.invokeMethod(action, "queryThirdCheckMaxId",
                processOrchestrationDTO);
        // 指定具体的参数类型
        verify(processExecutionCheckPointDOMapper, times(1))
                .selectByExample(any(ScrmAmProcessExecutionCheckPointDOExample.class));

        // 修正断言 - 使用 assertEquals 的正确参数顺序
        assertEquals(Long.valueOf(0L), num);;
    }


}
