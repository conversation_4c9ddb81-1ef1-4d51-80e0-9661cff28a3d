package com.sankuai.scrm.core.service.external.contact.handler;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.dianping.cat.Cat;
import com.sankuai.scrm.core.service.chat.dal.entity.WelcomeMessage;
import com.sankuai.scrm.core.service.chat.domain.WelcomeMessageDomainService;
import com.sankuai.scrm.core.service.dashboard.domain.DashBoardDomainService;
import com.sankuai.scrm.core.service.external.contact.bo.ExternalContactAddBO;
import com.sankuai.scrm.core.service.external.contact.domain.ContactUserDomain;
import com.sankuai.scrm.core.service.external.contact.domain.ExternalContactBaseInfoDomainService;
import com.sankuai.scrm.core.service.external.contact.domain.PrivateGroupFissionDomainService;
import com.sankuai.scrm.core.service.external.contact.handler.ExternalContactAddHandler;
import com.sankuai.scrm.core.service.external.contact.mq.producer.DtFriendMsgProducer;
import com.sankuai.scrm.core.service.flow.domain.FlowMaterialDomainService;
import com.sankuai.scrm.core.service.flowV2.domain.FlowEntryEventLogDomainService;
import com.sankuai.scrm.core.service.friend.dynamiccode.domain.FriendChannelDynamicCodeDomainService;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.CorpWxContactAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxContactUserDetail;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxFollowUserInfo;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsAssistantAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsFriendAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter.MtUserCenterAclService;
import com.sankuai.scrm.core.service.infrastructure.acl.order.UnifiedOrderAclService;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.payment.domain.PaymentSceneService;
import com.sankuai.scrm.core.service.payment.wx.domain.WxBalanceService;
import com.sankuai.scrm.core.service.tag.domain.StaticTaskDomainService;
import com.sankuai.scrm.core.service.tag.domain.TagDomainService;
import com.sankuai.scrm.core.service.user.domain.ScrmUserTagDomainService;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import org.junit.*;
import org.junit.runner.RunWith;
import org.junit.runner.RunWith.*;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class ExternalContactAddHandlerTest {

    @InjectMocks
    private ExternalContactAddHandler externalContactAddHandler;

    @Mock
    private CorpWxContactAcl corpWxContactAcl;

    @Mock
    private TagDomainService tagDomainService;

    @Mock
    private StaticTaskDomainService staticTaskDomainService;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private ExternalContactBaseInfoDomainService baseInfoDomainService;

    @Mock
    private DsFriendAcl dsFriendAcl;

    @Mock
    private DsAssistantAcl dsAssistantAcl;

    @Mock
    private MtUserCenterAclService mtUserCenterAclService;

    @Mock
    private PaymentSceneService paymentSceneService;

    @Mock
    private WxBalanceService wxBalanceService;

    @Mock
    private ContactUserDomain contactUserDomain;

    @Mock
    private PrivateGroupFissionDomainService privateGroupFissionDomainService;

    @Mock
    private FriendChannelDynamicCodeDomainService friendChannelDynamicCodeDomainService;

    @Mock
    private FlowEntryEventLogDomainService entryEventLogDomainService;

    @Mock
    private UnifiedOrderAclService unifiedOrderAclService;

    @Mock
    private DtFriendMsgProducer dtFriendMsgProducer;

    @Mock
    private CorpWxAcl corpWxAclService;

    @Mock
    private FlowMaterialDomainService flowMaterialDomainService;

    @Mock
    private WelcomeMessageDomainService welcomeMessageDomainService;

    @Mock
    private ScrmUserTagDomainService scrmUserTagDomainService;

    @Mock
    private Cat cat;

    private ExternalContactAddBO data;

    @Before
    public void setUp() {
        data = new ExternalContactAddBO();
        data.setCorpId("corpId");
        data.setUserId("userId");
        data.setExternalUserId("externalUserId");
    }

    @Test
    public void testHandleDataWhenDataIsNull() throws Throwable {
        externalContactAddHandler.handleData(null);
        verifyNoInteractions(corpWxContactAcl, tagDomainService, staticTaskDomainService, appConfigRepository, baseInfoDomainService, dsFriendAcl, dsAssistantAcl, mtUserCenterAclService, paymentSceneService, wxBalanceService, contactUserDomain, privateGroupFissionDomainService, friendChannelDynamicCodeDomainService, entryEventLogDomainService, unifiedOrderAclService, dtFriendMsgProducer, corpWxAclService, welcomeMessageDomainService);
    }

    @Test
    public void testHandleDataWhenUserIdOrExternalUserIdIsNull() throws Throwable {
        ExternalContactAddBO data = new ExternalContactAddBO();
        data.setUserId(null);
        data.setExternalUserId("externalUserId");
        data.setCorpId("corpId");
        externalContactAddHandler.handleData(data);
        verifyNoInteractions(corpWxContactAcl, tagDomainService, staticTaskDomainService, appConfigRepository, baseInfoDomainService, dsFriendAcl, dsAssistantAcl, mtUserCenterAclService, paymentSceneService, wxBalanceService, contactUserDomain, privateGroupFissionDomainService, friendChannelDynamicCodeDomainService, entryEventLogDomainService, unifiedOrderAclService, dtFriendMsgProducer, corpWxAclService, welcomeMessageDomainService);
    }

    @Test
    public void testHandleDataWhenUserDetailIsNull() throws Throwable {
        ExternalContactAddBO data = new ExternalContactAddBO();
        data.setUserId("userId");
        data.setExternalUserId("externalUserId");
        data.setCorpId("corpId");
        when(corpWxContactAcl.getUserDetail(anyString(), anyString())).thenReturn(null);
        when(appConfigRepository.getAppIdByCorpId(anyString())).thenReturn("appId");
        when(welcomeMessageDomainService.queryWelcomeMessage(anyString(), anyString(), any())).thenReturn(new WelcomeMessage());
        when(baseInfoDomainService.queryBaseInfoByExternalUserId(anyString(), anyString())).thenReturn(null);
        externalContactAddHandler.handleData(data);
        verify(corpWxContactAcl).getUserDetail(anyString(), anyString());
        verify(appConfigRepository).getAppIdByCorpId(anyString());
        verify(welcomeMessageDomainService).queryWelcomeMessage(anyString(), anyString(), any());
        verify(baseInfoDomainService).queryBaseInfoByExternalUserId(anyString(), anyString());
    }

    @Test
    public void testHandleDataWhenFollowUserInfosIsEmpty() throws Throwable {
        ExternalContactAddBO data = new ExternalContactAddBO();
        data.setUserId("userId");
        data.setExternalUserId("externalUserId");
        data.setCorpId("corpId");
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("unionId");
        when(corpWxContactAcl.getUserDetail(anyString(), anyString())).thenReturn(userDetail);
        when(corpWxContactAcl.getUserFollowUserInfo(anyString(), anyString())).thenReturn(new ArrayList<>());
        when(appConfigRepository.getAppIdByCorpId(anyString())).thenReturn("appId");
        when(welcomeMessageDomainService.queryWelcomeMessage(anyString(), anyString(), any())).thenReturn(new WelcomeMessage());
        when(baseInfoDomainService.queryBaseInfoByExternalUserId(anyString(), anyString())).thenReturn(null);
        doNothing().when(scrmUserTagDomainService).handleScrmtUserTagWhenAddContactUser(anyString(), anyString());
        externalContactAddHandler.handleData(data);
        verify(corpWxContactAcl).getUserDetail(anyString(), anyString());
        verify(corpWxContactAcl).getUserFollowUserInfo(anyString(), anyString());
        verify(appConfigRepository).getAppIdByCorpId(anyString());
        verify(welcomeMessageDomainService).queryWelcomeMessage(anyString(), anyString(), any());
        verify(baseInfoDomainService).queryBaseInfoByExternalUserId(anyString(), anyString());
        verify(scrmUserTagDomainService).handleScrmtUserTagWhenAddContactUser(anyString(), anyString());
    }

    @Test
    public void testHandleDataWhenUserDetailAndFollowUserInfosAreNotNull() throws Throwable {
        ExternalContactAddBO data = new ExternalContactAddBO();
        data.setUserId("userId");
        data.setExternalUserId("externalUserId");
        data.setCorpId("corpId");
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setUnionId("unionId");
        List<WxFollowUserInfo> followUserInfos = new ArrayList<>();
        WxFollowUserInfo followUserInfo = new WxFollowUserInfo();
        followUserInfo.setUserId("userId");
        List<String> remarkMobiles = new ArrayList<>();
        remarkMobiles.add("12345678901");
        followUserInfo.setRemarkMobiles(remarkMobiles);
        followUserInfos.add(followUserInfo);
        when(corpWxContactAcl.getUserDetail(anyString(), anyString())).thenReturn(userDetail);
        when(corpWxContactAcl.getUserFollowUserInfo(anyString(), anyString())).thenReturn(followUserInfos);
        when(mtUserCenterAclService.getUserIdByUnionIdFromMtUserCenter(anyString())).thenReturn(12345L);
        when(appConfigRepository.getAppIdByCorpId(anyString())).thenReturn("appId");
        when(welcomeMessageDomainService.queryWelcomeMessage(anyString(), anyString(), any())).thenReturn(new WelcomeMessage());
        when(baseInfoDomainService.queryBaseInfoByExternalUserId(anyString(), anyString())).thenReturn(null);
        doNothing().when(scrmUserTagDomainService).handleScrmtUserTagWhenAddContactUser(anyString(), anyString());
        externalContactAddHandler.handleData(data);
        verify(corpWxContactAcl).getUserDetail(anyString(), anyString());
        verify(corpWxContactAcl).getUserFollowUserInfo(anyString(), anyString());
        verify(mtUserCenterAclService).getUserIdByUnionIdFromMtUserCenter(anyString());
        verify(dtFriendMsgProducer).sendMsg(anyString());
        verify(flowMaterialDomainService).attributionAndTag(anyString(), any(ExternalContactAddBO.class));
        verify(welcomeMessageDomainService).queryWelcomeMessage(anyString(), anyString(), any());
        verify(scrmUserTagDomainService).handleScrmtUserTagWhenAddContactUser(anyString(), anyString());
    }

    /**
     * 测试 handleData 方法，当 data 为 null 时，方法直接返回
     */
    @Test
    public void testHandleData_DataIsNull() throws Throwable {
        // arrange
        ExternalContactAddBO nullData = null;
        // act
        externalContactAddHandler.handleData(nullData);
        // assert
        verify(corpWxContactAcl, never()).getUserDetail(anyString(), anyString());
    }

    /**
     * 测试 handleData 方法，当 userId 为空时，方法直接返回
     */
    @Test
    public void testHandleData_UserIdIsNull() throws Throwable {
        // arrange
        data.setUserId(null);
        // act
        externalContactAddHandler.handleData(data);
        // assert
        verify(corpWxContactAcl, never()).getUserDetail(anyString(), anyString());
    }

    /**
     * 测试 handleData 方法，当 externalUserId 为空时，方法直接返回
     */
    @Test
    public void testHandleData_ExternalUserIdIsNull() throws Throwable {
        // arrange
        data.setExternalUserId(null);
        // act
        externalContactAddHandler.handleData(data);
        // assert
        verify(corpWxContactAcl, never()).getUserDetail(anyString(), anyString());
    }

    /**
     * 测试 handleData 方法，当 userDetail 为 null 时，方法正常执行，但不会调用后续的 userDetail 相关方法
     */
    @Test
    public void testHandleData_UserDetailIsNull() throws Throwable {
        // arrange
        when(corpWxContactAcl.getUserDetail(data.getCorpId(), data.getExternalUserId())).thenReturn(null);
        // act
        externalContactAddHandler.handleData(data);
        // assert
        verify(corpWxContactAcl).getUserDetail(data.getCorpId(), data.getExternalUserId());
        verify(corpWxContactAcl).getUserFollowUserInfo(data.getCorpId(), data.getExternalUserId());
        // No need to verify private methods
    }

    /**
     * 测试 handleData 方法，当 followUserInfos 为空时，方法正常执行，但不会调用后续的 followUserInfos 相关方法
     */
    @Test
    public void testHandleData_FollowUserInfosIsEmpty() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        when(corpWxContactAcl.getUserDetail(data.getCorpId(), data.getExternalUserId())).thenReturn(userDetail);
        when(corpWxContactAcl.getUserFollowUserInfo(data.getCorpId(), data.getExternalUserId())).thenReturn(Collections.emptyList());
        // act
        externalContactAddHandler.handleData(data);
        // assert
        verify(corpWxContactAcl).getUserDetail(data.getCorpId(), data.getExternalUserId());
        verify(corpWxContactAcl).getUserFollowUserInfo(data.getCorpId(), data.getExternalUserId());
        // No need to verify private methods
    }

    /**
     * 测试 handleData 方法，当所有参数正常时，方法正常执行所有逻辑
     */
    @Test
    public void testHandleData_AllParametersAreValid() throws Throwable {
        // arrange
        WxContactUserDetail userDetail = new WxContactUserDetail();
        List<WxFollowUserInfo> followUserInfos = Collections.singletonList(new WxFollowUserInfo());
        when(corpWxContactAcl.getUserDetail(data.getCorpId(), data.getExternalUserId())).thenReturn(userDetail);
        when(corpWxContactAcl.getUserFollowUserInfo(data.getCorpId(), data.getExternalUserId())).thenReturn(followUserInfos);
        // act
        externalContactAddHandler.handleData(data);
        // assert
        verify(corpWxContactAcl).getUserDetail(data.getCorpId(), data.getExternalUserId());
        verify(corpWxContactAcl).getUserFollowUserInfo(data.getCorpId(), data.getExternalUserId());
        // No need to verify private methods
    }
}
