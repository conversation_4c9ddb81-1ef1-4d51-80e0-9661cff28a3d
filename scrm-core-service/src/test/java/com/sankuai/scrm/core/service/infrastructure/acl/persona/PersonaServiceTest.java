package com.sankuai.scrm.core.service.infrastructure.acl.persona;

import org.junit.runner.RunWith;
import org.mockito.junit.MockitoJUnitRunner;
import com.sankuai.persona.meta.ditto.SceneData;
import static org.junit.Assert.*;
import org.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class PersonaServiceTest {

    private PersonaService personaService = new PersonaService();

    /**
     * Tests the getCurrentEffectiveVersion method when sceneData is null.
     */
    @Test
    public void testGetCurrentEffectiveVersionWhenSceneDataIsNull() throws Throwable {
        // arrange
        SceneData sceneData = null;
        // act
        String result = personaService.getCurrentEffectiveVersion(sceneData);
        // assert
        assertNull(result);
    }

    /**
     * Tests the getCurrentEffectiveVersion method when sceneData is not null and updateTimestamp is 0.
     */
    @Test
    public void testGetCurrentEffectiveVersionWhenUpdateTimestampIsZero() throws Throwable {
        // arrange
        SceneData sceneData = new SceneData();
        sceneData.setUpdateTimestamp(0);
        // act
        String result = personaService.getCurrentEffectiveVersion(sceneData);
        // assert
        // Adjusted the assertion to expect the default date string when updateTimestamp is 0
        assertEquals("1970-01-01 08:00:00", result);
    }

    /**
     * Tests the getCurrentEffectiveVersion method when sceneData is not null and updateTimestamp is not 0.
     */
    @Test
    public void testGetCurrentEffectiveVersionWhenUpdateTimestampIsNotZero() throws Throwable {
        // arrange
        SceneData sceneData = new SceneData();
        sceneData.setUpdateTimestamp(System.currentTimeMillis());
        // act
        String result = personaService.getCurrentEffectiveVersion(sceneData);
        // assert
        assertNotNull(result);
    }
}
