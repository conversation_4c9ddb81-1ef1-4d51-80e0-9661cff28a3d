package com.sankuai.scrm.core.service.infrastructure.acl.ds;

import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsCorpGroupAclService;
import com.sankuai.service.fe.corp.wx.thrift.CorpWxService;
import com.sankuai.service.fe.corp.wx.thrift.ModifyGroupNameRequest;
import com.sankuai.service.fe.corp.wx.thrift.ModifyGroupNameResponse;
import org.apache.thrift.TException;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.assertFalse;
import static org.junit.Assert.assertTrue;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class DsCorpGroupAclServiceUpdateGroupNameTest {

    @InjectMocks
    private DsCorpGroupAclService dsCorpGroupAclService;

    @Mock
    private CorpWxService.Iface corpWxService;

    private String groupId;

    private String groupName;

    private String mobile;

    @Before
    public void setUp() {
        groupId = "groupId";
        groupName = "groupName";
        mobile = "mobile";
    }

    @Test
    public void testUpdateGroupNameWithEmptyInput() {
        assertFalse(dsCorpGroupAclService.updateGroupName("", groupName, mobile));
        assertFalse(dsCorpGroupAclService.updateGroupName(groupId, "", mobile));
        assertFalse(dsCorpGroupAclService.updateGroupName(groupId, groupName, ""));
    }

    @Test
    public void testUpdateGroupNameWithNullResponse() throws TException {
        when(corpWxService.modifyGroupName(any(ModifyGroupNameRequest.class))).thenReturn(null);
        assertFalse(dsCorpGroupAclService.updateGroupName(groupId, groupName, mobile));
    }

    @Test
    public void testUpdateGroupNameWithNonZeroCode() throws TException {
        ModifyGroupNameResponse response = new ModifyGroupNameResponse();
        response.setCode(1);
        when(corpWxService.modifyGroupName(any(ModifyGroupNameRequest.class))).thenReturn(response);
        assertFalse(dsCorpGroupAclService.updateGroupName(groupId, groupName, mobile));
    }

    @Test
    public void testUpdateGroupNameWithException() throws TException {
        when(corpWxService.modifyGroupName(any(ModifyGroupNameRequest.class))).thenThrow(TException.class);
        assertFalse(dsCorpGroupAclService.updateGroupName(groupId, groupName, mobile));
    }

    @Test
    public void testUpdateGroupNameWithSuccess() throws TException {
        ModifyGroupNameResponse response = new ModifyGroupNameResponse();
        response.setCode(0);
        when(corpWxService.modifyGroupName(any(ModifyGroupNameRequest.class))).thenReturn(response);
        assertTrue(dsCorpGroupAclService.updateGroupName(groupId, groupName, mobile));
    }
}
