package com.sankuai.scrm.core.service.userWechatCoupon.dal.example;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import ai.djl.repository.zoo.Criteria;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class ScrmUserWechatCouponExampleLimitTest {

    private ScrmUserWechatCouponExample example;

    @BeforeEach
    public void setUp() {
        example = new ScrmUserWechatCouponExample();
    }

    /**
     * 测试正常情况：offset和rows都是正整数
     * 验证方法正确设置offset和rows并返回自身实例
     */
    @Test
    public void testLimitWithPositiveOffsetAndRows() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int offset = 10;
        int rows = 20;
        // act
        ScrmUserWechatCouponExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "方法应返回自身实例以实现链式调用");
        assertEquals(offset, example.getOffset(), "offset应被正确设置");
        assertEquals(rows, example.getRows(), "rows应被正确设置");
    }

    /**
     * 测试边界情况：offset为0，rows为0
     * 验证方法能正确处理0值边界情况
     */
    @Test
    public void testLimitWithZeroValues() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int offset = 0;
        int rows = 0;
        // act
        ScrmUserWechatCouponExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "方法应返回自身实例以实现链式调用");
        assertEquals(offset, example.getOffset(), "offset为0时应被正确设置");
        assertEquals(rows, example.getRows(), "rows为0时应被正确设置");
    }

    /**
     * 测试异常情况：offset为null
     * 验证方法能正确处理null值
     */
    @Test
    public void testLimitWithNullOffset() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        Integer offset = null;
        int rows = 20;
        // act
        ScrmUserWechatCouponExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "方法应返回自身实例以实现链式调用");
        assertNull(example.getOffset(), "offset为null时应保持null");
        assertEquals(rows, example.getRows(), "rows应被正确设置");
    }

    /**
     * 测试异常情况：rows为null
     * 验证方法能正确处理null值
     */
    @Test
    public void testLimitWithNullRows() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int offset = 10;
        Integer rows = null;
        // act
        ScrmUserWechatCouponExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "方法应返回自身实例以实现链式调用");
        assertEquals(offset, example.getOffset(), "offset应被正确设置");
        assertNull(example.getRows(), "rows为null时应保持null");
    }

    /**
     * 测试异常情况：offset为负数
     * 验证方法能正确处理负数值
     */
    @Test
    public void testLimitWithNegativeOffset() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int offset = -1;
        int rows = 20;
        // act
        ScrmUserWechatCouponExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "方法应返回自身实例以实现链式调用");
        assertEquals(offset, example.getOffset(), "负数的offset应被正确设置");
        assertEquals(rows, example.getRows(), "rows应被正确设置");
    }

    /**
     * 测试异常情况：rows为负数
     * 验证方法能正确处理负数值
     */
    @Test
    public void testLimitWithNegativeRows() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int offset = 10;
        int rows = -1;
        // act
        ScrmUserWechatCouponExample result = example.limit(offset, rows);
        // assert
        assertSame(example, result, "方法应返回自身实例以实现链式调用");
        assertEquals(offset, example.getOffset(), "offset应被正确设置");
        assertEquals(rows, example.getRows(), "负数的rows应被正确设置");
    }

    /**
     * 测试方法链式调用
     * 验证多个limit调用能正确覆盖之前的值
     */
    @Test
    public void testLimitMethodChaining() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample example = new ScrmUserWechatCouponExample();
        int firstOffset = 10;
        int firstRows = 20;
        int secondOffset = 30;
        int secondRows = 40;
        // act
        ScrmUserWechatCouponExample result = example.limit(firstOffset, firstRows).limit(secondOffset, secondRows);
        // assert
        assertSame(example, result, "方法应返回自身实例以实现链式调用");
        assertEquals(secondOffset, example.getOffset(), "第二次调用的offset应覆盖第一次的值");
        assertEquals(secondRows, example.getRows(), "第二次调用的rows应覆盖第一次的值");
    }

    /**
     * 测试首次调用createCriteria时，oredCriteria为空的情况
     * 验证：会添加新Criteria到列表，并返回新创建的Criteria
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsEmpty() throws Throwable {
        // act
        ScrmUserWechatCouponExample.Criteria result = example.createCriteria();
        // assert
        assertNotNull(result);
        assertEquals(1, example.getOredCriteria().size());
        assertSame(result, example.getOredCriteria().get(0));
    }

    /**
     * 测试非首次调用createCriteria时，oredCriteria不为空的情况
     * 验证：不会添加新Criteria到列表，但仍返回新创建的Criteria
     */
    @Test
    public void testCreateCriteriaWhenOredCriteriaIsNotEmpty() throws Throwable {
        // arrange
        // 首次调用填充列表
        ScrmUserWechatCouponExample.Criteria firstCriteria = example.createCriteria();
        // act
        ScrmUserWechatCouponExample.Criteria secondCriteria = example.createCriteria();
        // assert
        assertNotNull(secondCriteria);
        assertEquals(1, example.getOredCriteria().size());
        assertSame(firstCriteria, example.getOredCriteria().get(0));
        assertNotSame(secondCriteria, example.getOredCriteria().get(0));
    }

    /**
     * 测试多次调用createCriteria的情况
     * 验证：只有第一次调用会添加Criteria到列表
     */
    @Test
    public void testCreateCriteriaMultipleCalls() throws Throwable {
        // act
        ScrmUserWechatCouponExample.Criteria first = example.createCriteria();
        ScrmUserWechatCouponExample.Criteria second = example.createCriteria();
        ScrmUserWechatCouponExample.Criteria third = example.createCriteria();
        // assert
        assertEquals(1, example.getOredCriteria().size());
        assertSame(first, example.getOredCriteria().get(0));
        assertNotSame(second, example.getOredCriteria().get(0));
        assertNotSame(third, example.getOredCriteria().get(0));
    }

    /**
     * 测试在clear后调用createCriteria的情况
     * 验证：clear后再次调用相当于首次调用
     */
    @Test
    public void testCreateCriteriaAfterClear() throws Throwable {
        // arrange
        // 首次调用
        example.createCriteria();
        // 清空
        example.clear();
        // act
        ScrmUserWechatCouponExample.Criteria criteria = example.createCriteria();
        // assert
        assertNotNull(criteria);
        assertEquals(1, example.getOredCriteria().size());
        assertSame(criteria, example.getOredCriteria().get(0));
    }

    /**
     * 测试createCriteria后oredCriteria不为null
     */
    @Test
    public void testOredCriteriaNotNullAfterCreate() throws Throwable {
        // act
        example.createCriteria();
        // assert
        assertNotNull(example.getOredCriteria());
    }

    /**
     * 测试createCriteria返回的Criteria实例不为null
     */
    @Test
    public void testCreateCriteriaReturnsNonNull() throws Throwable {
        // act & assert
        assertNotNull(example.createCriteria());
    }

    /**
     * 测试连续两次clear后createCriteria的行为
     */
    @Test
    public void testCreateCriteriaAfterDoubleClear() throws Throwable {
        // arrange
        example.createCriteria();
        example.clear();
        example.clear();
        // act
        ScrmUserWechatCouponExample.Criteria criteria = example.createCriteria();
        // assert
        assertNotNull(criteria);
        assertEquals(1, example.getOredCriteria().size());
    }

    /**
     * 测试createCriteriaInternal被正确调用的场景
     * 使用Mockito验证内部方法调用
     */
    @Test
    public void testCreateCriteriaInternalInvocation() throws Throwable {
        // arrange
        ScrmUserWechatCouponExample spyExample = spy(example);
        // act
        spyExample.createCriteria();
        // assert
        verify(spyExample, times(1)).createCriteriaInternal();
    }
}
