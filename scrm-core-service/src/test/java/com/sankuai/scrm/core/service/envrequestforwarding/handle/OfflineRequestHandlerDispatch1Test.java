package com.sankuai.scrm.core.service.envrequestforwarding.handle;

import com.dianping.lion.Environment;
import com.dianping.lion.client.Lion;
import com.meituan.dorado.rpc.meta.RpcInvocation;
import com.sankuai.dz.srcm.envrequestforwarding.dto.OfflineRequestHandlerResponseDTO;
import com.sankuai.scrm.core.service.envrequestforwarding.config.OfflineDataSyncConfig;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import java.util.Collections;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.eq;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class OfflineRequestHandlerDispatch1Test {

    @InjectMocks
    private OfflineRequestHandler offlineRequestHandler;

    @Mock
    private RpcInvocation invocation;

    private MockedStatic<Environment> environmentMockedStatic;

    private MockedStatic<Lion> lionMockedStatic;

    @After
    public void tearDown() {
        if (environmentMockedStatic != null) {
            environmentMockedStatic.close();
        }
        if (lionMockedStatic != null) {
            lionMockedStatic.close();
        }
    }

    @Test
    public void testDispatch_WhenInvocationIsNull() throws Throwable {
        environmentMockedStatic = mockStatic(Environment.class);
        environmentMockedStatic.when(Environment::isProductEnv).thenReturn(false);
        OfflineRequestHandlerResponseDTO result = offlineRequestHandler.dispatch(null);
        assertNotNull(result);
    }

    @Test
    public void testDispatch_WhenConfigIsNull() throws Throwable {
        environmentMockedStatic = mockStatic(Environment.class);
        environmentMockedStatic.when(Environment::isProductEnv).thenReturn(false);
        lionMockedStatic = mockStatic(Lion.class);
        lionMockedStatic.when(() -> Lion.getBean(any(), eq(OfflineDataSyncConfig.class))).thenReturn(null);
        OfflineRequestHandlerResponseDTO result = offlineRequestHandler.dispatch(invocation);
        assertNotNull(result);
    }

    @Test
    public void testDispatch_WhenInterfaceNamesEmptyAndArgumentsNull() throws Throwable {
        environmentMockedStatic = mockStatic(Environment.class);
        environmentMockedStatic.when(Environment::isProductEnv).thenReturn(false);
        OfflineDataSyncConfig config = new OfflineDataSyncConfig();
        config.setInterfaceNames(Collections.emptyList());
        lionMockedStatic = mockStatic(Lion.class);
        lionMockedStatic.when(() -> Lion.getBean(any(), eq(OfflineDataSyncConfig.class))).thenReturn(config);
        // Mock a specific interface class instead of Object.class
        when(invocation.getServiceInterface()).thenReturn((Class) String.class);
        OfflineRequestHandlerResponseDTO result = offlineRequestHandler.dispatch(invocation);
        assertNotNull(result);
    }
}
