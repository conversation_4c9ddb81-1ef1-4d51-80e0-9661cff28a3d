package com.sankuai.scrm.core.service.infrastructure.acl.mtusercenter;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.sankuai.wpt.user.merge.query.thrift.message.BindRelation;
import com.sankuai.wpt.user.merge.query.thrift.message.BindRelationResp;
import com.sankuai.wpt.user.merge.query.thrift.message.UserIdModel;
import com.sankuai.wpt.user.merge.query.thrift.message.UserMergeQueryService;
import org.apache.thrift.TException;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class MtUserCenterAclServiceGetMtUserIdByDpUserIdTest {

    @Mock
    private UserMergeQueryService.Iface userMergeQueryService;

    @InjectMocks
    private MtUserCenterAclService mtUserCenterAclService;

    /**
     * Test when dpUserId is null should return null
     */
    @Test
    public void testGetMtUserIdByDpUserId_NullInput() throws Throwable {
        // arrange - no setup needed for null input
        // act
        Long result = mtUserCenterAclService.getMtUserIdByDpUserId(null);
        // assert
        assertNull(result);
        verifyNoInteractions(userMergeQueryService);
    }

    /**
     * Test when dpUserId is zero or negative should return null
     */
    @Test
    public void testGetMtUserIdByDpUserId_ZeroOrNegativeInput() throws Throwable {
        // arrange - no setup needed for invalid input
        // act & assert for 0
        assertNull(mtUserCenterAclService.getMtUserIdByDpUserId(0L));
        // act & assert for negative
        assertNull(mtUserCenterAclService.getMtUserIdByDpUserId(-1L));
        verifyNoInteractions(userMergeQueryService);
    }

    /**
     * Test when service returns successful response with valid mtUserId
     */
    @Test
    public void testGetMtUserIdByDpUserId_SuccessWithValidMtUserId() throws Throwable {
        // arrange
        long dpUserId = 123L;
        long expectedMtUserId = 456L;
        BindRelationResp mockResp = new BindRelationResp();
        mockResp.setSuccess(true);
        UserIdModel userIdModel = new UserIdModel();
        userIdModel.setId(expectedMtUserId);
        mockResp.setData(new BindRelation());
        mockResp.getData().setMtUserId(userIdModel);
        when(userMergeQueryService.getRealBindByDpUserId(dpUserId)).thenReturn(mockResp);
        // act
        Long result = mtUserCenterAclService.getMtUserIdByDpUserId(dpUserId);
        // assert
        assertEquals(expectedMtUserId, result);
        verify(userMergeQueryService).getRealBindByDpUserId(dpUserId);
    }

    /**
     * Test when service returns successful response but null data
     */
    @Test
    public void testGetMtUserIdByDpUserId_SuccessButNullData() throws Throwable {
        // arrange
        long dpUserId = 123L;
        BindRelationResp mockResp = new BindRelationResp();
        mockResp.setSuccess(true);
        mockResp.setData(null);
        when(userMergeQueryService.getRealBindByDpUserId(dpUserId)).thenReturn(mockResp);
        // act
        Long result = mtUserCenterAclService.getMtUserIdByDpUserId(dpUserId);
        // assert
        assertNull(result);
        verify(userMergeQueryService).getRealBindByDpUserId(dpUserId);
    }

    /**
     * Test when service returns successful response but null mtUserId
     */
    @Test
    public void testGetMtUserIdByDpUserId_SuccessButNullMtUserId() throws Throwable {
        // arrange
        long dpUserId = 123L;
        BindRelationResp mockResp = new BindRelationResp();
        mockResp.setSuccess(true);
        mockResp.setData(new BindRelation());
        mockResp.getData().setMtUserId(null);
        when(userMergeQueryService.getRealBindByDpUserId(dpUserId)).thenReturn(mockResp);
        // act
        Long result = mtUserCenterAclService.getMtUserIdByDpUserId(dpUserId);
        // assert
        assertNull(result);
        verify(userMergeQueryService).getRealBindByDpUserId(dpUserId);
    }

    /**
     * Test when service throws TException
     */
    @Test
    public void testGetMtUserIdByDpUserId_ThrowsException() throws Throwable {
        // arrange
        long dpUserId = 123L;
        when(userMergeQueryService.getRealBindByDpUserId(dpUserId)).thenThrow(new TException("Test exception"));
        // act
        Long result = mtUserCenterAclService.getMtUserIdByDpUserId(dpUserId);
        // assert
        assertNull(result);
        verify(userMergeQueryService).getRealBindByDpUserId(dpUserId);
    }

    /**
     * Test when service returns unsuccessful response
     */
    @Test
    public void testGetMtUserIdByDpUserId_UnsuccessfulResponse() throws Throwable {
        // arrange
        long dpUserId = 123L;
        BindRelationResp mockResp = new BindRelationResp();
        mockResp.setSuccess(false);
        when(userMergeQueryService.getRealBindByDpUserId(dpUserId)).thenReturn(mockResp);
        // act
        Long result = mtUserCenterAclService.getMtUserIdByDpUserId(dpUserId);
        // assert
        assertNull(result);
        verify(userMergeQueryService).getRealBindByDpUserId(dpUserId);
    }
}
