package com.sankuai.scrm.core.service.infrastructure.acl.haima;

import com.dianping.haima.client.HaimaClient;
import com.dianping.haima.entity.haima.HaimaConfig;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Spy;
import java.util.ArrayList;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import static org.mockito.ArgumentMatchers.any;
import com.dianping.haima.client.request.HaimaRequest;
import com.dianping.haima.client.response.HaimaResponse;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;
import static org.mockito.Mockito.*;
import org.mockito.junit.*;

@RunWith(MockitoJUnitRunner.class)
public class HaimaAclService_GetConfigTest {

    @Spy
    @InjectMocks
    private HaimaAclService haimaAclService;

    @Mock(lenient = true)
    private HaimaClient haimaClient;

    private final String key = "testKey";

    private final Map<String, String> params = new HashMap<>();

    private final Class<HaimaConfig> clazz = HaimaConfig.class;

    @Before
    public void setUp() {
        params.put("key1", "value1");
    }

    /**
     * Test getConfig method when getConfigListByParams returns an empty list.
     */
    @Test
    public void testGetConfigWhenListIsEmpty() throws Throwable {
        doReturn(new ArrayList<HaimaConfig>()).when(haimaAclService).getConfigListByParams(any(), any(), any());
        HaimaConfig result = haimaAclService.getConfig(key, params, clazz);
        assertNull(result);
    }

    /**
     * Test getConfig method when getConfigListByParams returns a non-empty list.
     */
    @Test
    public void testGetConfigWhenListIsNotEmpty() throws Throwable {
        HaimaConfig mockConfig = new HaimaConfig();
        List<HaimaConfig> mockList = new ArrayList<>();
        mockList.add(mockConfig);
        doReturn(mockList).when(haimaAclService).getConfigListByParams(any(), any(), any());
        HaimaConfig result = haimaAclService.getConfig(key, params, clazz);
        assertNotNull(result);
    }

    /**
     * Test getConfig method when getConfigListByParams returns null.
     */
    @Test
    public void testGetConfigWhenReturnIsNull() throws Throwable {
        doReturn(null).when(haimaAclService).getConfigListByParams(any(), any(), any());
        HaimaConfig result = haimaAclService.getConfig(key, params, clazz);
        assertNull(result);
    }
}
