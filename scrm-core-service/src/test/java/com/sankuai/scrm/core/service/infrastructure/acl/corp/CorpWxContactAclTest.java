package com.sankuai.scrm.core.service.infrastructure.acl.corp;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;
import com.dianping.baby.http.HttpClientUtil;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxContactUserDetail;
import com.sankuai.scrm.core.service.infrastructure.acl.corp.entity.WxContactUserDetailResponse;
import java.io.IOException;
import java.util.HashMap;
import java.util.Map;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.mockito.Spy;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
class CorpWxContactAclTest {

    @Mock
    private CorpWxAcl corpWxAclService;

    @Mock
    private ObjectMapper objectMapper;

    @InjectMocks
    private CorpWxContactAcl corpWxContactAcl;

    @BeforeEach
    void setUp() {
        reset(corpWxAclService, objectMapper);
    }

    /**
     * 测试corpId为空时抛出IllegalArgumentException
     */
    @Test
    void testGetUserDetail_CorpIdIsBlank() throws Throwable {
        // arrange
        String corpId = "";
        String externalUserId = "external123";
        // act & assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> corpWxContactAcl.getUserDetail(corpId, externalUserId));
        assertEquals("查询企微用户详情需要企业id或者token", exception.getMessage());
    }

    /**
     * 测试externalUserId为空时抛出IllegalArgumentException
     */
    @Test
    void testGetUserDetail_ExternalUserIdIsBlank() throws Throwable {
        // arrange
        String corpId = "corp123";
        String externalUserId = "";
        // act & assert
        IllegalArgumentException exception = assertThrows(IllegalArgumentException.class, () -> corpWxContactAcl.getUserDetail(corpId, externalUserId));
        assertEquals("查询企微客户详情需要外部联系人id", exception.getMessage());
    }

    /**
     * 测试获取token失败时抛出RuntimeException
     */
    @Test
    void testGetUserDetail_GetTokenFailed() throws Throwable {
        // arrange
        String corpId = "corp123";
        String externalUserId = "external123";
        when(corpWxAclService.getTokenByCorpId(corpId)).thenReturn("");
        // act & assert
        RuntimeException exception = assertThrows(RuntimeException.class, () -> corpWxContactAcl.getUserDetail(corpId, externalUserId));
        assertEquals("获取企微查询token失败", exception.getMessage());
    }

    /**
     * 测试API调用成功返回用户详情
     */
    @Test
    void testGetUserDetail_ApiCallSuccess() throws Throwable {
        // arrange
        String corpId = "corp123";
        String externalUserId = "external123";
        String token = "token123";
        String apiResponse = "{\"errcode\":0,\"external_contact\":{\"external_userid\":\"external123\",\"name\":\"test\"}}";
        WxContactUserDetailResponse response = new WxContactUserDetailResponse();
        response.setErrCode(0);
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setExternalUserId(externalUserId);
        userDetail.setName("test");
        response.setUserDetail(userDetail);
        when(corpWxAclService.getTokenByCorpId(corpId)).thenReturn(token);
        try (MockedStatic<HttpClientUtil> httpClientUtil = mockStatic(HttpClientUtil.class)) {
            httpClientUtil.when(() -> HttpClientUtil.get(anyString(), anyMap(), anyMap())).thenReturn(apiResponse);
            // act
            WxContactUserDetail result = corpWxContactAcl.getUserDetail(corpId, externalUserId);
            // assert
            assertNotNull(result);
            assertEquals(externalUserId, result.getExternalUserId());
            assertEquals("test", result.getName());
            httpClientUtil.verify(() -> HttpClientUtil.get(anyString(), anyMap(), anyMap()), times(1));
        }
    }

    /**
     * 测试第一次API调用失败但第二次成功
     */
    @Test
    void testGetUserDetail_RetrySuccess() throws Throwable {
        // arrange
        String corpId = "corp123";
        String externalUserId = "external123";
        String token = "token123";
        String failResponse = "{\"errcode\":40001}";
        String successResponse = "{\"errcode\":0,\"external_contact\":{\"external_userid\":\"external123\",\"name\":\"test\"}}";
        WxContactUserDetailResponse failResponseObj = new WxContactUserDetailResponse();
        failResponseObj.setErrCode(40001);
        WxContactUserDetailResponse successResponseObj = new WxContactUserDetailResponse();
        successResponseObj.setErrCode(0);
        WxContactUserDetail userDetail = new WxContactUserDetail();
        userDetail.setExternalUserId(externalUserId);
        userDetail.setName("test");
        successResponseObj.setUserDetail(userDetail);
        when(corpWxAclService.getTokenByCorpId(corpId)).thenReturn(token);
        try (MockedStatic<HttpClientUtil> httpClientUtil = mockStatic(HttpClientUtil.class)) {
            httpClientUtil.when(() -> HttpClientUtil.get(anyString(), anyMap(), anyMap())).thenReturn(failResponse).thenReturn(successResponse);
            // act
            WxContactUserDetail result = corpWxContactAcl.getUserDetail(corpId, externalUserId);
            // assert
            assertNotNull(result);
            assertEquals(externalUserId, result.getExternalUserId());
            assertEquals("test", result.getName());
            httpClientUtil.verify(() -> HttpClientUtil.get(anyString(), anyMap(), anyMap()), times(2));
        }
    }

    /**
     * 测试两次API调用都失败返回null
     */
    @Test
    void testGetUserDetail_BothApiCallsFail() throws Throwable {
        // arrange
        String corpId = "corp123";
        String externalUserId = "external123";
        String token = "token123";
        String failResponse = "{\"errcode\":40001}";
        WxContactUserDetailResponse failResponseObj = new WxContactUserDetailResponse();
        failResponseObj.setErrCode(40001);
        when(corpWxAclService.getTokenByCorpId(corpId)).thenReturn(token);
        try (MockedStatic<HttpClientUtil> httpClientUtil = mockStatic(HttpClientUtil.class)) {
            httpClientUtil.when(() -> HttpClientUtil.get(anyString(), anyMap(), anyMap())).thenReturn(failResponse).thenReturn(failResponse);
            // act
            WxContactUserDetail result = corpWxContactAcl.getUserDetail(corpId, externalUserId);
            // assert
            assertNull(result);
            httpClientUtil.verify(() -> HttpClientUtil.get(anyString(), anyMap(), anyMap()), times(2));
        }
    }

    /**
     * 测试API返回空结果时返回null
     */
    @Test
    void testGetUserDetail_EmptyResponse() throws Throwable {
        // arrange
        String corpId = "corp123";
        String externalUserId = "external123";
        String token = "token123";
        when(corpWxAclService.getTokenByCorpId(corpId)).thenReturn(token);
        try (MockedStatic<HttpClientUtil> httpClientUtil = mockStatic(HttpClientUtil.class)) {
            httpClientUtil.when(() -> HttpClientUtil.get(anyString(), anyMap(), anyMap())).thenReturn("");
            // act
            WxContactUserDetail result = corpWxContactAcl.getUserDetail(corpId, externalUserId);
            // assert
            assertNull(result);
            httpClientUtil.verify(() -> HttpClientUtil.get(anyString(), anyMap(), anyMap()), times(2));
        }
    }

    /**
     * 测试网络请求异常时返回null
     */
    @Test
    void testGetUserDetail_NetworkException() throws Throwable {
        // arrange
        String corpId = "corp123";
        String externalUserId = "external123";
        String token = "token123";
        when(corpWxAclService.getTokenByCorpId(corpId)).thenReturn(token);
        try (MockedStatic<HttpClientUtil> httpClientUtil = mockStatic(HttpClientUtil.class)) {
            httpClientUtil.when(() -> HttpClientUtil.get(anyString(), anyMap(), anyMap())).thenThrow(new RuntimeException("network error"));
            // act
            WxContactUserDetail result = corpWxContactAcl.getUserDetail(corpId, externalUserId);
            // assert
            assertNull(result);
            httpClientUtil.verify(() -> HttpClientUtil.get(anyString(), anyMap(), anyMap()), times(1));
        }
    }
}
