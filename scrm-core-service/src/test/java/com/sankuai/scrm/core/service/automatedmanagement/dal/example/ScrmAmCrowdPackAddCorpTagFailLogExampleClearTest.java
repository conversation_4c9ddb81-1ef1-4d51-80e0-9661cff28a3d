package com.sankuai.scrm.core.service.automatedmanagement.dal.example;

import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

public class ScrmAmCrowdPackAddCorpTagFailLogExampleClearTest {

    private ScrmAmCrowdPackAddCorpTagFailLogExample example;

    @Before
    public void setUp() {
        example = new ScrmAmCrowdPackAddCorpTagFailLogExample();
    }

    /**
     * 测试 clear 方法
     */
    @Test
    public void testClear() {
        // arrange
        example.setOrderByClause("test");
        example.setDistinct(true);
        example.setRows(10);
        example.setOffset(10);
        ScrmAmCrowdPackAddCorpTagFailLogExample.Criteria criteria = example.createCriteria();
        example.getOredCriteria().add(criteria);
        // act
        example.clear();
        // assert
        assertTrue(example.getOredCriteria().isEmpty());
        assertNull(example.getOrderByClause());
        assertFalse(example.isDistinct());
        assertNull(example.getRows());
        assertNull(example.getOffset());
    }
}
