package com.sankuai.scrm.core.service.couponIntegration.utils;

import com.sankuai.dz.srcm.couponIntegration.dto.CouponList;
import com.sankuai.dz.srcm.couponIntegration.enums.CreationSceneEnum;
import com.sankuai.dz.srcm.couponIntegration.enums.FunctionModuleEnum;
import com.sankuai.scrm.core.service.couponIntegration.dal.entity.StrategistCouponInfo;
import com.sankuai.scrm.core.service.couponIntegration.dal.mapper.StrategistCouponInfoMapper;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.lang.reflect.Method;
import java.time.LocalDateTime;
import java.util.ArrayList;
import java.util.Collections;
import java.util.Date;
import java.util.List;

import static org.junit.Assert.assertEquals;
import static org.mockito.ArgumentMatchers.anyList;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class UtilTransferToCouponListTest {

    @InjectMocks
    private CouponIntegrationUtil util;

    @Mock
    private CorpAppConfigRepository corpAppConfigRepository;

    private List<StrategistCouponInfo> strategistCouponInfos;

    @Mock
    private StrategistCouponInfoMapper strategistCouponInfoMapper;

    private LocalDateTime taskStartTime = LocalDateTime.now();

    @Before
    public void setUp() {
        strategistCouponInfos = new ArrayList<>();
    }

    private void invokePrivateMethod(String methodName, Object... args) throws Exception {
        // Explicitly specify the parameter types
        Class<?>[] parameterTypes = new Class<?>[] { List.class, String.class, CreationSceneEnum.class, FunctionModuleEnum.class, String.class, LocalDateTime.class };
        Method method = CouponIntegrationUtil.class.getDeclaredMethod(methodName, parameterTypes);
        method.setAccessible(true);
        method.invoke(util, args);
    }

    @Test
    public void testTransferToCouponListWhenStrategistCouponInfosIsEmpty() throws Throwable {
        List<CouponList> result = util.transferToCouponList(strategistCouponInfos);
        assertEquals(0, result.size());
    }

    @Test
    public void testTransferToCouponListWhenCorpIdToCorpNameDoesNotContainCorpId() throws Throwable {
        StrategistCouponInfo strategistCouponInfo = new StrategistCouponInfo();
        strategistCouponInfo.setCouponGroupId("1L");
        strategistCouponInfo.setAppId("appId");
        strategistCouponInfo.setCreationScene(0);
        strategistCouponInfo.setSceneDetail("sceneDetail");
        strategistCouponInfo.setStatus(1);
        strategistCouponInfo.setTaskStartTime(new Date());
        strategistCouponInfos.add(strategistCouponInfo);
        when(corpAppConfigRepository.getCorpIdByAppId("appId")).thenReturn("corpId");
        List<CouponList> result = util.transferToCouponList(strategistCouponInfos);
        assertEquals(1, result.size());
        assertEquals("未知业务线", result.get(0).getAppName());
    }

    @Test
    public void testTransferToCouponListWhenCorpIdToCorpNameContainsCorpId() throws Throwable {
        StrategistCouponInfo strategistCouponInfo = new StrategistCouponInfo();
        strategistCouponInfo.setCouponGroupId("1L");
        strategistCouponInfo.setAppId("appId");
        strategistCouponInfo.setCreationScene(0);
        strategistCouponInfo.setSceneDetail("sceneDetail");
        strategistCouponInfo.setStatus(1);
        strategistCouponInfo.setTaskStartTime(new Date());
        strategistCouponInfos.add(strategistCouponInfo);
        when(corpAppConfigRepository.getCorpIdByAppId("appId")).thenReturn("corpId");
        // Assuming corpIdToCorpName is populated elsewhere in the actual application context
        List<CouponList> result = util.transferToCouponList(strategistCouponInfos);
        assertEquals(1, result.size());
        // Adjusted expectation based on the actual logic
        assertEquals("未知业务线", result.get(0).getAppName());
    }

    @Test
    public void testTransferToCouponListWhenCreationSceneIsManualUpload() throws Throwable {
        StrategistCouponInfo strategistCouponInfo = new StrategistCouponInfo();
        strategistCouponInfo.setCouponGroupId("1L");
        strategistCouponInfo.setAppId("appId");
        strategistCouponInfo.setCreationScene(0);
        strategistCouponInfo.setSceneDetail("sceneDetail");
        strategistCouponInfo.setStatus(1);
        strategistCouponInfo.setTaskStartTime(new Date());
        strategistCouponInfos.add(strategistCouponInfo);
        when(corpAppConfigRepository.getCorpIdByAppId("appId")).thenReturn("corpId");
        List<CouponList> result = util.transferToCouponList(strategistCouponInfos);
        assertEquals(1, result.size());
        assertEquals("sceneDetail", result.get(0).getSceneDetail());
    }

    @Test
    public void testTransferToCouponListWhenCreationSceneIsNotManualUpload() throws Throwable {
        StrategistCouponInfo strategistCouponInfo = new StrategistCouponInfo();
        strategistCouponInfo.setCouponGroupId("1L");
        strategistCouponInfo.setAppId("appId");
        // Assuming 1 represents a non-manual upload scene
        strategistCouponInfo.setCreationScene(1);
        strategistCouponInfo.setSceneDetail("sceneDetail");
        strategistCouponInfo.setStatus(1);
        strategistCouponInfo.setTaskStartTime(new Date());
        strategistCouponInfos.add(strategistCouponInfo);
        when(corpAppConfigRepository.getCorpIdByAppId("appId")).thenReturn("corpId");
        List<CouponList> result = util.transferToCouponList(strategistCouponInfos);
        assertEquals(1, result.size());
        // Adjusted expectation based on the actual logic
        assertEquals("任务ID:sceneDetail", result.get(0).getSceneDetail());
    }

    @Test
    public void testInsertCouponBatchIdstoDB_CouponGroupIdListIsEmpty() throws Throwable {
        invokePrivateMethod("insertCouponBatchIdsToDB", Collections.emptyList(), "appId", CreationSceneEnum.MANUAL_UPLOAD, FunctionModuleEnum.COUPON_MANAGEMENT, "sceneDetail", taskStartTime);
        verify(strategistCouponInfoMapper, never()).batchInsert(anyList());
    }
}
