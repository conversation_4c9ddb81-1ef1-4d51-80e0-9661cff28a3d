package com.sankuai.scrm.core.service.automatedmanagement.domainservice.crowdpack;

import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyDetailDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.crowdpack.ScrmCrowdPackUpdateStrategyInfoDTO;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.crowdpack.ScrmCrowdPackBaseInfoConverter;
import com.sankuai.scrm.core.service.automatedmanagement.converter.dto2do.crowdpack.ScrmCrowdPackUpdateStrategyConverter;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackBaseInfoDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmCrowdPackUpdateStrategyDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmCrowdPackUpdateStrategyDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackBaseInfoDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ext.ExtScrmAmCrowdPackDetailInfoDOMapper;
import com.sankuai.scrm.core.service.user.enums.ScrmUserTagEnum;
import org.apache.commons.collections4.CollectionUtils;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.verify;
import static org.mockito.Mockito.when;

class CrowdPackReadDomainServiceQueryCrowdPackDetailInfoTest {

    @Mock
    private ExtScrmAmCrowdPackBaseInfoDOMapper extScrmAmCrowdPackBaseInfoDOMapper;

    @Mock
    private ExtScrmAmCrowdPackDetailInfoDOMapper extScrmAmCrowdPackDetailInfoDOMapper;

    @Mock
    private ScrmAmCrowdPackUpdateStrategyDOMapper updateStrategyDOMapper;

    @Mock
    private ScrmCrowdPackBaseInfoConverter packBaseInfoConverter;

    @Mock
    private ScrmCrowdPackUpdateStrategyConverter updateStrategyConverter;

    @InjectMocks
    private CrowdPackReadDomainService crowdPackReadDomainService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test when base info not found
     */
    @Test
    public void testQueryCrowdPackDetailInfoBaseInfoNotFound() throws Throwable {
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(any())).thenReturn(null);
        ScrmCrowdPackDTO result = crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId");
        assertNull(result);
        verify(extScrmAmCrowdPackBaseInfoDOMapper).selectByPrimaryKey(1L);
    }

    /**
     * Test when appId doesn't match (case insensitive)
     */
    @Test
    public void testQueryCrowdPackDetailInfoAppIdNotMatch() throws Throwable {
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setAppId("differentApp");
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(any())).thenReturn(baseInfoDO);
        ScrmCrowdPackDTO result = crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId");
        assertNull(result);
    }

    /**
     * Test when no strategies found
     */
    @Test
    public void testQueryCrowdPackDetailInfoNoStrategies() throws Throwable {
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setAppId("appId");
        baseInfoDO.setValidPackVersion("v1");
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(any())).thenReturn(baseInfoDO);
        ScrmCrowdPackDTO dto = new ScrmCrowdPackDTO();
        dto.setId(1L);
        dto.setValidPackVersion("v1");
        when(packBaseInfoConverter.convertToDTO(any())).thenReturn(dto);
        when(updateStrategyDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        when(extScrmAmCrowdPackDetailInfoDOMapper.countByExample(any())).thenReturn(0L);
        ScrmCrowdPackDTO result = crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId");
        assertNotNull(result);
        assertNull(result.getScrmCrowdPackUpdateStrategyInfoDTO());
    }

    /**
     * Test with staff filter strategy
     */
    @Test
    public void testQueryCrowdPackDetailInfoWithStaffStrategy() throws Throwable {
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setAppId("appId");
        baseInfoDO.setValidPackVersion("v1");
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(any())).thenReturn(baseInfoDO);
        ScrmCrowdPackDTO dto = new ScrmCrowdPackDTO();
        dto.setId(1L);
        dto.setValidPackVersion("v1");
        when(packBaseInfoConverter.convertToDTO(any())).thenReturn(dto);
        when(extScrmAmCrowdPackDetailInfoDOMapper.countByExample(any())).thenReturn(0L);
        ScrmAmCrowdPackUpdateStrategyDO strategy = new ScrmAmCrowdPackUpdateStrategyDO();
        strategy.setFilterFieldId(ScrmUserTagEnum.STAFF_FRIENDS.getTagId());
        strategy.setParam("{\"executorType\":1,\"executorId\":\"123\",\"executorName\":\"test\"}");
        when(updateStrategyDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(strategy));
        ScrmCrowdPackDTO result = crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId");
        assertNotNull(result);
        assertNotNull(result.getScrmCrowdPackUpdateStrategyInfoDTO());
        assertTrue(CollectionUtils.isEmpty(result.getScrmCrowdPackUpdateStrategyInfoDTO().getCrowdPackUpdateStrategy()));
        assertEquals(1, result.getScrmCrowdPackUpdateStrategyInfoDTO().getCrowdPackUpdateStrategyStaff().size());
    }

    /**
     * Test with regular filter strategy
     */
    @Test
    public void testQueryCrowdPackDetailInfoWithRegularStrategy() throws Throwable {
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setAppId("appId");
        baseInfoDO.setValidPackVersion("v1");
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(any())).thenReturn(baseInfoDO);
        ScrmCrowdPackDTO dto = new ScrmCrowdPackDTO();
        dto.setId(1L);
        dto.setValidPackVersion("v1");
        when(packBaseInfoConverter.convertToDTO(any())).thenReturn(dto);
        when(extScrmAmCrowdPackDetailInfoDOMapper.countByExample(any())).thenReturn(0L);
        ScrmAmCrowdPackUpdateStrategyDO strategy = new ScrmAmCrowdPackUpdateStrategyDO();
        // Not staff friends
        strategy.setFilterFieldId(100L);
        strategy.setParam("[\"param1\",\"param2\"]");
        when(updateStrategyDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(strategy));
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO = new ScrmCrowdPackUpdateStrategyDetailDTO();
        when(updateStrategyConverter.convertToDTO(any())).thenReturn(detailDTO);
        ScrmCrowdPackDTO result = crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId");
        assertNotNull(result);
        assertNotNull(result.getScrmCrowdPackUpdateStrategyInfoDTO());
        assertEquals(1, result.getScrmCrowdPackUpdateStrategyInfoDTO().getCrowdPackUpdateStrategy().size());
        assertTrue(CollectionUtils.isEmpty(result.getScrmCrowdPackUpdateStrategyInfoDTO().getCrowdPackUpdateStrategyStaff()));
    }

    /**
     * Test with mixed strategies
     */
    @Test
    public void testQueryCrowdPackDetailInfoWithMixedStrategies() throws Throwable {
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setAppId("appId");
        baseInfoDO.setValidPackVersion("v1");
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(any())).thenReturn(baseInfoDO);
        ScrmCrowdPackDTO dto = new ScrmCrowdPackDTO();
        dto.setId(1L);
        dto.setValidPackVersion("v1");
        when(packBaseInfoConverter.convertToDTO(any())).thenReturn(dto);
        when(extScrmAmCrowdPackDetailInfoDOMapper.countByExample(any())).thenReturn(0L);
        List<ScrmAmCrowdPackUpdateStrategyDO> strategies = new ArrayList<>();
        // Staff strategy
        ScrmAmCrowdPackUpdateStrategyDO staffStrategy = new ScrmAmCrowdPackUpdateStrategyDO();
        staffStrategy.setFilterFieldId(ScrmUserTagEnum.STAFF_FRIENDS.getTagId());
        staffStrategy.setParam("{\"executorType\":1,\"executorId\":\"123\",\"executorName\":\"test\"}");
        strategies.add(staffStrategy);
        // Regular strategy
        ScrmAmCrowdPackUpdateStrategyDO regularStrategy = new ScrmAmCrowdPackUpdateStrategyDO();
        regularStrategy.setFilterFieldId(100L);
        regularStrategy.setParam("[\"param1\",\"param2\"]");
        strategies.add(regularStrategy);
        when(updateStrategyDOMapper.selectByExample(any())).thenReturn(strategies);
        ScrmCrowdPackUpdateStrategyDetailDTO detailDTO = new ScrmCrowdPackUpdateStrategyDetailDTO();
        when(updateStrategyConverter.convertToDTO(regularStrategy)).thenReturn(detailDTO);
        ScrmCrowdPackDTO result = crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId");
        assertNotNull(result);
        ScrmCrowdPackUpdateStrategyInfoDTO strategyInfo = result.getScrmCrowdPackUpdateStrategyInfoDTO();
        assertNotNull(strategyInfo);
        assertEquals(1, strategyInfo.getCrowdPackUpdateStrategy().size());
        assertEquals(1, strategyInfo.getCrowdPackUpdateStrategyStaff().size());
    }

    /**
     * Test with invalid JSON in staff strategy param - expects method to throw RuntimeException
     */
    @Test
    public void testQueryCrowdPackDetailInfoWithInvalidStaffParam() throws Throwable {
        ScrmAmCrowdPackBaseInfoDO baseInfoDO = new ScrmAmCrowdPackBaseInfoDO();
        baseInfoDO.setAppId("appId");
        baseInfoDO.setValidPackVersion("v1");
        when(extScrmAmCrowdPackBaseInfoDOMapper.selectByPrimaryKey(any())).thenReturn(baseInfoDO);
        ScrmCrowdPackDTO dto = new ScrmCrowdPackDTO();
        dto.setId(1L);
        dto.setValidPackVersion("v1");
        when(packBaseInfoConverter.convertToDTO(any())).thenReturn(dto);
        when(extScrmAmCrowdPackDetailInfoDOMapper.countByExample(any())).thenReturn(0L);
        ScrmAmCrowdPackUpdateStrategyDO strategy = new ScrmAmCrowdPackUpdateStrategyDO();
        strategy.setFilterFieldId(ScrmUserTagEnum.STAFF_FRIENDS.getTagId());
        strategy.setParam("invalid json");
        when(updateStrategyDOMapper.selectByExample(any())).thenReturn(Collections.singletonList(strategy));
        // Expect the method to throw RuntimeException when JSON parsing fails
        assertThrows(RuntimeException.class, () -> crowdPackReadDomainService.queryCrowdPackDetailInfo(1L, "appId"));
    }
}
