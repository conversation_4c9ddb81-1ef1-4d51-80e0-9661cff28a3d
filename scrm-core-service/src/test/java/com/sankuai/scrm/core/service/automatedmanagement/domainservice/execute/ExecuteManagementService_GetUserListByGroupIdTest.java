package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.ExecuteManagementService;
import com.sankuai.scrm.core.service.group.dal.entity.MemberInfoEntity;
import com.sankuai.scrm.core.service.group.dal.example.MemberInfoEntityExample;
import com.sankuai.scrm.core.service.group.dal.babymapper.MemberInfoEntityMapper;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import java.util.Collections;
import java.util.List;
import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ExecuteManagementService_GetUserListByGroupIdTest {

    @InjectMocks
    private ExecuteManagementService executeManagementService;

    @Mock(lenient = true)
    private MemberInfoEntityMapper memberInfoEntityMapper;

    @Mock(lenient = true)
    private CorpAppConfigRepository appConfigRepository;

    private String groupId = "groupId";

    private String appId = "appId";

    @Before
    public void setUp() {
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn("corpId");
    }

    /**
     * 测试正常场景
     */
    @Test
    public void testGetUserListByGroupIdNormal() {
        // arrange
        List<MemberInfoEntity> expected = Collections.singletonList(new MemberInfoEntity());
        when(memberInfoEntityMapper.selectByExample(any(MemberInfoEntityExample.class))).thenReturn(expected);
        // act
        List<MemberInfoEntity> actual = executeManagementService.getUserListByGroupId(groupId, appId);
        // assert
        assertEquals(expected, actual);
        verify(memberInfoEntityMapper, times(1)).selectByExample(any(MemberInfoEntityExample.class));
    }

    /**
     * 测试边界场景
     */
    @Test
    public void testGetUserListByGroupIdBoundary() {
        // arrange
        when(memberInfoEntityMapper.selectByExample(any(MemberInfoEntityExample.class))).thenReturn(Collections.emptyList());
        // act
        List<MemberInfoEntity> actual = executeManagementService.getUserListByGroupId(groupId, appId);
        // assert
        assertEquals(Collections.emptyList(), actual);
        verify(memberInfoEntityMapper, times(1)).selectByExample(any(MemberInfoEntityExample.class));
    }

    /**
     * 测试异常场景
     */
    @Test(expected = Exception.class)
    public void testGetUserListByGroupIdException() {
        // arrange
        when(memberInfoEntityMapper.selectByExample(any(MemberInfoEntityExample.class))).thenThrow(new RuntimeException());
        // act
        executeManagementService.getUserListByGroupId(groupId, appId);
        // assert
        verify(memberInfoEntityMapper, times(1)).selectByExample(any(MemberInfoEntityExample.class));
    }
}
