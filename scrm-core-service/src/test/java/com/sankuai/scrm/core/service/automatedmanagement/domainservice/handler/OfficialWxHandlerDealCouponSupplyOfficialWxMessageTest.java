package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.ArgumentMatchers.anyString;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionContentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.dto.StepExecuteResultDTO;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.WxGroupSendMessageAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.response.WxGroupSendMessageResponse;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.List;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerDealCouponSupplyOfficialWxMessageTest {

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeLogDOMapper wxInvokeLogDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private WxGroupSendMessageAcl wxGroupSendMessageAcl;

    @Mock
    private WeChatTokenAcl weChatTokenAcl;

    @Mock
    private ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO;

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    private ScrmProcessOrchestrationDTO processOrchestrationDTO;

    private String executorId;

    private InvokeDetailKeyObject keyObject;

    private List<ScrmAmProcessOrchestrationWxInvokeDetailDO> totalInvokeDetailDOS;

    private StepExecuteResultDTO stepExecuteResultDTO;

    @BeforeEach
    void setUp() {
        processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setId(1L);
        processOrchestrationDTO.setAppId("testAppId");
        processOrchestrationDTO.setValidVersion("1.0");
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        executorId = "testExecutor";
        keyObject = new InvokeDetailKeyObject("testType", (byte) 1, (byte) 1, 1L);
        stepExecuteResultDTO = new StepExecuteResultDTO();
        totalInvokeDetailDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setProcessOrchestrationNodeId(1L);
        detailDO.setTargetId("testTarget");
        detailDO.setExecuteLogId(1L);
        totalInvokeDetailDOS.add(detailDO);
        // Setup ActionDTO and ContentDTO
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        actionDTO.setProcessOrchestrationNodeId(1L);
        List<ScrmProcessOrchestrationActionContentDTO> contentDTOS = new ArrayList<>();
        ScrmProcessOrchestrationActionContentDTO contentDTO = new ScrmProcessOrchestrationActionContentDTO();
        contentDTO.setContent("Test content");
        contentDTOS.add(contentDTO);
        // Setup mocks for NodeMediumDTO
    }

    /**
     * Test empty invoke detail list scenario
     */
    @Test
    public void testDealCouponSupplyOfficialWxMessage_EmptyDetailList() throws Throwable {
        // arrange
        totalInvokeDetailDOS = Collections.emptyList();
        // act
        officialWxHandler.dealCouponSupplyOfficialWxMessage(processOrchestrationDTO, executorId, keyObject, totalInvokeDetailDOS, stepExecuteResultDTO);
        // assert
        verify(wxInvokeLogDOMapper, never()).insert(any());
        verify(wxInvokeDetailDOMapper, never()).updateByExampleSelective(any(), any());
    }
}
