package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import com.sankuai.dz.srcm.automatedmanagement.enums.ScrmProcessOrchestrationTaskTypeEnum;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class ExecuteManagementService_IsTaskRunningTest {

    @InjectMocks
    private ExecuteManagementService executeManagementService;

    @Mock(lenient = true)
    private RedisStoreClient redisClient;

    @Before
    public void setUp() {
        when(redisClient.get(any(StoreKey.class))).thenReturn(1);
    }

    /**
     * 测试任务状态不为空且大于0的情况
     */
    @Test
    public void testIsTaskRunning_NormalCase1() {
        assertTrue(executeManagementService.isTaskRunning(1, 1L));
    }

    /**
     * 测试任务类型是PERIODIC_CROWD_PACK_UPDATE，任务状态或主任务状态不为空且大于0的情况
     */
    @Test
    public void testIsTaskRunning_NormalCase2() {
        assertTrue(executeManagementService.isTaskRunning(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue().intValue(), 1L));
    }

    /**
     * 测试任务状态为空或小于等于0的情况
     */
    @Test
    public void testIsTaskRunning_ExceptionCase1() {
        when(redisClient.get(any(StoreKey.class))).thenReturn(0);
        assertFalse(executeManagementService.isTaskRunning(1, 1L));
    }

    /**
     * 测试任务类型是PERIODIC_CROWD_PACK_UPDATE，任务状态和主任务状态都为空或小于等于0的情况
     */
    @Test
    public void testIsTaskRunning_ExceptionCase2() {
        when(redisClient.get(any(StoreKey.class))).thenReturn(0);
        assertFalse(executeManagementService.isTaskRunning(ScrmProcessOrchestrationTaskTypeEnum.PERIODIC_CROWD_PACK_UPDATE.getValue().intValue(), 1L));
    }
}
