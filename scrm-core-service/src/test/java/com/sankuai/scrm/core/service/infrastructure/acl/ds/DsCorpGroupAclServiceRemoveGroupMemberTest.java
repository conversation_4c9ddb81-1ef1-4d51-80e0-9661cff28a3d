package com.sankuai.scrm.core.service.infrastructure.acl.ds;

import com.sankuai.scrm.core.service.infrastructure.acl.ds.DsCorpGroupAclService;
import com.sankuai.service.fe.corp.wx.thrift.open.UserTypeEnum;
import com.sankuai.service.fe.corp.wx.thrift.internal.model.SimpleResponse;
import org.apache.thrift.TException;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;
import static org.mockito.Mockito.*;
import com.sankuai.service.fe.corp.wx.thrift.open.OpenGroupService;
import static org.junit.Assert.*;
import org.junit.*;
import org.junit.runner.RunWith.*;

@RunWith(MockitoJUnitRunner.class)
public class DsCorpGroupAclServiceRemoveGroupMemberTest {

    @InjectMocks
    private DsCorpGroupAclService dsCorpGroupAclService;

    @Mock
    private OpenGroupService.Iface openGroupService;

    private long bizId = 1L;

    private String businessCode = "testBusinessCode";

    private String groupId = "testGroupId";

    private String userId = "testUserId";

    private String unionId = "testUnionId";

    private String assistantAccount = "testAssistantAccount";

    private UserTypeEnum userType = UserTypeEnum.EXTERNAL;

    @Before
    public void setUp() throws Exception {
        when(openGroupService.removeGroupMember(any())).thenReturn(new SimpleResponse());
    }

    @Test(expected = IllegalArgumentException.class)
    public void testRemoveGroupMemberBusinessCodeIsNull() throws Throwable {
        dsCorpGroupAclService.removeGroupMember(bizId, null, groupId, userId, unionId, assistantAccount, userType);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testRemoveGroupMemberGroupIdIsNull() throws Throwable {
        dsCorpGroupAclService.removeGroupMember(bizId, businessCode, null, userId, unionId, assistantAccount, userType);
    }

    @Test(expected = IllegalArgumentException.class)
    public void testRemoveGroupMemberUserIdAndUnionIdAreNull() throws Throwable {
        dsCorpGroupAclService.removeGroupMember(bizId, businessCode, groupId, null, null, assistantAccount, userType);
    }

    @Test
    public void testRemoveGroupMemberThrowsTException() throws Throwable {
        when(openGroupService.removeGroupMember(any())).thenThrow(TException.class);
        assertFalse(dsCorpGroupAclService.removeGroupMember(bizId, businessCode, groupId, userId, unionId, assistantAccount, userType));
    }

    @Test
    public void testRemoveGroupMemberResponseCodeIsNotZero() throws Throwable {
        SimpleResponse response = new SimpleResponse();
        response.setCode(1);
        when(openGroupService.removeGroupMember(any())).thenReturn(response);
        assertFalse(dsCorpGroupAclService.removeGroupMember(bizId, businessCode, groupId, userId, unionId, assistantAccount, userType));
    }

    @Test
    public void testRemoveGroupMemberResponseCodeIsZero() throws Throwable {
        SimpleResponse response = new SimpleResponse();
        response.setCode(0);
        when(openGroupService.removeGroupMember(any())).thenReturn(response);
        assertTrue(dsCorpGroupAclService.removeGroupMember(bizId, businessCode, groupId, userId, unionId, assistantAccount, userType));
    }
}
