package com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler;

import com.google.common.collect.Lists;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationNodeMediumDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeDetailDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationWxInvokeLogDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.example.ScrmAmProcessOrchestrationWxInvokeDetailDOExample;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationExecuteLogDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.dal.mapper.ScrmAmProcessOrchestrationWxInvokeDetailDOMapper;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.actions.dto.InvokeDetailKeyObject;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.handler.strategy.GroupSendStrategy;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.productpool.ProductManagementService;
import com.sankuai.scrm.core.service.infrastructure.acl.UploadWxMediaAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.WeChatTokenAcl;
import com.sankuai.scrm.core.service.infrastructure.acl.wx.request.vo.AttachmentVO;
import com.sankuai.scrm.core.service.infrastructure.repository.CorpAppConfigRepository;
import com.sankuai.scrm.core.service.infrastructure.vo.WeChatTokenResult;
import com.sankuai.scrm.core.service.infrastructure.vo.WechatMediaResult;
import com.sankuai.scrm.core.service.infrastructure.vo.WxMediaType;
import com.sankuai.scrm.core.service.message.push.dto.MsgTaskDetailResultDTO;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationActionAttachmentDTO;
import com.sankuai.dz.srcm.automatedmanagement.dto.processorchestration.ScrmProcessOrchestrationAttachmentSupplyDetailDTO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationActivSceneCodeDO;
import com.sankuai.scrm.core.service.automatedmanagement.dal.entity.ScrmAmProcessOrchestrationProductActivityPageDO;
import com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute.InformationGatheringService;
import com.sankuai.scrm.core.service.message.push.dto.MsgPushContentDTO;
import com.sankuai.service.fe.corp.ds.TRequest.openapi.msg.content.MiniProgramDTO;
import com.sankuai.service.fe.corp.ds.enums.msg.ContentTypeTEnum;
import java.util.HashMap;
import java.util.Map;
import org.mockito.junit.*;

@ExtendWith(MockitoExtension.class)
public class OfficialWxHandlerDealAIRecallSupplyOfficialWxGroupMessageV21Test {

    @InjectMocks
    private OfficialWxHandler officialWxHandler;

    @Mock
    private ProductManagementService productManagementService;

    @Mock
    private GroupSendStrategy groupSendStrategy;

    @Mock
    private CorpAppConfigRepository appConfigRepository;

    @Mock
    private WeChatTokenAcl weChatTokenAcl;

    @Mock
    private UploadWxMediaAcl uploadWxMediaAcl;

    private static final String GROUP_MESSAGE = "groupMessage";

    private static final String MESSAGE = "message";

    @Mock
    private ScrmAmProcessOrchestrationWxInvokeDetailDOMapper wxInvokeDetailDOMapper;

    @Mock
    private ScrmAmProcessOrchestrationExecuteLogDOMapper executeLogDOMapper;

    @Mock
    private InformationGatheringService informationGatheringService;

    @BeforeEach
    void setUp() {
        MockitoAnnotations.openMocks(this);
    }

    /**
     * Test case for empty invoke detail list
     */
    @Test
    public void testDealAIRecallSupplyOfficialWxGroupMessageV2_EmptyInvokeDetails() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        List<String> executorId = Lists.newArrayList("executor1", "executor12");
        InvokeDetailKeyObject keyObject = new InvokeDetailKeyObject("key", (byte) 1, (byte) 1, 1L);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> emptyList = new ArrayList<>();
        // act
        officialWxHandler.dealAIRecallSupplyOfficialWxGroupMessageV2(processOrchestrationDTO, executorId, keyObject, emptyList, null);
        // assert
        verify(productManagementService, never()).queryActivityPagesById(any());
        verify(groupSendStrategy, never()).processSuccess(any(), any(), any(), any(), any());
    }

    /**
     * Test case for message sending with invalid product type
     */
    @Test
    public void testDealAIRecallSupplyOfficialWxGroupMessageV2_InvalidProductType() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testApp");
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = mock(ScrmProcessOrchestrationNodeMediumDTO.class);
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO.setProcessOrchestrationNodeId(1L);
        invokeDetailDO.setType("invalidType");
        ScrmProcessOrchestrationActionDTO actionDTO = new ScrmProcessOrchestrationActionDTO();
        when(nodeMediumDTO.getActionDTO(anyLong())).thenReturn(actionDTO);
        // act
        officialWxHandler.dealAIRecallSupplyOfficialWxGroupMessageV2(processOrchestrationDTO, Lists.newArrayList("executor1"), new InvokeDetailKeyObject("key", (byte) 1, (byte) 1, 1L), Lists.newArrayList(invokeDetailDO), null);
        // assert
        verify(productManagementService, never()).queryActivityPagesById(any());
        verify(groupSendStrategy, never()).processSuccess(any(), any(), any(), any(), any());
    }

    /**
     * Test case for message sending with empty content
     */
    @Test
    public void testDealAIRecallSupplyOfficialWxGroupMessageV2_EmptyContent() throws Throwable {
        // arrange
        ScrmProcessOrchestrationDTO processOrchestrationDTO = new ScrmProcessOrchestrationDTO();
        processOrchestrationDTO.setAppId("testApp");
        ScrmProcessOrchestrationNodeMediumDTO nodeMediumDTO = mock(ScrmProcessOrchestrationNodeMediumDTO.class);
        processOrchestrationDTO.setNodeMediumDTO(nodeMediumDTO);
        ScrmAmProcessOrchestrationWxInvokeDetailDO invokeDetailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        invokeDetailDO.setProcessOrchestrationNodeId(1L);
        when(nodeMediumDTO.getActionDTO(anyLong())).thenReturn(null);
        // act
        officialWxHandler.dealAIRecallSupplyOfficialWxGroupMessageV2(processOrchestrationDTO, Lists.newArrayList("executor1"), new InvokeDetailKeyObject("key", (byte) 1, (byte) 1, 1L), Lists.newArrayList(invokeDetailDO), null);
        // assert
        verify(productManagementService, never()).queryActivityPagesById(any());
        verify(groupSendStrategy, never()).processSuccess(any(), any(), any(), any(), any());
    }

    /**
     * Test case for successful execution of getOfficialLinkAttachmentVO
     */
    @Test
    public void testGetOfficialLinkAttachmentVO_SuccessfulExecution() throws Throwable {
        // arrange
        String title = "Test Title";
        String picUrl = "http://example.com/image.jpg";
        String url = "http://example.com";
        String appId = "testAppId";
        String corpId = "testCorpId";
        String accessToken = "testAccessToken";
        String mediaId = "testMediaId";
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
        when(weChatTokenAcl.getTokenByCorpId(corpId)).thenReturn(new WeChatTokenResult(0, "success", accessToken, 7200));
        when(uploadWxMediaAcl.uploadWxTmpMedia(picUrl, WxMediaType.image, accessToken, true)).thenReturn(new WechatMediaResult(mediaId, System.currentTimeMillis(), "image"));
        // act
        AttachmentVO result = officialWxHandler.getOfficialLinkAttachmentVO(title, picUrl, url, appId);
        // assert
        assertNotNull(result);
        assertEquals("link", result.getMsgtype());
        assertEquals(title, result.getLink().getTitle());
        assertEquals(picUrl, result.getLink().getPicurl());
        assertEquals(url, result.getLink().getUrl());
        assertEquals(mediaId, result.getLink().getMedia_id());
    }

    /**
     * Test case for null corpId returned by appConfigRepository
     */
    @Test
    public void testGetOfficialLinkAttachmentVO_NullCorpId() throws Throwable {
        // arrange
        String title = "Test Title";
        String picUrl = "http://example.com/image.jpg";
        String url = "http://example.com";
        String appId = "testAppId";
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(null);
        // act
        AttachmentVO result = officialWxHandler.getOfficialLinkAttachmentVO(title, picUrl, url, appId);
        // assert
        assertNull(result);
    }

    /**
     * Test case for unsuccessful WeChatTokenResult
     */
    @Test
    public void testGetOfficialLinkAttachmentVO_UnsuccessfulTokenResult() throws Throwable {
        // arrange
        String title = "Test Title";
        String picUrl = "http://example.com/image.jpg";
        String url = "http://example.com";
        String appId = "testAppId";
        String corpId = "testCorpId";
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
        when(weChatTokenAcl.getTokenByCorpId(corpId)).thenReturn(new WeChatTokenResult(1, "error", null, -1));
        // act
        AttachmentVO result = officialWxHandler.getOfficialLinkAttachmentVO(title, picUrl, url, appId);
        // assert
        assertNull(result);
    }

    /**
     * Test case for null WechatMediaResult from uploadWxMediaAcl
     */
    @Test
    public void testGetOfficialLinkAttachmentVO_NullMediaResult() throws Throwable {
        // arrange
        String title = "Test Title";
        String picUrl = "http://example.com/image.jpg";
        String url = "http://example.com";
        String appId = "testAppId";
        String corpId = "testCorpId";
        String accessToken = "testAccessToken";
        when(appConfigRepository.getCorpIdByAppId(appId)).thenReturn(corpId);
        when(weChatTokenAcl.getTokenByCorpId(corpId)).thenReturn(new WeChatTokenResult(0, "success", accessToken, 7200));
        when(uploadWxMediaAcl.uploadWxTmpMedia(picUrl, WxMediaType.image, accessToken, true)).thenReturn(null);
        // act
        AttachmentVO result = officialWxHandler.getOfficialLinkAttachmentVO(title, picUrl, url, appId);
        // assert
        assertNull(result);
    }

    /**
     * Test case for null input parameters
     */
    @Test
    public void testGetOfficialLinkAttachmentVO_NullInputParameters() throws Throwable {
        // arrange
        String title = null;
        String picUrl = null;
        String url = null;
        String appId = null;
        // act
        AttachmentVO result = officialWxHandler.getOfficialLinkAttachmentVO(title, picUrl, url, appId);
        // assert
        assertNull(result);
    }

    /**
     * Test when taskDetailResultDTOS is empty, method should return immediately
     */
    @Test
    public void testDealNotFriendResultV2EmptyTaskDetailList() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = Collections.emptyList();
        // act
        officialWxHandler.dealNotFriendResultV2(processOrchestrationId, processOrchestrationVersion, invokeLogDO, taskDetailResultDTOS);
        // assert
        verifyNoInteractions(wxInvokeDetailDOMapper);
        verifyNoInteractions(executeLogDOMapper);
    }

    /**
     * Test when no matching records found in database
     */
    @Test
    public void testDealNotFriendResultV2NoMatchingRecords() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setType(MESSAGE);
        MsgTaskDetailResultDTO taskDetail = new MsgTaskDetailResultDTO();
        taskDetail.setReceiverId("user1");
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = Collections.singletonList(taskDetail);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(Collections.emptyList());
        // act
        officialWxHandler.dealNotFriendResultV2(processOrchestrationId, processOrchestrationVersion, invokeLogDO, taskDetailResultDTOS);
        // assert
        verify(wxInvokeDetailDOMapper).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(wxInvokeDetailDOMapper, never()).updateByExampleSelective(any(), any());
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    /**
     * Test when executeLogId is null in detail record
     */
    @Test
    public void testDealNotFriendResultV2NullExecuteLogId() throws Throwable {
        // arrange
        Long processOrchestrationId = 1L;
        String processOrchestrationVersion = "v1";
        ScrmAmProcessOrchestrationWxInvokeLogDO invokeLogDO = new ScrmAmProcessOrchestrationWxInvokeLogDO();
        invokeLogDO.setType(MESSAGE);
        MsgTaskDetailResultDTO taskDetail = new MsgTaskDetailResultDTO();
        taskDetail.setReceiverId("user1");
        List<MsgTaskDetailResultDTO> taskDetailResultDTOS = Collections.singletonList(taskDetail);
        ScrmAmProcessOrchestrationWxInvokeDetailDO detailDO = new ScrmAmProcessOrchestrationWxInvokeDetailDO();
        detailDO.setExecuteLogId(null);
        List<ScrmAmProcessOrchestrationWxInvokeDetailDO> failedList = Collections.singletonList(detailDO);
        when(wxInvokeDetailDOMapper.selectByExample(any())).thenReturn(failedList);
        when(wxInvokeDetailDOMapper.updateByExampleSelective(any(), any())).thenReturn(1);
        // act
        officialWxHandler.dealNotFriendResultV2(processOrchestrationId, processOrchestrationVersion, invokeLogDO, taskDetailResultDTOS);
        // assert
        verify(wxInvokeDetailDOMapper).selectByExample(any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(wxInvokeDetailDOMapper).updateByExampleSelective(any(), any(ScrmAmProcessOrchestrationWxInvokeDetailDOExample.class));
        verify(executeLogDOMapper, never()).updateByExampleSelective(any(), any());
    }

    @Test
    public void testBuildActivityPageAttachmentsV2NullAttachment() throws Throwable {
        // arrange
        List<ScrmAmProcessOrchestrationProductActivityPageDO> pageDOS = new ArrayList<>();
        ScrmAmProcessOrchestrationProductActivityPageDO page = new ScrmAmProcessOrchestrationProductActivityPageDO();
        page.setId(1L);
        page.setRelatedProductIds("1,2,3");
        pageDOS.add(page);
        Map<String, MsgPushContentDTO> existedAttachmentMap = new HashMap<>();
        // Mock getActivityPageAttachmentVOV2 to return null
        OfficialWxHandler spyHandler = spy(officialWxHandler);
        doReturn(null).when(spyHandler).getActivityPageAttachmentVOV2(any(), any(), any(), any(), any(), any());
        // act
        List<MsgPushContentDTO> result = spyHandler.buildActivityPageAttachmentsV2(mock(ScrmProcessOrchestrationDTO.class), mock(ScrmProcessOrchestrationActionAttachmentDTO.class), null, pageDOS, existedAttachmentMap, false, null);
        // assert
        assertTrue(result.isEmpty());
    }
}
