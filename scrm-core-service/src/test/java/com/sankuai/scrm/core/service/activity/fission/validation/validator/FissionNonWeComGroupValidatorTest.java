package com.sankuai.scrm.core.service.activity.fission.validation.validator;

import static org.junit.jupiter.api.Assertions.*;
import static org.mockito.Mockito.*;
import com.sankuai.dz.srcm.activity.fission.dto.activity.PersonalGroupInfoDTO;
import com.sankuai.dz.srcm.activity.fission.request.GroupFissionActivityRequest;
import com.sankuai.scrm.core.service.activity.fission.dal.entity.GroupFissionActivity;
import com.sankuai.scrm.core.service.activity.fission.validation.exception.FissionValidatorException;
import java.lang.reflect.InvocationTargetException;
import java.lang.reflect.Method;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.Collections;
import java.util.Date;
import java.util.List;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.*;
import org.mockito.junit.jupiter.MockitoExtension;

@ExtendWith(MockitoExtension.class)
public class FissionNonWeComGroupValidatorTest {

    private final FissionNonWeComGroupValidator validator = new FissionNonWeComGroupValidator();

    @Mock
    private GroupFissionActivityRequest request;

    private void invokePrivateMethod(GroupFissionActivityRequest request, List<Long> existGroupIds) throws Exception {
        Method method = FissionNonWeComGroupValidator.class.getDeclaredMethod("checkUpdateGroupListWhenActivityStarted", GroupFissionActivityRequest.class, List.class);
        method.setAccessible(true);
        method.invoke(validator, request, existGroupIds);
    }

    private PersonalGroupInfoDTO createGroupInfo(Long dsGroupId) {
        PersonalGroupInfoDTO dto = new PersonalGroupInfoDTO();
        dto.setDsGroupId(dsGroupId);
        return dto;
    }

    /**
     * Helper method to invoke private hasOverlap method using reflection
     */
    private boolean invokeHasOverlap(List<GroupFissionActivity> activityList, Long startTime, Long endTime) throws Exception {
        Method method = FissionNonWeComGroupValidator.class.getDeclaredMethod("hasOverlap", List.class, Long.class, Long.class);
        method.setAccessible(true);
        return (boolean) method.invoke(new FissionNonWeComGroupValidator(), activityList, startTime, endTime);
    }

    /**
     * 创建模拟的PersonalGroupInfoDTO对象
     */
    private PersonalGroupInfoDTO createMockGroupInfoDTO(Long groupId) {
        PersonalGroupInfoDTO dto = mock(PersonalGroupInfoDTO.class);
        when(dto.getDsGroupId()).thenReturn(groupId);
        return dto;
    }

    @Test
    public void testCheckUpdateGroupListWhenActivityStartedEmptyRequestGroups() throws Throwable {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setPersonalGroupInfo(Collections.emptyList());
        List<Long> existGroupIds = Arrays.asList(1L, 2L);
        Exception exception = assertThrows(InvocationTargetException.class, () -> invokePrivateMethod(request, existGroupIds));
        assertTrue(exception.getCause() instanceof FissionValidatorException);
        assertEquals("活动开始前2分钟之后不允许删除群列表，只允许新增群", exception.getCause().getMessage());
    }

    @Test
    public void testCheckUpdateGroupListWhenActivityStartedSmallerRequestGroups() throws Throwable {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setPersonalGroupInfo(Arrays.asList(createGroupInfo(1L), createGroupInfo(2L)));
        List<Long> existGroupIds = Arrays.asList(1L, 2L, 3L);
        Exception exception = assertThrows(InvocationTargetException.class, () -> invokePrivateMethod(request, existGroupIds));
        assertTrue(exception.getCause() instanceof FissionValidatorException);
        assertEquals("活动开始前2分钟之后不允许删除群列表，只允许新增群", exception.getCause().getMessage());
    }

    @Test
    public void testCheckUpdateGroupListWhenActivityStartedChangedOrder() throws Throwable {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setPersonalGroupInfo(Arrays.asList(createGroupInfo(2L), createGroupInfo(1L)));
        List<Long> existGroupIds = Arrays.asList(1L, 2L);
        Exception exception = assertThrows(InvocationTargetException.class, () -> invokePrivateMethod(request, existGroupIds));
        assertTrue(exception.getCause() instanceof FissionValidatorException);
        assertEquals("活动开始前2分钟之后不允许删除群列表，且原有的群顺序不允许改变", exception.getCause().getMessage());
    }

    @Test
    public void testCheckUpdateGroupListWhenActivityStartedSameSizeAndOrder() throws Throwable {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setPersonalGroupInfo(Arrays.asList(createGroupInfo(1L), createGroupInfo(2L)));
        List<Long> existGroupIds = Arrays.asList(1L, 2L);
        assertDoesNotThrow(() -> invokePrivateMethod(request, existGroupIds));
    }

    @Test
    public void testCheckUpdateGroupListWhenActivityStartedLargerRequestGroups() throws Throwable {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setPersonalGroupInfo(Arrays.asList(createGroupInfo(1L), createGroupInfo(2L), createGroupInfo(3L)));
        List<Long> existGroupIds = Arrays.asList(1L, 2L);
        assertDoesNotThrow(() -> invokePrivateMethod(request, existGroupIds));
    }

    @Test
    public void testCheckUpdateGroupListWhenActivityStartedNullRequest() throws Throwable {
        List<Long> existGroupIds = Arrays.asList(1L, 2L);
        Exception exception = assertThrows(InvocationTargetException.class, () -> invokePrivateMethod(null, existGroupIds));
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    @Test
    public void testCheckUpdateGroupListWhenActivityStartedNullExistGroups() throws Throwable {
        GroupFissionActivityRequest request = new GroupFissionActivityRequest();
        request.setPersonalGroupInfo(Arrays.asList(createGroupInfo(1L)));
        Exception exception = assertThrows(InvocationTargetException.class, () -> invokePrivateMethod(request, null));
        assertTrue(exception.getCause() instanceof NullPointerException);
    }

    /**
     * Test when activity list is empty - should return false
     */
    @Test
    void testHasOverlapWithEmptyList() throws Throwable {
        // Arrange
        List<GroupFissionActivity> emptyList = Collections.emptyList();
        Long startTime = 1000L;
        Long endTime = 2000L;
        // Act
        boolean result = invokeHasOverlap(emptyList, startTime, endTime);
        // Assert
        assertFalse(result, "Empty list should not have overlap");
    }

    /**
     * Test when activity list is null - should return false
     */
    @Test
    void testHasOverlapWithNullList() throws Throwable {
        // Arrange
        Long startTime = 1000L;
        Long endTime = 2000L;
        // Act
        boolean result = invokeHasOverlap(null, startTime, endTime);
        // Assert
        assertFalse(result, "Null list should not have overlap");
    }

    /**
     * Test when single activity doesn't overlap with time range
     */
    @Test
    void testHasOverlapWithNonOverlappingActivity() throws Throwable {
        // Arrange
        GroupFissionActivity activity = new GroupFissionActivity();
        activity.setStartTime(new Date(3000L));
        activity.setEndTime(new Date(4000L));
        List<GroupFissionActivity> activityList = Collections.singletonList(activity);
        Long startTime = 1000L;
        Long endTime = 2000L;
        // Act
        boolean result = invokeHasOverlap(activityList, startTime, endTime);
        // Assert
        assertFalse(result, "Non-overlapping activity should return false");
    }

    /**
     * Test when single activity overlaps with time range
     */
    @Test
    void testHasOverlapWithOverlappingActivity() throws Throwable {
        // Arrange
        GroupFissionActivity activity = new GroupFissionActivity();
        activity.setStartTime(new Date(1500L));
        activity.setEndTime(new Date(2500L));
        List<GroupFissionActivity> activityList = Collections.singletonList(activity);
        Long startTime = 1000L;
        Long endTime = 2000L;
        // Act
        boolean result = invokeHasOverlap(activityList, startTime, endTime);
        // Assert
        assertTrue(result, "Overlapping activity should return true");
    }

    /**
     * Test when multiple activities exist with some overlapping
     */
    @Test
    void testHasOverlapWithMultipleActivitiesSomeOverlapping() throws Throwable {
        // Arrange
        GroupFissionActivity nonOverlapping = new GroupFissionActivity();
        nonOverlapping.setStartTime(new Date(3000L));
        nonOverlapping.setEndTime(new Date(4000L));
        GroupFissionActivity overlapping = new GroupFissionActivity();
        overlapping.setStartTime(new Date(1500L));
        overlapping.setEndTime(new Date(2500L));
        List<GroupFissionActivity> activityList = Arrays.asList(nonOverlapping, overlapping);
        Long startTime = 1000L;
        Long endTime = 2000L;
        // Act
        boolean result = invokeHasOverlap(activityList, startTime, endTime);
        // Assert
        assertTrue(result, "Should return true when at least one activity overlaps");
    }

    /**
     * Test boundary condition where activity end time equals range start time
     */
    @Test
    void testHasOverlapBoundaryConditionEndEqualsStart() throws Throwable {
        // Arrange
        GroupFissionActivity activity = new GroupFissionActivity();
        activity.setStartTime(new Date(500L));
        activity.setEndTime(new Date(1000L));
        List<GroupFissionActivity> activityList = Collections.singletonList(activity);
        Long startTime = 1000L;
        Long endTime = 2000L;
        // Act
        boolean result = invokeHasOverlap(activityList, startTime, endTime);
        // Assert
        assertTrue(result, "Boundary condition (end=start) should be considered overlapping");
    }

    /**
     * Test boundary condition where activity start time equals range end time
     */
    @Test
    void testHasOverlapBoundaryConditionStartEqualsEnd() throws Throwable {
        // Arrange
        GroupFissionActivity activity = new GroupFissionActivity();
        activity.setStartTime(new Date(2000L));
        activity.setEndTime(new Date(3000L));
        List<GroupFissionActivity> activityList = Collections.singletonList(activity);
        Long startTime = 1000L;
        Long endTime = 2000L;
        // Act
        boolean result = invokeHasOverlap(activityList, startTime, endTime);
        // Assert
        assertTrue(result, "Boundary condition (start=end) should be considered overlapping");
    }

    /**
     * 测试正常情况：部分群组已存在，部分需要新增
     */
    @Test
    void testGetInsertGroupIdsWithMixedExistingAndNewGroups() throws Throwable {
        // arrange
        List<PersonalGroupInfoDTO> groupInfos = Arrays.asList(createMockGroupInfoDTO(1L), createMockGroupInfoDTO(2L), createMockGroupInfoDTO(3L));
        when(request.getPersonalGroupInfo()).thenReturn(groupInfos);
        List<Long> existGroupIds = Arrays.asList(1L, 2L);
        // act
        Method method = FissionNonWeComGroupValidator.class.getDeclaredMethod("getInsertGroupIds", GroupFissionActivityRequest.class, List.class);
        method.setAccessible(true);
        List<Long> result = (List<Long>) method.invoke(null, request, existGroupIds);
        // assert
        assertEquals(1, result.size(), "应只返回1个需要新增的群组ID");
        assertEquals(3L, result.get(0), "返回的新增群组ID应为3");
    }

    /**
     * 测试边界情况：所有群组都需要新增（existGroupIds为空）
     */
    @Test
    void testGetInsertGroupIdsWithAllNewGroups() throws Throwable {
        // arrange
        List<PersonalGroupInfoDTO> groupInfos = Arrays.asList(createMockGroupInfoDTO(1L), createMockGroupInfoDTO(2L));
        when(request.getPersonalGroupInfo()).thenReturn(groupInfos);
        List<Long> existGroupIds = Collections.emptyList();
        // act
        Method method = FissionNonWeComGroupValidator.class.getDeclaredMethod("getInsertGroupIds", GroupFissionActivityRequest.class, List.class);
        method.setAccessible(true);
        List<Long> result = (List<Long>) method.invoke(null, request, existGroupIds);
        // assert
        assertEquals(2, result.size(), "应返回所有群组ID作为新增群组");
        assertEquals(Arrays.asList(1L, 2L), result, "返回的新增群组ID列表应与请求中的群组ID一致");
    }

    /**
     * 测试边界情况：没有需要新增的群组（所有群组已存在）
     */
    @Test
    void testGetInsertGroupIdsWithNoNewGroups() throws Throwable {
        // arrange
        List<PersonalGroupInfoDTO> groupInfos = Arrays.asList(createMockGroupInfoDTO(1L), createMockGroupInfoDTO(2L));
        when(request.getPersonalGroupInfo()).thenReturn(groupInfos);
        List<Long> existGroupIds = Arrays.asList(1L, 2L);
        // act
        Method method = FissionNonWeComGroupValidator.class.getDeclaredMethod("getInsertGroupIds", GroupFissionActivityRequest.class, List.class);
        method.setAccessible(true);
        List<Long> result = (List<Long>) method.invoke(null, request, existGroupIds);
        // assert
        assertEquals(0, result.size(), "应返回空列表，因为没有需要新增的群组");
    }

    /**
     * 测试边界情况：请求中的群组列表为空
     */
    @Test
    void testGetInsertGroupIdsWithEmptyRequestGroups() throws Throwable {
        // arrange
        when(request.getPersonalGroupInfo()).thenReturn(Collections.emptyList());
        List<Long> existGroupIds = Arrays.asList(1L, 2L);
        // act
        Method method = FissionNonWeComGroupValidator.class.getDeclaredMethod("getInsertGroupIds", GroupFissionActivityRequest.class, List.class);
        method.setAccessible(true);
        List<Long> result = (List<Long>) method.invoke(null, request, existGroupIds);
        // assert
        assertEquals(0, result.size(), "应返回空列表，因为请求中没有群组");
    }

    /**
     * 测试异常情况：request参数为null（@NotNull约束）
     */
    @Test
    void testGetInsertGroupIdsWithNullRequest() throws Throwable {
        // arrange
        List<Long> existGroupIds = Arrays.asList(1L, 2L);
        // act & assert
        Method method = FissionNonWeComGroupValidator.class.getDeclaredMethod("getInsertGroupIds", GroupFissionActivityRequest.class, List.class);
        method.setAccessible(true);
        InvocationTargetException exception = assertThrows(InvocationTargetException.class, () -> method.invoke(null, null, existGroupIds));
        assertTrue(exception.getCause() instanceof NullPointerException, "Expected a NullPointerException but was: " + exception.getCause().getClass());
    }

    /**
     * 测试异常情况：existGroupIds参数为null（@NotNull约束）
     */
    @Test
    void testGetInsertGroupIdsWithNullExistGroupIds() throws Throwable {
        // arrange
        // act & assert
        Method method = FissionNonWeComGroupValidator.class.getDeclaredMethod("getInsertGroupIds", GroupFissionActivityRequest.class, List.class);
        method.setAccessible(true);
        InvocationTargetException exception = assertThrows(InvocationTargetException.class, () -> method.invoke(null, request, null));
        assertTrue(exception.getCause() instanceof NullPointerException, "Expected a NullPointerException but was: " + exception.getCause().getClass());
    }

    /**
     * 测试边界情况：existGroupIds比request中的群组多（理论上不应发生）
     */
    @Test
    void testGetInsertGroupIdsWhenExistGroupsMoreThanRequestGroups() throws Throwable {
        // arrange
        List<PersonalGroupInfoDTO> groupInfos = Collections.singletonList(createMockGroupInfoDTO(1L));
        when(request.getPersonalGroupInfo()).thenReturn(groupInfos);
        List<Long> existGroupIds = Arrays.asList(1L, 2L, 3L);
        // act
        Method method = FissionNonWeComGroupValidator.class.getDeclaredMethod("getInsertGroupIds", GroupFissionActivityRequest.class, List.class);
        method.setAccessible(true);
        List<Long> result = (List<Long>) method.invoke(null, request, existGroupIds);
        // assert
        assertEquals(0, result.size(), "应返回空列表，因为existGroupIds数量不应多于请求中的群组数量");
    }
}
