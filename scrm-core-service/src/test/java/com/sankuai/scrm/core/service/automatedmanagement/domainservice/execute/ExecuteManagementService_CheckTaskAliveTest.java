package com.sankuai.scrm.core.service.automatedmanagement.domainservice.execute;

import com.dianping.squirrel.client.StoreKey;
import com.dianping.squirrel.client.impl.redis.RedisStoreClient;
import org.junit.Before;
import org.junit.Test;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.MockitoAnnotations;
import static org.junit.Assert.*;
import static org.mockito.Mockito.*;

public class ExecuteManagementService_CheckTaskAliveTest {

    @InjectMocks
    private ExecuteManagementService executeManagementService;

    @Mock(lenient = true)
    private RedisStoreClient redisStoreClient;

    @Before
    public void setUp() {
        MockitoAnnotations.initMocks(this);
    }

    /**
     * 测试 checkTaskAlive 方法，当 StoreKey 在 Redis 中存在时，应返回 true
     */
    @Test
    public void testCheckTaskAlive_StoreKeyExists() throws Throwable {
        // arrange
        Integer taskType = 1;
        Long taskId = 1L;
        when(redisStoreClient.get(any(StoreKey.class))).thenReturn(1);
        // act
        boolean result = executeManagementService.checkTaskAlive(taskType, taskId);
        // assert
        assertTrue(result);
    }

    /**
     * 测试 checkTaskAlive 方法，当 StoreKey 在 Redis 中不存在时，应返回 false
     */
    @Test
    public void testCheckTaskAlive_StoreKeyNotExists() throws Throwable {
        // arrange
        Integer taskType = 1;
        Long taskId = 1L;
        when(redisStoreClient.exists(any(StoreKey.class))).thenReturn(false);
        // act
        boolean result = executeManagementService.checkTaskAlive(taskType, taskId);
        // assert
        assertFalse(result);
    }
}
