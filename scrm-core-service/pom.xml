<?xml version="1.0" encoding="UTF-8"?>
<project xmlns="http://maven.apache.org/POM/4.0.0" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
         xsi:schemaLocation="http://maven.apache.org/POM/4.0.0 http://maven.apache.org/xsd/maven-4.0.0.xsd">
    <parent>
        <groupId>com.sankuai.scrm</groupId>
        <artifactId>scrm-core-server</artifactId>
        <version>0.0.1-SNAPSHOT</version>
    </parent>
    <modelVersion>4.0.0</modelVersion>
    <artifactId>scrm-core-service</artifactId>
    <version>0.0.1-SNAPSHOT</version>
    <packaging>jar</packaging>
    <name>scrm-core-service</name>
    <dependencies>
        <dependency>
            <groupId>com.dianping.deal</groupId>
            <artifactId>product-shelf-query-api</artifactId>
            <version>1.9.17</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>general-unified-search-api</artifactId>
            <version>1.8.15</version>
        </dependency>
        <dependency>
            <groupId>com.dianping.dztrade</groupId>
            <artifactId>dztrade-common-light</artifactId>
        </dependency>
        <dependency>
            <groupId>com.google.guava</groupId>
            <artifactId>guava</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.medicalcosmetology</groupId>
            <artifactId>product-selectify-api</artifactId>
        </dependency>
        <!--泛商品对外服务-->
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-common</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.tpfun</groupId>
            <artifactId>sku-aggregate-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.general.product</groupId>
            <artifactId>client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-pigeon</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-squirrel</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>com.dianping.squirrel</groupId>
                    <artifactId>squirrel-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.squirrel</groupId>
            <artifactId>squirrel-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.component</groupId>
            <artifactId>mdp-mybatis-core</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-zebra</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-web</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework.security</groupId>
                    <artifactId>spring-security-web</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate.validator</groupId>
                </exclusion>

            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>beautycontent.store.api</artifactId>
            <version>0.0.2.16</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>fastjson</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.scrm</groupId>
            <artifactId>scrm-core-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.beauty</groupId>
            <artifactId>beauty-arch-fundamental-threadpool</artifactId>
            <version>0.0.5</version>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>wedding-file-sdk</artifactId>
            <version>1.0.1</version>
        </dependency>
        <dependency>
            <groupId>com.alibaba</groupId>
            <artifactId>easyexcel</artifactId>
            <version>4.0.2</version>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-csv</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>beauty-cloud-api</artifactId>
            <version>1.1.3</version>
        </dependency>

        <!-- 节假日服务 start -->
        <dependency>
            <groupId>com.meituan.corehr</groupId>
            <artifactId>holiday-sdk</artifactId>
            <version>1.0.3-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 节假日服务 end -->

        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>baby-common-util</artifactId>
            <version>0.0.0.84</version>
            <exclusions>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.activation</groupId>
                    <artifactId>activation</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.fe</groupId>
            <artifactId>corp-wx-client</artifactId>
            <version>0.2.66</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.fe</groupId>
            <artifactId>corp-crm-client</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.activation</groupId>
                    <artifactId>javax.activation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.xml.bind</groupId>
                    <artifactId>jaxb-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.apache.httpcomponents</groupId>
            <artifactId>httpmime</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.inf</groupId>
            <artifactId>kms-pangolin-sdk</artifactId>

        </dependency>
        <dependency>
            <groupId>com.sankuai.beautycontent</groupId>
            <artifactId>task-api</artifactId>
            <version>0.0.11</version>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-mafka</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-cache</artifactId>
        </dependency>

        <dependency>
            <groupId>com.meituan.mdp</groupId>
            <artifactId>com.sankuai.dzshoplist.search.intervention.api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.beauty</groupId>
            <artifactId>beauty-beautibot-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.apache.poi</groupId>
                    <artifactId>poi-ooxml</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>baby-customer-api</artifactId>
            <version>0.0.1.18</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dianping</groupId>
                    <artifactId>ugc-review-api</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>account-validation-api</artifactId>
                    <groupId>com.dianping</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.squareup.okhttp3</groupId>
            <artifactId>okhttp</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>mobile-oss-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.education</groupId>
            <artifactId>education-lab-base-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-crane</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.open</groupId>
            <artifactId>open-picture-utils</artifactId>
        </dependency>

        <!--商家新老客判断start-->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>apollo-visit-api</artifactId>
            <version>1.5.9</version>
        </dependency>
        <!-- 商家新老客判断end -->

        <dependency>
            <groupId>com.meituan.ai</groupId>
            <artifactId>friday-java-sdk</artifactId>
            <version>0.1.1</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.opencsv</groupId>
            <artifactId>opencsv</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.user</groupId>
            <artifactId>thirdinfo-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.xm</groupId>
            <artifactId>openplatform-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-pacific-proxy-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpcrm.tools</groupId>
            <artifactId>qwtool-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.edc</groupId>
            <artifactId>edc-open-sdk</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>asm</groupId>
                    <artifactId>asm-all</artifactId>
                </exclusion>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate.validator</groupId>
                </exclusion>
                <exclusion>
                    <groupId>cn.hutool</groupId>
                    <artifactId>hutool-core</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-shop-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai</groupId>
            <artifactId>dztheme-generalproduct-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>dztheme-deal-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile.group</groupId>
            <artifactId>groupbase</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>mail</artifactId>
                    <groupId>javax.mail</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>activation</artifactId>
                    <groupId>javax.activation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.mobile</groupId>
            <artifactId>groupgeo</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>xercesImpl</artifactId>
                    <groupId>xerces</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmkt.coupon</groupId>
            <artifactId>mkt-coupon-issue-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpmkt.coupon</groupId>
            <artifactId>mkt-coupon-query-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.tgc</groupId>
            <artifactId>tgc-process-remote</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.haima</groupId>
            <artifactId>haima-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>slf4j-reload4j</artifactId>
                    <groupId>org.slf4j</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>servlet-api</artifactId>
                    <groupId>javax.servlet</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>org.restlet.ext.json</artifactId>
                    <groupId>org.restlet.jee</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>junglepoi.client</artifactId>
                    <groupId>com.meituan.service.mobile</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 图片对比服务 -->
        <dependency>
            <groupId>com.sankuai.horus</groupId>
            <artifactId>edfu-server-sdk</artifactId>
            <version>1.0.9-RELEASE</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wpt.user.retrieve</groupId>
            <artifactId>retrieve-api</artifactId>
        </dependency>
        <!-- 直播登录 -->
        <dependency>
            <groupId>com.sankuai.dzrtc</groupId>
            <artifactId>dzrtc-privatelive-biz-api</artifactId>
            <version>1.0.87</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 直播列表 -->
        <dependency>
            <groupId>com.sankuai.dzrtc</groupId>
            <artifactId>dzrtc-privatelive-auth-sdk</artifactId>
            <version>1.0.10</version>
            <exclusions>
                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate.validator</groupId>
                </exclusion>
                <exclusion>
                    <artifactId>jakarta.validation-api</artifactId>
                    <groupId>jakarta.validation</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzrtc</groupId>
            <artifactId>dzrtc-privatelive-operation-api</artifactId>
            <version>1.0.25</version>
        </dependency>
        <!-- 视频服务 -->
        <dependency>
            <groupId>com.meituan.horus</groupId>
            <artifactId>horus-sdk</artifactId>
            <version>1.6.99</version>
        </dependency>
        <!-- 直播推送 -->
        <dependency>
            <groupId>com.sankuai.dzmkt</groupId>
            <artifactId>dzmkt-private-biz-api</artifactId>
            <version>1.0.0</version>
        </dependency>

        <dependency>
            <groupId>com.sankuai.persona</groupId>
            <artifactId>persona-idl</artifactId>
            <version>1.1.7</version>
        </dependency>

        <!-- 优惠码、地推等渠道识别服务 -->
        <dependency>
            <groupId>com.meituan.carnation</groupId>
            <artifactId>carnation-distribution-api</artifactId>

            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>javax.annotation</groupId>
                    <artifactId>javax.annotation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>com.dp.arts</groupId>
                    <artifactId>arts-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dp.arts</groupId>
            <artifactId>arts-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-rgc-api</artifactId>
        </dependency>

        <!-- poi类目服务 -->
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-cateproperty-api</artifactId>
            <version>0.2.8</version>
        </dependency>

        <!-- 类目服务 -->
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-shopcateprop-api</artifactId>
            <version>0.5.7</version>
        </dependency>

        <!-- 订单服务 -->
        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-order-service</artifactId>
            <version>2.4.5</version>
        </dependency>

        <dependency>
            <groupId>com.dianping.pay</groupId>
            <artifactId>pay-order-common</artifactId>
            <version>2.4.5</version>
            <exclusions>
                <exclusion>
                    <groupId>org.springframework</groupId>
                    <artifactId>spring</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>commons-logging</groupId>
                    <artifactId>commons-logging</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>log4j</groupId>
                    <artifactId>log4j</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.gmkt</groupId>
            <artifactId>gmkt-event-manage-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- pike服务 start -->
        <dependency>
            <groupId>com.cip</groupId>
            <artifactId>pike-message-sdk</artifactId>
            <version>2.0.8-RELEASE</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- pike服务 end -->
        <!-- token 解密-->
        <dependency>
            <groupId>com.sankuai.conch.certify</groupId>
            <artifactId>tokenAccessSdk</artifactId>
        </dependency>
        <!-- swarm 服务 -->
        <dependency>
            <groupId>com.meituan.mos.mss</groupId>
            <artifactId>mss-common-service</artifactId>
            <version>1.0.6</version>
        </dependency>
        <dependency>
            <groupId>com.sankuai.wpt.user.merge</groupId>
            <artifactId>user-merge-query-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-gis-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.sinai</groupId>
            <artifactId>sinai-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.nibmp</groupId>
            <artifactId>mem-vaf-query-thrift</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.poi</groupId>
            <artifactId>poi-relation-service-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping.lion</groupId>
            <artifactId>lion-client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mafka</groupId>
            <artifactId>mafka-client_2.10</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.slf4j</groupId>
                    <artifactId>slf4j-log4j12</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.inf.leaf</groupId>
            <artifactId>leaf-idl</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.service.user</groupId>
            <artifactId>validate-token-api</artifactId>
        </dependency>
        <!-- 基础rhino包 -->
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-rhino</artifactId>
        </dependency>
        <dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-quartz</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>ugc-review-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.redecision</groupId>
            <artifactId>redecision-operation-api</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.hibernate</groupId>
                    <artifactId>hibernate-validator</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.meituan.rc.risk</groupId>
            <artifactId>rc-tatooine-client</artifactId>
        </dependency>
        <!--美团后台地理信息服务-->
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>mtgis-remote-service</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-all</artifactId>
        </dependency>
        <dependency>
            <groupId>cn.hutool</groupId>
            <artifactId>hutool-core</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.poi</groupId>
            <artifactId>poi-ooxml</artifactId>
        </dependency>
        <dependency>
            <groupId>commons-io</groupId>
            <artifactId>commons-io</artifactId>
        </dependency>
        <dependency>
            <groupId>org.apache.commons</groupId>
            <artifactId>commons-compress</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mpproduct.idservice</groupId>
            <artifactId>idservice-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.technician</groupId>
            <artifactId>technician-trade-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.mim</groupId>
            <artifactId>work-chat-api</artifactId>
            <version>1.0.0</version>
            <exclusions>
                <exclusion>
                    <groupId>com.sankuai.fbi</groupId>
                    <artifactId>fbi-adapter-for-nest</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 隐私号服务依赖 start -->
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>csc-did-provider-api</artifactId>
            <version>3.0.10</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.validation</groupId>
                    <artifactId>validation-api</artifactId>
                </exclusion>

                <exclusion>
                    <artifactId>hibernate-validator</artifactId>
                    <groupId>org.hibernate</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <!-- 隐私号服务 end -->

        <!--im服务jar包-->
        <dependency>
            <groupId>com.sankuai.dzim</groupId>
            <artifactId>groupchat-api</artifactId>
            <version>1.0.3</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzim</groupId>
            <artifactId>message-common</artifactId>
            <version>0.0.93</version>
            <exclusions>
                <exclusion>
                    <groupId>javax.servlet</groupId>
                    <artifactId>javax.servlet-api</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>com.dianping.vc</groupId>
            <artifactId>vc-interface</artifactId>
        </dependency>
        <!--es java client-->
        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>poros-high-level-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch</groupId>
            <artifactId>elasticsearch</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.elasticsearch.client</groupId>
                    <artifactId>elasticsearch-rest-client</artifactId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>org.elasticsearch.client</groupId>
            <artifactId>elasticsearch-rest-high-level-client</artifactId>
            <exclusions>
                <exclusion>
                    <artifactId>elasticsearch-rest-client</artifactId>
                    <groupId>org.elasticsearch.client</groupId>
                </exclusion>
            </exclusions>
        </dependency>
        <dependency>
            <groupId>javax.persistence</groupId>
            <artifactId>persistence-api</artifactId>
        </dependency>
        <dependency>
            <groupId>jakarta.persistence</groupId>
            <artifactId>jakarta.persistence-api</artifactId>
        </dependency>

        <dependency>
            <groupId>com.sankuai.meituan</groupId>
            <artifactId>poros-client</artifactId>
        </dependency>
        <dependency>
            <groupId>org.hibernate.validator</groupId>
            <artifactId>hibernate-validator</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.mdp.boot</groupId>
            <artifactId>mdp-boot-starter-cerberus</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.nibscp</groupId>
            <artifactId>unity-universal-diff</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.general</groupId>
            <artifactId>order-query-center-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.meituan.image</groupId>
            <artifactId>client</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzusergrowth</groupId>
            <artifactId>ai-assistant-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.sankuai.dzusergrowth</groupId>
            <artifactId>growth-privatelive-api</artifactId>
        </dependency>
        <dependency>
            <groupId>com.dianping</groupId>
            <artifactId>account-validation-api</artifactId>
        </dependency>
        <!--<dependency>
            <groupId>org.springframework.boot</groupId>
            <artifactId>spring-boot-starter-test</artifactId>
            <exclusions>
                <exclusion>
                    <groupId>org.junit.vintage</groupId>
                    <artifactId>junit-vintage-engine</artifactId>
                </exclusion>
            </exclusions>
        </dependency>-->
    </dependencies>
    <build>
        <finalName>${project.artifactId}</finalName>
        <plugins>
            <!--            <plugin>-->
            <!--                <groupId>com.meituan.mdp.maven.plugins</groupId>-->
            <!--                <artifactId>mdp-doc-maven-plugin</artifactId>-->
            <!--                <executions>-->
            <!--                    <execution>-->
            <!--                        <id>doc-verify</id>-->
            <!--                        <phase>package</phase>-->
            <!--                        <goals>-->
            <!--                            <goal>verify</goal>-->
            <!--                        </goals>-->
            <!--                    </execution>-->
            <!--                </executions>-->
            <!--            </plugin>-->
            <plugin>
                <groupId>org.springframework.boot</groupId>
                <artifactId>spring-boot-maven-plugin</artifactId>
            </plugin>
        </plugins>
    </build>
    <!--    <profiles>-->
    <!--        <profile>-->
    <!--            <id>test</id>-->
    <!--            <build>-->
    <!--                <plugins>-->
    <!--                    <plugin>-->
    <!--                        <groupId>org.springframework.boot</groupId>-->
    <!--                        <artifactId>spring-boot-maven-plugin</artifactId>-->
    <!--                        <version>2.0.7.RELEASE</version>-->
    <!--                        <executions>-->
    <!--                            <execution>-->
    <!--                                <goals>-->
    <!--                                    <goal>repackage</goal>-->
    <!--                                </goals>-->
    <!--                                <configuration>-->
    <!--                                    <layoutFactory implementation="com.sankuai.athena.boot.loader.AthenaLayoutFactory"></layoutFactory>-->
    <!--                                </configuration>-->
    <!--                            </execution>-->
    <!--                        </executions>-->
    <!--                        <dependencies>-->
    <!--                            <dependency>-->
    <!--                                <groupId>athena-home</groupId>-->
    <!--                                <artifactId>athena-boot-loader</artifactId>-->
    <!--                                <version>1.0.1</version>-->
    <!--                            </dependency>-->
    <!--                        </dependencies>-->
    <!--                    </plugin>-->
    <!--                </plugins>-->
    <!--            </build>-->
    <!--        </profile>-->
    <!--    </profiles>-->
    <profiles>
        <profile>
            <build>
                <plugins>
                    <plugin>
                        <groupId>org.apache.maven.plugins</groupId>
                        <artifactId>maven-surefire-plugin</artifactId>
                        <version>2.20</version>
                    </plugin>
                </plugins>
            </build>
        </profile>
    </profiles>
</project>